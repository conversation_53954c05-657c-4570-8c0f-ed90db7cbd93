<?xml version="1.0" encoding="UTF-8"?>
<root>

    <!--单gs最大同时在线玩家数控制-->
    <maxPlayerCount>
        <area name="default">5000</area>
        <area name="ko">5000</area>
        <area name="tc">5000</area>
    </maxPlayerCount>

    <maxPlayerCountPress>5000</maxPlayerCountPress>

    <!--通信协议是否加密-->
    <protocolEncrypt>true</protocolEncrypt>

    <!--Fork/Join线程池任务最大数量-->
    <forkJoinTaskCountLimit>2000</forkJoinTaskCountLimit>

    <!--Fork/Join线程池线程数-->
    <forkJoinThreadCount>30</forkJoinThreadCount>

    <!--登录&注册同时最大任务数量-->
    <loginOrRegisterMeantimeLimit>1000</loginOrRegisterMeantimeLimit>

    <!--登录校验签名GameKey-->
    <loginCheckSignGameKey>2a5fa67825a9bb8305be74f599ac1b5e</loginCheckSignGameKey>

    <!--重连最大时间间隔(秒)-->
    <reconnectMaxInterval>86400</reconnectMaxInterval>

    <!--保存玩家数据间隔(秒)-->
    <savePlayerDataPeriod>600</savePlayerDataPeriod>

    <!--MiniPlayer加载单页数量-->
    <loadMiniPlayerPageSize>500</loadMiniPlayerPageSize>

    <!--全局数据存储间隔(秒)-->
    <worldDataSavePeriod>30</worldDataSavePeriod>

    <!--缓存协议包数量-->
    <cachePacketCount>5</cachePacketCount>

    <!--时间统计器统计最小耗时毫秒数-->
    <elapsedTimeStatisticsMinNano>100000</elapsedTimeStatisticsMinNano>

    <!--数据包太多导致断开连接时间区间阈值(毫秒，值必须小于一分钟)-->
    <packetTooManyDisconnectDurationThreshold>1000</packetTooManyDisconnectDurationThreshold>

    <!--数据包太多导致断开连接数量阈值-->
    <packetTooManyDisconnectCountThreshold>5000</packetTooManyDisconnectCountThreshold>
    <packetTooManyDisconnectCountThresholdScene>5000</packetTooManyDisconnectCountThresholdScene>

    <!--Player对象缓存数量-->
    <playerObjectCacheCount>2000</playerObjectCacheCount>

</root>
#!/bin/bash

############### 引入环境变量 ###############
. /etc/profile
export MALLOC_ARENA_MAX=4



############### 经常改变的变量 ###############
memory=${MEMORY}m
metaspaceMemory=128m
maxMetaspaceMemory=256m
directMemory=1024m
mainClass=com.gy.server.core.Launcher




############### 不经常改变的变量 ###############
hotfixJar=hotfix-1.0-SNAPSHOT.jar
runLibPath=./dist
pidFile=bt.pid

logPath=/logs
runLogPath=${logPath}/run
stdLogFile=${logPath}/stdout.log
gcLogFile=${runLogPath}/gc.log



############### JVM参数 ###############
#设置jvm的内存
jvm=${jvm}' '-Xms${memory}
jvm=${jvm}' '-Xmx${memory}
jvm=${jvm}' '-XX:MetaspaceSize=${metaspaceMemory}
jvm=${jvm}' '-XX:MaxMetaspaceSize=${maxMetaspaceMemory}
jvm=${jvm}' '-XX:MaxDirectMemorySize=${directMemory}


#设置打印gc的参数
jvm=${jvm}' '-Xloggc:${gcLogFile}
jvm=${jvm}' '-XX:+PrintGC
jvm=${jvm}' '-XX:+PrintGCID
jvm=${jvm}' '-XX:+PrintGCCause
jvm=${jvm}' '-XX:+PrintGCDetails
jvm=${jvm}' '-XX:+PrintHeapAtGC
jvm=${jvm}' '-XX:+PrintGCDateStamps
jvm=${jvm}' '-XX:+PrintGCTimeStamps
jvm=${jvm}' '-XX:+PrintGCTaskTimeStamps
jvm=${jvm}' '-XX:+PrintGCApplicationStoppedTime
jvm=${jvm}' '-XX:+PrintGCApplicationConcurrentTime
jvm=${jvm}' '-XX:+PrintTenuringDistribution
jvm=${jvm}' '-XX:+HeapDumpOnOutOfMemoryError
jvm=${jvm}' '-XX:+PrintStringDeduplicationStatistics
jvm=${jvm}' '-XX:+DisableExplicitGC


#远程监控参数
jvm=${jvm}' '-Dcom.sun.management.jmxremote.rmi.port=${JMX_PORT}
jvm=${jvm}' '-Dcom.sun.management.jmxremote=true
jvm=${jvm}' '-Dcom.sun.management.jmxremote.port=${JMX_PORT}
jvm=${jvm}' '-Dcom.sun.management.jmxremote.ssl=false
jvm=${jvm}' '-Dcom.sun.management.jmxremote.authenticate=false
jvm=${jvm}' '-Dcom.sun.management.jmxremote.local.only=false
jvm=${jvm}' '-Djava.rmi.server.hostname=${JMX_IP}


#调整young gen和old gen的大小,年轻代:年老代=1:NewRatio
jvm=${jvm}' '-XX:NewRatio=4

#调整young gen的Survivor和Eden大小,Survivor:Eden=2:SurvivorRatio
jvm=${jvm}' '-XX:SurvivorRatio=8
jvm=${jvm}' '-XX:MaxTenuringThreshold=15
jvm=${jvm}' '-XX:+UseParNewGC
jvm=${jvm}' '-XX:+UseConcMarkSweepGC

#压缩64位jvm的指针提升性能,压缩后只支持32G的堆,如果heap超过32G,不能压缩
jvm=${jvm}' '-XX:+UseCompressedOops
jvm=${jvm}' '-XX:+OptimizeStringConcat
jvm=${jvm}' '-XX:+UseFastAccessorMethods
jvm=${jvm}' '-XX:+CMSParallelRemarkEnabled
jvm=${jvm}' '-XX:+CMSClassUnloadingEnabled
jvm=${jvm}' '-XX:+ExplicitGCInvokesConcurrent
jvm=${jvm}' '-XX:-OmitStackTraceInFastThrow
jvm=${jvm}' '-XX:+ExplicitGCInvokesConcurrentAndUnloadsClasses
jvm=${jvm}' '-XX:CMSInitiatingOccupancyFraction=80




############### 必要目录创建 ###############
if [[ ! -d ${logPath} ]] ;then
mkdir ${logPath}
fi
if [[ ! -d ${runLogPath} ]] ;then
mkdir ${runLogPath}
fi




############### 启动前处理 ###############


#切分创建stdout.log
if [[ -f ${stdLogFile} ]] ;then
dateStr=`date +%Y%m%d%H%M`
rm -f ${stdLogFile}.${dateStr}
mv ${stdLogFile} ${stdLogFile}.${dateStr}
touch ${stdLogFile}
fi


#设置最大文件开启数
ulimit -n 65535


#获取脚本执行绝对路径
basePath=$(cd `dirname $0`; pwd)


############### 启动 ###############
exec java -server -javaagent:./${runLibPath}/${hotfixJar} ${jvm} -Xdebug -Xnoagent -Djava.compiler=NONE -Xrunjdwp:transport=dt_socket,address=${REMOTE_DEBUG_PORT},server=y,suspend=n -classpath "${runLibPath}/*" ${mainClass} docker ${basePath} $@ >> ${stdLogFile} 2>&1
<?xml version="1.0" encoding="UTF-8"?>
<!--级别:TRACE > DEBUG > INFO > WARN > ERROR > FATAL-->
<Configuration status="WARN" shutdownHook="disable">
    <Loggers>
        <logger name="org.hibernate" level="warn"/>
        <logger name="org.reflections" level="warn"/>
        <logger name="net.sf.ehcache" level="warn"/>
        <logger name="io.netty" level="warn"/>
        <logger name="com.mchange" level="warn"/>
        <logger name="io.lettuce" level="warn"/>
        <logger name="org.redisson" level="warn"/>
        <logger name="com.baidu" level="warn"/>

        <!--additivity,boolean,用来指定输出添加关系,子Logger的输出是否需要输出到父Logger中-->
        <Root level="trace" includeLocation="false" additivity="true">
            <AppenderRef ref="Console"/>
        </Root>
    </Loggers>

    <Appenders>
        <!--log格式采用'|'为分列符号,方便日志分析程序进行分列处理,并且带时区,用以跨地区日志分析使用-->
        <Console name="Console">
            <PatternLayout charset="UTF-8" pattern="%-5p|%d{yyyy-MM-dd HH:mm:ss,SSS Z}|%-6c|%m%n"/>
        </Console>

    </Appenders>
</Configuration>
package com.gy.server.docker;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Objects;

import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

/**
 * <AUTHOR> - [Created on 2022-04-07 10:12]
 */
public class DockerSplit {
    private static final String file_prefix = "gs";
    private static final int file_suffix = 1;
    private static final int split_count = 5;



    public static void main(String[] args) throws IOException, InvalidFormatException {
        String mainFileUrl = "C:/Users/<USER>/Desktop/sdk/redis/gs.xlsx";
        File file = new File(mainFileUrl);
        XSSFWorkbook workbook = new XSSFWorkbook(new FileInputStream(file));

        XSSFSheet sheetAt = workbook.getSheet("docker");

        XSSFWorkbook newBook;
        int fileSuffix = file_suffix;
        FileOutputStream outputStream = null;
        try {
            for(int i = 2; i < sheetAt.getLastRowNum(); i++){
                //初始化表头
                newBook = new XSSFWorkbook();
                newBook.createSheet("docker");
                PoiUtil.copyCellStyle(workbook, newBook, workbook.getCellStyleAt(0), newBook.getCellStyleAt(0));
                PoiUtil.copyRows(workbook, newBook, 0, 0, 0, 1, 0);
                //复制数据
                PoiUtil.copyRows(workbook, newBook, 0, 0, i, i + split_count - 1, 2);
                //创建文件
                File newFile = new File(file.getParent() + "/" +file_prefix + fileSuffix + ".xlsx");
                outputStream = new FileOutputStream(newFile);
                newBook.write(outputStream);
                outputStream.flush();

                i += (split_count - 1);
                fileSuffix++;
            }
        } finally {
            if(Objects.nonNull(outputStream)){
                outputStream.close();
            }
        }

    }

}

package com.gy.server.rank;

import com.gy.server.common.util.ConsumeTime;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.rank.RankData;
import com.gy.server.game.rank.RankItem;
import com.gy.server.game.rank.RankManager;
import com.gy.server.game.rank.RankType;
import com.gy.server.utils.ArrayUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

public class RankTest {
    private static ConsumeTime consumeTime = new ConsumeTime();
    private static final RankType rankType = RankType.playerFightPower;

    public static void main(String[] args) {

        /**
         *
         *
         * 测试结果：
         * 2W人，一个省份 5个城市 8个区，耗时10-40毫秒
         *
         */


        rankInit();
        RankData rankData = RankManager.getRankData(rankType);
        System.out.println("init finish !!!!");
        for (int i = 0; i < 100; i++){
            consumeTime.reset();
            List<RankItem> allRanks = rankData.getRankItemListByArenaId(null);
            List<RankItem> provinceRanks = rankData.getRankItemListByArenaId(miniGamePlayer -> miniGamePlayer.getProvinceId() == 1000);
            List<RankItem> cityRanks = rankData.getRankItemListByArenaId(miniGamePlayer -> miniGamePlayer.getCityId() == 1010);
            List<RankItem> districtRanks = rankData.getRankItemListByArenaId(miniGamePlayer -> miniGamePlayer.getDistrictId() == 1033);
            consumeTime.println("get all rank cost : %s");


            consumeTime.reset();
            rankData.convertArenaItems();
            consumeTime.println("convertArenaItems cost : %s");
        }

    }


    public static void rankInit(){

        int maxCount = 20000;

        int provinceId = 1000;
        int cityId = 1010;

        //构建2W个miniPlayer
        List<Long> rankPlayerIds = new ArrayList<>();
        ConcurrentHashMap<Long, MiniGamePlayer> miniPlayersRaw = PlayerManager.getMiniPlayersRaw();
        int districtCount = 500;
        int districtId = cityId + 1;
        for (long i = 0; i < maxCount; i++) {
            long playerId = i + 1;
            MiniGamePlayer player = new MiniGamePlayer();
            player.setPlayerId(playerId);
            //设置地区信息
            player.setProvinceId(provinceId);
            if(i > 0 && i % 4000 == 0){
                cityId+=10;
                districtId = cityId;
            }
            if(districtCount <= 0){
                districtCount = 500;
                districtId = districtId + 1;
            }
            player.setCityId(cityId);
            player.setDistrictId(districtId);
            player.setFightingPower(i);
            miniPlayersRaw.put(playerId, player);
            rankPlayerIds.add(playerId);
            districtCount--;
        }

        //构建排行榜信息
        for (Long rankPlayerId : rankPlayerIds) {
            RankItem item = new RankItem();
            item.setRankId(rankType.getRankId());
            item.setItemIds(new long[]{rankPlayerId});
            item.setItemIdText(ArrayUtil.toString(item.getItemIds()));
            item.setValues(new long[]{
                    miniPlayersRaw.get(rankPlayerId).getFightingPower(),
                    -System.currentTimeMillis()
            });
            item.setValueText(ArrayUtil.toString(item.getValues()));
            item.setExtraText("");

            RankData rankData = RankManager.getRankData(rankType);
            rankData.postItem(item);
        }

        RankData rankData = RankManager.getRankData(rankType);
        consumeTime.reset();
        rankData.checkSort();
        consumeTime.println("checkSort cost : %s");

    }


}

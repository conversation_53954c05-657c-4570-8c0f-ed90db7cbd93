package com.gy.server.out;

import com.gy.server.game.item.ItemLibertyType;
import org.junit.Test;

/**
 * <AUTHOR> 2024/5/30 13:32
 **/
public class SwitchCasePrint {

    @Test
    public void print(){
        System.out.println("switch (type){");
        for (ItemLibertyType bean : ItemLibertyType.values()) {
            System.out.println("    case " + bean.getType() + ": return " + bean + ";");
        }
        System.out.println("    default : return " + ItemLibertyType.none + ";");
        System.out.println("}");
    }
}

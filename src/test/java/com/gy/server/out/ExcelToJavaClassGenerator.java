package com.gy.server.out;

import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * excel自动生成java类
 * @author: gbk
 * @date: 2025-03-07 07:07
 */
public class ExcelToJavaClassGenerator {

    public static void main(String[] args) {
        //下面两个路径需要手动修改
        // Excel文件路径
        String excelFilePath = "C:\\Users\\<USER>\\Desktop\\sdk\\redis\\tl\\theSutraRepository.xlsx";
        //生成java文件路径
        String outFilePath = "G:\\tlbbWorkspace\\trunk\\game\\server\\tl-game\\src\\main\\java\\com\\gy\\server\\game\\activity\\data\\sutraPavilion\\template";

        start(excelFilePath, outFilePath);
    }

    private static void start(String excelFilePath, String outFilePath){
        File file = new File(excelFilePath);
        List<String> configFileNames = new ArrayList<>();
        try (FileInputStream fis = new FileInputStream(file);
             Workbook workbook = new XSSFWorkbook(fis)) {
            String fileName = file.getName().split("\\.")[0];
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(i);
                String className = sheet.getSheetName(); // 使用表格名作为类名
                if (className.equals("!const")) {
                    generateConstClass(outFilePath, sheet, capitalize(fileName) +  "ConstTemplate");
                    configFileNames.add(fileName + "_const");
                }else{
                    if(fileName.equalsIgnoreCase(className)) {
                        generateClass(outFilePath, sheet, capitalize(fileName) + "Template");
                    }else{
                        generateClass(outFilePath, sheet, capitalize(fileName) + capitalize(className) + "Template");
                    }
                    configFileNames.add(fileName + "_" + className);
                }
//                break;
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
        System.out.println("configFileNames: " + configFileNames);
    }

    private static String capitalize(String str) {
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }

    private static void generateConstClass(String outFilePath, Sheet sheet, String className) {
        String packagePath = convertPathToPackage(outFilePath);
        StringBuilder classBuilder = new StringBuilder();
        classBuilder.append("package " + packagePath + ";\n\n");
        classBuilder.append("import java.util.*;\n");
        classBuilder.append("import com.gy.server.common.base.AbsTemplate;\n");
        classBuilder.append("import com.gy.server.game.drop.RewardTemplate;\n\n");

        classBuilder.append("public class ").append(className).append(" implements AbsTemplate {\n\n");

        for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
            //按行读取
            Row row = sheet.getRow(i);
            if(Objects.isNull(row)){
                continue;
            }
            String columnName = row.getCell(0).getStringCellValue();//字段名
            String csType = row.getCell(1).getStringCellValue();//关注类型
            String fieldType = row.getCell(2).getStringCellValue();//类型数据
            String noteName = row.getCell(4).getStringCellValue();//注释

            if(columnName.isEmpty() || columnName.equals("$")
                    || !csType.contains("S")){
                continue;
            }
            fieldType = getFieldType(fieldType);

            classBuilder.append("    public ").append(fieldType).append(" ")
                    .append(columnName).append("; // ").append(noteName).append(" \n");
        }

        classBuilder.append("\n    @Override\n");
        classBuilder.append("    public void readTxt(Map<String, String> map) {\n");
        for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
            //按行读取
            Row row = sheet.getRow(i);
            if(Objects.isNull(row)){
                continue;
            }
            String columnName = row.getCell(0).getStringCellValue();//字段名
            String csType = row.getCell(1).getStringCellValue();//关注类型
            String fieldType = row.getCell(2).getStringCellValue();//类型数据

            if(columnName.isEmpty() || columnName.equals("$")
                    || !csType.contains("S")){
                continue;
            }
            fieldType = getFieldType(fieldType);

            if (fieldType.equals("int")) {
                classBuilder.append("        ").append(columnName).append(" = Integer.parseInt(map.get(\"")
                        .append(columnName).append("\"));\n");
            } else if (fieldType.equals("String")) {
                classBuilder.append("        ").append(columnName).append(" = map.get(\"")
                        .append(columnName).append("\");\n");
            }else if (fieldType.equals("List<RewardTemplate>")) {
                classBuilder.append("        ").append(columnName).append(" = RewardTemplate.readListFromText(map.get(\"")
                        .append(columnName).append("\"));\n");
            }
        }

        classBuilder.append("    }\n");

        classBuilder.append("\n    @Override\n");
        classBuilder.append("    public String toString() {\n");
        classBuilder.append("        return \"" + className + "{\"\n");

        for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
            //按行读取
            Row row = sheet.getRow(i);
            if(Objects.isNull(row)){
                continue;
            }
            String columnName = row.getCell(0).getStringCellValue();//字段名
            String csType = row.getCell(1).getStringCellValue();//关注类型
            if(columnName.isEmpty() || columnName.equals("$")
                    || !csType.contains("S")){
                continue;
            }
            classBuilder.append("                + \"").append(columnName).append("=\" + ").append(columnName).append("\n");
        }
        classBuilder.append("                + '}';\n");
        classBuilder.append("    }\n");


        classBuilder.append("}\n");

        writeFile(classBuilder.toString(), outFilePath, className);
    }
    private static void generateClass(String outFilePath, Sheet sheet, String className) {
        String packagePath = convertPathToPackage(outFilePath);
        StringBuilder classBuilder = new StringBuilder();
        classBuilder.append("package "+packagePath+";\n\n");
        classBuilder.append("import java.util.*;\n");
        classBuilder.append("import com.gy.server.common.base.AbsTemplate;\n");
        classBuilder.append("import com.gy.server.game.drop.RewardTemplate;\n\n");

        classBuilder.append("public class ").append(className).append(" implements AbsTemplate {\n\n");

        Row noteRow = sheet.getRow(0);//第一行是注释
        Row csTypeRow = sheet.getRow(1);//第二行是需要关注的类型
        Row fieldTypeRow = sheet.getRow(2);//第三行是字段类型
        Row headerRow = sheet.getRow(4);//第五行是成员变量名字
        for (int j = 0; j < headerRow.getPhysicalNumberOfCells(); j++) {
            if(headerRow.getCell(j) == null){
                continue;
            }
            String columnName = headerRow.getCell(j).getStringCellValue();
            String csType = csTypeRow.getCell(j).getStringCellValue();
            if(columnName.equals("$")
                || !csType.contains("S")){
                continue;
            }
            String noteName = noteRow.getCell(j).getStringCellValue();
            String fieldType = getFieldType(fieldTypeRow.getCell(j).getStringCellValue());

            classBuilder.append("    public ").append(fieldType).append(" ")
                    .append(columnName).append("; // ").append(noteName).append(" \n");
        }

        classBuilder.append("\n    @Override\n");
        classBuilder.append("    public void readTxt(Map<String, String> map) {\n");
        for (int j = 0; j < headerRow.getPhysicalNumberOfCells(); j++) {
            if(headerRow.getCell(j) == null){
                continue;
            }
            String columnName = headerRow.getCell(j).getStringCellValue();
            String csType = csTypeRow.getCell(j).getStringCellValue();
            if(columnName.equals("$")
                    || !csType.contains("S")){
                continue;
            }
            String fieldType = getFieldType(fieldTypeRow.getCell(j).getStringCellValue());

            if (fieldType.equals("int")) {
                classBuilder.append("        ").append(columnName).append(" = Integer.parseInt(map.get(\"")
                        .append(columnName).append("\"));\n");
            } else if (fieldType.equals("String")) {
                classBuilder.append("        ").append(columnName).append(" = map.get(\"")
                        .append(columnName).append("\");\n");
            }else if (fieldType.equals("List<RewardTemplate>")) {
                classBuilder.append("        ").append(columnName).append(" = RewardTemplate.readListFromText(map.get(\"")
                        .append(columnName).append("\"));\n");
            }
        }
        classBuilder.append("    }\n");

        classBuilder.append("\n    @Override\n");
        classBuilder.append("    public String toString() {\n");
        classBuilder.append("        return \"" + className + "{\"\n");
        for (int j = 0; j < headerRow.getPhysicalNumberOfCells(); j++) {
            if(headerRow.getCell(j) == null){
                continue;
            }
            String columnName = headerRow.getCell(j).getStringCellValue();
            String csType = csTypeRow.getCell(j).getStringCellValue();
            if(columnName.equals("$")
                    || !csType.contains("S")){
                continue;
            }
            classBuilder.append("                + \"").append(columnName).append("=\" + ").append(columnName).append("\n");
        }
        classBuilder.append("                + '}';\n");
        classBuilder.append("    }\n");


        classBuilder.append("}\n");

        //生成class
        writeFile(classBuilder.toString(), outFilePath, className);
    }

    private static String convertPathToPackage(String filePath) {
        String[] parts = filePath.split("src\\\\main\\\\java\\\\");
        if (parts.length > 1) {
            return parts[1].replace("\\", ".");
        }
        return "";
    }

    private static void writeFile(String content, String packagePath, String className) {
        // 将包路径转换为文件路径
        String dirPath = packagePath.replace('.', '/');
        new File(dirPath).mkdirs(); // 创建文件夹
        String filePath = dirPath + "/" + className + ".java";

        try (BufferedWriter writer = new BufferedWriter(new FileWriter(filePath))) {
            writer.write(content);
            System.out.println("Class " + className + " has been written to " + filePath);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static String getFieldType(String cellNum) {
        if (cellNum.equals("number")) {
            return "int"; // 这里可以根据需要扩展为 double 或其他类型
        } else if (cellNum.equals("string")) {
            return "String";
        } else if(cellNum.equals("RewardStr") || cellNum.equals("rewardStr")){
            return "List<RewardTemplate>";
        }
        return "String"; // 默认返回
    }

}
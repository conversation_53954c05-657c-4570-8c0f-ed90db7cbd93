package com.gy.server.serverinnertest;


import com.gy.server.game.attribute.AttributeSourceType;
import com.gy.server.game.attribute.Attributes;
import com.gy.server.game.hero.Hero;
import com.gy.server.game.hero.HeroPassGongType;
import com.gy.server.game.passGong.bean.PassGongSharePropertyEnum;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.warZone.WarZoneHelper;
import com.gy.server.game.warZone.WarZoneTypeEnums;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.Md5Util;
import com.gy.server.utils.net.HttpUtil;

import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.util.Map;

/**
 * 服务器先行的自主测试基础类
 * <AUTHOR> - [Create on 2019/05/10 18:47]
 */
public class BaseTester {


    public static void main(String[] args) throws NoSuchAlgorithmException, IOException {
//        3006


        WarZoneHelper.allocateWarZone(WarZoneTypeEnums.MountainRiverTournament);


        Player player = PlayerManager.getOnlinePlayer(9970000009L);
        for (Hero allHero : player.getBagModel().getAllHeroes()) {
            if(allHero.getPgType() == HeroPassGongType.bePass){
                allHero.refreshEquipmentExtendsProperty();
                allHero.refreshBagAttributes(true, true, AttributeSourceType.装备);
            }
        }

        Hero hero = player.getBagModel().getHeroByTemplateId(3008);

        Map<AttributeSourceType, Attributes> attributesMap = hero.getAttributesMap();
        for (AttributeSourceType attributeSourceType : attributesMap.keySet()) {
            Attributes attributes = attributesMap.get(attributeSourceType);
            System.out.println(attributeSourceType.name() + " : " + attributes.getAttributes());
        }
        PlayerHelper.gmDeal(player, "/gm maxlvl");
        PlayerHelper.gmDeal(player, "/gm addAllHero ");
        PlayerHelper.gmDeal(player, "/gm addAllItem 999 ");
        PlayerHelper.gmDeal(player, "/gm mission 1010 101010");
        PlayerHelper.gmDeal(player, "/gm level 60");
        PlayerHelper.gmDeal(player, "/gm nb");


        String s = HttpUtil.requestHttpWithPostReturnString("http://tl.shangua.com:16001/center/platform_info_default", "area=-1&test=true&version=999999999.0.0", "UTF8");
        System.out.println(s);

        System.out.println(Md5Util.MD5(PbProtocol.PetSkerryModifyReq.newBuilder().setPetId(1).setIndex(1).build().toByteArray()).toUpperCase());
        System.out.println(Md5Util.MD5(PbProtocol.PetSkerryModifyReq.newBuilder().setPetId(1).setIndex(2).build().toByteArray()).toUpperCase());
        System.out.println(Md5Util.MD5(PbProtocol.PetSkerryModifyReq.newBuilder().setPetId(1).setIndex(1).build().toByteArray()).toUpperCase());

//        Player onlinePlayer = PlayerManager.getOnlinePlayer(9970000001L);
//        PlayerHelper.gmDeal(onlinePlayer, "/gm addPet 600001 1");
//
//
//        ServerConstants.maxPlayerCount = 1;
//
//        Configuration.showNetMessage = true;
//
//
//        ServerConstants.packetTooManyDisconnectCountThreshold = 1000;
//
//        String accountId = "*****************";
//        String accountType = "********";
//        String hash = BaseRedisKey.PlayerKey.Account_2_PID.getRedisKey(accountId, accountType);
//        RedisAssistant redisAssistant = TLBase.getInstance().getRedisAssistant();
//        Map<String, String> roles = redisAssistant.hashGetAllString(hash);
//        for (String pid : roles.values()) {
//            String minRedisKey = BaseRedisKey.Commons.MINI_PLAYER.getRedisKey(pid);
//            byte[] bytes = redisAssistant.bytesGet(minRedisKey);
//            if (bytes != null) {
//                MiniPlayer miniPlayer = PbUtilCompress.decode(MiniPlayer.class, bytes);
//                RoleInfo roleInfo = new RoleInfo(miniPlayer);
////                log.log(roleInfo.name);
//            }
//        }

    }

}

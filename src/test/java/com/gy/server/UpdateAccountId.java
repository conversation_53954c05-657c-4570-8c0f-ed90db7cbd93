package com.gy.server;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.google.gson.reflect.TypeToken;
import com.gy.server.utils.JsonUtil;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> - [Create on 2020/02/18 10:37]
 */
public class UpdateAccountId {

    // 给绿洲提供的。结果将导出为excel
    public static final String GS_QUERY = "SELECT `id`, `level`, `vip_point`, `account_id` FROM player WHERE `account_type` = ********";

    /* *********************************绿洲信息*************************************** */
    private static final String OASIS_FILE_PATH = "E:\\myfile\\live-update\\2020.02.21\\mbsaen-new.log";
    // <绿洲playerId: List<绿洲uuid>>。注意：list只允许存在1个元素，超过1个元素，则报错（因为绿洲提供的，是uuid-playerId唯一有效的Player）
    private static final Map<String, List<String>> SDK_INFOS = new HashMap<>();

    // MySQL信息
    private static final String MYSQL_DRIVER_CLASS_NAME = "com.mysql.jdbc.Driver";
    /* *********************************gs*************************************** */
    private static final String LOCAL_GS_MYSQL_URL = "**********************************************************************************************************************************************************************************************";
    private static final String LOCAL_MYSQL_USER_NAME = "root";
    private static final String LOCAL_MYSQL_PASSWORD = "123456";

    private static final String GS_SQL_FILE_PATH = "E:\\myfile\\live-update\\2020.02.21\\update-account-id\\updatePlayerAccountId.sql";
    private static Connection connection = null;

    // <绿洲uuid: bean>，注意：uuid就是原来的account_id
    private static final Map<String, PlayerInfo> PLAYER_INFOS = new HashMap<>();
    private static final List<String> GS_SQLS = new ArrayList<>();

    /* *********************************billing*************************************** */
    private static final String LOCAL_BILLING_MYSQL_URL = "***************************************************************************************************************************************************************************************************";

    private static final String BILLING_SQL_FILE_PATH = "E:\\myfile\\live-update\\2020.02.21\\update-account-id\\updateLoginAccountId.sql";
    private static Connection billingConnection = null;

    // <绿洲uuid: bean>，只有拥有角色信息的，才会进入这个集合
    private static final Map<String, LoginInfo> LOGIN_INFOS = new HashMap<>();
    private static final List<LoginInfo> ERROR_LOGIN_INFOS = new ArrayList<>();
    private static final List<String> BILLING_SQLS = new ArrayList<>();

    public static void main(String[] args) throws Exception {
        try {
            readMbsaenTxt();
            printSdkInfo();
            checkMbsaen();
            System.out.println("读取绿洲uuid-playerId映射表完成。");

            // gs操作
            connection = getGsConnection();
            readPlayerInfo();
            createGsUpdateSql();
//            printGsSql();
//            printPlayerInfo();
            checkGs();
            gSSqlIntoFile();
            System.out.println("gs sql生成完毕。");

            // billing操作
            billingConnection = getBillingConnection();
            readLoginInfo();
            createBillingUpdateSql();
//            checkBilling();
            billingSqlIntoFile();
            System.out.println("billing sql生成完毕。");
        } finally {
            close();
        }
    }

    private static void readMbsaenTxt() throws Exception {
        InputStream inputStream = new FileInputStream(OASIS_FILE_PATH);
        InputStreamReader inputStreamReader = new InputStreamReader(inputStream);
        BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
        String line;
        while ((line = bufferedReader.readLine()) != null) {
            String[] split = line.split(" \t");
            if (!split[0].equals("#")) {
                SDK_INFOS.computeIfAbsent(split[0].trim(), playerId -> new ArrayList<>()).add(split[1].trim());
            }
        }
    }

    private static void checkMbsaen() {
        int errorNum = 0;
        int effectiveNum = 0;
        for (Map.Entry<String, List<String>> entry : SDK_INFOS.entrySet()) {
            String playerId = entry.getKey();
            List<String> uuids = entry.getValue();
            if (uuids.size() > 1) {
                System.err.println(String.format("mbsaen info error, playerId: %s, uuids: %s", playerId, uuids));
                errorNum++;
            } else {
                effectiveNum++;
            }
        }

        System.out.println(String.format("有效的绿洲映射数: %s, 错误的绿洲映射数: %s.", effectiveNum, errorNum));

        if (errorNum > 0) {
            throw new RuntimeException();
        }
    }

    private static void printSdkInfo() {
        for (Map.Entry<String, List<String>> entry : SDK_INFOS.entrySet()) {
            String playerId = entry.getKey();
            List<String> uuidList = entry.getValue();
            System.out.println(String.format("playerId: %s, uuid list: %s", playerId, uuidList));
        }
    }

    private static Connection getGsConnection() throws ClassNotFoundException, SQLException {
        Class.forName(MYSQL_DRIVER_CLASS_NAME);
        return DriverManager.getConnection(LOCAL_GS_MYSQL_URL, LOCAL_MYSQL_USER_NAME, LOCAL_MYSQL_PASSWORD);
    }

    private static void readPlayerInfo() throws Exception {
        String query = "SELECT `id`, `name`, `level`, `vip_point`, `account_id`, `account_type` FROM player WHERE `account_type` = ********";
        PreparedStatement statement = connection.prepareStatement(query);
        ResultSet resultSet = statement.executeQuery();
        while (resultSet.next()) {
            long id = Long.valueOf(resultSet.getString("id"));
            String name = resultSet.getString("name");
            int level = Integer.valueOf(resultSet.getString("level"));
            int vipPoint = Integer.valueOf(resultSet.getString("vip_point"));
            String accountId = resultSet.getString("account_id");
            int accountType = Integer.parseInt(resultSet.getString("account_type"));

            PlayerInfo playerInfo = new PlayerInfo(id, name, level, vipPoint, accountId, accountType);
            PLAYER_INFOS.put(playerInfo.accountId, playerInfo);

            if (PLAYER_INFOS.size() % 1000 == 0) {
                System.out.println("read player info size: " + PLAYER_INFOS.size());
            }
        }

        System.out.println("read player info success, size: " + PLAYER_INFOS.size());
    }

    private static void createGsUpdateSql() throws Exception {
        String sql = "UPDATE player SET account_id = \"%s\" WHERE id = %s;\n";

        for (Map.Entry<String, List<String>> entry : SDK_INFOS.entrySet()) {
            String playerId = entry.getKey();
            String uuid = entry.getValue().get(0);

            if (!StringUtils.equals(uuid, "#")) {
                PlayerInfo playerInfo = PLAYER_INFOS.get(uuid);
                if (playerInfo != null) {
                    String updateSql = String.format(sql, playerId, playerInfo.id);

                    playerInfo.setSql(updateSql);
                    playerInfo.setOasisPlayerId(playerId);

                    GS_SQLS.add(updateSql);
                } else {
                    System.err.println(String.format("uuid %s not found playerInfo.", uuid));
                }
            }
        }
    }

    private static void printGsSql() {
        for (String sql : GS_SQLS) {
            System.out.println(sql);
        }
    }

    private static void printPlayerInfo() {
        for (Map.Entry<String, PlayerInfo> entry : PLAYER_INFOS.entrySet()) {
            PlayerInfo playerInfo = entry.getValue();
            System.out.println(playerInfo);
        }
    }

    private static void checkGs() {
        List<PlayerInfo> errors = new ArrayList<>();
        for (Map.Entry<String, PlayerInfo> entry : PLAYER_INFOS.entrySet()) {
            PlayerInfo playerInfo = entry.getValue();
            if (StringUtils.isBlank(playerInfo.getSql())) {
                errors.add(playerInfo);
            }
        }

        if (!errors.isEmpty()) {
            System.err.println(String.format("player error!! has %s not found oasis playerId:", errors.size()));

            for (PlayerInfo playerInfo : errors) {
                System.err.println(playerInfo);
            }

            throw new RuntimeException();
        }
    }

    private static void gSSqlIntoFile() throws IOException {
        try (OutputStream outputStream = new FileOutputStream(GS_SQL_FILE_PATH);
             OutputStreamWriter outputStreamWriter = new OutputStreamWriter(outputStream);
             BufferedWriter writer = new BufferedWriter(outputStreamWriter)) {
            for (String sql : GS_SQLS) {
                writer.write(sql);
            }
        }
    }

    private static Connection getBillingConnection() throws ClassNotFoundException, SQLException {
        Class.forName(MYSQL_DRIVER_CLASS_NAME);
        return DriverManager.getConnection(LOCAL_BILLING_MYSQL_URL, LOCAL_MYSQL_USER_NAME, LOCAL_MYSQL_PASSWORD);
    }

    private static void readLoginInfo() throws SQLException {
        String query = "SELECT `id`, `account_type`, `account_id`, `role_info` FROM `login` WHERE `account_type` = ********";
        PreparedStatement preparedStatement = billingConnection.prepareStatement(query);

        ResultSet resultSet = preparedStatement.executeQuery();
        while (resultSet.next()) {
            // 拥有角色信息的，才需要修正
            String roleInfoStr = resultSet.getString("role_info");
            if (StringUtils.isNotBlank(roleInfoStr)) {
                int id = Integer.parseInt(resultSet.getString("id"));
                int accountType = Integer.parseInt(resultSet.getString("account_type"));
                String accountId = resultSet.getString("account_id");

                Map<Integer, RoleInfo> map = JsonUtil.json2Collection(roleInfoStr, new TypeToken<Map<Integer, RoleInfo>>() {
                }.getType());

                LoginInfo loginInfo = new LoginInfo(id, accountType, accountId);
                loginInfo.map = map;

                LOGIN_INFOS.put(loginInfo.accountId, loginInfo);
            }
        }
    }

    private static void createBillingUpdateSql() throws Exception {
        String update = "UPDATE `login` SET `account_id` = \"%s\", `role_info` = \'%s\' WHERE `id` = %s;\n";

        // 依赖于gs的Player用于更新，如果Player不存在，那就是不正确的
        for (Map.Entry<String, LoginInfo> entry : LOGIN_INFOS.entrySet()) {
            LoginInfo loginInfo = entry.getValue();
            PlayerInfo playerInfo = PLAYER_INFOS.get(loginInfo.accountId);
            if (playerInfo != null && StringUtils.isNotBlank(playerInfo.getOasisPlayerId())) {
                loginInfo.setOasisPlayerId(playerInfo.getOasisPlayerId());

                for (Map.Entry<Integer, RoleInfo> infoEntry : loginInfo.map.entrySet()) {
                    RoleInfo roleInfo = infoEntry.getValue();
                    roleInfo.accountId = playerInfo.getOasisPlayerId();
                }

                String sql = String.format(update, loginInfo.getOasisPlayerId(), JsonUtil.map2Json(loginInfo.map), loginInfo.id);

                loginInfo.setSql(sql);
                BILLING_SQLS.add(sql);
            } else {
                ERROR_LOGIN_INFOS.add(loginInfo);
            }
        }
    }

    private static void checkBilling() {
        if (!ERROR_LOGIN_INFOS.isEmpty()) {
            System.err.println(String.format("login error!! has %s not found oasis playerId:", ERROR_LOGIN_INFOS.size()));
            for (LoginInfo loginInfo : ERROR_LOGIN_INFOS) {
                System.err.println(loginInfo);
            }

            throw new RuntimeException();
        }

        boolean error = false;
        for (Map.Entry<String, LoginInfo> entry : LOGIN_INFOS.entrySet()) {
            LoginInfo loginInfo = entry.getValue();
            // 拥有角色信息才能进入到LOGIN_INFOS集合内，所以集合内如果有元素并未拥有sql语句，或者没有绿洲playerId，则视为错误
            if (StringUtils.isBlank(loginInfo.getSql()) || StringUtils.isBlank(loginInfo.getOasisPlayerId())) {
                System.err.println("found error login info, " + loginInfo);
                error = true;
            }
        }
        if (error) {
            throw new RuntimeException();
        }
    }

    private static void billingSqlIntoFile() throws IOException {
        try (OutputStream outputStream = new FileOutputStream(BILLING_SQL_FILE_PATH);
             OutputStreamWriter outputStreamWriter = new OutputStreamWriter(outputStream);
             BufferedWriter writer = new BufferedWriter(outputStreamWriter)) {
            for (String sql : BILLING_SQLS) {
                writer.write(sql);
            }
        }
    }

    private static void close() throws SQLException {
        if (connection != null && !connection.isClosed()) {
            try {
                connection.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private static class PlayerInfo {
        public final long id;
        public final String name;
        public final int level;
        public final int vipPoint;
        public final String accountId;
        public final int accountType;

        private String oasisPlayerId;
        private String sql;

        public PlayerInfo(long id, String name, int level, int vipPoint, String accountId, int accountType) {
            this.id = id;
            this.name = name;
            this.level = level;
            this.vipPoint = vipPoint;
            this.accountId = accountId;
            this.accountType = accountType;
        }

        public String getOasisPlayerId() {
            return oasisPlayerId;
        }

        public void setOasisPlayerId(String oasisPlayerId) {
            this.oasisPlayerId = oasisPlayerId;
        }

        public String getSql() {
            return sql;
        }

        public void setSql(String sql) {
            this.sql = sql;
        }

        @Override
        public String toString() {
            return "PlayerInfo{" +
                    "id=" + id +
                    ", name='" + name + '\'' +
                    ", level=" + level +
                    ", vipPoint=" + vipPoint +
                    ", accountId='" + accountId + '\'' +
                    ", accountType=" + accountType +
                    ", oasisPlayerId='" + oasisPlayerId + '\'' +
                    ", sql='" + sql + '\'' +
                    '}';
        }

    }

    private static class LoginInfo {
        public final int id;
        public final int accountType;
        public final String accountId;

        public Map<Integer, RoleInfo> map;

        private String oasisPlayerId;
        private String sql;

        public LoginInfo(int id, int accountType, String accountId) {
            this.id = id;
            this.accountType = accountType;
            this.accountId = accountId;
        }

        public String getOasisPlayerId() {
            return oasisPlayerId;
        }

        public void setOasisPlayerId(String oasisPlayerId) {
            this.oasisPlayerId = oasisPlayerId;
        }

        public String getSql() {
            return sql;
        }

        public void setSql(String sql) {
            this.sql = sql;
        }

        @Override
        public String toString() {
            return "LoginInfo{" +
                    "id=" + id +
                    ", accountType=" + accountType +
                    ", accountId='" + accountId + '\'' +
                    ", oasisPlayerId='" + oasisPlayerId + '\'' +
                    ", sql='" + sql + '\'' +
                    '}';
        }

    }

    class RoleInfo {

        public long playerId;

        public String name;

        public int level;

        public int vipLevel;

        public int iconId;

        public long lastLoginTime;

        public String accountId;

        public int accountType;

        public int serverNum;

        @Override
        public String toString() {
            return "RoleInfo{" +
                    "playerId=" + playerId +
                    ", name='" + name + '\'' +
                    ", level=" + level +
                    ", vipLevel=" + vipLevel +
                    ", iconId=" + iconId +
                    ", lastLoginTime=" + lastLoginTime +
                    ", accountId='" + accountId + '\'' +
                    ", accountType=" + accountType +
                    ", serverNum=" + serverNum +
                    '}';
        }
    }

}

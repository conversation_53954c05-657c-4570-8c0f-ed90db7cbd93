package com.gy.server;

import java.io.File;
import java.lang.reflect.Method;
import java.nio.charset.Charset;
import java.util.List;
import java.util.Map;
import java.util.Scanner;
import java.util.Set;
import java.util.Timer;
import java.util.TimerTask;
import java.util.regex.Pattern;

import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import com.google.common.io.Files;
import com.google.protobuf.AbstractMessage;
import com.google.protobuf.ByteString;
import com.google.protobuf.Descriptors;
import com.google.protobuf.Message;
import com.gy.server.game.packet.PtCode;
import com.gy.server.net.netty.codec.PacketDecoder;
import com.gy.server.net.netty.codec.gy.GyPacketEncoder;
import com.gy.server.net.util.ProtocolEncryptUtil;
import com.gy.server.packet.PbPacket;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.compressor.CompressorUtil;

import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelOption;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.codec.protobuf.ProtobufDecoder;
import io.netty.handler.codec.protobuf.ProtobufEncoder;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> - [Created on 2022-02-24 16:40]
 */
public class TestClient {
    static String protoPath = "F:\\code\\TLBB\\程序文件\\协议\\proto\\protocol.proto";
    static String accountId = "liuyuzhen";
    static int serverNum = 1902;

    static ChannelFuture channelFuture;

    static String gateIp = "tl.shangua.com";
    static int gatePort = 15001;
    static String token = "5bKnASXNC18mtjJiTJRXz0i3e";

    static long lastHeartTime = 0;

    public static void main(String[] args) throws Exception {

        readFile(protoPath);
        beforeStart();
        while (true) {
            try {
                Scanner sc = new Scanner(System.in);
                System.out.println("请输入协议号：");
                String ptCodeStr = sc.nextLine();
                int ptCode = Integer.parseInt(ptCodeStr);
                Message.Builder msgBuilder = ptCodeReqMap.get(ptCode);
                // Key Filed
                Map<String, String> filedMap = Maps.newTreeMap();
                if (msgBuilder != null) {
                    msgBuilder.clear();
                    Descriptors.Descriptor descriptor = msgBuilder.getDescriptorForType();   // 得到 descriptor
                    List<Descriptors.FieldDescriptor> fields = descriptor.getFields();
                    for (Descriptors.FieldDescriptor field : fields) {
                        filedMap.put(field.getName(), field.getJavaType().toString());
                    }
                    System.out.println("请输入请求参数，格式如下：");
                    System.out.println("参数|参数|循环参数,循环参数");
                    System.out.println("请输入：");
                    System.out.println(Joiner.on("|").join(filedMap.keySet()));
                    String paramStr = sc.nextLine();
                    Map<String, String> inputMap = Maps.newTreeMap();
                    String[] split = StringUtils.split(paramStr, "|");
                    Set<String> keySet = filedMap.keySet();
                    int i = 0;
                    for (String key : keySet) {
                        inputMap.put(key, split[i++]);
                    }
                    buildMessage(msgBuilder, inputMap);
                    send(ptCode, msgBuilder.build());
                } else {
                    send(ptCode, null);
                }

            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private static void beforeStart() throws Exception {
        NioEventLoopGroup workerGroup = new NioEventLoopGroup();
        Bootstrap bootstrap = new Bootstrap();
        bootstrap.group(workerGroup)
                .channel(NioSocketChannel.class)
                .option(ChannelOption.SO_KEEPALIVE, true)
                .handler(new ChannelInitializer<SocketChannel>() {
                    @Override
                    public void initChannel(SocketChannel ch) throws Exception {
                        ch.pipeline()
                                .addLast("CodecDecoder", new TlPacketDecoder())
                                .addLast("PbDecoder", new ProtobufDecoder(PbPacket.Packet.getDefaultInstance()))
                                .addLast("CodecEncoder", new GyPacketEncoder())
                                .addLast("PbEncoder", new ProtobufEncoder())
                                .addLast("ProtocolHandler", new SimpleClientHandler());
                    }
                });

        channelFuture = bootstrap.connect(gateIp, gatePort).sync();

        loginGate();

        // 心跳
        Timer timer = new Timer();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                checkHeart();
            }
        }, 10, 5000);
    }

    public static void send(int ptCode, Message message) {
        PbPacket.Packet.Builder packet = PbPacket.Packet.newBuilder()
                .setPtCode(ptCode);
        if (message != null) {
            packet.setData(message.toByteString());
        }

        send(packet.build());
    }

    public static void send(AbstractMessage message) {
        channelFuture.channel().writeAndFlush(message);
    }


    private static void loginGate() {
        PbProtocol.LoginCheckReq.Builder req = PbProtocol.LoginCheckReq.newBuilder();
        req.setAccountId(accountId);
        req.setAccountType(1000000);
        req.setAnonymousId("anonymousId");
        req.setPayChannel("payChannel");
        req.setDeviceId("deviceId");
        req.setDeviceType("windows");
        req.setDeviceVersion("deviceVersion");
        req.setDeviceModel("deviceModel");
        req.setDeviceManufacturer("deviceManufacturer");
        req.setDeviceResolution("deviceResolution");
        req.setToken(token);
        req.setServerNum(serverNum);
//        req.setServerNumShow(999);
        req.setVersion("2.2.2");
        req.setReConnect(false);
        req.setLanguage("1");
        req.setAdChannel("adChannel");
        req.addParams("params");
        req.setSuperLoginPlayerId(0);
        req.setClientInitVersion("clientInitVersion");
        req.setNetType("wifi");
//        req.setLoginSyncVersion(1);
        req.setSdkVersion("sdkVersion");
        req.setSdkDeviceId("sdkDeviceId");
        send(PtCode.LOGIN_CHECK_CLIENT, req.build());
    }


    public static void checkHeart() {
        long now = System.currentTimeMillis();
        if (now - lastHeartTime > 5000) {
            lastHeartTime = now;
            send(PtCode.HEART_CLIENT, null);
        }
    }

    /**
     * 处理服务端返回的数据
     *
     * <AUTHOR>
     */
    static class SimpleClientHandler extends ChannelInboundHandlerAdapter {

        @Override
        public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
            if (msg instanceof AbstractMessage) {
                PbPacket.Packet packet = (PbPacket.Packet) msg;
                // 心跳过滤
                if (packet.getPtCode() != 1006) {
                    System.out.println("服务器端返回的数据:" + packet.getPtCode());
                }

            }
        }
    }


    static class TlPacketDecoder extends PacketDecoder {

        /**
         * 魔术包头
         */
        static final byte[] magicHeader = new byte[]{'m', 'a', 'g', 'i', 'c'};

        @Override
        protected void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) throws Exception {
            try {
                //缓冲区中可能包含了多个消息包，需要循环读取，在读取失败的时候退出，设置count防止死循环
                int count = 100;
                while (count-- > 0) {
                    //记录当前读取位置
                    in.markReaderIndex();

                    //判断缓冲区可读数据是否能完整读取一个包体长度
                    if (in.readableBytes() <= (4 * 2 + magicHeader.length)) {
                        //不能或者读完包体长度无法继续读取包体，本次读取失败，不做任何读取，直接返回，等待下次调用
                        return;
                    } else {
                        //按顺序读取相应内容
                        int bodyLength = in.readInt();//包体长度
                        for (int i = 0; i < magicHeader.length; i++) {//魔术包头
                            in.readByte();
                        }
                        in.readInt();//包体长度
                        if (bodyLength > 1024 * 1024 * 2) {//包体超过2M时认为数据错误，直接断开连接
                            throw new Exception(String.format("bad packet length : %s, ip : %s", bodyLength, ctx.channel().remoteAddress()));
                        }
                        if (bodyLength < 0) {
                            throw new Exception(String.format("bodyLength is negative number length : %s, ip : %s ", bodyLength, ctx.channel().remoteAddress()));
                        }
                        //接下来要读取 压缩标记+是否加密+包体+字节m+字节a
                        if (in.readableBytes() < bodyLength + 4) {
                            //不够读取接下来的内容，重置读取位置，返回，等待下次调用
                            in.resetReaderIndex();
                            return;
                        } else {
                            byte compress = in.readByte();//压缩标记
                            byte isMagic = in.readByte();//加密标记

                            ByteBuf bodyBuf = in.readBytes(bodyLength);//包体
                            byte[] bytes = new byte[bodyLength];
                            bodyBuf.getBytes(0, bytes);
                            bodyBuf.release();
                            int ptCode = in.readInt();
                            long timeStamp = in.readLong();
                            in.readByte();//字节m
                            in.readByte();//字节a

                            if (isMagic == 1) {//解密
                                bytes = ProtocolEncryptUtil.decrypt(bytes);
                            }

                            if (compress == 1) {//解压缩
                                bytes = CompressorUtil.deflate().decompress(bytes);
                            }
                            PbPacket.Packet.Builder builder = PbPacket.Packet.newBuilder();
                            builder.setPtCode(ptCode);
                            builder.setTimeStamp(timeStamp);
                            if (bytes.length > 0) {
                                ByteString byteString = ByteString.copyFrom(bytes);
                                builder.setData(byteString);
                            }
                            out.add(builder.build());
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                throw e;
            }
        }
    }

    private static final Pattern linePattern = Pattern.compile("//\\d{3,7}\\s\\w.*");
    private static final Map<Integer, Message.Builder> ptCodeReqMap = Maps.newHashMap();
    private static final Map<Integer, Message.Builder> ptCodeRstMap = Maps.newHashMap();

    private static void readFile(String path) throws ClassNotFoundException {
        // 开始扫描协议文件
        File file = new File(path);
        if (!file.exists()) {
            System.out.println("文件不存在");
            return;
        }
        try {
            List<String> lineList = Files.readLines(file, Charset.defaultCharset());
            for (int i = 0; i < lineList.size(); i++) {
                String lineStr = lineList.get(i);
                if (linePattern.matcher(lineStr).matches()) {
                    int endSplit = lineStr.indexOf(" ");
                    int ptCode = Integer.parseInt(lineStr.substring(2, endSplit));
                    String messageStr = lineList.get(i + 1);
                    if (messageStr.indexOf("message") == 0) {
                        String messageName = messageStr.substring(7, messageStr.length() - 1).trim();
                        Class<?> aClass = Class.forName("com.gy.server.packet.PbProtocol$" + messageName);
                        Method method = aClass.getMethod("newBuilder");
                        Object obj = method.invoke(null, null);
                        Message.Builder msgBuilder = (Message.Builder) obj;       // 得到 builder
                        if (messageName.matches(".*Req")) {
                            ptCodeReqMap.put(ptCode, msgBuilder);
                        } else if (messageName.matches(".*Rst")) {
                            ptCodeRstMap.put(ptCode, msgBuilder);
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void buildMessage(Message.Builder msgBuilder, Map<String, String> map) {
        Descriptors.Descriptor descriptor = msgBuilder.getDescriptorForType();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            Descriptors.FieldDescriptor filedDescriptor = descriptor.findFieldByName(entry.getKey());
            if (filedDescriptor == null) {
                continue;
            }
            boolean isRepeated = filedDescriptor.isRepeated();
            Descriptors.FieldDescriptor.JavaType type = filedDescriptor.getJavaType();
            if (isRepeated) {
                String value = entry.getValue();
                String[] strArray = value.split(",");
                for (int i = 0; i < strArray.length; ++i) {
                    Object valueObject = getObject(strArray[i], filedDescriptor); // getObject
                    if (valueObject == null) {
                        continue;
                    }
                    msgBuilder.addRepeatedField(filedDescriptor, valueObject);
                }
            } else {
                Object valueObject = getObject(entry.getValue(), filedDescriptor);
                if (valueObject == null) {
                    continue;
                }
                msgBuilder.setField(filedDescriptor,
                        getObject(entry.getValue(), filedDescriptor));
            }
        }
    }

    private static Object getObject(String rawString,
                                    Descriptors.FieldDescriptor filedDescriptor) {
        Descriptors.FieldDescriptor.JavaType type = filedDescriptor.getJavaType();
        try {
            switch (type) {
                case INT:
                    return Integer.valueOf(rawString);
                case LONG:
                    return Long.valueOf(rawString);
                case FLOAT:
                    return Float.valueOf(rawString);
                case DOUBLE:
                    return Double.valueOf(rawString);
                case BOOLEAN:
                    return Boolean.valueOf(rawString);
                case STRING:
                    return rawString;
                case ENUM:
                    return filedDescriptor.getEnumType().findValueByName(rawString);
                default:
                    // BYTE_STRING, ENUM, MESSAGE 哈哈先支持以上这些啦
                    return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

}

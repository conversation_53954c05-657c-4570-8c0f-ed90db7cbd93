package com.gy.server.es;

import co.elastic.clients.elasticsearch._types.query_dsl.QueryBuilders;
import co.elastic.clients.elasticsearch._types.query_dsl.RangeQuery;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import com.gy.server.common.es.EsUtil;
import com.gy.server.core.Configuration;
import com.gy.server.db.search.ESManager;
import com.gy.server.db.search.IndexDocument;
import org.apache.commons.lang3.tuple.Pair;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 内网部署的kibana
 * http://xy-center.shangua.com:9091/
 * 连接的es
 * ESManager.init("xy-center.shangua.com", 9200);
 *
 * <AUTHOR> - [Created on 2022-11-01 14:56]
 */
public class EsTest {

    private static final String pgName = "tl-game";
    private static final String env = "test";

    public static String genIndexName(String moduleName){
        return ESManager.genIndexName(pgName, env, moduleName);
    }

    public static class PlayerTest extends IndexDocument implements Serializable {

        private static final long serialVersionUID = 1L;

        String name;

        int groupId;

        @Override
        public String getIndexName() {
            return genIndexName("player1");
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getGroupId() {
            return groupId;
        }

        public void setGroupId(int groupId) {
            this.groupId = groupId;
        }

        @Override
        public String toString() {
            return id + " _ " + name + " _ " + groupId;
        }
    }



    public static void main(String[] args) throws Exception {
        Configuration.esUrl = "";
        Configuration.runMode = Configuration.RunMode.test;
        EsUtil.start();

        System.out.println("es init");

        String player_key = genIndexName("player1");

//        PlayerTest playerTest = new PlayerTest();
//        playerTest.setId("1");
//        playerTest.setName("abc");
        //插入
//        boolean insert = EsUtil.insert(playerTest);
//        System.out.println(insert);
//
        //批量查询
//        List<PlayerTest> list = new ArrayList<>();
//        PlayerTest p11 = new PlayerTest();
//        p11.setId("11");
//        p11.setName("dsada1111");
//        p11.setGroupId(1);
//        list.add(p11);
//        PlayerTest p12 = new PlayerTest();
//        p12.setId("12");
//        p12.setName("aa2131231gfdg");
//        p12.setGroupId(1);
//        list.add(p12);
//        PlayerTest p21 = new PlayerTest();
//        p21.setId("13");
//        p21.setName("aaddee");
//        p21.setGroupId(2);
//        list.add(p21);
//
//        PlayerTest p22 = new PlayerTest();
//        p22.setId("12");
//        p22.setName("aabbddcc");
//        p21.setGroupId(3);
//        list.add(p22);
//        EsUtil.insertBatch(player_key, list);
//        EsUtil.update(p12);


//        SearchRequest.Builder sr = new SearchRequest.Builder();
//        sr.index(player_key).query(QueryBuilders.wildcard(builder -> {
//            builder.field("name").wildcard("a*");
//            return builder;
//        })).from(0).size(10);

//        SearchResponse<PlayerTest> search = EsUtil.getClient().search(sr.build(), PlayerTest.class);
//        for (Hit<PlayerTest> hit : search.hits().hits()) {
//
//            EsUtil.accurateDelete(player_key, hit.id());
//        }
//        List<PlayerTest> collect = search.hits().hits().stream().map(Hit::source).collect(Collectors.toList());
//        System.out.println(collect);


////        //模糊查询
//        List<PlayerTest> search = EsUtil.wildcardSearch(player_key, "name", "a*", PlayerTest.class, 0, 10);
//        System.out.println(search);
//
//        //精准搜索id
        List<Pair<String, String>> matches = new ArrayList<>();
        matches.add(Pair.of("groupId", "3"));
//        List<String> ids = EsUtil.accurateSearchId(player_key, matches, PlayerTest.class, 0, 1);
//        System.out.println(ids);
//
//        //精准删除
//        boolean b = EsUtil.accurateDelete(player_key, ids.get(0));
//        System.out.println(b);

//        List<String> ids = EsUtil.accurateSearchId(player_key, matches, PlayerTest.class, 0, 1);
//        System.out.println(ids);


        SearchRequest.Builder sr = new SearchRequest.Builder();
        RangeQuery build = QueryBuilders.range().field("groupId").from(1 + "").to(3 + "").build();
        sr.index(player_key).query(build._toQuery());




        SearchResponse<PlayerTest> search = EsUtil.getClient().search(sr.build(), PlayerTest.class);
        System.out.println(search.hits().hits());



        Thread.sleep(5000L);

        EsUtil.stop();

    }



}

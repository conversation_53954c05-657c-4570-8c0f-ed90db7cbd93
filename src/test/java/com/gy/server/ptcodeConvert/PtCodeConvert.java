package com.gy.server.ptcodeConvert;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * <AUTHOR> - [Created on 2022-03-09 16:01]
 */
public class PtCodeConvert {

    private static Map<String, Integer> oldPtCodes = new HashMap<>();
    private static Map<String, Integer> newPtCodes = new HashMap<>();
    private static Map<Integer, Integer> ptCodeConvert = new HashMap<>();

    public static void initPtCode(){
        loadPtCode("D:/tlbbWorkspace/trunk/test/protocolBak", false);
        loadPtCode("D:/tlbbWorkspace/trunk/test/protocol.proto", true);
        for (String s : newPtCodes.keySet()) {
            int newPtCode = newPtCodes.get(s);
            int oldPtCode = oldPtCodes.get(s);
            ptCodeConvert.put(oldPtCode, newPtCode);
        }
        System.out.println("newPtCodes size : " + newPtCodes.size());
        System.out.println("ptCodeConvert size : " + ptCodeConvert.size());
    }

    private static void loadPtCode(String url, boolean isNew){
        File file = new File(url);
        StringBuilder result = new StringBuilder();
        BufferedReader br = null;
        try {
            br = new BufferedReader(new InputStreamReader(new FileInputStream(file), StandardCharsets.UTF_8));//构造一个BufferedReader类来读取文件
            String line = null;
            while ((line = br.readLine()) != null) {
                //获取新文件内容
                if(line.length() > 2 && line.substring(0, 2).equals("//")){
                    if(line.split(" ").length == 3
                            && !line.contains("****")
                            && !line.contains("=====")
                            && !line.contains("--------")){
                        String[] contents = line.split(" ");
                        String ptcode = contents[0].replace("//", "");
                        String protocol = contents[1];
                        String desc = contents[2];
                        String key = protocol  + "_" + desc;
                        if(isNew){
                            newPtCodes.put(key, Integer.parseInt(ptcode));
                        }else{
                            oldPtCodes.put(key, Integer.parseInt(ptcode));
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (Objects.nonNull(br)) {
                try {
                    br.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }


    public static void main(String[] args) throws IOException {

        initPtCode();

        //开始替换文件
        File file = new File("D:/tlbbWorkspace/trunk/test/PtCode.java");
        BufferedReader br = null;
        try {
            br = new BufferedReader(new InputStreamReader(new FileInputStream(file), StandardCharsets.UTF_8));//构造一个BufferedReader类来读取文件
            String line = null;
            while ((line = br.readLine()) != null) {
                String[] split = line.split("=");
                if(split.length == 2){
                    int oldPtCode = Integer.parseInt(split[1].split(";")[0].trim());
                    if(ptCodeConvert.containsKey(oldPtCode)){
                        int newPtCode = ptCodeConvert.get(oldPtCode);
                        String result = line.replace(oldPtCode + "", newPtCode + "");
                        System.out.println(result);
                    }else{
//                        System.out.println("error ptcode : " + oldPtCode);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (Objects.nonNull(br)) {
                try {
                    br.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

    }

}

package com.gy.server.map;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class MapReadTest {


    public static void main(String[] args) {
        // 指定JSON文件路径
        String filePath = "G:\\tlbbWorkspace\\trunk\\game\\server\\tl-game\\map.txt";

        try {
            //level1名字_level2名字（直辖市没有）_level3名字
            Map<String, Map<String, String>> mapInfos = new HashMap<>();

            // 创建ObjectMapper实例
            ObjectMapper mapper = new ObjectMapper();

            JsonNode rootNode = mapper.readTree(new File(filePath));
            //读取所有直辖市和省
            JsonNode result = rootNode.get("result");
            for (JsonNode jsonNode : result) {
                String level1Name = jsonNode.get("fullname").asText();
                int level1Id = jsonNode.get("id").asInt();
                //获取下级地区
                JsonNode districts = jsonNode.get("districts");
                for (JsonNode district : districts) {
                    JsonNode districts1 = district.get("districts");
                    if(Objects.nonNull(districts1)){
                        String level2Name = district.get("fullname").asText();
                        for (JsonNode node : districts1) {
                            int level2Id = district.get("id").asInt();
                            String level3Name = node.get("fullname").asText();
                            int level3Id = node.get("id").asInt();
                            fillMapInfo(level1Id, level2Id, level3Id, level1Name, level2Name, level3Name);
                        }
                    }else{
                        //直辖市
                        String level2Name = "-1";
                        int level2Id = -1;
                        String level3Name = district.get("fullname").asText();
                        int level3Id = district.get("id").asInt();
                        fillMapInfo(level1Id, level2Id, level3Id, level1Name, level2Name, level3Name);
                    }
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    private static void fillMapInfo(int level1Id, int level2Id, int level3Id, String level1Name, String level2Name, String level3Name){
//        System.out.println(level1Id + " : " + level1Name+ " : " + level2Id + " : " + level2Name +  " : " + level3Id + " : " + level3Name);
        if(level2Id <= 0){
            //直辖市
            System.out.println(new CityInfo(-1, level1Id, level3Id, "NONE", level1Name, level3Name));
        }else{
            System.out.println(new CityInfo(level1Id,level2Id, level3Id, level1Name, level2Name, level3Name));
        }
    }


}

class CityInfo{
    public static final int country = 1;
    public String province;//省
    public String city;//市
    public String district;//区（县）
    public int provinceId;
    public int cityId;
    public int districtId;


    public CityInfo(int provinceId, int cityId, int districtId
        , String province, String city, String district){
        this.provinceId = provinceId;
        this.cityId = cityId;
        this.districtId = districtId;
        this.province = province;
        this.city = city;
        this.district = district;
    }

    public boolean isDirectCity(){
        return provinceId <= 0;
    }

    @Override
    public String toString() {
        return provinceId + " : " + province+ " : " + cityId + " : " + city +  " : " + districtId + " : " + district;
    }
}


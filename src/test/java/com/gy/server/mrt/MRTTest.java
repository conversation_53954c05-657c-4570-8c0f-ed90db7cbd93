package com.gy.server.mrt;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.core.Configuration;
import com.gy.server.core.delay.MessageSystemHashInvoke;
import com.gy.server.core.delay.MessageSystemSyncInvoke;
import com.gy.server.db.nosql.redis.GyBatch;
import com.gy.server.db.nosql.redis.GyRedisTop;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.serialize.RedisAssistant;

import java.util.*;
import java.util.concurrent.ExecutionException;

/**
 * @author: gbk
 * @date: 2025-02-17 20:39
 */
public class MRTTest {

    public static void main(String[] args) throws Exception {

        initRedis();

        System.out.println("test start");
        resetTime();
//        initHashTest();
        printTime();

        //开始测试
        resetTime();
        testZSet1();
        printTime();

        System.out.println("test end");
    }

    private static void initHashTest(){
        int count = 5000000;
        String redisKey = "test_hash_key";
        RedisAssistant ra = TLBase.getInstance().getRedisAssistant();
        for (int i = 0; i < count; i++) {
            if(i % 100000 == 0){
                try {
                    System.out.println("init test memory,  count:" + i);
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
            int key = i + 1;
            int score = i + 1;
            ra.hashPut(redisKey, key + "", score + "");
        }
        long resultCount = ra.rankCount(redisKey);
        System.out.println("init test memory success,  count:" + resultCount);
    }

    private static void testHash(){
        String redisKey = "test_hash_key";
        RedisAssistant ra = TLBase.getInstance().getRedisAssistant();
        long count = ra.hashLength(redisKey);
        int pageSize = 100000;
        List<String> keys = new ArrayList<>();
        for (int i = 1; i <= count; i++) {
            keys.add(i + "");
            if(keys.size() == pageSize){
                Map<String, byte[]> stringMap = ra.hashGetBytes(redisKey, keys);
//                System.out.println("i : " + i +", hash get count:" + stringMap.size());
                keys.clear();
                try {
                    Thread.sleep(10L);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }

        }
//        System.out.println("all count : " + count);
    }
    private static void testHash2(){
        String redisKey = "test_hash_key";
        RedisAssistant ra = TLBase.getInstance().getRedisAssistant();
        long count = ra.hashLength(redisKey);
        int pageSize = 100000;
        int forCount = (int)(count / 100000);
        for (int i = 0; i < forCount; i++ ){
            GyBatch batch = ra.createBatch();
            for (int j = 0; j < pageSize; j++){
                int key = (i * pageSize) + j + 1;
                batch.hashGet(redisKey, key + "");
            }
            try {
                List<Object> execute = batch.execute();
//                System.out.println("batch execute count:" + execute.size());
                Thread.sleep(10L);
            } catch (ExecutionException | InterruptedException e) {
                throw new RuntimeException(e);
            }
        }

    }

    private static void testHash3(){
        String redisKey = "test_hash_key";
        RedisAssistant ra = TLBase.getInstance().getRedisAssistant();
        long count = ra.hashLength(redisKey);
        int pageSize = 100000;
        int forCount = (int)(count / 100000);
        for (int i = 0; i < forCount; i++ ){
            GyBatch batch = ra.createBatch();
            Set<String> keys = new HashSet<>();
            for (int j = 0; j < pageSize; j++){
                int key = (i * pageSize) + j + 1;
                keys.add( key + "");
            }
            batch.hashGetAll(redisKey, keys);
            try {
                List<Object> execute = batch.execute();
//                System.out.println("batch execute count:" + execute.size());
                Thread.sleep(10L);
            } catch (ExecutionException | InterruptedException e) {
                throw new RuntimeException(e);
            }
        }

    }


    private static void testZSet(){
        RedisAssistant ra = TLBase.getInstance().getRedisAssistant();
        String redisKey = GsRedisKey.MountainRiverTournament.level_rank.getRedisKey(warZoneId + "");
        System.out.println(ra.rankCount(redisKey));
        //500W数据，10W取一次
        long count = ra.rankCount(redisKey);
        int pageSize = 100000;
        int pageCount = (int) (count / pageSize) + 1;
        for (int i = 1; i <= pageCount; i++) {
            int startRank = (i - 1) * pageSize + 1;
            int endRank = i * pageSize;
            List<GyRedisTop> gyRedisTops = ra.rankRange(redisKey, startRank, endRank, false);
            System.out.println("startRank:" + startRank + ", endRank:" + endRank + ", count:" + gyRedisTops.size());
            try {
                Thread.sleep(10);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }

    }

    private static void testZSet1(){
        RedisAssistant ra = TLBase.getInstance().getRedisAssistant();
        String redisKey = GsRedisKey.MountainRiverTournament.level_rank.getRedisKey(warZoneId + "");
        System.out.println(ra.rankCount(redisKey));

        int pageSize = 100000;
        int maxLoopCount = 10000000;
        for(int i = 0; i < maxLoopCount; i++){
            int offset = i * pageSize;
            List<GyRedisTop> gyRedisTops = ra.rankRangeByScore(redisKey, 1000000L, 3000000L, offset, pageSize);
            if(gyRedisTops.size() != pageSize){
                break;
            }
            System.out.println("i:" + i + ", count:" + gyRedisTops.size() + ", startRank:" + gyRedisTops.get(0).getScore());
            try {
                Thread.sleep(10);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }

        long count = ra.rankCount(redisKey);

        int pageCount = (int) (count / pageSize) + 1;
        for (int i = 1; i <= pageCount; i++) {
            int startRank = (i - 1) * pageSize + 1;
            int endRank = i * pageSize;

        }




    }


    private static void initTestMemory(){
        //准备一千万数据
        int count = 10000000;
        String redisKey = GsRedisKey.MountainRiverTournament.level_rank.getRedisKey(warZoneId + "");
        RedisAssistant ra = TLBase.getInstance().getRedisAssistant();
        for (int i = 0; i < count; i++) {
            if(i % 100000 == 0){
                try {
                    System.out.println("init test memory,  count:" + i);
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
            int key = i + 1;
            int score = i + 1;
            ra.rankPut(redisKey, key + "", score);
        }
        long resultCount = ra.rankCount(redisKey);
        System.out.println("init test memory success,  count:" + resultCount);
    }

    static long time;
    static int warZoneId = 999;

    private static void resetTime(){
        time = System.currentTimeMillis();
    }

    private static void printTime(){
        System.out.println("cost time:" + (System.currentTimeMillis() - time));
    }

    private static void initRedis() throws Exception {
        Configuration.init("");
//        LoggerManager.init();
        Configuration.serverId = 998;
        Configuration.startupMode = Configuration.StartupMode.xml;
        TLBase.getInstance().init(Configuration.serverId, ServerType.GAME, "game", Configuration.startupMode.name(), MessageSystemSyncInvoke.getInstance(), MessageSystemHashInvoke.getInstance());
    }

}
package com.gy.server.ChineseChess;

import com.gy.server.common.redis.RedisScript;
import com.gy.server.core.Configuration;
import com.gy.server.core.delay.MessageSystemHashInvoke;
import com.gy.server.core.delay.MessageSystemSyncInvoke;
import com.gy.server.db.nosql.redis.GyRedisTop;
import com.gy.server.game.activity.chineseChess.ChineseChessActivityHelper;
import com.gy.server.game.warZone.WarZoneService;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.serialize.RedisAssistant;

import java.util.List;

public class CCTest {


    public static void main(String[] args) throws Exception {

        Configuration.init("");
        Configuration.serverId = 997;
        Configuration.startupMode = Configuration.StartupMode.xml;
        TLBase.getInstance().init(Configuration.serverId, ServerType.GAME, "game", Configuration.startupMode.name(), MessageSystemSyncInvoke.getInstance(), MessageSystemHashInvoke.getInstance());
//        new ActivityService().systemStartup();
        new WarZoneService().systemStartup();
        RedisScript.init();

        System.out.println("start : !!!!!!!!!");

        RedisAssistant redisAssistant = TLBase.getInstance().getRedisAssistant();
        int activityId = 20103;
        String redisRankKey = ChineseChessActivityHelper.getRedisRankKey(activityId);
        redisAssistant.del(redisRankKey);
        ChineseChessActivityHelper.addScore(activityId, 9970000001L, 1, false);
        ChineseChessActivityHelper.addScore(activityId, 9970000002L, 1, true);
        ChineseChessActivityHelper.addScore(activityId, 9970000003L, 10, false);
        ChineseChessActivityHelper.addScore(activityId, 9970000004L, 10, true);

        Thread.sleep(3 * 1000L);
        List<GyRedisTop> gyRedisTops = ChineseChessActivityHelper.genSourceRankNodeList(activityId, 100);
        for (GyRedisTop gyRedisTop : gyRedisTops) {
            int rank = gyRedisTop.getRank();
            String rankInfo = gyRedisTop.getScore().toString();
            long score = gyRedisTop.getScore().longValue();
            long memberId = Long.parseLong(gyRedisTop.getValue());
            System.out.println(rank + " : " + rankInfo + " : " + score + " : " + memberId);
        }
    }

}

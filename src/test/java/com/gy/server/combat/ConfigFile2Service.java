package com.gy.server.combat;

import java.util.List;

import com.gy.server.common.util.FindUsagesUtil;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.utils.CollectionUtil;

/**
 * <AUTHOR> - [Created on 2024-03-16 11:07]
 */
public class ConfigFile2Service {


    public static void main(String[] args) {
        FindUsagesUtil.init();
        for (ConfigFile configFile : ConfigFile.values()) {
            String name = configFile.name();
            List<String> usages = FindUsagesUtil.findUsages(name);
            if(CollectionUtil.isNotEmpty(usages)){
                System.out.println(usages.get(0) + "       ---       " + name);
            }
        }
    }

}

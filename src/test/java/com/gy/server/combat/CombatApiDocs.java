package com.gy.server.combat;

import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.serialize.RedisAssistant;

//import io.github.yedaxia.apidocs.Docs;
//import io.github.yedaxia.apidocs.DocsConfig;

/**
 * 战斗接口文档生成工具
 * <AUTHOR> - [Created on 2022-06-24 9:36]
 */
public class CombatApiDocs {

    public static void main(String[] args) {
//        DocsConfig config = new DocsConfig();
//        // 项目根目录
//        config.setProjectPath("G:\\tlbbWorkspace\\trunk\\game\\server\\tl-game");
//        // 项目名称
//        config.setProjectName("tl-game");
//        // 声明该API的版本
//        config.setApiVersion("combatApi");
//        // 生成API 文档所在目录
//        config.setDocsPath("G:\\tlbbWorkspace\\trunk\\game\\data\\scripts\\src\\main\\resource");
//        // 配置自动生成
//        config.setAutoGenerate(Boolean.FALSE);
//
//        config.setMvcFramework("generic");
//
//        // 执行生成文档
//        Docs.buildHtmlDocs(config);

    }

    public static void xpendingTest(){
        RedisAssistant redisAssistant = TLBase.getInstance().getRedisAssistant();
        String luaScript = "local pending = redis.call('xpending', 'msg_all_GS', 998, '-', '+', 998)\n" +
                "return #pending";
        redisAssistant.scriptLoad("msg_all_GS", luaScript);
    }

}

package com.gy.server.combat;

import com.gy.server.game.player.PlayerModel;
import com.gy.server.utils.ReflectionUtil;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.Set;

/**
 * @author: gbk
 * @date: 2024-07-29 19:30
 */
public class FAbstractMemberFields {


    public static void main(String[] args) {

        Set<Class<? extends PlayerModel>> subClassSet = ReflectionUtil.getSubClassSetNoInterfaceAndAbstract(PlayerModel.class
                , "com.gy.server.game");
        for (Class<? extends PlayerModel> clazz : subClassSet) {
            int size = clazz.getDeclaredFields().length;
            for (Field declaredField : clazz.getDeclaredFields()) {
                if(Modifier.isStatic(declaredField.getModifiers())){
                    size--;
                }
            }
            System.out.println(clazz.getName() + "  :  " + size);
        }


    }

}
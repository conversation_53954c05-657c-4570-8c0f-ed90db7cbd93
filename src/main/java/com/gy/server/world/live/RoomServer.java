package com.gy.server.world.live;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.ttlike.server.tl.baselib.ServerType;

/**
 * @program: tl_game_4_live
 * @description:
 * @author: <PERSON><PERSON>
 * @create: 2025/1/12
 **/
public class RoomServer {

    @Protobuf(order = 1)
    private ServerType serverType;

    @Protobuf(order = 2)
    private int serverId;

    public RoomServer(ServerType serverType, int serverId) {
        this.serverType = serverType;
        this.serverId = serverId;
    }

    public RoomServer() {
    }

    public ServerType getServerType() {
        return serverType;
    }

    public void setServerType(ServerType serverType) {
        this.serverType = serverType;
    }

    public int getServerId() {
        return serverId;
    }

    public void setServerId(int serverId) {
        this.serverId = serverId;
    }
}

package com.gy.server.world.live;

import com.baidu.bjf.remoting.protobuf.annotation.Ignore;
import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.google.protobuf.InvalidProtocolBufferException;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.core.Configuration;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.cos.COSManager;
import com.gy.server.core.log.SystemLogger;
import com.gy.server.game.combat.StageType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.text.Text;
import com.gy.server.game.tournament.TournamentService;
import com.gy.server.packet.PbLive;
import com.gy.server.packet.PbProtocol;
import com.ttlike.server.tl.baselib.TLBase;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @program: tl_game_4
 * @description: 直播间
 * @author: <PERSON>.<PERSON>
 * @create: 2025/1/9
 **/
public class LiveRoom {

    @Protobuf(order = 1)
    private long id; // 直播间ID
    @Protobuf(order = 2)
    private String path; // 直播间地址
    @Protobuf(order = 3)
    private int stageType; // 玩法类型
    @Protobuf(order = 4)
    private long startTime; // 直播开始时间
    @Protobuf(order = 5)
    private byte[] extData; // 扩展数据
    @Protobuf(order = 6)
    private RoomServer server; // 负责写入直播数据的服务器信息

    @Ignore
    PbLive.TournamentLiveRecord tournament;

    @Ignore
    private AtomicInteger index = new AtomicInteger(0);//当前序号
    @Ignore
    private volatile int lastNodeId;//最后一次数据来源节点，用于判断
    @Ignore
    private volatile long lastNodeIndex;//最后一次数据来源节点分配的序号

    public LiveRoom(long id, String path, int stageType) {
        this.id = id;
        this.path = path;
        this.stageType = stageType;
        this.startTime = ServerConstants.getCurrentTimeMillis();

        this.server = new RoomServer(TLBase.getInstance().getCurNodeType(), Configuration.serverId);
    }

    public LiveRoom(){}

    public void init(){
        try {
            StageType type = StageType.getStageTypeById(this.stageType);
            switch (type) {
                case PVPRealTime: {
                    this.tournament = PbLive.TournamentLiveRecord.parseFrom(this.extData);
                }
                default: {}
            }
        } catch (InvalidProtocolBufferException e) {
            SystemLogger.error("Parse extData failed : " + e.getMessage());
        }
    }

    public void beforeSave(){
        try {
            StageType type = StageType.getStageTypeById(this.stageType);
            switch (type) {
                case PVPRealTime: {
                    this.extData = this.tournament.toByteArray();
                }
                default: {}
            }
        } catch (Exception e) {
            SystemLogger.error("Save extData failed : " + e.getMessage());
        }
    }

    public void removeFromRedis(){
        TLBase.getInstance().getRedisAssistant().hashRemove(GsRedisKey.Tournament.live_list.getRedisKey(), id+"");
        String key = GsRedisKey.Live.live_count.getRedisKey(id);
        TLBase.getInstance().getRedisAssistant().delAsync(key);
    }


    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public RoomServer getServer() {
        return server;
    }

    public void setServer(RoomServer server) {
        this.server = server;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public int getStageType() {
        return stageType;
    }

    public void setStageType(int stageType) {
        this.stageType = stageType;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public byte[] getExtData() {
        return extData;
    }

    public void setExtData(byte[] extData) {
        this.extData = extData;
    }

    public AtomicInteger getIndex() {
        return index;
    }

    public void setIndex(AtomicInteger index) {
        this.index = index;
    }

    public int getLastNodeId() {
        return lastNodeId;
    }

    public void setLastNodeId(int lastNodeId) {
        this.lastNodeId = lastNodeId;
    }

    public long getLastNodeIndex() {
        return lastNodeIndex;
    }

    public void setLastNodeIndex(long lastNodeIndex) {
        this.lastNodeIndex = lastNodeIndex;
    }

    public PbLive.TournamentLiveRecord getTournament() {
        return tournament;
    }

    public void setTournament(PbLive.TournamentLiveRecord tournament) {
        this.tournament = tournament;
    }

    /**
     * 耗时操作，需要异步调用
     * 最多尝试5次，失败后输出日志
     */
    public synchronized void addData(int nodeId, long nodeIndex, int ptCode, byte[] data){
        if(nodeId == lastNodeId && nodeIndex <= lastNodeIndex){
            //重复添加
            return;
        }

        lastNodeId = nodeId;
        lastNodeIndex = nodeIndex;

        byte[] liveData = LiveHelper.genLiveData(ptCode, data);
        int index = this.index.getAndIncrement();

        int retryCount = 5;
        while (retryCount-- > 0) {
            boolean rst = COSManager.getInstance().putObjectSync(path+index, liveData);
            if (rst) {
                break;
            }

            try {
                //暂停然后重试
                TimeUnit.SECONDS.sleep(3);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }

            if (retryCount == 0 && !rst) {
                //最后一次机会还是失败
                SystemLogger.error("Live data record failed : path = " + path + ", index = " + index);
            }
        }
    }

    public void writeState(PbLive.LiveData.LiveDataType type){
        PbLive.LiveData data =PbLive.LiveData.newBuilder()
               .setType(type)
               .build();

        PbProtocol.LiveDataPushRst.Builder b = PbProtocol.LiveDataPushRst.newBuilder();
        b.setResult(Text.genOkServerRstInfo())
                .addData(data);

        addData(Configuration.serverId, LiveRoomManager.getInstance().getIndexGenerator().incrementAndGet(),
                PtCode.LIVE_DATA_PUSH_RST, b.build().toByteArray());
    }

    public void writeViewCount(int count){
        PbLive.LiveData data =PbLive.LiveData.newBuilder()
                .setType(PbLive.LiveData.LiveDataType.VIEWER_COUNT_CHANGE)
                .setViewerCount(count)
                .build();

        PbProtocol.LiveDataPushRst.Builder b = PbProtocol.LiveDataPushRst.newBuilder();
        b.setResult(Text.genOkServerRstInfo())
                .addData(data);

        addData(Configuration.serverId, LiveRoomManager.getInstance().getIndexGenerator().incrementAndGet(),
                PtCode.LIVE_DATA_PUSH_RST, b.build().toByteArray());
    }

    public boolean isLiveOver(){
        return ServerConstants.getCurrentTimeMillis() - startTime
                > TournamentService.getTournamentConst().liveRoomMaxTime;
    }

    public PbLive.LiveRoom.Builder toPbBuilder(int viewerCount){
        PbLive.LiveRoom.Builder builder = PbLive.LiveRoom.newBuilder();
        builder.setId(id);
        builder.setPath(path);
        builder.setStageType(stageType);
        builder.setStartTime(startTime);
        builder.setViewerCount(viewerCount);
        if(this.tournament != null){
            builder.setTournamentRecord(tournament);
        }
        return builder;
    }
}

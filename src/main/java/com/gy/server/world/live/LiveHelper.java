package com.gy.server.world.live;

import com.google.protobuf.AbstractMessage;
import com.google.protobuf.ByteString;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.core.Configuration;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.callback.response.CallbackResponse;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.core.cos.COSManager;
import com.gy.server.core.log.SystemLogger;
import com.gy.server.db.cache.guava.GuavaCacheUtil;
import com.gy.server.game.combat.Stage;
import com.gy.server.game.combat.StageType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.text.Text;
import com.gy.server.game.tournament.TournamentHelper;
import com.gy.server.game.tournament.TournamentService;
import com.gy.server.game.tournament.bean.TournamentMatchEnum;
import com.gy.server.game.tournament.stage.TournamentStage;
import com.gy.server.packet.*;
import com.gy.server.utils.LockUtil;
import com.gy.server.utils.Md5Util;
import com.gy.server.utils.net.HttpUtil;
import com.gy.server.utils.time.DateTimeFormatterType;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.CommonsConfiguration;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import com.ttlike.server.tl.baselib.rpc.TLMessageCallbackTask;

import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * @program: tl_game_4
 * @description:
 * @author: Huang.Xia
 * @create: 2025/1/9
 **/
public class LiveHelper {
    private static final String CACHE_ROOMS = "live_rooms";
    private static final String CACHE_ROOM_SERVER = "live_server";
    static {
        //房间列表，5秒有效期
        GuavaCacheUtil.addCache(CACHE_ROOMS, 5);
        //房间所在节点信息，1小时有效期
        GuavaCacheUtil.addCache(CACHE_ROOM_SERVER, 1*60*60);
    }

    public static Map<Long, Integer> getLiveViewerCount(Set<Long> liveIds) {

        return null;
    }

    /**
     * @param message 原始业务数据
     */
    public static int writeLiveData(long roomId, int ptCode, AbstractMessage message){
        //先检查是否本节点
        LiveRoom liveRoom = LiveRoomManager.getInstance().getLiveRooms().get(roomId);
        if(liveRoom != null){
            liveRoom.addData(Configuration.serverId, LiveRoomManager.getInstance().getIndexGenerator().incrementAndGet(), ptCode, message.toByteArray());
        }else{
            RoomServer roomServer = null;
            Object obj = GuavaCacheUtil.get(CACHE_ROOM_SERVER, roomId);
            if(obj != null){
                roomServer = (RoomServer) obj;
            }else{
                LiveRoom room = TLBase.getInstance().getRedisAssistant().hashGetBeans(GsRedisKey.Tournament.live_list.getRedisKey(), roomId+"",LiveRoom.class);
                if(room != null){
                    roomServer = room.getServer();
                    GuavaCacheUtil.put(CACHE_ROOM_SERVER, roomId, roomServer);
                }
            }

            if(roomServer == null){
                return Text.直播已结束;
            }

            Counter counter = new Counter(3);
            writeCheck(counter, roomServer, roomId, ptCode, message.toByteArray(), LiveRoomManager.getInstance().getIndexGenerator().incrementAndGet());
        }
        return Text.没有异常;
    }

    /**
     * 按照指定的次数重试，直到成功
     */
    private static void writeCheck(Counter counter, RoomServer roomServer, long roomId, int ptCode, byte[] data, long nodeIndex){
        ServerCommandRequest req = CommandRequests.newServerCommandRequest("LiveCommandService.writeLiveData");
        TLBase.getInstance().getRpcUtil().sendToNodeWithCallBack(new WriteLiveDataMessageCallbackTask(counter, roomServer, roomId, ptCode, data, nodeIndex), roomServer.getServerType(), roomServer.getServerId(), req, roomId, ptCode, data, Configuration.serverId, nodeIndex);
    }

    public static void liveRoomCreateCheck(Stage stage) {
        if(stage instanceof TournamentStage){
            LiveRoom liveRoom = createTournamentLiveRoom((TournamentStage) stage);
            if(liveRoom != null){
                LiveRoomManager.getInstance().addLiveRoom(liveRoom);
            }
        }
    }

    /**
     * 检查是否符合直播间创建条件，负责则创建并给Stage添加标记
     * 条件：
     * 1. 征召开放则只允许创建征召直播间
     * 2. 限制直播间数量
     */
    private static LiveRoom createTournamentLiveRoom(TournamentStage stage) {
        try {
            //机器人不直播
            if (stage.getDefMatchInfo().isRobotA() || stage.getDefMatchInfo().isRobotB()) {
                return null;
            }

            //类型判断
            if (TournamentHelper.haveCallUpOpen()
                    && stage.getMatchEnum() != TournamentMatchEnum.callUp) {
                return null;
            }

            long size = TLBase.getInstance().getRedisAssistant().hashLength(GsRedisKey.Tournament.live_list.getRedisKey());
            if (size < TournamentService.getTournamentConst().liveMaxCount) {
                PbRecord.RecordPlayer atkPlayer = stage.getAtkRecordPlayer();
                atkPlayer = tournamentPlayerLineupModify(atkPlayer, stage.getAtkSelect());
                PbRecord.RecordPlayer defPlayer = stage.getDefRecordPlayer();
                defPlayer = tournamentPlayerLineupModify(defPlayer, stage.getDefSelect());

                long roomId = TLBase.getInstance().getRedisAssistant().increamentId(GsRedisKey.Live.live_id.getRedisKey());
                LiveRoom liveRoom = new LiveRoom(roomId, genLivePath(roomId, stage.getStageType()), stage.getStageType().getId());
                int tierId = TournamentService.getDanByStar(stage.getAtkMatchInfo().getStar());
                PbLive.TournamentLiveRecord.Builder extData = PbLive.TournamentLiveRecord.newBuilder()
                        .setAtk(atkPlayer)
                        .setDef(defPlayer)
                        .setTierId(tierId);
                liveRoom.setTournament(extData.build());
                liveRoom.beforeSave();

                TLBase.getInstance().getRedisAssistant().hashPut(GsRedisKey.Tournament.live_list.getRedisKey(), liveRoom.getId() + "", liveRoom);
                stage.setLiveRoom(liveRoom);
                return liveRoom;
            }
        }catch (Exception e){
            SystemLogger.error(e);
        }

        return null;
    }

    private static PbRecord.RecordPlayer tournamentPlayerLineupModify(PbRecord.RecordPlayer atkPlayer, int select){
        if(atkPlayer.getLineup().getLineupBaseInfoPbCount() > 1){
            //多选一，只保留使用的阵容
            PbCommons.LineupStandBaseInfoPb stand = atkPlayer.getLineup();
            PbCommons.LineupBaseInfoPb lineup = atkPlayer.getLineup().getLineupBaseInfoPb(select);
            PbRecord.RecordPlayer.Builder bd = atkPlayer.toBuilder();
            bd.setLineup(PbCommons.LineupStandBaseInfoPb.newBuilder().setType(stand.getType())
                    .setTotalFightPower(stand.getTotalFightPower())
                    .addLineupBaseInfoPb(lineup));
            atkPlayer = bd.build();
        }
        return atkPlayer;
    }

    public static void modifyViewCount(long liveId, int count){
        String key = GsRedisKey.Live.live_count.getRedisKey(liveId);
        TLBase.getInstance().getRedisAssistant().increamentIdBy(key, count);
    }

    public static List<PbLive.LiveRoom> getTournamentLiveRooms() {
        Object obj = GuavaCacheUtil.get(CACHE_ROOMS, "tournament_live_rooms");
        if (obj != null) {
            return (List<PbLive.LiveRoom>) obj;
        }

        synchronized (LockUtil.getLockKey("tournament_live_rooms")) {
            obj = GuavaCacheUtil.get(CACHE_ROOMS, "tournament_live_rooms");
            if (obj != null) {
                return (List<PbLive.LiveRoom>) obj;
            }

            Map<String, LiveRoom> liveRooms = TLBase.getInstance().getRedisAssistant().hashGetAllBeans(GsRedisKey.Tournament.live_list.getRedisKey(), LiveRoom.class);
            List<LiveRoom> result = new ArrayList<>();
            for (LiveRoom liveRoom : liveRooms.values()) {
                liveRoom.init();

                if (!liveRoom.isLiveOver()) {
                    result.add(liveRoom);
                } else {
                    //过期的直播间删除
                    liveRoom.removeFromRedis();
                }
            }

            String[] keys = new String[result.size()];
            for (int i = 0; i < result.size(); i++) {
                keys[i] = GsRedisKey.Live.live_count.getRedisKey(result.get(i).getId());
            }
            Map<String, String> counts = TLBase.getInstance().getRedisAssistant().mgetString(keys);

            List<PbLive.LiveRoom> resultBuilder = new ArrayList<>();
            for(LiveRoom liveRoom : result){
                String count = counts.get(GsRedisKey.Live.live_count.getRedisKey(liveRoom.getId()));
                if(count == null){
                    count = "0";
                }

                int viewCount = Integer.parseInt(count);
                if(viewCount < 0){
                    viewCount = 0;
                }

                resultBuilder.add(liveRoom.toPbBuilder(viewCount).build());
            }

            if(result.size() > 0) {
                GuavaCacheUtil.put(CACHE_ROOMS, "tournament_live_rooms", result);
            }
            return resultBuilder;
        }
    }

    public static String genLivePath(long roomId, StageType stageType) {
        String cosPath = COSManager.genPrefix(CommonsConfiguration.envName, "live") + stageType.name() + "/" + roomId + "/" + genRandomPrefix(4) + ".";
        return cosPath.toLowerCase();
    }

    /**
     * 生成随机字母前缀
     */
    public static String genRandomPrefix(int length) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append((char) (int) (Math.random() * 26 + 'a'));
        }
        return sb.toString();
    }

    public static byte[] genLiveData(int ptcode, byte[] bytes) {
        try {
            PbPacket.Packet.Builder packet = PbPacket.Packet.newBuilder();
            packet.setData(ByteString.copyFrom(bytes))
                    .setPtCode(ptcode)
                    .setTimeStamp(ServerConstants.getCurrentTimeMillis());

            byte[] data = packet.build().toByteArray();
            String md5 = Md5Util.MD5(data).toLowerCase();

            ByteBuf byteBuf = Unpooled.buffer();
            try {
                byteBuf.writeBytes(data);
                byteBuf.writeBytes(md5.getBytes(StandardCharsets.UTF_8));
                byteBuf.writeBytes("!majiang0".getBytes(StandardCharsets.UTF_8));//结束标志
                byte[] rst = new byte[byteBuf.readableBytes()];
                byteBuf.readBytes(rst);
                return rst;
            } finally {
                byteBuf.release();
            }
        } catch (Exception e) {
            SystemLogger.error(e);
            return null;
        }
    }

    public static void main(String[] args) {

        analyseLiveData("https://tl-1251093490.cos.ap-shanghai.myqcloud.com/live/pvprealtime/113/edze.");
    }

    public static void analyseLiveData(String path){
        for(int i = 0 ; i < 300; i++){
            String url = path + i;
            //download file from url
            try {
                byte[] bytes = HttpUtil.requestHttpsWithGetReturnBytes(url);
                if(bytes != null && bytes.length > 0){
                    System.out.print(" index " + i + " ::  ");
                    //尝试解包
                    byte[] data = Arrays.copyOfRange(bytes, 0, bytes.length - 41);
                    PbPacket.Packet packet = PbPacket.Packet.parseFrom(data);
                    String time = DateTimeFormatterType.date_time_millis.toString(DateTimeUtil.toLocalDateTime(packet.getTimeStamp()));
                    if(packet.getPtCode() == PtCode.LIVE_DATA_PUSH_RST){
                        PbProtocol.LiveDataPushRst rst = PbProtocol.LiveDataPushRst.parseFrom(packet.getData());
                        for(PbLive.LiveData liveData : rst.getDataList()){
                            if(liveData.getType() == PbLive.LiveData.LiveDataType.CHAT_MSG){
                                for(String chat : liveData.getChatMsgList()) {
                                    System.out.println(" " + time + " " + liveData.getType() + " " + chat);
                                }
                            }else if(liveData.getType() == PbLive.LiveData.LiveDataType.VIEWER_COUNT_CHANGE){
                                System.out.println(" " + time + " " + liveData.getType() + " " + liveData.getViewerCount());
                            }else{
                                System.out.println(" " + time + " " + liveData.getType());

                                if (liveData.getType() == PbLive.LiveData.LiveDataType.STATE_CLOSED
                                        || liveData.getType() == PbLive.LiveData.LiveDataType.STATE_EXCEPTION_CLOSED) {
                                    System.out.println("room " + path + " index " + i + " closed");
                                    return;
                                }
                            }
                        }
                    }else {
                        System.out.println(" " + time + " " + packet.getPtCode());
                    }

                }else{
                    System.out.println("room " + path + " index " + i + " 404");
                    break;
                }
            }catch (Exception e){
                e.printStackTrace();
            }
        }
    }

    private static class WriteLiveDataMessageCallbackTask extends TLMessageCallbackTask {
        private final Counter counter;
        private final RoomServer roomServer;
        private final long roomId;
        private final int ptCode;
        private final byte[] data;
        private final long nodeIndex;

        public WriteLiveDataMessageCallbackTask(Counter counter, RoomServer roomServer, long roomId, int ptCode, byte[] data, long nodeIndex) {
            this.counter = counter;
            this.roomServer = roomServer;
            this.roomId = roomId;
            this.ptCode = ptCode;
            this.data = data;
            this.nodeIndex = nodeIndex;
        }

        @Override
        public void complete(CallbackResponse response) {
            //成功不做任何处理
        }

        @Override
        public void timeout() {
            if(counter.count > 0){
                counter.count --;

                writeCheck(counter, roomServer, roomId, ptCode, data, nodeIndex);
            }
        }

    }
}

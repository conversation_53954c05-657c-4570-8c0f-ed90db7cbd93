package com.gy.server.world.live;

import com.gy.server.annotation.MessageMethod;
import com.gy.server.core.command.CommandRequestParams;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.text.Text;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

/**
 * @program: tl_game_4_live
 * @description:
 * @author: <PERSON><PERSON>
 * @create: 2025/1/12
 **/
public class LiveCommandService {
    @MessageMethod(description = "写入直播数据")
    private static void writeLiveData(ServerCommandRequest request, CommandRequestParams params) {
        long roomId = params.getParam(0);
        int ptCode = params.getParam(1);
        byte[] data = params.getParam(2);
        int nodeId = params.getParam(3);
        long nodeIndex = params.getParam(4);

        LiveRoom liveRoom = LiveRoomManager.getInstance().getLiveRooms().get(roomId);
        if(liveRoom != null){
            ThreadPool.execute(() -> {
                liveRoom.addData(nodeId, nodeIndex, ptCode, data);
            });
        }
        request.addCallbackParam(Text.没有异常);
    }
}

package com.gy.server.world.live;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.tournament.TournamentService;
import com.gy.server.packet.PbLive;
import com.gy.server.utils.function.Ticker;
import com.gy.server.utils.runner.Runner;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * @program: tl_game_4
 * @description: 直播间管理器, 负责管理本节点负责的直播间
 * @author: Huang.<PERSON>a
 * @create: 2025/1/10
 **/
public class LiveRoomManager implements Ticker, Runner {

    private static final LiveRoomManager INSTANCE = new LiveRoomManager();

    /**
     * 负责本节点进行中的直播间
     */
    private Map<Long, LiveRoom> liveRooms = new ConcurrentHashMap<>();

    /**
     * 本服玩家直播观看映射
     * 主要用来辅助更新观看数据
     */
    private Map<Long, Long> pidViewRoomId = new ConcurrentHashMap<>();

    /**
     * 聊天时间记录
     */
    private Map<Long, Long> chatTime = new HashMap<>();

    private AtomicLong indexGenerator = new AtomicLong(0);

    private long lastCheckTime = 0;

    public static LiveRoomManager getInstance() {
        return INSTANCE;
    }

    @Override
    public void tick() {
        //间隔1分钟检查
        if(System.currentTimeMillis() - lastCheckTime > 60*1000){
            lastCheckTime = System.currentTimeMillis();
        }else{
            return;
        }

        for(LiveRoom liveRoom : liveRooms.values()){
            if(liveRoom.isLiveOver()){
                ThreadPool.execute(() -> {
                    liveRoom.writeState(PbLive.LiveData.LiveDataType.STATE_EXCEPTION_CLOSED);
                    liveRooms.remove(liveRoom.getId());
                    liveRoom.removeFromRedis();
                });
            }
        }
    }

    public boolean chatCDCheck(long pid){
        Long lastTime = chatTime.get(pid);
        if(lastTime == null){
            return true;
        }

        if(ServerConstants.getCurrentTimeMillis() - lastTime < TournamentService.getTournamentConst().chatSendCD){
            return false;
        }
        return true;
    }

    public void addChatTime(long pid){
        chatTime.put(pid, System.currentTimeMillis());
    }

    public void addLiveRoom(LiveRoom liveRoom) {
        liveRoom.writeState(PbLive.LiveData.LiveDataType.STATE_START);
        liveRooms.put(liveRoom.getId(), liveRoom);
    }

    public void removeLiveRoom(LiveRoom liveRoom) {
        liveRoom.writeState(PbLive.LiveData.LiveDataType.STATE_CLOSED);
        liveRooms.remove(liveRoom.getId());
    }

    public void enterRoom(long pid, long roomId) {
        this.exitRoom(pid);
        this.pidViewRoomId.put(pid, roomId);
        LiveHelper.modifyViewCount(roomId, 1);
    }

    public void exitRoom(long pid) {
        Long roomId = pidViewRoomId.get(pid);
        if(roomId != null){
            pidViewRoomId.remove(pid);
            LiveHelper.modifyViewCount(roomId, -1);
        }

    }

    public void shutdown(){
        for(LiveRoom liveRoom : liveRooms.values()){
            liveRoom.writeState(PbLive.LiveData.LiveDataType.STATE_EXCEPTION_CLOSED);
            liveRoom.removeFromRedis();
        }

        //扣除直播观看人数
        for(Map.Entry<Long, Long> entry : pidViewRoomId.entrySet()) {
            long roomId = entry.getValue();
            LiveHelper.modifyViewCount(roomId, -1);
        }
    }

    public Map<Long, LiveRoom> getLiveRooms() {
        return liveRooms;
    }

    public AtomicLong getIndexGenerator() {
        return indexGenerator;
    }

    @Override
    public void runnerExecute() throws Exception {
        if(liveRooms.isEmpty()){
            return;
        }
        List<LiveRoom> rooms = new ArrayList<>(liveRooms.values());
        String[] keys = new String[rooms.size()];
        for (int i = 0; i < rooms.size(); i++) {
            keys[i] = GsRedisKey.Live.live_count.getRedisKey(rooms.get(i).getId());
        }
        Map<String, String> counts = TLBase.getInstance().getRedisAssistant().mgetString(keys);

        for(LiveRoom liveRoom : rooms){
            String count = counts.get(GsRedisKey.Live.live_count.getRedisKey(liveRoom.getId()));
            if(count == null){
                count = "0";
            }

            int viewCount = Integer.parseInt(count);
            if(viewCount < 0){
                viewCount = 0;
            }
            liveRoom.writeViewCount(viewCount);
        }
    }

    @Override
    public long getRunnerInterval() {
        return 2000L;
    }
}

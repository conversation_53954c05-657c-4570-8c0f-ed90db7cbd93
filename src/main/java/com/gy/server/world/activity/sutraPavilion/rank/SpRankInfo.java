package com.gy.server.world.activity.sutraPavilion.rank;

import java.util.ArrayList;
import java.util.List;

public class SpRankInfo {

    private int groupId;

    private List<SpSingleRankInfo> singleRankInfoList = new ArrayList<>();
    
    private List<SpSingleRankInfo> teamRankInfoList = new ArrayList<>();

    public void addPlayerPoint(long playerId, int floorId, int point){
        addPoint(singleRankInfoList, playerId, floorId, point);
    }

    public void addTeamPoint(long teamId, int floorId, int point){
        addPoint(teamRankInfoList, teamId, floorId, point);
    }

    private void addPoint(List<SpSingleRankInfo> rankList, long id, int floorId,int point){
        for (SpSingleRankInfo spSingleRankInfo : rankList) {
            if(spSingleRankInfo.getId() == id){
                spSingleRankInfo.addPoint(point);
                return;
            }
        }
        SpSingleRankInfo spSingleRankInfo = new SpSingleRankInfo();
        spSingleRankInfo.setId(id);
        spSingleRankInfo.addPoint(point);
        spSingleRankInfo.setFloorId(floorId);
        rankList.add(spSingleRankInfo);
    }

    public void modifyPlayerFloor(long playerId, int floorId){
        modifyFloor(singleRankInfoList, playerId, floorId);
    }

    public void modifyTeamFloor(long teamId, int floorId){
        modifyFloor(teamRankInfoList, teamId, floorId);
    }
    private void modifyFloor(List<SpSingleRankInfo> rankList, long id, int floorId){
        for (SpSingleRankInfo spSingleRankInfo : rankList) {
            if(spSingleRankInfo.getId() == id){
                spSingleRankInfo.setFloorId(floorId);
                return;
            }
        }
        SpSingleRankInfo spSingleRankInfo = new SpSingleRankInfo();
        spSingleRankInfo.setId(id);
        spSingleRankInfo.setFloorId(floorId);
        rankList.add(spSingleRankInfo);
    }

    public int getGroupId() {
        return groupId;
    }

    public void setGroupId(int groupId) {
        this.groupId = groupId;
    }

    public List<SpSingleRankInfo> getSingleRankInfoList() {
        return singleRankInfoList;
    }

    public void setSingleRankInfoList(List<SpSingleRankInfo> singleRankInfoList) {
        this.singleRankInfoList = singleRankInfoList;
    }

    public List<SpSingleRankInfo> getTeamRankInfoList() {
        return teamRankInfoList;
    }

    public void setTeamRankInfoList(List<SpSingleRankInfo> teamRankInfoList) {
        this.teamRankInfoList = teamRankInfoList;
    }

    public void sortRank(){
        singleRankInfoList.sort((o1, o2) -> {
            if(o2.getFloorId() == o1.getFloorId()){
                if (o2.getPoint() == o1.getPoint()) {
                    return (int) (o1.getLastUpdateTime() - o2.getLastUpdateTime());
                }
                return o2.getPoint() - o1.getPoint();
            }
            return o2.getFloorId() - o1.getFloorId();
        });

        teamRankInfoList.sort((o1, o2) -> {
            if(o2.getFloorId() == o1.getFloorId()){
                if (o2.getPoint() == o1.getPoint()) {
                    return (int) (o1.getLastUpdateTime() - o2.getLastUpdateTime());
                }
                return o2.getPoint() - o1.getPoint();
            }
            return o2.getFloorId() - o1.getFloorId();
        });
    }


}

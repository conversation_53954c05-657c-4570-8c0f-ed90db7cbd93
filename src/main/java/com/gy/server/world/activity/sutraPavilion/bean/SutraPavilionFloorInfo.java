package com.gy.server.world.activity.sutraPavilion.bean;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.gy.server.game.activity.ActivityService;
import com.gy.server.game.activity.data.sutraPavilion.SutraPavilionActivityData;
import com.gy.server.game.activity.data.sutraPavilion.template.TheSutraRepositoryFloorTemplate;
import com.gy.server.packet.PbActivityTeam;
import com.ttlike.server.tl.baselib.serialize.base.RedisListIntegerBean;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SutraPavilionFloorInfo {

    @Protobuf(order = 1)
    int floorId;
    @Protobuf(order = 2)
    Map<Integer, SutraPavilionNodeInfo> nodeInfoMap = new HashMap<>();
    @Protobuf(order = 3)
    private int keyNum;
    //已经触发过地块id（仅限秘宝层使用）
    @Protobuf(order = 4)
    private Map<Long, RedisListIntegerBean> hadTriggerNodeIds = new HashMap<>();

    public SutraPavilionFloorInfo(){

    }

    public int getFloorId() {
        return floorId;
    }

    public void setFloorId(int floorId) {
        this.floorId = floorId;
    }

    public SutraPavilionFloorInfo(int floorId){
        this.floorId = floorId;
    }

    public Map<Long, RedisListIntegerBean> getHadTriggerNodeIds() {
        return hadTriggerNodeIds;
    }

    public void setHadTriggerNodeIds(Map<Long, RedisListIntegerBean> hadTriggerNodeIds) {
        this.hadTriggerNodeIds = hadTriggerNodeIds;
    }

    public TheSutraRepositoryFloorTemplate getFloorTemplate(int activityId){
        SutraPavilionActivityData data = ActivityService.getActivityData(activityId);
        return  data.getFloorTemplates().get(floorId);
    }

    public void addKeyNum(){
        keyNum++;
    }

    public int getKeyNum() {
        return keyNum;
    }

    public void setKeyNum(int keyNum) {
        this.keyNum = keyNum;
    }

    public Map<Integer, SutraPavilionNodeInfo> getNodeInfoMap() {
        return nodeInfoMap;
    }

    public void setNodeInfoMap(Map<Integer, SutraPavilionNodeInfo> nodeInfoMap) {
        this.nodeInfoMap = nodeInfoMap;
    }

    public List<PbActivityTeam.SutraPavilionNodeInfo> genNodeList(){
        List<PbActivityTeam.SutraPavilionNodeInfo> result = new ArrayList<>();
        for (SutraPavilionNodeInfo nodeInfo : nodeInfoMap.values()) {
            result.add(nodeInfo.genNodeInfo());
        }
        return result;
    }
}

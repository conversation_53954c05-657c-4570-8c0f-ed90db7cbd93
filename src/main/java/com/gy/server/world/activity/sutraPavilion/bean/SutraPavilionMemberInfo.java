package com.gy.server.world.activity.sutraPavilion.bean;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.gy.server.core.ServerConstants;
import com.gy.server.packet.PbActivityTeam;
import com.gy.server.packet.PbCommons;
import com.gy.server.world.activityTeam.bean.ActivityTeam;
import org.apache.commons.lang3.tuple.Pair;

/**
 * 双人组队-夜探藏经阁队伍成员信息
 */
public class SutraPavilionMemberInfo {

    /**
     * 成员id
     */
    @Protobuf(order = 1)
    private long memberId;

    /**
     * 双倍增益
     */
    @Protobuf(order = 2)
    private boolean isDouble;

    /**
     * 层数id
     */
    @Protobuf(order = 3)
    private int floorId;

    @Protobuf(order = 4)
    private int x;

    @Protobuf(order = 5)
    private int y;

    @Protobuf(order = 6)
    private int point;

    @Protobuf(order = 7)
    private long rankTime;

    @Protobuf(order = 8)
    private int unlockTimes;

    public int getUnlockTimes() {
        return unlockTimes;
    }

    public void addUnlockTimes(){
        unlockTimes++;
    }

    public void setUnlockTimes(int unlockTimes) {
        this.unlockTimes = unlockTimes;
    }

    public int getPoint() {
        return point;
    }

    public void setPoint(int point) {
        this.point = point;
    }

    public void addPoint(int addedPoint, ActivityTeam team){
        this.point += addedPoint;
        this.rankTime = ServerConstants.getCurrentTimeMillis();
        team.setTeamScore(team.getTeamScore() + addedPoint);
        team.setRankTime(ServerConstants.getCurrentTimeMillis());
    }

    public long getRankTime() {
        return rankTime;
    }

    public void setRankTime(long rankTime) {
        this.rankTime = rankTime;
    }

    public long getMemberId() {
        return memberId;
    }

    public void setMemberId(long memberId) {
        this.memberId = memberId;
    }

    public boolean isDouble() {
        return isDouble;
    }

    public void setDouble(boolean aDouble) {
        isDouble = aDouble;
    }

    public int getFloorId() {
        return floorId;
    }

    public void setFloorId(int floorId, ActivityTeam team) {
        this.floorId = floorId;
        this.rankTime = ServerConstants.getCurrentTimeMillis();
        if(team.getSpFloorId() < floorId){
            team.setSpFloorId(floorId);
            team.setRankTime(ServerConstants.getCurrentTimeMillis());
        }
    }

    public int getX() {
        return x;
    }

    public void setX(int x) {
        this.x = x;
    }

    public int getY() {
        return y;
    }

    public void setY(int y) {
        this.y = y;
    }

    public int getNodeId(){
        return SutraPavilionNodeInfo.calNodeId(x, y);
    }

    public void setNodeId(int nodeId){
        Pair<Integer, Integer> point = SutraPavilionNodeInfo.calXY(nodeId);
        setX(point.getKey());
        setY(point.getValue());
    }

    public PbCommons.KeyValueDb genPosInfo(){
        return PbCommons.KeyValueDb.newBuilder().setIntKey(x).setIntValue(y).build();
    }

    public PbActivityTeam.SurtaPavilionPlayerRankInfo.Builder genSpRankInfo(int rank){
        PbActivityTeam.SurtaPavilionPlayerRankInfo.Builder builder = PbActivityTeam.SurtaPavilionPlayerRankInfo.newBuilder();
        builder.setRank(rank)
                .setUser(PbCommons.MinMiniUser.newBuilder().setId(this.memberId))
                .setScore(point)
                .setFloorId(floorId);
        return builder;
    }

}

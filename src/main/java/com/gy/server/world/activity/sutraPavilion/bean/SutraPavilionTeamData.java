package com.gy.server.world.activity.sutraPavilion.bean;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.activity.ActivityService;
import com.gy.server.game.activity.data.sutraPavilion.SutraPavilionActivityData;
import com.gy.server.game.activity.data.sutraPavilion.template.TheSutraRepositoryFloorTemplate;
import com.gy.server.game.activity.data.sutraPavilion.template.TheSutraRepositoryIncidentGroupTemplate;
import com.gy.server.game.activity.data.sutraPavilion.template.TheSutraRepositoryIncidentTemplate;
import com.gy.server.game.activity.module.sutraPavilion.SutraPavilionHelper;
import com.gy.server.game.activity.module.sutraPavilion.event.AbsSPEvent;
import com.gy.server.game.activity.module.sutraPavilion.event.SpEventType;
import com.gy.server.game.player.Player;
import com.gy.server.packet.PbActivityTeam;
import com.gy.server.packet.PbCommons;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.MathUtil;
import com.gy.server.utils.StringUtil;
import com.gy.server.world.activityTeam.bean.ActivityTeam;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.serialize.scene.ScenePosition2D;

import java.util.*;

/**
 * 夜探藏经阁队伍信息
 */
public class SutraPavilionTeamData {

    /**
     * 地块信息
     * floorId_nodeId（x，y）_地块信息
     */
    @Protobuf(order = 1)
    private Map<Integer, SutraPavilionFloorInfo> floorInfos = new HashMap<>();

    /**
     * 成员位置信息
     * memberId_memberInfo
     */
    @Protobuf(order = 2)
    private Map<Long, SutraPavilionMemberInfo> memberInfos = new HashMap<>();

    /**
     * 协助信息
     */
    @Protobuf(order = 3)
    private List<SutraPavilionAssistInfo> assistInfos = new ArrayList<>();

    @Protobuf(order = 4)
    private List<SutraPavilionLogInfo> logInfos = new ArrayList<>();

    public void addPlayer(ActivityTeam team, long playerId, int activityId){
        SutraPavilionMemberInfo memberInfo = new SutraPavilionMemberInfo();
        memberInfo.setMemberId(playerId);
        SutraPavilionActivityData data = ActivityService.getActivityData(activityId);
        Integer floorId = Collections.min(data.getFloorTemplates().keySet());
        memberInfo.setFloorId(floorId, team);
        TheSutraRepositoryFloorTemplate floorTemplate = data.getFloorTemplates().get(floorId);
        floorTemplate.initMemberPos(memberInfo);
        memberInfos.put(memberInfo.getMemberId(), memberInfo);
    }

    public List<SutraPavilionAssistInfo> getAssistInfos() {
        return assistInfos;
    }

    public void setAssistInfos(List<SutraPavilionAssistInfo> assistInfos) {
        this.assistInfos = assistInfos;
    }

    public List<SutraPavilionLogInfo> getLogInfos() {
        return logInfos;
    }

    public void setLogInfos(List<SutraPavilionLogInfo> logInfos) {
        this.logInfos = logInfos;
    }

    public void removePlayer(long playerId){
        memberInfos.remove(playerId);
    }


    public Map<Integer, SutraPavilionFloorInfo> getFloorInfos() {
        return floorInfos;
    }

    public SutraPavilionFloorInfo getFloorInfo(int floorId){
        return floorInfos.get(floorId);
    }

    public void setFloorInfos(Map<Integer, SutraPavilionFloorInfo> floorInfos) {
        this.floorInfos = floorInfos;
    }

    public Map<Long, SutraPavilionMemberInfo> getMemberInfos() {
        return memberInfos;
    }

    public SutraPavilionMemberInfo getMemberInfo(long playerId){
        return memberInfos.get(playerId);
    }

    public void setMemberInfos(Map<Long, SutraPavilionMemberInfo> memberInfos) {
        this.memberInfos = memberInfos;
    }

    /**
     * 初始化信息
     */
    public void init(int activityId){
        SutraPavilionActivityData data = ActivityService.getActivityData(activityId);
        initNodeInfo(activityId, Collections.min(data.getFloorTemplates().keySet()));
    }

    /**
     * 初始化地块信息（随机地图事件）
     * @param activityId 活动id
     * @param floorId 层数id
     */
    public void initNodeInfo(int activityId, int floorId){
        SutraPavilionActivityData data = ActivityService.getActivityData(activityId);
        TheSutraRepositoryFloorTemplate floorTemplate = data.getFloorTemplates().get(floorId);
        Map<Integer, SutraPavilionNodeInfo> nodeInfos = new HashMap<>();
        //构建基础地块信息
        int cols = floorTemplate.length; //x
        int rows = floorTemplate.length; //y
        List<Integer> spaceNodeIds = floorTemplate.spaceNodeIds;
        List<Integer> lucencyNodeIds = floorTemplate.lucencyNodeIds;
        List<Integer> gatePositionNodeIdList = floorTemplate.gatePositionNodeIdList;
        int simpleNodeId = AbsSPEvent.getSimpleNodeId(activityId);
        //随机是否守卫替换大门
        String[] split = floorTemplate.guardsmanChance.split(";");
        Map<Integer, Integer> eventWeights = new HashMap<>();
        for (String s : split) {
            int[] ints = StringUtil.splitToIntArray(s, "_");
            eventWeights.put(ints[1], ints[0]);
        }
        //随机出大门事件（有概率是怪物）
        Integer guardEventId = MathUtil.weightRandom(eventWeights);
        eventWeights.remove(guardEventId);
        Integer otherEventId = eventWeights.keySet().iterator().next();
        TheSutraRepositoryIncidentTemplate guardEventTemplate = data.getEventTemplates().get(guardEventId);
        for(int y = 1; y <= rows; y++){
            for(int x = 1; x <= cols; x++){
                SutraPavilionNodeInfo nodeInfo = new SutraPavilionNodeInfo(floorId, x, y);
                //构建出口相关
                if(spaceNodeIds.contains(nodeInfo.getNodeId())){
                    //设置必须空白位置
                    nodeInfo.setEventId(simpleNodeId);
                }
                if(lucencyNodeIds.contains(nodeInfo.getNodeId())){
                    //设置必须障碍物
                    nodeInfo.setEventId(data.getConstTemplate().obstacleEventId);
                }
                if(gatePositionNodeIdList.contains(nodeInfo.getNodeId())){
                    //设置大门/守卫
                    nodeInfo.setEventId(guardEventId);
                    if(guardEventTemplate.eventType == SpEventType.monster){
                        //记录怪物变身大门
                        nodeInfo.setMonsterKillEventId(otherEventId);
                    }
                }
                nodeInfos.put(nodeInfo.getNodeId(), nodeInfo);
            }
        }
        if(floorTemplate.treasure){
            //获取秘宝层宝箱
            List<TheSutraRepositoryIncidentGroupTemplate> groupTemplates = data.getEventGroupTemplates().get(floorTemplate.incidentGroup);
            TheSutraRepositoryIncidentGroupTemplate template = groupTemplates.get(0);
            for (Integer nodeId : nodeInfos.keySet()) {
                SutraPavilionNodeInfo nodeInfo = nodeInfos.get(nodeId);
                //设置秘宝
                if(floorTemplate.treasureLocationNodeIds.contains(nodeId)){
                    nodeInfo.setEventId(template.ID);
                }
                //全部解锁
                nodeInfo.setStatus(SutraPavilionNodeInfo.had_unlock, true);
            }
        }else{
            List<ScenePosition2D> canUseNodeInfos = new ArrayList<>();
            for (SutraPavilionNodeInfo nodeInfo : nodeInfos.values()) {
                if(nodeInfo.getEventId() <= 0){
                    ScenePosition2D scenePosition2D = new ScenePosition2D();
                    scenePosition2D.setX(nodeInfo.getX());
                    scenePosition2D.setY(nodeInfo.getY());
                    canUseNodeInfos.add(scenePosition2D);
                }
            }
            //先做随机，再找地块
            Collections.shuffle(canUseNodeInfos);
            //刷新野怪（怪物会占多个格子，所以先确定怪物，再确定其他格子）
            Map<SpEventType, List<TheSutraRepositoryIncidentGroupTemplate>> groupTemplates = data.getEventGroupTemplateMap(floorTemplate.incidentGroup);
            if(groupTemplates.containsKey(SpEventType.monster)){
                int monsterGroupId = 1;
                List<TheSutraRepositoryIncidentGroupTemplate> monsterEvents = groupTemplates.get(SpEventType.monster);
                //开始处理怪物随机
                for (TheSutraRepositoryIncidentGroupTemplate monsterEvent : monsterEvents) {
                    TheSutraRepositoryIncidentTemplate eventTemplate = monsterEvent.getEventTemplate(data);
                    String[] param = eventTemplate.getParam();
                    //怪物占用地块数量
                    int needNodeSize = Integer.parseInt(param[0]);
                    //怪物数量权重
                    Map<Integer, Integer> monsterCountWeight = new HashMap<>();
                    for (String monsterCount : param[1].split(";")) {
                        int[] ints = StringUtil.splitToIntArray(monsterCount, "_");
                        monsterCountWeight.put(ints[0], ints[1]);
                    }
                    //随机怪物数量
                    int count = MathUtil.weightRandom(monsterCountWeight);
                    for (int i = 0; i < count; i++) {
                        //开始随机地块
                        List<ScenePosition2D> freeNodes = SutraPavilionHelper.getFreeNode(needNodeSize, canUseNodeInfos, floorTemplate.length);
                        if(CollectionUtil.isNotEmpty(freeNodes)){
                            for (ScenePosition2D freeNode : freeNodes) {
                                int nodeId = SutraPavilionNodeInfo.calNodeId(freeNode.getX(), freeNode.getY());
                                SutraPavilionNodeInfo nodeInfo = nodeInfos.get(nodeId);
                                //标注事件id
                                nodeInfo.setEventId(eventTemplate.incidentId);
                                nodeInfo.setMonsterGroupId(monsterGroupId);
                                canUseNodeInfos.remove(freeNode);
                            }
                            monsterGroupId ++;
                        }
                    }

                }
            }
            //开始随机其他事件
            groupTemplates.remove(SpEventType.monster);
            //刷新障碍物
            List<TheSutraRepositoryIncidentGroupTemplate> obstacleEventTemps = groupTemplates.get(SpEventType.obstacle);
            if(CollectionUtil.isNotEmpty(obstacleEventTemps)){
                List<ScenePosition2D> obCanUseNodeInfos = new ArrayList<>(canUseNodeInfos);
                List<Integer> forbidObstacleNodeIds = data.getConstTemplate().getForbidObstacleNodeIds(floorTemplate.length);
                for (ScenePosition2D obCanUseNodeInfo : obCanUseNodeInfos) {
                    int nodeId = SutraPavilionNodeInfo.calNodeId(obCanUseNodeInfo.getX(), obCanUseNodeInfo.getY());
                    if(forbidObstacleNodeIds.contains(nodeId)){
                        obCanUseNodeInfos.remove(obCanUseNodeInfo);
                    }
                }
                int nodeIndex = 0;
                for (TheSutraRepositoryIncidentGroupTemplate obstacleEventTemp : obstacleEventTemps) {
                    TheSutraRepositoryIncidentTemplate eventTemplate = obstacleEventTemp.getEventTemplate(data);
                    Map<Integer, Integer> countWeight = new HashMap<>();
                    String[] param = eventTemplate.getParam();
                    for (String eventCount : param[1].split(";")) {
                        int[] ints = StringUtil.splitToIntArray(eventCount, "_");
                        countWeight.put(ints[0], ints[1]);
                    }
                    int count = MathUtil.weightRandom(countWeight);
                    for (int i = 0; i < count; i++) {
                        ScenePosition2D freeNode = canUseNodeInfos.get(nodeIndex);
                        int nodeId = SutraPavilionNodeInfo.calNodeId(freeNode.getX(), freeNode.getY());
                        SutraPavilionNodeInfo nodeInfo = nodeInfos.get(nodeId);
                        nodeInfo.setEventId(obstacleEventTemp.eventId);
                        //障碍物直接显现
                        nodeInfo.setStatus(SutraPavilionNodeInfo.had_unlock, true);
                        canUseNodeInfos.remove(freeNode);
                        nodeIndex++;
                    }
                }
            }

            int nodeIndex = 0;
            for (List<TheSutraRepositoryIncidentGroupTemplate> templates : groupTemplates.values()) {
                for (TheSutraRepositoryIncidentGroupTemplate template : templates) {
                    TheSutraRepositoryIncidentTemplate eventTemplate = template.getEventTemplate(data);
                    Map<Integer, Integer> countWeight = new HashMap<>();
                    String[] param = eventTemplate.getParam();
                    for (String eventCount : param[1].split(";")) {
                        int[] ints = StringUtil.splitToIntArray(eventCount, "_");
                        countWeight.put(ints[0], ints[1]);
                    }
                    int count = MathUtil.weightRandom(countWeight);
                    for (int i = 0; i < count; i++) {
                        ScenePosition2D freeNode = canUseNodeInfos.get(nodeIndex);
                        int nodeId = SutraPavilionNodeInfo.calNodeId(freeNode.getX(), freeNode.getY());
                        SutraPavilionNodeInfo nodeInfo = nodeInfos.get(nodeId);
                        nodeInfo.setEventId(template.eventId);
                        nodeIndex++;
                    }
                }
            }
            //设置出生点为已激活
            int[] initPos = StringUtil.splitToIntArray(floorTemplate.initialPosition, ",");
            int nodeId = SutraPavilionNodeInfo.calNodeId(initPos[0], initPos[1]);
            nodeInfos.get(nodeId).setStatus(SutraPavilionNodeInfo.had_unlock, true);
        }
        //填充普通地块
        for (SutraPavilionNodeInfo nodeInfo : nodeInfos.values()) {
            if(nodeInfo.getEventId() <= 0){
                nodeInfo.setEventId(simpleNodeId);
            }
        }
        SutraPavilionFloorInfo floorInfo = new SutraPavilionFloorInfo(floorId);
        floorInfo.setNodeInfoMap(nodeInfos);
        this.floorInfos.put(floorId, floorInfo);
    }


    /**
     * 玩家进入指定层
     * @param activityId 活动id
     * @param playerId 玩家id
     * @param floorId 层数id
     */
    public boolean enterNode(ActivityTeam team, int activityId, long playerId, int floorId){
        SutraPavilionActivityData data = ActivityService.getActivityData(activityId);
        TheSutraRepositoryFloorTemplate floorTemplate = data.getFloorTemplates().get(floorId);
        if(Objects.isNull(floorTemplate)){
            return false;
        }
        if(!memberInfos.containsKey(playerId)){
            return false;
        }
        if(!floorInfos.containsKey(floorId)){
            //初始化地块事件
            initNodeInfo(activityId, floorId);
        }
        SutraPavilionMemberInfo memberInfo = memberInfos.get(playerId);
        floorTemplate.initMemberPos(memberInfo);

        memberInfo.setFloorId(floorId, team);

        return true;
    }

    public PbActivityTeam.SutraPavilionFloorInfo genFloorInfo(long playerId){
        PbActivityTeam.SutraPavilionFloorInfo.Builder builder = PbActivityTeam.SutraPavilionFloorInfo.newBuilder();
        SutraPavilionMemberInfo myInfo = memberInfos.get(playerId);
        SutraPavilionFloorInfo floorInfo = floorInfos.get(myInfo.getFloorId());
        builder.setFloorId(myInfo.getFloorId());
        builder.addAllNodeInfos(floorInfo.genNodeList());
        builder.putMemberPosInfos(myInfo.getMemberId(), myInfo.genPosInfo());
        for (SutraPavilionMemberInfo memberInfo : memberInfos.values()) {
            if(memberInfo.getMemberId() == playerId){
                continue;
            }
            if(memberInfo.getFloorId() == myInfo.getFloorId()){
                builder.putMemberPosInfos(memberInfo.getMemberId(), memberInfo.genPosInfo());
            }
        }
        return builder.build();
    }


    public void sync(long floorId, int type, Object... param){
        List<Long> syncPlayerIds = new ArrayList<>();
        for (SutraPavilionMemberInfo member : memberInfos.values()) {
            if(member.getFloorId() == floorId){
                syncPlayerIds.add(member.getMemberId());
            }
        }
        PbProtocol.SutraPavilionSyncInfoNotify.Builder notify = PbProtocol.SutraPavilionSyncInfoNotify.newBuilder();
        notify.setType(type);
        if(CollectionUtil.isNotEmpty(syncPlayerIds)){
            switch (type){
                case 1:{
                    //解锁地块
                    SutraPavilionNodeInfo nodeInfo = (SutraPavilionNodeInfo) param[0];
                    notify.addNodeInfo(nodeInfo.genNodeInfo());
                    break;
                }
                case 2:
                    //移动/进入
                case 3:{
                    //离开
                    SutraPavilionMemberInfo memberInfo = (SutraPavilionMemberInfo) param[0];
                    notify.setMemberId(memberInfo.getMemberId());
                    notify.setMemberPosInfo(PbCommons.KeyValueDb.newBuilder().setIntKey(memberInfo.getX()).setIntValue(memberInfo.getY()).build());
                    break;
                }

                case 4:{
                    notify.setType(1);
                    //解锁地块
                    List<SutraPavilionNodeInfo> nodeInfos = (List<SutraPavilionNodeInfo>) param[0];
                    for (SutraPavilionNodeInfo nodeInfo : nodeInfos) {
                        notify.addNodeInfo(nodeInfo.genNodeInfo());
                    }
                    break;
                }
            }
            for (Long syncPlayerId : syncPlayerIds) {
                ServerCommandRequest req = CommandRequests.newServerCommandRequest("ActivityTeamGameCommandService.spNotify", true);
                TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, Player.getRealServerId(syncPlayerId), req, syncPlayerId, notify.build());
            }
        }

    }

}

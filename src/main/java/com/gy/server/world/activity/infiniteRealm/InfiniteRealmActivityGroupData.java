package com.gy.server.world.activity.infiniteRealm;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.gy.server.core.Configuration;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.activity.ActivityService;
import com.gy.server.game.activity.ActivityType;
import com.gy.server.game.activity.data.infiniteRealm.InfiniteRealmActivityData;
import com.gy.server.game.event.ServerEvent;
import com.gy.server.game.event.ServerEventType;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.game.role.PlayerRoleService;
import com.gy.server.packet.PbActivity;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.world.activity.ActivityGroupData;
import com.gy.server.world.activityTeam.ActivityTeamHelper;
import com.gy.server.world.activityTeam.ActivityTeamManager;
import com.gy.server.world.activityTeam.bean.ActivityTeam;
import com.gy.server.world.activityTeam.bean.InfiniteRealmPlayer;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;

import java.util.*;

/**
 * @program: tl_game_4
 * @description: 无量秘境活动跨服数据
 * @author: Huang.Xia
 * @create: 2025/2/8
 **/
public class InfiniteRealmActivityGroupData extends ActivityGroupData {

    /**
     * 是否已经通知发放了结算奖励
     */
    @Protobuf(order = 1)
    private boolean rewarded = false;

    public InfiniteRealmActivityGroupData(int id, int groupId) {
        this.groupId = groupId;
        this.activityId = id;
        this.activityType = ActivityType.infiniteRealm.getId();
    }
    public InfiniteRealmActivityGroupData() {
    }

    @Override
    public void finish() {
        ActivityTeamManager.getInstance().closeActivity(getActivityId(), getGroupId());
    }

    @Override
    public Collection<ServerEventType> getCareEvent() {
        Set<ServerEventType> set = new HashSet<>();
        set.add(ServerEventType.day5clock);
        set.add(ServerEventType.everyHour);
        return set;
    }

    @Override
    public void eventHandle(ServerEvent event) {
        switch (event.getEventType()) {
            case day5clock:{
                Map<Long, ActivityTeam> teams = ActivityTeamManager.getInstance().getTeams().get(getActivityId(), getGroupId());
                if(teams != null && teams.isEmpty()){
                    for(ActivityTeam team : teams.values()){
                        team.getSecretRealmTeamData().newDayRefresh(getActivityId());
                    }
                }
                break;
            }
            case everyHour: {
                if(!rewarded){
                    InfiniteRealmActivityData data = ActivityService.getActivityData(getActivityId());
                    if(data!= null){
                        if(data.getState(getActivityId()) == 4){
                            rewarded = true;
                            ActivityTeamHelper.rankRewardNotify(data, getActivityId(), getGroupId());
                        }
                    }
                }
                break;
            }
        }
    }

    @Override
    public void init() {
    }

    @Override
    public void fillModuleAsync(PbActivity.ActivityModule.Builder builder, long playerId) {
        ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(getActivityId(), getGroupId(), playerId);
        PbActivity.InfiniteRealmActivityModule.Builder md = PbActivity.InfiniteRealmActivityModule.newBuilder();
        if (team!= null) {
            md.setCurSceneId(team.getSecretRealmTeamData().getMembers().get(playerId).getSceneId());
            Map<Long, MiniGamePlayer> players = PlayerHelper.getMiniPlayersForMap(team.getMembers());
            for(Long memberId : team.getMembers()){
                MiniGamePlayer player = players.get(memberId);
                if(player != null) {
                    md.addTeamProtagonistIds(PlayerRoleService.getProtagonistTemplate(player.getProfession(), player.getGender()).id);
                }
            }
        }
        builder.setInfiniteRealm(md);
    }

}

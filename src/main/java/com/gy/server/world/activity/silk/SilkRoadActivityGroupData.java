package com.gy.server.world.activity.silk;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.gy.server.game.activity.ActivityType;
import com.gy.server.game.event.ServerEventType;
import com.gy.server.world.activity.ActivityGroupData;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

public class SilkRoadActivityGroupData extends ActivityGroupData {

    @Protobuf(order = 1)
    private boolean grouped;

    public SilkRoadActivityGroupData() {}

    public SilkRoadActivityGroupData(int activityId, int groupId) {
        this.activityId = activityId;
        this.groupId = groupId;
        this.activityType = ActivityType.silkRoad.getId();
    }

    @Override
    public void init() {
        //分组
        if(!grouped) {
            ThreadPool.execute(new SilkGroupRunner(this));
        }
    }

    public boolean isGrouped() {
        return grouped;
    }

    public void setGrouped(boolean grouped) {
        this.grouped = grouped;
    }

    @Override
    public Collection<ServerEventType> getCareEvent() {
        List<ServerEventType> events = new ArrayList<>();
        events.add(ServerEventType.everyHour);
        return events;
    }
}
package com.gy.server.world.activity.silk;

import com.gy.server.core.callback.response.CallbackResponse;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.core.log.SystemLogger;
import com.gy.server.game.activity.ActivityService;
import com.gy.server.game.activity.silk.SilkRoadActivityData;
import com.gy.server.game.activity.silk.template.SilkRoadConst;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.warZone.WarZoneHelper;
import com.gy.server.game.warZone.WarZoneTypeEnums;
import com.gy.server.world.common.NodeCommonManager;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.rpc.TLMessageCallbackTask;
import com.ttlike.server.tl.baselib.serialize.warZone.WarZoneInfo;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @program: tl_game_4_live
 * @description: 丝绸之路分组线程
 * @author: Huang.Xia
 * @create: 2025/3/25
 **/
public class SilkGroupRunner implements Runnable {
    private SilkRoadActivityGroupData groupData;

    private Set<Integer> targetSids = new HashSet<>();

    /**
     * sid, 符合条件帮派数量
     */
    private Map<Integer, Integer> leagueCount = new HashMap<>();

    private boolean finished = false;
    public SilkGroupRunner(SilkRoadActivityGroupData groupData) {
        this.groupData = groupData;
    }

    @Override
    public void run() {
        boolean first = true;
        while (!finished) {
            try {
                if(!first){
                    TimeUnit.SECONDS.sleep(5);
                }else{
                    first = false;
                }

                //确认服务器进程数量稳定
                if(!NodeCommonManager.getInstance().isStableServerCount()){
//                    GameLogger.slgDebug("SilkGroupRunner wait for server count stable.");
                    continue;
                }

                //锁定报名通知的目标区服
                if(targetSids.size() == 0){
                    WarZoneInfo info = WarZoneHelper.getWarZoneInfo(WarZoneTypeEnums.silkRoad);
                    WarZoneInfo.WarZoneSingleInfo singleInfo = info.getInfoByWarZone(groupData.getGroupId());
                    Set<Integer> sids = new HashSet<>(singleInfo.getServerIds());
                    Set<Integer> liveSids = new HashSet<>(NodeCommonManager.getInstance().getHealthyServerId(1));

                    // 过滤掉不在线的服务器
                    sids.retainAll(liveSids);

                    if(sids.size() == 0){
                        GameLogger.slgDebug("SilkGroupRunner no server available. groupId = " + groupData.getGroupId());
                        this.finished = true;
                        continue;
                    }else{
                        targetSids.addAll(sids);
                    }
                }

                //通知并等待上报结果
                notifyRegister();
            }catch (Exception e){
                SystemLogger.error("SilkGroupRunner errorgroupId = " + groupData.getGroupId(), e);
            }
        }

        this.groupData.setGrouped(true);
    }

    private void notifyRegister(){
        Set<Integer> sids = new HashSet<>(targetSids);
        sids.removeAll(this.leagueCount.keySet());
        if(sids.size() == 0){
            //分组
            SilkRoadActivityData  data = ActivityService.getActivityData(this.groupData.getActivityId());
            SilkRoadConst constData = data.getSilkRoadConst();
            List<Integer> ids = new ArrayList<>(this.targetSids);
            Collections.sort(ids);

            int groupIndex = 1;
            int leagueCount = 0;
            Set<Integer> groupIds = new HashSet<>();
            for(int i = 0; i < ids.size(); i++){
                int serverId = ids.get(i);
                int count = this.leagueCount.get(serverId);
                if(count >= constData.minGroupCount){
                    //自成一组，组号几位自己的服务器编号
                    int groupId = serverId;
                    ServerCommandRequest req = CommandRequests.newServerCommandRequest("SilkRoadGameCommandService.groupResult");
                    TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, serverId, req, this.groupData.getActivityId(), groupId, ids);
                }else{
                    //累计进组
                    leagueCount += count;
                    groupIds.add(serverId);
                    if(leagueCount >= constData.minGroupCount){
                        for(int targetServerId : groupIds){
                            int groupId = -(this.groupData.getGroupId()*10000 + groupIndex);
                            ServerCommandRequest req = CommandRequests.newServerCommandRequest("SilkRoadGameCommandService.groupResult");
                            TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, targetServerId, req, this.groupData.getActivityId(), groupId, ids);
                        }

                        leagueCount = 0;
                        groupIds.clear();
                        groupIndex++;
                    }
                }
            }

            //剩余部分分到一组
            if(groupIds.size() > 0){
                for(int targetServerId : groupIds){
                    int groupId = -(this.groupData.getGroupId()*10000 + groupIndex);
                    ServerCommandRequest req = CommandRequests.newServerCommandRequest("SilkRoadGameCommandService.groupResult");
                    TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, targetServerId, req, this.groupData.getActivityId(), groupId, ids);
                }
            }

            this.finished = true;
        }else{
            for(int sid : sids){
                ServerCommandRequest req = CommandRequests.newServerCommandRequest("SilkRoadGameCommandService.sign");
                TLBase.getInstance().getRpcUtil().sendToNodeWithCallBack(new SignCallbackTask(sid),ServerType.GAME, sid, req);
            }
        }
    }

    private class SignCallbackTask extends TLMessageCallbackTask {
        private final int sid;

        public SignCallbackTask(int sid) {
            this.sid = sid;
        }

        @Override
        public void complete(CallbackResponse response) {
            int count = response.getParam(0);
            leagueCount.put(sid, count);
        }

        @Override
        public void timeout() {
            //等待下次调用即可
        }
    }
}

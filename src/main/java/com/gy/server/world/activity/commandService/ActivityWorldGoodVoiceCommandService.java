package com.gy.server.world.activity.commandService;

import com.gy.server.annotation.MessageMethod;
import com.gy.server.annotation.MessageServiceBean;
import com.gy.server.core.MessageServerType;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.callback.AbstractActiveCallbackTask;
import com.gy.server.core.command.CommandRequestParams;
import com.gy.server.core.command.PlayerCommandRequest;
import com.gy.server.core.cos.COSManager;
import com.gy.server.game.activity.helper.GoodVoiceHelper;
import com.gy.server.game.activity.service.GoodVoiceActivityService;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbActivity;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.time.DateTimeUtil;
import com.gy.server.world.activity.ActivityCrossData;
import com.gy.server.world.activity.goodVoice.GoodVoiceActivityGroupData;
import com.gy.server.world.crossData.CrossDataManager;
import com.gy.server.world.crossData.CrossDataType;
import com.ttlike.server.tl.baselib.serialize.goodVoice.GoodVoiceMsgDb;
import com.ttlike.server.tl.baselib.serialize.goodVoice.PlayerGoodVoiceDb;
import com.ttlike.server.tl.baselib.serialize.goodVoice.TopPlayerGoodVoiceDb;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> - [Created on 2025/4/7 15:53]
 **/

@MessageServiceBean(description = "跨服活动 好声音消息服务", messageServerType = MessageServerType.world)
public class ActivityWorldGoodVoiceCommandService {

    @MessageMethod(description = "获取玩家的声音信息")
    private static void goodVoicePlayerInfo(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int groupId = params.getParam(0);
        int activityId = params.getParam(1);
        int seasonId = params.getParam(2);
        long voiceOwner = params.getParam(3);

        ActivityCrossData crossData = CrossDataManager.getInstance().getData(CrossDataType.Activity, groupId);
        if (crossData == null) {
            request.addCallbackParam(Text.活动暂未开启);
            return;
        }
        GoodVoiceActivityGroupData groupData = crossData.getActivityGroupData(activityId);
        if (groupData == null) {
            request.addCallbackParam(Text.活动暂未开启);
            return;
        }
        int globalSeasonId = GoodVoiceHelper.getSeasonIdByStartTime(activityId, DateTimeUtil.toLocalDateTime(groupData.getCurrentSeasonStartTime()));
        if (globalSeasonId != seasonId) {
            request.addCallbackParam(Text.好声音页面数据超时);
            return;
        }
        if (voiceOwner != playerId && !groupData.getPlayerVoiceDbMap().containsKey(voiceOwner)) {
            request.addCallbackParam(Text.好声音声音不存在);
            return;
        }
        PlayerGoodVoiceDb voiceDb = groupData.getPlayerVoiceDbMap().get(voiceOwner);
        PbProtocol.GoodVoiceActivityVoiceRst.Builder rst = PbProtocol.GoodVoiceActivityVoiceRst.newBuilder()
                .setResult(Text.genOkServerRstInfo()).setActivityId(activityId);
        //主线程填充数据
        rst.setRank(groupData.playerRankMap.getOrDefault(voiceOwner, -1));
        if (voiceDb != null) {
            //声音
            rst.setGoodVoiceInfo(GoodVoiceHelper.genGoodVoiceInfo(voiceDb));
            //留言数据
            for (int i = voiceDb.getMsgList().size() - 1; i >= 0; i--) {
                GoodVoiceMsgDb msg = voiceDb.getMsgList().get(i);
                rst.addMsg(GoodVoiceHelper.genVoiceMsgPb(msg));
                if (rst.getMsgCount() >= GoodVoiceActivityService.MSG_PAGE_SIZE) {
                    break;
                }
            }
        }

        ThreadPool.execute(new GoodVoicePlayerInfoActiveCallbackTask(request, voiceOwner, rst));


    }

    @MessageMethod(description = "获取玩家的声音二进制")
    private static void goodVoiceVoiceBytes(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int groupId = params.getParam(0);
        int activityId = params.getParam(1);
        int seasonId = params.getParam(2);
        long voiceOwner = params.getParam(3);

        ActivityCrossData crossData = CrossDataManager.getInstance().getData(CrossDataType.Activity, groupId);
        if (crossData == null) {
            request.addCallbackParam(Text.活动暂未开启);
            return;
        }
        GoodVoiceActivityGroupData groupData = crossData.getActivityGroupData(activityId);
        if (groupData == null) {
            request.addCallbackParam(Text.活动暂未开启);
            return;
        }
        int globalSeasonId = GoodVoiceHelper.getSeasonIdByStartTime(activityId, DateTimeUtil.toLocalDateTime(groupData.getCurrentSeasonStartTime()));
        if (globalSeasonId != seasonId) {
            request.addCallbackParam(Text.好声音页面数据超时);
            return;
        }
        PlayerGoodVoiceDb voiceDb = groupData.getPlayerVoiceDbMap().get(voiceOwner);
        if (voiceDb == null) {
            request.addCallbackParam(Text.好声音声音不存在);
            return;
        }
        request.addCallbackParam(Text.没有异常);
        request.addCallbackParam(voiceDb.getVoiceBytes());

    }

    @MessageMethod(description = "上传音频")
    private static void goodVoiceUpload(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int groupId = params.getParam(0);
        int activityId = params.getParam(1);
        int seasonId = params.getParam(2);
        int content = params.getParam(3);
        String url = params.getParam(4);

        ActivityCrossData crossData = CrossDataManager.getInstance().getData(CrossDataType.Activity, groupId);
        if (crossData == null) {
            request.addCallbackParam(Text.活动暂未开启);
            return;
        }
        GoodVoiceActivityGroupData groupData = crossData.getActivityGroupData(activityId);
        if (groupData == null) {
            request.addCallbackParam(Text.活动暂未开启);
            return;
        }
        int globalSeasonId = GoodVoiceHelper.getSeasonIdByStartTime(activityId, DateTimeUtil.toLocalDateTime(groupData.getCurrentSeasonStartTime()));
        if (globalSeasonId != seasonId) {
            request.addCallbackParam(Text.好声音页面数据超时);
            return;
        }
        if (groupData.isOutOperationTime()) {
            request.addCallbackParam(Text.好声音当前不可操作);
            return;
        }

        PlayerGoodVoiceDb voiceDb = new PlayerGoodVoiceDb();
        voiceDb.setPlayerId(playerId);
        voiceDb.setSelectContent(content);
        voiceDb.setVoiceUrl(url);
        voiceDb.setCreateTime(ServerConstants.getCurrentTimeMillis());
        groupData.getPlayerVoiceDbMap().put(playerId, voiceDb);
        groupData.getGoodVoicePlayers().add(playerId);

        request.addCallbackParam(Text.没有异常);
        request.addCallbackParam(GoodVoiceHelper.genGoodVoiceInfo(voiceDb).toByteArray());
    }

    @MessageMethod(description = "删除上传音频")
    private static void goodVoiceVoiceDel(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int groupId = params.getParam(0);
        int activityId = params.getParam(1);
        int seasonId = params.getParam(2);

        ActivityCrossData crossData = CrossDataManager.getInstance().getData(CrossDataType.Activity, groupId);
        if (crossData == null) {
            request.addCallbackParam(Text.活动暂未开启);
            return;
        }
        GoodVoiceActivityGroupData groupData = crossData.getActivityGroupData(activityId);
        if (groupData == null) {
            request.addCallbackParam(Text.活动暂未开启);
            return;
        }
        int globalSeasonId = GoodVoiceHelper.getSeasonIdByStartTime(activityId, DateTimeUtil.toLocalDateTime(groupData.getCurrentSeasonStartTime()));
        if (globalSeasonId != seasonId) {
            request.addCallbackParam(Text.好声音页面数据超时);
            return;
        }
        if (groupData.isOutOperationTime()) {
            request.addCallbackParam(Text.好声音当前不可操作);
            return;
        }

        groupData.getGoodVoicePlayers().remove(playerId);
        groupData.getPlayerVoiceDbMap().remove(playerId);
        if (groupData.playerRankMap.containsKey(playerId)) {
            groupData.resetRankMap(false);
        }
        request.addCallbackParam(Text.没有异常);
    }

    @MessageMethod(description = "系统删除非法音频")
    private static void goodVoiceVoiceSysDel(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int groupId = params.getParam(0);
        int activityId = params.getParam(1);
        int seasonId = params.getParam(2);
        String url = params.getParam(3);

        ActivityCrossData crossData = CrossDataManager.getInstance().getData(CrossDataType.Activity, groupId);
        if (crossData == null) {
            request.addCallbackParam(Text.活动暂未开启);
            return;
        }
        GoodVoiceActivityGroupData groupData = crossData.getActivityGroupData(activityId);
        if (groupData == null) {
            request.addCallbackParam(Text.活动暂未开启);
            return;
        }

        PlayerGoodVoiceDb voiceDb = groupData.getPlayerVoiceDbMap().get(playerId);
        if(voiceDb != null && voiceDb.getVoiceUrl().equals(url)){
            groupData.getGoodVoicePlayers().remove(playerId);
            groupData.getPlayerVoiceDbMap().remove(playerId);
            if (groupData.playerRankMap.containsKey(playerId)) {
                groupData.resetRankMap(false);
            }

            request.addCallbackParam(Text.没有异常);
            return;
        }
        request.addCallbackParam(Text.参数异常);
    }

    @MessageMethod(description = "声音留言")
    private static void goodVoiceSendMsg(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int groupId = params.getParam(0);
        int activityId = params.getParam(1);
        int seasonId = params.getParam(2);
        long voiceOwner = params.getParam(3);
        String msg = params.getParam(4);
        String playerName = params.getParam(5);

        ActivityCrossData crossData = CrossDataManager.getInstance().getData(CrossDataType.Activity, groupId);
        if (crossData == null) {
            request.addCallbackParam(Text.活动暂未开启);
            return;
        }
        GoodVoiceActivityGroupData groupData = crossData.getActivityGroupData(activityId);
        if (groupData == null) {
            request.addCallbackParam(Text.活动暂未开启);
            return;
        }
        if (groupData.isOutOperationTime()) {
            request.addCallbackParam(Text.好声音当前不可操作);
            return;
        }
        int globalSeasonId = GoodVoiceHelper.getSeasonIdByStartTime(activityId, DateTimeUtil.toLocalDateTime(groupData.getCurrentSeasonStartTime()));
        if (globalSeasonId != seasonId) {
            request.addCallbackParam(Text.好声音页面数据超时);
            return;
        }
        PlayerGoodVoiceDb voiceDb = groupData.getPlayerVoiceDbMap().get(voiceOwner);
        if (voiceDb == null) {
            request.addCallbackParam(Text.好声音声音不存在);
            return;
        }
        GoodVoiceMsgDb goodVoiceMsgDb = GoodVoiceHelper.msgVoice(voiceDb, groupData.createMsgId(), playerId, playerName, msg);
        request.addCallbackParam(Text.没有异常);
        request.addCallbackParam(GoodVoiceHelper.genVoiceMsgPb(goodVoiceMsgDb).toByteArray());
    }

    @MessageMethod(description = "声音留言删除")
    private static void goodVoiceDelMsg(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int groupId = params.getParam(0);
        int activityId = params.getParam(1);
        int seasonId = params.getParam(2);
        long voiceOwner = params.getParam(3);
        long msgId = params.getParam(4);

        ActivityCrossData crossData = CrossDataManager.getInstance().getData(CrossDataType.Activity, groupId);
        if (crossData == null) {
            request.addCallbackParam(Text.活动暂未开启);
            return;
        }
        GoodVoiceActivityGroupData groupData = crossData.getActivityGroupData(activityId);
        if (groupData == null) {
            request.addCallbackParam(Text.活动暂未开启);
            return;
        }
        int globalSeasonId = GoodVoiceHelper.getSeasonIdByStartTime(activityId, DateTimeUtil.toLocalDateTime(groupData.getCurrentSeasonStartTime()));
        if (globalSeasonId != seasonId) {
            request.addCallbackParam(Text.好声音页面数据超时);
            return;
        }
        PlayerGoodVoiceDb voiceDb = groupData.getPlayerVoiceDbMap().get(voiceOwner);
        if (voiceDb == null) {
            request.addCallbackParam(Text.好声音声音不存在);
            return;
        }
        Optional<GoodVoiceMsgDb> first = voiceDb.getMsgList().stream().filter(e -> e.getId() == msgId).findFirst();
        if (!first.isPresent()) {
            request.addCallbackParam(Text.好声音拉票留言不存在);
            return;
        }
        if (playerId != voiceOwner && first.get().getSenderId() != playerId) {
            request.addCallbackParam(Text.好声音留言不可删除);
        } else {
            voiceDb.getMsgList().remove(first.get());
            request.addCallbackParam(Text.没有异常);
        }
    }

    @MessageMethod(description = "拉票")
    private static void goodVoicePullTicket(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int groupId = params.getParam(0);
        int activityId = params.getParam(1);
        int seasonId = params.getParam(2);
        long voiceOwner = params.getParam(3);

        ActivityCrossData crossData = CrossDataManager.getInstance().getData(CrossDataType.Activity, groupId);
        if (crossData == null) {
            request.addCallbackParam(Text.活动暂未开启);
            return;
        }
        GoodVoiceActivityGroupData groupData = crossData.getActivityGroupData(activityId);
        if (groupData == null) {
            request.addCallbackParam(Text.活动暂未开启);
            return;
        }
        if (groupData.isOutOperationTime()) {
            request.addCallbackParam(Text.好声音当前不可操作);
            return;
        }
        int globalSeasonId = GoodVoiceHelper.getSeasonIdByStartTime(activityId, DateTimeUtil.toLocalDateTime(groupData.getCurrentSeasonStartTime()));
        if (globalSeasonId != seasonId) {
            request.addCallbackParam(Text.好声音页面数据超时);
            return;
        }
        PlayerGoodVoiceDb voiceDb = groupData.getPlayerVoiceDbMap().get(voiceOwner);
        if (voiceDb == null) {
            request.addCallbackParam(Text.好声音声音不存在);
            return;
        }
        GoodVoiceHelper.pullVoiceVote(voiceDb, 1);
        request.addCallbackParam(Text.没有异常);

    }

    @MessageMethod(description = "查看更多留言")
    private static void goodVoiceMsgMore(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int groupId = params.getParam(0);
        int activityId = params.getParam(1);
        int seasonId = params.getParam(2);
        long voiceOwner = params.getParam(3);
        long msgId = params.getParam(4);

        ActivityCrossData crossData = CrossDataManager.getInstance().getData(CrossDataType.Activity, groupId);
        if (crossData == null) {
            request.addCallbackParam(Text.活动暂未开启);
            return;
        }
        GoodVoiceActivityGroupData groupData = crossData.getActivityGroupData(activityId);
        if (groupData == null) {
            request.addCallbackParam(Text.活动暂未开启);
            return;
        }
        int globalSeasonId = GoodVoiceHelper.getSeasonIdByStartTime(activityId, DateTimeUtil.toLocalDateTime(groupData.getCurrentSeasonStartTime()));
        if (globalSeasonId != seasonId) {
            request.addCallbackParam(Text.好声音页面数据超时);
            return;
        }
        PlayerGoodVoiceDb voiceDb = groupData.getPlayerVoiceDbMap().get(voiceOwner);
        if (voiceDb == null) {
            request.addCallbackParam(Text.好声音声音不存在);
            return;
        }
        request.addCallbackParam(Text.没有异常);

        PbProtocol.GoodVoiceActivityMsgMoreRst.Builder rst = PbProtocol.GoodVoiceActivityMsgMoreRst.newBuilder();
        for (int i = voiceDb.getMsgList().size() - 1; i >= 0; i--) {
            GoodVoiceMsgDb msgDb = voiceDb.getMsgList().get(i);
            if (msgId > 0 && msgDb.getId() >= msgId) {
                continue;
            }
            rst.addMsg(GoodVoiceHelper.genVoiceMsgPb(msgDb));
            if (rst.getMsgCount() >= GoodVoiceActivityService.MSG_PAGE_SIZE) {
                break;
            }
        }
        request.addCallbackParam(rst.build().toByteArray());
    }

    @MessageMethod(description = "获取推荐")
    private static void goodVoiceRecommend(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int groupId = params.getParam(0);
        int activityId = params.getParam(1);
        int seasonId = params.getParam(2);
        Set<Long> leagueMember = params.getParam(3);
        int filter = params.getParam(4);
        Set<Long> votePlayers = params.getParam(5);
        List<Long> friendIds = params.getParam(6);

        ActivityCrossData crossData = CrossDataManager.getInstance().getData(CrossDataType.Activity, groupId);
        if (crossData == null) {
            request.addCallbackParam(Text.活动暂未开启);
            return;
        }
        GoodVoiceActivityGroupData groupData = crossData.getActivityGroupData(activityId);
        if (groupData == null) {
            request.addCallbackParam(Text.活动暂未开启);
            return;
        }
        int globalSeasonId = GoodVoiceHelper.getSeasonIdByStartTime(activityId, DateTimeUtil.toLocalDateTime(groupData.getCurrentSeasonStartTime()));
        if (globalSeasonId != seasonId) {
            request.addCallbackParam(Text.好声音页面数据超时);
            return;
        }

        List<Long> allPlayerIds = new ArrayList<>(groupData.getGoodVoicePlayers());
        Map<Long, PlayerGoodVoiceDb> dbMap = new ConcurrentHashMap<>(groupData.getPlayerVoiceDbMap());
        Map<Long, Integer> rankMap = new ConcurrentHashMap<>(groupData.playerRankMap);

        ThreadPool.execute(new GoodVoiceRecommendActiveCallbackTask(request, allPlayerIds, filter, playerId, leagueMember, votePlayers, dbMap, rankMap, friendIds));
    }

    @MessageMethod(description = "投票")
    private static void goodVoiceVote(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int groupId = params.getParam(0);
        int activityId = params.getParam(1);
        int seasonId = params.getParam(2);
        long voiceOwner = params.getParam(3);

        ActivityCrossData crossData = CrossDataManager.getInstance().getData(CrossDataType.Activity, groupId);
        if (crossData == null) {
            request.addCallbackParam(Text.活动暂未开启);
            return;
        }
        GoodVoiceActivityGroupData groupData = crossData.getActivityGroupData(activityId);
        if (groupData == null) {
            request.addCallbackParam(Text.活动暂未开启);
            return;
        }
        if (groupData.isOutOperationTime()) {
            request.addCallbackParam(Text.好声音当前不可操作);
            return;
        }
        int globalSeasonId = GoodVoiceHelper.getSeasonIdByStartTime(activityId, DateTimeUtil.toLocalDateTime(groupData.getCurrentSeasonStartTime()));
        if (globalSeasonId != seasonId) {
            request.addCallbackParam(Text.好声音页面数据超时);
            return;
        }
        PlayerGoodVoiceDb voiceDb = groupData.getPlayerVoiceDbMap().get(voiceOwner);
        if (voiceDb == null) {
            request.addCallbackParam(Text.好声音声音不存在);
            return;
        }
        GoodVoiceHelper.doVoiceVote(voiceDb, 1);
        groupData.resetRankMap(true);
        request.addCallbackParam(Text.没有异常);
    }

    @MessageMethod(description = "排行")
    private static void goodVoiceRank(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int groupId = params.getParam(0);
        int activityId = params.getParam(1);
        int seasonId = params.getParam(2);
        Set<Long> leagueMember = params.getParam(3);

        ActivityCrossData crossData = CrossDataManager.getInstance().getData(CrossDataType.Activity, groupId);
        if (crossData == null) {
            request.addCallbackParam(Text.活动暂未开启);
            return;
        }
        GoodVoiceActivityGroupData groupData = crossData.getActivityGroupData(activityId);
        if (groupData == null) {
            request.addCallbackParam(Text.活动暂未开启);
            return;
        }
        int globalSeasonId = GoodVoiceHelper.getSeasonIdByStartTime(activityId, DateTimeUtil.toLocalDateTime(groupData.getCurrentSeasonStartTime()));
        if (globalSeasonId != seasonId) {
            request.addCallbackParam(Text.好声音页面数据超时);
            return;
        }

        List<Long> allPlayerIds = new ArrayList<>(groupData.playerRankMap.keySet());
        Map<Integer, PlayerGoodVoiceDb> dbMap = new ConcurrentHashMap<>(groupData.rank200Map);

        ThreadPool.execute(new GoodVoiceRankActiveCallbackTask(request, allPlayerIds, playerId, dbMap, leagueMember, groupData));
    }

    @MessageMethod(description = "历史")
    private static void goodVoiceHis(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int groupId = params.getParam(0);
        int activityId = params.getParam(1);
        int seasonId = params.getParam(2);
        long localStartTime = params.getParam(3);

        ActivityCrossData crossData = CrossDataManager.getInstance().getData(CrossDataType.Activity, groupId);
        if (crossData == null) {
            request.addCallbackParam(Text.活动暂未开启);
            return;
        }
        List<TopPlayerGoodVoiceDb> historyTops = new ArrayList<>(crossData.getGoodVoiceTopMap().values());
        historyTops.sort(Comparator.comparingInt(TopPlayerGoodVoiceDb::getSeasonId));
        PbProtocol.GoodVoiceActivityHistoryRst.Builder rst1 = PbProtocol.GoodVoiceActivityHistoryRst.newBuilder();
        int localSeasonId = GoodVoiceHelper.getSeasonIdByStartTime(activityId, DateTimeUtil.toLocalDateTime(localStartTime));
        ThreadPool.execute(new GoodVoiceHisActiveCallbackTask(request, historyTops, localSeasonId, rst1));
    }

    @MessageMethod(description = "获取历史第一玩家声音信息")
    private static void goodVoiceHisTopVoice(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int groupId = params.getParam(0);
        int activityId = params.getParam(1);
        int seasonId = params.getParam(2);

        ActivityCrossData crossData = CrossDataManager.getInstance().getData(CrossDataType.Activity, groupId);
        if (crossData == null) {
            request.addCallbackParam(Text.活动暂未开启);
            return;
        }
        TopPlayerGoodVoiceDb hisTopDb = crossData.getGoodVoiceTopMap().get(seasonId);
        if (hisTopDb == null) {
            request.addCallbackParam(Text.任务数据不存在);
            return;
        }
        PbProtocol.GoodVoiceActivityHisTopVoiceRst.Builder rst = PbProtocol.GoodVoiceActivityHisTopVoiceRst.newBuilder();

        ThreadPool.execute(new GoodVoiceHisTopVoiceActiveCallbackTask(request, hisTopDb, rst));
    }

    @MessageMethod(description = "获取历史第一玩家声音音频")
    private static void goodVoiceHisTopVoiceByte(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int groupId = params.getParam(0);
        int activityId = params.getParam(1);
        int seasonId = params.getParam(2);

        ActivityCrossData crossData = CrossDataManager.getInstance().getData(CrossDataType.Activity, groupId);
        if (crossData == null) {
            request.addCallbackParam(Text.活动暂未开启);
            return;
        }
        TopPlayerGoodVoiceDb hisTopDb = crossData.getGoodVoiceTopMap().get(seasonId);
        if (hisTopDb == null) {
            request.addCallbackParam(Text.任务数据不存在);
            return;
        }
        request.addCallbackParam(Text.没有异常);
        request.addCallbackParam(hisTopDb.getVoiceBytes());
    }

    @MessageMethod(description = "获取历史第一玩家留言")
    private static void goodVoiceHisTopMsgPlayer(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int groupId = params.getParam(0);
        int activityId = params.getParam(1);
        int seasonId = params.getParam(2);
        long msgId = params.getParam(3);

        ActivityCrossData crossData = CrossDataManager.getInstance().getData(CrossDataType.Activity, groupId);
        if (crossData == null) {
            request.addCallbackParam(Text.活动暂未开启);
            return;
        }
        TopPlayerGoodVoiceDb hisTopDb = crossData.getGoodVoiceTopMap().get(seasonId);
        if (hisTopDb == null) {
            request.addCallbackParam(Text.任务数据不存在);
            return;
        }
        PbProtocol.GoodVoiceActivityHisTopMsgMoreRst.Builder rst = PbProtocol.GoodVoiceActivityHisTopMsgMoreRst.newBuilder();
        for (int i = hisTopDb.getMsgList().size() - 1; i >= 0; i--) {
            GoodVoiceMsgDb msgDb = hisTopDb.getMsgList().get(i);
            if (msgId == 0 || msgDb.getId() < msgId) {
                rst.addMsg(GoodVoiceHelper.genVoiceMsgPb(msgDb));
            }
            if (rst.getMsgCount() >= GoodVoiceActivityService.MSG_PAGE_SIZE) {
                break;
            }
        }
        request.addCallbackParam(Text.没有异常);
        request.addCallbackParam(rst.build().toByteArray());
    }

    private static class GoodVoiceHisActiveCallbackTask extends AbstractActiveCallbackTask {
        private final List<TopPlayerGoodVoiceDb> historyTops;
        private final int localSeasonId;
        private final PbProtocol.GoodVoiceActivityHistoryRst.Builder rst1;

        public GoodVoiceHisActiveCallbackTask(PlayerCommandRequest request, List<TopPlayerGoodVoiceDb> historyTops, int localSeasonId, PbProtocol.GoodVoiceActivityHistoryRst.Builder rst1) {
            super(request);
            this.historyTops = historyTops;
            this.localSeasonId = localSeasonId;
            this.rst1 = rst1;
        }

        @Override
        protected void execute() {
            for (TopPlayerGoodVoiceDb historyTop : historyTops) {
                if (historyTop.getSeasonId() <= localSeasonId) {
                    continue;
                }
                MiniGamePlayer miniPlayer = PlayerHelper.getMiniPlayer(historyTop.getPlayerId());
                rst1.addHisInfo(GoodVoiceHelper.genHistoryPb(miniPlayer, historyTop));
            }
            request.addCallbackParam(Text.没有异常);
            request.addCallbackParam(rst1.build().toByteArray());
        }
    }

    private static class GoodVoiceHisTopVoiceActiveCallbackTask extends AbstractActiveCallbackTask {
        private final TopPlayerGoodVoiceDb hisTopDb;
        private final PbProtocol.GoodVoiceActivityHisTopVoiceRst.Builder rst;

        public GoodVoiceHisTopVoiceActiveCallbackTask(PlayerCommandRequest request, TopPlayerGoodVoiceDb hisTopDb, PbProtocol.GoodVoiceActivityHisTopVoiceRst.Builder rst) {
            super(request);
            this.hisTopDb = hisTopDb;
            this.rst = rst;
        }

        @Override
        protected void execute() {
            //异步填充数据
            MiniGamePlayer miniPlayer = PlayerHelper.getMiniPlayer(hisTopDb.getPlayerId());
            if (miniPlayer != null) {
                rst.setUser(miniPlayer.genMinMiniUser())
                        .setRank(1)
                        .setGoodVoiceInfo(GoodVoiceHelper.genGoodVoiceInfo(hisTopDb));
                for (int i = hisTopDb.getMsgList().size() - 1; i >= 0; i--) {
                    GoodVoiceMsgDb msgDb = hisTopDb.getMsgList().get(i);
                    rst.addMsg(GoodVoiceHelper.genVoiceMsgPb(msgDb));
                    if (rst.getMsgCount() >= GoodVoiceActivityService.MSG_PAGE_SIZE) {
                        break;
                    }
                }
            }
            request.addCallbackParam(Text.没有异常);
            request.addCallbackParam(rst.build().toByteArray());
        }
    }

    private static class GoodVoicePlayerInfoActiveCallbackTask extends AbstractActiveCallbackTask {
        private final long voiceOwner;
        private final PbProtocol.GoodVoiceActivityVoiceRst.Builder rst;

        public GoodVoicePlayerInfoActiveCallbackTask(PlayerCommandRequest request, long voiceOwner, PbProtocol.GoodVoiceActivityVoiceRst.Builder rst) {
            super(request);
            this.voiceOwner = voiceOwner;
            this.rst = rst;
        }

        @Override
        protected void execute() {
            //异步填充数据
            MiniGamePlayer miniPlayer = PlayerHelper.getMiniPlayer(voiceOwner);
            if (miniPlayer != null) {
                rst.setUser(miniPlayer.genMinMiniUser());
            }
            request.addCallbackParam(Text.没有异常);
            request.addCallbackParam(rst.build().toByteArray());
        }
    }

    private static class GoodVoiceRankActiveCallbackTask extends AbstractActiveCallbackTask {
        private final List<Long> allPlayerIds;
        private final long playerId;
        private final Map<Integer, PlayerGoodVoiceDb> dbMap;
        private final Set<Long> leagueMember;
        private final GoodVoiceActivityGroupData groupData;

        public GoodVoiceRankActiveCallbackTask(PlayerCommandRequest request, List<Long> allPlayerIds, long playerId, Map<Integer, PlayerGoodVoiceDb> dbMap, Set<Long> leagueMember, GoodVoiceActivityGroupData groupData) {
            super(request);
            this.allPlayerIds = allPlayerIds;
            this.playerId = playerId;
            this.dbMap = dbMap;
            this.leagueMember = leagueMember;
            this.groupData = groupData;
        }

        @Override
        protected void execute() {
            MiniGamePlayer myPlayer = PlayerHelper.getMiniPlayer(playerId);
            boolean containSelf = false;
            PbProtocol.GoodVoiceActivityRankRst.Builder rst1 = PbProtocol.GoodVoiceActivityRankRst.newBuilder();
            for (int rank = 1; rank <= GoodVoiceActivityService.constant.crossServerRank; rank++) {
                PlayerGoodVoiceDb voiceDb = dbMap.get(rank);
                if (voiceDb == null) {
                    break;
                }
                long pid = voiceDb.getPlayerId();
                MiniGamePlayer miniPlayer = pid == playerId ? myPlayer : PlayerHelper.getMiniPlayer(pid);
                if (miniPlayer == null) {
                    continue;
                }
                int distance = GoodVoiceHelper.calculateCityDistance(myPlayer, miniPlayer);
                PbActivity.GoodVoiceUserInfo rankBuilder = GoodVoiceHelper.genRecommend(miniPlayer, voiceDb, rank,
                        false, leagueMember.contains(pid), distance);
                rst1.addRankData(rankBuilder);
                if (pid == playerId) {
                    rst1.setMyRank(rankBuilder);
                    containSelf = true;
                }
            }
            if (!containSelf) {
                MiniGamePlayer miniPlayer = PlayerHelper.getMiniPlayer(playerId);
                if (miniPlayer != null) {
                    PbActivity.GoodVoiceUserInfo.Builder rankBuilder = PbActivity.GoodVoiceUserInfo.newBuilder().setMiniUser(miniPlayer.genMinMiniUser())
                            .setRank(-1);
                    PlayerGoodVoiceDb voiceDb = groupData.getPlayerVoiceDbMap().get(playerId);
                    if (voiceDb != null) {
                        rankBuilder.setGoodVoiceInfo(GoodVoiceHelper.genGoodVoiceInfo(voiceDb));
                    }
                    rst1.setMyRank(rankBuilder);
                }
            }
            request.addCallbackParam(Text.没有异常);
            request.addCallbackParam(rst1.build().toByteArray());
        }
    }

    private static class GoodVoiceRecommendActiveCallbackTask extends AbstractActiveCallbackTask {
        private final List<Long> allPlayerIds;
        private final int filter;
        private final long playerId;
        private final Set<Long> leagueMember;
        private final Set<Long> votePlayers;
        private final Map<Long, PlayerGoodVoiceDb> dbMap;
        private final Map<Long, Integer> rankMap;
        List<Long> friendIds;

        public GoodVoiceRecommendActiveCallbackTask(PlayerCommandRequest request, List<Long> allPlayerIds
                , int filter, long playerId, Set<Long> leagueMember, Set<Long> votePlayers
                , Map<Long, PlayerGoodVoiceDb> dbMap, Map<Long, Integer> rankMap, List<Long> friendIds) {
            super(request);
            this.allPlayerIds = allPlayerIds;
            this.filter = filter;
            this.playerId = playerId;
            this.leagueMember = leagueMember;
            this.votePlayers = votePlayers;
            this.dbMap = dbMap;
            this.rankMap = rankMap;
            this.friendIds = friendIds;
        }

        @Override
        protected void execute() {
            List<Long> recommends = new ArrayList<>();
            List<Long> recommends1 = new ArrayList<>();
            Set<Long> friendSet = new HashSet<>();
            for (int i = allPlayerIds.size() - 1; i >= 0; i--) {
                long pid = allPlayerIds.get(i);
                if (filter == GoodVoiceActivityService.RECOMMEND_FILTER_FRIEND && !friendIds.contains(pid)) { //好友过滤
                    continue;
                } else if (filter == GoodVoiceActivityService.RECOMMEND_FILTER_LEAGUE && leagueMember.contains(pid)) { //同盟过滤
                    continue;
                } else if (filter == GoodVoiceActivityService.RECOMMEND_FILTER_CITY) { //同城
                    continue;
                }
                if (votePlayers.contains(pid)) {
                    recommends1.add(pid);
                } else {
                    recommends.add(pid);
                }
                if (recommends.size() >= GoodVoiceActivityService.constant.recommendNum) {
                    break;
                }
            }
            if (recommends.size() < GoodVoiceActivityService.constant.recommendNum) {
                for (Long pid : recommends1) {
                    recommends.add(pid);
                    if (recommends.size() >= GoodVoiceActivityService.constant.recommendNum) {
                        break;
                    }
                }
            }
            if (filter != GoodVoiceActivityService.RECOMMEND_FILTER_FRIEND) {
                for (Long pid : recommends) {
                    if (!friendIds.contains(pid)) {
                        friendSet.add(pid);
                    }
                }
            }
            MiniGamePlayer myPlayer = PlayerHelper.getMiniPlayer(playerId);
            PbProtocol.GoodVoiceActivityRecommendRst.Builder rst1 = PbProtocol.GoodVoiceActivityRecommendRst.newBuilder();
            //异步填充数据
            for (Long recommend : recommends) {
                MiniGamePlayer miniPlayer = recommend == playerId ? myPlayer : PlayerHelper.getMiniPlayer(recommend);
                if (miniPlayer == null) {
                    continue;
                }
                PlayerGoodVoiceDb voiceDb = dbMap.get(recommend);
                if (voiceDb == null) {
                    continue;
                }
                int distance = GoodVoiceHelper.calculateCityDistance(myPlayer, miniPlayer);
                rst1.addRecommends(GoodVoiceHelper.genRecommend(miniPlayer, voiceDb, rankMap.getOrDefault(recommend, -1),
                        filter == GoodVoiceActivityService.RECOMMEND_FILTER_FRIEND || friendSet.contains(recommend), leagueMember.contains(recommend), distance));
            }
            request.addCallbackParam(Text.没有异常);
            request.addCallbackParam(rst1.build().toByteArray());
        }
    }
}

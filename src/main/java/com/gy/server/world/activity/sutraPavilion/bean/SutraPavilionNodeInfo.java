package com.gy.server.world.activity.sutraPavilion.bean;

import com.gy.server.game.activity.ActivityService;
import com.gy.server.game.activity.data.sutraPavilion.SutraPavilionActivityData;
import com.gy.server.game.activity.data.sutraPavilion.template.TheSutraRepositoryIncidentTemplate;
import com.gy.server.packet.PbActivityTeam;
import com.gy.server.packet.PbCommons;
import com.ttlike.server.tl.baselib.serialize.scene.ScenePosition2D;
import org.apache.commons.lang3.tuple.Pair;

/**
 * 双人组队-夜探藏经阁地块信息
 */
public class SutraPavilionNodeInfo {

    /**
     * 解锁
     */
    public static final int had_unlock = 0;

    /**
     * 移动
     */
    public static final int had_move = 1;

    /**
     * 触发
     */
    public static final int had_trigger = 2;

    /**
     * 层数id
     */
    private int floorId;

    /**
     * x坐标
     */
    private int x;

    /**
     * y坐标
     */
    private int y;

    /**
     * 事件id
     */
    private int eventId;
    private int monsterKillEventId;

    /**
     * 地块状态
     */
    private int status;

    /**
     * 怪物组id
     */
    private int monsterGroupId;

    /**
     * 解锁玩家id
     */
    private long unlockPlayerId;

    public SutraPavilionNodeInfo(){

    }

    public SutraPavilionNodeInfo(int floorId, int x, int y){
        this.floorId = floorId;
        this.x = x;
        this.y = y;
    }

    public int getMonsterKillEventId() {
        return monsterKillEventId;
    }

    public void setMonsterKillEventId(int monsterKillEventId) {
        this.monsterKillEventId = monsterKillEventId;
    }

    public long getUnlockPlayerId() {
        return unlockPlayerId;
    }

    public void setUnlockPlayerId(long unlockPlayerId) {
        this.unlockPlayerId = unlockPlayerId;
    }

    /**
     * 修改状态
     * @param statusType 状态类型
     * @param isReal 是否为真
     */
    public void setStatus(int statusType, boolean isReal){
        if(isReal){
            status |= (1 << statusType);
        }else{
            status &= ~(1 << statusType);
        }
    }

    /**
     * 获得状态类型
     */
    public boolean getStatus(int statusType){
        return (status & (1 << statusType)) > 0;
    }

    /**
     * 地块id
     * @return
     */
    public int getNodeId(){
        return calNodeId(x, y);
    }


    private static final int point_multi = 1000;
    /**
     * 计算地块id （假设边长不会超过1000）
     * @param x x坐标
     * @param x y坐标
     * @return 地块id
     */
    public static int calNodeId(int x, int y){
        return x * point_multi + y;
    }

    public static Pair<Integer, Integer> calXY(int nodeId){
        return Pair.of(nodeId / point_multi, nodeId % point_multi);
    }

    public static int calNodeId(ScenePosition2D pos){
        return calNodeId(pos.getX(), pos.getY());
    }

    public int getX() {
        return x;
    }

    public void setX(int x) {
        this.x = x;
    }

    public int getY() {
        return y;
    }

    public void setY(int y) {
        this.y = y;
    }

    public int getEventId() {
        return eventId;
    }

    public TheSutraRepositoryIncidentTemplate getEventTemplate(int activityId){
        SutraPavilionActivityData data = ActivityService.getActivityData(activityId);
        return data.getEventTemplates().get(eventId);
    }

    public void setEventId(int eventId) {
        this.eventId = eventId;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getFloorId() {
        return floorId;
    }

    public void setFloorId(int floorId) {
        this.floorId = floorId;
    }

    public int getMonsterGroupId() {
        return monsterGroupId;
    }

    public void setMonsterGroupId(int monsterGroupId) {
        this.monsterGroupId = monsterGroupId;
    }

    public PbActivityTeam.SutraPavilionNodeInfo genNodeInfo(){
        PbActivityTeam.SutraPavilionNodeInfo.Builder builder = PbActivityTeam.SutraPavilionNodeInfo.newBuilder();
        builder.setPointInfo(PbCommons.KeyValueDb.newBuilder().setIntKey(x).setIntValue(y));
        builder.setStatus(status);
        builder.setEventId(eventId);
        return builder.build();
    }
}

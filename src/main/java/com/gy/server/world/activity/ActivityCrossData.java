package com.gy.server.world.activity;

import com.baidu.bjf.remoting.protobuf.annotation.Ignore;
import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.gy.server.core.command.CommandRequest;
import com.gy.server.core.cos.COSManager;
import com.gy.server.core.log.SystemLogger;
import com.gy.server.game.activity.ActivityType;
import com.gy.server.game.event.ServerEvent;
import com.gy.server.game.event.ServerEventHandler;
import com.gy.server.game.event.ServerEventType;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.player.Player;
import com.gy.server.game.util.pb.Bytes;
import com.gy.server.utils.ReflectionUtil;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.gy.server.world.crossData.CrossData;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.serialize.goodVoice.TopPlayerGoodVoiceDb;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @program: tl_game_4
 * @description: 跨服活动world维护数据，同一个分组下，所有活动均在这里
 * @author: Huang.Xia
 * @create: 2025/2/7
 **/
public class ActivityCrossData extends CrossData implements ServerEventHandler {

    @Ignore
    private static ServerEventType[] careEvents;

    //通过反射，找出所有跨服活动监听的事件
    static {
        Set<ServerEventType> result = new HashSet<>();

        Set<Class<? extends ActivityGroupData>> classSet = ReflectionUtil.getSubClassSetNoInterfaceAndAbstract(ActivityGroupData.class, "com.gy.server.world.activity");
        for (Class<? extends ActivityGroupData> clazz : classSet) {

            try {
                Object obj = clazz.newInstance();
                if(obj instanceof ActivityGroupData){
                    result.addAll(((ActivityGroupData) obj).getCareEvent());
                }
            }catch (Exception e){
                throw new RuntimeException(e);
            }
        }
        careEvents = result.toArray(new ServerEventType[0]);
    }

    @Ignore
    private Map<Integer, ActivityGroupData> activityData = new ConcurrentHashMap<>();

    @Protobuf(order = 1)
    private Map<Integer, Bytes> dataBytes = new HashMap<>();

    /**
     * 天龙好声音活动历史数据--战区历史第一名的数据 k:seasonId, v:声音数据
     * !!! 活动不能多开， 好声音的历史组数据不关联 activityId
     */
    @Protobuf(order = 2)
    private Map<Integer, TopPlayerGoodVoiceDb> goodVoiceTopMap = new HashMap<>();
    /**
     * 天龙好声音活动历史数据--玩家对应的排名 k:pid v:rank
     */
    @Protobuf(order = 3)
    private Map<Long, Integer> goodVoiceRewardInfoMap = new HashMap<>();
    /**
     * 天龙好声音活动历史数据- 待删除的cos上文件
     */
    @Protobuf(order = 4)
    private Set<String> waitingDelCosVoiceUrls = new HashSet<>();


    @Override
    public void tick() {
        //tick
        this.activityData.values().forEach(ActivityGroupData::tick);
        //好声音检测发放奖励
        goodVoiceCheckNoticeRewardMail();
        //检测删除cos上声音文件
        if (!waitingDelCosVoiceUrls.isEmpty()) {
            List<String> rmUrls = new ArrayList<>();
            for (String voiceUrl : this.waitingDelCosVoiceUrls) {
                rmUrls.add(voiceUrl);
                if (rmUrls.size() >= 100) {
                    break;
                }
            }
            rmUrls.forEach(this.waitingDelCosVoiceUrls::remove);
            ThreadPool.execute(() -> COSManager.getInstance().deleteObjectByKeyList(rmUrls));
        }
    }

    @Override
    public void init() {
        activityData.clear();
        dataBytes.forEach((k,v)-> {
            try {
                ActivityGroupData activityGroupData = PbUtilCompress.decode(ActivityGroupData.class, v.getBytes());
                ActivityType type = ActivityType.getActivityTypeById(activityGroupData.getActivityType());
                //拿到实际的子类型
                activityGroupData = type.createActivityGroupData(activityGroupData.getActivityId(),groupId);
                activityGroupData = PbUtilCompress.decode(activityGroupData.getClass(), v.getBytes());
                activityData.put(activityGroupData.getActivityId(), activityGroupData);
            }catch (Exception e){
                //若遇到数据转换异常，可能是活动类型不匹配，需要终止并寻找原因
                SystemLogger.error("init activity data error, activityId : " + k);
                throw new RuntimeException(e);
            }
        });

        activityData.forEach((k,v)->v.init());
    }

    @Override
    protected void beforeSave() {
        activityData.forEach((k,v)->v.beforeSave());
        dataBytes.clear();
        activityData.forEach((k,v)->dataBytes.put(k, new Bytes(PbUtilCompress.encode(v))));
    }

    /**
     * 获取指定活动World数据
     * <p>
     * 有可能返还Null，注意判断为Null情况
     */
    @SuppressWarnings("unchecked")
    public <T extends ActivityGroupData> T getActivityGroupData(int activityId) {
        if(activityData.containsKey(activityId)) {
            return (T) this.activityData.get(activityId);
        }
        return null;
    }

    public void createActivityGroupData(int activityId, ActivityType activityType){
        ActivityGroupData activityGroupData = activityType.createActivityGroupData(activityId, groupId);
        activityGroupData.init();
        activityData.put(activityId, activityGroupData);
    }

    @Override
    public ServerEventType[] getEventTypes() {
        return careEvents;
    }

    @Override
    public void handle(ServerEvent event) {
        this.activityData.values().stream().
                filter(worldData -> worldData.getCareEvent().contains(event.getEventType())).
                forEach(worldData -> worldData.eventHandle(event));
    }

    /**
     * 好声音检测发放赛季排名奖励
     */
    public void goodVoiceCheckNoticeRewardMail() {
        if (goodVoiceRewardInfoMap.isEmpty()) {
            return;
        }
        Map<Long, Integer> rewardMap = new HashMap<>();
        int nodeId = 0;
        for (Map.Entry<Long, Integer> entry : goodVoiceRewardInfoMap.entrySet()) {
            if (nodeId == 0 || nodeId == Player.getRealServerId(entry.getKey())) {
                nodeId = Player.getRealServerId(entry.getKey());
                rewardMap.put(entry.getKey(), entry.getValue());
                GameLogger.goodVoice("cross good voice tick notice send mail " + nodeId + ", " + entry.getKey() + "," + entry.getValue());
            }
        }
        //移除待发送
        rewardMap.forEach(goodVoiceRewardInfoMap::remove);
        //通知节点发奖励
        CommandRequest req = CommandRequests.newServerCommandRequest("GoodVoiceGameCommandService.seasonFinish");
        TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, nodeId, req, rewardMap);
    }


    public Map<Integer, ActivityGroupData> getActivityData() {
        return activityData;
    }

    public void setActivityData(Map<Integer, ActivityGroupData> activityData) {
        this.activityData = activityData;
    }

    public Map<Integer, Bytes> getDataBytes() {
        return dataBytes;
    }

    public void setDataBytes(Map<Integer, Bytes> dataBytes) {
        this.dataBytes = dataBytes;
    }

    public Map<Integer, TopPlayerGoodVoiceDb> getGoodVoiceTopMap() {
        return goodVoiceTopMap;
    }

    public void setGoodVoiceTopMap(Map<Integer, TopPlayerGoodVoiceDb> goodVoiceTopMap) {
        this.goodVoiceTopMap = goodVoiceTopMap;
    }

    public Map<Long, Integer> getGoodVoiceRewardInfoMap() {
        return goodVoiceRewardInfoMap;
    }

    public void setGoodVoiceRewardInfoMap(Map<Long, Integer> goodVoiceRewardInfoMap) {
        this.goodVoiceRewardInfoMap = goodVoiceRewardInfoMap;
    }

    public Set<String> getWaitingDelCosVoiceUrls() {
        return waitingDelCosVoiceUrls;
    }

    public void setWaitingDelCosVoiceUrls(Set<String> waitingDelCosVoiceUrls) {
        this.waitingDelCosVoiceUrls = waitingDelCosVoiceUrls;
    }
}

package com.gy.server.world.activity;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.gy.server.game.event.ServerEvent;
import com.gy.server.game.event.ServerEventType;
import com.gy.server.packet.PbActivity;

import java.util.Collection;
import java.util.Collections;

/**
 * @program: tl_game_4
 * @description: 活动跨服数据
 * @author: Huang.<PERSON>
 * @create: 2025/2/8
 **/
public class ActivityGroupData {
    /**
     * 活动id
     */
    @Protobuf(order = 100)
    protected int activityId;

    @Protobuf(order = 101)
    protected int activityType;

    @Protobuf(order = 102)
    protected int groupId;

    public int getActivityId() {
        return activityId;
    }

    public void setActivityId(int activityId) {
        this.activityId = activityId;
    }

    public int getActivityType() {
        return activityType;
    }

    public void setActivityType(int activityType) {
        this.activityType = activityType;
    }

    public int getGroupId() {
        return groupId;
    }

    public void setGroupId(int groupId) {
        this.groupId = groupId;
    }

    public void tick() {
    }

    public Collection<ServerEventType> getCareEvent() {
        return Collections.emptySet();
    }

    public void eventHandle(ServerEvent event) {
    }

    public void finish() {}

    public void init(){}

    public void beforeSave(){}

    /**
     * 是否应该移除
     * 好声音一轮完后，需要移除重新开启新一轮
     */
    public boolean needRemove() {
        return false;
    }

    /**
     * 跨服数据填充
     * 主线程中填充模块数据
     * 先调用
     */
    public void fillModuleSync(PbActivity.ActivityModule.Builder builder, long playerId) {
    }

    /**
     * 跨服数据填充
     * 异步线程中填充模块数据
     * 后调用
     */
    public void fillModuleAsync(PbActivity.ActivityModule.Builder builder, long playerId) {
    }

}

package com.gy.server.world.activity.sutraPavilion.stage;

import com.gy.server.game.activity.ActivityService;
import com.gy.server.game.activity.data.sutraPavilion.SutraPavilionActivityData;
import com.gy.server.game.activity.data.sutraPavilion.template.TheSutraRepositoryIncidentTemplate;
import com.gy.server.game.activity.team.ActivityTeamService;
import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.combat.StageType;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.combat.unit.TeamUnit;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.player.Player;
import com.gy.server.game.record.RecordManager;
import com.gy.server.game.record.RecordType;
import com.gy.server.game.record.combat.CombatRecord;
import com.gy.server.game.robot.RobotHelper;
import com.gy.server.game.robot.RobotType;
import com.gy.server.game.robot.bean.BaseRobot;
import com.gy.server.game.world.WorldLevelUseTypeEnum;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbRecord;
import com.gy.server.packet.PbRobot;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.world.activity.sutraPavilion.bean.SutraPavilionCombatRecord;
import com.gy.server.world.activityTeam.ActivityTeamHelper;

import java.util.*;

/**
 * 双人组队-夜探藏经阁 战斗
 */
public class SutraPavilionStage extends AbstractStage {
    //进攻玩家id
    Player atkPlayer;
    //怪物事件
    TheSutraRepositoryIncidentTemplate eventTemplate;

    //剩余血量
    Map<Integer, Long> surplusHps;

    int activityId;
    StageType stageType = StageType.surtaPavilion;
    LineupType lineupType = LineupType.surtaPavilion;
    BaseRobot defRobot;
    int floorId;

    public SutraPavilionStage(Player atkPlayer, int activityId, int floorId, TheSutraRepositoryIncidentTemplate eventTemplate){
        this.atkPlayer = atkPlayer;
        this.eventTemplate = eventTemplate;
        this.surplusHps = null;
        this.activityId = activityId;
        this.floorId = floorId;
    }

    public SutraPavilionStage(Player atkPlayer, int activityId, int floorId, TheSutraRepositoryIncidentTemplate eventTemplate, Map<Integer, Long> surplusHps){
        this.atkPlayer = atkPlayer;
        this.eventTemplate = eventTemplate;
        this.surplusHps = surplusHps;
        this.activityId = activityId;
        this.floorId = floorId;
    }


    @Override
    public void init() {
        List<HeroUnit> atks = atkPlayer.getLineupModel().createHeroUnits(stageType, lineupType);
        TeamUnit atkTeam = new TeamUnit(getNewId(), atks);

        String[] param = eventTemplate.getParam();
        int defRobotId = Integer.parseInt(param[4]);
        defRobot = RobotHelper.getRobotBean(RobotType.SP, defRobotId);
        int worldLevel = WorldLevelUseTypeEnum.theSutraRepository.getWorldLevel();
        List<TeamUnit> defRobotTeamUnits = defRobot.createTeamUnits(this, worldLevel);
        TeamUnit defTeam = defRobotTeamUnits.get(0);
        if(CollectionUtil.isNotEmpty(surplusHps)){
            //继续上次血量
            for (HeroUnit def : defTeam.getUnits()) {
                if(Objects.nonNull(def) && surplusHps.containsKey(def.getPosIndex())){
                    def.getAttributes().setHp(surplusHps.get(def.getPosIndex()));
                }
            }
        }

        init(defRobot.getRobotBattleCollect().getBattleCollectId(), stageType, atkTeam, defTeam, lineupType);
    }

    @Override
    public void afterFinish() {
        PbProtocol.CombatSettlementNotify.Builder builder = genCombatSettlement();
        List<RewardTemplate> allRewards = new ArrayList<>();
        if(isWin()){
            //只有战斗胜利才会获得奖励
            String[] param = eventTemplate.getParam();
            //固定奖励
            String fixReward = param[2];
            int randomRewardId = Integer.parseInt(param[3]);

            allRewards.addAll(RewardTemplate.readListFromText(fixReward));
            SutraPavilionActivityData data = ActivityService.getActivityData(activityId);
            RewardTemplate rewardTemplate = data.randomBoxReward(randomRewardId);
            allRewards.add(rewardTemplate);

            //给玩家奖励
            builder.addAllRewards(Reward.writeCollectionToPb(Reward.addFromTemplates(allRewards, atkPlayer, BehaviorType.sutraPavilionBattle)));

        }else{
            if(CollectionUtil.isEmpty(surplusHps)){
                //首次击杀失败，需要记录协助信息
                TeamUnit teamUnit = getDefs().get(0);
                Map<Integer, Long> surplusHps = new HashMap<>();
                for (HeroUnit unit : teamUnit.getUnits()) {
                    if(Objects.nonNull(unit)){
                        surplusHps.put(unit.getPosIndex(), unit.getAttributes().getHp());
                    }
                }
                int localWarZoneId = ActivityTeamService.getLocalWarZoneId();
                //增加协助信息
                ActivityTeamHelper.business(
                        errorCode -> {
                            GameLogger.error("SurtaPavilionStage deal finish is error !!");
                        },
                        response -> {
                            //NONE
                        },
                        () -> {
                            //本地逻辑
                            ActivityTeamHelper.addSpAssistInfo(activityId, localWarZoneId, atkPlayer.getPlayerId(), floorId, eventTemplate.incidentId, surplusHps);
                        }
                        , atkPlayer, activityId, "ActivityTeamWorldCommandService.sutraPavilionAddAssistInfo", activityId, localWarZoneId, floorId, eventTemplate.incidentId, surplusHps);
            }
        }

        RecordType recordType =  RecordType.surtaPavilion;
        //增加战报
        CombatRecord combatRecord = CombatRecord.create(this, atkPlayer.getMiniPlayer()
                , defRobot.genMiniUser(lineupType), false
                , CombatRecord.OverdueTime.SP, recordType.getId());

        PbRecord.RecordPlayer atkRecordPlayer = RecordManager.genRecordPlayer(atkPlayer, lineupType, getAtks()).build();
        PbRobot.BaseRobot baseRobot = defRobot.genPb();
        //增加进攻方战报
        SutraPavilionCombatRecord atkRecord = new SutraPavilionCombatRecord(combatRecord.getCosKey(), isWin, recordType, combatRecord.getId(),
                atkRecordPlayer, baseRobot, Reward.templateCollectionToReward(allRewards));
        RecordManager.addRecord(atkRecord, atkPlayer);
        RecordManager.save(combatRecord);


        notifyCombatSettlement(atkPlayer, builder.build());
    }
}

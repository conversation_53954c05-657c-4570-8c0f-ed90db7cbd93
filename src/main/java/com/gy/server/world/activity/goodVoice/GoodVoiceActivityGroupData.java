package com.gy.server.world.activity.goodVoice;

import com.baidu.bjf.remoting.protobuf.annotation.Ignore;
import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.activity.ActivityService;
import com.gy.server.game.activity.ActivityTemplate;
import com.gy.server.game.activity.ActivityType;
import com.gy.server.game.activity.helper.GoodVoiceHelper;
import com.gy.server.game.activity.service.GoodVoiceActivityService;
import com.gy.server.game.log.GameLogger;
import com.gy.server.utils.time.DateTimeUtil;
import com.gy.server.world.activity.ActivityCrossData;
import com.gy.server.world.activity.ActivityGroupData;
import com.gy.server.world.crossData.CrossDataManager;
import com.gy.server.world.crossData.CrossDataType;
import com.ttlike.server.tl.baselib.serialize.goodVoice.PlayerGoodVoiceDb;
import com.ttlike.server.tl.baselib.serialize.goodVoice.TopPlayerGoodVoiceDb;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 处理跨服赛季数据
 *
 * <AUTHOR> - [Created on 2025/4/3 18:23]
 **/
public class GoodVoiceActivityGroupData extends ActivityGroupData {

    /**
     * 当前赛季开启时间
     */
    @Protobuf(order = 1)
    private long currentSeasonStartTime;
    /**
     * 本赛季结算状态
     */
    @Protobuf(order = 2)
    private boolean settlementFinish;
    /**
     * 本轮玩家好声音数据
     */
    @Protobuf(order = 3)
    private Map<Long, PlayerGoodVoiceDb> playerVoiceDbMap = new HashMap<>();
    /**
     * 自增留言id
     */
    @Protobuf(order = 4)
    private long globalMsgId;
    /**
     * 前200排名数据
     */
    @Ignore
    public Map<Integer, PlayerGoodVoiceDb> rank200Map = new HashMap<>();
    /**
     * 前200 玩家对应排名
     */
    @Ignore
    public Map<Long, Integer> playerRankMap = new HashMap<>();
    /**
     * 按照时间顺序的玩家-用于推荐
     */
    @Ignore
    private List<Long> goodVoicePlayers = new ArrayList<>();
    @Ignore
    private int lowestTicket;

    public GoodVoiceActivityGroupData(int id, int groupId) {
        this.groupId = groupId;
        this.activityId = id;
        this.activityType = ActivityType.goodVoice.getId();
    }

    public GoodVoiceActivityGroupData() {
    }

    @Override
    public void init() {
        resetRankMap(false);
        List<Long> pids = this.playerVoiceDbMap.values().stream()
                .sorted(Comparator.comparingLong(PlayerGoodVoiceDb::getCreateTime))
                .map(PlayerGoodVoiceDb::getPlayerId).collect(Collectors.toList());
        this.goodVoicePlayers.addAll(pids);
    }

    @Override
    public void tick() {
        long now = ServerConstants.getCurrentTimeMillis();
        if (this.currentSeasonStartTime == 0) {
            //初始第一次数据
            ActivityTemplate activityTemplate = ActivityService.getActivityTemplate(activityId);
            LocalDateTime startTime = activityTemplate.startTime.withDayOfMonth(1);
            LocalDateTime endTime = startTime.plusMonths(1);
            if (ServerConstants.getCurrentTimeLocalDateTime().isAfter(endTime)) {
                startTime = GoodVoiceHelper.getDay5clock().withDayOfMonth(1);
            }
            this.currentSeasonStartTime = DateTimeUtil.toMillis(startTime);
            this.settlementFinish = false;
        } else if (!this.settlementFinish && now > GoodVoiceHelper.calSettlementTime(this.currentSeasonStartTime)) {
            this.doPreSeasonFinish();
        }
    }

    @Override
    public boolean needRemove() {
        return settlementFinish && ServerConstants.getCurrentTimeMillis() > GoodVoiceHelper.calEndTime(this.currentSeasonStartTime);
    }

    public long createMsgId() {
        this.globalMsgId++;
        return globalMsgId;
    }

    /**
     * 非可操作时间段
     */
    public boolean isOutOperationTime() {
        return settlementFinish || ServerConstants.getCurrentTimeMillis() > GoodVoiceHelper.calSettlementTime(this.currentSeasonStartTime);
    }

    /**
     * 结算跨服赛季
     */
    private void doPreSeasonFinish() {
        this.settlementFinish = true;
        ActivityCrossData crossData = CrossDataManager.getInstance().getData(CrossDataType.Activity, groupId);
        //记录预赛的数据
        int seasonId = GoodVoiceHelper.getSeasonIdByStartTime(activityId, DateTimeUtil.toLocalDateTime(this.currentSeasonStartTime));
        List<PlayerGoodVoiceDb> allList = new ArrayList<>(playerVoiceDbMap.values());
        long topPlayerId = 0L;
        if (allList.isEmpty()) {
            crossData.getGoodVoiceTopMap().put(seasonId, new TopPlayerGoodVoiceDb(seasonId));
        } else {
            PlayerGoodVoiceDb voiceDb = allList.get(0);
            topPlayerId = voiceDb.getPlayerId();
            crossData.getGoodVoiceTopMap().put(seasonId, new TopPlayerGoodVoiceDb(voiceDb, seasonId));
        }
        //记录发奖数据 不考虑上赛季奖励发不完的情况
        for (int i = 0; i < allList.size(); i++) {
            int rank = i + 1;
            PlayerGoodVoiceDb voiceDb = allList.get(i);
            crossData.getGoodVoiceRewardInfoMap().put(voiceDb.getPlayerId(), rank);
            if (topPlayerId != voiceDb.getPlayerId() && voiceDb.getVoiceUrl() != null) {
                crossData.getWaitingDelCosVoiceUrls().add(voiceDb.getVoiceUrl());
            }
            GameLogger.goodVoice("cross server good voice final rank " + rank + "," + voiceDb.getPlayerId() + "," + voiceDb.getTicketCount() + "," + voiceDb.getDayTicketTime());
        }
    }

    /**
     * 重新计算排行榜
     */
    public void resetRankMap(boolean useLowestTicket) {
        List<PlayerGoodVoiceDb> list;
        if (useLowestTicket) {
            list = playerVoiceDbMap.values().stream().filter(e -> e.getTicketCount() > 0 && e.getTicketCount() >= lowestTicket).collect(Collectors.toList());
        } else {
            list = playerVoiceDbMap.values().stream().filter(e -> e.getTicketCount() > 0).collect(Collectors.toList());
        }
        list.sort(GoodVoiceHelper.playerVoiceRankComparator);
        this.rank200Map.clear();
        this.playerRankMap.clear();
        for (int i = 0; i < list.size(); i++) {
            this.rank200Map.put(i + 1, list.get(i));
            this.playerRankMap.put(list.get(i).getPlayerId(), i + 1);
            this.lowestTicket = list.get(i).getTicketCount();
            if (this.rank200Map.size() >= GoodVoiceActivityService.constant.crossServerRank) {
                break;
            }
        }
    }

    public long getCurrentSeasonStartTime() {
        return currentSeasonStartTime;
    }

    public void setCurrentSeasonStartTime(long currentSeasonStartTime) {
        this.currentSeasonStartTime = currentSeasonStartTime;
    }

    public boolean isSettlementFinish() {
        return settlementFinish;
    }

    public void setSettlementFinish(boolean settlementFinish) {
        this.settlementFinish = settlementFinish;
    }

    public Map<Long, PlayerGoodVoiceDb> getPlayerVoiceDbMap() {
        return playerVoiceDbMap;
    }

    public void setPlayerVoiceDbMap(Map<Long, PlayerGoodVoiceDb> playerVoiceDbMap) {
        this.playerVoiceDbMap = playerVoiceDbMap;
    }

    public List<Long> getGoodVoicePlayers() {
        return goodVoicePlayers;
    }

    public void setGoodVoicePlayers(List<Long> goodVoicePlayers) {
        this.goodVoicePlayers = goodVoicePlayers;
    }

    public long getGlobalMsgId() {
        return globalMsgId;
    }

    public void setGlobalMsgId(long globalMsgId) {
        this.globalMsgId = globalMsgId;
    }

}

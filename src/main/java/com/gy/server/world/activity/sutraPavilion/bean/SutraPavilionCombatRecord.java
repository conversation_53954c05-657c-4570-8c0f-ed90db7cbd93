package com.gy.server.world.activity.sutraPavilion.bean;

import com.google.common.collect.Sets;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.record.Record;
import com.gy.server.game.record.RecordHelper;
import com.gy.server.game.record.RecordType;
import com.gy.server.game.robot.bean.BaseRobot;
import com.gy.server.packet.PbRecord;
import com.gy.server.packet.PbRobot;
import com.ttlike.server.tl.baselib.serialize.record.RecordDb;
import com.ttlike.server.tl.baselib.serialize.record.SpCombatRecordDb;

import java.util.List;
import java.util.Set;

public class SutraPavilionCombatRecord extends Record {


    private PbRecord.RecordPlayer atkUser;//进攻者信息
    private PbRobot.BaseRobot defInfo;//对手信息

    private List<Reward> rewardList;

    public SutraPavilionCombatRecord(String cosKey, boolean isWin, RecordType type, long combatRecordId,
                                     PbRecord.RecordPlayer atkUser, PbRobot.BaseRobot defInfo, List<Reward> rewardList){
        super(type, isWin, combatRecordId, cosKey);
        this.atkUser = atkUser;
        this.defInfo = defInfo;
        this.rewardList = rewardList;
    }

    @Override
    public void readFromDb2(RecordDb db) {
        SpCombatRecordDb spCombatRecordDb = db.getSpCombatRecordDb();
        this.atkUser = RecordHelper.getInstance().genRecordPlayerPb(spCombatRecordDb.getAtkUser());
        this.defInfo = BaseRobot.genPb(spCombatRecordDb.getRobotInfo());
        this.rewardList = Reward.readFromDb(spCombatRecordDb.getRewardDbList());
    }

    @Override
    public void writeToDb(RecordDb db) {
        SpCombatRecordDb spCombatRecordDb = new SpCombatRecordDb();
        spCombatRecordDb.setAtkUser(RecordHelper.getInstance().genRecordPlayerDb(this.atkUser));
        spCombatRecordDb.setRobotInfo(BaseRobot.genDb(this.defInfo));
        spCombatRecordDb.setRewardDbList(Reward.genDbs(this.rewardList));
        db.setSpCombatRecordDb(spCombatRecordDb);
    }

    @Override
    protected void subReadFromPb(PbRecord.Record record) {
        PbRecord.SurtaPavilionCombatRecord spRecord = record.getSpRecord();
        atkUser = spRecord.getAtk();
        defInfo = spRecord.getRobotInfo();
        this.rewardList = Reward.readFromPb(spRecord.getRewardsList());
    }

    @Override
    protected void subWriteToPb(PbRecord.Record.Builder builder) {
        PbRecord.SurtaPavilionCombatRecord.Builder spRecordBuilder = PbRecord.SurtaPavilionCombatRecord.newBuilder();
        spRecordBuilder.setAtk(atkUser);
        spRecordBuilder.setRobotInfo(defInfo);
        spRecordBuilder.addAllRewards(Reward.writeCollectionToPb(rewardList));
        builder.setSpRecord(spRecordBuilder.build());
    }

    @Override
    protected Set<PbRecord.RecordPlayer> getDetailRecordSet() {
        return Sets.newHashSet(atkUser);
    }
}

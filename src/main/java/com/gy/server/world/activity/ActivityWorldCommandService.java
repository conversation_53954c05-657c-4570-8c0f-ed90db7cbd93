package com.gy.server.world.activity;

import com.gy.server.annotation.MessageMethod;
import com.gy.server.annotation.MessageServiceBean;
import com.gy.server.core.MessageServerType;
import com.gy.server.core.callback.AbstractActiveCallbackTask;
import com.gy.server.core.command.CommandRequestParams;
import com.gy.server.core.command.PlayerCommandRequest;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbActivity;
import com.gy.server.packet.PbActivityTeam;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.world.activityTeam.ActivityTeamHelper;
import com.gy.server.world.activityTeam.ActivityTeamManager;
import com.gy.server.world.activityTeam.bean.ActivityTeam;
import com.gy.server.world.activityTeam.bean.JoinCondition;
import com.gy.server.world.crossData.CrossDataManager;
import com.gy.server.world.crossData.CrossDataType;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.List;
import java.util.Set;

/**
 * @program: tl_game_4
 * @description: 跨服活动world消息服务
 * @author: Huang.Xia
 * @create: 2025/2/11
 **/
@MessageServiceBean(description = "跨服活动 WORLD消息服务", messageServerType = MessageServerType.world)
public class ActivityWorldCommandService {
    @MessageMethod(description = "队伍列表")
    private static void genActivityModule(PlayerCommandRequest request, CommandRequestParams params){
        long playerId = request.getPlayerId();
        int groupId = params.getParam(1);
        int activityId = params.getParam(0);

        ActivityCrossData crossData = CrossDataManager.getInstance().getData(CrossDataType.Activity, groupId);
        if (crossData != null) {
            ActivityGroupData groupData = crossData.getActivityGroupData(activityId);
            if(groupData != null){
                PbActivity.ActivityModule.Builder module = PbActivity.ActivityModule.newBuilder();
                //主线程填充数据
                groupData.fillModuleSync(module, playerId);
                ThreadPool.execute(new GenActivityModuleCallbackTask(request, groupData, module, playerId));
                return;
            }
        }
        request.addCallbackParam(Text.活动暂未开启);
    }

    private static class GenActivityModuleCallbackTask extends AbstractActiveCallbackTask {
        private final ActivityGroupData groupData;
        private final PbActivity.ActivityModule.Builder module;
        private final long playerId;

        public GenActivityModuleCallbackTask(PlayerCommandRequest request, ActivityGroupData groupData, PbActivity.ActivityModule.Builder module, long playerId) {
            super(request);
            this.groupData = groupData;
            this.module = module;
            this.playerId = playerId;
        }

        @Override
        protected void execute() {
            //异步填充数据
            groupData.fillModuleAsync(module, playerId);
            request.addCallbackParam(Text.没有异常);
            request.addCallbackParam(module.build());
        }
    }
}

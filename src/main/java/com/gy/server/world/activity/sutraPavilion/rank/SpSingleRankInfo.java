package com.gy.server.world.activity.sutraPavilion.rank;

public class SpSingleRankInfo {

    private long id;

    private int point;

    private int floorId;

    private long lastUpdateTime;

    public int getFloorId() {
        return floorId;
    }

    public void setFloorId(int floorId) {
        this.floorId = floorId;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public int getPoint() {
        return point;
    }

    public void addPoint(int addedPoint){
        point += addedPoint;
        lastUpdateTime = System.currentTimeMillis();
    }

    public void modifyFloorId(int curFloorId){
        if(curFloorId > floorId){
            floorId = curFloorId;
            lastUpdateTime = System.currentTimeMillis();
        }
    }

    public void setPoint(int point) {
        this.point = point;
    }

    public long getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(long lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

}

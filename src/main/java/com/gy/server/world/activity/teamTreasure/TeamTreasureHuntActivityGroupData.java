package com.gy.server.world.activity.teamTreasure;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.core.log.CommonLogger;
import com.gy.server.game.activity.ActivityService;
import com.gy.server.game.activity.ActivityTemplate;
import com.gy.server.game.activity.ActivityType;
import com.gy.server.game.activity.teamTreasureHunt.TeamTreasureBoxPosition;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.sync.DataSyncModule;
import com.gy.server.game.team.TeamManager;
import com.gy.server.packet.PbActivity;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbSync;
import com.gy.server.packet.PbTeamTreasure;
import com.gy.server.scene.map.bean.SceneMapType;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.world.activity.ActivityGroupData;
import com.gy.server.world.activityTeam.ActivityTeamManager;
import com.gy.server.world.activityTeam.bean.ActivityTeam;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;

public class TeamTreasureHuntActivityGroupData extends ActivityGroupData {
    /**
         * 宝箱信息
         */
        @Protobuf(order = 1)
        private Map<Integer, TeamTreasureHuntBox> boxMap = new HashMap<>();
        /**
         * 宝箱ID 自增
         */
        @Protobuf(order = 2)
        private int uidIncr;


        /**
         * 新增宝箱
         */
        public void addBox(int activityId, int type, int treasureType, int dropId, int dropCount, long belongPlayerId, String belongPlayerName,
                boolean isPerson, TeamTreasureBoxPosition position, long endTime, Set<Long> memberIdSet) {
            TeamTreasureHuntBox huntBox = new TeamTreasureHuntBox();
            huntBox.setUid(genId());
            huntBox.setType(type);
            huntBox.setTreasureType(treasureType);
            huntBox.setDropId(dropId);
            huntBox.setDropCount(dropCount);
            huntBox.setBelongPlayerId(belongPlayerId);
            huntBox.setBelongPlayerName(belongPlayerName);
            huntBox.setPerson(isPerson);
            huntBox.setPosition(position);
            huntBox.setEndTime(endTime);
            if (CollectionUtil.isNotEmpty(memberIdSet)) {
                huntBox.getMemberIdSet().addAll(memberIdSet);
            }
            boxMap.put(huntBox.getUid(), huntBox);
            CommonLogger.info(String.format("teamTreasure add Box, boxId:%s, type:%s, treasureType:%s, playerId:%s", huntBox.getUid(), type, treasureType, belongPlayerId));
            PbTeamTreasure.TeamTreasureSync.Builder builder = PbTeamTreasure.TeamTreasureSync.newBuilder()
                    .setType(PbTeamTreasure.TeamTreasureSync.Type.ADD_BOX)
                    .setActiveId(activityId)
                    .setAddBox(huntBox.genPb());
            //通过场景服向玩家广播
            sceneSync(activityId, Player.getRealServerId(belongPlayerId), PbProtocol.TeamTreasureInfoNotify.newBuilder().setSync(builder.build()).build());

        }

        public void sceneSync(int activityId, int serverId, PbProtocol.TeamTreasureInfoNotify notify){
            ActivityTemplate activityTemplate = ActivityService.getActivityTemplate(activityId);
            int routeSceneId = SceneMapType.teamTreasureHunt.getRouteSceneId(serverId, activityTemplate.crossType);
            ServerCommandRequest commandRequest = CommandRequests.newServerCommandRequest("SceneOperationCommandService.msgNotify", serverId);
            TLBase.getInstance().getRpcUtil().sendToNode(ServerType.SCENE, routeSceneId, commandRequest, SceneMapType.teamTreasureHunt, PtCode.TEAM_TREASURE_INFO_NOTIFY, notify, -1);
        }

        /**
         * 感谢弹幕
         */
        public void luckyLog(int activityId, int uid) {
            TeamTreasureHuntBox huntBox = boxMap.get(uid);
            PbTeamTreasure.TeamTreasureSync.Builder builder = PbTeamTreasure.TeamTreasureSync.newBuilder()
                    .setType(PbTeamTreasure.TeamTreasureSync.Type.LUCKY_LOG)
                    .setActiveId(activityId)
                    .setLuckyLog(PbTeamTreasure.LuckyLog.newBuilder()
                            .setThanksName(huntBox.getBelongPlayerName()));
            //通过场景服向玩家广播
            sceneSync(activityId, Player.getRealServerId(huntBox.getBelongPlayerId()), PbProtocol.TeamTreasureInfoNotify.newBuilder().setSync(builder.build()).build());
        }

        /**
         * 移除宝箱
         */
        public void removeBox(int activityId, int uid) {
            if(boxMap.containsKey(uid)){
                TeamTreasureHuntBox remove = boxMap.remove(uid);
                PbTeamTreasure.TeamTreasureSync.Builder builder = PbTeamTreasure.TeamTreasureSync.newBuilder()
                        .setType(PbTeamTreasure.TeamTreasureSync.Type.REMOVE_BOX)
                        .setActiveId(activityId)
                        .setRemoveBoxId(uid);
                //通过场景服向玩家广播
                sceneSync(activityId, Player.getRealServerId(remove.getBelongPlayerId()), PbProtocol.TeamTreasureInfoNotify.newBuilder().setSync(builder.build()).build());
            }
        }

        @Override
        public void tick() {
            List<Integer> removeBoxList = new ArrayList<>();
            for (TeamTreasureHuntBox treasureHuntBox : boxMap.values()) {
                if (treasureHuntBox.getEndTime() != 0 && ServerConstants.getCurrentTimeMillis() >= treasureHuntBox.getEndTime()) {
                    removeBoxList.add(treasureHuntBox.getUid());
                }
            }
            //移除
            for (Integer removeId : removeBoxList) {
                removeBox(activityId, removeId);
            }
        }

        public int genId() {
            return ++uidIncr;
        }


        public Map<Integer, TeamTreasureHuntBox> getBoxMap() {
            return boxMap;
        }

        public void setBoxMap(Map<Integer, TeamTreasureHuntBox> boxMap) {
            this.boxMap = boxMap;
        }

        public int getUidIncr() {
            return uidIncr;
        }

        public void setUidIncr(int uidIncr) {
            this.uidIncr = uidIncr;
    }

    public TeamTreasureHuntActivityGroupData() {
    }

    public TeamTreasureHuntActivityGroupData(int id, int groupId) {
        this.groupId = groupId;
        this.activityId = id;
        this.activityType = ActivityType.teamTreasureHunt.getId();
    }


    @Override
    public void fillModuleAsync(PbActivity.ActivityModule.Builder builder, long playerId) {
    }


}

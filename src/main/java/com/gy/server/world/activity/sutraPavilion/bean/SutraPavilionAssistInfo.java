package com.gy.server.world.activity.sutraPavilion.bean;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.gy.server.packet.PbActivityTeam;

import java.util.HashMap;
import java.util.Map;

/**
 * 夜探藏经阁-协助信息
 */

public class SutraPavilionAssistInfo {

    //发现者
    @Protobuf(order = 1)
    private long findPlayerId;

    //层数id
    @Protobuf(order = 2)
    private int floorId;

    //协助事件id
    @Protobuf(order = 3)
    private int eventId;

    //剩余血量
    @Protobuf(order = 4)
    private Map<Integer, Long> surplusHp = new HashMap<>();

    //是否通知过
    @Protobuf(order = 5)
    private boolean isNotify;

    public PbActivityTeam.SurtaPavilionAssistInfo genPb(){
        PbActivityTeam.SurtaPavilionAssistInfo.Builder builder = PbActivityTeam.SurtaPavilionAssistInfo.newBuilder();
        builder.setFindPlayerId(findPlayerId);
        builder.setFloorId(floorId);
        builder.setEventId(eventId);
        builder.putAllSurplusHps(surplusHp);
        builder.setIsNotify(isNotify);
        return builder.build();
    }

    public long getFindPlayerId() {
        return findPlayerId;
    }

    public void setFindPlayerId(long findPlayerId) {
        this.findPlayerId = findPlayerId;
    }

    public int getFloorId() {
        return floorId;
    }

    public void setFloorId(int floorId) {
        this.floorId = floorId;
    }

    public int getEventId() {
        return eventId;
    }

    public void setEventId(int eventId) {
        this.eventId = eventId;
    }

    public Map<Integer, Long> getSurplusHp() {
        return surplusHp;
    }

    public void setSurplusHp(Map<Integer, Long> surplusHp) {
        this.surplusHp = surplusHp;
    }

    public boolean isNotify() {
        return isNotify;
    }

    public void setNotify(boolean notify) {
        isNotify = notify;
    }
}

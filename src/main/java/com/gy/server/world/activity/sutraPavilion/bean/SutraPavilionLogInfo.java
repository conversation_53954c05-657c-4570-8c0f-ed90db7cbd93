package com.gy.server.world.activity.sutraPavilion.bean;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.gy.server.game.drop.Reward;
import com.gy.server.packet.PbActivityTeam;
import com.ttlike.server.tl.baselib.serialize.reward.RewardDb;

import java.util.ArrayList;
import java.util.List;

/**
 * 日志信息
 */
public class SutraPavilionLogInfo {

    @Protobuf(order = 1)
    private long playerId;

    @Protobuf(order = 2)
    private int floorId;

    @Protobuf(order = 3)
    private int operation;

    @Protobuf(order = 4)
    private List<RewardDb> rewardDbList = new ArrayList<>();

    public PbActivityTeam.SurtaPavilionLogInfo genPb(){
        PbActivityTeam.SurtaPavilionLogInfo.Builder builder = PbActivityTeam.SurtaPavilionLogInfo.newBuilder();
        builder.setPlayerId(playerId);
        builder.setFloorId(floorId);
        builder.setOperation(operation);
        builder.addAllRewards(Reward.writeCollectionToPb(Reward.readFromDb(rewardDbList)));
        return builder.build();
    }

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public int getFloorId() {
        return floorId;
    }

    public void setFloorId(int floorId) {
        this.floorId = floorId;
    }

    public int getOperation() {
        return operation;
    }

    public void setOperation(int operation) {
        this.operation = operation;
    }

    public List<RewardDb> getRewardDbList() {
        return rewardDbList;
    }

    public void setRewardDbList(List<RewardDb> rewardDbList) {
        this.rewardDbList = rewardDbList;
    }
}

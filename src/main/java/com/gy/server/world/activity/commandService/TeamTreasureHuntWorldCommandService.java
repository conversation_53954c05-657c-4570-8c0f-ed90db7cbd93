package com.gy.server.world.activity.commandService;

import com.gy.server.annotation.MessageMethod;
import com.gy.server.annotation.MessageServiceBean;
import com.gy.server.core.MessageServerType;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.callback.response.CallbackResponse;
import com.gy.server.core.command.CommandRequestParams;
import com.gy.server.core.command.PlayerCommandRequest;
import com.gy.server.game.activity.ActivityService;
import com.gy.server.game.activity.data.teamTreasureHunt.TeamTreasureActivityData;
import com.gy.server.game.activity.data.teamTreasureHunt.TeamTreasureHuntTemplate;
import com.gy.server.game.activity.teamTreasureHunt.TeamTreasureHuntHelper;
import com.gy.server.game.player.Player;
import com.gy.server.game.team.TeamHelper;
import com.gy.server.game.team.TeamManager;
import com.gy.server.game.text.Text;
import com.gy.server.utils.MathUtil;
import com.gy.server.world.activityTeam.bean.TeamTreasureHuntTeamData;
import com.gy.server.world.team.base.TeamInfo;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.rpc.TLMultiMessageCallbackTask;

import java.util.*;

@MessageServiceBean(description = "跨服活动 圣兽寻宝", messageServerType = MessageServerType.world)
public class TeamTreasureHuntWorldCommandService {


    @MessageMethod(description = "队长选择次数")
    private static void selectCount(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        int activityId = params.getParam(1);
        int count = params.getParam(2);
        long teamId = params.getParam(3);
        TeamTreasureHuntHelper.selectCount(playerId, teamId, activityId, count, request);
    }

    @MessageMethod(description = "队长选择宝藏")
    private static void selectTreasure(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        int activityId = params.getParam(1);
        int type = params.getParam(2);
        long teamId = params.getParam(3);
        TeamTreasureHuntHelper.selectTreasure(playerId, teamId, activityId, type, request);
    }

    @MessageMethod(description = "选择信物")
    private static void selectItem(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        int activityId = params.getParam(1);
        int index = params.getParam(2);
        boolean isSelect = params.getParam(3);
        long teamId = params.getParam(4);
        TeamTreasureHuntHelper.selectItem(playerId, teamId, activityId, index, isSelect, request);
    }

    @MessageMethod(description = "队伍开启宝藏")
    private static void teamOpen(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        int activityId = params.getParam(1);
        String playerName = params.getParam(2);
        long teamId = params.getParam(3);

        TeamTreasureHuntHelper.teamOpen(playerId, teamId, activityId, playerName, request);
    }

    @MessageMethod(description = "开启宝箱/福袋")
    private static void openBox(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        int activityId = params.getParam(1);
        int boxId = params.getParam(2);
        long teamId = params.getParam(3);
        TeamTreasureHuntHelper.openBox(playerId, teamId, activityId, boxId, request);
    }

    @MessageMethod(description = "圣兽寻宝查看地图福袋")
    private static void queryBox(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        int activityId = params.getParam(1);
        TeamTreasureHuntHelper.queryBox(playerId, activityId, request);
    }

    @MessageMethod(description = "个人开启宝藏")
    private static void personOpen(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        int activityId = params.getParam(1);
        int selectCount = params.getParam(2);
        int type = params.getParam(3);
        String playerName = params.getParam(4);

        TeamTreasureHuntHelper.personOpen(playerId, activityId, selectCount, type, playerName, request);

    }

    public static class TeamOpenMultiMessageCallbackTask extends TLMultiMessageCallbackTask {
        private final int activityId;
        private final PlayerCommandRequest request;
        private final long playerId;
        private final int selectTreasureType;
        private final String playerName;
        private final TeamInfo teamInfo;

        public TeamOpenMultiMessageCallbackTask(int activityId, TeamTreasureHuntTeamData teamTreasureHuntTeamData
                , PlayerCommandRequest request, long playerId, TeamTreasureActivityData teamTreasureActivityData
                , int selectTreasureType, String playerName, TeamInfo teamInfo) {
            this.activityId = activityId;
            this.request = request;
            this.playerId = playerId;
            this.selectTreasureType = selectTreasureType;
            this.playerName = playerName;
            this.teamInfo = teamInfo;
        }

        @Override
        public void complete(Map<String, CallbackResponse> subResponses) {
            //预扣除ID
            Map<Long, Integer> preCostIdMap = new HashMap<>();
            int finalResult = -1;
            for (CallbackResponse callbackResponse : subResponses.values()) {
                int result = callbackResponse.getParam(0);
                if (result == -1) {
                    preCostIdMap.putAll(callbackResponse.getParam(1));
                } else {
                    finalResult = Text.圣兽寻宝选择信物不足;
                }
            }
            //有的队友都没扣除成功
            if (finalResult != -1) {
                TeamTreasureHuntTeamData teamTreasureHuntTeamInfoNotNull = teamInfo.getTeamTreasureHuntTeamInfoNotNull();
                int selectCount = teamTreasureHuntTeamInfoNotNull.getSelectCount();
                //回滚预扣除
                for (Map.Entry<Long, Integer> entry : preCostIdMap.entrySet()) {
                    Long rollbackPlayerId = entry.getKey();
                    Integer rollbackPreCostId = entry.getValue();
                    TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, Player.getRealServerId(rollbackPlayerId),
                            CommandRequests.newPlayerCommandRequest("TeamTreasureHuntGameCommandService.rollbackPreCost", rollbackPlayerId), activityId, rollbackPreCostId, selectCount);
                }
                request.addCallbackParam(finalResult);
                TLBase.getInstance().getRpcUtil().toCallback(request);
                return;
            }
            request.addCallbackParam(Text.没有异常);
            TLBase.getInstance().getRpcUtil().toCallback(request);

            addBox(activityId, teamInfo, selectTreasureType, false, playerId, playerName);
            //通知玩家开启成功
            for (Map.Entry<Long, Integer> entry : preCostIdMap.entrySet()) {
                Long successPlayerId = entry.getKey();
                Integer successPreCostId = entry.getValue();
                TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, Player.getRealServerId(successPlayerId),
                        CommandRequests.newPlayerCommandRequest("TeamTreasureHuntGameCommandService.teamOpenSuccess", successPlayerId), activityId,
                        successPreCostId);
            }
        }

        public static void addBox(int activityId, TeamInfo teamInfo,
                                  int selectTreasureType, boolean isLocal, long playerId, String playerName){
            TeamTreasureActivityData teamTreasureActivityData = ActivityService.getActivityData(activityId);
            TeamTreasureHuntTeamData teamTreasureHuntTeamData = teamInfo.getTeamTreasureHuntTeamInfoNotNull();
            //队伍解散
            TeamHelper.commonDestroyTeamLogic(0, teamInfo.getTeamId(), ServerConstants.getCurrentTimeMillis());
            // 生成宝箱
            TeamTreasureHuntTemplate teamTreasureHuntTemplate = teamTreasureActivityData.treasureHuntTemplateMap.get(selectTreasureType);
            Set<Long> members = TeamManager.getInstance().getMembers(teamInfo.getTeamId());
            TeamTreasureHuntHelper.methodDo(isLocal, activityId, "addBox", activityId, 1,
                    teamTreasureHuntTemplate.type, teamTreasureHuntTemplate.drop,
                    teamTreasureHuntTeamData.getSelectCount(), playerId, playerName, true,
                    MathUtil.random(teamTreasureActivityData.constant.point), 0, new HashSet<>(members));
        }

        @Override
        public void timeout(Map<String, CallbackResponse> subResponses) {
            request.addCallbackParam(Text.跨服调用超时);
            TLBase.getInstance().getRpcUtil().toCallback(request);
        }

    }
}

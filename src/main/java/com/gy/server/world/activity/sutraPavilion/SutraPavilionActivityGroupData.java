package com.gy.server.world.activity.sutraPavilion;

import com.gy.server.game.activity.ActivityType;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.game.role.PlayerRoleService;
import com.gy.server.packet.PbActivity;
import com.gy.server.world.activity.ActivityGroupData;
import com.gy.server.world.activity.sutraPavilion.rank.SpRankInfo;
import com.gy.server.world.activityTeam.ActivityTeamHelper;
import com.gy.server.world.activityTeam.ActivityTeamManager;
import com.gy.server.world.activityTeam.bean.ActivityTeam;

import java.util.HashMap;
import java.util.Map;

public class SutraPavilionActivityGroupData extends ActivityGroupData {


    /**
     * groupId_rankInfo
     */
    private Map<Integer, SpRankInfo> crossRankInfos = new HashMap<>();

    public void addPoint(int groupId, long playerId, long teamId, int floorId, int point){
        if(!crossRankInfos.containsKey(groupId)){
            crossRankInfos.put(groupId, new SpRankInfo());
        }
        SpRankInfo spRankInfo = crossRankInfos.get(groupId);
        spRankInfo.addPlayerPoint(playerId, floorId, point);
        spRankInfo.addTeamPoint(teamId, floorId, point);
        spRankInfo.sortRank();
    }

    public void modifyFloor(int groupId, long playerId, long teamId, int floorId){
        if(!crossRankInfos.containsKey(groupId)){
            crossRankInfos.put(groupId, new SpRankInfo());
        }
        SpRankInfo spRankInfo = crossRankInfos.get(groupId);
        spRankInfo.modifyPlayerFloor(playerId, floorId);
        spRankInfo.modifyTeamFloor(teamId, floorId);
        spRankInfo.sortRank();
    }

    public Map<Integer, SpRankInfo> getCrossRankInfos() {
        return crossRankInfos;
    }

    public void setCrossRankInfos(Map<Integer, SpRankInfo> crossRankInfos) {
        this.crossRankInfos = crossRankInfos;
    }

    public SutraPavilionActivityGroupData(){}
    public SutraPavilionActivityGroupData(int id, int groupId) {
        this.groupId = groupId;
        this.activityId = id;
        this.activityType = ActivityType.theSutraRepository.getId();
    }

    @Override
    public void fillModuleAsync(PbActivity.ActivityModule.Builder builder, long playerId) {
        ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(getActivityId(), getGroupId(), playerId);
        PbActivity.SutraPavilionActivityModule.Builder md = PbActivity.SutraPavilionActivityModule.newBuilder();
        if (team!= null) {
            Map<Long, MiniGamePlayer> players = PlayerHelper.getMiniPlayersForMap(team.getMembers());
            for(Long memberId : team.getMembers()){
                MiniGamePlayer player = players.get(memberId);
                if(player != null) {
                    md.addTeamProtagonistIds(PlayerRoleService.getProtagonistTemplate(player.getProfession(), player.getGender()).id);
                }
            }
        }
        builder.setSpModule(md);
    }

    @Override
    public void finish() {
        //活动结束开始结算
        ActivityTeamHelper.spSettlement(activityId, groupId, true);

    }
}

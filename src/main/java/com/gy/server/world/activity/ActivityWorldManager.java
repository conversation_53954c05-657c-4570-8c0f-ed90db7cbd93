package com.gy.server.world.activity;

import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.Configuration;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.activity.ActivityService;
import com.gy.server.game.activity.ActivityTemplate;
import com.gy.server.game.activity.ActivityType;
import com.gy.server.game.warZone.WarZoneHelper;
import com.gy.server.utils.function.Ticker;
import com.gy.server.utils.runner.Runner;
import com.gy.server.world.crossData.CrossDataManager;
import com.gy.server.world.crossData.CrossDataType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.serialize.warZone.WarZoneInfo;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 跨服活动管理器
 */
public class ActivityWorldManager implements Ticker, Runner {

    private static final long CHECK_PERIOD_SECOND = 5;

    private static final ActivityWorldManager INSTANCE = new ActivityWorldManager();

    /**
     * 下次存储数据时间
     */
    private ThreadLocal<LocalDateTime> nextCheckTime = new ThreadLocal<>();

    public static ActivityWorldManager getInstance() {
        return INSTANCE;
    }

    @Override
    public void tick() {
        LocalDateTime now = ServerConstants.getCurrentTimeLocalDateTime();
        LocalDateTime localDateTime = nextCheckTime.get();
        if (localDateTime == null) {
            //初始化下次数据存储时间
            nextCheckTime.set(ServerConstants.getCurrentTimeLocalDateTime().plusSeconds(CHECK_PERIOD_SECOND));
        }

        //检查到期活动
        if (now.isAfter(nextCheckTime.get())) {
            nextCheckTime.set(now.plusSeconds(CHECK_PERIOD_SECOND));

            //检查并移除过期的活动
            checkActivityData();
        }

    }

    /**
     * 检查并移除过期的活动
     */
    private void checkActivityData() {
        List<ActivityCrossData> list = CrossDataManager.getInstance().getDataList(CrossDataType.Activity);
        for (ActivityCrossData activityCrossData : list) {
            Set<Integer> removes = new HashSet<>();
            for (ActivityGroupData activityGroupData : activityCrossData.getActivityData().values()) {
                ActivityTemplate template = ActivityService.getActivityTemplate(activityGroupData.getActivityId());
                if (activityGroupData.needRemove() || (template != null && !template.isOpenNow())) {
                    activityGroupData.finish();
                    removes.add(activityGroupData.getActivityId());
                }
            }
            removes.forEach(activityCrossData.getActivityData()::remove);
        }
    }

    @Override
    public void runnerExecute() throws Exception {
        //检查world活动数据构建
        List<ActivityCrossData> list = CrossDataManager.getInstance().getDataList(CrossDataType.Activity);
        for (ActivityType activityType : ActivityType.values()) {
            if(activityType.canCreateActivityGroup()){
                List<ActivityTemplate> activityTemplates = ActivityService.getActivityTemplatesByType(activityType);
                for (ActivityTemplate activityTemplate : activityTemplates) {
                    if (activityTemplate != null && activityTemplate.crossType != ActivityTemplate.CrossType.local && activityTemplate.isOpenNow()) {
                        //检查内存中该活动是否已经开启
                        boolean isActivityOpen = false;
                        for(ActivityCrossData activityCrossData : list){
                            if(activityCrossData.getActivityGroupData(activityTemplate.id) != null){
                                //已经开启，不需要再次开启
                                isActivityOpen = true;
                                break;
                            }
                        }
                        if(isActivityOpen){
                            continue;
                        }

                        //整理分组id
                        Set<Integer> groupIds = new HashSet<>();
                        if(activityTemplate.crossType == ActivityTemplate.CrossType.worldCross){
                            groupIds.add(CrossDataManager.WORLD_CROSS_GROUP_ID);//区服活动，特殊id
                        }else{
                            //分配战区/赛区--主world负责
                            if(CommonUtils.isMainWorldServer()) {
                                WarZoneHelper.allocateWarZone(activityType.getWarZoneTypeEnums());
                            }
                            if(activityTemplate.crossType == ActivityTemplate.CrossType.warZoneCross){
                                WarZoneInfo warZoneInfo = WarZoneHelper.getWarZoneInfo(activityType.getWarZoneTypeEnums());
                                if(warZoneInfo != null){
                                    for(WarZoneInfo.WarZoneSingleInfo info: warZoneInfo.getSingleInfoList()){
                                        groupIds.add(info.getWarZoneId());
                                    }
                                }

                            }else if(activityTemplate.crossType == ActivityTemplate.CrossType.raceZoneCross){
                                WarZoneInfo warZoneInfo = WarZoneHelper.getWarZoneInfo(activityType.getWarZoneTypeEnums());
                                if(warZoneInfo != null){
                                    for(WarZoneInfo.WarZoneSingleInfo info: warZoneInfo.getSingleInfoList()){
                                        groupIds.add(info.getRaceId());
                                    }
                                }
                            }
                        }

                        //构造ActivityGroupData
                        for(int groupId : groupIds) {
                            if((groupId == CrossDataManager.WORLD_CROSS_GROUP_ID && CommonUtils.isMainWorldServer())
                                || (Configuration.serverId == TLBase.getInstance().getRpcUtil().hashKeyToNodeId(groupId))){

                                ActivityCrossData crossData =  CrossDataManager.getInstance().getData(CrossDataType.Activity, groupId);
                                if(crossData == null){
                                    crossData = CrossDataManager.getInstance().createData(CrossDataType.Activity, groupId);
                                }

                                crossData.createActivityGroupData(activityTemplate.id, activityTemplate.type);
                            }
                        }
                    }
                }
            }
        }



    }

    @Override
    public long getRunnerInterval() {
        return 1000;
    }
}

package com.gy.server.world.activity.teamTreasure;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.gy.server.game.activity.teamTreasureHunt.TeamTreasureBoxPosition;
import com.gy.server.packet.PbTeamTreasure;

import java.util.HashSet;
import java.util.Set;

/**
 * 宝箱信息
 */
public class TeamTreasureHuntBox {
    /**
     * 宝箱唯一ID
     */
    @Protobuf(order = 1)
    private int uid;
    /**
     * 宝箱类型 1=宝箱 2=福袋
     */
    @Protobuf(order = 2)
    private int type;
    /**
     * 宝箱类型
     */
    @Protobuf(order = 3)
    private int treasureType;
    /**
     * 掉落次数
     */
    @Protobuf(order = 4)
    private int dropCount;
    /**
     * 宝箱所属玩家ID
     */
    @Protobuf(order = 5)
    private long belongPlayerId;
    /**
     * 福袋位置
     */
    @Protobuf(order = 6)
    private TeamTreasureBoxPosition position;
    /**
     * 是否是专属福袋
     */
    @Protobuf(order = 7)
    private boolean isPerson;
    /**
     * 福袋消失时间
     */
    @Protobuf(order = 8)
    private long endTime;
    /**
     * 组队玩家ID
     */
    @Protobuf(order = 9)
    private Set<Long> memberIdSet = new HashSet<>();
    /**
     * 掉落ID
     */
    @Protobuf(order = 10)
    private int dropId;
    /**
     * 宝箱所属玩家名
     */
    @Protobuf(order = 11)
    private String belongPlayerName;


    public PbTeamTreasure.TreasureBox genPb() {
        return PbTeamTreasure.TreasureBox.newBuilder()
                .setUid(uid)
                .setType(type)
                .setPosition(position.genPb())
                .setBelongPlayerId(belongPlayerId)
                .setBelongPlayerName(belongPlayerName)
                .setIsPerson(isPerson)
                .setEndTime(endTime)
                .build();
    }

    /**
     * 是否是福袋
     */
    public boolean isLuckyBox() {
        return type == 2;
    }


    public int getUid() {
        return uid;
    }

    public void setUid(int uid) {
        this.uid = uid;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getTreasureType() {
        return treasureType;
    }

    public void setTreasureType(int treasureType) {
        this.treasureType = treasureType;
    }

    public int getDropCount() {
        return dropCount;
    }

    public void setDropCount(int dropCount) {
        this.dropCount = dropCount;
    }

    public long getBelongPlayerId() {
        return belongPlayerId;
    }

    public void setBelongPlayerId(long belongPlayerId) {
        this.belongPlayerId = belongPlayerId;
    }

    public TeamTreasureBoxPosition getPosition() {
        return position;
    }

    public void setPosition(TeamTreasureBoxPosition position) {
        this.position = position;
    }

    public boolean isPerson() {
        return isPerson;
    }

    public void setPerson(boolean person) {
        isPerson = person;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public Set<Long> getMemberIdSet() {
        return memberIdSet;
    }

    public void setMemberIdSet(Set<Long> memberIdSet) {
        this.memberIdSet = memberIdSet;
    }

    public int getDropId() {
        return dropId;
    }

    public void setDropId(int dropId) {
        this.dropId = dropId;
    }

    public String getBelongPlayerName() {
        return belongPlayerName;
    }

    public void setBelongPlayerName(String belongPlayerName) {
        this.belongPlayerName = belongPlayerName;
    }
}

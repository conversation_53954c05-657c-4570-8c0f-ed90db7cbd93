package com.gy.server.world.mountainRiverTournament.async;

import com.gy.server.common.redis.bean.RedisStringBean;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.core.delay.DelayTaskManager;
import com.gy.server.db.nosql.redis.GyBatch;
import com.gy.server.db.nosql.redis.GyRedisTop;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.mountainRiverTournament.MountainRiverTournamentService;
import com.gy.server.game.mountainRiverTournament.async.AbsMRTAsync;
import com.gy.server.game.mountainRiverTournament.bean.MRTPlayEnums;
import com.gy.server.game.mountainRiverTournament.template.MRTConstant;
import com.gy.server.game.mountainRiverTournament.template.MRTPointTemplate;
import com.gy.server.game.player.Player;
import com.gy.server.game.unparalleledChallenge.UnparalleledChallengeService;
import com.gy.server.game.unparalleledChallenge.template.UnparalleledChallengeConstant;
import com.gy.server.game.warZone.WarZoneHelper;
import com.gy.server.game.warZone.WarZoneTypeEnums;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.gy.server.world.mountainRiverTournament.WorldMRTGlobalData;
import com.gy.server.world.mountainRiverTournament.bean.MRTPointRankInfo;
import com.gy.server.world.unparalleledChallenge.WorldUnparalleledChallengeHelper;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.serialize.RedisAssistant;
import com.ttlike.server.tl.baselib.serialize.mrt.MRTPlayerLevelInfo;
import com.ttlike.server.tl.baselib.serialize.warZone.WarZoneInfo;
import redis.clients.util.SafeEncoder;

import java.util.*;
import java.util.concurrent.ExecutionException;

/**
 * 切换赛季操作
 * @author: gbk
 * @date: 2024-11-27 16:11
 */
public class MRTChangeSeasonAsync extends AbsMRTAsync {

    WorldMRTGlobalData globalData;
    boolean isRealChangeSeason;

    //战区id，玩家列表
    Map<Integer, List<Long>> pointRankInfos = new HashMap<>();

    public MRTChangeSeasonAsync(WorldMRTGlobalData globalData, boolean isRealChangeSeason){
        this.globalData = globalData;
        this.isRealChangeSeason = isRealChangeSeason;
    }

    @Override
    public void asyncExecute() {
        //获取旧赛季信息
        WarZoneInfo oldWarZone = getWarZone();
        //重新分配赛季信息
        WarZoneHelper.allocateWarZone(WarZoneTypeEnums.MountainRiverTournament);
        WarZoneInfo newWarZone = getWarZone();
        MRTConstant constant = MountainRiverTournamentService.getConstant();

        RedisAssistant ra = TLBase.getInstance().getRedisAssistant();

        MRTPlayEnums oldPlayEnums = globalData.getPlayEnums();
        long startTime = System.currentTimeMillis();
        if(Objects.nonNull(oldPlayEnums)){

            //记录当前玩法段位积分信息（用于显示上赛季信息和结算）
            Map<Long, Double> lastLevelInfos = new HashMap<>();
            //下个段位信息（用于继承段位）
            Map<Integer, Map<Long, Double>> newPlayerRankInfos = new HashMap<>();
            //serverId : 段位信息 (用于结算)
            Map<Integer, List<MRTPlayerLevelInfo>> curLevelInfos = new HashMap<>();
            for (WarZoneInfo.WarZoneSingleInfo warZoneSingleInfo : oldWarZone.getSingleInfoList()) {
                int warZoneId = warZoneSingleInfo.getWarZoneId();
                String redisKey = GsRedisKey.MountainRiverTournament.level_rank.getRedisKey(warZoneId);
                //切换战区玩家（用于删除旧战区信息）
                Set<Long> needRemoveRankInfos = new HashSet<>();
                //分批读取段位信息
                long count = ra.rankCount(redisKey);
                int pageSize = 10000;
                int pageCount = (int) (count / pageSize) + 1;
                for (int i = 1; i <= pageCount; i++) {
                    int startRank = (i - 1) * pageSize + 1;
                    int endRank = i * pageSize;
                    List<GyRedisTop> gyRedisTops = ra.rankRange(redisKey, startRank, endRank, false);
                    for (GyRedisTop gyRedisTop : gyRedisTops) {
                        long playerId = Long.parseLong(gyRedisTop.getValue());
                        Double point = gyRedisTop.getScore();
                        int realServerId = Player.getRealServerId(playerId);
                        if(!curLevelInfos.containsKey(realServerId)){
                            curLevelInfos.put(realServerId,new ArrayList<>());
                        }
                        curLevelInfos.get(realServerId).add(MRTPlayerLevelInfo.newInfo(playerId, point));
                        lastLevelInfos.put(Long.parseLong(gyRedisTop.getValue()), point);
                        //重置积分
                        MRTPointTemplate pointTemp = MountainRiverTournamentService.getPointTemp((int) (double) point);
                        MRTPointTemplate nextLevel = MountainRiverTournamentService.getPointTemplateById(pointTemp.Nextrank);
                        //需要进入新战区
                        WarZoneInfo.WarZoneSingleInfo newWarZoneInfo = newWarZone.getInfoByServerId(Player.getRealServerId(playerId));
                        if(warZoneId != newWarZoneInfo.getWarZoneId()){
                            needRemoveRankInfos.add(playerId);
                        }
                        if(!newPlayerRankInfos.containsKey(newWarZoneInfo.getWarZoneId())){
                            newPlayerRankInfos.put(newWarZoneInfo.getWarZoneId(), new HashMap<>());
                        }
                        newPlayerRankInfos.get(newWarZoneInfo.getWarZoneId()).put(playerId, (double)nextLevel.rankCount);
                        try {
                            Thread.sleep(5L);
                        } catch (InterruptedException e) {
                            throw new RuntimeException(e);
                        }
                    }
                }
                //删除旧赛季信息
                GyBatch batch = ra.createBatch();
                batch.zrem(GsRedisKey.MountainRiverTournament.level_rank.getRedisKey(warZoneId), needRemoveRankInfos.toArray(new String[0]));
                try {
                    batch.execute();
                } catch (ExecutionException | InterruptedException e) {
                    throw new RuntimeException(e);
                }
                GameLogger.mrtInfo(warZoneId, "get old season rank info, cost time: " + (System.currentTimeMillis() - startTime) + " ms");
                startTime = System.currentTimeMillis();
            }

            //更新新赛季段位
            GyBatch batch = ra.createBatch();
            for (Integer warZoneId : newPlayerRankInfos.keySet()) {
                Map<Long, Double> playerRankInfos = newPlayerRankInfos.get(warZoneId);
                String level_rank = GsRedisKey.MountainRiverTournament.level_rank.getRedisKey(warZoneId);
                playerRankInfos.forEach((playerId, rankCount) -> batch.zadd(level_rank, playerId + "",  rankCount));
                try {
                    //以战区为单位，分批写入
                    batch.execute();
                    Thread.sleep(5L);
                } catch (ExecutionException | InterruptedException e) {
                    throw new RuntimeException(e);
                }
                GameLogger.mrtInfo(warZoneId, "batch update new season rank info, cost time: " + (System.currentTimeMillis() - startTime) + " ms");
                startTime = System.currentTimeMillis();
            }

            //存储上赛季段位信息
            globalData.getPlayerLastLevelInfos().putAll(lastLevelInfos);
            //发送结算奖励（2025.02.17废除结算奖励）
//            for (Integer serverId : curLevelInfos.keySet()) {
//                List<MRTPlayerLevelInfo> mrtPlayerLevelInfos = curLevelInfos.get(serverId);
//                ServerCommandRequest request = CommandRequests.newServerCommandRequest("GsMRTCommandService.settlementReward");
//                TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, serverId, request, oldPlayEnums.getType(), mrtPlayerLevelInfos, globalData.getSeasonId());
//            }
            globalData.setPlayEnums(oldPlayEnums == MRTPlayEnums.P6V6 ? MRTPlayEnums.P3V3 : MRTPlayEnums.P6V6);
        }else{
            globalData.setPlayEnums(constant.openType);
        }



        if(isRealChangeSeason){

            //需要根据这个积分榜，进入群龙聚首
            UnparalleledChallengeConstant ucConstant = UnparalleledChallengeService.getConstant();
            for (WarZoneInfo.WarZoneSingleInfo warZoneSingleInfo : oldWarZone.getSingleInfoList()) {
                int warZoneId = warZoneSingleInfo.getWarZoneId();
                String redisKey = GsRedisKey.MountainRiverTournament.point_rank.getRedisKey(warZoneId);
                List<GyRedisTop> gyRedisTops = ra.rankRange(redisKey, 1, ucConstant.eligibility, false);
                if(CollectionUtil.isNotEmpty(gyRedisTops)){
                    List<Long> playerIds = new ArrayList<>();
                    for (GyRedisTop gyRedisTop : gyRedisTops) {
                        playerIds.add(Long.parseLong(gyRedisTop.getValue()));
                    }
                    pointRankInfos.put(warZoneId, playerIds);
                }
            }


            //清空积分榜
            Map<Integer, MRTPointRankInfo> pointRankInfoMap = globalData.getPointRankInfoMap();
            pointRankInfoMap.clear();
            for (WarZoneInfo.WarZoneSingleInfo warZoneSingleInfo : oldWarZone.getSingleInfoList()) {
                int warZoneId = warZoneSingleInfo.getWarZoneId();
                ra.del(GsRedisKey.MountainRiverTournament.point_rank.getRedisKey(warZoneId));
                ra.del(GsRedisKey.MountainRiverTournament.point_rank_temp.getRedisKey(warZoneId));
            }
            //数量不多，所以点赞直接清除
            String support_count_key = GsRedisKey.MountainRiverTournament.support_count_key.getRedisKey();
            Set<String> playerIds = ra.setGetAll(support_count_key);
            String[] delRedisKeys = new String[playerIds.size()];
            int index = 0;
            for (String playerId : playerIds) {
                delRedisKeys[index] = GsRedisKey.MountainRiverTournament.support_count.getRedisKey(playerId);
                index++;
            }
            ra.delAsync(delRedisKeys);
            ra.del(support_count_key);
        }else{
            //积分榜刷新到上届信息
            Map<Integer, MRTPointRankInfo> pointRankInfoMap = globalData.getPointRankInfoMap();
            for (Integer warZoneId : pointRankInfoMap.keySet()) {
                MRTPointRankInfo mrtPointRankInfo = pointRankInfoMap.get(warZoneId);
                mrtPointRankInfo.syncPoint2Temp();

                GameLogger.mrtInfo(warZoneId, "pont rank info refresh to last season, cost time: " + (System.currentTimeMillis() - startTime) + " ms");
                startTime = System.currentTimeMillis();
            }

        }

        globalData.syncToRedis();

    }

    @Override
    public void execute() {
        //开启群龙聚首
        if(CollectionUtil.isNotEmpty(pointRankInfos)){
            WorldUnparalleledChallengeHelper.openNewSeason(pointRankInfos);
        }

    }
}
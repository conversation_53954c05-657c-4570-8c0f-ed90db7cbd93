package com.gy.server.world.mountainRiverTournament.bean;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.db.nosql.redis.GyBatch;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.serialize.RedisAssistant;
import com.ttlike.server.tl.baselib.serialize.mrt.WorldMRTGlobalDataDb;
import redis.clients.util.SafeEncoder;

import java.io.Serializable;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutionException;

/**
 * 剑会群雄-逸侠对决 积分榜数据
 * @author: gbk
 * @date: 2024-11-14 11:12
 */
@ProtobufClass
public class MRTPointRankInfo implements Serializable {

    //赛区id
    @Protobuf(order = 1)
    private int warZoneId;
    //积分信息
    @Protobuf(order = 2)
    private Map<Long, Long> pointInfos = new HashMap<>();

    public MRTPointRankInfo(){}

    public MRTPointRankInfo(int warZoneId){
        this.warZoneId = warZoneId;
    }

    /**
     * 增加积分
     * @param playerId 玩家id
     * @param addedCount 积分数
     */
    public void addPoint(long playerId, long addedCount){
        pointInfos.put(playerId, pointInfos.getOrDefault(playerId, 0L) + addedCount);
    }

    public void syncPointRank2Redis(Collection<Long> needSyncPlayerIds){
//        Map<String, byte[]> playerPointInfos = new HashMap<>();
        RedisAssistant ra = TLBase.getInstance().getRedisAssistant();
        GyBatch batch = ra.createBatch();
        String redisKey = GsRedisKey.MountainRiverTournament.point_rank.getRedisKey(warZoneId);
        needSyncPlayerIds.forEach(id -> batch.zadd(redisKey, id + "", (double)pointInfos.get(id)));
        try {
            batch.execute();
        } catch (ExecutionException | InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    public void syncPoint2Temp(){
        RedisAssistant ra = TLBase.getInstance().getRedisAssistant();
        String redisKey = GsRedisKey.MountainRiverTournament.point_rank_temp.getRedisKey(warZoneId);
        Map<String, byte[]> mapInfos = new HashMap<>();
        pointInfos.forEach((id, point) -> mapInfos.put(id + "", SafeEncoder.encode(point + "")));
        ra.hashPutString(redisKey, mapInfos);
    }

    public void readFromDb(WorldMRTGlobalDataDb.MRTPointRankInfoDb rankInfoDb){
        this.warZoneId = rankInfoDb.getWarZoneId();
        this.pointInfos = rankInfoDb.getPointInfos();
    }

    public WorldMRTGlobalDataDb.MRTPointRankInfoDb writeToDb(){
        WorldMRTGlobalDataDb.MRTPointRankInfoDb rankInfoDb = new WorldMRTGlobalDataDb.MRTPointRankInfoDb();
        rankInfoDb.setWarZoneId(warZoneId);
        rankInfoDb.setPointInfos(pointInfos);
        return rankInfoDb;
    }

    public int getWarZoneId() {
        return warZoneId;
    }

    public void setWarZoneId(int warZoneId) {
        this.warZoneId = warZoneId;
    }

    public Map<Long, Long> getPointInfos() {
        return pointInfos;
    }

    public void setPointInfos(Map<Long, Long> pointInfos) {
        this.pointInfos = pointInfos;
    }
}
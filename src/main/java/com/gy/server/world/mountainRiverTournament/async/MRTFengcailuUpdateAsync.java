package com.gy.server.world.mountainRiverTournament.async;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.db.nosql.redis.GyRedisTop;
import com.gy.server.game.mountainRiverTournament.async.AbsMRTAsync;
import com.gy.server.game.mountainRiverTournament.bean.MRTSpecialReportInfo;
import com.gy.server.game.warZone.WarZoneHelper;
import com.gy.server.game.warZone.WarZoneTypeEnums;
import com.gy.server.utils.CollectionUtil;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.serialize.RedisAssistant;
import com.ttlike.server.tl.baselib.serialize.warZone.WarZoneInfo;

import java.util.List;
import java.util.Map;

/**
 * 剑会群雄（逸侠对决）-竞技风采录更新
 * @author: gbk
 * @date: 2024-11-25 18:03
 */
public class MRTFengcailuUpdateAsync extends AbsMRTAsync {



    @Override
    public void asyncExecute() {
        WarZoneInfo warZoneInfo = WarZoneHelper.getWarZoneInfo(WarZoneTypeEnums.MountainRiverTournament);
        RedisAssistant ra = TLBase.getInstance().getRedisAssistant();
        for (WarZoneInfo.WarZoneSingleInfo singleInfo : warZoneInfo.getSingleInfoList()) {
            int warZoneId = singleInfo.getWarZoneId();
            //清除竞技风采录
            String fengtailuRedisKey = GsRedisKey.MountainRiverTournament.fengtailu.getRedisKey(warZoneId);
            ra.del(fengtailuRedisKey);
            //重新刷新竞技风采录
            String rankRedisKey = GsRedisKey.MountainRiverTournament.level_rank.getRedisKey(warZoneId);
            List<GyRedisTop> gyRedisTops = ra.rankRange(rankRedisKey, 1, 3, false);
            if(CollectionUtil.isNotEmpty(gyRedisTops)){
                for (GyRedisTop gyRedisTop : gyRedisTops) {
                    ra.listRPush(fengtailuRedisKey, gyRedisTop.getValue());
                }
            }
            //刷新特殊战报
            String srRedisKey = GsRedisKey.MountainRiverTournament.special_report.getRedisKey(warZoneId);
            Map<String, MRTSpecialReportInfo> reportInfoMap = ra.hashGetAllBeans(srRedisKey, MRTSpecialReportInfo.class);

            String srShowRedisKey = GsRedisKey.MountainRiverTournament.special_report_show.getRedisKey(warZoneId);
            reportInfoMap.forEach((k, v) -> ra.hashPut(srShowRedisKey, k, v));

            ra.del(srRedisKey);
        }
        //清除匹配权重
        ra.del(GsRedisKey.MountainRiverTournament.match_weight.getRedisKey());
    }

    @Override
    public void execute() {

    }
}
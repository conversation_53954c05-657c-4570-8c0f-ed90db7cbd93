package com.gy.server.world.mountainRiverTournament.async;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.db.nosql.redis.GyRedisTop;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.mountainRiverTournament.async.AbsMRTAsync;
import com.gy.server.game.mountainRiverTournament.bean.MRTPlayEnums;
import com.gy.server.game.player.Player;
import com.gy.server.world.mountainRiverTournament.WorldMRTGlobalData;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.serialize.RedisAssistant;
import com.ttlike.server.tl.baselib.serialize.mrt.MRTPlayerLevelInfo;
import com.ttlike.server.tl.baselib.serialize.warZone.WarZoneInfo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 每日结算奖励
 * @author: gbk
 * @date: 2024-11-28 10:31
 */
public class MRTDailyRewardAsync extends AbsMRTAsync {
    MRTPlayEnums playEnums;

    public MRTDailyRewardAsync(MRTPlayEnums playEnums){
        this.playEnums = playEnums;
    }

    @Override
    public void asyncExecute() {
        WarZoneInfo warZoneInfo = getWarZone();
        RedisAssistant ra = TLBase.getInstance().getRedisAssistant();
        //获取所有人的当前段位信息
        //serverId : 段位信息
        Map<Integer, List<MRTPlayerLevelInfo>> curLevelInfos = new HashMap<>();
        long startTime = System.currentTimeMillis();
        //获取所有战区信息
        for (WarZoneInfo.WarZoneSingleInfo singleInfo : warZoneInfo.getSingleInfoList()) {
            int warZoneId = singleInfo.getWarZoneId();
            //500W条数据时，按照10W条数据的方式取一次，耗时20s左右
            String redisKey = GsRedisKey.MountainRiverTournament.level_rank.getRedisKey(warZoneId + "");
            long count = ra.rankCount(redisKey);
            int pageSize = 10000;
            int pageCount = (int) (count / pageSize) + 1;
            for (int i = 1; i <= pageCount; i++) {
                int startRank = (i - 1) * pageSize + 1;
                int endRank = i * pageSize;
                List<GyRedisTop> gyRedisTops = ra.rankRange(redisKey, startRank, endRank, false);
                for (GyRedisTop gyRedisTop : gyRedisTops) {
                    long playerId = Long.parseLong(gyRedisTop.getValue());
                    Double point = gyRedisTop.getScore();
                    if(point > 0){
                        int realServerId = Player.getRealServerId(playerId);
                        if(!curLevelInfos.containsKey(realServerId)){
                            curLevelInfos.put(realServerId,new ArrayList<>());
                        }
                        curLevelInfos.get(realServerId).add(MRTPlayerLevelInfo.newInfo(playerId,point));
                    }
                }
                try {
                    Thread.sleep(5L);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }

            GameLogger.mrtInfo(warZoneId, "daily reward, cost time: " + (System.currentTimeMillis() - startTime) + " ms");
            startTime = System.currentTimeMillis();
        }

        WorldMRTGlobalData globalData = GlobalDataManager.getData(GlobalDataType.mountainRiverTournament);
        //获取所有人上次段位信息
        for (List<MRTPlayerLevelInfo> playerLevelInfos : curLevelInfos.values()) {
            for (MRTPlayerLevelInfo playerLevelInfo : playerLevelInfos) {
                playerLevelInfo.setLastPoint(globalData.getPlayerLastLevelInfos().getOrDefault(playerLevelInfo.getPlayerId(), 0D));
            }
        }
        
        //已经获取了所有服务器的段位信息
        for (Integer serverId : curLevelInfos.keySet()) {
            List<MRTPlayerLevelInfo> mrtPlayerLevelInfos = curLevelInfos.get(serverId);
            ServerCommandRequest request = CommandRequests.newServerCommandRequest("GsMRTCommandService.dailyLevelReward");
            TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, serverId, request, playEnums.getType(), mrtPlayerLevelInfos);
        }

    }

    @Override
    public void execute() {

    }
}
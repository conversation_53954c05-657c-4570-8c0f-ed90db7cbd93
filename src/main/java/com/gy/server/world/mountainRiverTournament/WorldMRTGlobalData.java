package com.gy.server.world.mountainRiverTournament;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.common.util.CommonUtils;
import com.gy.server.game.event.ServerEvent;
import com.gy.server.game.event.ServerEventHandler;
import com.gy.server.game.event.ServerEventType;
import com.gy.server.game.global.GlobalData;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.mountainRiverTournament.MountainRiverTournamentService;
import com.gy.server.game.mountainRiverTournament.bean.MRTInfo;
import com.gy.server.game.mountainRiverTournament.bean.MRTPlayEnums;
import com.gy.server.game.mountainRiverTournament.template.MRTConstant;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.gy.server.utils.time.DateTimeUtil;
import com.gy.server.world.mountainRiverTournament.async.MRTChangeSeasonAsync;
import com.gy.server.world.mountainRiverTournament.async.MRTFengcailuUpdateAsync;
import com.gy.server.world.mountainRiverTournament.bean.MRTPointRankInfo;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.serialize.RedisAssistant;
import com.ttlike.server.tl.baselib.serialize.mrt.WorldMRTGlobalDataDb;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 剑会群雄-逸侠对决world服数据
 * @author: gbk
 * @date: 2024-11-11 17:30
 */
public class WorldMRTGlobalData extends GlobalData implements ServerEventHandler {

    //赛季信息
    private int seasonId = -1;
    //玩法类型
    private volatile MRTPlayEnums playEnums;
    //下次刷新时间
    private long nextRefreshTime;
    //积分排名信息
    private Map<Integer, MRTPointRankInfo> pointRankInfoMap = new HashMap<>();
    //上次增加积分时间
    private long lastAddPointTime;
    //可以结算时间（每日结算）
    private volatile long canSettlementTime;
    //玩家上次积分
    private Map<Long, Double> playerLastLevelInfos = new ConcurrentHashMap<>();

    public Map<Long, Double> getPlayerLastLevelInfos() {
        return playerLastLevelInfos;
    }

    public void setPlayerLastLevelInfos(Map<Long, Double> playerLastLevelInfos) {
        this.playerLastLevelInfos = playerLastLevelInfos;
    }

    public long getCanSettlementTime() {
        return canSettlementTime;
    }

    public void setCanSettlementTime(long canSettlementTime) {
        this.canSettlementTime = canSettlementTime;
    }

    public long getLastAddPointTime() {
        return lastAddPointTime;
    }

    public void setLastAddPointTime(long lastAddPointTime) {
        this.lastAddPointTime = lastAddPointTime;
    }

    public int getSeasonId() {
        return seasonId;
    }

    public void setSeasonId(int seasonId) {
        this.seasonId = seasonId;
    }

    public MRTPlayEnums getPlayEnums() {
        return playEnums;
    }

    public void setPlayEnums(MRTPlayEnums playEnums) {
        this.playEnums = playEnums;
    }

    public long getNextRefreshTime() {
        return nextRefreshTime;
    }

    public void setNextRefreshTime(long nextRefreshTime) {
        this.nextRefreshTime = nextRefreshTime;
    }

    /**
     * 切换赛季（开启赛季）
     */
    public void changeSeason(){
        changeSeason(-1);
    }


    public void changeSeason(long openTime){
        //切换赛季
        MRTConstant constant = MountainRiverTournamentService.getConstant();
        boolean isRealChangeSeason = playEnums == constant.endType;
        if(isRealChangeSeason){
            this.seasonId = this.seasonId + 1;
        }else{
            this.seasonId = Math.max(this.seasonId, 1);
        }

        nextRefreshTime = openTime > 0 ? openTime : DateTimeUtil.toMillis(DateTimeUtil.toLocalDateTime(nextRefreshTime).plusDays(constant.duration));

        GameLogger.mrtInfo(-1, String.format("change season, playType : %s, nextRefreshTime : %s", Objects.isNull(playEnums) ? "null" : playEnums.name(), DateTimeUtil.toLocalDateTime(nextRefreshTime)));

        ThreadPool.execute(new MRTChangeSeasonAsync(this, isRealChangeSeason));
    }

    /**
     * 同步到redis并且通知GS
     */
    public void syncToRedis(){
        final MRTInfo mrtInfo = buildMRTInfo();
        ThreadPool.execute(()->{
            RedisAssistant redisAssistant = TLBase.getInstance().getRedisAssistant();
            redisAssistant.setBean(getRedisKey(), mrtInfo);
        });
    }

    public void loadByRedis(MRTInfo info){
        this.seasonId = info.getSeasonId();
        this.playEnums = info.getPlayTypeEnum();
        this.nextRefreshTime = info.getNextRefreshTime();
    }

    public static String getRedisKey(){
        return GsRedisKey.MountainRiverTournament.season.getRedisKey();
    }

    public MRTInfo buildMRTInfo(){
        MRTInfo info = new MRTInfo();
        info.setSeasonId(seasonId);
        info.setPlayType(playEnums.getType());
        info.setNextRefreshTime(nextRefreshTime);
        return info;
    }

    @Override
    public void init() {
//        if(CommonUtils.isMainWorldServer()){
//            //检查redis数据
//            RedisAssistant redisAssistant = TLBase.getInstance().getRedisAssistant();
//            MRTInfo info = redisAssistant.getBean(MRTInfo.class, getRedisKey());
//            if(Objects.nonNull(info) && info.getSeasonId() != seasonId){
//                loadByRedis(info);
//            }
//        }
    }
    public Map<Integer, MRTPointRankInfo> getPointRankInfoMap() {
        return pointRankInfoMap;
    }

    public void setPointRankInfoMap(Map<Integer, MRTPointRankInfo> pointRankInfoMap) {
        this.pointRankInfoMap = pointRankInfoMap;
    }

    @Override
    public void readFromPb(byte[] bytes) throws Exception {
        WorldMRTGlobalDataDb globalDataDb = PbUtilCompress.decode(WorldMRTGlobalDataDb.class, bytes);
        if(Objects.nonNull(globalDataDb)){
            this.seasonId = globalDataDb.getSeasonId();
            if(globalDataDb.getPlayEnums() > 0){
                this.playEnums = MRTPlayEnums.getPlayByType(globalDataDb.getPlayEnums());
            }
            this.nextRefreshTime = globalDataDb.getNextRefreshTime();
            for (WorldMRTGlobalDataDb.MRTPointRankInfoDb pointRankInfoDb : globalDataDb.getPointRankInfoMap()) {
                MRTPointRankInfo rankInfo = new MRTPointRankInfo();
                rankInfo.readFromDb(pointRankInfoDb);
                this.pointRankInfoMap.put(rankInfo.getWarZoneId(), rankInfo);
            }
            this.lastAddPointTime = globalDataDb.getLastAddPointTime();
            this.canSettlementTime = globalDataDb.getCanSettlementTime();
            this.playerLastLevelInfos = globalDataDb.getPlayerLastLevelInfos();
        }
    }

    @Override
    public byte[] writeToPb() {
        WorldMRTGlobalDataDb globalDataDb = new WorldMRTGlobalDataDb();
        globalDataDb.setSeasonId(seasonId);
        if(Objects.nonNull(playEnums)){
            globalDataDb.setPlayEnums(playEnums.getType());
        }
        globalDataDb.setNextRefreshTime(nextRefreshTime);
        for (MRTPointRankInfo info : pointRankInfoMap.values()) {
            globalDataDb.getPointRankInfoMap().add(info.writeToDb());
        }
        globalDataDb.setLastAddPointTime(lastAddPointTime);
        globalDataDb.setCanSettlementTime(canSettlementTime);
        globalDataDb.setPlayerLastLevelInfos(playerLastLevelInfos);
        return PbUtilCompress.encode(globalDataDb);
    }


    @Override
    public ServerEventType[] getEventTypes() {
        return new ServerEventType[]{ServerEventType.day5clock};
    }

    @Override
    public void handle(ServerEvent event) {
        switch (event.getEventType()){
            case day5clock:{
                //检查是否开启
                if(getSeasonId() > 0){
                    ThreadPool.execute(new MRTFengcailuUpdateAsync());
                }
                break;
            }
        }
    }
}
package com.gy.server.world.mountainRiverTournament.async;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.db.nosql.redis.GyRedisTop;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.mountainRiverTournament.MountainRiverTournamentService;
import com.gy.server.game.mountainRiverTournament.template.MRTConstant;
import com.gy.server.game.mountainRiverTournament.template.MRTPointTemplate;
import com.gy.server.game.warZone.WarZoneHelper;
import com.gy.server.game.warZone.WarZoneTypeEnums;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.world.mountainRiverTournament.WorldMRTGlobalData;
import com.gy.server.world.mountainRiverTournament.bean.MRTPointRankInfo;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.serialize.RedisAssistant;
import com.ttlike.server.tl.baselib.serialize.warZone.WarZoneInfo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 剑会群雄-逸侠对决 异步增加竞技点
 * @author: gbk
 * @date: 2024-11-14 13:38
 */
public class MRTAddPointAsync extends AsyncCall {

    //待增加
    Map<Integer, Map<Long, Integer>> waitAddPlayerIds = new HashMap<>();

    public MRTAddPointAsync(){
        multi = 1;
    }

    int multi;
    public MRTAddPointAsync(int multi){
        this.multi = multi;
    }

    @Override
    public void asyncExecute() {
        MRTConstant constant = MountainRiverTournamentService.getConstant();
        MRTPointTemplate pointTemplateById = MountainRiverTournamentService.getPointTemplateById(constant.startToAcquire);
        int minPoint = pointTemplateById.rankCount;
        WarZoneInfo warZoneInfo = WarZoneHelper.getWarZoneInfo(WarZoneTypeEnums.MountainRiverTournament);

        for (WarZoneInfo.WarZoneSingleInfo warZoneSingleInfo : warZoneInfo.getSingleInfoList()) {
            int warZoneId = warZoneSingleInfo.getWarZoneId();
            String redisKey = GsRedisKey.MountainRiverTournament.level_rank.getRedisKey(warZoneId);
            //获取段位榜数据
            RedisAssistant ra = TLBase.getInstance().getRedisAssistant();
            List<GyRedisTop> gyRedisTops = ra.rankRangeByScore(redisKey, minPoint, Long.MAX_VALUE);
            if(CollectionUtil.isNotEmpty(gyRedisTops)){
                int rank = 1;
                for (int i = gyRedisTops.size() - 1; i >= 0; i--) {
                    GyRedisTop gyRedisTop = gyRedisTops.get(i);
                    if(!waitAddPlayerIds.containsKey(warZoneId)){
                        waitAddPlayerIds.put(warZoneId, new HashMap<>());
                    }
                    Map<Long, Integer> waitAddPlayerId = waitAddPlayerIds.get(warZoneId);
                    waitAddPlayerId.put(Long.parseLong(gyRedisTop.getValue()), rank);
                    rank++;
                }
            }
        }
    }

    @Override
    public void execute() {
        if(CollectionUtil.isNotEmpty(waitAddPlayerIds)){
            WorldMRTGlobalData globalData = GlobalDataManager.getData(GlobalDataType.mountainRiverTournament);
            List<Long> needSyncPlayerIds = new ArrayList<>();
            for (Integer warZoneId : waitAddPlayerIds.keySet()) {
                Map<Integer, MRTPointRankInfo> pointRankInfoMap = globalData.getPointRankInfoMap();
                MRTPointRankInfo rankInfo = pointRankInfoMap.getOrDefault(warZoneId, new MRTPointRankInfo(warZoneId));
                Map<Long, Integer> waitAddPlayerId = waitAddPlayerIds.get(warZoneId);
                if(CollectionUtil.isNotEmpty(waitAddPlayerId)){
                    needSyncPlayerIds.clear();
                    for (Long playerId : waitAddPlayerId.keySet()) {
                        Integer rank = waitAddPlayerId.get(playerId);
                        int pointByRank = MountainRiverTournamentService.getPointByRank(rank);
                        if(pointByRank > 0){
                            rankInfo.addPoint(playerId, pointByRank * multi);
                            needSyncPlayerIds.add(playerId);
                        }
                    }
                    rankInfo.syncPointRank2Redis(needSyncPlayerIds);
                }
                pointRankInfoMap.put(warZoneId, rankInfo);
            }
        }
    }
}
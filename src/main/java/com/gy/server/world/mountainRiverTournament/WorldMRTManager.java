package com.gy.server.world.mountainRiverTournament;

import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.mountainRiverTournament.MountainRiverTournamentService;
import com.gy.server.game.mountainRiverTournament.bean.MRTPlayEnums;
import com.gy.server.game.mountainRiverTournament.template.MRTConstant;
import com.gy.server.utils.function.Ticker;
import com.gy.server.utils.runner.LongRunner;
import com.gy.server.utils.time.DateTimeUtil;
import com.gy.server.world.mountainRiverTournament.async.MRTAddPointAsync;
import com.gy.server.world.mountainRiverTournament.async.MRTDailyRewardAsync;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.time.LocalDateTime;

/**
 * 剑会群雄-逸侠对决 赛季切换
 * @author: gbk
 * @date: 2024-11-11 19:53
 */
public class WorldMRTManager implements Ticker,LongRunner {

    public static final long addPointRate = 1L * DateTimeUtil.MillisOfMinute;

    @Override
    public void tick() {
        if(CommonUtils.isMainWorldServer()){
            //检查切换赛季
            WorldMRTGlobalData globalData = GlobalDataManager.getData(GlobalDataType.mountainRiverTournament);
            MRTConstant constant = MountainRiverTournamentService.getConstant();
            if(globalData.getSeasonId() <= 0){
                //赛季开启
                //因为需要记录赛季，所以开启时间必须是个未来的事件
                LocalDateTime firstOpenTime = constant.getFirstOpenTime();
                if(ServerConstants.getCurrentTimeMillis() >= DateTimeUtil.toMillis(firstOpenTime)){
                    globalData.changeSeason(DateTimeUtil.toMillis(firstOpenTime.plusDays(constant.duration)));
                    globalData.setCanSettlementTime(DateTimeUtil.toMillis(firstOpenTime.withHour(constant.DailyBonusTime)));
                }
                GameLogger.mrtInfo(-1, String.format("mrt first open, seasonId : %s, openTime : %s" ,globalData.getSeasonId(), firstOpenTime));
            }else{
                long now = ServerConstants.getCurrentTimeMillis();
                //赛季切换
                if(now >= globalData.getNextRefreshTime()){
                    //切换赛季
                    globalData.changeSeason();
                }
                //定时发币
                if(now - globalData.getLastAddPointTime() >= addPointRate){
                    globalData.setLastAddPointTime(now);
                    ThreadPool.execute(new MRTAddPointAsync());
                }
                //赛季结算
//                globalData.setCanSettlementTime(DateTimeUtil.toMillis(DateTimeUtil.toLocalDateTime(ServerConstants.getCurrentTimeMillis()).minusDays(1).withHour(22).withMinute(0).withSecond(0).withNano(0)));
                if(now >= globalData.getCanSettlementTime()){
                    globalData.setCanSettlementTime(globalData.getCanSettlementTime() + 1L * DateTimeUtil.MillisOfDay);
                    //开始结算
                    MRTPlayEnums playEnums = globalData.getPlayEnums();
                    ThreadPool.execute(new MRTDailyRewardAsync(playEnums));
                }
            }
        }
    }

    public static void startUp(){
        if(CommonUtils.isMainWorldServer()){
            WorldMRTGlobalData globalData = GlobalDataManager.getData(GlobalDataType.mountainRiverTournament);
            if(globalData.getSeasonId() > 0){
                int multi = (int)((ServerConstants.getCurrentTimeMillis() - globalData.getLastAddPointTime()) / addPointRate);
                long surplusTime = (ServerConstants.getCurrentTimeMillis() - globalData.getLastAddPointTime()) % addPointRate;
                if(multi > 0){
                    globalData.setLastAddPointTime(globalData.getLastAddPointTime() + (long)multi * addPointRate + surplusTime);
                    ThreadPool.execute(new MRTAddPointAsync(multi));
                }
            }
        }
    }

    private static final WorldMRTManager manager = new WorldMRTManager();
    public static WorldMRTManager getInstance(){
        return manager;
    }

    @Override
    public long getMaxExecuteTime() {
        return 5 * DateTimeUtil.MillisOfHour;
    }

    @Override
    public void runnerExecute() throws Exception {

    }

    @Override
    public long getRunnerInterval() {
        return 3 * DateTimeUtil.MillisOfSecond;
    }
}
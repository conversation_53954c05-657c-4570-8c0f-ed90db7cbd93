package com.gy.server.world.mountainRiverTournament;

import com.gy.server.annotation.MessageMethod;
import com.gy.server.annotation.MessageServiceBean;
import com.gy.server.core.MessageServerType;
import com.gy.server.core.MethodInvokeType;
import com.gy.server.core.command.CommandRequestParams;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.core.thread.SimpleHashedThreadPool;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.mountainRiverTournament.MRTHelper;
import com.gy.server.game.mountainRiverTournament.MountainRiverTournamentService;
import com.gy.server.game.mountainRiverTournament.template.MRTConstant;
import com.gy.server.utils.time.DateTimeUtil;

import java.time.LocalDateTime;

/**
 * 剑会群雄-逸侠对决 世界服消息
 * @author: gbk
 * @date: 2024-11-13 14:37
 */
@MessageServiceBean(description = "剑会群雄-逸侠对决消息", messageServerType = MessageServerType.world)
public class WorldMRTCommandService {

    @MessageMethod(description = "减少玩家匹配权重", invokeType = MethodInvokeType.sync)
    private static void reducePlayerMatchWeight(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        SimpleHashedThreadPool.getInstance().addTask(()->{
            MRTHelper.reducePlayerMatchWeight(playerId);
        }, playerId);
    }

    @MessageMethod(description = "修改玩家积分", invokeType = MethodInvokeType.sync)
    private static void modifyPlayerPoint(ServerCommandRequest request, CommandRequestParams params) {
        int warZoneId = params.getParam(0);
        long playerId = params.getParam(1);
        int point = params.getParam(2);
        SimpleHashedThreadPool.getInstance().addTask(()->{
            MRTHelper.modifyPlayerPoint(warZoneId, playerId, point);
        }, playerId);
    }

    @MessageMethod(description = "尝试进入事件", invokeType = MethodInvokeType.sync)
    private static void tryEnterSpecialReport(ServerCommandRequest request, CommandRequestParams params) {
        int warZoneId = params.getParam(0);
        long playerId = params.getParam(1);
        long recordId= params.getParam(2);
        int srId = params.getParam(3);
        int serverId = params.getParam(4);
        int recordType = params.getParam(5);
        Object[] param = params.getParam(6);
        MRTHelper.tryEnterSpecialReport(warZoneId, playerId, recordId, srId, serverId, recordType, param);
    }


    @MessageMethod(description = "gm-重置", invokeType = MethodInvokeType.sync)
    private static void testReset(ServerCommandRequest request, CommandRequestParams params) {
        WorldMRTGlobalData globalData = GlobalDataManager.getData(GlobalDataType.mountainRiverTournament);
        MRTConstant constant = MountainRiverTournamentService.getConstant();
        globalData.setSeasonId(-1);
        globalData.setPlayEnums(null);
        globalData.setLastAddPointTime(-1);
        globalData.getPointRankInfoMap().clear();
        if(globalData.getSeasonId() <= 0) {
            //赛季开启
            //因为需要记录赛季，所以开启时间必须是个未来的事件
            LocalDateTime firstOpenTime = DateTimeUtil.toLocalDateTime(constant.getNowWeekTestOpenTime());
            globalData.changeSeason(DateTimeUtil.toMillis(firstOpenTime.plusDays(constant.duration)));
            globalData.setCanSettlementTime(DateTimeUtil.toMillis(firstOpenTime.withHour(constant.DailyBonusTime)));
        }
    }

}
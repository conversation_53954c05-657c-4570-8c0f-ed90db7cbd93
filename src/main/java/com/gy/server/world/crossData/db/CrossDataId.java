package com.gy.server.world.crossData.db;

import com.gy.server.world.crossData.CrossDataType;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.io.Serializable;

/**
 * <AUTHOR> - [Created on 2024/8/15 20:21]
 */
@Embeddable
public class CrossDataId implements Serializable {

    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    private CrossDataType type;

    @Column(name = "group_id")
    private int groupId;

    public CrossDataType getType() {
        return type;
    }

    public void setType(CrossDataType type) {
        this.type = type;
    }

    public int getGroupId() {
        return groupId;
    }

    public void setGroupId(int groupId) {
        this.groupId = groupId;
    }
}

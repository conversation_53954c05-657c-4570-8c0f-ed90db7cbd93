package com.gy.server.world.crossData.db;

import org.hibernate.annotations.Type;

import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 跨服组数据
 *
 * <AUTHOR> - [Created on 2024/8/15 20:17]
 */
@Entity
@Table(name = "cross_group")
public class CrossDataDTO {

    @EmbeddedId
    private CrossDataId id;

    @Column(name = "info")
    @Type(type = "com.gy.server.world.crossData.db.CrossDataInfo")
    private CrossDataInfo info = new CrossDataInfo();


    public CrossDataId getId() {
        return id;
    }

    public void setId(CrossDataId id) {
        this.id = id;
    }

    public CrossDataInfo getInfo() {
        return info;
    }

    public void setInfo(CrossDataInfo info) {
        this.info = info;
    }
}

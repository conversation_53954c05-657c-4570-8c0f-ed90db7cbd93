package com.gy.server.world.crossData;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;

/**
 * <AUTHOR> - [Created on 2024/8/15 20:28]
 */
public class CrossData {
    @Protobuf(order = 100)
    protected String type;
    @Protobuf(order = 101)
    protected int groupId;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getGroupId() {
        return groupId;
    }

    public void setGroupId(int groupId) {
        this.groupId = groupId;
    }

    /**
     * 初始化方法
     * 如果数据库里已有数据，会先进行加载，然后执行本方法
     */
    public void init() {
    }

    /**
     * 入库前调用
     */
    protected void beforeSave() {

    }

    public void tick() {

    }
}

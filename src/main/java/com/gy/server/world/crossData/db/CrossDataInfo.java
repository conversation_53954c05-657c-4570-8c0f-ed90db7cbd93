package com.gy.server.world.crossData.db;

import com.gy.server.game.db.InfoUserType;

/**
 * <AUTHOR> - [Created on 2024/8/15 20:26]
 */
public class CrossDataInfo extends InfoUserType {

    private byte[] bytes;

    @Override
    protected InfoUserType readFromPb(byte[] bytes, Object owner) throws Exception {
        CrossDataInfo info = new CrossDataInfo();
        info.bytes = new byte[bytes.length];
        System.arraycopy(bytes, 0, info.bytes, 0, bytes.length);
        return info;
    }

    @Override
    protected byte[] writeToPb() throws Exception {
        byte[] bytes = new byte[this.bytes.length];
        System.arraycopy(this.bytes, 0, bytes, 0, bytes.length);
        return bytes;
    }

    public byte[] getBytes() {
        return bytes;
    }

    public void setBytes(byte[] bytes) {
        this.bytes = bytes;
    }
}

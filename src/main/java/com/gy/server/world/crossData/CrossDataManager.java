package com.gy.server.world.crossData;

import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.Configuration;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.log.SystemLogger;
import com.gy.server.game.db.DbAssistant;
import com.gy.server.game.db.DbManager;
import com.gy.server.game.event.ServerEventHandler;
import com.gy.server.game.event.ServerEventManager;
import com.gy.server.game.warZone.WarZoneTypeEnums;
import com.gy.server.utils.function.Ticker;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.gy.server.world.crossData.db.CrossDataDTO;
import com.gy.server.world.crossData.db.CrossDataId;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 跨服组管理器
 *
 * <AUTHOR> - [Created on 2024/8/15 20:34]
 */
public class CrossDataManager implements Ticker {

    //世界跨服时，默认分组ID
    public static final int WORLD_CROSS_GROUP_ID = 0;

    private static final long SAVE_PERIOD_SECOND = 30;

    private static final CrossDataManager INSTANCE = new CrossDataManager();

    /**
     * 跨服组数据
     * <type, groupId, data>
     */
    private final Map<CrossDataType, Map<Integer, CrossData>> dataMap = new ConcurrentHashMap<>();

    /**
     * 下次存储数据时间
     */
    private ThreadLocal<LocalDateTime> nextSaveTime = new ThreadLocal<>();

    public static CrossDataManager getInstance() {
        return INSTANCE;
    }

    public void startup() throws Exception {
        init();
    }


    private void init() throws Exception {
        for (CrossDataType type : CrossDataType.values()) {
            //读取数据
            List<CrossData> crossGroupDataList = load(type);
            for (CrossData data : crossGroupDataList) {
                //数据初始化
                data.init();
                if (data instanceof ServerEventHandler) {
                    ServerEventManager.registerEventHandler((ServerEventHandler)data);
                }
                //放入集合
                Map<Integer, CrossData> subMap = dataMap.computeIfAbsent(type, sub -> new ConcurrentHashMap<>());
                subMap.put(data.getGroupId(), data);
            }
        }
    }

    private List<CrossData> load(CrossDataType type) throws Exception {
        List<CrossData> dataList = new ArrayList<>();
        List<CrossDataDTO> crossGroupList = DbAssistant.getCrossGroupListByType(type);
        for (CrossDataDTO crossGroup : crossGroupList) {
            byte[] bytes = crossGroup.getInfo().getBytes();
            CrossData globalData = PbUtilCompress.decode(type.getClazz(), bytes);
            int groupId = globalData.getGroupId();
            if(groupId <= 0){
                //主节点数据
                if(CommonUtils.isMainWorldServer()){
                    dataList.add(globalData);
                }
            }else{
                int hashWorldId = TLBase.getInstance().getRpcUtil().hashKeyToNodeId(groupId);
                //只加载hash自己的服务器数据
                if (Configuration.serverId == hashWorldId) {
                    dataList.add(globalData);
                }
            }
        }
        return dataList;
    }

    private static void save(CrossData data) {
        try {
            data.beforeSave();
            CrossDataDTO global = new CrossDataDTO();
            CrossDataId crossGroupId = new CrossDataId();
            crossGroupId.setType(CrossDataType.valueOf(data.type));
            crossGroupId.setGroupId(data.groupId);
            global.setId(crossGroupId);
            global.getInfo().setBytes(PbUtilCompress.encode(data));
            ThreadPool.execute(() -> DbManager.update(global));
        } catch (Exception e) {
            // 增加错误处理机制
            SystemLogger.error("Failed to save cross data: " + e.getMessage(),e);
            e.printStackTrace();
        }
    }

    public void shutdown() {
        for (Map<Integer, CrossData> value : dataMap.values()) {
            for (CrossData crossGroupData : value.values()) {
                save(crossGroupData);
            }
        }
    }


    /**
     * 创建跨服数据
     *
     * @param type    功能类型
     * @param groupId 跨服组ID
     * @param <T>     指定数据类型
     * @return 指定功能的跨服组数据
     */
    @SuppressWarnings("unchecked")
    public <T extends CrossData> T createData(CrossDataType type, int groupId) {
        Map<Integer, CrossData> groupDataMap = dataMap.computeIfAbsent(type, sub -> new ConcurrentHashMap<>());
        return (T) groupDataMap.computeIfAbsent(groupId, sub -> {
            CrossData crossGroupData = type.create();
            crossGroupData.setType(type.name());
            crossGroupData.setGroupId(groupId);
            save(crossGroupData);

            if (crossGroupData instanceof ServerEventHandler) {
                ServerEventManager.registerEventHandler((ServerEventHandler)crossGroupData);
            }
            return crossGroupData;
        });
    }

    /**
     * 获取跨服组数据
     *
     * @param type    功能类型
     * @param groupId 跨服组ID
     */
    public <T extends CrossData> T getData(CrossDataType type, int groupId) {
        Map<Integer, CrossData> groupDataMap = dataMap.get(type);
        if(groupDataMap != null){
            return (T) groupDataMap.get(groupId);
        }

        return null;
    }

    /**
     * 获取本服加载的跨服组数据
     *
     * @param type 功能类型
     * @param <T>  指定数据类型
     * @return 指定功能的跨服组数据
     */
    @SuppressWarnings("unchecked")
    public <T extends CrossData> List<T> getDataList(CrossDataType type) {
        List<T> crossDataList = new ArrayList<>();
        Map<Integer, CrossData> crossGroupDataMap = dataMap.get(type);
        if (Objects.nonNull(crossGroupDataMap)) {
            crossDataList.addAll((Collection<? extends T>) crossGroupDataMap.values());
        }
        return crossDataList;
    }

    @Override
    public void tick() {
        LocalDateTime now = ServerConstants.getCurrentTimeLocalDateTime();
        LocalDateTime localDateTime = nextSaveTime.get();
        if (localDateTime == null) {
            //初始化下次数据存储时间
            nextSaveTime.set(ServerConstants.getCurrentTimeLocalDateTime().plusSeconds(SAVE_PERIOD_SECOND));
        }
        //保存数据
        if (now.isAfter(nextSaveTime.get())) {
            nextSaveTime.set(now.plusSeconds(SAVE_PERIOD_SECOND));
            for (Map<Integer, CrossData> value : dataMap.values()) {
                for (CrossData crossGroupData : value.values()) {
                    save(crossGroupData);
                }
            }
        }
        //模块tick
        for (Map<Integer, CrossData> groupDataMap : dataMap.values()) {
            for (CrossData crossGroupData : groupDataMap.values()) {
                crossGroupData.tick();
            }
        }
    }
}

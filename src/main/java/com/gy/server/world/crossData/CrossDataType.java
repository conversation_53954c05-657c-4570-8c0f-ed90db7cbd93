package com.gy.server.world.crossData;

import com.gy.server.world.activity.ActivityCrossData;

import java.lang.reflect.InvocationTargetException;

public enum CrossDataType {
    Activity(ActivityCrossData.class),
    ;

    private final Class<? extends CrossData> clazz;

    CrossDataType(Class<? extends CrossData> clazz) {
        this.clazz = clazz;
    }

    public Class<? extends CrossData> getClazz() {
        return clazz;
    }

    public CrossData create() {
        try {
            return clazz.getDeclaredConstructor().newInstance();
        } catch (InstantiationException | IllegalAccessException | InvocationTargetException |
                 NoSuchMethodException e) {
            throw new RuntimeException(e);
        }
    }
}

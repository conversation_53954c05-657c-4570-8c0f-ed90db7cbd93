package com.gy.server.world.net;

import com.google.protobuf.AbstractMessage;
import com.gy.server.core.ServerConstants;
import com.gy.server.net.netty.ClientConnection;
import com.gy.server.packet.PbPacket;

import io.netty.channel.ChannelHandlerContext;

/**
 * <AUTHOR> - [Created on 2018/9/12 19:52]
 */
public class GameNodeConnection extends ClientConnection {


    public GameNodeConnection(ChannelHandlerContext ctx) {
        super(ctx);
    }

    @Override
    public AbstractMessage buildPacket(int ptCode, AbstractMessage message) {
        return buildPacket(ptCode, message, ServerConstants.getCurrentTimeMillis());
    }

    private AbstractMessage buildPacket(int ptCode, AbstractMessage message, long timeStamp) {
        PbPacket.Packet.Builder packet = PbPacket.Packet.newBuilder()
                .setPtCode(ptCode);
        if (message != null) {
            packet.setData(message.toByteString())
                    .setTimeStamp(timeStamp);
        }

        return packet.build();
    }

}

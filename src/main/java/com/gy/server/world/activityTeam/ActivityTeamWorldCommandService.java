package com.gy.server.world.activityTeam;

import com.gy.server.annotation.MessageMethod;
import com.gy.server.annotation.MessageServiceBean;
import com.gy.server.core.MessageServerType;
import com.gy.server.core.callback.AbstractActiveCallbackTask;
import com.gy.server.core.command.CommandRequestParams;
import com.gy.server.core.command.PlayerCommandRequest;
import com.gy.server.game.activity.bean.infiniteRealm.InfiniteHelpInfo;
import com.gy.server.game.activity.bean.infiniteRealm.InfiniteRealmScoreModify;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbActivityTeam;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.world.activityTeam.bean.ActivityTeam;
import com.gy.server.world.activityTeam.bean.InfiniteRealmPlayer;
import com.gy.server.world.activityTeam.bean.JoinCondition;
import com.ttlike.server.tl.baselib.thread.ThreadPool;
import org.apache.commons.lang3.tuple.Pair;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @program: tl_game_4
 * @description:
 * @author: Huang.Xia
 * @create: 2025/1/25
 **/
@MessageServiceBean(description = "组队活动 WORLD消息服务", messageServerType = MessageServerType.world)
public class ActivityTeamWorldCommandService {
    @MessageMethod(description = "队伍列表")
    private static void teamList(PlayerCommandRequest request, CommandRequestParams params){
        long playerId = request.getPlayerId();
        int zoneId = params.getParam(0);
        int activityId = params.getParam(1);

        List<PbActivityTeam.ActivityTeamInfo> list = ActivityTeamHelper.list(playerId, ActivityTeamManager.getInstance().getTeams().get(activityId, zoneId), true, false);
        request.addCallbackParam(-1);
        request.addCallbackParam(list);
    }

    @MessageMethod(description = "创建队伍")
    private static void createTeam(PlayerCommandRequest request, CommandRequestParams params){
        long playerId = request.getPlayerId();
        int zoneId = params.getParam(0);
        int activityId = params.getParam(1);

        String teamName = params.getParam(2);
        int ruleId = params.getParam(3);
        List<PbActivityTeam.ActivityTeamJoinCondition> joinConditions = params.getParam(4);
        boolean needApproval = params.getParam(5);

        int code = ActivityTeamHelper.createTeam(playerId, activityId, zoneId, teamName, ruleId, joinConditions, needApproval);
        request.addCallbackParam(code);
        if (code == Text.没有异常) {
            ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, zoneId, playerId);
            request.addCallbackParam(team.genPb(true, zoneId, activityId));
        }
    }

    @MessageMethod(description = "队伍详情")
    private static void teamDetail(PlayerCommandRequest request, CommandRequestParams params){
        long playerId = request.getPlayerId();
        int zoneId = params.getParam(0);
        int activityId = params.getParam(1);

        long teamId = params.getParam(2);

        ActivityTeam team = ActivityTeamManager.getInstance().getTeamById(activityId, zoneId, teamId, playerId);
        if (team != null) {
            ThreadPool.execute(new TeamDetailAbstractActiveCallbackTask(request, team, zoneId, activityId));
        }else{
            request.addCallbackParam(Text.组队队伍不存在);
        }
    }

    @MessageMethod(description = "加入队伍")
    private static void teamJoin(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int zoneId = params.getParam(0);
        int activityId = params.getParam(1);

        Set<Long> teamIds = params.getParam(2);
        List<JoinCondition> conditions = params.getParam(3);

        int rstCode = Text.没有异常;

        ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, zoneId, playerId);
        if (team != null) {
            rstCode =Text.组队已在队伍中;
        }else {
            rstCode = ActivityTeamHelper.joinTeam(playerId, activityId, zoneId, teamIds, conditions);
            if(rstCode == Text.没有异常){
                team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, zoneId, playerId);
            }
        }

        request.addCallbackParam(rstCode);
        if(team != null){
            request.addCallbackParam(team.genPb(true, zoneId, activityId));
        }
    }

    @MessageMethod(description = "加入队伍")
    private static void joinList(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int zoneId = params.getParam(0);
        int activityId = params.getParam(1);

        ThreadPool.execute(new JoinListActiveCallbackTask(request, playerId, activityId, zoneId));
    }

    @MessageMethod(description = "申请审批")
    private static void joinApproval(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int zoneId = params.getParam(0);
        int activityId = params.getParam(1);
        Set<Long> pids = params.getParam(2);
        boolean agree = params.getParam(3);

        int code = ActivityTeamHelper.joinApproval(playerId, activityId, zoneId, pids, agree);
        request.addCallbackParam(code);
    }

    @MessageMethod(description = "修改加入条件")
    private static void changeJoinCondition(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int zoneId = params.getParam(0);
        int activityId = params.getParam(1);
        boolean needApproval = params.getParam(2);
        List<PbActivityTeam.ActivityTeamJoinCondition> joinConditions = params.getParam(3);

        int code = ActivityTeamHelper.changeJoinCondition(playerId, activityId, zoneId, needApproval, joinConditions);
        request.addCallbackParam(code);
    }

    @MessageMethod(description = "退出队伍")
    private static void quitTeam(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int zoneId = params.getParam(0);
        int activityId = params.getParam(1);

        int code = ActivityTeamHelper.quitTeam(playerId, activityId, zoneId, true);
        request.addCallbackParam(code);
    }

    @MessageMethod(description = "踢人")
    private static void kickMember(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int zoneId = params.getParam(0);
        int activityId = params.getParam(1);
        long memberId = params.getParam(2);

        int code = ActivityTeamHelper.kickMember(playerId, memberId, activityId, zoneId, true);
        request.addCallbackParam(code);
    }

    @MessageMethod(description = "无量幻境主界面")
    private static void secretRealmMainInfo(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int zoneId = params.getParam(0);
        int activityId = params.getParam(1);

        ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, zoneId, playerId);
        if(team == null){
            request.addCallbackParam(Text.组队队伍不存在);
            return;
        }

        request.addCallbackParam(Text.没有异常);
        request.addCallbackParam(team.getMembers());
        request.addCallbackParam(team.getSecretRealmTeamData().genSecretRealmHelpTips(playerId));
        request.addCallbackParam(team.getSecretRealmTeamData().genScoreAdd());
        request.addCallbackParam(team.getSecretRealmTeamData().getMemberScenes());
        request.addCallbackParam(team.getSecretRealmTeamData().getLeftHps());
    }

    @MessageMethod(description = "查询自己的队伍id")
    private static void invitePlayer(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int zoneId = params.getParam(0);
        int activityId = params.getParam(1);

        long targetPid = params.getParam(2);

        Pair<Integer, Long> pair = ActivityTeamHelper.invitePlayer(playerId, targetPid, activityId, zoneId);

        request.addCallbackParam(pair.getLeft());
        request.addCallbackParam(pair.getRight());
    }

    @MessageMethod(description = "查询自己的队伍id")
    private static void inviteAgree(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int zoneId = params.getParam(0);
        int activityId = params.getParam(1);

        long teamId = params.getParam(2);
        boolean agree = params.getParam(3);

        int code = ActivityTeamHelper.inviteAgree(teamId, playerId, activityId, zoneId, agree);


        request.addCallbackParam(code);
    }

    @MessageMethod(description = "进入下一层")
    private static void secretRealmNextStage(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int zoneId = params.getParam(0);
        int activityId = params.getParam(1);

        int sceneId = params.getParam(2);
        long maxHp = params.getParam(3);
        long atk = params.getParam(4);
        int score = params.getParam(5);

        ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, zoneId, playerId);
        if(team == null){
            request.addCallbackParam(Text.组队队伍不存在);
            return;
        }

        int addScore = 0;

        InfiniteRealmPlayer player = team.getSecretRealmTeamData().getMembers().get(playerId);
        if(player != null){
            player.setSceneId(sceneId);
            player.setMaxHp(maxHp);
            player.setAtk(atk);
            if(score > 0) {
                addScore = player.addTeamScore(score, team);
            }
        }
        request.addCallbackParam(Text.没有异常);
        request.addCallbackParam(team.getSecretRealmTeamData().getSameProgressMembers(playerId));
        request.addCallbackParam(addScore);
        request.addCallbackParam(sceneId);
    }

    @MessageMethod(description = "求助信息")
    private static void secretRealmHelpInfoReq(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int zoneId = params.getParam(0);
        int activityId = params.getParam(1);

        ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, zoneId, playerId);
        if(team == null){
            request.addCallbackParam(Text.组队队伍不存在);
            return;
        }

        //同步构建数据
        List<PbActivityTeam.InfiniteRealmHelpInfo> list = team.getSecretRealmTeamData().genSecretRealmHelpInfo(playerId, activityId, false);

        //异步加载mini player并补充
        ThreadPool.execute(new SecretRealmHelpInfoActiveCallbackTask(request, list));
    }

    @MessageMethod(description = "求助记录")
    private static void secretRealmHelpRecordReq(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int zoneId = params.getParam(0);
        int activityId = params.getParam(1);

        ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, zoneId, playerId);
        if(team == null){
            request.addCallbackParam(Text.组队队伍不存在);
            return;
        }

        List<PbActivityTeam.InfiniteRealmHelpRecord> list = team.getSecretRealmTeamData().genSecretRealmHelpRecord(playerId,activityId,false);

        //异步加载mini player并补充
        ThreadPool.execute(new SecretRealmHelpRecordActiveCallbackTask(request, list));
    }

    @MessageMethod(description = "PVP匹配")
    private static void secretRealmPVPMatch(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int zoneId = params.getParam(0);
        int activityId = params.getParam(1);

        ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, zoneId, playerId);
        if(team == null){
            request.addCallbackParam(Text.组队队伍不存在);
            return;
        }

        InfiniteRealmPlayer toMatchPlayer = ActivityTeamHelper.secretRealmPvPMatch(team, playerId, activityId, zoneId);
        if(toMatchPlayer == null){
            request.addCallbackParam(Text.没有可匹配的玩家);
            return;
        }

        toMatchPlayer.setMatched(true);


        request.addCallbackParam(Text.没有异常);
        request.addCallbackParam(toMatchPlayer.getPid());
        request.addCallbackParam(toMatchPlayer.getMaxHp());
        request.addCallbackParam(toMatchPlayer.getAtk());
    }

    @MessageMethod(description = "求助事件挑战-自己")
    private static void secretRealmChallenge(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int zoneId = params.getParam(0);
        int activityId = params.getParam(1);
        int eventId = params.getParam(2);

        ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, zoneId, playerId);
        if(team == null){
            request.addCallbackParam(Text.组队队伍不存在);
            return;
        }

        InfiniteHelpInfo helpInfo = team.getSecretRealmTeamData().getHelpInfo(playerId, eventId);
        if(helpInfo == null){
            request.addCallbackParam(Text.求助事件不存在);
            return;
        }

        if(helpInfo.isClosed() || helpInfo.getLeftHp() == 0){
            request.addCallbackParam(Text.挑战的事件已经结束);
        }

        if(!helpInfo.canChallenge(playerId)){
            request.addCallbackParam(Text.队友正在挑战中);
            return;
        }

        //挑战中标记
        helpInfo.setCurChallengePid(playerId);

        request.addCallbackParam(Text.没有异常);
        request.addCallbackParam(helpInfo.getLeftHp());
        request.addCallbackParam(helpInfo.getMaxHp());
    }

    @MessageMethod(description = "添加团队积分")
    private static void addTeamScore(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int zoneId = params.getParam(0);
        int activityId = params.getParam(1);
        int score= params.getParam(2);

        ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, zoneId, playerId);
        if(team != null) {
            InfiniteRealmPlayer member =team.getSecretRealmTeamData().getMembers().get(playerId);
            if(member != null){
                member.addTeamScore(score, team);
            }
        }
    }

    @MessageMethod(description = "添加个人积分")
    private static void addPlayerScore(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int zoneId = params.getParam(0);
        int activityId = params.getParam(1);
        int score= params.getParam(2);

        ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, zoneId, playerId);
        if(team != null) {
            InfiniteRealmPlayer member =team.getSecretRealmTeamData().getMembers().get(playerId);
            if(member != null){
                member.addPlayerScore(score, team);
            }
        }
    }

    @MessageMethod(description = "更新求助事件剩余血量")
    private static void updateHelpEventLeftHps(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int zoneId = params.getParam(0);
        int activityId = params.getParam(1);
        int eventId = params.getParam(2);
        long damage = params.getParam(3);
        long helpSendPlayerId = params.getParam(4);

        ActivityTeamHelper.updateHelpEventLeftHps(playerId,activityId, zoneId, helpSendPlayerId, eventId, damage);
    }

    @MessageMethod(description = "更新求助事件剩余血量")
    private static void secretRealmHelpRequestReq(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int zoneId = params.getParam(0);
        int activityId = params.getParam(1);
        int eventId = params.getParam(2);
        long leftHp = params.getParam(3);
        long targetPid = params.getParam(4);
        long hp = params.getParam(5);
        long atk = params.getParam(6);
        int eventTemplateId = params.getParam(7);

        ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, zoneId, playerId);
        if(team != null) {
            if(!team.getSecretRealmTeamData().canRequestHelp(playerId)){
                request.addCallbackParam(Text.同时只能有一个求助);
            }else{
                request.addCallbackParam(Text.没有异常);
                team.getSecretRealmTeamData().createHelp(playerId, eventId, eventTemplateId, activityId, leftHp, targetPid, hp, atk);
            }
        }else{
            request.addCallbackParam(Text.组队队伍不存在);
        }
    }

    @MessageMethod(description = "求助事件挑战-队友")
    private static void secretRealmHelpChallenge(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int zoneId = params.getParam(0);
        int activityId = params.getParam(1);
        int helpId = params.getParam(2);

        ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, zoneId, playerId);
        if(team == null){
            request.addCallbackParam(Text.组队队伍不存在);
            return;
        }

        InfiniteHelpInfo helpInfo = team.getSecretRealmTeamData().getHelpInfo(helpId);
        if(helpInfo == null){
            request.addCallbackParam(Text.求助信息不存在);
        }else if(helpInfo.getLeftHp() == 0){
            request.addCallbackParam(Text.已经完成挑战);
        }else if(!helpInfo.canChallenge(playerId)){
            request.addCallbackParam(Text.队友正在挑战中);
        }else {
            //挑战中标记
            helpInfo.setCurChallengePid(playerId);

            request.addCallbackParam(Text.没有异常);
            request.addCallbackParam(helpInfo.getEventId());
            request.addCallbackParam(helpInfo.getLeftHp());
            request.addCallbackParam(helpInfo.getSendPid());
        }
    }

    @MessageMethod(description = "领取援助奖励")
    private static void secretRealmHelpRewardClaim(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int zoneId = params.getParam(0);
        int activityId = params.getParam(1);
        int helpId = params.getParam(2);

        ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, zoneId, playerId);
        if (team != null) {
            InfiniteHelpInfo helpInfo = team.getSecretRealmTeamData().getHelpInfo(helpId);
            if(helpInfo == null){
                request.addCallbackParam(Text.求助信息不存在);
            }else if(helpInfo.getLeftHp() > 0){
                request.addCallbackParam(Text.挑战尚未完成不能领奖);
            }else if(helpInfo.isGetReward(playerId)){
                request.addCallbackParam(Text.奖励已经领取过);
            }else if(!helpInfo.getDamages().containsKey(playerId) || helpInfo.getSceneId() != playerId){
                request.addCallbackParam(Text.没有可领取的奖励);
            }else{
                request.addCallbackParam(Text.没有异常);
                int score = helpInfo.getRewardScore(playerId, activityId);
                //领奖标记
                helpInfo.getGetReward().put(playerId, true);
                //领取团队积分
                InfiniteRealmPlayer member =team.getSecretRealmTeamData().getMembers().get(playerId);
                if(member != null && score > 0){
                    member.addPlayerScore(score, team);
                }
                request.addCallbackParam(score);

                if(helpInfo.getSceneId() == playerId){
                   request.addCallbackParam(true);
                }else{
                   request.addCallbackParam(false);
                }
                request.addCallbackParam(helpInfo.getEventTemplateId());
                request.addCallbackParam(helpInfo.getEventId());
            }
        } else {
            request.addCallbackParam(Text.组队队伍不存在);
        }
    }

    @MessageMethod(description = "添加积分加成")
    private static void addScoreModify(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int zoneId = params.getParam(0);
        int activityId = params.getParam(1);
        int rate = params.getParam(2);
        int times = params.getParam(3);
        int itemId = params.getParam(4);
        int addType = params.getParam(5);

        ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, zoneId, playerId);
        if (team != null) {
            team.getSecretRealmTeamData().getScoreModify().add(new InfiniteRealmScoreModify(rate, times, playerId, itemId, addType));
        }
    }

    @MessageMethod(description = "取消匹配装")
    private static void cancelMatch(PlayerCommandRequest request, CommandRequestParams params) {
        int zoneId = params.getParam(0);
        int activityId = params.getParam(1);

        long targetPid = params.getParam(2);

        ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, zoneId, targetPid);
        if (team == null) {
            if(team != null){
                InfiniteRealmPlayer targetPlayer = team.getSecretRealmTeamData().getMembers().get(targetPid);
                if(targetPlayer != null){
                    targetPlayer.setMatched(false);
                }
            }
        }
    }

    @MessageMethod(description = "排行榜")
    private static void secretRealmRanks(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int zoneId = params.getParam(0);
        int activityId = params.getParam(1);

        int rankType = params.getParam(2);

        PbProtocol.InfiniteRealmRankInfoRst rst = ActivityTeamHelper.genInfiniteRanks(activityId, rankType, zoneId, playerId);
        request.addCallbackParam(Text.没有异常);
        request.addCallbackParam(rst);
    }

    @MessageMethod(description = "排行榜领奖")
    private static void secretRealmRankReward(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int zoneId = params.getParam(0);
        int activityId = params.getParam(1);

        int rankType = params.getParam(2);

        Pair<Integer,Integer> pair = ActivityTeamHelper.infiniteRankRewardCheck(activityId, rankType, zoneId, playerId);

        request.addCallbackParam(pair.getLeft());
        request.addCallbackParam(pair.getRight());
    }

    @MessageMethod(description = "双人组队-夜探藏经阁-主界面")
    private static void sutraPavilionMainReq(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int zoneId = params.getParam(0);
        int activityId = params.getParam(1);

        ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, zoneId, playerId);
        if(team == null || team.getSpData() == null){
            request.addCallbackParam(Text.组队队伍不存在);
            return;
        }
        request.addCallbackParam(Text.没有异常);
        request.addCallbackParam(team.getSpData().genFloorInfo(playerId));
        request.addCallbackParam(team.getMembers());

    }
    @MessageMethod(description = "双人组队-夜探藏经阁-解锁地块")
    private static void sutraPavilionUnlockNodeReq(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int activityId = params.getParam(0);
        int zoneId = params.getParam(1);
        int nodeId = params.getParam(2);

        PbProtocol.SutraPavilionUnlockNodeRst rst = ActivityTeamHelper.spUnlockNode(activityId, zoneId, playerId, nodeId);
        request.addCallbackParam(rst);
    }

    @MessageMethod(description = "双人组队-夜探藏经阁-触发地块")
    private static void sutraPavilionTriggerReq(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int activityId = params.getParam(0);
        int zoneId = params.getParam(1);
        int nodeId = params.getParam(2);
        int targetNodeId = params.getParam(3);

        PbProtocol.SutraPavilionTriggerNodeRst rst = ActivityTeamHelper.spTriggerNode(activityId, zoneId, playerId, nodeId, targetNodeId);
        request.addCallbackParam(rst);
    }

    @MessageMethod(description = "双人组队-夜探藏经阁-移动")
    private static void sutraPavilionMoveReq(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int activityId = params.getParam(0);
        int zoneId = params.getParam(1);
        int nodeId = params.getParam(2);

        PbProtocol.SutraPavilionMoveRst rst = ActivityTeamHelper.spMove(activityId, zoneId, playerId, nodeId);
        request.addCallbackParam(rst);
    }

    @MessageMethod(description = "双人组队-夜探藏经阁-协助信息")
    private static void sutraPavilionAssistReq(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int activityId = params.getParam(0);
        int zoneId = params.getParam(1);
        PbProtocol.SutraPavilionAssistRst assistRst = ActivityTeamHelper.spAssist(activityId, zoneId, playerId);
        request.addCallbackParam(assistRst);
    }

    @MessageMethod(description = "双人组队-夜探藏经阁-日志信息")
    private static void sutraPavilionLogReq(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int activityId = params.getParam(0);
        int zoneId = params.getParam(1);
        PbProtocol.SutraPavilionLogRst logRst = ActivityTeamHelper.spLog(activityId, zoneId, playerId);
        request.addCallbackParam(logRst);
    }

    @MessageMethod(description = "双人组队-夜探藏经阁-协助")
    private static void sutraPavilionHelpReq(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int activityId = params.getParam(0);
        int zoneId = params.getParam(1);
        int index = params.getParam(2);
        PbProtocol.SutraPavilionHelpMemberRst helpMemberRst = ActivityTeamHelper.spHelpMember(activityId, zoneId, playerId, index);
        request.addCallbackParam(helpMemberRst);
    }

    @MessageMethod(description = "双人组队-夜探藏经阁-增加协助信息")
    private static void sutraPavilionAddAssistInfo(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int activityId = params.getParam(0);
        int zoneId = params.getParam(1);
        int floorId = params.getParam(2);
        int eventId = params.getParam(3);
        Map<Integer, Long> surplusHps = params.getParam(4);
        ActivityTeamHelper.addSpAssistInfo(activityId, zoneId, playerId, floorId, eventId, surplusHps);
    }

    @MessageMethod(description = "双人组队-夜探藏经阁-排行榜信息")
    private static void sutraPavilionRankInfo(PlayerCommandRequest request, CommandRequestParams params) {
        long playerId = request.getPlayerId();
        int activityId = params.getParam(0);
        int zoneId = params.getParam(1);
        int type = params.getParam(2);
        PbProtocol.SutraPavilionRankRst rankRst = ActivityTeamHelper.spRankInfo(activityId, zoneId, playerId, type);
        request.addCallbackParam(rankRst);
    }

    private static class JoinListActiveCallbackTask extends AbstractActiveCallbackTask {
        private final long playerId;
        private final int activityId;
        private final int zoneId;

        public JoinListActiveCallbackTask(PlayerCommandRequest request, long playerId, int activityId, int zoneId) {
            super(request);
            this.playerId = playerId;
            this.activityId = activityId;
            this.zoneId = zoneId;
        }

        @Override
        protected void execute() {
            List<PbActivityTeam.ActivityTeamJoinInfo> list = ActivityTeamHelper.joinList(playerId, activityId, zoneId);
            request.addCallbackParam(Text.没有异常);
            if(CollectionUtil.isNotEmpty(list)){
                request.addCallbackParam(list);
            }
        }
    }

    private static class SecretRealmHelpInfoActiveCallbackTask extends AbstractActiveCallbackTask {
        private final List<PbActivityTeam.InfiniteRealmHelpInfo> list;

        public SecretRealmHelpInfoActiveCallbackTask(PlayerCommandRequest request, List<PbActivityTeam.InfiniteRealmHelpInfo> list) {
            super(request);
            this.list = list;
        }

        @Override
        protected void execute() {
            //补全PVP事件的mini player，若有
            List<PbActivityTeam.InfiniteRealmHelpInfo> rst = new ArrayList<>();
            for(PbActivityTeam.InfiniteRealmHelpInfo info : list){
                PbActivityTeam.InfiniteRealmHelpInfo.Builder b = info.toBuilder();
                //填充求助玩家信息
                long pid = info.getPlayerId();
                MiniGamePlayer miniGamePlayer = PlayerHelper.getMiniPlayer(pid);
                if(miniGamePlayer != null){
                    b.setUser(miniGamePlayer.genMinMiniUser());
                }
                //填充敌人信息
                if(info.getEnemy().hasPlayer()){
                    PbActivityTeam.InfiniteRealmEnemy.Builder enemy = b.getEnemy().toBuilder();
                    PbActivityTeam.InfiniteRealmEnemy.Player.Builder player = b.getEnemy().getPlayer().toBuilder();
                    pid = player.getUser().getId();
                    miniGamePlayer = PlayerHelper.getMiniPlayer(pid);
                    if(miniGamePlayer != null){
                        player.setUser(miniGamePlayer.genMinMiniUser());
                        enemy.setPlayer(player);
                        b.setEnemy(enemy);
                    }
                }

                rst.add(b.build());
            }

            request.addCallbackParam(Text.没有异常);
            request.addCallbackParam(rst);
        }
    }

    private static class SecretRealmHelpRecordActiveCallbackTask extends AbstractActiveCallbackTask {
        private final List<PbActivityTeam.InfiniteRealmHelpRecord> list;

        public SecretRealmHelpRecordActiveCallbackTask(PlayerCommandRequest request, List<PbActivityTeam.InfiniteRealmHelpRecord> list) {
            super(request);
            this.list = list;
        }

        @Override
        protected void execute() {
            //补全PVP事件的mini player，若有
            List<PbActivityTeam.InfiniteRealmHelpRecord> rst = new ArrayList<>();
            for(PbActivityTeam.InfiniteRealmHelpRecord info : list){
                if(info.getEnemy().hasPlayer()){
                    PbActivityTeam.InfiniteRealmHelpRecord.Builder b = info.toBuilder();
                    PbActivityTeam.InfiniteRealmEnemy.Builder enemy = b.getEnemy().toBuilder();
                    PbActivityTeam.InfiniteRealmEnemy.Player.Builder player = b.getEnemy().getPlayer().toBuilder();
                    long pid = player.getUser().getId();
                    MiniGamePlayer miniGamePlayer = PlayerHelper.getMiniPlayer(pid);
                    if(miniGamePlayer != null){
                        player.setUser(miniGamePlayer.genMinMiniUser());
                        enemy.setPlayer(player);
                        b.setEnemy(enemy);
                        rst.add(b.build());
                    }
                }else{
                    rst.add(info);
                }
            }

            request.addCallbackParam(Text.没有异常);
            request.addCallbackParam(rst);
        }
    }

    private static class TeamDetailAbstractActiveCallbackTask extends AbstractActiveCallbackTask {
        private final ActivityTeam team;
        private final int zoneId;
        private final int activityId;

        public TeamDetailAbstractActiveCallbackTask(PlayerCommandRequest request, ActivityTeam team, int zoneId, int activityId) {
            super(request);
            this.team = team;
            this.zoneId = zoneId;
            this.activityId = activityId;
        }

        @Override
        protected void execute() {
            request.addCallbackParam(Text.没有异常);
            request.addCallbackParam(team.genPb(true, zoneId, activityId));
        }
    }
}

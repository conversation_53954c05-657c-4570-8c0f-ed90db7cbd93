package com.gy.server.world.activityTeam;

import com.gy.server.core.Configuration;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.callback.response.CallbackResponse;
import com.gy.server.core.command.CommandRequest;
import com.gy.server.core.command.PlayerCommandRequest;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.core.log.SystemLogger;
import com.gy.server.core.pathFind.Point;
import com.gy.server.core.pathFind.ReachFinder;
import com.gy.server.game.activity.*;
import com.gy.server.game.activity.bean.InfiniteRealmEvent;
import com.gy.server.game.activity.bean.infiniteRealm.InfiniteHelpInfo;
import com.gy.server.game.activity.data.infiniteRealm.InfiniteRealmActivityData;
import com.gy.server.game.activity.data.infiniteRealm.InfiniteRealmRankRewardTemplate;
import com.gy.server.game.activity.data.infiniteRealm.InfiniteRealmSceneTemplate;
import com.gy.server.game.activity.data.sutraPavilion.SutraPavilionActivityData;
import com.gy.server.game.activity.data.sutraPavilion.template.TheSutraRepositoryFloorTemplate;
import com.gy.server.game.activity.data.sutraPavilion.template.TheSutraRepositoryIncidentGroupTemplate;
import com.gy.server.game.activity.data.sutraPavilion.template.TheSutraRepositoryIncidentTemplate;
import com.gy.server.game.activity.data.sutraPavilion.template.TheSutraRepositoryRewardTemplate;
import com.gy.server.game.activity.module.InfiniteRealmActivityModule;
import com.gy.server.game.activity.module.SutraPavilionActivityModule;
import com.gy.server.game.activity.module.sutraPavilion.SutraPavilionHelper;
import com.gy.server.game.activity.module.sutraPavilion.event.AbsSPEvent;
import com.gy.server.game.activity.module.sutraPavilion.event.SpEventType;
import com.gy.server.game.activity.team.ActivityTeamService;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.mail.MailHelper;
import com.gy.server.game.mail.MailManager;
import com.gy.server.game.mail.MailType;
import com.gy.server.game.mail.globalMail.GlobalMailType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.reddot.RedDot;
import com.gy.server.game.role.PlayerRoleService;
import com.gy.server.game.text.Text;
import com.gy.server.game.text.TextParamText;
import com.gy.server.game.warZone.WarZoneTypeEnums;
import com.gy.server.packet.PbActivityTeam;
import com.gy.server.packet.PbCommons;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.world.activity.sutraPavilion.bean.*;
import com.gy.server.world.activityTeam.bean.ActivityTeam;
import com.gy.server.world.activityTeam.bean.InfiniteRealmPlayer;
import com.gy.server.world.activityTeam.bean.InfiniteRealmTeamData;
import com.gy.server.world.activityTeam.bean.JoinCondition;
import com.gy.server.world.activityTeam.template.TeamRuleTemplate;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.rpc.TLMessageCallbackTask;
import com.ttlike.server.tl.baselib.thread.ThreadPool;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;
import java.util.function.Consumer;

/**
 * @program: tl_game_4
 * @description:
 * @author: Huang.Xia
 * @create: 2025/1/25
 **/
public class ActivityTeamHelper {

    public static WarZoneTypeEnums getWarZoneType(int activityId){
        ActivityTemplate data = ActivityService.getActivityTemplate(activityId);
        WarZoneTypeEnums warZoneTypeEnums = data.type.getWarZoneTypeEnums();
        if (warZoneTypeEnums == null){
            SystemLogger.error("ActivityTeamHelper.getWarZoneType: unknown activity type: " + data.type);
            return null;
        }
        return warZoneTypeEnums;
    }

    /**
     * 限GS调用
     */
    public static int getWarZoneId(int activityId, boolean isCross){
        if(!isCross){
            return -1 * Configuration.serverId;
        }
        ActivityTemplate activityTemplate = ActivityService.getActivityTemplate(activityId);
        int[] crossNodeGroup = activityTemplate.getCrossNodeGroup();
        return crossNodeGroup[1];
    }

    //GS专用
    public static TeamActivity getTeamActivity(Player player,int activityId){
        ActivityModule activityModule = player.getActivityModel().getActivityModule(activityId);
        if (activityModule != null) {
            if (activityModule instanceof TeamActivity) {
                return (TeamActivity) activityModule;
            }
        }

        return null;
    }

    public static int playerOptCheck(Player player, int activityId){
        ActivityModule activityModule = player.getActivityModel().getActivityModule(activityId);
        if (activityModule == null) {
            return Text.活动暂未开启;
        }
        return Text.没有异常;
    }

    public static int infiniteRealmPlayCheck(Player player, int activityId){
        ActivityModule activityModule = player.getActivityModel().getActivityModule(activityId);
        if (activityModule == null) {
            return Text.活动暂未开启;
        }

        if(!(activityModule instanceof InfiniteRealmActivityModule)){
            return Text.数据异常;
        }

        InfiniteRealmActivityData data = ((InfiniteRealmActivityModule)((InfiniteRealmActivityModule) activityModule)).getData();
        if(data == null){
            return  Text.数据异常;
        }

        if(data.getState(activityId) != 3){
            return Text.无量秘境不在玩法时间;
        }
        return Text.没有异常;
    }

    /**
     * GS专用业务处理
     * Service辅助方法
     * @param errorConsumer  错误处理
     * @param responseConsumer 回调处理
     * @param localConsumer 本地处理
     * @param player 玩家
     * @param activityId 活动id
     * @param methodId 远程方法名
     * @param params 附加参数，默认添加：第一个参数为战区id，第二个参数为活动id
     */
    public static void business(Consumer<Integer> errorConsumer,
                                Consumer<CallbackResponse> responseConsumer, Runnable localConsumer,
                                Player player, int activityId, String methodId, Object... params) {

        ActivityModule activityModule = player.getActivityModel().getActivityModule(activityId);
        if (activityModule == null) {
            errorConsumer.accept(Text.活动暂未开启);
            return;
        }

        if (activityModule instanceof TeamActivity) {
            TeamActivity activity = (TeamActivity) activityModule;
            if (activity.isWarZoneCross()) {
                ActivityTemplate activityTemplate = ActivityService.getActivityTemplate(activityId);
                int[] crossNodeGroup = activityTemplate.getCrossNodeGroup();
                int nodeId = crossNodeGroup[0];
                int zoneId = crossNodeGroup[1];
                Object[] objects;
                if(params == null){
                    objects = new Object[2];
                }else{
                    objects = new Object[params.length + 2];
                }
                objects[0] = zoneId;
                objects[1] = activityId;
                System.arraycopy(params, 0, objects, 2, params.length);

                //战区范围
                PlayerCommandRequest request = CommandRequests.newPlayerCommandRequest(methodId, player.getPlayerId(), -1,true);
                TLBase.getInstance().getRpcUtil().sendToHashNodeWithCallBack(new BusinessCallbackTask(responseConsumer, errorConsumer), ServerType.WORLD, nodeId, request, objects);
            } else {
                localConsumer.run();
            }
        }else{
            errorConsumer.accept(Text.参数异常);
        }

    }

    public static void businessWithoutResponse(Runnable localConsumer, Player player, int activityId, String methodId, Object... params) {
        ActivityModule activityModule = player.getActivityModel().getActivityModule(activityId);
        if (activityModule == null) {
            return;
        }

        if (activityModule instanceof TeamActivity) {
            TeamActivity activity = (TeamActivity) activityModule;
            if (activity.isWarZoneCross()) {
                ActivityTemplate activityTemplate = ActivityService.getActivityTemplate(activityId);
                int[] crossNodeGroup = activityTemplate.getCrossNodeGroup();
                int nodeId = crossNodeGroup[0];
                int zoneId = crossNodeGroup[1];
                Object[] objects;
                if(params == null){
                    objects = new Object[2];
                }else{
                    objects = new Object[params.length + 2];
                }
                objects[0] = zoneId;
                objects[1] = activityId;
                System.arraycopy(params, 0, objects, 2, params.length);

                //战区范围
                PlayerCommandRequest request = CommandRequests.newPlayerCommandRequest(methodId, player.getPlayerId(), -1,true);
                TLBase.getInstance().getRpcUtil().sendToHashNode(ServerType.WORLD, nodeId, request, objects);
            } else {
                localConsumer.run();
            }
        }

    }



    public static List<PbActivityTeam.ActivityTeamInfo> list(long pid, Map<Long, ActivityTeam> teams, boolean isCross, boolean withPlayerData){
        List<PbActivityTeam.ActivityTeamInfo> list = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(teams)){
             List<ActivityTeam> filtered = new ArrayList<>();
             int maxMemberCount = 0;
             for(ActivityTeam team : teams.values()) {
                 if(maxMemberCount == 0){
                     TeamRuleTemplate rule = ActivityTeamService.getTeamRuleTemplates().get(team.getRuleId());
                     maxMemberCount = rule.memberCount;
                 }
                 if (team.getMembers().size() < maxMemberCount) {
                     filtered.add(team);
                 }
             }

             if(filtered.size() > 5){
                 Collections.shuffle(filtered);
             }
             for(int i = 0; i < filtered.size() && i < 5; i++){
                 ActivityTeam team = filtered.get(i);
                 PbActivityTeam.ActivityTeamInfo teamInfo = team.genInfo(pid, maxMemberCount, isCross, withPlayerData);
                 if(teamInfo != null){
                     list.add(teamInfo);
                 }
             }
        }
        return list;
    }

    public static int createTeam(long pid, int activityId, int warZoneId, String teamName, int ruleId,
                                 List<PbActivityTeam.ActivityTeamJoinCondition> joinConditions, boolean needApproval){

        ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, warZoneId, pid);
        if (team != null){
            return Text.组队已在队伍中;
        }
        ActivityTeamManager.getInstance().createTeam(activityId, warZoneId, teamName, pid,  ruleId, needApproval, joinConditions);
        return Text.没有异常;
    }

    public static int joinTeam(long pid, int activityId, int warZoneId, Set<Long> teamIds, List<JoinCondition> joinConditions){
        if(ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, warZoneId, pid) != null){
            return Text.组队已在队伍中;
        }
        List<ActivityTeam> teams = new ArrayList<>();
        TeamRuleTemplate rule = null;
        for (Long teamId : teamIds) {
            ActivityTeam team = ActivityTeamManager.getInstance().getTeamById(activityId, warZoneId, teamId, pid);
            if (team != null){
                rule = ActivityTeamService.getTeamRuleTemplates().get(team.getRuleId());
                if(rule != null && team.getMembers().size() < rule.memberCount) {
                    //人不满才可以加入
                    if(team.checkJoinCondition(joinConditions)) {
                        //加入条件满足才可以加入
                        teams.add(team);
                    }else if(teamIds.size() == 1){
                        return Text.不满足队伍加入条件;
                    }
                }
            }else if(teamIds.size() == 1){
                return Text.组队队伍不存在;
            }
        }

        for (ActivityTeam team : teams) {
            if(!team.isNeedApproval()){
                //不需要审批直接加入
                team.addMember(pid, activityId);
                return Text.没有异常;
            }
        }

        if(teamIds.size() == 1){
            //只申请了一个队伍
            ActivityTeam team = null;
            if(teams.size() == 0){
                return  Text.组队队伍不存在;
            }
            team = teams.get(0);
            if(team.getMembers().size() >= rule.memberCount){
                return Text.组队队伍满员;
            }
            if(team.getApplicants().size() >= rule.applyCount){
                return Text.组队申请人数达到上限;
            }
        }

        for(ActivityTeam team : teams){
            if(team.getApplicants().size() < rule.applyCount) {
                team.getApplicants().add(pid);

                ActivityTeamHelper.refreshAsyncRedDotNotice(team.getMembers().get(0), RedDot.teamActivityApplyRedDot.getId(), activityId);
            }
        }

        return Text.没有异常;
    }

    /**
     * 异步调用
     */
    public static List<PbActivityTeam.ActivityTeamJoinInfo> joinList(long pid, int activityId, int warZoneId){
        List<PbActivityTeam.ActivityTeamJoinInfo> joinInfos = new ArrayList<>();
        ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, warZoneId, pid);
        if(team != null && team.getApplicants().size() > 0 && team.getMembers().get(0) == pid){
            Map<Long, MiniGamePlayer> players = PlayerHelper.getMiniPlayersForMap(team.getApplicants());
            for(long memberId : team.getApplicants()){
                MiniGamePlayer miniPlayer = players.get(memberId);
                if(miniPlayer != null) {
                    PbActivityTeam.ActivityTeamJoinInfo.Builder joinInfo = PbActivityTeam.ActivityTeamJoinInfo.newBuilder()
                            .setPid(memberId)
                            .setName(miniPlayer.getName())
                            .setFightPower(miniPlayer.getFightingPower())
                            .setServerId(miniPlayer.getServerNum());
                    PbActivityTeam.ActivityTeamMember.Builder member = PbActivityTeam.ActivityTeamMember.newBuilder()
                            .setHeadId(miniPlayer.getHeadImage())
                            .setHeadFrameId(miniPlayer.getHeadFrame())
                            .setProtagonistId(PlayerRoleService.getProtagonistTemplate(miniPlayer.getProfession(), miniPlayer.getGender()).id)
                            .setLevel(miniPlayer.getLevel());
                    joinInfo.setHead(member);
                    joinInfos.add(joinInfo.build());
                }
            }
        }
        return joinInfos;
    }

    public static int joinApproval(long pid, int activityId, int warZoneId, Set<Long> pids, boolean agree) {
        ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, warZoneId, pid);
        if(team == null){
            return Text.组队队伍不存在;
        }
        if(team.getMembers().get(0) != pid){
            return Text.组队没有权限;
        }
        TeamRuleTemplate rule =  ActivityTeamService.getTeamRuleTemplates().get(team.getRuleId());
        if(rule == null){
            return Text.数据异常;
        }

        if(!agree){
            //拒绝
            for (long memberId : pids) {
                team.getApplicants().remove(memberId);
            }
        }else {
            //同意
            if (team.getMembers().size() >= rule.memberCount) {
                return Text.组队队伍满员;
            }

            for (long memberId : pids) {
                ActivityTeam playerTeam = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, warZoneId, memberId);
                if (playerTeam != null) {
                    if (pids.size() == 1) {
                        return Text.组队已在队伍中;
                    }
                } else {
                    if (team.getMembers().size() < rule.memberCount) {
                        team.addMember(memberId, activityId);
                    }
                }

                team.getApplicants().remove(memberId);
            }
        }


        return Text.没有异常;
    }

    public static Pair<Integer, Long> invitePlayer(long playerId, long targetPid, int activityId, int zoneId){
        ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, zoneId, playerId);
        if(team == null){
            return Pair.of(Text.组队队伍不存在, 0L);
        }

        if(Configuration.isGameServer()){
            //本服
            if(Player.getRealServerId(playerId) != Player.getRealServerId(targetPid)){
                return Pair.of(Text.对方处于不同活动分组, 0L);
            }
        }else{
            //world
            ActivityTemplate activityTemplate = ActivityService.getActivityTemplate(activityId);
            int[] crossNodeGroup = activityTemplate.getCrossNodeGroup();
            int targetZoneId = crossNodeGroup[1];
            if(zoneId != targetZoneId){
                return Pair.of(Text.对方处于不同活动分组, 0L);
            }
        }

        ActivityTeam targetTeam = ActivityTeamManager.getInstance().getTeamById(activityId, zoneId, -1, targetPid);
        if(targetTeam != null){
            return Pair.of(Text.组队已在队伍中, 0L);
        }


        team.getInvitedPlayers().add(targetPid);

        return Pair.of(Text.没有异常, team.getId());
    }

    public static int inviteAgree(long teamId, long pid, int activityId, int warZoneId, boolean agree) {
        ActivityTeam team = ActivityTeamManager.getInstance().getTeamById(activityId, warZoneId, teamId, -1);
        if (team == null) {
            return Text.组队队伍不存在;
        }
        if (!team.getInvitedPlayers().contains(pid)) {
            return Text.未找到邀请信息或邀请已过期;
        }
        TeamRuleTemplate rule = ActivityTeamService.getTeamRuleTemplates().get(team.getRuleId());
        if (rule == null) {
            return Text.数据异常;
        }

        if (!agree) {
            //拒绝
            team.getInvitedPlayers().remove(pid);
            return Text.没有异常;
        } else {
            //同意
            if (team.getMembers().size() >= rule.memberCount) {
                return Text.组队队伍满员;
            }

            ActivityTeam playerTeam = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, warZoneId, pid);
            if (playerTeam != null) {
                return Text.组队已在队伍中;
            } else {
                if (team.getMembers().size() < rule.memberCount) {
                    team.addMember(pid, activityId);
                }
            }

        }


        return Text.没有异常;
    }

    public static int changeJoinCondition(long pid, int activityId, int warZoneId, boolean needApproval, List<PbActivityTeam.ActivityTeamJoinCondition> joinConditions) {
        ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, warZoneId, pid);
        if(team == null){
            return Text.组队队伍不存在;
        }
        if(team.getMembers().get(0) != pid){
            return Text.组队没有权限;
        }

        team.setNeedApproval(needApproval);

        team.getJoinConditions().clear();
        if(joinConditions != null){
            for(PbActivityTeam.ActivityTeamJoinCondition joinCondition : joinConditions){
                team.getJoinConditions().add(new JoinCondition(joinCondition.getType(), joinCondition.getValue()));
            }
        }

        return Text.没有异常;
    }

    public static int quitTeam(long pid, int activityId, int warZoneId, boolean isCross) {
        ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, warZoneId, pid);
        if(team == null){
            return Text.组队队伍不存在;
        }

        if(team.getMembers().get(0) == pid){
            //解散队伍
            ActivityTeamManager.getInstance().deleteTeam(activityId, warZoneId, team);

            //推送给其他玩家
            Set<Long> pids = new HashSet<>(team.getMembers());
            pids.remove(pid);
            if(pids.size() > 0) {
                if (isCross) {
                    //跨服推送
                    Map<Integer, Set<Long>> serverMap = getPlayerIdsByServer(pids);
                    for (int serverId : serverMap.keySet()) {
                        Set<Long> serverPids = serverMap.get(serverId);
                        ServerCommandRequest req = CommandRequests.newServerCommandRequest("ActivityTeamGameCommandService.teamRelease", true);
                        TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, serverId, req, serverPids);
                    }
                } else {
                    teamReleasePush(pids);
                }
            }
        }else{
            //自己退出
            team.removeMember(pid);
        }

        return Text.没有异常;
    }

    public static int kickMember(long pid, long kickPid, int activityId, int warZoneId, boolean isCross) {
        ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, warZoneId, pid);
        if(team == null){
            return Text.组队队伍不存在;
        }

        if(team.getMembers().get(0) == pid && team.getMembers().contains(kickPid)){
            //队长才能踢人
            team.removeMember(kickPid);

            //推送给被踢玩家

            if (isCross) {
                //跨服推送
                ServerCommandRequest req = CommandRequests.newServerCommandRequest("ActivityTeamGameCommandService.kickMember", true);
                TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, Player.getRealServerId(kickPid), req);
            } else {
                kickNotice(kickPid);
            }

        }else{
            //无权限
            return Text.组队没有权限;
        }

        return Text.没有异常;
    }

    //把玩家id按照区服分开
    public static Map<Integer, Set<Long>> getPlayerIdsByServer(Set<Long> pids){
        Map<Integer, Set<Long> > serverMap = new HashMap<>();
        for(long pid : pids) {
            int serverId = Player.getRealServerId(pid);
            Set<Long> serverPids = serverMap.get(serverId);
            if(serverPids == null){
                serverPids = new HashSet<>();
                serverMap.put(serverId, serverPids);
            }
            serverPids.add(pid);
        }
        return serverMap;
    }

    public static void teamReleasePush(Set<Long> pids){
        PbProtocol.ActivityTeamReleasePushRst rst = PbProtocol.ActivityTeamReleasePushRst.newBuilder().setResult(Text.genOkServerRstInfo()).build();
        for (long pid : pids) {
            Player player = PlayerManager.getOnlinePlayer(pid);
            if(player != null){
                player.send(PtCode.ACTIVITY_TEAM_RELEASE_PUSH, rst);
            }
        }
    }

    public static void kickNotice(long pid){
        Player player = PlayerManager.getOnlinePlayer(pid);
        if(player != null) {
            PbProtocol.ActivityTeamKickPushRst.Builder builder = PbProtocol.ActivityTeamKickPushRst.newBuilder();
            builder.setResult(Text.genOkServerRstInfo());
            player.send(PtCode.ACTIVITY_TEAM_KICK_PUSH_RST, builder.build());
        }
    }

    /**
     * 构建无量秘境主界面信息
     * GS专用
     * 跨服场景下必须异步调用
     */
    public static PbProtocol.InfiniteRealmMainInfoRst.Builder genSecretRealmMainInfo(Player player, int activityId,
                  List<Long> memberIds, List<PbActivityTeam.InfiniteRealmHelpTips> helpTips, Map<Long, Integer> scoreAdd,
                                                                                   Map<Long, Integer> memberSceneIds, Map<Integer, Long> helpLeftHps){

        PbProtocol.InfiniteRealmMainInfoRst.Builder builder = PbProtocol.InfiniteRealmMainInfoRst.newBuilder();
        InfiniteRealmActivityModule module = player.getActivityModel().getActivityModule(activityId);
        InfiniteRealmActivityData data = ActivityService.getActivityData(activityId);
        if(module == null || data == null){
            builder.setResult(Text.genServerRstInfo(Text.活动暂未开启));
            return builder;
        }

        builder.setResult(Text.genOkServerRstInfo());
        if(helpTips != null && helpTips.size() > 0){
            builder.addAllHelpInfos(helpTips);
        }
        Map<Long, MiniGamePlayer> miniPlayers = PlayerHelper.getMiniPlayersForMap(memberIds);
        for(long memberId : memberIds) {
            MiniGamePlayer miniPlayer = miniPlayers.get(memberId);
            if (miniPlayer != null) {
                builder.addMembers(miniPlayer.genMinMiniUser());
            }
        }
        if(scoreAdd != null && scoreAdd.size() > 0){
            builder.putAllScoreAdd(scoreAdd);
        }
        builder.setCurSceneId(module.getCurSceneId())
//                .setPower(module.getPower())
                .setMaxPower(data.constTemplate.maxStamina);
        if(memberSceneIds != null && memberSceneIds.size() > 0){
            builder.putAllMemberScenes(memberSceneIds);
        }
        List<PbActivityTeam.InfiniteRealmEvent> events = new ArrayList<>();
        for(InfiniteRealmEvent event : module.getCurEvents().values()){
            if(!event.isHelpAsked()) {
                Long leftHp = helpLeftHps.get(event.getId());
                if (leftHp != null) {
                    builder.addEvents(event.genPb(data, leftHp));
                } else {
                    builder.addEvents(event.genPb(data));
                }
            }
        }
        if(events != null && events.size() > 0){
            builder.addAllEvents(events);
        }

        return builder;
    }

    public static InfiniteRealmPlayer secretRealmPvPMatch(ActivityTeam team, long playerId, int activityId, int zoneId){
        InfiniteRealmPlayer player = team.getSecretRealmTeamData().getMembers().get(playerId);
        List<ActivityTeam> list = ActivityTeamManager.getInstance().getAllTeams(activityId, zoneId);
        List<InfiniteRealmPlayer> toMatchPlayers = new ArrayList<>();
        for(ActivityTeam tm : list){
            if(tm != team){
                for(InfiniteRealmPlayer p : tm.getSecretRealmTeamData().getMembers().values()){
                    if(!p.isMatched() && p.getSceneId() == player.getSceneId()){
                        toMatchPlayers.add(p);
                    }
                }
            }
        }

        if(toMatchPlayers.size() == 0){
            return null;
        }

        Collections.shuffle(toMatchPlayers);
        InfiniteRealmPlayer toMatchPlayer = toMatchPlayers.get(0);
        toMatchPlayer.setMatched(true);
        return toMatchPlayer;
    }

    /**
     * GS调用
     * 更新队伍积分
     */
    public static void addPlayerScore(Player player, int activityId, int score){
        businessWithoutResponse(() -> {
            ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, ActivityTeamService.getLocalWarZoneId(), player.getPlayerId());
            if(team != null) {
                InfiniteRealmPlayer member =team.getSecretRealmTeamData().getMembers().get(player.getPlayerId());
                if(member != null){
                    member.addPlayerScore(score, team);
                }
            }
        }, player, activityId, "ActivityTeamWorldCommandService.addPlayerScore", score);
    }

    /**
     * GS调用
     * 更新队伍积分
     */
    public static void addTeamScore(Player player, int activityId, int score){
        businessWithoutResponse(() -> {
            ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, ActivityTeamService.getLocalWarZoneId(), player.getPlayerId());
            if(team != null) {
                InfiniteRealmPlayer member =team.getSecretRealmTeamData().getMembers().get(player.getPlayerId());
                if(member != null){
                    member.addTeamScore(score, team);
                }
            }
        }, player, activityId, "ActivityTeamWorldCommandService.addTeamScore", score);
    }

    /**
     * 更新求助事件的剩余血量
     */
    public static void updateHelpEventLeftHps(Player player, int activityId, int eventId, long helpSenderPid, long damage){
        businessWithoutResponse(() -> {
            updateHelpEventLeftHps(player.getPlayerId(),activityId, ActivityTeamService.getLocalWarZoneId(), helpSenderPid, eventId, damage);
        }, player, activityId, "ActivityTeamWorldCommandService.updateHelpEventLeftHps", eventId, damage, helpSenderPid);
    }


    /**
     * 更新求助事件
     *
     * 更新剩余血量
     * 更新造成的伤害量
     * 清除求助的战斗标志
     *
     * 如果PVP胜利，抢夺玩家积分并将目标踢回上一层
     */
    public static void updateHelpEventLeftHps(long atkPlayerId, int activityId, int zoneId, long helpSendPlayerId, int eventId, long damage){
        ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, zoneId, helpSendPlayerId);
        if(team != null) {
            InfiniteHelpInfo helpInfo = team.getSecretRealmTeamData().getHelpInfo(helpSendPlayerId, eventId);
            if(helpInfo != null){
                helpInfo.updateLeftHps(atkPlayerId, damage);
            }

            if(helpInfo.getTargetPlayerId() > 0 && helpInfo.getLeftHp() == 0){
                //PVP胜利
                infiniteRealmKickback(activityId, helpInfo.getTargetPlayerId(), helpInfo.getSendPid());
            }
        }
    }

    /**
     * 击退通知-GS调用
     */
    public static void fightBackNoticeGS(long pid, int sceneId, int activityId){
        Player player = PlayerManager.getOnlinePlayer(pid);
        if(player != null){
            PbProtocol.InfiniteRealmFightBackPushRst.Builder builder = PbProtocol.InfiniteRealmFightBackPushRst.newBuilder();
            builder.setResult(Text.genOkServerRstInfo());
            player.send(PtCode.INFINITE_REALM_FIGHTBACK_PUSH_RST, builder.build());

            InfiniteRealmActivityModule module = player.getActivityModel().getActivityModule(activityId);
            if(module != null) {
                module.setCurSceneId(sceneId);
            }
        }
    }

    /**
     * 击退通知-World调用
     */
    public static void fightBackNoticeWorld(long pid, int sceneId, int activityId){
        ServerCommandRequest req = CommandRequests.newServerCommandRequest("ActivityTeamGameCommandService.fightBackNotice", true);
        TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, Player.getRealServerId(pid), req, pid, sceneId, activityId);
    }


    public static void secretRealmCancelMatch(Player player, long targetPid, int activityId){
        businessWithoutResponse(()->{
            ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, ActivityTeamService.getLocalWarZoneId(), targetPid);
            if(team != null){
                InfiniteRealmPlayer targetPlayer = team.getSecretRealmTeamData().getMembers().get(targetPid);
                if(targetPlayer != null){
                    targetPlayer.setMatched(false);
                }
            }
        },player,activityId,"ActivityTeamWorldCommandService.cancelMatch", targetPid);
    }

    /**
     * PVP胜利
     * 抢夺积分并把目标踢回上一层
     */
    public static void infiniteRealmKickback(int activityId, long defPid, long eventPid){
        InfiniteRealmActivityData data = ActivityService.getActivityData(activityId);
        ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, ActivityTeamService.getLocalWarZoneId(), eventPid);
        InfiniteRealmPlayer atkPlayer = team.getSecretRealmTeamData().getMembers().get(eventPid);
        atkPlayer.addTeamScore(data.constTemplate.deductPoint, team);
        ActivityTeam defTeam = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, ActivityTeamService.getLocalWarZoneId(), defPid);
        if(defTeam != null){
            InfiniteRealmPlayer defPlayer = defTeam.getSecretRealmTeamData().getMembers().get(defPid);
            if(defPlayer != null) {
                defPlayer.addTeamScore(-data.constTemplate.deductPoint, defTeam);
                atkPlayer.addTeamScore(data.constTemplate.deductPoint, team);

                InfiniteRealmSceneTemplate sceneTemplate = data.scenes.get(defPlayer.getSceneId());
                if(sceneTemplate != null){
                    defPlayer.setSceneId(sceneTemplate.preSceneId);

                    //通知玩家被击退
                    if(Configuration.isGameServer()){
                        fightBackNoticeGS(defPlayer.getPid(), defPlayer.getSceneId(), activityId);
                    }else if(Configuration.isWorldServer()){
                        fightBackNoticeWorld(defPlayer.getPid(), defPlayer.getSceneId(), activityId);
                    }
                }
            }
        }
    }

    public static final int RANK_SIZE = 50;


    /**
     * 构造排行榜数据
     * 队伍榜单不包含对账名字
     * 个人榜单不包含mini user
     * 数据需要发送前补充
     */
    public static PbProtocol.InfiniteRealmRankInfoRst genInfiniteRanks(int activityId, int rankType, int warZoneId, long pid){
        PbProtocol.InfiniteRealmRankInfoRst.Builder b = PbProtocol.InfiniteRealmRankInfoRst.newBuilder();
        b.setRankType(rankType)
                .setResult(Text.genOkServerRstInfo());

        //个人榜
        List<InfiniteRealmPlayer> playerList = ActivityTeamManager.getInstance().getPlayerInfiniteRanks(activityId, warZoneId);
        InfiniteRealmPlayer myPlayerNode = null;
        int myPlayerRank = -1;

        //队伍榜
        List<ActivityTeam> leagueList = ActivityTeamManager.getInstance().getTeamRanks(activityId, warZoneId);
        ActivityTeam myTeamNode = null;
        int myTeamRank = -1;

        if(rankType == 1) {
            //团队榜
            for(int i = 0; i < leagueList.size() && i < RANK_SIZE; i++){
                ActivityTeam team = leagueList.get(i);
                b.addTeamNodes(team.genRankNode(i+1));

                if(team.getMembers().contains(pid)){
                    myTeamNode = team;
                    myTeamRank = i+1;
                }
            }
        }else {
            //个人榜
            for(int i = 0; i < playerList.size() && i < RANK_SIZE; i++){
                InfiniteRealmPlayer player = playerList.get(i);
                b.addPlayerNodes(player.genRankNode(i+1));

                if(player.getPid() == pid){
                    myPlayerNode = player;
                    myPlayerRank = i+1;
                }
            }
        }

        if(myTeamNode == null){
            for(int i = 0; i < leagueList.size(); i++){
                ActivityTeam team = leagueList.get(i);
                if(team.getMembers().contains(pid)){
                    myTeamNode = team;
                    myTeamRank = i+1;
                    break;
                }
            }
        }

        if(myPlayerNode == null){
            for(int i = 0; i < playerList.size(); i++){
                InfiniteRealmPlayer player = playerList.get(i);
                if(player.getPid() == pid){
                    myPlayerNode = player;
                    myPlayerRank = i+1;
                    break;
                }
            }
        }

        //每次都需要个人和队伍数据
        if(myTeamNode != null){
            b.setMyTeamNode(myTeamNode.genRankNode(myTeamRank));
        }
        if(myPlayerNode != null){
            b.setMyPlayerNode(myPlayerNode.genRankNode(myPlayerRank));
        }

        return b.build();
    }

    /**
     * 排行榜奖励预领取处理
     * 1. 检查是否领取过
     * 2. 标记已领取，返回排名
     */
    public static Pair<Integer,Integer> infiniteRankRewardCheck(int activityId, int rankType, int warZoneId, long pid){
        ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, warZoneId, pid);
        InfiniteRealmPlayer player = team.getSecretRealmTeamData().getMembers().get(pid);
        if(player == null){
            return Pair.of(Text.参数异常, 0);
        }

        if(player.getRankRewardGetRecord().containsKey(rankType) && player.getRankRewardGetRecord().get(rankType)){
            return Pair.of(Text.奖励已经领取过, 0);
        }

        int rank = -1;
        if(rankType == 1){
            //团队
            List<ActivityTeam> list = ActivityTeamManager.getInstance().getTeamRanks(activityId,warZoneId);
            for(int i = 0; i < list.size(); i++){
                ActivityTeam t = list.get(i);
                if(t.getMembers().contains(pid)){
                    rank = i + 1;
                    break;
                }
            }
        }else{
            //个人
            List<InfiniteRealmPlayer> list = ActivityTeamManager.getInstance().getPlayerInfiniteRanks(activityId,warZoneId);
            for(int i = 0; i < list.size(); i++){
                InfiniteRealmPlayer p = list.get(i);
                if(p.getPid() == pid){
                    rank = i + 1;
                    break;
                }
            }
        }

        player.getRankRewardGetRecord().put(rankType, true);
        return Pair.of(Text.没有异常, rank);
    }

    public static void notifyRankReward(int activityId, Map<Long, Integer> teamRank, Map<Long, Integer> soloRank){
        InfiniteRealmActivityData data = ActivityService.getActivityData(activityId);
        if(CollectionUtil.isNotEmpty(teamRank)){
            for(Map.Entry<Long, Integer> entry : teamRank.entrySet()){
                long pid = entry.getKey();
                int rank = entry.getValue();
                Map.Entry<Integer, InfiniteRealmRankRewardTemplate> rankData= data.teamRanks.ceilingEntry(rank);
                if(rankData != null){
                    InfiniteRealmRankRewardTemplate template = rankData.getValue();
                    MailType mailType = MailType.infiniteRealmTeamRankReward;
                    PbCommons.PbText title = Text.genText(mailType.getTitleId()).build();
                    PbCommons.PbText content = Text.genText(mailType.getContentId(), new TextParamText(String.valueOf(rank)))
                            .build();
                    MailManager.sendMail(
                            mailType,
                            pid,
                            title,
                            content,
                            ServerConstants.getCurrentTimeMillis(),
                            Reward.templateCollectionToReward(template.rewards));
                }
            }
        }

        if(CollectionUtil.isNotEmpty(soloRank)){
            for(Map.Entry<Long, Integer> entry : soloRank.entrySet()){
                long pid = entry.getKey();
                int rank = entry.getValue();
                Map.Entry<Integer, InfiniteRealmRankRewardTemplate> rankData= data.playerRanks.ceilingEntry(rank);
                if(rankData != null){
                    InfiniteRealmRankRewardTemplate template = rankData.getValue();
                    MailType mailType = MailType.infiniteRealmPlayerRankReward;
                    PbCommons.PbText title = Text.genText(mailType.getTitleId()).build();
                    PbCommons.PbText content = Text.genText(mailType.getContentId(), new TextParamText(String.valueOf(rank)))
                            .build();
                    MailManager.sendMail(
                            mailType,
                            pid,
                            title,
                            content,
                            ServerConstants.getCurrentTimeMillis(),
                            Reward.templateCollectionToReward(template.rewards));
                }
            }
        }
    }

    /**
     * 通知发奖
     */
    public static void rankRewardNotify(InfiniteRealmActivityData data, int activityId, int groupId){
        int soloRankCount = data.playerRanks.floorEntry(Integer.MAX_VALUE).getValue().rank;
        int teamRankCount = data.teamRanks.floorEntry(Integer.MAX_VALUE).getValue().rank;
        List<ActivityTeam> teams = ActivityTeamManager.getInstance().getTeamRanks(activityId, groupId);
        Map<Integer,Map<Long, Integer>> teamRanks = new HashMap<>();
        if(CollectionUtil.isNotEmpty(teams)) {
            for (int i = 0; i < teamRankCount && i < teams.size(); i++) {
                ActivityTeam team = teams.get(i);
                for (Long memberId : team.getMembers()) {
                    Map<Long, Integer> map = teamRanks.get(Player.getRealServerId(memberId));
                    if(map == null){
                        map = new HashMap<>();
                        teamRanks.put(Player.getRealServerId(memberId), map);
                    }
                    map.put(memberId, i + 1);
                }
            }
        }
        Map<Integer,Map<Long, Integer>> playerRanks = new HashMap<>();
        List<InfiniteRealmPlayer> players = ActivityTeamManager.getInstance().getPlayerInfiniteRanks(activityId, groupId);
        if(CollectionUtil.isNotEmpty(players)){
            for(int i = 0; i < soloRankCount && i < players.size(); i++){
                InfiniteRealmPlayer player = players.get(i);
                Map<Long, Integer> map = playerRanks.get(Player.getRealServerId(player.getPid()));
                if(map == null){
                    map = new HashMap<>();
                    playerRanks.put(Player.getRealServerId(player.getPid()), map);
                }
                map.put(player.getPid(), i + 1);
            }
        }

        if(Configuration.isGameServer()){
            //game本地运行
            ActivityTeamHelper.notifyRankReward(activityId, teamRanks.get(Configuration.serverId), playerRanks.get(Configuration.serverId));
        }else {
            Set<Integer> sids = new HashSet<>();
            sids.addAll(teamRanks.keySet());
            sids.addAll(playerRanks.keySet());

            for (int sid : sids) {
                ServerCommandRequest request = CommandRequests.newServerCommandRequest("ActivityTeamGameCommandService.notifyRankReward", false);
                Map<Long, Integer> teamData = teamRanks.get(sid);
                Map<Long, Integer> playerData = playerRanks.get(sid);
                if (teamData == null) {
                    teamData = new HashMap<>();
                }
                if (playerData == null) {
                    playerData = new HashMap<>();
                }
                TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, sid, request, activityId, teamData, playerData);
            }
        }

    }

    public static void helpPush(InfiniteHelpInfo info, InfiniteRealmTeamData teamData) {
        Set<Long> pids = new HashSet<>();
        pids.addAll(teamData.getMembers().keySet());
        pids.remove(info.getSendPid());
        if (CollectionUtil.isNotEmpty(pids)) {
            if (Configuration.isGameServer()) {
                //game本地运行
                PbProtocol.InfiniteRealmHelpPushRst.Builder builder = PbProtocol.InfiniteRealmHelpPushRst.newBuilder();
                builder.setResult(Text.genOkServerRstInfo());
                MiniGamePlayer sender = PlayerManager.getMiniPlayer(info.getSendPid());
                if (sender != null) {
                    builder.setHelpAskUser(sender.genMinMiniUser());
                    builder.setHelpId(info.getId());
                    for (long pid : pids) {
                        Player player = PlayerManager.getOnlinePlayer(pid);
                        if (player != null) {
                            player.send(PtCode.INFINITE_REALM_HELP_PUSH_RST, builder.build());
                        }
                    }
                }
            } else {
                //world服务器运行
                ThreadPool.execute(()->{
                    MiniGamePlayer sender = PlayerManager.getMiniPlayer(info.getSendPid());
                    if (sender != null) {
                        ServerCommandRequest request = CommandRequests.newServerCommandRequest("ActivityTeamGameCommandService.helpPush", true);
                        for (long pid : pids) {
                            TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, Player.getRealServerId(pid), request, pid, info.getId(), sender.genMinMiniUser());
                        }
                    }
                });

            }
        }
    }


    /**
     * 主界面信息
     * @param activityId 活动id
     * @param player 玩家信息
     * @param memberIds 成员列表
     * @param floorInfo 层数信息
     * @return
     */
    public static PbProtocol.SutraPavilionMainRst spMainInfo(int activityId, Player player, List<Long> memberIds
            , PbActivityTeam.SutraPavilionFloorInfo floorInfo){
        PbProtocol.SutraPavilionMainRst.Builder rst = PbProtocol.SutraPavilionMainRst.newBuilder().setResult(Text.genOkServerRstInfo());
        SutraPavilionActivityModule module = player.getActivityModel().getActivityModule(activityId);
        SutraPavilionActivityData data = ActivityService.getActivityData(activityId);
        logic:{
            if(module == null || data == null){
                rst.setResult(Text.genServerRstInfo(Text.活动暂未开启));
                break logic;
            }
            for (Long memberId : memberIds) {
                MiniGamePlayer miniPlayer = PlayerHelper.getMiniPlayer(memberId);
                if(Objects.nonNull(miniPlayer)){
                    rst.addMembers(miniPlayer.genMinMiniUser());
                }
            }
            rst.setFloorInfo(floorInfo)
                    .setPower(module.getCurPower())
                    .setMaxPower(data.getConstTemplate().rewardview);

        }

        return rst.build();
    }

    /**
     * 夜探藏经阁-解锁地块
     * @param activityId 活动id
     * @param warZoneId 战区id
     * @param playerId 玩家id
     * @param nodeId 地块id
     */
    public static PbProtocol.SutraPavilionUnlockNodeRst spUnlockNode(int activityId, int warZoneId
            , long playerId, int nodeId){
        PbProtocol.SutraPavilionUnlockNodeRst.Builder rst = PbProtocol.SutraPavilionUnlockNodeRst.newBuilder();
        ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, warZoneId, playerId);
        logic:{
            if(team == null || team.getSpData() == null){
                rst.setResult(Text.genServerRstInfo(Text.组队队伍不存在));
                break logic;
            }
            SutraPavilionTeamData spData = team.getSpData();
            SutraPavilionMemberInfo memberInfo = spData.getMemberInfo(playerId);
            if(Objects.isNull(memberInfo)){
                rst.setResult(Text.genServerRstInfo(Text.组队你不在队伍));
                break logic;
            }
            int floorId = memberInfo.getFloorId();
            SutraPavilionFloorInfo floorInfo = spData.getFloorInfo(floorId);
            if(!floorInfo.getNodeInfoMap().containsKey(nodeId)){
                rst.setResult(Text.genServerRstInfo(Text.双人组队_夜探藏经阁_地块不存在));
                break logic;
            }
            int simpleNodeId = AbsSPEvent.getSimpleNodeId(activityId);
            for (SutraPavilionNodeInfo nodeInfo : floorInfo.getNodeInfoMap().values()) {
                if(nodeInfo.getStatus(SutraPavilionNodeInfo.had_unlock) && nodeInfo.getEventId() != simpleNodeId){
                    rst.setResult(Text.genServerRstInfo(Text.双人组队_夜探藏经阁_地块存在事件不能继续解锁));
                    break logic;
                }
            }
            SutraPavilionNodeInfo nodeInfo = floorInfo.getNodeInfoMap().get(nodeId);
            if(nodeInfo.getStatus(SutraPavilionNodeInfo.had_unlock)){
                rst.setResult(Text.genServerRstInfo(Text.双人组队_夜探藏经阁_地块已经解锁));
                break logic;
            }
            Map<Integer, SutraPavilionNodeInfo> nodeInfoMap = floorInfo.getNodeInfoMap();
            if(!floorInfo.getNodeInfoMap().containsKey(memberInfo.getNodeId())
                || !SutraPavilionHelper.checkIsNear(nodeInfoMap.get(memberInfo.getNodeId()), nodeInfo)){
                rst.setResult(Text.genServerRstInfo(Text.双人组队_夜探藏经阁_不在地块附近));
                break logic;
            }
            //检查保底，可能会替换事件
            TheSutraRepositoryFloorTemplate floorTemplate = floorInfo.getFloorTemplate(activityId);
            SutraPavilionActivityData data = ActivityService.getActivityData(activityId);
            List<TheSutraRepositoryIncidentGroupTemplate> groupTemplates = data.getEventGroupTemplates().get(floorTemplate.incidentGroup);
            Map<Integer, Integer> eventAndTimeInfoTemps = new HashMap<>();
            for (TheSutraRepositoryIncidentGroupTemplate groupTemplate : groupTemplates) {
                if(groupTemplate.minimumGuarantee > 0){
                    eventAndTimeInfoTemps.put(groupTemplate.eventId, groupTemplate.minimumGuarantee);
                }
            }
            //当前地图事件保底情况
            //eventId_times
            Map<Integer, Integer> eventAndTimeInfos = new HashMap<>();
            for (SutraPavilionNodeInfo targetNodeInfo : nodeInfoMap.values()) {
                if(!targetNodeInfo.getStatus(SutraPavilionNodeInfo.had_unlock) && eventAndTimeInfoTemps.containsKey(targetNodeInfo.getEventId())){
                    eventAndTimeInfos.put(targetNodeInfo.getEventId(), eventAndTimeInfoTemps.get(targetNodeInfo.getEventId()));
                }
            }
            if(!eventAndTimeInfos.containsKey(nodeInfo.getEventId())){
                //没有触发到事件,检测是否可以触发事件
                int canReplaceEventId = -1;
                for (Map.Entry<Integer, Integer> eventTimesInfos : eventAndTimeInfos.entrySet()) {
                    //触发保底，并且当前事件不是怪物
                    if(memberInfo.getUnlockTimes() >= eventTimesInfos.getValue() && nodeInfo.getEventTemplate(activityId).eventType != SpEventType.monster){
                        canReplaceEventId = eventTimesInfos.getKey();
                        break;
                    }
                }
                if(canReplaceEventId > 0){
                    //替换事件
                    for (SutraPavilionNodeInfo targetNodeInfo : nodeInfoMap.values()) {
                        if(!targetNodeInfo.getStatus(SutraPavilionNodeInfo.had_unlock) && targetNodeInfo.getEventId() == canReplaceEventId){
                            //交换事件
                            targetNodeInfo.setEventId(nodeInfo.getEventId());
                            nodeInfo.setEventId(canReplaceEventId);
                            break ;
                        }
                    }
                    memberInfo.setUnlockTimes(0);
                }else{
                    memberInfo.addUnlockTimes();
                }
            }else{
                memberInfo.addUnlockTimes();
            }
            //解锁地块
            TheSutraRepositoryIncidentTemplate eventTemplate = nodeInfo.getEventTemplate(activityId);
            rst.setFloorId(memberInfo.getFloorId());
            PbProtocol.SutraPavilionUnlockNodeRst build = rst.build();
            eventTemplate.eventType.unlock(team, activityId, spData, memberInfo, nodeInfo, build);
            //同步地块解锁消息
            if(eventTemplate.eventType != SpEventType.monster
                && eventTemplate.eventType != SpEventType.mistDisperses
                    && eventTemplate.eventType != SpEventType.randomMistDisperses){
                spData.sync(floorId, 1, nodeInfo);
            }

            //增加日志
            if(build.getResult().getResult()) {
                SutraPavilionLogInfo logInfo = new SutraPavilionLogInfo();
                logInfo.setPlayerId(memberInfo.getMemberId());
                logInfo.setOperation(eventTemplate.incidentId);
                logInfo.setFloorId(memberInfo.getFloorId());
                logInfo.setRewardDbList(Reward.genDbs2(build.getRewardListList()));
                spData.getLogInfos().add(logInfo);
            }
            return build;
        }
        return rst.build();
    }

    public static PbProtocol.SutraPavilionTriggerNodeRst spTriggerNode(int activityId, int warZoneId
            , long playerId, int nodeId, int targetNodeId){
        PbProtocol.SutraPavilionTriggerNodeRst.Builder rst = PbProtocol.SutraPavilionTriggerNodeRst.newBuilder().setResult(Text.genOkServerRstInfo());
        ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, warZoneId, playerId);
        logic:
        {
            if (team == null || team.getSpData() == null) {
                rst.setResult(Text.genServerRstInfo(Text.组队队伍不存在));
                break logic;
            }
            SutraPavilionTeamData spData = team.getSpData();
            SutraPavilionMemberInfo memberInfo = spData.getMemberInfo(playerId);
            if (Objects.isNull(memberInfo)) {
                rst.setResult(Text.genServerRstInfo(Text.组队你不在队伍));
                break logic;
            }
            int floorId = memberInfo.getFloorId();
            SutraPavilionFloorInfo floorInfo = spData.getFloorInfo(floorId);
            if (!floorInfo.getNodeInfoMap().containsKey(nodeId)
                || (targetNodeId > 0 && !floorInfo.getNodeInfoMap().containsKey(targetNodeId))) {
                rst.setResult(Text.genServerRstInfo(Text.双人组队_夜探藏经阁_地块不存在));
                break logic;
            }

            SutraPavilionNodeInfo nodeInfo = floorInfo.getNodeInfoMap().get(nodeId);
            if (!nodeInfo.getStatus(SutraPavilionNodeInfo.had_unlock)) {
                rst.setResult(Text.genServerRstInfo(Text.双人组队_夜探藏经阁_地块没有解锁));
                break logic;
            }

            if (!floorInfo.getNodeInfoMap().containsKey(memberInfo.getNodeId())
                    || !SutraPavilionHelper.checkIsNear(floorInfo.getNodeInfoMap().get(memberInfo.getNodeId()), nodeInfo)) {
                rst.setResult(Text.genServerRstInfo(Text.双人组队_夜探藏经阁_不在地块附近));
                break logic;
            }
            //触发地块事件
            TheSutraRepositoryIncidentTemplate eventTemplate = nodeInfo.getEventTemplate(activityId);
            rst.setNewFloorId(memberInfo.getFloorId());
            PbProtocol.SutraPavilionTriggerNodeRst build = rst.build();
            eventTemplate.eventType.tryTriggerEvent(activityId, team, spData, memberInfo, nodeInfo, build, targetNodeId);
            //增加日志
            if(build.getResult().getResult()) {
                SutraPavilionLogInfo logInfo = new SutraPavilionLogInfo();
                logInfo.setPlayerId(memberInfo.getMemberId());
                logInfo.setOperation(eventTemplate.incidentId);
                logInfo.setFloorId(memberInfo.getFloorId());
                logInfo.setRewardDbList(Reward.genDbs2(build.getRewardListList()));
                spData.getLogInfos().add(logInfo);
            }
            return build;
        }


        return rst.build();
    }

    public static List<Reward> spAddReward(Player player, int activityId
            , List<PbCommons.PbReward> rewards, int rechargeId, int addedPower, BehaviorType behaviorType
            , int newFloorId){
        List<Reward> rewardList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(rewards)){
            rewardList.addAll(Reward.readFromPb(rewards));
        }
        if(rechargeId > 0){
            ActivityTemplate activityTemplate = ActivityService.getActivityTemplate(activityId);
            player.getPlayerGiftPackageModel().addPackage(rechargeId, activityTemplate);
        }
        SutraPavilionActivityModule module = player.getActivityModel().getActivityModule(activityId);
        if(addedPower > 0){
            module.setCurPower(module.getCurPower() + addedPower);
        }
        module.setFloorId(newFloorId);
        if(CollectionUtil.isNotEmpty(rewardList)){
            Reward.merge(rewardList);
            Reward.add(rewardList, player, behaviorType);
        }
        return rewardList;
    }

    public static PbProtocol.SutraPavilionMoveRst spMove(int activityId, int warZoneId
            , long playerId, int nodeId){
        PbProtocol.SutraPavilionMoveRst.Builder rst = PbProtocol.SutraPavilionMoveRst.newBuilder().setResult(Text.genOkServerRstInfo());
        ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, warZoneId, playerId);
        logic:
        {
            if (team == null || team.getSpData() == null) {
                rst.setResult(Text.genServerRstInfo(Text.组队队伍不存在));
                break logic;
            }
            SutraPavilionTeamData spData = team.getSpData();
            SutraPavilionMemberInfo memberInfo = spData.getMemberInfo(playerId);
            if (Objects.isNull(memberInfo)) {
                rst.setResult(Text.genServerRstInfo(Text.组队你不在队伍));
                break logic;
            }
            if(memberInfo.getNodeId() == nodeId){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            int floorId = memberInfo.getFloorId();
            SutraPavilionFloorInfo floorInfo = spData.getFloorInfo(floorId);
            if (!floorInfo.getNodeInfoMap().containsKey(nodeId)) {
                rst.setResult(Text.genServerRstInfo(Text.双人组队_夜探藏经阁_地块不存在));
                break logic;
            }

            SutraPavilionNodeInfo nodeInfo = floorInfo.getNodeInfoMap().get(nodeId);
            if (!nodeInfo.getStatus(SutraPavilionNodeInfo.had_unlock)) {
                rst.setResult(Text.genServerRstInfo(Text.双人组队_夜探藏经阁_地块没有解锁));
                break logic;
            }
            Pair<Integer, Integer> targetPoint = SutraPavilionNodeInfo.calXY(nodeId);
            List<Point> obstacles = new ArrayList<>();
            for (SutraPavilionNodeInfo targetNodeInfo : floorInfo.getNodeInfoMap().values()) {
                //未解锁或者障碍物事件 都为障碍物
                if(!targetNodeInfo.getStatus(SutraPavilionNodeInfo.had_unlock)
                    || targetNodeInfo.getEventTemplate(activityId).eventType == SpEventType.obstacle){
                    obstacles.add(Point.of(targetNodeInfo.getX(), targetNodeInfo.getY()));
                }
            }
            TheSutraRepositoryFloorTemplate floorTemplate = floorInfo.getFloorTemplate(activityId);
            //检查能否移动
            boolean canMove = ReachFinder.fbsFind(Point.of(memberInfo.getX(), memberInfo.getY()), Point.of(targetPoint.getKey(), targetPoint.getKey()), obstacles, floorTemplate.length);
            if(!canMove){
                rst.setResult(Text.genServerRstInfo(Text.双人组队_夜探藏经阁_移动失败));
                break logic;
            }
            memberInfo.setX(targetPoint.getKey());
            memberInfo.setY(targetPoint.getValue());

            spData.sync(floorId, 2, memberInfo);
        }

        return rst.build();
    }

    public static PbProtocol.SutraPavilionAssistRst spAssist(int activityId, int warZoneId, long playerId){
        PbProtocol.SutraPavilionAssistRst.Builder rst = PbProtocol.SutraPavilionAssistRst.newBuilder().setResult(Text.genOkServerRstInfo());
        ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, warZoneId, playerId);
        logic:
        {
            if (team == null || team.getSpData() == null) {
                rst.setResult(Text.genServerRstInfo(Text.组队队伍不存在));
                break logic;
            }
            SutraPavilionTeamData spData = team.getSpData();
            SutraPavilionMemberInfo memberInfo = spData.getMemberInfo(playerId);
            if (Objects.isNull(memberInfo)) {
                rst.setResult(Text.genServerRstInfo(Text.组队你不在队伍));
                break logic;
            }
            //协助信息
            for (SutraPavilionAssistInfo assistInfo : spData.getAssistInfos()) {
                rst.addAssistInfos(assistInfo.genPb());
            }

        }
        return rst.build();
    }

    public static PbProtocol.SutraPavilionLogRst spLog(int activityId, int warZoneId, long playerId){
        PbProtocol.SutraPavilionLogRst.Builder rst = PbProtocol.SutraPavilionLogRst.newBuilder().setResult(Text.genOkServerRstInfo());
        ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, warZoneId, playerId);
        logic:
        {
            if (team == null || team.getSpData() == null) {
                rst.setResult(Text.genServerRstInfo(Text.组队队伍不存在));
                break logic;
            }
            SutraPavilionTeamData spData = team.getSpData();
            SutraPavilionMemberInfo memberInfo = spData.getMemberInfo(playerId);
            if (Objects.isNull(memberInfo)) {
                rst.setResult(Text.genServerRstInfo(Text.组队你不在队伍));
                break logic;
            }
            //日志信息
            for (SutraPavilionLogInfo logInfo : spData.getLogInfos()) {
                rst.addLogInfos(logInfo.genPb());
            }
        }


        return rst.build();
    }

    public static PbProtocol.SutraPavilionHelpMemberRst spHelpMember(int activityId, int warZoneId
            , long playerId, int index){
        PbProtocol.SutraPavilionHelpMemberRst.Builder rst = PbProtocol.SutraPavilionHelpMemberRst.newBuilder().setResult(Text.genOkServerRstInfo());
        ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, warZoneId, playerId);
        logic:
        {
            if (team == null || team.getSpData() == null) {
                rst.setResult(Text.genServerRstInfo(Text.组队队伍不存在));
                break logic;
            }
            SutraPavilionTeamData spData = team.getSpData();
            SutraPavilionMemberInfo memberInfo = spData.getMemberInfo(playerId);
            if (Objects.isNull(memberInfo)) {
                rst.setResult(Text.genServerRstInfo(Text.组队你不在队伍));
                break logic;
            }
            if(spData.getAssistInfos().size() >= index){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            SutraPavilionAssistInfo assistInfo = spData.getAssistInfos().get(index);
            if(assistInfo.getFindPlayerId() == playerId){
                rst.setResult(Text.genServerRstInfo(Text.双人组队_夜探藏经阁_不能协助自己));
                break logic;
            }

            //再次战斗
            rst.setCombatEventId(assistInfo.getEventId());
            rst.putAllSurplusHps(assistInfo.getSurplusHp());
        }


        return rst.build();
    }

    public static void addSpAssistInfo(int activityId, int warZoneId
            , long playerId, int floorId, int eventId, Map<Integer, Long> surplusHps){
        ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(
                activityId, warZoneId, playerId);
        if (team == null || team.getSpData() == null) {
            return;
        }

        SutraPavilionTeamData spData = team.getSpData();
        SutraPavilionMemberInfo memberInfo = spData.getMemberInfo(playerId);
        if(memberInfo == null){
            return;
        }
        SutraPavilionAssistInfo assistInfo = new SutraPavilionAssistInfo();
        assistInfo.setFloorId(floorId);
        assistInfo.setNotify(false);
        assistInfo.setFindPlayerId(playerId);
        assistInfo.setSurplusHp(surplusHps);
        assistInfo.setEventId(eventId);

        spData.getAssistInfos().add(assistInfo);
    }

    public static PbProtocol.SutraPavilionRankRst spRankInfo(int activityId, int warZoneId, long playerId, int type){
        PbProtocol.SutraPavilionRankRst.Builder rst = PbProtocol.SutraPavilionRankRst.newBuilder().setResult(Text.genOkServerRstInfo());
        ActivityTeam team = ActivityTeamManager.getInstance().getTeamByPlayerId(activityId, warZoneId, playerId);
        logic:
        {
            if (team == null || team.getSpData() == null) {
                rst.setResult(Text.genServerRstInfo(Text.组队队伍不存在));
                break logic;
            }
            SutraPavilionTeamData spData = team.getSpData();
            SutraPavilionMemberInfo memberInfo = spData.getMemberInfo(playerId);
            if (Objects.isNull(memberInfo)) {
                rst.setResult(Text.genServerRstInfo(Text.组队你不在队伍));
                break logic;
            }
            if(type == 1){
                //个人
                List<SutraPavilionMemberInfo> ranks = ActivityTeamManager.getInstance().getSpPlayerRanks(activityId, warZoneId);
                int rankCount = Math.min(ranks.size(), RANK_SIZE);
                for (int i = 0; i < rankCount; i++) {
                    SutraPavilionMemberInfo rankMemberInfo = ranks.get(i);
                    rst.addPlayerRankInfos(rankMemberInfo.genSpRankInfo(i + 1));
                }
                int spPlayerRank = ActivityTeamManager.getInstance().getSpPlayerRank(activityId, warZoneId, playerId);
                rst.setMyPlayerRankInfo(memberInfo.genSpRankInfo(spPlayerRank));
            }else{
                List<ActivityTeam> ranks = ActivityTeamManager.getInstance().getTeamSpRanks(activityId, warZoneId);
                int rankCount = Math.min(ranks.size(), RANK_SIZE);
                for (int i = 0; i < rankCount; i++) {
                    ActivityTeam activityTeam = ranks.get(i);
                    rst.addTeamRankInfos(activityTeam.genSpRankInfo(i + 1));
                }
                int spTeamRank = ActivityTeamManager.getInstance().getSpTeamRank(activityId, warZoneId, team.getId());
                rst.setMyTeamRankInfo(team.genSpRankInfo(spTeamRank));
            }
            rst.setRankType(type);
        }
        return rst.build();
    }

    public static void spSettlement(int activityId, int zoneId, boolean isCross){
        List<ActivityTeam> teamRanks = ActivityTeamManager.getInstance().getTeamSpRanks(activityId, zoneId);
        //serverId_playerId_teamRank
        Map<Integer, Map<Long, Integer>> teamRankInfos = new HashMap<>();
        if(CollectionUtil.isNotEmpty(teamRanks)){
            for (int i = 0; i < teamRanks.size(); i++) {
                ActivityTeam activityTeam = teamRanks.get(i);
                int rank = i + 1;
                for (Long memberId : activityTeam.getMembers()) {
                    int serverId = Player.getRealServerId(memberId);
                    if(!teamRankInfos.containsKey(serverId)){
                        teamRankInfos.put(serverId, new HashMap<>());
                    }
                    teamRankInfos.get(serverId).put(memberId, rank);
                }
            }
        }
        //serverId_playerId_playerRank
        Map<Integer, Map<Long, Integer>> playerRankInfos = new HashMap<>();
        List<SutraPavilionMemberInfo> playerRanks = ActivityTeamManager.getInstance().getSpPlayerRanks(activityId, zoneId);
        if(CollectionUtil.isNotEmpty(playerRanks)){
            for (int i = 0; i < playerRanks.size(); i++) {
                int rank = i + 1;
                SutraPavilionMemberInfo sutraPavilionMemberInfo = playerRanks.get(i);
                long memberId = sutraPavilionMemberInfo.getMemberId();
                int serverId = Player.getRealServerId(memberId);
                if(!playerRankInfos.containsKey(serverId)){
                    playerRankInfos.put(serverId, new HashMap<>());
                }
                playerRankInfos.get(serverId).put(memberId, rank);
            }
        }
        if(isCross){
            Set<Integer> serverIds = new HashSet<>();
            serverIds.addAll(playerRankInfos.keySet());
            serverIds.addAll(teamRankInfos.keySet());
            for (Integer serverId : serverIds) {
                ServerCommandRequest req = CommandRequests.newServerCommandRequest("ActivityTeamGameCommandService.addSPRankGlobalMail", true);
                TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, serverId, req, activityId, teamRankInfos.getOrDefault(serverId, new HashMap<>()), playerRankInfos.getOrDefault(serverId, new HashMap<>()));
            }
        }else{
            //直接添加global邮件
            addSPRankGlobalMail(activityId, teamRankInfos.get(Configuration.serverId), playerRankInfos.get(Configuration.serverId));
        }

    }

    /**
     * 增加global结算邮件奖励（需要存储奖励信息，担心活动过期，拿不到数据）
     * @param activityId 活动id
     * @param teamRankInfos 队伍排名信息
     * @param playerRankInfos 个人排名信息
     */
    public static void addSPRankGlobalMail(int activityId, Map<Long, Integer> teamRankInfos, Map<Long, Integer> playerRankInfos){
        SutraPavilionActivityData activityData = ActivityService.getActivityData(activityId);
        if(CollectionUtil.isNotEmpty(teamRankInfos)){
            Map<Integer, TheSutraRepositoryRewardTemplate> teamRankRewardMap = activityData.getRewardTemplates().get(2);
            Map<String, List<RewardTemplate>> rewardInfos = new HashMap<>();
            for (TheSutraRepositoryRewardTemplate rewardTemplate : teamRankRewardMap.values()) {
                rewardInfos.put(rewardTemplate.rewardCondition, rewardTemplate.rewardGroup);
            }
            MailHelper.addGlobalMail(GlobalMailType.spActivityTeamRank.build(activityId, teamRankInfos, rewardInfos));
        }
        if(CollectionUtil.isNotEmpty(playerRankInfos)){
            Map<Integer, TheSutraRepositoryRewardTemplate> playerRankRewardMap = activityData.getRewardTemplates().get(1);
            Map<String, List<RewardTemplate>> rewardInfos = new HashMap<>();
            for (TheSutraRepositoryRewardTemplate rewardTemplate : playerRankRewardMap.values()) {
                rewardInfos.put(rewardTemplate.rewardCondition, rewardTemplate.rewardGroup);
            }
            MailHelper.addGlobalMail(GlobalMailType.spActivityPlayerRank.build(activityId, teamRankInfos, rewardInfos));
        }
    }


    private static class BusinessCallbackTask extends TLMessageCallbackTask {
        private final Consumer<CallbackResponse> responseConsumer;
        private final Consumer<Integer> errorConsumer;

        public BusinessCallbackTask(Consumer<CallbackResponse> responseConsumer, Consumer<Integer> errorConsumer) {
            this.responseConsumer = responseConsumer;
            this.errorConsumer = errorConsumer;
        }

        @Override
        public void complete(CallbackResponse response) {
            responseConsumer.accept(response);
        }

        @Override
        public void timeout() {
            errorConsumer.accept(Text.跨服调用超时);
        }
    }

    /**
     * 刷新异步红点通知
     * 非活动红点，activityId为0
     */
    public static void refreshAsyncRedDotNotice(long pid, int redDotId, int activityId){
        if(Player.getRealServerId(pid) == Configuration.serverId && Configuration.serverType == com.gy.server.core.ServerType.game){
            Player player = PlayerManager.getOnlinePlayer(pid);
            if(player == null){
                return;
            }
            RedDot redDot = RedDot.getRedDotById(redDotId);
            if(redDot != null) {
                if (activityId > 0) {
                    //活动红点
                    ActivityModule module = player.getActivityModel().getActivityModule(activityId);
                    if (module != null) {
                        redDot.asyncSync(player, module);
                    }
                } else {
                    //功能红点
                    redDot.sync(player);
                }
            }
        }else{
            //跨服调用
            CommandRequest req = CommandRequests.newServerCommandRequest("ActivityTeamGameCommandService.refreshAsyncRedDotNotice", true);
            TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, Player.getRealServerId(pid), req, pid, redDotId, activityId);
        }
    }

    public static void clearRedisData(int activityId) throws InterruptedException {
        if(!Configuration.runMode.isTest()){
            //仅限测试模式
            return;
        }

        ActivityTemplate activityTemplate = ActivityService.getActivityTemplate(activityId);
        if(activityTemplate != null &&activityTemplate.isOpenNow()){
            //活动进行中，无法删除数据
            SystemLogger.error("活动进行中，无法删除数据, 请先调整到活动关闭时间. Cannot delete data while activity is running.");
        }else{
            Set<String> keys = new HashSet<>();
            if(activityTemplate != null && activityTemplate.crossType == ActivityTemplate.CrossType.local) {
                keys.addAll(TLBase.getInstance().getRedisAssistant().getRedisKeys("ACT_TEAM_DATA_" + activityId + "_" + -Configuration.serverId + "_*", 100));
            }else{
                keys.addAll(TLBase.getInstance().getRedisAssistant().getRedisKeys("ACT_TEAM_DATA_" + activityId + "_*", 100));
                keys.addAll(TLBase.getInstance().getRedisAssistant().getRedisKeys("ACT_TEAM_ID_" + activityId + "_*", 100));
            }

            TLBase.getInstance().getRedisAssistant().delAsync(keys.toArray(new String[0]));
            SystemLogger.info("清理活动数据成功. Clear activity data successfully. activityId: " + activityId + keys);

            for(int i = 0; i < 10; i++) {
                SystemLogger.warn("即将强制关服，请手动重启... " + (10 - i));
                try {
                    Thread.sleep(1000);
                }catch (InterruptedException e){}
            }
            System.exit(0);
        }
    }

    public static int getOpenActivityID(Player player){
        List<ActivityModule> modules = player.getActivityModel().getActivityModulesByType(ActivityType.infiniteRealm);
        if (CollectionUtil.isNotEmpty(modules)) {
            //同一个玩家不能同时多开
            ActivityModule module = modules.get(0);
            return module.getActivityId();
        }
        return -1;
    }
}

package com.gy.server.world.activityTeam.bean;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.gy.server.game.activity.data.teamTreasureHunt.TeamTreasureActivityData;
import com.gy.server.packet.PbActivityTeam;

import java.util.HashMap;
import java.util.Map;

/**
 * 圣兽寻宝队伍扩展数据
 */
public class TeamTreasureHuntTeamData {

    @Protobuf(order = 1)
    private Map<Long, TeamTreasureHuntPlayer> playerMap = new HashMap<>();
    /**
     * 选择的次数
     */
    @Protobuf(order = 2)
    private int selectCount = 1;
    /**
     * 选择的宝藏类型
     */
    @Protobuf(order = 3)
    private int selectTreasureType = TeamTreasureActivityData.TREASURE_TYPE1;


    public PbActivityTeam.TreasureHuntTeamInfo genPb() {
        PbActivityTeam.TreasureHuntTeamInfo.Builder builder = PbActivityTeam.TreasureHuntTeamInfo.newBuilder()
                .setSelectCount(selectCount)
                .setSelectTreasureType(selectTreasureType);
        for (TeamTreasureHuntPlayer huntPlayer : playerMap.values()) {
            builder.addTreasurePlayer(huntPlayer.genPb());
        }
        return builder.build();
    }


    public Map<Long, TeamTreasureHuntPlayer> getPlayerMap() {
        return playerMap;
    }

    public void setPlayerMap(Map<Long, TeamTreasureHuntPlayer> playerMap) {
        this.playerMap = playerMap;
    }

    public int getSelectCount() {
        return selectCount;
    }

    public void setSelectCount(int selectCount) {
        this.selectCount = selectCount;
    }

    public int getSelectTreasureType() {
        return selectTreasureType;
    }

    public void setSelectTreasureType(int selectTreasureType) {
        this.selectTreasureType = selectTreasureType;
    }
}

package com.gy.server.world.activityTeam.bean;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.gy.server.packet.PbActivityTeam;
import com.gy.server.packet.PbCommons;

import java.util.HashMap;
import java.util.Map;

/**
 * @program: tl_game_4
 * @description: 无量秘境组队玩家数据
 * @author: Huang<PERSON>
 * @create: 2025/2/22
 **/
public class InfiniteRealmPlayer {

    @Protobuf(order = 1)
    private long pid;
    @Protobuf(order = 2)
    private int teamScore;
    @Protobuf(order = 3)
    private int playerScore;
    @Protobuf(order = 4)
    private int sceneId;

    /**
     * 已经被匹配
     */
    @Protobuf(order = 6)
    private boolean matched = false;

    /**
     * 排行榜奖励领取记录
     * 放在全局数据，主要是为了结算方便
     */
    @Protobuf(order = 7)
    private Map<Integer, Boolean> rankRewardGetRecord = new HashMap<>();

    /**
     * 最大血量
     */
    @Protobuf(order = 8)
    private long maxHp;

    /**
     * 攻击力
     */
    @Protobuf(order = 9)
    private long atk;

    public long getPid() {
        return pid;
    }

    public void setPid(long pid) {
        this.pid = pid;
    }

    public int getTeamScore() {
        return teamScore;
    }

    public void setTeamScore(int teamScore) {
        this.teamScore = teamScore;
    }

    public int getPlayerScore() {
        return playerScore;
    }

    public void setPlayerScore(int playerScore) {
        this.playerScore = playerScore;
    }

    public int getSceneId() {
        return sceneId;
    }

    public void setSceneId(int sceneId) {
        this.sceneId = sceneId;
    }

    public long getMaxHp() {
        return maxHp;
    }

    public void setMaxHp(long maxHp) {
        this.maxHp = maxHp;
    }

    public long getAtk() {
        return atk;
    }

    public void setAtk(long atk) {
        this.atk = atk;
    }

    public boolean isMatched() {
        return matched;
    }

    public void setMatched(boolean matched) {
        this.matched = matched;
    }

    public Map<Integer, Boolean> getRankRewardGetRecord() {
        return rankRewardGetRecord;
    }

    public void setRankRewardGetRecord(Map<Integer, Boolean> rankRewardGetRecord) {
        this.rankRewardGetRecord = rankRewardGetRecord;
    }

    public int addPlayerScore(int score, ActivityTeam team) {
        int newScore = team.getSecretRealmTeamData().scoreModify(2, score, this.pid);
        this.playerScore += newScore;
        if(this.playerScore < 0){
            //异常纠正
            this.playerScore = 0;
        }
        return newScore;
    }

    public int addTeamScore(int score, ActivityTeam team) {
        int newScore = team.getSecretRealmTeamData().scoreModify(1, score, this.pid);
        this.teamScore += newScore;
        //更新团队总分
        team.setTeamScore(team.getTeamScore() + newScore);
        return newScore;
    }

    public PbActivityTeam.InfiniteRealmPlayerRankNode.Builder genRankNode(int rank){
        PbActivityTeam.InfiniteRealmPlayerRankNode.Builder rankNode = PbActivityTeam.InfiniteRealmPlayerRankNode.newBuilder();
        rankNode.setRank(rank);
        rankNode.setScore(this.playerScore);
        rankNode.setUser(PbCommons.MinMiniUser.newBuilder().setId(this.pid));
        return rankNode;
    }
}

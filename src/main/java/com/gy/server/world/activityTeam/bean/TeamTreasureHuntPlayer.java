package com.gy.server.world.activityTeam.bean;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.gy.server.packet.PbActivityTeam;

import java.util.ArrayList;
import java.util.List;

/**
 * 圣兽寻宝队伍玩家数据
 */
public class TeamTreasureHuntPlayer {

    /**
     * 玩家ID
     */
    @Protobuf(order = 1)
    private long playerId;
    /**
     * 巡逻队宝藏选择信物的索引
     */
    @Protobuf(order = 2)
    private List<Integer> selectItemIndexList = new ArrayList<>();

    public TeamTreasureHuntPlayer(long playerId) {
        this.playerId = playerId;
    }

    public TeamTreasureHuntPlayer() {

    }

    public PbActivityTeam.TreasureHuntPlayer genPb() {
        return PbActivityTeam.TreasureHuntPlayer.newBuilder()
                .setPlayerId(playerId)
                .addAllSelectItemIndex(selectItemIndexList)
                .build();
    }


    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public List<Integer> getSelectItemIndexList() {
        return selectItemIndexList;
    }

    public void setSelectItemIndexList(List<Integer> selectItemIndexList) {
        this.selectItemIndexList = selectItemIndexList;
    }
}

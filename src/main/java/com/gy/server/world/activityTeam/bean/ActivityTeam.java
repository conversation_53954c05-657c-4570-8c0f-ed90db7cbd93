package com.gy.server.world.activityTeam.bean;

import com.baidu.bjf.remoting.protobuf.annotation.Ignore;
import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.gy.server.game.activity.ActivityService;
import com.gy.server.game.activity.ActivityTemplate;
import com.gy.server.game.activity.ActivityType;
import com.gy.server.game.activity.team.ActivityTeamService;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.game.role.PlayerRoleService;
import com.gy.server.packet.PbActivityTeam;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.world.activity.sutraPavilion.bean.SutraPavilionMemberInfo;
import com.gy.server.world.activity.sutraPavilion.bean.SutraPavilionTeamData;
import com.gy.server.world.activityTeam.template.TeamRuleTemplate;

import java.util.*;

/**
 * @program: tl_game_4
 * @description: 队伍
 * @author: <PERSON><PERSON>
 * @create: 2025/1/25
 **/
public class ActivityTeam {
    @Protobuf(order = 1)
    private long id;
    /**
     * 加入条件
     */
    @Protobuf(order = 2)
    private List<JoinCondition> joinConditions = new ArrayList<>();

    /**
     * 队伍成员
     * 第一个是队长
     */
    @Protobuf(order = 3)
    private List<Long> members = new ArrayList<>();

    /**
     * 申请加入的玩家
     */
    private Set<Long> applicants = new HashSet<>();

    /**
     * 队伍规则id
     */
    @Protobuf(order = 4)
    private int ruleId;

    /**
     * 需要审批才能加入
     */
    @Protobuf(order = 5)
    private boolean needApproval;

    /**
     * 无量秘境扩展数据
     */
    @Protobuf(order = 6)
    private InfiniteRealmTeamData secretRealmTeamData;

    @Protobuf(order = 7)
    private String teamName;

    /**
     * 排序用，排序前会先刷新
     */
    @Ignore
    private int teamScore;

    @Protobuf(order = 9)
    private SutraPavilionTeamData spData;

    @Ignore
    private int spFloorId;
    @Ignore
    private long rankTime;

    /**
     * 受邀玩家
     */
    @Protobuf(order = 10)
    private Set<Long> invitedPlayers = new HashSet<>();

    public ActivityTeam() {
    }

    public ActivityTeam(long id, int activityId, String teamName, long leaderId, int ruleId, boolean needApproval, List<PbActivityTeam.ActivityTeamJoinCondition> joinConditions) {
        this.id = id;
        this.teamName = teamName;
        this.members.add(leaderId);
        this.needApproval = needApproval;
        this.ruleId = ruleId;
        if (joinConditions != null) {
            for (PbActivityTeam.ActivityTeamJoinCondition joinCondition : joinConditions) {
                this.joinConditions.add(new JoinCondition(joinCondition.getType(), joinCondition.getValue()));
            }
        }

        ActivityTemplate activityTemplate = ActivityService.getActivityTemplate(activityId);
        if (activityTemplate != null && activityTemplate.type == ActivityType.infiniteRealm) {
            this.secretRealmTeamData = new InfiniteRealmTeamData();
            this.secretRealmTeamData.addPlayer(leaderId, activityId);
        }
        if(activityTemplate != null && activityTemplate.type == ActivityType.theSutraRepository){
            this.spData = new SutraPavilionTeamData();
            this.spData.init(activityId);
            this.spData.addPlayer(this, leaderId, activityId);
        }
    }

    public void init() {
        if (this.secretRealmTeamData != null) {
            for (InfiniteRealmPlayer player : this.secretRealmTeamData.getMembers().values()) {
                this.teamScore += player.getTeamScore();
            }
        }
        if (this.spData != null) {
            int floorId = -1;
            long modifyTime = Integer.MAX_VALUE;
            for (SutraPavilionMemberInfo memberInfo : this.spData.getMemberInfos().values()) {
                this.teamScore += memberInfo.getPoint();
                if(memberInfo.getFloorId() >= floorId){
                    floorId = memberInfo.getFloorId();
                    modifyTime = Math.min(modifyTime, memberInfo.getRankTime());
                }
            }
            spFloorId = floorId;
            rankTime = modifyTime;
        }
    }


    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public List<JoinCondition> getJoinConditions() {
        return joinConditions;
    }

    public void setJoinConditions(List<JoinCondition> joinConditions) {
        this.joinConditions = joinConditions;
    }

    public List<Long> getMembers() {
        return members;
    }

    public void setMembers(List<Long> members) {
        this.members = members;
    }

    public Set<Long> getApplicants() {
        return applicants;
    }

    public void setApplicants(Set<Long> applicants) {
        this.applicants = applicants;
    }

    public int getRuleId() {
        return ruleId;
    }

    public void setRuleId(int ruleId) {
        this.ruleId = ruleId;
    }

    public boolean isNeedApproval() {
        return needApproval;
    }

    public void setNeedApproval(boolean needApproval) {
        this.needApproval = needApproval;
    }

    public InfiniteRealmTeamData getSecretRealmTeamData() {
        return secretRealmTeamData;
    }

    public void setSecretRealmTeamData(InfiniteRealmTeamData secretRealmTeamData) {
        this.secretRealmTeamData = secretRealmTeamData;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }

    /**
     * 有redis访问，需要改为异步调用
     */
    public PbActivityTeam.ActivityTeamDetail genPb(boolean isCross, int zoneId, int activityId) {
        PbActivityTeam.ActivityTeamDetail.Builder builder = PbActivityTeam.ActivityTeamDetail.newBuilder();
        builder.setName(teamName)
                .setIsCross(isCross)
                .setTeamId(id)
                .setServerId(Player.getRealServerId(members.get(0)))
                .setTeamRuleId(this.ruleId);

        TeamRuleTemplate rule = ActivityTeamService.teamRuleTemplates.get(ruleId);
        if (rule != null) {
            builder.setMaxMemberCount(rule.memberCount);
        }

        Map<Long, MiniGamePlayer> players = PlayerHelper.getMiniPlayersForMap(this.members);
        for (Long memberId : this.members) {
            MiniGamePlayer miniGamePlayer = players.get(memberId);
            if (miniGamePlayer != null) {
                builder.addMembers(miniGamePlayer.genMinMiniUser());
            }
        }

        for (JoinCondition con : this.joinConditions) {
            builder.addJoinCondition(con.toPb());
        }

        builder.setNeedApproval(this.needApproval);

        if (secretRealmTeamData != null) {
            builder.setSecretRealm(secretRealmTeamData.genPb(zoneId, activityId, id));
        }
        return builder.build();
    }

    public PbActivityTeam.ActivityTeamInfo genInfo(long pid, int maxMemberCount, boolean isCross, boolean withPlayerData) {
        PbActivityTeam.ActivityTeamInfo.Builder builder = PbActivityTeam.ActivityTeamInfo.newBuilder();
        builder.setTeamId(id)
                .setName(this.teamName)
                .setMemberCount(this.members.size())
                .setMaxMemberCount(maxMemberCount)
                .setServerId(Player.getRealServerId(members.get(0)))
                .setIsCross(isCross)
                .setIsApplied(this.applicants.contains(pid));

        //加入条件
        if (CollectionUtil.isNotEmpty(this.joinConditions)) {
            for (JoinCondition joinCondition : this.joinConditions) {
                builder.addJoinCondition(joinCondition.toPb());
            }
        }

        if (withPlayerData) {
            MiniGamePlayer leader = PlayerHelper.getMiniPlayer(members.get(0));
            if (leader != null) {
                PbActivityTeam.ActivityTeamMember.Builder leaderBuilder = PbActivityTeam.ActivityTeamMember.newBuilder();
                leaderBuilder.setLevel(leader.getLevel())
                        .setHeadFrameId(leader.getHeadFrame())
                        .setPid(leader.getPlayerId())
                        .setProtagonistId(PlayerRoleService.getProtagonistTemplate(leader.getProfession(), leader.getGender()).id)
                        .setHeadId(leader.getHeadImage());
                builder.setLeader(leaderBuilder.build());
            } else {
                return null;
            }
        } else {
            PbActivityTeam.ActivityTeamMember.Builder leaderBuilder = PbActivityTeam.ActivityTeamMember.newBuilder();
            leaderBuilder
                    .setPid(members.get(0));
            builder.setLeader(leaderBuilder.build());
        }

        return builder.build();
    }

    public boolean checkJoinCondition(List<JoinCondition> joinConditions) {
        if (this.joinConditions == null || this.joinConditions.size() == 0) {
            return true;
        }

        for (JoinCondition joinCondition : this.joinConditions) {
            boolean ok = false;
            for (JoinCondition condition : joinConditions) {
                if (joinCondition.getType() == condition.getType() && joinCondition.getValue() <= condition.getValue()) {
                    ok = true;
                    break;
                }
            }
            if (!ok) {
                return false;
            }
        }
        return true;
    }

    public void updateJoinCondition(List<PbActivityTeam.ActivityTeamJoinCondition> joinConditions) {
        if (joinConditions == null || joinConditions.size() == 0) {
            this.joinConditions.clear();
        }

        for (PbActivityTeam.ActivityTeamJoinCondition joinCondition : joinConditions) {
            boolean ok = false;
            for (JoinCondition condition : this.joinConditions) {
                if (joinCondition.getType() == condition.getType()) {
                    condition.setValue(joinCondition.getValue());
                    ok = true;
                    break;
                }
            }
            if (!ok) {
                this.joinConditions.add(new JoinCondition(joinCondition.getType(), joinCondition.getValue()));
            }
        }

        List<JoinCondition> removeList = new ArrayList<>();
        for (JoinCondition condition : this.joinConditions) {
            boolean ok = false;
            for (PbActivityTeam.ActivityTeamJoinCondition joinCondition : joinConditions) {
                if (condition.getType() == joinCondition.getType()) {
                    ok = true;
                    break;
                }
            }
            if (!ok) {
                removeList.add(condition);
            }
        }

        this.joinConditions.removeAll(removeList);
    }

    public void addMember(long memberId, int activityId) {
        this.members.add(memberId);
        if (this.secretRealmTeamData != null) {
            this.secretRealmTeamData.addPlayer(memberId, activityId);
        }
        if(this.spData != null){
            this.spData.addPlayer(this, memberId, activityId);
        }
    }

    public void removeMember(long memberId) {
        this.members.remove(memberId);
        if (this.secretRealmTeamData != null) {
            this.secretRealmTeamData.removePlayer(memberId);
        }
        if (this.spData != null) {
            this.spData.removePlayer(memberId);
        }
    }

    public int getTeamScore() {
        return teamScore;
    }

    public void setTeamScore(int teamScore) {
        this.teamScore = teamScore;
    }

    public PbActivityTeam.InfiniteRealmTeamRankNode.Builder genRankNode(int rank) {
        PbActivityTeam.InfiniteRealmTeamRankNode.Builder builder = PbActivityTeam.InfiniteRealmTeamRankNode.newBuilder();
        builder.setRank(rank)
                .setTeamId(id)
                .setScore(teamScore)
                .setTeamName(teamName)
                .setLeaderId(members.get(0));
        return builder;
    }

    public PbActivityTeam.SurtaPavilionTeamRankInfo.Builder genSpRankInfo(int rank){
        PbActivityTeam.SurtaPavilionTeamRankInfo.Builder builder = PbActivityTeam.SurtaPavilionTeamRankInfo.newBuilder();
        builder.setRank(rank)
                .setTeamId(id)
                .setScore(teamScore)
                .setFloorId(spFloorId)
                .setTeamName(teamName)
                .setLeaderId(members.get(0));
        return builder;
    }

    public SutraPavilionTeamData getSpData() {
        return spData;
    }

    public void setSpData(SutraPavilionTeamData spData) {
        this.spData = spData;
    }

    public int getSpFloorId() {
        return spFloorId;
    }

    public void setSpFloorId(int spFloorId) {
        this.spFloorId = spFloorId;
    }

    public long getRankTime() {
        return rankTime;
    }

    public void setRankTime(long rankTime) {
        this.rankTime = rankTime;
    }

    public Set<Long> getInvitedPlayers() {
        return invitedPlayers;
    }

    public void setInvitedPlayers(Set<Long> invitedPlayers) {
        this.invitedPlayers = invitedPlayers;
    }
}

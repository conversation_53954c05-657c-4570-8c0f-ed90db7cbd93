package com.gy.server.world.activityTeam;

import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.core.Configuration;
import com.gy.server.game.activity.ActivityService;
import com.gy.server.game.activity.ActivityTemplate;
import com.gy.server.game.warZone.WarZoneHelper;
import com.gy.server.game.warZone.WarZoneTypeEnums;
import com.gy.server.packet.PbActivityTeam;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.function.Ticker;
import com.gy.server.utils.runner.Runner;
import com.gy.server.world.activity.sutraPavilion.bean.SutraPavilionMemberInfo;
import com.gy.server.world.activityTeam.bean.ActivityTeam;
import com.gy.server.world.activityTeam.bean.InfiniteRealmPlayer;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.serialize.warZone.WarZoneInfo;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @program: tl_game_4
 * @description: 活动组队管理器，world和game都会跑
 * @author: Huang.Xia
 * @create: 2025/1/25
 **/
public class ActivityTeamManager implements Ticker, Runner {

    public static final long START_TEAM_ID = 10000l;

    private static final ActivityTeamManager INSTANCE = new ActivityTeamManager();

    /**
     * 活动id、战区id，<队伍id, 队伍信息>
     *  redis存储
     */
    private Table<Integer, Integer, Map<Long, ActivityTeam>> teams = HashBasedTable.create();
    /**
     * 活动id、战区id，队伍最大id
     * redis存储
     */
    private Table<Integer, Integer, Long> teamIds = HashBasedTable.create();

    public static ActivityTeamManager getInstance() {
        return INSTANCE;
    }

    /**
     * 运行时数据，无需保存
     */
    private int saveIndex = 0;

    /**
     * 运行时数据，无需保存
     */
    private long lastActivityCheckTime = 0;

    public Table<Integer, Integer, Map<Long, ActivityTeam>> getTeams() {
        return teams;
    }

    public Map<Long, ActivityTeam> getTeams(int activityId, int  groupId){
        return teams.get(activityId, groupId);
    }

    public void setTeams(Table<Integer, Integer, Map<Long, ActivityTeam>> teams) {
        this.teams = teams;
    }


    public void tick(){
/*        if(ServerConstants.getCurrentTimeMillis() - lastActivityCheckTime > 10 * 1000){
            //10秒检查一次
            lastActivityCheckTime = ServerConstants.getCurrentTimeMillis();
        }else{
            return;
        }


        //遍历teams,脏数据移除，正常不会有
        for(Table.Cell<Integer, Integer, Map<Long, ActivityTeam>> cell : teams.cellSet()){
            int activityId = cell.getRowKey();
            ActivityTemplate activityTemplate = ActivityService.getActivityTemplate(activityId);
            if(activityTemplate == null){
                closeActivity(activityId, cell.getColumnKey());
            }
        }*/
    }

    public void closeActivity(int activityId, int warZoneId){
        Set<Long> allIds = new HashSet<>();
        Map<Long, ActivityTeam> map = teams.get(activityId, warZoneId);
        if(map != null && map.size() > 0) {
            allIds.addAll(map.keySet());
            String[] delKeys = new String[allIds.size()];
            int index = 0;
            for (long tid : allIds) {
               delKeys[index++] = GsRedisKey.ActivityTeam.team_data.getRedisKey(activityId, warZoneId, tid);
            }
            TLBase.getInstance().getRedisAssistant().delAsync(delKeys);
        }
        TLBase.getInstance().getRedisAssistant().del(GsRedisKey.ActivityTeam.team_count.getRedisKey(activityId,warZoneId));
        TLBase.getInstance().getRedisAssistant().setRemove(GsRedisKey.ActivityTeam.team_act_ids.getRedisKey(Configuration.serverType, Configuration.serverId),activityId+"");
        teams.remove(activityId, warZoneId);
        teamIds.remove(activityId, warZoneId);
    }

    /**
     * 加载本节点负责的组队信息
     * world 加载自己负责的战区组队数据
     * game 加载自己负责的单服组队数据
     */
    public void start(){
        //活动id集合更新
        String activityIdsKey = GsRedisKey.ActivityTeam.team_act_ids.getRedisKey(Configuration.serverType, Configuration.serverId);
        Set<String> activityIds = TLBase.getInstance().getRedisAssistant().setGetAll(activityIdsKey);
        if(CollectionUtil.isNotEmpty(activityIds)){
            for(String activityId : activityIds){
                int activityIdInt = Integer.parseInt(activityId);
                if(Configuration.isWorldServer()){
                    //处理自己所负责的所有战区
                    WarZoneInfo warZoneInfo = WarZoneHelper.getWarZoneInfo(WarZoneTypeEnums.infiniteRealm);
                    if (warZoneInfo != null){
                        for(WarZoneInfo.WarZoneSingleInfo warZoneSingleInfo : warZoneInfo.getSingleInfoList()){
                            if(TLBase.getInstance().getRpcUtil().hashKeyToNodeId(warZoneSingleInfo.getWarZoneId()) == Configuration.serverId){
                                //只加载自己负责的战区
                                String teamIdsKey = GsRedisKey.ActivityTeam.team_count.getRedisKey(activityId, warZoneSingleInfo.getWarZoneId());
                                long count = TLBase.getInstance().getRedisAssistant().getLong(teamIdsKey);
                                if(count > 0) {
                                    teamIds.put(activityIdInt, warZoneSingleInfo.getWarZoneId(), count);
                                    for(long id = START_TEAM_ID; id <= count; id++){
                                        ActivityTeam team = TLBase.getInstance().getRedisAssistant().getBean( ActivityTeam.class, GsRedisKey.ActivityTeam.team_data.getRedisKey(activityId, warZoneSingleInfo.getWarZoneId(), id));
                                        if(team != null){
                                            team.init();
                                            Map<Long, ActivityTeam> teamMap = teams.get(activityIdInt, warZoneSingleInfo.getWarZoneId());
                                            if(teamMap == null){
                                                teamMap = new ConcurrentHashMap<>();
                                                teams.put(activityIdInt, warZoneSingleInfo.getWarZoneId(), teamMap);
                                            }
                                            teamMap.put(team.getId(), team);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }else{
                    int warZoneId = ActivityTeamHelper.getWarZoneId(activityIdInt, false);
                    //负责自己单服
                    String teamIdsKey = GsRedisKey.ActivityTeam.team_count.getRedisKey(activityId, warZoneId);
                    long count = TLBase.getInstance().getRedisAssistant().getLong(teamIdsKey);
                    teamIds.put(activityIdInt, warZoneId, count);
                    if(count > 0) {
                        for(long id = START_TEAM_ID; id <= count; id++){
                            ActivityTeam team = TLBase.getInstance().getRedisAssistant().getBean(ActivityTeam.class, GsRedisKey.ActivityTeam.team_data.getRedisKey(activityId, warZoneId, id));
                            if(team != null){
                                team.init();
                                Map<Long, ActivityTeam> teamMap = teams.get(activityIdInt, warZoneId);
                                if(teamMap == null){
                                    teamMap = new ConcurrentHashMap<>();
                                    teams.put(activityIdInt, warZoneId, teamMap);
                                }
                                teamMap.put(team.getId(), team);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 全局数据每次都存
     * 队伍数据每次存1%,6s执行一次，这样10分钟存完一次
     */
    public void save(boolean saveAll){
        //活动id集合更新
        String activityIdsKey = GsRedisKey.ActivityTeam.team_act_ids.getRedisKey(Configuration.serverType, Configuration.serverId);
        TLBase.getInstance().getRedisAssistant().del(activityIdsKey);
        for(int activityId : teams.rowKeySet()){
            TLBase.getInstance().getRedisAssistant().setAddAsync(activityIdsKey, activityId+"");
        }

        //最大id更新
        for(int activityId : teams.rowKeySet()){
            ActivityTemplate activityTemplate = ActivityService.getActivityTemplate(activityId);
            if(activityTemplate == null || !activityTemplate.isOpenNow()){
                continue;
            }

            for(int zoneId : teams.columnKeySet()){
                String teamIdsKey = GsRedisKey.ActivityTeam.team_count.getRedisKey(activityId, zoneId);
                Long maxId = teamIds.get(activityId, zoneId);
                if(maxId != null){
                    TLBase.getInstance().getRedisAssistant().set(teamIdsKey, maxId+"");
                }
            }
        }

        //保存队伍数据
        for(int activityId : teams.rowKeySet()){
            ActivityTemplate activityTemplate = ActivityService.getActivityTemplate(activityId);
            if(activityTemplate == null || !activityTemplate.isOpenNow()){
                continue;
            }
            for(int zoneId : teams.columnKeySet()){
                for(ActivityTeam team : teams.get(activityId, zoneId).values()){
                    if(team.getId() % 100 == saveIndex || saveAll){
                        String teamKey = GsRedisKey.ActivityTeam.team_data.getRedisKey(activityId, zoneId, team.getId());
                        TLBase.getInstance().getRedisAssistant().setBean(teamKey, team);
                    }
                }
            }
        }

        saveIndex++;
        if(saveIndex >= 100){
            saveIndex = 0;
        }
    }

    public ActivityTeam getTeamByPlayerId(int activityId, int zoneId, long pid){
        Map<Long, ActivityTeam> teamMap = teams.get(activityId, zoneId);
        if(teamMap!= null){
            for(ActivityTeam team : teamMap.values()){
                if(team.getMembers().contains(pid)){
                    return team;
                }
            }
        }
        return null;
    }

    /**
     * 获取当前分区所有组队
     */
    public List<ActivityTeam> getAllTeams(int activityId, int zoneId){
        List<ActivityTeam> list = new ArrayList<>();
        Map<Long, ActivityTeam> teamMap = teams.get(activityId, zoneId);
        if(teamMap!= null){
            list.addAll(teamMap.values());
        }
        return list;
    }

    public void deleteTeam(int activityId, int zoneId, ActivityTeam team){
        Map<Long, ActivityTeam> teamMap = teams.get(activityId, zoneId);
        if(teamMap!= null){
            teamMap.remove(team.getId());
            String teamKey = GsRedisKey.ActivityTeam.team_data.getRedisKey(activityId, zoneId, team.getId());
            TLBase.getInstance().getRedisAssistant().delAsync(teamKey);
        }
    }

    public ActivityTeam getTeamById(int activityId, int zoneId, long teamId, long pid){
        if(teamId <= 0){
            return getTeamByPlayerId(activityId, zoneId, pid);
        }
        Map<Long, ActivityTeam> teamMap = teams.get(activityId, zoneId);
        if(teamMap!= null){
            return teamMap.get(teamId);
        }
        return null;
    }

    private long newTeamId(int activityId, int zoneId){
        Long maxId = teamIds.get(activityId, zoneId);
        if(maxId == null){
            maxId = START_TEAM_ID;
        }
        maxId++;
        teamIds.put(activityId, zoneId, maxId);
        return maxId;
    }
    public ActivityTeam createTeam(int activityId, int zoneId, String name,long pid, int ruleId, boolean needApproval, List<PbActivityTeam.ActivityTeamJoinCondition>joinConditions){

        long teamId = newTeamId(activityId, zoneId);
        ActivityTeam team = new ActivityTeam(teamId, activityId, name, pid, ruleId,needApproval, joinConditions);
        Map<Long, ActivityTeam> map = teams.get(activityId, zoneId);
        if(map == null){
            map = new ConcurrentHashMap<>();
            teams.put(activityId, zoneId, map);
        }
        map.put(teamId, team);
        return team;
    }


    @Override
    public void runnerExecute() throws Exception {
        save(false);
    }

    public void shutdown(){
        save(true);
    }

    @Override
    public long getRunnerInterval() {
        return 6*1000;
    }

    public List<ActivityTeam> getTeamRanks(int activityId, int zoneId){
        List<ActivityTeam> list = new ArrayList<>();
        Map<Long, ActivityTeam> teamMap = teams.get(activityId, zoneId);
        if(teamMap != null){
            list.addAll(teamMap.values());
        }
        Collections.sort(list, (o1, o2) -> {
            if(o1.getTeamScore() == o2.getTeamScore()){
                return Long.compare(o1.getId(), o2.getId());
            }else {
                return Integer.compare(o2.getTeamScore(), o1.getTeamScore());
            }
        });
        return list;
    }

    public List<InfiniteRealmPlayer> getPlayerInfiniteRanks(int activityId, int zoneId){
        List<InfiniteRealmPlayer> list = new ArrayList<>();
        Map<Long, ActivityTeam> teamMap = teams.get(activityId, zoneId);
        if(teamMap != null) {
            for (ActivityTeam team : teamMap.values()) {
                if(team.getSecretRealmTeamData() != null){
                    list.addAll(team.getSecretRealmTeamData().getMembers().values());
                }
            }
        }
        Collections.sort(list, (o1, o2) -> {
            if (o1.getPlayerScore() == o2.getPlayerScore()) {
                return Long.compare(o1.getPid(), o2.getPid());
            } else {
                return Integer.compare(o2.getPlayerScore(), o1.getPlayerScore());
            }
        });

        return list;
    }

    public int getInfiniteRealmTeamRank(int activityId, int zoneId, long teamId){
        List<ActivityTeam> list = getTeamRanks(activityId, zoneId);
        int rank = 1;
        for(ActivityTeam team : list){
            if(team.getId() == teamId){
                return rank;
            }
            rank++;
        }
        return -1;
    }

    public List<ActivityTeam> getTeamSpRanks(int activityId, int zoneId){
        List<ActivityTeam> list = new ArrayList<>();
        Map<Long, ActivityTeam> teamMap = teams.get(activityId, zoneId);
        if(teamMap != null){
            list.addAll(teamMap.values());
        }
        list.sort((o1, o2) -> {
            if (o2.getSpFloorId() == o1.getSpFloorId()) {
                if (o2.getTeamScore() == o1.getTeamScore()) {
                    return (int) (o1.getRankTime() - o2.getRankTime());
                }
                return o2.getTeamScore() - o1.getTeamScore();
            }
            return o2.getSpFloorId() - o1.getSpFloorId();
        });
        return list;
    }

    public List<SutraPavilionMemberInfo> getSpPlayerRanks(int activityId, int zoneId){
        List<SutraPavilionMemberInfo> list = new ArrayList<>();
        Map<Long, ActivityTeam> teamMap = teams.get(activityId, zoneId);
        if(teamMap != null) {
            for (ActivityTeam team : teamMap.values()) {
                if(team.getSpData() != null){
                    list.addAll(team.getSpData().getMemberInfos().values());
                }
            }
        }
        list.sort((o1, o2) -> {
            if (o2.getFloorId() == o1.getFloorId()) {
                if (o2.getPoint() == o1.getPoint()) {
                    return (int) (o1.getRankTime() - o2.getRankTime());
                }
                return o2.getPoint() - o1.getPoint();
            }
            return o2.getFloorId() - o1.getFloorId();
        });

        return list;
    }

    public int getSpTeamRank(int activityId, int zoneId, long teamId){
        List<ActivityTeam> ranks = getTeamSpRanks(activityId, zoneId);
        int rank = 1;
        for(ActivityTeam team : ranks){
            if(team.getId() == teamId){
                return rank;
            }
            rank++;
        }
        return -1;
    }

    public int getSpPlayerRank(int activityId, int zoneId, long teamId){
        List<SutraPavilionMemberInfo> ranks = getSpPlayerRanks(activityId, zoneId);
        int rank = 1;
        for(SutraPavilionMemberInfo memberInfo : ranks){
            if(memberInfo.getMemberId() == teamId){
                return rank;
            }
            rank++;
        }
        return -1;
    }

}

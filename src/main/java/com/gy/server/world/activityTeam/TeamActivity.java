package com.gy.server.world.activityTeam;

import com.gy.server.world.activityTeam.template.TeamRuleTemplate;

/**
 * @program: tl_game_4
 * @description: 组队活动接口
 * @author: <PERSON><PERSON>
 * @create: 2025/1/25
 **/
public interface TeamActivity {

    /**
     * 是否战区玩法
     * @return true:战区玩法，false:本服玩法
     */
    public boolean isWarZoneCross();

    /**
     * 创建队伍次数
     */
    public int getCreateTimes();

    /**
     * 创建队伍次数修改
     */
    public void setCreateTimes(int num);

    public TeamRuleTemplate getTeamRuleTemplate();

    public boolean canReleaseTeam();

    public boolean canQuitTeam();
}

package com.gy.server.world.activityTeam.bean;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.gy.server.game.activity.ActivityService;
import com.gy.server.game.activity.bean.infiniteRealm.InfiniteHelpInfo;
import com.gy.server.game.activity.bean.infiniteRealm.InfiniteRealmScoreModify;
import com.gy.server.game.activity.data.infiniteRealm.InfiniteRealmActivityData;
import com.gy.server.game.activity.data.infiniteRealm.InfiniteRealmEventTemplate;
import com.gy.server.game.attribute.AttributeKey;
import com.gy.server.game.battleCollect.BattleCollectService;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.combat.unit.HeroUnitCreator;
import com.gy.server.game.combat.unit.TeamUnit;
import com.gy.server.game.lineup.bean.LineupInfoBean;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.packet.PbActivityTeam;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.gy.server.world.activityTeam.ActivityTeamHelper;
import com.gy.server.world.activityTeam.ActivityTeamManager;
import com.ttlike.server.tl.baselib.serialize.hero.HeroUnitCreatorDb;
import com.ttlike.server.tl.baselib.serialize.lineup.LineupDb;

import java.util.*;

/**
 * @program: tl_game_4
 * @description: 无量秘境队伍数据
 * @author: Huang.Xia
 * @create: 2025/1/25
 **/
public class InfiniteRealmTeamData {

    /**
     * 成员
     * pid -> playerData
     */
    @Protobuf(order = 1)
    private Map<Long, InfiniteRealmPlayer> members= new HashMap<>();

    /**
     * 团队积分加成
     */
    @Protobuf(order = 2)
    private List<InfiniteRealmScoreModify> scoreModify = new ArrayList<>();

    /**
     * 求助信息
     * 进行中的，一个玩家只能一个，
     * 已完成、已关闭的、的可以一直挂着
     */
    @Protobuf(order = 3)
    private List<InfiniteHelpInfo> helpInfos = new ArrayList<>();

    public void newDayRefresh(int activityId){
        // 重置成员进度
        InfiniteRealmActivityData data = ActivityService.getActivityData(activityId);
        if(data != null){
            int initSceneId = data.getInitScene().id;
            for(InfiniteRealmPlayer player : members.values()){
                player.setSceneId(initSceneId);
                player.setMatched(false);
            }
        }

        //未完成任务清理
        Iterator<InfiniteHelpInfo> iterator = helpInfos.iterator();
        while(iterator.hasNext()){
            InfiniteHelpInfo info = iterator.next();
            if(info.getLeftHp() > 0 || info.getLeftHp() == -1){
                iterator.remove();
            }
        }
    }


    public Map<Long, InfiniteRealmPlayer> getMembers() {
        return members;
    }

    public void setMembers(Map<Long, InfiniteRealmPlayer> members) {
        this.members = members;
    }

    public List<InfiniteRealmScoreModify> getScoreModify() {
        return scoreModify;
    }

    public void setScoreModify(List<InfiniteRealmScoreModify> scoreModify) {
        this.scoreModify = scoreModify;
    }

    public List<InfiniteHelpInfo> getHelpInfos() {
        return helpInfos;
    }

    public InfiniteHelpInfo getHelpInfo(long pid, int eventId){
        for(InfiniteHelpInfo info : this.helpInfos){
            if(info.getSendPid() == pid && info.getEventId() == eventId){
                return info;
            }
        }
        return null;
    }

    public InfiniteHelpInfo getHelpInfo(int helpId){
        for(InfiniteHelpInfo info : this.helpInfos){
            if(info.getId() == helpId){
                return info;
            }
        }
        return null;
    }

    /**
     * 根据当前最大helpId递增
     */
    public int genNewHelpId(){
        int max = 0;
        for(InfiniteHelpInfo info : this.helpInfos){
            if(info.getId() > max){
                max = info.getId();
            }
        }
        return max + 1;
    }

    public void createHelp(long pid, int eventId,int eventTemplateId, int activityId, long leftHp){
        createHelp(pid, eventId, eventTemplateId, activityId, leftHp, -1, 0, 0);
    }

    public void createHelp(long pid, int eventId, int eventTemplateId, int activityId, long leftHp, long targetPlayerId, long playerMaxHp, long playerAtk){
        InfiniteHelpInfo info = new InfiniteHelpInfo();
        info.setId(genNewHelpId());
        info.setLeftHp(leftHp);
        info.setTargetPlayerId(targetPlayerId);
        info.setEventId(eventId);
        info.setSendPid(pid);
        info.setEventTemplateId(eventTemplateId);

        //计算总血量
        if(targetPlayerId <= 0){
            //PVE
            InfiniteRealmActivityData data = ActivityService.getActivityData(activityId);
            InfiniteRealmEventTemplate eventTemplate = data.events.get(eventTemplateId);

            info.setMaxHp(eventTemplate.params[2]);
        }else{
            //PVP
            info.setMaxHp(playerMaxHp);
            info.setTargetPlayerAtk(playerAtk);
        }


        //计算伤害数据
        long damage = info.getMaxHp() - info.getLeftHp();
        if(damage > 0){
            info.getDamages().put(pid, damage);
        }

        //设置场景id
        info.setSceneId(members.get(pid).getSceneId());

        this.helpInfos.add(info);

        ActivityTeamHelper.helpPush(info, this);
    }

    public boolean canRequestHelp(long pid){
        for(InfiniteHelpInfo info : this.helpInfos){
            //有进行中的求助，不能继续
            if(!info.isClosed() && info.getSendPid() == pid && (info.getLeftHp() > 0 || info.getLeftHp() == -1)){
                return  false;
            }
        }
        return true;
    }

    public void setHelpInfos(List<InfiniteHelpInfo> helpInfos) {
        this.helpInfos = helpInfos;
    }

    public PbActivityTeam.InfiniteRealmTeamInfo.Builder  genPb(int zoneId, int activityId, long teamId){
        PbActivityTeam.InfiniteRealmTeamInfo.Builder b = PbActivityTeam.InfiniteRealmTeamInfo.newBuilder();
        b.setRank(ActivityTeamManager.getInstance().getInfiniteRealmTeamRank(zoneId, activityId, teamId));
        for(InfiniteRealmPlayer player : this.members.values()){
            b.putTeamScores(player.getPid(), player.getTeamScore());
            b.putPersonalScores(player.getPid(), player.getPlayerScore());
        }
        Map<Long, Integer> scoreAdd = new HashMap<>();
        for(InfiniteRealmScoreModify m : this.scoreModify){
            Integer old = scoreAdd.getOrDefault(m.getPid(), 0);
            scoreAdd.put(m.getPid(), old + m.getRate());
        }
        b.putAllScoreAdd(scoreAdd);
        return b;
    }

    public List<PbActivityTeam.InfiniteRealmHelpTips> genSecretRealmHelpTips(long playerId){
        List<PbActivityTeam.InfiniteRealmHelpTips> tips = new ArrayList<>();
        for(InfiniteHelpInfo info : this.helpInfos){
            if(!info.showInHelpList(playerId)){
                continue;
            }
            PbActivityTeam.InfiniteRealmHelpTips.Builder b = PbActivityTeam.InfiniteRealmHelpTips.newBuilder();
            b.setPlayerId(info.getSendPid())
                    .setType(info.getLeftHp() > 0 ? 1 : 2);
            tips.add(b.build());
        }
        return tips;
    }


    public List<PbActivityTeam.InfiniteRealmHelpInfo> genSecretRealmHelpInfo(long playerId, int activityId, boolean withMiniPlayer){
        List<PbActivityTeam.InfiniteRealmHelpInfo> helps = new ArrayList<>();
        for(InfiniteHelpInfo info : this.helpInfos){
            if(!info.showInHelpList(playerId)) {
                //无需展示事件
                continue;
            }
            long leftSeconds = info.getLeftSeconds(activityId);
            if(!info.isClosed()) {
                PbActivityTeam.InfiniteRealmHelpInfo.Builder b = PbActivityTeam.InfiniteRealmHelpInfo.newBuilder();
                b.setPlayerId(info.getSendPid())
                        .setHelpLeftSeconds(leftSeconds)
                        .setGetReward(info.isGetReward(playerId))
                        .setRewardScore(info.getRewardScore(playerId, activityId))
                        .setEnemy(info.genEnemyBuilder(activityId, withMiniPlayer))
                        .setHelpId(info.getId());
                if (withMiniPlayer) {
                    MiniGamePlayer player = PlayerManager.getMiniPlayer(info.getSendPid());
                    if (player != null) {
                        b.setUser(player.genMinMiniUser());
                    }
                }
                helps.add(b.build());
            }
        }

        Iterator<InfiniteHelpInfo> it = this.helpInfos.iterator();
        while(it.hasNext()){
            InfiniteHelpInfo helpInfo = it.next();
            if(helpInfo.isClosed()){
                it.remove();
            }
        }
        return helps;
    }

    public List<PbActivityTeam.InfiniteRealmHelpRecord> genSecretRealmHelpRecord(long pid, int activityId, boolean withMiniPlayer){
        List<PbActivityTeam.InfiniteRealmHelpRecord> records = new ArrayList<>();
        for(InfiniteHelpInfo info : this.helpInfos){
            if(info.getSendPid() == pid || info.getDamages().containsKey(pid)){
                //自己发起的求助，或者自己参与过的援助
                PbActivityTeam.InfiniteRealmHelpRecord.Builder b = PbActivityTeam.InfiniteRealmHelpRecord.newBuilder();
                b.setEnemy(info.genEnemyBuilder(activityId, withMiniPlayer))
                                .setPlayerId(info.getSendPid())
                                        .setMyScore(info.getRewardScore(pid, activityId));
                Long damage = info.getDamages().get(pid);
                if(damage != null){
                    b.setMyDamage(damage);
                }

                records.add(b.build());
            }
        }
        return records;
    }

    /**
     * 获取所有求助事件的剩余总血量
     * eventId <-----> 剩余总血量
     * @return
     */
    public Map<Integer, Long> getLeftHps(){
        Map<Integer, Long> leftHps = new HashMap<>();
        return leftHps;
    }


    public List<Long> getSameProgressMembers(long pid){
        int progress = this.members.get(pid).getSceneId();
        List<Long> sameProgressMembers = new ArrayList<>();
        for(InfiniteRealmPlayer p : this.members.values()){
            if(p.getSceneId() == progress && p.getPid() != pid){
                sameProgressMembers.add(p.getPid());
            }
        }
        return sameProgressMembers;
    }

    public Map<Long,Integer> getMemberScenes(){
        Map<Long, Integer> scenes = new HashMap<>();
        for(InfiniteRealmPlayer p : this.members.values()){
           scenes.put(p.getPid(), p.getSceneId());
        }
        return scenes;
    }

    public Map<Long, Integer> genScoreAdd(){
        Map<Long, Integer> rst = new HashMap<>();
        for(InfiniteRealmScoreModify m : this.scoreModify){
            Integer score = rst.get(m.getPid());
            if(score != null){
                score += m.getRate();
                rst.put(m.getPid(), score);
            }else{
                rst.put(m.getPid(), m.getRate());
            }
        }
        return rst;
    }

    public void addPlayer(long pid, int activityId){
        InfiniteRealmActivityData data = ActivityService.getActivityData(activityId);
        if(data != null) {
            InfiniteRealmPlayer player = new InfiniteRealmPlayer();
            player.setPid(pid);
            player.setSceneId(data.getInitScene().id);
            this.members.put(pid, player);
        }
    }

    public void removePlayer(long pid) {
        this.members.remove(pid);
        //从scoreModify中移除
        List<InfiniteRealmScoreModify> toRemove = new ArrayList<>();
        for (InfiniteRealmScoreModify m : this.scoreModify) {
            if (m.getPid() == pid) {
                toRemove.add(m);
            }
        }
        this.scoreModify.removeAll(toRemove);
    }

    public int scoreModify(int type, int score, long pid){
        int rate = 0;
        for(InfiniteRealmScoreModify m : this.scoreModify){
            if(m.getAddType() == type){
                if(type == 1 || m.getPid() == pid){
                    rate += m.getRate();
                    if(m.getLeftEffectTimes() > 0){
                        m.setLeftEffectTimes(m.getLeftEffectTimes() - 1);
                    }
                }
            }
        }

        if(rate != 0){
            score += score * rate / 10000;
            Iterator<InfiniteRealmScoreModify> it = this.scoreModify.iterator();
            while(it.hasNext()){
                InfiniteRealmScoreModify m = it.next();
                if(m.getLeftEffectTimes() == 0){
                    it.remove();
                }
            }
        }
        return score;
    }
}

package com.gy.server.world.activityTeam.bean;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.gy.server.game.player.Player;
import com.gy.server.packet.PbActivityTeam;

import java.util.ArrayList;
import java.util.List;

/**
 * @program: tl_game_4
 * @description: 加入条件
 * @author: <PERSON><PERSON>
 * @create: 2025/1/25
 **/
public class JoinCondition implements java.io.Serializable {
    public static final int TYPE_FIGHTING_POWER = 1;
    public static final int TYPE_LEVEL = 2;

    private static final long serialVersionUID = 23482923492340L;
    @Protobuf(order = 1)
    private int type;
    @Protobuf(order = 2)
    private long value;

    public JoinCondition() {}

    public JoinCondition(int type, long value) {
        this.type = type;
        this.value = value;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public long getValue() {
        return value;
    }

    public void setValue(long value) {
        this.value = value;
    }

    public static List<JoinCondition> getPlayerJoinConditions(Player player){
        List<JoinCondition> conditions = new ArrayList<>();
        conditions.add(new JoinCondition(TYPE_FIGHTING_POWER, player.getFightingPower()));
        conditions.add(new JoinCondition(TYPE_LEVEL, player.getLevel()));
        return conditions;
    }

    public PbActivityTeam.ActivityTeamJoinCondition toPb(){
        PbActivityTeam.ActivityTeamJoinCondition.Builder builder = PbActivityTeam.ActivityTeamJoinCondition.newBuilder();
        builder.setType(type);
        builder.setValue((int) value);
        return builder.build();
    }
}

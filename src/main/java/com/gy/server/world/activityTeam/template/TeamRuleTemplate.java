package com.gy.server.world.activityTeam.template;

import com.gy.server.game.drop.RewardTemplate;

import java.util.List;
import java.util.Map;

/**
 * @program: tl_game_4
 * @description: 组队规则
 * @author: <PERSON><PERSON>
 * @create: 2025/1/25
 **/
public class TeamRuleTemplate {
    /**
     * 积分排行
     */
    public static final int TYPE_SCORE_RANK = 1;
    /**
     * 格子爬塔
     */
    public static final int TYPE_GRID_TOWER = 2;

    public int id;
    public int type;
    public int memberCount;
    public String unlockType;
    public RewardTemplate createCost;
    public int freeTimes;

    public int applyCount;

    public static TeamRuleTemplate fromMap(Map<String, String> map) {
        TeamRuleTemplate rule = new TeamRuleTemplate();
        rule.id = Integer.parseInt(map.get("id"));
        rule.type = Integer.parseInt(map.get("type"));
        rule.memberCount = Integer.parseInt(map.get("playerNumber"));
        rule.unlockType = map.get("unlockType");

        List<RewardTemplate> list = RewardTemplate.readListFromText(map.get("teamCost"));
        if(list.size() > 0){
            rule.createCost = list.get(0);
        }
        rule.freeTimes = Integer.parseInt(map.get("feelTimes"));
        rule.applyCount = Integer.parseInt(map.get("messageLimit"));
        return rule;
    }
}

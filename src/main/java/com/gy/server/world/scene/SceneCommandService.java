package com.gy.server.world.scene;

import com.gy.server.annotation.MessageMethod;
import com.gy.server.annotation.MessageServiceBean;
import com.gy.server.core.MessageServerType;
import com.gy.server.core.MethodInvokeType;
import com.gy.server.core.command.CommandRequestParams;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.scene.SceneCheckManager;
import com.gy.server.utils.CollectionUtil;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.redis.key.BaseRedisKey;

import java.util.*;

/**
 * 场景服相关
 *
 * <AUTHOR> - [Created on 2022-03-22 15:42]
 */
@MessageServiceBean(description = "场景服相关", messageServerType = MessageServerType.world)
public class SceneCommandService {
    private static final int single_sceneServer_max_bands = 50;//的那个场景服最高绑定

//    @MessageMethod(description = "分配场景服id", invokeType = MethodInvokeType.free)
//    public static void sceneAllocation(ServerCommandRequest request, CommandRequestParams params) {
//        int serverId = params.getParam(0);
//        String sceneId = params.getParam(1);
//
//        //查询当前serverId是否存在
//        String sceneBandKey = BaseRedisKey.Scene.sceneId2SceneServerId.getRedisKey(sceneId);
//        String sceneServerId = TLBase.getInstance().getRedisAssistant().get(sceneBandKey);
//        //开始绑定(注意，如果这时候sceneServerId场景服挂掉，这边不会分配，gs会检测场景服是否存活，否 清除绑定信息再重新绑定)
//        if (Objects.isNull(sceneServerId) || sceneServerId.isEmpty()) {
//            System.out.println(String.format("start allocate scene, serverId : %s, sceneId : %s", serverId, sceneId));
//
//            //获得所有场景服id
//            String sceneServerIdRedisKey = BaseRedisKey.Scene.sceneServerId.getRedisKey();
//            Set<String> allSceneServerIds = TLBase.getInstance().getRedisAssistant().setGetAll(sceneServerIdRedisKey);
//
//            Map<String, Integer> finalSceneServerIdCount = new HashMap<>();
//            for (String targetSceneServerId : allSceneServerIds) {
//                //获得所有存活场景服，并加入统计
//                String liveSceneServerIdRedisKey = BaseRedisKey.Scene.live_sceneServerId.getRedisKey(targetSceneServerId);
//                int bandCount = TLBase.getInstance().getRedisAssistant().getInt(liveSceneServerIdRedisKey);
//                if (bandCount >= 0) {
//                    finalSceneServerIdCount.put(targetSceneServerId, bandCount);
//                }
//            }
//
//            System.out.println(String.format("get can allocate sceneServerSize : %s", finalSceneServerIdCount.size()));
//            if (CollectionUtil.isNotEmpty(finalSceneServerIdCount)) {
//                List<Map.Entry<String, Integer>> list = new ArrayList<>(finalSceneServerIdCount.entrySet());
//                //降序排序
//                list.sort(Map.Entry.comparingByValue());
//                //获得最终场景id，开始绑定
//                Map.Entry<String, Integer> finalSceneServerId = list.get(0);
//                TLBase.getInstance().getRedisAssistant().set(sceneBandKey, finalSceneServerId.getKey());
//                String sceneServerId2SceneIdRedisKey = BaseRedisKey.Scene.sceneServerId2SceneId.getRedisKey(finalSceneServerId.getKey());
//                TLBase.getInstance().getRedisAssistant().setAdd(sceneServerId2SceneIdRedisKey, sceneId);
//                //设置双倍过期时间
//                TLBase.getInstance().getRedisAssistant().pexpire(sceneBandKey, SceneCheckManager.key_alive_time * 2);
//                System.out.println(String.format("finish allocate scene, serverId : %s, sceneId : %s, sceneServerId : %s", serverId, sceneId, finalSceneServerId.getKey()));
//                //最大绑定检测
//                list.stream().filter(entry -> entry.getValue() >= single_sceneServer_max_bands).forEach(entry -> System.out.println(String.format("single scene sever bands is full, sceneServerId : %s, bandNum : %s", entry.getKey(), entry.getValue())));
//
//                request.addCallbackParam(finalSceneServerId.getKey());
//            } else {
//                request.addCallbackParam(-1);
//                System.err.println("allocate scene is error !!!!");
//            }
//        }
//
//
//    }
}

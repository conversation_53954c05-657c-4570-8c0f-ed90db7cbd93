package com.gy.server.world.leagueSLG.bean;

import com.google.protobuf.Message;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.leagueSLG.LeagueSLGService;
import com.gy.server.packet.PbSLG;
import com.gy.server.world.leagueSLG.LeagueSLGWorldManager;

/**
 * @program: tl_game_4
 * @description: 战斗
 * @author: <PERSON><PERSON>
 * @create: 2024/11/26
 **/
public class SLGFight {

    private long finishTime;

    private SLGPlayer atk;
    private SLGPlayer defPlayer;

    private SLGGuard defGuard;

    /**
     * 是否出发了压制模式
     */
    private boolean fastMode;

    public SLGFight(SLGPlayer atk, SLGPlayer def) {
        this.atk = atk;
        this.defPlayer = def;

        if(LeagueSLGWorldManager.getInstance().getStatus().canFightFastMode()) {
            if (atk.getArmy() >= def.getArmy() * 3) {
                if (atk.getLeague().isTop3Medal(atk.getPid())) {
                    this.fastMode = true;
                }
            }
        }

        if(fastMode){
            this.finishTime = ServerConstants.getCurrentTimeMillis() + LeagueSLGService.getConstant().fightTimeFast * 1000;
        }else {
            this.finishTime = ServerConstants.getCurrentTimeMillis() + LeagueSLGService.getConstant().fightTime * 1000;
        }

        atk.updateFightFinishTime(this.finishTime);
        def.updateFightFinishTime(this.finishTime);
    }

    public SLGFight(SLGPlayer atk, SLGGuard def) {
        this.finishTime = ServerConstants.getCurrentTimeMillis() + LeagueSLGService.getConstant().fightTime * 1000;
        this.atk = atk;
        this.defGuard = def;

        atk.updateFightFinishTime(this.finishTime);
    }

    public SLGPlayer getAtk() {
        return atk;
    }

    public void setAtk(SLGPlayer atk) {
        this.atk = atk;
    }

    public long getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(long finishTime) {
        this.finishTime = finishTime;
    }

    public SLGPlayer getDefPlayer() {
        return defPlayer;
    }

    public void setDefPlayer(SLGPlayer defPlayer) {
        this.defPlayer = defPlayer;
    }

    public SLGGuard getDefGuard() {
        return defGuard;
    }

    public void setDefGuard(SLGGuard defGuard) {
        this.defGuard = defGuard;
    }

    public boolean isFinish(){
        if(ServerConstants.getCurrentTimeMillis() >= finishTime){
            return true;
        }

        return false;
    }

    public void settle(SLGCity city, SLGBattleField field){
        PbSLG.SLGAchievementAttacker.Builder atkRecord = PbSLG.SLGAchievementAttacker.newBuilder();
        atkRecord.setPlayerId(atk.getPid())
                .setArmy(atk.getArmy());
        PbSLG.SLGAchievementAttacker.Builder defRecord = PbSLG.SLGAchievementAttacker.newBuilder();

        if(defPlayer!= null){
            defRecord.setPlayerId(defPlayer.getPid())
                    .setArmy(defPlayer.getArmy());

            //防守方是玩家
            if(fastMode){
                //压制模式
                int damage = Math.max(1, (int) (atk.getArmy() * 0.2));
                atk.damageArmy((damage));
                atkRecord.setCostArmy(damage);

                damage = Math.max(1,(int) (defPlayer.getArmy() * 0.2));
                defPlayer.damageArmy((damage));
                defRecord.setCostArmy(damage);

                if(atk.getArmy() > defPlayer.getArmy()){
                    atk.addContinuousWinCount();
                    defPlayer.setContinuousWinCount(0);
                }else {
                    defPlayer.addContinuousWinCount();
                    atk.setContinuousWinCount(0);
                }

                if(defPlayer.getArmy() <= 0){
                    city.occupationCheck(atk);
                }

                if(atk.getArmy() <= 0){
                    playerLose(atk, field, city, 0,defPlayer);
                }
                if(defPlayer.getArmy() <= 0){
                    playerLose(defPlayer, field, city, 0,atk);
                    city.occupationCheck(atk);
                }
            }else{
                if(atk.getArmy() > defPlayer.getArmy()){
                    //攻击方胜利
                    int damage = calcWinnerDamage(atk.getArmy(), defPlayer.getArmy());
                    atk.damageArmy(damage);
                    atk.addContinuousWinCount();
                    atkRecord.setCostArmy(damage);
                    defRecord.setCostArmy(defPlayer.getArmy());
                    playerLose(defPlayer, field, city, defPlayer.getArmy(),atk);

                    //击退次数累计
                    atk.setFightBackCount(atk.getFightBackCount() + 1);
                    defPlayer.addFightBackedRecord(atk.getPid());
                    city.occupationCheck(atk);
                }else if(atk.getArmy() < defPlayer.getArmy()){
                    //防守方胜利
                    int damage = calcWinnerDamage(defPlayer.getArmy(), atk.getArmy());
                    defPlayer.damageArmy(damage);
                    defPlayer.addContinuousWinCount();
                    defRecord.setCostArmy(damage);
                    atkRecord.setCostArmy(atk.getArmy());
                    playerLose(atk, field, city, atk.getArmy(),defPlayer);

                    //击退次数累计
                    defPlayer.setFightBackCount(defPlayer.getFightBackCount() + 1);
                    atk.addFightBackedRecord(defPlayer.getPid());
                }else{
                    //同归于尽
                    defRecord.setCostArmy(defRecord.getArmy());
                    atkRecord.setCostArmy(atk.getArmy());

                    playerLose(atk, field, city, atk.getArmy(),defPlayer);
                    playerLose(defPlayer, field, city, defPlayer.getArmy(),atk);

                    //击退次数累计
                    defPlayer.setFightBackCount(defPlayer.getFightBackCount() + 1);
                    atk.addFightBackedRecord(defPlayer.getPid());
                    atk.setFightBackCount(atk.getFightBackCount() + 1);
                    defPlayer.addFightBackedRecord(atk.getPid());

                    city.occupationCheck(atk);
                }
            }
        }else{
            //防守方是守卫
            defRecord.setPlayerId(-1)
                    .setArmy(defGuard.getArmy());

            if(atk.getArmy() > defGuard.getArmy()){
                //攻击方胜利
                int damage = calcWinnerDamage(atk.getArmy(), defGuard.getArmy());
                atk.damageArmy(damage);
                atkRecord.setCostArmy(damage);
                defRecord.setCostArmy(defGuard.getArmy());
                atk.addContinuousWinCount();

                city.getGuards().remove(defGuard.getId());
                city.getDetailSync().addRemovedUnitIds(defGuard.getId());
                city.occupationCheck(atk);
                city.setNeedSync(true);
            }else if(atk.getArmy() < defGuard.getArmy()){
                //防守方胜利
                atkRecord.setCostArmy(atk.getArmy());
                int damage = calcWinnerDamage(defGuard.getArmy(), atk.getArmy());
                defGuard.setArmy(defGuard.getArmy() - damage);

                playerLose(atk, field, city, atk.getArmy(), null);
                defRecord.setCostArmy(damage);
            }else{
                //同归于尽
                atkRecord.setCostArmy(atk.getArmy());
                defRecord.setCostArmy(defGuard.getArmy());

                city.getGuards().remove(defGuard.getId());
                city.getDetailSync().addRemovedUnitIds(defGuard.getId());
                city.setNeedSync(true);
                playerLose(atk, field, city, atk.getArmy(), null);

                city.occupationCheck(atk);
            }
        }

        PbSLG.SLGAchievementRecord.Builder achievementRecord = PbSLG.SLGAchievementRecord.newBuilder();
        achievementRecord.setAttacker(atkRecord)
                .setDefender(defRecord)
                .setCityId(city.getTid());
        PbSLG.SLGAchievementRecord b = achievementRecord.build();
        atk.addFightRecord(b);
        if(defPlayer != null){
            defPlayer.addFightRecord(b);
        }
    }

    private void playerLose(SLGPlayer player, SLGBattleField field, SLGCity city, int damage, SLGPlayer enemy){
        player.damageArmy(damage);
        city.removePlayer(player);
        //回到大本营，如果大本营还在的话
        SLGLeague league = field.getLeagueByPid(player.getPid());
        SLGCity home = field.getCities().get(league.getHomeCity());
        if(home.getLid() == league.getId()){
            home.addPlayer(player);
        }else{
            //无家可归
            player.setCityId(-1);
        }

        //溃乱
        player.getCamp().addWeak();

        //标记需要同步
        player.setStatusNeedSync(true);

        if(enemy != null){
            player.addWeakedMsg(enemy.getPid(), city.getTid());
        }

        player.setContinuousWinCount(0);
    }

    private int calcWinnerDamage(int winnerArmy, int loserArmy){
        double rst = (double) loserArmy/winnerArmy;
        rst = Math.pow(rst, 1.632);
        rst = (rst * winnerArmy + 20);

        return (int) Math.min(winnerArmy - 1, rst);
    }

    public PbSLG.SLGFight.Builder genPb(){
        PbSLG.SLGFight.Builder fight = PbSLG.SLGFight.newBuilder();
        fight.setAttacker(atk.genSLGAttackerBuilder(true));
        if(defPlayer!= null){
            fight.setDefender(defPlayer.genSLGAttackerBuilder(false));
        }else{
            fight.setDefender(defGuard.generateAttackerBuilder());
        }
        fight.setFinishTime(this.finishTime);
        return fight;
    }

    public int getFinishLeftSeconds(){
        int rst = (int) ((finishTime - ServerConstants.getCurrentTimeMillis()) / 1000);
        return Math.max(rst, 1);//至少1秒，避免客户端显示为0
    }
}

package com.gy.server.world.leagueSLG.bean;

import com.baidu.bjf.remoting.protobuf.annotation.Ignore;
import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.command.CommandRequest;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.core.log.SystemLogger;
import com.gy.server.game.league.League;
import com.gy.server.game.leagueSLG.LeagueSLGService;
import com.gy.server.game.leagueSLG.template.SLGBattleRewardTemplate;
import com.gy.server.game.leagueSLG.template.SLGCityTemplate;
import com.gy.server.game.leagueSLG.template.SLGGroupTemplate;
import com.gy.server.game.leagueSLG.template.SLGStrategyTemplate;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.player.Player;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbSLG;
import com.gy.server.utils.MathUtil;
import com.gy.server.world.leagueSLG.LeagueSLGWorldManager;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: tl_game_4
 * @description: 一场12个帮派的战斗
 * @author: Huang.Xia
 * @create: 2024/11/26
 **/
public class SLGBattleField {

    /**
     * 战场等级，初始1级
     */
    @Protobuf(order = 1)
    private int level = 1;

    @Protobuf(order = 2)
    private Map<Long, SLGLeague> leagues = new HashMap<>();

    @Protobuf(order = 3)
    private Map<Integer, SLGCity> cities = new HashMap<>();

    /**
     * 用来对应战略区域的位置
     */
    @Protobuf(order = 4)
    private List<Integer> powerBlocks = new ArrayList<>();

    @Protobuf(order = 5)
    private long lastTickTime;

    /**
     * 赛区序号
     */
    @Protobuf(order = 6)
    private int index;

    /**
     * 战区级别数据同步
     * 运行时数据
     */
    @Ignore
    PbProtocol.SLGBattlePushRst.Builder syncData = PbProtocol.SLGBattlePushRst.newBuilder();

    /**
     * 按照服务器分组的帮派id
     * 运行时数据
     */
    @Ignore
    private Map<Integer, List<Long>> serverGroupedLids = new HashMap<>();

    /**
     * 初始化赋值
     */
    @Ignore
    private SLGArea area;

    /**
     * 玩家当前关注的据点
     */
    private Map<Long, Integer> playerCityWatch = new HashMap<>();

    public void init(SLGArea area){
        this.area = area;
        for(SLGLeague league : leagues.values()){
            league.init(this);
        }
        for(SLGCity city : cities.values()){
            city.init(this);
        }

        //初始数据
        for(SLGLeague league : leagues.values()){
            int serverId = League.getRealServerId(league.getId());
            List<Long> lids = serverGroupedLids.getOrDefault(serverId, new ArrayList<>());
            lids.add(league.getId());
            serverGroupedLids.put(serverId, lids);

            if(this.level == 3){
                //3级战场纳入竞猜目标
                area.getGuessLeagues().add(league);
            }
        }
    }

    public void newDayCombat(){
        for(SLGLeague league : leagues.values()){
            league.newDayCombat();
        }
    }

    public void beforeSave(){
        for(SLGLeague league : leagues.values()){
            league.beforeSave();
        }
        for(SLGCity city : cities.values()){
            city.beforeSave();
        }
    }

    public Map<Long, Integer> getPlayerCityWatch() {
        return playerCityWatch;
    }

    public void changePlayerCityWatch(long playerId, int cityId){
        playerCityWatch.put(playerId, cityId);
    }

    public void setPlayerCityWatch(Map<Long, Integer> playerCityWatch) {
        this.playerCityWatch = playerCityWatch;
    }

    public void init(SLGArea area, int index){
        this.area = area;

        //初始数据
        for(SLGLeague league : leagues.values()){
            int serverId = League.getRealServerId(league.getId());
            List<Long> lids = serverGroupedLids.getOrDefault(serverId, new ArrayList<>());
            lids.add(league.getId());
            serverGroupedLids.put(serverId, lids);
        }

        this.index = index;
        //新建所有城池
        cities = new HashMap<>();
        for(SLGCityTemplate cityTemplate : LeagueSLGService.getCities().values()){
            SLGCity city = new SLGCity(cityTemplate.id, this);
            city.initGuard(cityTemplate.guardAmount, cityTemplate.guardArmy);
            cities.put(cityTemplate.id, city);
        }

        //用玩家最新兵力刷新下帮派战力
        for(SLGLeague league : leagues.values()){
            league.refresh();
        }
        List<SLGLeague> list = new ArrayList<>(leagues.values());
        //战力排序
        Collections.sort(list, SLGLeague.powerComparator);

        powerBlocks = new ArrayList<>(LeagueSLGService.getCityPhyBlocks());
        Collections.shuffle(powerBlocks);

        List< SLGCityTemplate> homeTemplates = LeagueSLGService.getCities().values().stream()
                .filter(city -> city.type == SLGCityTemplate.TYPE_大本营).collect(Collectors.toList());
        Collections.shuffle(homeTemplates);

        //逐个帮派分配城池
        for(int i = 0; i < list.size(); i++){
            //逐个给帮派找位置
            SLGLeague league = list.get(i);

            if(i < powerBlocks.size()){
                //大佬之一，强制分配到对应战略区域
                int block = powerBlocks.get(i);
                for(SLGCityTemplate homeTemplate : homeTemplates){
                    if(homeTemplate.physicBlock == block){
                        location(league, homeTemplate);
                        homeTemplates.remove(homeTemplate);
                        break;
                    }
                }

            }else{
                SLGCityTemplate homeTemplate = homeTemplates.get(0);
                location(league, homeTemplate);
                homeTemplates.remove(homeTemplate);
            }
        }

        initStrategy();
    }

    /**
     * 首次谋士初始化
     */
    private void initStrategy(){
        //生成0点的谋士
        List<Integer> strategyIds = new ArrayList<>(LeagueSLGService.getStrategies().keySet());
        Collections.sort(strategyIds);
        Set<Integer> usedIndexes = new HashSet<>();
        for(int id : strategyIds){
            SLGStrategyTemplate strategyTemplate = LeagueSLGService.getStrategies().get(id);
            int index = MathUtil.weightRandom(strategyTemplate.refreshWeight,usedIndexes);
            usedIndexes.add(index);
            int block = powerBlocks.get(index);
            List<SLGCityTemplate> cityTemplates = LeagueSLGService.getCities().values().stream()
                    .filter(city ->  city.strategyOn && city.physicBlock == block)
                    .collect(Collectors.toList());
            SLGCityTemplate cityTemplate = cityTemplates.get(MathUtil.randomInt(cityTemplates.size()));
            SLGCity city = cities.get(cityTemplate.id);
            city.updateStrategy(id);
        }
    }

    /**
     * 16点谋士刷新
     */
    public void refreshStrategy() {
        //先清除掉现有谋士
        for(SLGCity city : cities.values()){
            city.updateStrategy(-1);
        }

        //生成16点的谋士
        List<SLGStrategyTemplate> strategyTemplates = new ArrayList<>(LeagueSLGService.getStrategies().values());

        //中立据点
        List<SLGCity> list = new ArrayList<>();
        for(SLGCity city : cities.values()){
            if(city.getLid() <= 0
                    && city.getCityTemplate().strategyOn){//中都、修罗城不能生成谋士
                list.add(city);
            }
        }

        for(int i = 0; i < list.size() && i < strategyTemplates.size(); i++){
            SLGCity city = list.get(i);
            SLGStrategyTemplate strategyTemplate = strategyTemplates.get(i);

            city.updateStrategy(strategyTemplate.id);
            strategyTemplates.remove(strategyTemplate);
        }

        if(strategyTemplates.size() > 0){
            //如果还有剩余的谋士，需要按照城池占有排序分配
            Map<Long, LeagueCityCount> leagueList = new HashMap<>();
            for(SLGCity city : cities.values()){
                if(city.getLid() > 0 && city.getCityTemplate().strategyOn){
                    LeagueCityCount count = leagueList.getOrDefault(city.getLid(), new LeagueCityCount());
                    count.cityCount++;
                    leagueList.put(city.getLid(), count);
                }
            }
            List<LeagueCityCount> countList = new ArrayList<>(leagueList.values());
            Collections.sort(countList, (c1, c2)->{
                if(c1.cityCount == c2.cityCount){
                    return Long.compare(c1.lid, c2.lid);
                }else{
                    return Integer.compare(c2.cityCount, c1.cityCount);
                }
            });

            Iterator<SLGStrategyTemplate> iterator = strategyTemplates.iterator();
            while (iterator.hasNext()){
                SLGStrategyTemplate strategyTemplate = iterator.next();
                if(countList.size() > strategyTemplate.refreshOrder){
                    long lid = countList.get(strategyTemplate.refreshOrder - 1).lid;
                    SLGCity city = getStrategyCity(lid);
                    if(city!= null) {
                        city.updateStrategy(strategyTemplate.id);
                        iterator.remove();
                    }
                }
            }

            //如果还有剩余的谋士，则随机分配
            for(SLGStrategyTemplate strategyTemplate : strategyTemplates){
                //还有没分配完成的，随机分配
                SLGCity city = getRandomStrategyCity(strategyTemplate.id);
                if(city!= null) {
                    city.updateStrategy(strategyTemplate.id);
                }
            }
        }
    }

    private SLGCity getRandomStrategyCity(int cityId){
        List<SLGCity> list = new ArrayList<>();
        for(SLGCity city : cities.values()){
            if(city.getStrategyId() <= 0 && city.getCityTemplate().strategyOn){
                list.add(city);
            }
        }
        return list.get(MathUtil.randomInt(list.size()));
    }

    private SLGCity getStrategyCity(long lid){
        List<SLGCity> list = new ArrayList<>();
        for(SLGCity city : cities.values()){
            if(city.getLid() == lid && city.getCityTemplate().strategyOn){
                list.add(city);
            }
        }

        if(list.size() == 0){
            return null;
        }

        return list.get(MathUtil.randomInt(list.size()));
    }

    private void location(SLGLeague league, SLGCityTemplate homeTemplate){
        league.setHomeCity(homeTemplate.id);

        //所有玩家放入大本营
        SLGCity city = cities.get(homeTemplate.id);
        for(SLGPlayer player : league.getPlayers().values()){
            city.addPlayer(player);
        }
        city.setLid(league.getId());

        //刷新帮派当前占领积分
        league.winCity(city);
    }

    public void tick(){
        if(LeagueSLGWorldManager.getInstance().getStatus().canBattleTick()) {
            //控制刷新频率
            if (ServerConstants.getCurrentTimeMillis() - lastTickTime < 500) {
                return;
            }
            this.lastTickTime = ServerConstants.getCurrentTimeMillis();

            //刷新逻辑
            for (SLGLeague league : leagues.values()) {
                league.tick();
            }
            for (SLGCity city : cities.values()) {
                city.tick();

                if(city.isNeedSync()){
                    city.setNeedSync(false);
                    syncData.addCities(city.genPb());
                }
            }

            //战况同步
            if(syncData.getAttackMsgsCount() > 0
                    || syncData.getCitiesCount() > 0
                    || syncData.getMarchesCount() > 0
                    || syncData.hasBattleStatus()
            ){
                syncData.setResult(Text.genOkServerRstInfo());
                PbProtocol.SLGBattlePushRst rst = syncData.build();
                syncData.clear();
                for(Map.Entry<Integer, List<Long> > entry : serverGroupedLids.entrySet()){
                    int sid = entry.getKey();
                    List<Long> lids = entry.getValue();

                    ServerCommandRequest req = CommandRequests.newServerCommandRequest("LeagueSLGGameCommandService.battlePushLeague");
                    TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, sid,req,
                            lids, this.index,rst);
                }
            }
        }
    }


    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public Map<Long, SLGLeague> getLeagues() {
        return leagues;
    }

    public void setLeagues(Map<Long, SLGLeague> leagues) {
        this.leagues = leagues;
    }

    public Map<Integer, SLGCity> getCities() {
        return cities;
    }

    public void setCities(Map<Integer, SLGCity> cities) {
        this.cities = cities;
    }

    private void settleTax(SLGArea area){
        //税银结算
        Map<Long, Integer> taxes = new HashMap<>();
        for(SLGCity city : cities.values()){
            if(city.getLid() > 0){
                SLGCityTemplate cityTemplate = city.getCityTemplate();
                SLGLeague league = leagues.get(city.getLid());
                if(cityTemplate.type != SLGCityTemplate.TYPE_大本营
                        || city.getTid() == league.getHomeCity()){

                    for(SLGPlayer player : league.getPlayers().values()){
                        int tax = city.calculateTax(player.getLastFarmTime());
                        if(tax > 0) {
                            int taxAmount = taxes.getOrDefault(player.getPid(), 0);
                            taxAmount += tax;
                            taxes.put(player.getPid(), taxAmount);
                        }
                    }
                }
            }
        }

        for(Map.Entry<Long, Integer> entry : taxes.entrySet()) {
            long pid = entry.getKey();
            int taxAmount = entry.getValue();
            GameLogger.slgDebug("Tax: " + pid + " amount = " + taxAmount);
        }

        if(taxes.size() > 0){
            //数据按照服务器分组
            Map<Integer, Map<Long, Integer>> serverTaxes = new HashMap<>();
            for(Map.Entry<Long, Integer> entry : taxes.entrySet()){
                int serverId = Player.getRealServerId(entry.getKey());
                Map<Long, Integer> serverMap = serverTaxes.getOrDefault(serverId, new HashMap<>());
                serverMap.put(entry.getKey(), entry.getValue());
                serverTaxes.put(serverId, serverMap);
            }

            for(Map.Entry<Integer, Map<Long, Integer>> entry : serverTaxes.entrySet()){
                CommandRequest req = CommandRequests.newServerCommandRequest("LeagueSLGGameCommandService.cityProduce");
                TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, entry.getKey(),req, -1, taxes);
            }
        }
    }

    public void settle(SLGArea area){

        //税银结算
        settleTax(area);

        List<SLGLeague> leagueIds = new ArrayList<>(leagues.values());
        //越早淘汰越靠后，分数越高越靠前
        Collections.sort(leagueIds, SLGLeague.scoreComparator);

        GameLogger.slgDebug("settle League order : " + leagueIds.stream().map(league -> league.getId()).collect(Collectors.toList()));

        Map<Long, Integer> scores = new HashMap<>();
        Map<Long, PbProtocol.SLGBattleFinishPushRst.Builder> finishBuilders = new HashMap<>();

        for(int i = 0; i < leagueIds.size(); i++){
            int rank = i+1;
            SLGLeague league = leagueIds.get(i);
            SLGBattleRewardTemplate template = LeagueSLGService.getBattleScoresRankMap().floorEntry(rank).getValue();
            scores.put(league.getId(), league.getCurrentScore() + template.points[this.level - 1]);

            PbProtocol.SLGBattleFinishPushRst.Builder finishBuilder = PbProtocol.SLGBattleFinishPushRst.newBuilder();
            int levelChange = calcLevelChange(rank);
            finishBuilder.setResult(Text.genOkServerRstInfo())
                    .setFieldIndex(this.index)
                    .setFieldLevel(this.level)
                    .setFirstLeague(leagueIds.get(0).getSimpleLeague())
                    .setLeagueRank(rank)
                    .setLevelChange(levelChange);
            finishBuilders.put(league.getId(), finishBuilder);

            if(this.level == 2 && levelChange == 1){
                //3级战场纳入竞猜目标
                area.getGuessLeagues().add(league);
            }
        }

        //帮派积分更新到排行榜
        for(Map.Entry<Long, Integer> entry : scores.entrySet()){
            SLGLeagueRankNode node = area.getRankNode(entry.getKey());
            if(node != null){
                SLGLeague league = leagues.get(entry.getKey());
                node.setScore(node.getScore() + entry.getValue());
                node.setFightingPower(league.getFightingPower());
                GameLogger.slgDebug("Settle League rank score : " + league.getId() + " score = " + node.getScore());

                PbProtocol.SLGBattleFinishPushRst.Builder finishBuilder = finishBuilders.get(entry.getKey());
                finishBuilder.setLeagueScoreAdd(entry.getValue())
                        .setLeagueScore(node.getScore());
            }
        }

        //累积据点占领数量
        for(SLGCity city : cities.values()){
            if(city.getLid() > 0){
                SLGLeague league = leagues.get(city.getLid());
                league.setOccupyCityCount(league.getOccupyCityCount() + 1);
            }
        }

        //计算信息推送到gs
        pushFinishInfo(finishBuilders);

        //清理谋士数据
        for(SLGLeague league : leagues.values()){
            league.getStrategies().clear();
        }
    }

    private void pushFinishInfo(Map<Long, PbProtocol.SLGBattleFinishPushRst.Builder> finishBuilders){
        Map<Integer, Map<Long, PbProtocol.SLGBattleFinishPushRst>> data = new HashMap<>();
        for(Map.Entry<Long, PbProtocol.SLGBattleFinishPushRst.Builder> entry : finishBuilders.entrySet()) {
            long leagueId = entry.getKey();
            PbProtocol.SLGBattleFinishPushRst b = entry.getValue().build();
            Map<Long, PbProtocol.SLGBattleFinishPushRst> rst = data.getOrDefault(League.getRealServerId(leagueId), new HashMap<>());
            data.put(League.getRealServerId(leagueId), rst);
            rst.put(leagueId, b);
        }

        for(Map.Entry<Integer, Map<Long, PbProtocol.SLGBattleFinishPushRst>> entry : data.entrySet()){
            int serverId = entry.getKey();
            Map<Long, PbProtocol.SLGBattleFinishPushRst> rstMap = entry.getValue();
            CommandRequest req = CommandRequests.newServerCommandRequest("LeagueSLGGameCommandService.battleFinishPush");
            TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, serverId,req, rstMap);
        }
    }

    /**
     * 等级变化
     * 1:升一级
     * 2:无变化
     * 3:降一级
     * 0:最后一天战斗无晋级降级
     */
    private int calcLevelChange(int rank){
        SLGGroupTemplate groupTemplate = LeagueSLGService.getGroups().get(this.area.getPlanId());
        int dayOfWeek = ServerConstants.getCurrentTimeLocalDateTime().getDayOfWeek().getValue();

        if (dayOfWeek == LeagueSLGService.getConstant().regroupDay1 - 1) {
            //周四
            if (rank <= groupTemplate.day1upgrade) {
                return 1;
            } else {
                return 2;
            }

        } else if (dayOfWeek == LeagueSLGService.getConstant().regroupDay2 - 1) {
            //周五
            if (rank <= groupTemplate.day2upgrade) {
                return 1;
            } else if (rank > groupTemplate.day2downgrade) {
                return 3;
            } else {
                return 2;
            }

        } else {
            return 0;
        }

    }

    public SLGLeague getLeagueByPid(long pid){
        for(SLGLeague league : leagues.values()){
            if(league.getPlayers().containsKey(pid)){
                return league;
            }
        }
        return null;
    }

    public SLGPlayer getPlayerByPid(long pid){
        for(SLGLeague league : leagues.values()){
            if(league.getPlayers().containsKey(pid)){
                return league.getPlayers().get(pid);
            }
        }
        return null;
    }

    /**
     * 是否可以行军
     * @param player 玩家id
     * @param cityIds 路径，最后一个点为目的地
     * @return
     */
    public int canMarch(SLGPlayer player, List<Integer> cityIds){
        //战场状态
        if(!LeagueSLGWorldManager.getInstance().getStatus().canPlayerOperate()){
            return Text.SLG_不在战斗时间;
        }
        //玩家状态-行军中
        if(player.isMarching()){
            return Text.SLG_行军中不能进行该操作;
        }
        //玩家状态-战斗中
        SLGCity city = cities.get(player.getCityId());
        if(city == null){
            return Text.SLG_已淘汰;
        }
        if(city.isFighting(player.getPid())){
            return Text.SLG_战斗中不能进行该操作;
        }

        SLGCity target = this.cities.get(cityIds.get(cityIds.size() - 1));
        if(target == null){
            return Text.数据异常;
        }

        //如果是从家里出发，则需要判断是否有足够的兵力
        SLGLeague league = getLeagueByPid(player.getPid());
        SLGCityTemplate cityTemplate = city.getCityTemplate();
        if(cityTemplate.type == SLGCityTemplate.TYPE_大本营 && city.getTid() == league.getHomeCity()){
            if(!player.getCamp().armyEnough(player)){
                return Text.SLG_兵力不足无法行军;
            }
        }

        //可达性检测
        if(!canArrive(player, cityIds)) {
            return Text.SLG_目标城池不可达;
        }

        return Text.没有异常;
    }

    private boolean canArrive(SLGPlayer player, List<Integer> cityIds){
        SLGCity currentCity = cities.get(player.getCityId());
        for(int i = 0; i < cityIds.size(); i++){
            int cityId = cityIds.get(i);
            SLGCityTemplate cityTemplate = currentCity.getCityTemplate();
            if(!cityTemplate.neighbors.containsKey(cityId)){
                return false;
            }else{
                //物理可达，还得看阵营是否相同
                SLGCity newOne = cities.get(cityId);
                if(i < cityIds.size() - 1){
                    if(newOne.getLid() != player.getLeague().getId()){
                        return false;
                    }
                }

                //目的地如果是非己方，免战状态不能去
                if(i == cityIds.size() - 1
                        && newOne.getLid() != player.getLeague().getId()
                        && LeagueSLGWorldManager.getInstance().isShieldCity(newOne.getCityTemplate().type)){
                       return false;
                }
                currentCity = newOne;
            }
        }
        return true;
    }

    public List<Integer> calcPath(long actorLid, int start, int target){
        if(start == target){
            return null;
        }

        //使用广度优先算法，让玩家途径最少的据点
        Map<Integer, Integer> path = new HashMap<>();
        Set<Integer> checkedList = new HashSet<>();
        Queue<Integer> todo = new LinkedList<>();
        todo.offer(start);

        while(!todo.isEmpty()){
            int current = todo.poll();
            SLGCity city = cities.get(current);
            if(city != null) {
                SLGCityTemplate cityTemplate = city.getCityTemplate();
                for(int neighbor : cityTemplate.neighbors.keySet()){
                    if(neighbor == target){
                        //找到路径
                        List<Integer> result = new ArrayList<>();
                        result.add(neighbor);
                        result.add(current);
                        Integer pre = path.get(current);
                        while(pre != null && pre != start){
                            result.add(pre);
                            pre = path.get(pre);
                        }
                        Collections.reverse(result);
                        return result;
                    }
                    if(!checkedList.contains(neighbor)){
                        SLGCity neighborCity = cities.get(neighbor);
                        if(neighborCity != null && neighborCity.getLid() == actorLid) {
                            todo.offer(neighbor);
                            path.put(neighbor, current);
                        }
                    }
                }
                checkedList.add(current);
            }
        }

        return null;
    }

    private long arriveCost(SLGPlayer player, List<Integer> cityIds){
        SLGCity currentCity = cities.get(player.getCityId());
        int cost = 0;
        for(int i = 0; i < cityIds.size(); i++){
            int cityId = cityIds.get(i);
            SLGCityTemplate cityTemplate = currentCity.getCityTemplate();
            cost += cityTemplate.neighbors.getOrDefault(cityId,0);//上一步已经完成可达性检测，此处不再拦截，防止热更之类的导致卡死
            currentCity = cities.get(cityId);
        }
        cost = cost * LeagueSLGService.getConstant().baseRunTime * 1000;
        //谋略修正
        SLGLeague league = player.getLeague();
        cost = league.marchTimeModify(cost);
        return cost;
    }

    public void march(SLGPlayer player, List<Integer> cityIds){
        SLGCity oldCity = cities.get(player.getCityId());
        SLGCity newCity = cities.get(cityIds.get(cityIds.size() - 1));
        long cost = arriveCost(player, cityIds);

        oldCity.removePlayer(player);
        player.setArriveTime(ServerConstants.getCurrentTimeMillis() + cost);

        List<Integer> path = new ArrayList<>(cityIds);
        //存储玩家路径，用于客户端表现
        path.add(0, oldCity.getTid());
        player.setMarchPath(path);
//        player.setStatusNeedSync(true); 个人行军数据从公共行军数据中解析

        //加入新据点
        newCity.addPlayer(player);
        GameLogger.slgDebug("March: " + player.getPid() + " from " + oldCity.getTid() + " to " + newCity.getTid() + " path = " + path);

        //全局广播
        player.getLeague().getField().getSyncData().addMarches(player.genSLGMarchBuilder());
    }

    public PbProtocol.SLGBattlePushRst.Builder getSyncData() {
        return syncData;
    }

    public void setSyncData(PbProtocol.SLGBattlePushRst.Builder syncData) {
        this.syncData = syncData;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public SLGArea getArea() {
        return area;
    }

    public void setArea(SLGArea area) {
        this.area = area;
    }

    public void printInfo(){
        SystemLogger.info("SLG BattleField: index = " + index + " level = " + level);
        for(SLGLeague league : leagues.values()){
            SystemLogger.info("SLG league: " + league.getId() + " homeCity = " + league.getHomeCity() + " players = " + league.getPlayers().keySet());
        }
    }
}

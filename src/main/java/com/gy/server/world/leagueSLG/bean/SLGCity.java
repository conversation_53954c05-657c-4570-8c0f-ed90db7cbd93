package com.gy.server.world.leagueSLG.bean;

import com.baidu.bjf.remoting.protobuf.annotation.Ignore;
import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.command.CommandRequest;
import com.gy.server.core.command.PlayerCommandRequest;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.leagueSLG.LeagueSLGService;
import com.gy.server.game.leagueSLG.template.SLGCityTemplate;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.player.Player;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbSLG;
import com.gy.server.world.leagueSLG.mainStatus.impl.LeagueSLGMatchingHandler;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;

import java.util.*;

/**
 * @program: tl_game_4
 * @description: 据点
 * @author: Huang.Xia
 * @create: 2024/11/26
 **/
public class SLGCity {
    /**
     * 据点模版id
     */
    @Protobuf(order = 1)
    private int tid;
    /**
     * 当前所属军团
     */
    @Protobuf(order = 2)
    private long lid;

    /**
     * 开始生成时间
     */
    @Protobuf(order = 3)
    private long startProduceTime;

    /**
     * 守卫
     */
    @Protobuf(order = 4)
    private Map<Integer, SLGGuard> guards = new HashMap<>();

    /**
     * 进攻、防守玩家都在这里
     */
    @Ignore
    private List<SLGPlayer> players = new ArrayList<>();

    /**
     * 战斗队列
     * 不存储，丢失就当作没发起战斗
     */
    @Ignore
    private List<SLGFight> fights = new ArrayList<>();

    /**
     * 谋士id
     */
    @Protobuf(order = 6)
    private int strategyId;

    @Ignore
    private SLGBattleField field;

    /**
     * 是否需要同步给客户端
     */
    @Ignore
    private boolean needSync = false;

    /**
     * 详情同步信息
     */
    @Ignore
    private PbProtocol.SLGCityDetailPushRst.Builder detailSync = PbProtocol.SLGCityDetailPushRst.newBuilder();



    public void init(SLGBattleField field){
        this.field = field;

        for(SLGLeague league : field.getLeagues().values()){
            for(SLGPlayer player : league.getPlayers().values()){
                if(player.getCityId() == this.tid){
                    players.add(player);
                }
            }
        }
    }

    public void beforeSave(){

    }

    public SLGCity(int tid, SLGBattleField field) {
        this.tid = tid;
        this.field = field;
    }

    public SLGCity() {}

    public void initGuard(int count, int maxHp){
        for(int i = 1; i <= count; i++){
            guards.put(i, new SLGGuard(i, maxHp));
        }
    }

    public void addGuard(int count){
        SLGCityTemplate cityTemplate = LeagueSLGService.getCities().get(this.tid);
        int totalCount = cityTemplate.guardAmount + count;
        for(int i = cityTemplate.guardAmount + 1; i <= totalCount; i++ ) {
            SLGGuard guard = new SLGGuard(i, cityTemplate.guardArmy);
            guards.put(i, guard);
            this.detailSync.addNewUnits(guard.generateAttackerBuilder());
        }

        needSync = true;
    }

    public boolean isNeedSync() {
        return needSync;
    }

    public void setNeedSync(boolean needSync) {
        this.needSync = needSync;
    }

    public void removeGuard(int count){
        //至少留一个守卫
        int removeCount = Math.min(count, guards.size() - 1);
        Iterator<SLGGuard> it = guards.values().iterator();
        while(it.hasNext()){
            SLGGuard guard = it.next();
           if(removeCount <= 0){
               break;
           }

           //战斗中的守卫不能移除
           if(!isFighting(guard)){
               it.remove();
               removeCount--;
               this.detailSync.addRemovedUnitIds(guard.getId());
           }

        }
        needSync = true;
    }

    public void addPlayer(SLGPlayer player) {
        players.add(player);
        if(players.size() > 1){
            Collections.sort(this.players, SLGPlayer.medalComparator);

            if(players.indexOf(player) < 3){
                this.needSync = true;
            }
        }
        player.setCityId(this.tid);

        this.detailSync.addNewUnits(player.genSLGAttackerBuilder(player.getLeague().getId() != this.lid));
    }

    public void removePlayer(SLGPlayer player){
        if(players.indexOf(player) < 3){
            this.needSync = true;
        }
        players.remove(player);
        player.setCityId(0);

        this.detailSync.addRemovedUnitIds(player.getPid());
    }

    public int getTid() {
        return tid;
    }

    public void setTid(int tid) {
        this.tid = tid;
    }

    public long getLid() {
        return lid;
    }

    public void setLid(long lid) {
        this.lid = lid;
    }

    public long getStartProduceTime() {
        return startProduceTime;
    }

    public void setStartProduceTime(long startProduceTime) {
        this.startProduceTime = startProduceTime;
    }

    public List<SLGPlayer> getPlayers() {
        return players;
    }

    public void setPlayers(List<SLGPlayer> players) {
        this.players = players;
    }

    public List<SLGFight> getFights() {
        return fights;
    }

    public void addFight(SLGFight fight){
        this.fights.add(fight);

        if(this.fights.size() > 1){
            //从无战斗到有战斗
            this.needSync = true;
        }

        this.detailSync.addNewFights(fight.genPb());
    }

    public PbProtocol.SLGCityDetailPushRst.Builder getDetailSync() {
        return detailSync;
    }

    public void setDetailSync(PbProtocol.SLGCityDetailPushRst.Builder detailSync) {
        this.detailSync = detailSync;
    }

    public void setFights(List<SLGFight> fights) {
        this.fights = fights;
    }

    public int getStrategyId() {
        return strategyId;
    }

    public void setStrategyId(int strategyId) {
        this.strategyId = strategyId;
    }

    /**
     * 更新谋士
     */
    public void updateStrategy(int strategyId){
        this.strategyId = strategyId;
    }

    public boolean isFighting(long pid){
        for(SLGFight fight : fights){
            if(fight.getAtk().getPid() == pid){
                return true;
            }

            if(fight.getDefPlayer() != null){
                if(fight.getDefPlayer().getPid() == pid){
                    return true;
                }
            }
        }

        return false;
    }

    public SLGCityTemplate getCityTemplate(){
        return LeagueSLGService.getCities().get(tid);
    }

    public Map<Integer, SLGGuard> getGuards() {
        return guards;
    }

    public void setGuards(Map<Integer, SLGGuard> guards) {
        this.guards = guards;
    }


    public boolean isFighting(SLGPlayer player){
        for(SLGFight fight : fights){
            if(fight.getAtk().getPid() == player.getPid()){
                return true;
            }

            if(fight.getDefPlayer() != null){
                if(fight.getDefPlayer().getPid() == player.getPid()){
                    return true;
                }
            }
        }
        return false;
    }

    public boolean isFighting(SLGGuard guard){
        for(SLGFight fight : fights){
            if(fight.getDefGuard() != null && fight.getDefGuard().getId() == guard.getId()){
                return true;
            }
        }
        return false;
    }

    public SLGPlayer getPlayerByPid(long pid){
        for(SLGPlayer player : players){
            if(player.getPid() == pid){
                return player;
            }
        }
        return null;
    }

    public SLGPlayer getRandomDefencePlayer(SLGPlayer atk){
        if (atk.getLeague().getId() == this.lid) {
            //进攻方为防守方
            for(SLGPlayer player : players) {
                if (player.getLeague().getId() != atk.getLeague().getId()) {
                    return player;
                }
            }
        } else {
            //进攻方为进攻方

            //先打第三方
            for(SLGPlayer player : players) {
                if (player.getLeague().getId() != atk.getLeague().getId()
                        && player.getLeague().getId() != this.lid) {
                    return player;
                }
            }

            //再打防守方
            for(SLGPlayer player : players) {
                if (player.getLeague().getId() != atk.getLeague().getId()) {
                    return player;
                }
            }
        }

        return null;
    }

    public void tick(){
        Iterator<SLGFight> iterator = fights.iterator();
        boolean remove = false;
        while(iterator.hasNext()) {
            SLGFight fight = iterator.next();
            if(fight.isFinish()){
                iterator.remove();
                remove = true;

                //结算战斗
                if(fight.isFinish()){
                    fight.settle(this, field);
                }
            }
        }

        if(remove && this.fights.size() == 0){
            //战斗结束，更新城市状态
            this.needSync = true;
        }

        detailPushCheck();
    }

    public void detailPushCheck(){
        if(this.detailSync.getNewFightsCount() > 0
                || this.detailSync.getNewUnitsCount() > 0
                || this.detailSync.getRemovedUnitIdsCount() > 0){

            Set<Long> pids = new HashSet<>();
            field.getPlayerCityWatch().forEach((k,v)->{
                if(v == this.tid){
                    pids.add(k);
                }
            });
            PbProtocol.SLGCityDetailPushRst rst = this.detailSync.setResult(Text.genOkServerRstInfo()).build();
            this.detailSync.clear();
            if(pids.size() > 0){
                //分服务器发送
                Map<Integer, List<Long>> serverPids = new HashMap<>();
                for(long pid : pids) {
                    int serverId = Player.getRealServerId(pid);
                    List<Long> ids = serverPids.getOrDefault(serverId, new ArrayList<>());
                    ids.add(pid);
                    serverPids.put(serverId, ids);
                }

                CommandRequest req = CommandRequests.newServerCommandRequest("LeagueSLGGameCommandService.cityDetailPush");
                for(Map.Entry<Integer, List<Long>> entry : serverPids.entrySet()){
                    TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, entry.getKey(), req, rst, entry.getValue());
                }
            }
        }
    }

    /**
     * 检查城池中是否还有活着的防守玩家
     * 1. 与城池lid相同
     * 2. 已经到达
     * @return
     */
    private boolean hasDefPlayer(){
         for(SLGPlayer player : players){
             if(player.getLeague().getId() == this.lid && player.getArriveTimeLeft() <= 0 && player.getArmy() > 0){
                 return true;
             }
         }

         return false;
    }

    /**
     * 占领检测
     */
    public void occupationCheck(SLGPlayer atk){
        if(LeagueSLGMatchingHandler.debug){
            if(this.guards.size() > 20){
                this.removeGuard(this.guards.size() - 20);
                GameLogger.slgDebug("测试模式下仅保留少量守卫  cityId ： " + this.tid );
            }else{
                GameLogger.slgDebug("剩余守卫数量： " + this.guards.size() + "  cityId ： " + this.tid );
            }
        }
        if(this.guards.size() == 0 && !hasDefPlayer()){
            if(this.lid > 0) {
                SLGLeague league = field.getLeagues().get(this.lid);
                league.lostCity(this, atk);

                league.addCityOccupiedCount(atk.getLeague().getId());
                if(this.getCityTemplate().type == SLGCityTemplate.TYPE_大本营){
                    league.addHomeOccupiedCount(atk.getLeague().getId());
                }

                //据点被抢记录
                league.addFightRecord(3, this.tid, atk.getLeague().getId());
                //抢占据点记录
                atk.getLeague().addFightRecord(2, this.tid, league.getId());

                GameLogger.slgDebug("据点被占领：" + this.tid + "  原有军团：" + league.getId());
            }else{
                //占领中立据点记录
                atk.getLeague().addFightRecord(1, this.tid, -1);
            }

            this.lid = atk.getLeague().getId();
            SLGLeague league = field.getLeagues().get(this.lid);
            league.winCity(this);

            //从新生成守卫
            SLGCityTemplate cityTemplate = LeagueSLGService.getCities().get(this.tid);
            this.initGuard(cityTemplate.guardAmount + league.guardCountAdd(), cityTemplate.guardArmy);
            this.needSync = true;

            //开始税银生产
            this.startProduceTime = ServerConstants.getCurrentTimeMillis();

            GameLogger.slgDebug("占领据点：" + this.tid + "  占领者：" + atk.getPid() + "  占领军团：" + atk.getLeague().getId());
        }
    }

    public int calculateTax(long playerFarmTime){
        long time = ServerConstants.getCurrentTimeMillis() - Math.max(this.startProduceTime, playerFarmTime);
        time = Math.min(time, 10 * 60 *60 * 1000); //最长10小时
        int tax = (int) (getCityTemplate().production[field.getLevel() - 1] * (time / 1000f/ 60));
        return Math.max(tax, 0);
    }

    public PbSLG.SLGCity.Builder genPb(){
        PbSLG.SLGCity.Builder builder = PbSLG.SLGCity.newBuilder();
        builder.setCityId(this.tid)
                .setFighting(this.fights.size() > 0)
                .setLeftGuardCount(this.guards.size())
                .setLeagueId(this.lid);
        if(this.players.size() > 0){
            for(int i = 0; i < this.players.size() && i < 3; i++){
                SLGPlayer player = this.players.get(i);
                builder.addTop3Pid(player.getPid());
            }
        }
        return builder;
    }

    public List<PbSLG.SLGMarch> getMarchPbs(){
        if(this.players.size() > 0){
            List<PbSLG.SLGMarch> marchPbs = new ArrayList<>();
            for(SLGPlayer player : this.players){
                if(player.getArriveTimeLeft() > 0){
                    PbSLG.SLGMarch.Builder marchPb = player.genSLGMarchBuilder();
                    marchPbs.add(marchPb.build());
                }

            }
            return marchPbs;
        }else{
            return null;
        }
    }

    public PbSLG.SLGCityProduce.Builder genCityProducePb(long playerFarmTime, SLGLeague league){
        PbSLG.SLGCityProduce.Builder builder = PbSLG.SLGCityProduce.newBuilder();
        if(this.getCityTemplate().type == SLGCityTemplate.TYPE_大本营 && this.tid != league.getHomeCity()){
            //地方大本营，不生产税银
            builder.setCityId(this.tid)
                    .setProduceStartTime(0);
        }else {
            builder.setCityId(this.tid)
                    .setProduceStartTime(Math.max(this.startProduceTime, playerFarmTime));
        }
        return builder;
    }

    public void clearLeague(long leagueId){
        if(this.lid == leagueId){
            this.lid = -1;
            needSync = true;
        }

        if(this.players.size() > 0){
            List<SLGPlayer> removes = new ArrayList<>();
            for (SLGPlayer player : this.players){
                if(player.getLeague().getId() == leagueId){
                    removes.add(player);
                }
            }


            for(SLGPlayer player : removes){
                this.removePlayer(player);
            }
        }
    }

    /**
     * 自动挑选目标
     */
    public long getAutoTarget(long atkLid, int type){
        Collections.sort(this.players, SLGPlayer.armyComparator);

        if(type == -1){
            //自动占领，优先打防守方
            if(this.players.size() > 1){
                for(SLGPlayer player : this.players) {
                    if (player.getLeague().getId() == this.lid && player.getArriveTimeLeft() <= 0 && !isFighting(player.getPid()) && player.getArmy() > 0) {
                        return player.getPid();
                    }
                }
            }
        }else{
            //自动清场，优先打第三方
            if(this.players.size() > 1){
                for(SLGPlayer player : this.players){
                    if(player.getLeague().getId() != atkLid && player.getLeague().getId() != this.lid  && player.getArriveTimeLeft() <= 0
                            && !isFighting(player.getPid()) && player.getArmy() > 0){
                        return player.getPid();
                    }
                }
            }
        }

        //打任何人
        if(this.players.size() > 1){
            for(SLGPlayer player : this.players){
                if(player.getLeague().getId() != atkLid && player.getArriveTimeLeft() <= 0 && !isFighting(player.getPid()) && player.getArmy() > 0){
                    return player.getPid();
                }
            }
        }

        //打守卫
        if(this.guards.size() > 0){
            for(SLGGuard guard : this.guards.values()){
                if(!isFighting(guard)){
                    return -1 * guard.getId();
                }
            }
        }
        return 0;
    }

    /**
     * 自动战斗
     * @param modeType -1 占领  -2 清场
     */
    public void autoFight(SLGPlayer atk, int modeType){
        long targetId = getAutoTarget(atk.getLeague().getId(), modeType);
        boolean isGuard = false;
        if(targetId < 0){
            isGuard = true;
            targetId = -targetId;
        }

        if(targetId <= 0){
            return;
        }

        if(isGuard){
            SLGGuard guard = getGuards().get(Integer.valueOf((int) targetId));
            SLGFight fight = new SLGFight(atk, guard);
            addFight(fight);

            //战况同步
            SLGLeague league = field.getLeagues().get(getLid());
            if(league != null){
                league.addCityAttackedMsg(getTid());
            }
        }else{
            SLGPlayer target = getPlayerByPid(targetId);

            SLGFight fight = new SLGFight(atk, target);
            addFight(fight);

            //战况同步
            if(target.getLeague().getId() == getLid()){
                //防守方被攻击才会同步
                target.getLeague().addCityAttackedMsg(getTid());
            }
            target.addAttackedMsg();
        }
    }


}

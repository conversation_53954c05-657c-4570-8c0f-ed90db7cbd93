package com.gy.server.world.leagueSLG.bean;

import com.gy.server.game.leagueSLG.LeagueSLGService;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: tl_game_4
 * @description:
 * @author: <PERSON><PERSON>
 * @create: 2024/12/13
 **/
public class SLGArmyExpendCount {
    public static Comparator<SLGArmyExpendCount> comparator = new SLGArmyExpendCountComparator();

    private long playerId;

    private Map<Integer, Integer> count = new HashMap<>();

    private int[] orderedCount;

    /**
     * 按照物品id降序，整理好对应的操作数量
     */
    public void sortPrepare() {
        List<Integer> itemIds = new ArrayList<>(LeagueSLGService.getConstant().departmentExpItems.keySet());
        Collections.sort(itemIds, (o1, o2)->{
            return Integer.compare(o2, o1);
        });
        orderedCount = new int[itemIds.size()];
        for (int i = 0; i < itemIds.size(); i++) {
            orderedCount[i] = count.getOrDefault(itemIds.get(i), 0);
        }
    }

    public SLGArmyExpendCount(long playerId) {
        this.playerId = playerId;
    }

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public Map<Integer, Integer> getCount() {
        return count;
    }

    public void setCount(Map<Integer, Integer> count) {
        this.count = count;
    }

    public void addCount(int itemId, int count) {
        this.count.put(itemId, this.count.getOrDefault(itemId, 0) + count);
    }

    public int[] getOrderedCount() {
        return orderedCount;
    }

    public void setOrderedCount(int[] orderedCount) {
        this.orderedCount = orderedCount;
    }

    private static class SLGArmyExpendCountComparator implements Comparator<SLGArmyExpendCount> {
        @Override
        public int compare(SLGArmyExpendCount o1, SLGArmyExpendCount o2) {
            for(int i = 0; i < o1.orderedCount.length; i++){
                if(o1.orderedCount[i]!= o2.orderedCount[i]){
                    return Integer.compare(o2.orderedCount[i], o1.orderedCount[i]);
                }
            }

            return Long.compare(o2.playerId, o1.playerId);
        }
    }
}

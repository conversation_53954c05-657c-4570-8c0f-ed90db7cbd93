package com.gy.server.world.leagueSLG.mainStatus;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.leagueSLG.LeagueSLGGameGlobalData;
import com.gy.server.game.leagueSLG.LeagueSLGService;
import com.gy.server.packet.PbLeague;
import com.gy.server.packet.PbSLG;
import com.gy.server.utils.time.DateTimeUtil;
import com.gy.server.world.leagueSLG.LeagueSLGWorldGlobalData;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @program: tl_game_4
 * @description: 状态类型
 * @author: Huang.<PERSON>
 * @create: 2024/11/26
 **/
public enum SLGNodeType {


    /**
     * 未开启
     */
    Idle(0, new int[]{0, 0}, 0),

    /**
     * 休赛： 周一到周三匹配前
     */
    Rest(100, new int[]{16, 0}, 3),

    /**
     * 匹配： 周三匹配开始
     */
    Matching(200, new int[]{18, 0}, 3),

    /**
     * 匹配结果展示
     */
    MatchResult(201, new int[]{0, 0}, 4),

    /**
     * 战斗： 战斗3天
     */
    Battle_00(301, new int[]{10, 0}, 4, 5, 6),
    Battle_10(302, new int[]{12, 0}, 4, 5, 6),
    Battle_12(303, new int[]{16, 0}, 4, 5, 6),
    Battle_16(304, new int[]{18, 0}, 4, 5, 6),
    Battle_18(305, new int[]{19, 0}, 4, 5, 6),
    Battle_19(306, new int[]{20, 0}, 4, 5, 6),
    Battle_20(307, new int[]{21, 59}, 4, 5, 6),
    Battle_2159(308, new int[]{22, 0}, 4, 5, 6),
    Battle_22(309, new int[]{0, 0}, 5, 6, 7),

    /**
     * 领奖日： 周天
     */
    Reward(400, new int[]{0, 0}, 0)

    ;


    private int id;

    /**
     * 结束时间，小时：分钟
     */
    private int[] endTime = new int[2];

    /**
     * 结束日（星期几）
     */
    private List<Integer> endDay = new ArrayList<>();

    SLGNodeType(int id, int[] endTime, Integer endDay) {
        this.id = id;
        this.endTime = endTime;
        this.endDay.add(endDay);
    }

    SLGNodeType(int id, int[] endTime, Integer... endDay) {
        this.id = id;
        this.endTime = endTime;
        for (int day : endDay) {
            this.endDay.add(day);
        }
    }

    public int getId() {
        return id;
    }

    public static SLGNodeType valueOf(int id) {
        for (SLGNodeType type : SLGNodeType.values()) {
            if (type.getId() == id) {
                return type;
            }
        }
        return null;
    }

    public boolean isBattle(){
        return this.getId() > Matching.id && this.getId() < Battle_22.id;
    }

    public boolean canPlayerOperate(){
        return this.getId() >= Battle_10.id && this.getId() < Battle_2159.id;
    }

    public boolean canBattleTick(){
        return this.getId() >= Battle_10.id && this.getId() < Battle_22.id;
    }
    public boolean canFightFastMode(){
        return this.getId() >= Battle_19.id && this.getId() < Battle_2159.id;
    }

    public boolean canEnter(){
        return this.getId() > Matching.id;
    }

    public boolean isEnd(){
        if(this == Idle){
            //周一到周三匹配之前，才能进入休息状态。不然没法走完整一个赛季。
            if(!Rest.isEnd()){
                return true;
            }
        }else{
            return getLeftTimeSeconds() <= 0;
        }

        return false;
    }

    public long getLeftTimeSeconds(){
        LocalDateTime now = DateTimeUtil.toLocalDateTime(ServerConstants.getCurrentTimeDate());
        LocalDateTime end = getEndTime();
        if(end.isAfter(now)){
            return (long) Math.ceil((DateTimeUtil.toMillis(end) - DateTimeUtil.toMillis(now)) / 1000f);
        }
        return 0;
    }

    public LocalDateTime getEndTime(){
        LocalDateTime now = DateTimeUtil.toLocalDateTime(ServerConstants.getCurrentTimeDate());
        LocalDateTime end = now.withHour(endTime[0]).withMinute(endTime[1]).withSecond(0).withNano(0);
        int index = 0;
        if(this.id >= Battle_00.id && this.id < Reward.id){
            //战斗日
            LeagueSLGWorldGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueWorldSLG);
            if(globalData == null){
                index = 0;
            }else{
                index = globalData.getCombatDayIndex();
            }
        }
        int endDayValue = this.endDay.get(index);
        if(this == Reward){
            if(now.getDayOfWeek().getValue() > MatchResult.endDay.get(0)){
                //下周一0点结束
                endDayValue = 8;
            }else{
                //本周一0点结束
                endDayValue = 1;
            }
        }
        int day = endDayValue - now.getDayOfWeek().getValue();
        end = end.plusDays(day);
        return end;
    }

    public LocalDateTime getEndTime(int index){
        LocalDateTime now = DateTimeUtil.toLocalDateTime(ServerConstants.getCurrentTimeDate());
        LocalDateTime end = now.withHour(endTime[0]).withMinute(endTime[1]).withSecond(0).withNano(0);
        int day = endDay.get(index) - now.getDayOfWeek().getValue();
        end = end.plusDays(day);
        return end;
    }

    public LocalDateTime getEndTimeToday(){
        LocalDateTime now = DateTimeUtil.toLocalDateTime(ServerConstants.getCurrentTimeDate());
        LocalDateTime end = now.withHour(endTime[0]).withMinute(endTime[1]).withSecond(0).withNano(0);
        return end;
    }

    public static void main(String[] args) {
        for(int i = 0; i < 7; i++){
            ServerConstants.updateSystemTimeOffset(i * 24 * 3600);
            for (SLGNodeType type : SLGNodeType.values()) {
                System.out.println(type + " : " + type.getEndTime());
            }
        }
    }

    public static SLGNodeType getSLGNode(int id){
        for(SLGNodeType type : SLGNodeType.values()){
            if(type.getId() == id){
                return type;
            }
        }
        return null;
    }

    public PbSLG.SLGBattleStatus generateBattleStatus(){
        PbSLG.SLGBattleStatus.Builder b =PbSLG.SLGBattleStatus.newBuilder().setStatus(getId()).setStatusLeftSeconds(getLeftTimeSeconds());
        LeagueSLGWorldGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueWorldSLG);
        if(globalData.getCombatDayIndex() < 0) {
            b.setBattleDay(0);
        }else{
            b.setBattleDay(globalData.getCombatDayIndex() + 1);
        }
        return b.build();
    }

    public PbLeague.LeaguePlayInfo.SLGData.Builder genPlayInfo(long lid){
        com.gy.server.packet.PbLeague.LeaguePlayInfo.SLGData.Builder b = PbLeague.LeaguePlayInfo.SLGData.newBuilder();
        if(this == Idle){
            b.setState(0);
            //具体开启时间由上层逻辑控制，目前固定为下周
            b.setTime(DateTimeUtil.toMillis(SLGNodeType.Reward.getEndTime()));
        }else if(this == Rest || this == Matching){
            b.setState(1);
            b.setTime(DateTimeUtil.toMillis(SLGNodeType.Matching.getEndTime()));
        }else if(this == MatchResult){
            LeagueSLGGameGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueGameSLG);
            if(globalData != null && globalData.getGroupData().get(lid) != null){
                b.setState(3);
                b.setTime(DateTimeUtil.toMillis(SLGNodeType.MatchResult.getEndTime()));
            }else{
                b.setState(2);
            }
        }else if(this.id >= Battle_00.id && this.id < Reward.id){
            b.setState(4);
        }else{
            b.setState(5);
        }

        return b;
    }


}

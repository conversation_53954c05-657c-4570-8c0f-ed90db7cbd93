package com.gy.server.world.leagueSLG.bean;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.gy.server.packet.PbSLG;

/**
 * @program: tl_game_4
 * @description: 守卫
 * @author: <PERSON><PERSON>
 * @create: 2024/11/26
 **/
public class SLGGuard {
    @Protobuf(order = 1)
    private int id;
    @Protobuf(order = 2)
    private int army;

    public SLGGuard() {

    }

    public SLGGuard(int id, int hp) {
        this.id = id;
        this.army = hp;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getArmy() {
        return army;
    }

    public void setArmy(int army) {
        this.army = army;
    }

    public PbSLG.SLGAttacker.Builder generateAttackerBuilder() {
        PbSLG.SLGAttacker.Builder builder = PbSLG.SLGAttacker.newBuilder();
        builder.setGuardId(id)
                .setIsPlayer(false)
                .setIsAttacker(false)
                .setLeftArmy(army);
        return builder;
    }
}

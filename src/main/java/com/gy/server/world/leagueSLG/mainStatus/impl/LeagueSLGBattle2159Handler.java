package com.gy.server.world.leagueSLG.mainStatus.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.battleRule.base.BaseBattleInfo;
import com.gy.server.core.battleRule.base.IBattleStatusDeal;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.leagueSLG.LeagueSLGService;
import com.gy.server.utils.time.DateTimeUtil;
import com.gy.server.world.leagueSLG.LeagueSLGHelper;
import com.gy.server.world.leagueSLG.LeagueSLGWorldGlobalData;
import com.gy.server.world.leagueSLG.mainStatus.SLGNodeType;

import java.time.LocalDateTime;

/**
 * @program: tl_game_4
 * @description: 战斗中
 * @author: Huang.<PERSON>
 * @create: 2024/11/26
 **/
public class LeagueSLGBattle2159<PERSON><PERSON><PERSON> implements IBattleStatusDeal<BaseBattleInfo> {


    @Override
    public void init(BaseBattleInfo battleInfo) {
        LeagueSLGHelper.notifyStatus2allServers(this.nowStatus());
    }

    @Override
    public void deal(BaseBattleInfo battleInfo) {

    }

    @Override
    public boolean finish(BaseBattleInfo battleInfo) {
        SLGNodeType nodeType = SLGNodeType.getSLGNode(battleInfo.getStatus());
        return nodeType.isEnd();
    }

    @Override
    public int nowStatus() {
        return SLGNodeType.Battle_2159.getId();
    }

    @Override
    public int nextStatus(BaseBattleInfo battleInfo) {
        LeagueSLGWorldGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueWorldSLG);
        if(globalData.getCombatDayIndex() >= 2){
            //开始结算
            return SLGNodeType.Reward.getId();
        }else {
            //进入下一场战斗
            return SLGNodeType.Battle_22.getId();
        }
    }

    @Override
    public long getStatusDurationTime(BaseBattleInfo battleInfo) {
        return -1;
    }
}

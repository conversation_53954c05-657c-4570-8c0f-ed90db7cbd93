package com.gy.server.world.leagueSLG.mainStatus;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.battleRule.AbstractBattleManager;
import com.gy.server.core.battleRule.base.BaseBattleInfo;
import com.gy.server.core.battleRule.enums.BattleEnums;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.log.GameLogger;
import com.gy.server.utils.time.DateTimeUtil;
import com.gy.server.world.common.NodeCommonManager;
import com.gy.server.world.leagueSLG.LeagueSLGWorldGlobalData;

import java.util.Set;

/**
 * @program: tl_game_4
 * @description: SLG跨服管理器. 只有主world运行
 * @author: Huang.<PERSON>
 * @create: 2024/11/26
 **/
public class LeagueSLGWorldStatusManager extends AbstractBattleManager<BaseBattleInfo> {

    private static Set<Integer> allWorldIds;


    public LeagueSLGWorldStatusManager() {
        super(true);
    }



    @Override
    protected void saveSonStatus(BaseBattleInfo baseBattleInfo, int currentStatus) {

    }

    @Override
    public boolean isGameServer() {
        return false;
    }

    @Override
    public boolean isWorldServer() {
        return true;
    }

    @Override
    public BaseBattleInfo getBattleInfo() {
        LeagueSLGWorldGlobalData worldData = GlobalDataManager.getData(GlobalDataType.leagueWorldSLG);
        if(worldData.getBaseInfo() == null){
            BaseBattleInfo baseBattleInfo = new BaseBattleInfo(BattleEnums.league_slg);
            baseBattleInfo.setStatus(SLGNodeType.Idle.getId());
            baseBattleInfo.setSeasonId(1);
            baseBattleInfo.setStatusStartTime(ServerConstants.getCurrentTimeMillis());
            worldData.setBaseInfo(baseBattleInfo);
        }
        return worldData.getBaseInfo();
    }

    protected void printChangeStatus(int currentStatus, int nextStatus, long statusStartTime) {
        GameLogger.slgDebug(String.format("帮派SLG状态切换：%s -> %s, %s 开始时间: %s, 切换时间: %s", SLGNodeType.valueOf(currentStatus), SLGNodeType.valueOf(nextStatus),
                SLGNodeType.valueOf(currentStatus), DateTimeUtil.toLocalDateTime(statusStartTime), ServerConstants.getCurrentTimeLocalDateTime()));
    }

    @Override
    public void saveBattleInfo(BaseBattleInfo battleInfo) {

    }

    @Override
    public void start() {
        try {
            super.initClazz();
        } catch (IllegalAccessException | InstantiationException e) {
            e.printStackTrace();
        }
    }

    public static Set<Integer> getAllWorldIds() {
        if (allWorldIds == null) {
            allWorldIds = NodeCommonManager.getInstance().getAllWorldServerIds();
        }
        return allWorldIds;
    }
}

package com.gy.server.world.leagueSLG.mainStatus.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.battleRule.base.BaseBattleInfo;
import com.gy.server.core.battleRule.base.IBattleStatusDeal;
import com.gy.server.core.callback.response.CallbackResponse;
import com.gy.server.core.log.SystemLogger;
import com.gy.server.game.leagueSLG.LeagueSLGService;
import com.gy.server.game.log.GameLogger;
import com.gy.server.utils.time.DateTimeUtil;
import com.gy.server.world.leagueSLG.LeagueSLGHelper;
import com.gy.server.world.leagueSLG.LeagueSLGWorldManager;
import com.gy.server.world.leagueSLG.mainStatus.LeagueSLGWorldStatusManager;
import com.gy.server.world.leagueSLG.mainStatus.SLGNodeType;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.rpc.TLMessageCallbackTask;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * @program: tl_game_4
 * @description: 战斗中
 * @author: Huang.Xia
 * @create: 2024/11/26
 **/
public class LeagueSLGBattle00Handler implements IBattleStatusDeal<BaseBattleInfo> {

    public static volatile Set<Integer> refreshFinishedWorldIds = new HashSet<>();

    private long lastCheckTime = 0;

    @Override
    public void init(BaseBattleInfo battleInfo) {
        refreshFinishedWorldIds.clear();
        LeagueSLGWorldManager.getInstance().updateCombatDayIndex();
        LeagueSLGHelper.notifyStatus2allServers(this.nowStatus());
    }

    @Override
    public void deal(BaseBattleInfo battleInfo) {
        if (ServerConstants.getCurrentTimeMillis() - lastCheckTime > 5000) {
            lastCheckTime = ServerConstants.getCurrentTimeMillis();
            if (refreshFinishedWorldIds.size() < LeagueSLGWorldStatusManager.getAllWorldIds().size()) {
                Set<Integer> sids = new HashSet<>(LeagueSLGWorldStatusManager.getAllWorldIds());
                sids.removeAll(refreshFinishedWorldIds);
                for (int sid : sids) {
                    TLBase.getInstance().getRpcUtil()
                            .sendToNodeWithCallBack(new DealMessageCallbackTask(sid), ServerType.WORLD, sid, CommandRequests.newServerCommandRequest("LeagueSLGWorldCommandService.refreshCheck"));
                }
            }
        }
    }

    @Override
    public boolean finish(BaseBattleInfo battleInfo) {
        SLGNodeType nodeType = SLGNodeType.getSLGNode(battleInfo.getStatus());
        return nodeType.isEnd() && refreshFinishedWorldIds.size() >= LeagueSLGWorldStatusManager.getAllWorldIds().size();
    }

    @Override
    public int nowStatus() {
        return SLGNodeType.Battle_00.getId();
    }

    @Override
    public int nextStatus(BaseBattleInfo battleInfo) {
        return SLGNodeType.Battle_10.getId();
    }

    @Override
    public long getStatusDurationTime(BaseBattleInfo battleInfo) {
        return -1;
    }

    private static class DealMessageCallbackTask extends TLMessageCallbackTask {
        private final int sid;

        public DealMessageCallbackTask(int sid) {
            this.sid = sid;
        }

        @Override
        public void complete(CallbackResponse response) {
            Boolean result = (Boolean)response.getParam(0);
            if (!result) {
                refreshFinishedWorldIds.add(sid);
                if (refreshFinishedWorldIds.size() >= LeagueSLGWorldStatusManager.getAllWorldIds().size()) {
                    GameLogger.slgDebug("SLG refresh player data finished. sid : " + sid);
                }
            }
        }

        @Override
        public void timeout() {
            //等下次调用
        }
    }
}

package com.gy.server.world.leagueSLG.bean;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;

import java.io.Serializable;

/**
 * @program: tl_game_4
 * @description: 分组结果
 * @author: <PERSON><PERSON>
 * @create: 2024/11/29
 **/
public class SLGGroupResult implements Serializable {
    private static final long serialVersionUID = 3450234029342033L;

    /**
     * 赛区id
     */
    @Protobuf(order = 1)
    private int areaId;

    /**
     * 分组序号，甲乙丙丁那个
     */
    @Protobuf(order = 2)
    private int fieldIndex;

    /**
     * 帮派id
     */
    @Protobuf(order = 3)
    private long lid;

    public SLGGroupResult(){}

    public SLGGroupResult(int areaId, int fieldIndex, long lid) {
        this.areaId = areaId;
        this.fieldIndex = fieldIndex;
        this.lid = lid;
    }

    public int getAreaId() {
        return areaId;
    }

    public void setAreaId(int areaId) {
        this.areaId = areaId;
    }

    public int getFieldIndex() {
        return fieldIndex;
    }

    public void setFieldIndex(int fieldIndex) {
        this.fieldIndex = fieldIndex;
    }

    public long getLid() {
        return lid;
    }

    public void setLid(long lid) {
        this.lid = lid;
    }

    @Override
    public String toString() {
        return "SLGGroupResult{" +
                "areaId=" + areaId +
                ", fieldIndex=" + fieldIndex +
                ", lid=" + lid +
                '}';
    }

    public String toSimpleString() {
        return areaId + "_" + fieldIndex + "_" + lid;
    }

    public static SLGGroupResult fromSimpleString(String str) {
        String[] arr = str.split("_");
        if (arr.length != 3) {
            return null;
        }
        int areaId = Integer.parseInt(arr[0]);
        int fieldIndex = Integer.parseInt(arr[1]);
        long lid = Long.parseLong(arr[2]);
        return new SLGGroupResult(areaId, fieldIndex, lid);
    }
}

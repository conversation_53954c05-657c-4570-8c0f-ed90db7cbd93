package com.gy.server.world.leagueSLG.bean;

import java.io.Serializable;

/**
 * @program: tl_game_4
 * @description: 玩家同步数据
 * @author: <PERSON><PERSON>
 * @create: 2024/11/29
 **/
public class SLGPlayerSyncData implements Serializable {

    private static final long serialVersionUID = 345829248273499830L;

    /**
     * 玩家兵力上限
     */
    private int army;
    /**
     * 玩家头衔
     */
    private int titleId;

    private boolean commander;

    public int getArmy() {
        return army;
    }

    public void setArmy(int army) {
        this.army = army;
    }

    public int getTitleId() {
        return titleId;
    }

    public void setTitleId(int titleId) {
        this.titleId = titleId;
    }

    public boolean isCommander() {
        return commander;
    }

    public void setCommander(boolean commander) {
        this.commander = commander;
    }
}

package com.gy.server.world.leagueSLG.mainStatus.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.battleRule.base.BaseBattleInfo;
import com.gy.server.core.battleRule.base.IBattleStatusDeal;
import com.gy.server.game.leagueSLG.LeagueSLGService;
import com.gy.server.world.leagueSLG.LeagueSLGHelper;
import com.gy.server.world.leagueSLG.mainStatus.SLGNodeType;

import java.time.LocalDateTime;

/**
 * @program: tl_game_4
 * @description: 领奖日
 * @author: Huang.Xia
 * @create: 2024/11/26
 **/
public class LeagueSLGRewardHandler implements IBattleStatusDeal<BaseBattleInfo> {

    @Override
    public void init(BaseBattleInfo battleInfo) {
        LeagueSLGHelper.notifyStatus2allServers(this.nowStatus());
    }

    @Override
    public void deal(BaseBattleInfo battleInfo) {

    }

    @Override
    public boolean finish(BaseBattleInfo battleInfo) {
        SLGNodeType nodeType = SLGNodeType.getSLGNode(battleInfo.getStatus());
        return nodeType.isEnd();
    }

    @Override
    public int nowStatus() {
        return SLGNodeType.Reward.getId();
    }

    @Override
    public int nextStatus(BaseBattleInfo battleInfo) {
        return SLGNodeType.Rest.getId();
    }

    @Override
    public long getStatusDurationTime(BaseBattleInfo battleInfo) {
        return -1;
    }
}

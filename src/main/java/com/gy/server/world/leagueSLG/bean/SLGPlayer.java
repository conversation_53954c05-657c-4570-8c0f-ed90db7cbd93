package com.gy.server.world.leagueSLG.bean;

import com.baidu.bjf.remoting.protobuf.annotation.Ignore;
import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.google.protobuf.InvalidProtocolBufferException;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.leagueSLG.LeagueSLGService;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbSLG;
import com.tencentcloudapi.iotvideo.v20191126.models.BindUsrInfo;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;

import java.util.*;

/**
 * @program: tl_game_4
 * @description: 玩家数据
 * @author: Huang.Xia
 * @create: 2024/11/26
 **/
public class SLGPlayer {

    public static Comparator<SLGPlayer> medalComparator = new SLGPlayerMedalComparator();

    public static Comparator<SLGPlayer> armyComparator = new SLGPlayerArmyComparator();

    @Ignore
    private SLGLeague league;

    @Protobuf(order = 1)
    private long pid;

    /**
     * 当前兵力
     */
    @Protobuf(order = 2)
    private int army;

    /**
     * 兵力上限
     */
    @Protobuf(order = 3)
    private int maxArmy;

    @Protobuf(order = 4)
    private int maxArmyAdd;

    /**
     * 战功
     */
    @Protobuf(order = 5)
    private int combatMedals;

    /**
     * 军饷发放次数
     */
    @Protobuf(order = 6)
    private int grantArmyExpendCount;

    /**
     * 击退敌人次数
     */
    @Protobuf(order = 7)
    private int fightBackCount;

    /**
     * 被击退记录
     * pid -> 击退次数
     */
    @Protobuf(order = 8)
    private Map<Long, Integer> fightBackedRecord = new HashMap<>();

    @Protobuf(order = 9)
    private List<Integer> marchPath;

    /**
     * 兵营
     */
    @Protobuf(order = 10)
    private SLGCamp camp = new SLGCamp();

    /**
     * 最近一次城市收获时间
     */
    @Protobuf(order = 11)
    private long lastFarmTime;

    /**
     * 当前所在据点id，或者行军的目的地
     */
    @Protobuf(order = 12)
    private int cityId;

    /**
     * 行军到达时间
     */
    @Protobuf(order = 13)
    private long arriveTime;

    /**
     * 兵案领奖等级
     */
    @Protobuf(order = 14)
    private int departmentRewardLevel;

    /**
     * 是否是指挥官
     */
    @Protobuf(order = 15)
    private boolean commander;

    /**
     * 已经领取的军饷
     */
    @Protobuf(order = 16)
    private Set<Integer> fetchedArmyExpends = new HashSet<>();

    /**
     * 战功记录
     */
    @Ignore
    private List<PbSLG.SLGAchievementRecord> fightRecords = new ArrayList<>();
    @Protobuf(order = 17)
    private List<byte[]> fightRecordBytes = new ArrayList<>();

    /**
     * 连胜次数
     */
    @Protobuf(order = 18)
    private int continuousWinCount = 0;


    /**
     * 自动模式目标城市, -1表示未设置
     */
    @Protobuf(order = 19)
    private int autoModeCityId;

    /**
     * 自动模式 0-防守 1-攻击 2-防御
     */
    @Protobuf(order = 20)
    private int autoMode;

    /**
     * 战斗结束时间，无需存储
     */
    @Ignore
    private long fightFinishTime = 0L;

    /**
     * 玩家级别数据同步
     * 只用来存针对玩家的
     */
    @Ignore
    List<PbSLG.SLGAttackMsg> attackMsgs = new ArrayList<>();

    /**
     * 被攻击时间点
     * 运行时数据，无需存储
     */
    @Ignore
    private long attackedTime = 0L;

    /**
     * 标记玩家状态数据是否需要同步
     */
    @Ignore
    private boolean statusNeedSync = false;

    /**
     * 自动状态更新时间
     */
    @Ignore
    private long lastAutoModeTime = 0L;


    public void init(SLGLeague league){
        this.league = league;
        if(this.fightRecordBytes.size() > 0){
            this.fightRecords = new ArrayList<>(this.fightRecordBytes.size());
            for(byte[] bytes : this.fightRecordBytes){
                PbSLG.SLGAchievementRecord record = null;
                try {
                    record = PbSLG.SLGAchievementRecord.parseFrom(bytes);
                    this.fightRecords.add(record);
                } catch (InvalidProtocolBufferException e) {
                    e.printStackTrace();
                }
            }
            this.fightRecordBytes.clear();
        }
    }

    public void newDayCombat(){
        maxArmyAdd = 0;
        if(marchPath != null) {
            marchPath.clear();
        }
        camp.setSpeedAdd(0);
        camp.setProduceStartTime(0);
        camp.setWeakenStartTime(0);
        camp.setWeakenCount(0);
        lastFarmTime = 0;
        arriveTime = 0;
        continuousWinCount = 0;
    }

    public void beforeSave(){
        if(this.fightRecords.size() > 0) {
            this.fightRecordBytes = new ArrayList<>(this.fightRecords.size());
            for(PbSLG.SLGAchievementRecord record : this.fightRecords){
                this.fightRecordBytes.add(record.toByteArray());
            }
        }
    }



    public SLGLeague getLeague() {
        return league;
    }

    public void setLeague(SLGLeague league) {
        this.league = league;
    }

    public long getAttackedTime() {
        return attackedTime;
    }

    public void setAttackedTime(long attackedTime) {
        this.attackedTime = attackedTime;
    }

    public List<byte[]> getFightRecordBytes() {
        return fightRecordBytes;
    }

    public void setFightRecordBytes(List<byte[]> fightRecordBytes) {
        this.fightRecordBytes = fightRecordBytes;
    }

    public int getArmy() {
        return army;
    }

    public Map<Long, Integer> getFightBackedRecord() {
        return fightBackedRecord;
    }

    /**
     * 玩家被攻击同步
     */
    public void addAttackedMsg(){
        if(ServerConstants.getCurrentTimeMillis() > this.attackedTime + LeagueSLGService.getConstant().playerAttackedBroadcastCD * 1000){
            this.attackedTime = ServerConstants.getCurrentTimeMillis();

            PbSLG.SLGAttackMsg.Builder b = PbSLG.SLGAttackMsg.newBuilder();
            b.setMsgType(PbSLG.SLGAttackMsg.MsgType.PLAYER_ATTACKED);
            this.attackMsgs.add(b.build());
        }
    }

    public void addWeakedMsg(long enemyPid, int cityId) {
        PbSLG.SLGAttackMsg.Builder b = PbSLG.SLGAttackMsg.newBuilder();
        b.setMsgType(PbSLG.SLGAttackMsg.MsgType.PLAYER_WEAKED)
                .setPlayerId(enemyPid)
                .setCityId(cityId);
        this.attackMsgs.add(b.build());

    }

    public CountData getFightBackedPlayer(){
        if(fightBackedRecord.size() > 0){
            Integer maxCount = 0;
            long maxPid = 0;
            for(Map.Entry<Long, Integer> entry : fightBackedRecord.entrySet()){
                if(entry.getValue() > maxCount){
                    maxCount = entry.getValue();
                    maxPid = entry.getKey();
                }
            }
            MiniGamePlayer miniPlayer = PlayerHelper.getMiniPlayer(maxPid);
            return new CountData(miniPlayer.getName(), maxCount);
        }
        return null;
    }

    public void setFightBackedRecord(Map<Long, Integer> fightBackedRecord) {
        this.fightBackedRecord = fightBackedRecord;
    }

    public void addFightBackedRecord(long enemyPid){
        Integer count = this.fightBackedRecord.getOrDefault(enemyPid, 0);
        this.fightBackedRecord.put(enemyPid, count + 1);
    }

    public void setArmy(int army) {
        this.army = army;
    }

    public void damageArmy(int damage){
        //损失兵力
        this.army -= damage;
        //增加战功
        this.combatMedals += damage;
        //更新功勋排行
        this.league.updateMedalOrder(this);
        //数据同步
        this.statusNeedSync = true;
    }

    public int getMaxArmy() {
        return maxArmy;
    }

    public int getFightBackCount() {
        return fightBackCount;
    }

    public void setFightBackCount(int fightBackCount) {
        this.fightBackCount = fightBackCount;
    }

    public void setMaxArmy(int maxArmy) {
        this.maxArmy = maxArmy;
    }

    public int getCombatMedals() {
        return combatMedals;
    }

    public void setCombatMedals(int combatMedals) {
        this.combatMedals = combatMedals;
    }

    public SLGCamp getCamp() {
        return camp;
    }

    public void setCamp(SLGCamp camp) {
        this.camp = camp;
    }

    public long getPid() {
        return pid;
    }

    public void setPid(long pid) {
        this.pid = pid;
    }

    public long getLastFarmTime() {
        return lastFarmTime;
    }

    public void setLastFarmTime(long lastFarmTime) {
        this.lastFarmTime = lastFarmTime;
    }

    public int getCityId() {
        return cityId;
    }

    public void setCityId(int cityId) {
        this.cityId = cityId;
    }

    public long getArriveTime() {
        return arriveTime;
    }

    public int getArriveTimeLeft(){
        if(this.arriveTime > ServerConstants.getCurrentTimeMillis()){
            return (int)(this.arriveTime - ServerConstants.getCurrentTimeMillis()) / 1000;
        }
        return 0;
    }

    public int getFightLeftSeconds(){
        if(this.fightFinishTime > ServerConstants.getCurrentTimeMillis()){
            return (int)(this.fightFinishTime - ServerConstants.getCurrentTimeMillis()) / 1000;
        }
        return 0;
    }

    public long getRealFightFinishTime(){
        if(this.fightFinishTime > ServerConstants.getCurrentTimeMillis()){
            return this.fightFinishTime;
        }
        return 0L;
    }



    /**
     * 剩余时间减半
     */
    public void speedUp(){
        int leftSeconds = getArriveTimeLeft();
        if(leftSeconds > 0){
            this.arriveTime -= leftSeconds * 1000 / 2;
        }
    }

    public int getContinuousWinCount() {
        return continuousWinCount;
    }

    public void setContinuousWinCount(int continuousWinCount) {
        this.continuousWinCount = continuousWinCount;
    }

    public void addContinuousWinCount(){
        this.continuousWinCount++;

        //广播
        if(this.continuousWinCount >= LeagueSLGService.getConstant().broadcastKillCount){
            PbSLG.SLGAttackMsg.Builder b = PbSLG.SLGAttackMsg.newBuilder();
            b.setMsgType(PbSLG.SLGAttackMsg.MsgType.PLAYER_WINNING_STREAK)
                            .setPlayerId(this.pid)
                                    .setWinCount(this.continuousWinCount);
            league.getField().getSyncData().addAttackMsgs(b);
        }
    }

    /**
     * 行军中
     */
    public boolean isMarching(){
        return ServerConstants.getCurrentTimeMillis() < this.arriveTime;
    }

    public void setArriveTime(long arriveTime) {
        this.arriveTime = arriveTime;
    }

    public int getDepartmentRewardLevel() {
        return departmentRewardLevel;
    }

    public void setDepartmentRewardLevel(int departmentRewardLevel) {
        this.departmentRewardLevel = departmentRewardLevel;
    }

    public void refresh(){
        this.army = this.maxArmy;
        this.camp.setArmy(0);
    }

    public void tick(){
        //战场信息同步
        if(this.statusNeedSync || this.attackMsgs.size() > 0){
            PbProtocol.SLGBattlePushRst.Builder pushRst = PbProtocol.SLGBattlePushRst.newBuilder();
            if(this.statusNeedSync){
                pushRst.setMyStatus(genMyStatusBuilder());
                this.statusNeedSync = false;
            }
            if(this.attackMsgs.size() > 0){
                pushRst.addAllAttackMsgs(this.attackMsgs);
                this.attackMsgs.clear();
            }
            pushRst.setResult(Text.genOkServerRstInfo());

            ServerCommandRequest req = CommandRequests.newServerCommandRequest("LeagueSLGGameCommandService.battlePushPlayer");
            TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, Player.getRealServerId(this.pid),req, this.pid, league.getField().getIndex(),pushRst.build());
        }

        //自动模式 10秒检测一次
        if(this.autoModeCityId >= 0 && ServerConstants.getCurrentTimeMillis() - this.lastAutoModeTime > 10 * 1000){
            this.lastAutoModeTime = ServerConstants.getCurrentTimeMillis();
            autoModeTick();
        }
    }

    private void autoModeTick(){
        if(getArriveTimeLeft() > 0){
            return;
        }
        if(getFightLeftSeconds() > 0){
            return;
        }

        //在大本营
        if(cityId == league.getHomeCity()){
            //检查状态是否需要更新
            SLGCity targetCity = league.getField().getCities().get(this.autoModeCityId);
            if(targetCity == null){
                this.autoModeCityId = -1;
                return;
            }

            if(this.autoMode == 0){
                if(targetCity.getLid() != league.getId()){
                    //城池丢失，退出防守模式
                    this.autoModeCityId = -1;
                    return;
                }
            }else{
                if(targetCity.getLid() == league.getId()){
                    //城池变为己方，退出进攻模式
                    this.autoModeCityId = -1;
                    return;
                }
            }

            //目标可达性检测
            List<Integer> path = league.getField().calcPath(league.getId(),this.cityId, autoModeCityId);
            if(path == null){
                //路径不可达，退出模式
                this.autoModeCityId = -1;
                return;
            }

            //补兵
            camp.autoAddArmy(this);

            //尝试出征
            if(camp.armyEnough(this)){
                league.getField().march(this, path);
            }else{
                //十分钟以后再看
                this.lastAutoModeTime = ServerConstants.getCurrentTimeMillis() + 10 * 60 * 1000;
            }

        }else if(this.cityId == this.autoModeCityId){
            //在目标城池，进攻方开始干活；防守方待命即可
            SLGCity targetCity = league.getField().getCities().get(this.autoModeCityId);
            if(targetCity == null){
                this.autoModeCityId = -1;
                return;
            }
            if(targetCity.getLid() == league.getId()){
                //己方城池
                if(this.autoMode == 0){
                    //待命即可
                }else{
                    //退出进攻模式
                    this.autoModeCityId = -1;
                }
            }else{
                //敌方城池
                if(this.autoMode == 1){
                    targetCity.autoFight(this, -1);
                }else if(this.autoMode == 2){
                    targetCity.autoFight(this, -2);
                }else{
                    //退出防守模式
                    this.autoModeCityId = -1;
                }
            }
        }else{
            //在第三方城池，尝试去目标城池
            //目标可达性检测
            List<Integer> path = league.getField().calcPath(league.getId(),this.cityId, autoModeCityId);
            if(path == null){
                //路径不可达，退出模式
                this.autoModeCityId = -1;
                return;
            }
            league.getField().march(this, path);
        }
    }

    public int getMaxArmyAdd() {
        return maxArmyAdd;
    }

    public int getGrantArmyExpendCount() {
        return grantArmyExpendCount;
    }

    public void setGrantArmyExpendCount(int grantArmyExpendCount) {
        this.grantArmyExpendCount = grantArmyExpendCount;
    }

    public void setMaxArmyAdd(int maxArmyAdd) {
        this.maxArmyAdd = maxArmyAdd;
    }

    public int getTotalMaxArmy(){
        return this.maxArmy + this.maxArmyAdd;
    }

    public boolean isCommander() {
        return commander;
    }

    public void setCommander(boolean commander) {
        this.commander = commander;
    }

    public List<Integer> getMarchPath() {
        return marchPath;
    }

    public void setMarchPath(List<Integer> marchPath) {
        this.marchPath = marchPath;
    }

    public PbSLG.SLGAttacker.Builder genSLGAttackerBuilder(boolean isAtk){
        PbSLG.SLGAttacker.Builder builder = PbSLG.SLGAttacker.newBuilder();
        builder.setIsPlayer(true)
                .setPlayerId(pid)
                .setLeftArmy(army)
                .setIsAttacker(isAtk)
                .setMaxArmy(maxArmy);
        return builder;
    }

    public List<PbSLG.SLGAttackMsg> getAttackMsgs() {
        return attackMsgs;
    }

    public void setAttackMsgs(List<PbSLG.SLGAttackMsg> attackMsgs) {
        this.attackMsgs = attackMsgs;
    }

    public PbSLG.SLGMyStatus.Builder genMyStatusBuilder(){
        PbSLG.SLGMyStatus.Builder myStatus = PbSLG.SLGMyStatus.newBuilder();
        myStatus.setCityId(getCityId())
                .setMarchArriveTime(this.arriveTime)
                .setFightFinishTime(getRealFightFinishTime())
                .setArmy(getArmy())
                .setMaxArmy(getMaxArmy())
                .setAutoMode(autoMode)
                .setAutoTargetCityId(autoModeCityId)
                .setWeakEndLeftSeconds(getCamp().getWeakenLeftSeconds())
                .setWeakCount(getCamp().getWeakenCount());
        return myStatus;
    }

    public PbSLG.SLGMarch.Builder genSLGMarchBuilder(){
        PbSLG.SLGMarch.Builder marchPb = PbSLG.SLGMarch.newBuilder();
        marchPb.setLeagueId(getLeague().getId())
                .addAllCityIds(getMarchPath())
                .setArriveTime(this.arriveTime)
                .setPlayerId(this.pid)
                .setTop3(getLeague().isTop3Medal(getPid()));
        return marchPb;
    }

    public boolean isStatusNeedSync() {
        return statusNeedSync;
    }

    public void setStatusNeedSync(boolean statusNeedSync) {
        this.statusNeedSync = statusNeedSync;
    }

    public Set<Integer> getFetchedArmyExpends() {
        return fetchedArmyExpends;
    }

    public void setFetchedArmyExpends(Set<Integer> fetchedArmyExpends) {
        this.fetchedArmyExpends = fetchedArmyExpends;
    }

    public List<PbSLG.SLGAchievementRecord> getFightRecords() {
        return fightRecords;
    }

    public void addFightRecord(PbSLG.SLGAchievementRecord record){
        this.fightRecords.add(record);
        if(this.fightRecords.size() > LeagueSLGService.getConstant().playerRecordCount){
            this.fightRecords.remove(0);
        }
    }

    public long getFightFinishTime() {
        return fightFinishTime;
    }

    public void setFightFinishTime(long fightFinishTime) {
        this.fightFinishTime = fightFinishTime;
    }

    public void updateFightFinishTime(long fightFinishTime){
        this.fightFinishTime = fightFinishTime;
        this.statusNeedSync = true;
    }

    public void setFightRecords(List<PbSLG.SLGAchievementRecord> fightRecords) {
        this.fightRecords = fightRecords;
    }

    public int getAutoModeCityId() {
        return autoModeCityId;
    }

    public void setAutoModeCityId(int autoModeCityId) {
        this.autoModeCityId = autoModeCityId;
    }

    public int getAutoMode() {
        return autoMode;
    }

    public void setAutoMode(int autoMode) {
        this.autoMode = autoMode;
    }

    private static class SLGPlayerMedalComparator implements Comparator<SLGPlayer> {
        @Override
        public int compare(SLGPlayer o1, SLGPlayer o2) {
            if (o1.getCombatMedals() == o2.getCombatMedals()) {
                return Long.compare(o1.getPid(), o2.getPid());
            } else {
                return Integer.compare(o2.getCombatMedals(), o1.getCombatMedals());
            }
        }
    }

    private static class SLGPlayerArmyComparator implements Comparator<SLGPlayer> {
        @Override
        public int compare(SLGPlayer o1, SLGPlayer o2) {
            if (o1.getArmy() == o2.getArmy()) {
                return Long.compare(o1.getPid(), o2.getPid());
            } else {
                return Integer.compare(o1.getArmy(), o2.getArmy());
            }
        }
    }
}

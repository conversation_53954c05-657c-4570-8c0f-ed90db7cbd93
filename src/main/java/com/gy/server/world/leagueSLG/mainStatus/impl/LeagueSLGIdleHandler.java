package com.gy.server.world.leagueSLG.mainStatus.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.battleRule.base.BaseBattleInfo;
import com.gy.server.core.battleRule.base.IBattleStatusDeal;
import com.gy.server.game.leagueSLG.LeagueSLGService;
import com.gy.server.utils.time.DateTimeUtil;
import com.gy.server.world.leagueSLG.LeagueSLGHelper;
import com.gy.server.world.leagueSLG.mainStatus.SLGNodeType;

import java.time.DayOfWeek;
import java.time.LocalDateTime;

/**
 * @program: tl_game_4
 * @description: 休赛
 * @author: <PERSON>.<PERSON>
 * @create: 2024/11/26
 **/
public class LeagueSLGIdleHandler implements IBattleStatusDeal<BaseBattleInfo> {


    @Override
    public void init(BaseBattleInfo battleInfo) {
        LeagueSLGHelper.notifyStatus2allServers(this.nowStatus());
    }

    @Override
    public void deal(BaseBattleInfo battleInfo) {

    }

    @Override
    public boolean finish(BaseBattleInfo battleInfo) {
        //周一到周三匹配之前，才能进入休息状态。不然没法走完整一个赛季。
        SLGNodeType nodeType = SLGNodeType.getSLGNode(battleInfo.getStatus());
        if (true) {//后续加上判断功能开启控制
            return nodeType.isEnd();
        }else{
            return false;
        }
    }

    @Override
    public int nowStatus() {
        return SLGNodeType.Idle.getId();
    }

    @Override
    public int nextStatus(BaseBattleInfo battleInfo) {
        return SLGNodeType.Rest.getId();
    }

    @Override
    public long getStatusDurationTime(BaseBattleInfo battleInfo) {
        return -1;
    }
}

package com.gy.server.world.leagueSLG.bean;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.leagueSLG.LeagueSLGService;
import com.gy.server.game.leagueSLG.template.SLGCampTemplate;
import com.gy.server.game.text.Text;

/**
 * @program: tl_game_4
 * @description: 军营
 * @author: Huang.<PERSON>
 * @create: 2024/11/26
 **/
public class SLGCamp {

    /**
     * 当前军营的军力
     */
    @Protobuf(order = 1)
    private int army;

    /**
     * 军营模版id
     */
    @Protobuf(order = 2)
    private int tid;

    @Protobuf(order = 3)
    private long produceStartTime;
    /**
     * 兵案最近一次领奖等级
     */
    @Protobuf(order = 4)
    @Deprecated
    private int departmentRewardLevel;

    @Protobuf(order = 5)
    private long weakenStartTime;
    @Protobuf(order = 6)
    private int weakenCount;

    /**
     * 募兵速度增速
     */
    @Protobuf(order = 7)
    private int speedAdd;

    public int getArmy() {
        return army;
    }

    public void setArmy(int army) {
        this.army = army;
    }

    public int getTid() {
        return tid;
    }

    public void setTid(int tid) {
        this.tid = tid;
    }

    public long getProduceStartTime() {
        return produceStartTime;
    }

    public void setProduceStartTime(long produceStartTime) {
        this.produceStartTime = produceStartTime;
    }

    public int getDepartmentRewardLevel() {
        return departmentRewardLevel;
    }

    public void setDepartmentRewardLevel(int departmentRewardLevel) {
        this.departmentRewardLevel = departmentRewardLevel;
    }

    /**
     * 玩家补兵
     */
    public int addArmy(SLGPlayer player, int army){
        int totalArmy = getTotalArmy();

        //确认是在大本营中
        SLGBattleField field = player.getLeague().getField();
        SLGCity city = field.getCities().get(player.getCityId());
        if(city.getTid() != player.getLeague().getHomeCity() || city.getLid() != player.getLeague().getId()){
            return Text.SLG_己方大本营才能补兵;
        }else if(totalArmy < army){
            return Text.SLG_军营军力不足;
        }else if(player.getArmy() + army > player.getMaxArmy()){
            return Text.SLG_兵力不能超过上限;
        }else{
            player.setArmy(player.getArmy() + army);
            this.army = totalArmy - army;
            this.produceStartTime = ServerConstants.getCurrentTimeMillis();
        }
        return Text.没有异常;
    }

    /**
     * 结算当前最新兵力产出
     */
    public void updateArmy(){
        int totalArmy = getTotalArmy();
        this.army = totalArmy;
        this.produceStartTime = ServerConstants.getCurrentTimeMillis();
    }

    public void autoAddArmy(SLGPlayer player){
        int totalArmy = getTotalArmy();
        int count = Math.min(totalArmy, player.getMaxArmy() - player.getArmy());

        player.setArmy(player.getArmy() + count);
        this.army = totalArmy - count;
        this.produceStartTime = ServerConstants.getCurrentTimeMillis();
    }

    public void addCampArmy(int army){
        this.army = this.getTotalArmy();
        this.army += army;
        this.produceStartTime = ServerConstants.getCurrentTimeMillis();
    }

    public void weakCheck(){
        if(ServerConstants.getCurrentTimeMillis() >  LeagueSLGService.getConstant().weakTime * 1000 + weakenStartTime) {
            this.weakenStartTime = 0;
            this.weakenCount = 0;
        }
    }

    public void addWeak(){
        this.weakCheck();

        this.weakenCount++;
        this.weakenStartTime = ServerConstants.getCurrentTimeMillis();

    }

    /**
     * 判断兵力足够，可以行军
     */
    public boolean armyEnough(SLGPlayer player){
        return player.getArmy() >= marchNeed(player);
    }

    /**
     * 计算行军需要的最低军力
     */
    public int marchNeed(SLGPlayer player){
        if(this.weakenStartTime > 0){
            weakCheck();
        }
        int weakCount = weakenCount;
        if(weakCount >= LeagueSLGService.getConstant().armyNeedMore.length){
            weakCount = LeagueSLGService.getConstant().armyNeedMore.length - 1;
        }
        int rate = LeagueSLGService.getConstant().armyNeedMore[weakCount];
        return Math.max(player.getMaxArmy() * rate / 100, LeagueSLGService.getConstant().armyNeedMin);
    }

    public int getSpeedAdd() {
        return speedAdd;
    }

    public void setSpeedAdd(int speedAdd) {
        this.speedAdd = speedAdd;
    }
    public void speedModify(int rate) {

        SLGCampTemplate template = LeagueSLGService.getCamps().get(tid);

        //先结算当前军力
        int totalArmy = getTotalArmy();
        this.army = totalArmy;
        this.produceStartTime = ServerConstants.getCurrentTimeMillis();

        //计算增速
        int speed = template.armyGrow;
        this.speedAdd = speed * rate / 10000;
    }

    public int getTotalSpeed(){
        SLGCampTemplate template = LeagueSLGService.getCamps().get(tid);
        return this.speedAdd + template.armyGrow;
    }

    public int getTotalArmy(){
        SLGCampTemplate template = LeagueSLGService.getCamps().get(tid);
        int totalArmy = (int) (this.army + (ServerConstants.getCurrentTimeMillis() - this.produceStartTime)/60/1000 * (template.armyGrow + speedAdd));
        totalArmy = Math.min(totalArmy, template.maxArmy);
        return totalArmy;
    }

    public long getWeakenStartTime() {
        return weakenStartTime;
    }

    public void setWeakenStartTime(long weakenStartTime) {
        this.weakenStartTime = weakenStartTime;
    }

    public int getWeakenCount() {
        return weakenCount;
    }

    public void setWeakenCount(int weakenCount) {
        this.weakenCount = weakenCount;
    }

    public int getWeakenLeftSeconds() {
        if (weakenStartTime > 0) {
            if (LeagueSLGService.getConstant().weakTime * 1000 + weakenStartTime < ServerConstants.getCurrentTimeMillis()) {
                this.weakenCount = 0;
                this.weakenStartTime = 0;
                return 0;
            } else {
                return (int) ((LeagueSLGService.getConstant().weakTime * 1000 + weakenStartTime - ServerConstants.getCurrentTimeMillis()) / 1000);
            }
        } else {
            return 0;
        }
    }
}

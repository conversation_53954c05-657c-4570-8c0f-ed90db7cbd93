package com.gy.server.world.leagueSLG.mainStatus.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.battleRule.base.BaseBattleInfo;
import com.gy.server.core.battleRule.base.IBattleStatusDeal;
import com.gy.server.game.leagueSLG.LeagueSLGService;
import com.gy.server.utils.time.DateTimeUtil;
import com.gy.server.world.leagueSLG.LeagueSLGHelper;
import com.gy.server.world.leagueSLG.LeagueSLGWorldManager;
import com.gy.server.world.leagueSLG.mainStatus.SLGNodeType;

import java.time.LocalDateTime;

/**
 * @program: tl_game_4
 * @description: 匹配结果展示
 * @author: <PERSON>.<PERSON>
 * @create: 2025/01/02
 **/
public class LeagueSLGMatchResultHandler implements IBattleStatusDeal<BaseBattleInfo> {


    @Override
    public void init(BaseBattleInfo battleInfo) {
        LeagueSLGHelper.notifyStatus2allServers(this.nowStatus());
    }

    @Override
    public void deal(BaseBattleInfo battleInfo) {

    }

    @Override
    public boolean finish(BaseBattleInfo battleInfo) {
        SLGNodeType nodeType = SLGNodeType.getSLGNode(battleInfo.getStatus());
        return nodeType.isEnd();
    }

    @Override
    public int nowStatus() {
        return SLGNodeType.MatchResult.getId();
    }

    @Override
    public int nextStatus(BaseBattleInfo battleInfo) {
        return SLGNodeType.Battle_00.getId();
    }

    @Override
    public long getStatusDurationTime(BaseBattleInfo battleInfo) {
        return -1;
    }
}

package com.gy.server.world.leagueSLG.battleStatus;

/**
 * @program: tl_game_4
 * @description: 战斗日状态
 * @author: <PERSON><PERSON>
 * @create: 2024/12/2
 **/
public enum SLTBattleStatus {
    idle,
    ;
    /**
     * 非战斗状态
     */
    public static final int IDLE = 0;
    /**
     * 准备状态
     * 0~10点
     */
    public static final int PREPARE = 1;
    public static final int CLOCK_10 = 2;
    public static final int CLOCK_12 = 3;
    public static final int CLOCK_16 = 4;
    public static final int CLOCK_18 = 5;
    public static final int CLOCK_19 = 6;
    public static final int CLOCK_20 = 7;
    public static final int CLOCK_2159 = 8;
    public static final int CLOCK_22 = 9;

}

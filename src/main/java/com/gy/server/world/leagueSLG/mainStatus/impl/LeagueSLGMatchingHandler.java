package com.gy.server.world.leagueSLG.mainStatus.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.battleRule.base.BaseBattleInfo;
import com.gy.server.core.battleRule.base.IBattleStatusDeal;
import com.gy.server.core.callback.response.CallbackResponse;
import com.gy.server.core.command.CommandRequest;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.warZone.WarZoneHelper;
import com.gy.server.game.warZone.WarZoneTypeEnums;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.world.common.NodeCommonManager;
import com.gy.server.world.leagueSLG.LeagueSLGHelper;
import com.gy.server.world.leagueSLG.LeagueSLGWorldGlobalData;
import com.gy.server.world.leagueSLG.LeagueSLGWorldManager;
import com.gy.server.world.leagueSLG.bean.SLGLeague;
import com.gy.server.world.leagueSLG.bean.SLGRegister;
import com.gy.server.world.leagueSLG.mainStatus.SLGNodeType;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.rpc.TLMessageCallbackTask;
import com.ttlike.server.tl.baselib.serialize.warZone.WarZoneInfo;

import java.util.*;

/**
 * @program: tl_game_4
 * @description: 匹配分组中。 整个匹配过程均在内存中进行，若中途重启，则从头匹配。
 * @author: Huang.Xia
 * @create: 2024/11/26
 **/
public class LeagueSLGMatchingHandler implements IBattleStatusDeal<BaseBattleInfo> {

    /**
     * debug模式
     *  放松报名条件：所有军团均可报名
     *  放松赛区开启条件：超过2个军团即可开启
     */
    public static boolean debug = true;
    private long lastCheckTime = 0;

    private boolean matchFinish = false;
    private boolean registerFinish = false;
    private boolean allocateFinish = false;

    /**
     * 赛区id，报名帮派数据
     */
    private Map<Integer, SLGRegister> areas = new HashMap<>();

    @Override
    public void init(BaseBattleInfo battleInfo) {
            areas.clear();
            matchFinish = false;
            registerFinish = false;
            allocateFinish = false;

            //清理数据
            LeagueSLGWorldManager.getInstance().getRegisters().clear();

            //广播注册开始
            CommandRequest request = CommandRequests.newServerCommandRequest("LeagueSLGGameCommandService.registerNotify");
            TLBase.getInstance().getRpcUtil().sendToAll(ServerType.GAME, request);

            this.lastCheckTime = ServerConstants.getCurrentTimeMillis();

            LeagueSLGWorldGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueWorldSLG);
            globalData.setCombatDayIndex(-1);

            //广播通知world清理自己的赛区数据，主要是防止重启后多次匹配数据混乱
            LeagueSLGHelper.notifyStatus2allServers(this.nowStatus());

            GameLogger.slgDebug("League SLG register start");
    }

    @Override
    public void deal(BaseBattleInfo battleInfo) {
        //发报名通知
        if(!this.registerFinish) {
            registerCheck();
            return;
        }

        if(!this.matchFinish){
            GameLogger.slgDebug("League SLG match start");
            match();
            this.matchFinish = true;
            GameLogger.slgDebug("League SLG match finish");
            return;
        }

        if(!this.allocateFinish){
            allocateFinishCheck();
        }
    }

    @Override
    public boolean finish(BaseBattleInfo battleInfo) {
        SLGNodeType nodeType = SLGNodeType.getSLGNode(battleInfo.getStatus());

        return this.allocateFinish //分配完成
                && nodeType.isEnd();//到战斗日
    }

    @Override
    public int nowStatus() {
        return SLGNodeType.Matching.getId();
    }

    @Override
    public int nextStatus(BaseBattleInfo battleInfo) {
        if(this.areas.size() == 0){
            //没有赛区,本赛季直接结束
            return SLGNodeType.Idle.getId();
        }else {
            return SLGNodeType.MatchResult.getId();
        }
    }

    @Override
    public long getStatusDurationTime(BaseBattleInfo battleInfo) {
        return -1;
    }

    /**
     * 检查是否完成注册，缺失的区服单独通知
     */
    private void registerCheck() {

        if(ServerConstants.getCurrentTimeMillis() - lastCheckTime > 1000 * 10) {
            this.lastCheckTime = ServerConstants.getCurrentTimeMillis();
            if(!NodeCommonManager.getInstance().isStableServerCount()){
                GameLogger.slgDebug("League SLG wait for server count stable.");
                return;
            }
            Set<Integer> notRegisterServerId = getNotRegisterServerId();
            if (CollectionUtil.isNotEmpty(notRegisterServerId)) {
                for (int serverId : notRegisterServerId) {
                    CommandRequest request = CommandRequests.newServerCommandRequest("LeagueSLGGameCommandService.registerNotify");
                    TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, serverId, request);
                }
                GameLogger.slgDebug("League SLG send register notify to server : " + notRegisterServerId);
            }else{
                GameLogger.slgDebug("League SLG register finish. Server size : " + LeagueSLGWorldManager.getInstance().getRegisters().keySet().size());
                for(Map.Entry<Integer, SLGRegister> entry : LeagueSLGWorldManager.getInstance().getRegisters().entrySet()){
                    GameLogger.slgDebug("register detail :  serverId " + entry.getKey() + " , leagueIds :" + entry.getValue().getLeagues().keySet());
                }
                this.registerFinish = true;
            }
        }
    }

    /**
     * 检查还未上报数据的区服
     */
    private Set<Integer> getNotRegisterServerId(){
        Set<Integer> registered = LeagueSLGWorldManager.getInstance().getRegisters().keySet();
        Set<Integer> allSids = new HashSet<>(NodeCommonManager.getInstance().getHealthyServerId(1));
        allSids.removeAll(registered);
        if(allSids.size() > 0){
            GameLogger.slgDebug("League SLG register wait sids : " + allSids);
            return allSids;
        }else {
            return null;
        }
    }

    /**
     * 赛区匹配
     */
    private void match(){
        WarZoneHelper.allocateWarZone(WarZoneTypeEnums.leagueSLG);
        LeagueSLGWorldGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueWorldSLG);
        //报名数据
        Map<Integer, SLGRegister> registers = LeagueSLGWorldManager.getInstance().getRegisters();
        //战区数据
        WarZoneInfo warZoneInfo = WarZoneHelper.getWarZoneInfo(WarZoneTypeEnums.leagueSLG);
        List<ServerGroup> serverGroups = new LinkedList<>();
        warZoneInfo.getSingleInfoList().forEach(singleInfo -> {
            serverGroups.add(new ServerGroup(singleInfo.getServerIds(), registers));
        });

        //不在配置战区列表的报名服务器，包装一组
        Set<Integer> notInWarZoneServerId = new HashSet<>(registers.keySet());
        warZoneInfo.getSingleInfoList().forEach(singleInfo -> {
            notInWarZoneServerId.removeAll(singleInfo.getServerIds());
        });
        serverGroups.add(new ServerGroup(notInWarZoneServerId, registers));

        //初步决定赛区：归并为单组帮派数大于48
        boolean allFull = false;
        while(serverGroups.size() > 1 && !allFull){
            for(int i = 1; i < serverGroups.size(); i++){
                if(serverGroups.get(i).leagueCount < 48) {
                    if(i + 1 < serverGroups.size() &&
                            (serverGroups.get(i-1).leagueCount > serverGroups.get(i+1).leagueCount)){
                        //合并入后一组
                        serverGroups.get(i+1).sids.addAll(serverGroups.get(i).sids);
                        serverGroups.get(i+1).leagueCount += serverGroups.get(i).leagueCount;
                    }else{
                        //合并入前一组
                        serverGroups.get(i-1).sids.addAll(serverGroups.get(i).sids);
                        serverGroups.get(i-1).leagueCount += serverGroups.get(i).leagueCount;
                    }

                    if(i == serverGroups.size() - 1){
                        allFull = true;
                    }

                    serverGroups.remove(i);
                    break;
                }
            }
        }


        //赛区内细分
        List<ServerGroup> finalGroups = new ArrayList<>();
        for(ServerGroup serverGroup : serverGroups){
            if(serverGroup.leagueCount < 48 && !debug){
                continue;//人数不足，放弃分组
            }

            int count = 0;
            Set<Integer> sids = new HashSet<>();
            for(int sid : serverGroup.sids){
                count += registers.get(sid).getLeagues().size();
                sids.add(sid);

                if(count >= 72){
                    //满72个军团，分组

                    finalGroups.add(new ServerGroup(sids, registers));
                    sids = new HashSet<>();
                    count = 0;
                }
            }

            if(count >= 48 || debug){
                //新增一组
                finalGroups.add(new ServerGroup(sids, registers));
            }else{
                //合并入上一组
                finalGroups.get(finalGroups.size() - 1).sids.addAll(sids);
                finalGroups.get(finalGroups.size() - 1).leagueCount += count;
            }
        }

        //根据赛事方案，筛选参数帮会,生成最总赛区
        int areaId = 1;
        for(ServerGroup serverGroup : finalGroups){
            List<SLGLeague> leagues = new ArrayList<>();
            for(int sid : serverGroup.sids){
                for(Map.Entry<Long, Long> entry : registers.get(sid).getLeagues().entrySet()) {
                    SLGLeague league = new SLGLeague();
                    league.setId(entry.getKey());
                    league.setFightingPower(entry.getValue());
                    leagues.add(league);
                }
            }

            //排序
            Collections.sort(leagues, SLGLeague.powerComparator);

            int[] plans = new int[]{108,96,72,48};
            for(int plan : plans){
                if(leagues.size() >= plan){
                    leagues = leagues.subList(0, plan);
                    break;
                }
            }
            areas.put(areaId++, new SLGRegister(leagues));
        }

        globalData.setAreaCount(areas.size());
        for(Map.Entry<Integer, SLGRegister> entry : areas.entrySet()){
            GameLogger.slgDebug("League SLG areaId :  " + entry.getKey() + " , leagues : " + entry.getValue().getLeagues().keySet());
        }
    }

    private void allocateArea(){
        int count = 0;
        for(Map.Entry<Integer, SLGRegister> entry : areas.entrySet()){
            if(count >= 40){
                break;
            }

            if(!LeagueSLGWorldManager.getInstance().getAllocatedAreaIds().contains(entry.getKey())){
                count++;

                CommandRequest request = CommandRequests.newServerCommandRequest("LeagueSLGWorldCommandService.allocateArea");
                TLBase.getInstance().getRpcUtil().sendToHashNodeWithCallBack(new AllocateAreaMessageCallbackTask(entry), ServerType.WORLD, entry.getKey(), request, entry.getKey(), entry.getValue());
            }
        }
    }

    private void allocateFinishCheck(){
        if(ServerConstants.getCurrentTimeMillis() - lastCheckTime > 1000 * 10) {
            this.lastCheckTime = ServerConstants.getCurrentTimeMillis();
            if(LeagueSLGWorldManager.getInstance().getAllocatedAreaIds().size() < areas.size()){
                //有赛区分配还没有拿到回执
                allocateArea();
                GameLogger.slgDebug("League SLG wait for allocate finish");
            }else{
                this.allocateFinish = true;
                GameLogger.slgDebug("League SLG allocate finish. Area size : " + areas.size());
            }

        }
    }

    private static class AllocateAreaMessageCallbackTask extends TLMessageCallbackTask {
        private final Map.Entry<Integer, SLGRegister> entry;

        public AllocateAreaMessageCallbackTask(Map.Entry<Integer, SLGRegister> entry) {
            this.entry = entry;
        }

        @Override
        public void complete(CallbackResponse response) {
            LeagueSLGWorldManager.getInstance().getAllocatedAreaIds().add(entry.getKey());
        }

        @Override
        public void timeout() {
            //等下次重新请求即可
            GameLogger.slgDebug("SLG allocateArea timeout. serverId : " + entry.getKey());
        }
    }

    class ServerGroup{
        public int leagueCount;
        public Set<Integer> sids;

        /**
         * 自动踢掉没有帮派参与的服务器
         */
        public ServerGroup(Set<Integer> sids, Map<Integer, SLGRegister> registers) {
            this.sids = new HashSet<>();

            this.leagueCount = 0;
            for(int sid : sids){
                if(registers.containsKey(sid)) {
                    int count = registers.get(sid).getLeagues().size();
                    if(count > 0){
                        this.sids.add(sid);
                        this.leagueCount += count;
                    }
                }
            }
        }
    }
}

package com.gy.server.world.leagueSLG;

import com.gy.server.annotation.MessageMethod;
import com.gy.server.annotation.MessageServiceBean;
import com.gy.server.core.MessageServerType;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.command.CommandRequestParams;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.league.League;
import com.gy.server.game.leagueSLG.LeagueSLGService;
import com.gy.server.game.leagueSLG.template.SLGCityTemplate;
import com.gy.server.game.leagueSLG.template.SLGCombatMedalRewardTemplate;
import com.gy.server.game.leagueSLG.template.SLGCooperationTemplate;
import com.gy.server.game.leagueSLG.template.SLGDepartmentTemplate;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.game.text.Text;
import com.gy.server.game.text.TextParamCountdown;
import com.gy.server.packet.PbCommons;
import com.gy.server.packet.PbLeague;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbSLG;
import com.gy.server.utils.time.DateTimeUtil;
import com.gy.server.world.leagueSLG.bean.*;
import com.gy.server.world.leagueSLG.mainStatus.SLGNodeType;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.rpc.RPCUtil;

import java.util.*;

import static com.gy.server.world.leagueSLG.mainStatus.SLGNodeType.Battle_00;

/**
 * @program: tl_game_4
 * @description: RPC接口 world用
 * @author: Huang.Xia
 * @create: 2024/11/28
 **/
@MessageServiceBean(description = "帮派SLG WORLD消息服务", messageServerType = MessageServerType.world)
public class LeagueSLGWorldCommandService {

    @MessageMethod(description = "game获取当前状态")
    private static void getStatus(ServerCommandRequest request, CommandRequestParams params) {
        request.addCallbackParam(LeagueSLGWorldManager.getInstance().getStatus());
    }

    @MessageMethod(description = "分配赛区")
    private static void allocateArea(ServerCommandRequest request, CommandRequestParams params) {
        //构建内存结构
        int areaId = params.getParam(0);
        SLGRegister leagues = params.getParam(1);

        LeagueSLGWorldManager.getInstance().allocateArea(areaId, leagues);
        GameLogger.slgDebug("SLG allocate area: " + areaId);

        //通知gs赛区分配结果,按照区服汇总
        Map<Integer, Set<Long>> sid2Lids = new HashMap<>();
        for(Long lid : leagues.getLeagues().keySet()){
            Set<Long> set = sid2Lids.getOrDefault(League.getRealServerId(lid), new HashSet<>());
            set.add(lid);
            sid2Lids.put(League.getRealServerId(lid), set);
        }

        ServerCommandRequest req = CommandRequests.newServerCommandRequest("LeagueSLGGameCommandService.matchNotice");
        for(Map.Entry<Integer, Set<Long> > entry : sid2Lids.entrySet()){
            TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, entry.getKey(), req, entry.getValue());
        }
    }

    @MessageMethod(description = "报名")
    private static void register(ServerCommandRequest request, CommandRequestParams params) {
        SLGRegister register = params.getParam(0);
        LeagueSLGWorldManager.getInstance().getRegisters().put(request.getServerId(), register);
        GameLogger.slgDebug("SLG registered by serverId: " + request.getServerId());
    }

    @MessageMethod(description = "状态更新同步")
    private static void statusChanged(ServerCommandRequest request, CommandRequestParams params) {
        int id = params.getParam(0);
        int combatDayIndex = params.getParam(1);

        SLGNodeType nodeType =  SLGNodeType.valueOf(id);
        LeagueSLGWorldManager.getInstance().setStatus(nodeType);

        LeagueSLGWorldGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueWorldSLG);
        globalData.setCombatDayIndex(combatDayIndex);

        PbSLG.SLGBattleStatus battleStatus = nodeType.generateBattleStatus();

        GameLogger.slgDebug("SLG WORLD status changed to " + nodeType.name());
        for(SLGArea area : LeagueSLGWorldManager.getInstance().getAreas().values()){
            for(SLGBattleField field : area.getBattleFields()){
                field.getSyncData().setBattleStatus(battleStatus);
            }
        }

        switch (nodeType){
            case Matching:{
                LeagueSLGWorldManager.getInstance().startMatching();
                break;
            }
            case Battle_00:{
                LeagueSLGWorldManager.getInstance().startBattle();
                break;
            }
            case Battle_10:{
                //兵营开始生产
                for(SLGArea area : LeagueSLGWorldManager.getInstance().getAreas().values()){
                    for(SLGBattleField field : area.getBattleFields()){
                        for(SLGLeague league : field.getLeagues().values()){
                            for(SLGPlayer player : league.getPlayers().values()){
                                player.getCamp().setProduceStartTime(ServerConstants.getCurrentTimeMillis());
                            }
                        }
                        long startTime = DateTimeUtil.toMillis(Battle_00.getEndTimeToday());
                        for(SLGCity city : field.getCities().values()){
                            //大本营开始生产
                            if(city.getLid() > 0 && city.getCityTemplate().type == SLGCityTemplate.TYPE_大本营){
                                city.setStartProduceTime(startTime);
                            }
                        }
                    }
                }
                break;
            }
            case Battle_12:
            case Battle_18:{
                //谋士开始生效
                activeStrategies();
                break;
            }
            case Battle_16: {
                //谋略失效
                for(SLGArea area : LeagueSLGWorldManager.getInstance().getAreas().values()){
                    for(SLGBattleField field : area.getBattleFields()){
                        for(SLGLeague league : field.getLeagues().values()){
                            league.disactiveStrategy();
                        }
                    }
                }
                //重新刷新谋略
                for(SLGArea area : LeagueSLGWorldManager.getInstance().getAreas().values()){
                    for(SLGBattleField field : area.getBattleFields()){
                        field.refreshStrategy();
                    }
                }
                break;
            }
            case Battle_19: {
                LeagueSLGWorldManager.getInstance().getCityShielded().remove(SLGCityTemplate.TYPE_大本营);
                break;
            }
            case Battle_20: {
                LeagueSLGWorldManager.getInstance().getCityShielded().remove(SLGCityTemplate.TYPE_常规城池4);
                break;
            }
            case Reward:
            case Battle_22: {
                //结算
                for(SLGArea area : LeagueSLGWorldManager.getInstance().getAreas().values()) {
                    area.settle();
                }
                break;
            }
            case Rest: {
                for(SLGArea area : LeagueSLGWorldManager.getInstance().getAreas().values()) {
                    area.seasonFinish();
                }
                LeagueSLGWorldManager.getInstance().getAreas().clear();
                break;
            }


        }
    }

    private static void activeStrategies(){
        //谋士开始生效
        for(SLGArea area : LeagueSLGWorldManager.getInstance().getAreas().values()){
            for(SLGBattleField field : area.getBattleFields()){
                for(SLGCity city : field.getCities().values()){
                    if(city.getStrategyId() > 0 && city.getLid() > 0){
                        SLGLeague league = field.getLeagues().get(city.getLid());
                        league.activeStrategy(city.getStrategyId());
                    }
                }
            }
        }
    }

    @MessageMethod(description = "战前赛区数据刷新检查")
    private static void refreshCheck(ServerCommandRequest request, CommandRequestParams params) {
        request.addCallbackParam(LeagueSLGWorldManager.getInstance().isNeedRefresh());
    }

    @MessageMethod(description = "行军")
    private static void march(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        List<Integer> path = params.getParam(1);

        int rst;
        SLGBattleField field = LeagueSLGWorldManager.getInstance().getBattleFieldByPid(playerId);
        if(field != null){
            SLGPlayer player = field.getPlayerByPid(playerId);
            rst = field.canMarch(player, path);
            if(rst == Text.没有异常){
                field.march(player, path);
            }
        }else{
            rst = Text.SLG_无参赛资格;
        }
        request.addCallbackParam(rst);
    }


    @MessageMethod(description = "加速行军")
    private static void marchSpeedUp(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);

        int rst = Text.没有异常;
        long time = 0;
        SLGBattleField field = LeagueSLGWorldManager.getInstance().getBattleFieldByPid(playerId);
        if(field != null){
            SLGPlayer player = field.getPlayerByPid(playerId);
            if(player.getArriveTimeLeft() > 0){
                player.speedUp();
            }
            time = player.getArriveTime();

            //全局广播
            player.getLeague().getField().getSyncData().addMarches(player.genSLGMarchBuilder());
        }else{
            rst = Text.SLG_无参赛资格;
        }
        request.addCallbackParam(rst);
        request.addCallbackParam(time);
    }

    @MessageMethod(description = "结盟/取消结盟")
    private static void align(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        long lid = params.getParam(1);

        int rst = Text.没有异常;
        SLGBattleField field = LeagueSLGWorldManager.getInstance().getBattleFieldByPid(playerId);
        if(field != null){
            SLGPlayer player = field.getPlayerByPid(playerId);
            if(player != null) {
                SLGLeague league = player.getLeague();
                if(player.isCommander()){
                    if(league.getAlignmentLeagueIds().contains(lid)){
                        league.getAlignmentLeagueIds().remove(lid);
                    }else{
                        league.getAlignmentLeagueIds().add(lid);
                    }

                    //推送结盟关系变更
                    league.setSyncStatus(true);
                }else{
                    rst = Text.SLG_不是指挥官;
                }
            }else{
                rst = Text.参数异常;
            }
        }else{
            rst = Text.SLG_无参赛资格;
        }
        request.addCallbackParam(rst);
    }

    @MessageMethod(description = "兵营信息")
    private static void campInfo(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        PbProtocol.SLGCampInfoRst.Builder rst  = PbProtocol.SLGCampInfoRst.newBuilder();
        rst.setResult(Text.genOkServerRstInfo());

        SLGBattleField field = LeagueSLGWorldManager.getInstance().getBattleFieldByPid(playerId);
        if (field != null) {
            SLGPlayer player = field.getPlayerByPid(playerId);
            if(player != null) {
                rst.setCampArmy(player.getCamp().getTotalArmy());
                rst.setTitleId(player.getCamp().getTid());
                rst.setSpeed(player.getCamp().getTotalSpeed());
                rst.setMarchMinArmy(player.getCamp().marchNeed(player));
            }else{
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
            }
        }else{
            rst.setResult(Text.genServerRstInfo(Text.SLG_无参赛资格));
        }
        request.addCallbackParam(rst.build());
    }

    @MessageMethod(description = "补充兵力")
    private static void playerAddArmy(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        int army = params.getParam(1);

        int rst;
        int campArmy = 0;
        int playerArmy = 0;
        SLGBattleField field = LeagueSLGWorldManager.getInstance().getBattleFieldByPid(playerId);
        if(field != null){
            SLGPlayer player = field.getPlayerByPid(playerId);
            rst = player.getCamp().addArmy(player, army);
            campArmy = player.getCamp().getTotalArmy();
            playerArmy = player.getArmy();
        }else{
            rst = Text.SLG_无参赛资格;
        }
        request.addCallbackParam(rst);
        request.addCallbackParam(campArmy);
        request.addCallbackParam(playerArmy);
    }


    @MessageMethod(description = "军营道具补兵")
    private static void campAddArmy(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        int armyCount = params.getParam(1);

        int rst = Text.没有异常;
        int campArmy = 0;
        SLGBattleField field = LeagueSLGWorldManager.getInstance().getBattleFieldByPid(playerId);
        if(field != null){
            SLGPlayer player = field.getPlayerByPid(playerId);
            if(player != null) {
                player.getCamp().addCampArmy(armyCount);
                campArmy = player.getCamp().getTotalArmy();
            }else{
                rst = Text.参数异常;
            }
        }else{
            rst = Text.SLG_无参赛资格;
        }
        request.addCallbackParam(rst);
        request.addCallbackParam(campArmy);
    }

    @MessageMethod(description = "同步兵力")
    private static void syncArmy(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        SLGPlayerSyncData syncData = params.getParam(1);

        int rst = Text.没有异常;
        int titleId = 0;
        int playerMaxArmy = 0;
        SLGBattleField field = LeagueSLGWorldManager.getInstance().getBattleFieldByPid(playerId);
        if(field != null){
            SLGLeague league = field.getLeagueByPid(playerId);
            SLGPlayer player = league.getPlayers().get(playerId);

            //先结算战力，再同步。保证前面的产出使用的是原来的增速
            player.getCamp().updateArmy();

            league.syncPlayerArmy(playerId,syncData);
            titleId = player.getCamp().getTid();
            playerMaxArmy = player.getCamp().getTotalArmy();
            player.setStatusNeedSync(true);
        }else{
            rst = Text.SLG_无参赛资格;
        }
        request.addCallbackParam(rst);
        request.addCallbackParam(titleId);
        request.addCallbackParam(playerMaxArmy);
    }

    @MessageMethod(description = "兵案信息")
    private static void departmentInfo(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        PbProtocol.SLGDepartmentInfoRst.Builder rst  = PbProtocol.SLGDepartmentInfoRst.newBuilder();
        rst.setResult(Text.genOkServerRstInfo());
        SLGBattleField field = LeagueSLGWorldManager.getInstance().getBattleFieldByPid(playerId);
        if(field != null){
            SLGPlayer player = field.getPlayerByPid(playerId);
            if(player != null) {
                rst.setExp(player.getLeague().getDepartmentExp());
                rst.setRewardLevel(player.getDepartmentRewardLevel());
                for(SLGArmyExpend expend : player.getLeague().getArmyExpends()){
                    if(!player.getFetchedArmyExpends().contains(expend.getId())){
                        rst.addList(expend.genPb());
                    }
                }
            }else{
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
            }
        }else{
            rst.setResult(Text.genServerRstInfo(Text.SLG_无参赛资格));
        }
        request.addCallbackParam(rst.build());
    }

    @MessageMethod(description = "发放军饷")
    private static void armyExpendGrant(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        int itemId = params.getParam(1);
        int itemCount = params.getParam(2);

        int rst = Text.没有异常;
        List<PbSLG.SLGArmyExpend> list = new ArrayList<>();
        SLGBattleField field = LeagueSLGWorldManager.getInstance().getBattleFieldByPid(playerId);
        if(field != null){
            SLGPlayer player = field.getPlayerByPid(playerId);
            if(player != null) {
                for(int i = 0; i < itemCount; i++){
                    SLGArmyExpend expend = new SLGArmyExpend();
                    expend.setId(player.getLeague().getArmyExpends().size() + 1);
                    expend.setItemId(itemId);
                    expend.setSendPlayerId(playerId);
                    player.getLeague().getArmyExpends().add(expend);
                    player.setGrantArmyExpendCount(player.getGrantArmyExpendCount() + 1);
                }

                for(SLGArmyExpend expend : player.getLeague().getArmyExpends()){
                    if(!player.getFetchedArmyExpends().contains(expend.getId())){
                        list.add(expend.genPb().build());
                    }
                }
            }else{
                rst = Text.参数异常;
            }
        }else{
            rst = Text.SLG_无参赛资格;
        }
        request.addCallbackParam(rst);
        request.addCallbackParam(list);
    }

    @MessageMethod(description = "军饷领取")
    private static void armyExpendFetch(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        Set<Integer> ids = params.getParam(1);//领取的id

        int rst = Text.没有异常;
        int exp = 0;
        SLGBattleField field = LeagueSLGWorldManager.getInstance().getBattleFieldByPid(playerId);
        if(field != null){
            SLGPlayer player = field.getPlayerByPid(playerId);
            if(player != null) {
                for(int id : ids){
                    if(!player.getFetchedArmyExpends().contains(id)){
                        player.getFetchedArmyExpends().add(id);

                        for(SLGArmyExpend expend : player.getLeague().getArmyExpends()){
                            if(expend.getId() == id){
                                player.getLeague().setDepartmentExp(player.getLeague().getDepartmentExp() + expend.getExp());
                                break;
                            }
                        }
                    }
                }

               exp = player.getLeague().getDepartmentExp();
            }else{
                rst = Text.参数异常;
            }
        }else{
            rst = Text.SLG_无参赛资格;
        }
        request.addCallbackParam(rst);
        request.addCallbackParam(exp);
    }

    @MessageMethod(description = "军饷记录")
    private static void armyExpendRecord(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        int type = params.getParam(1);//1 发放 2 领取

        int rst = Text.没有异常;
        List<PbSLG.SLGArmyExpendRankNode> list = new ArrayList<>();
        SLGBattleField field = LeagueSLGWorldManager.getInstance().getBattleFieldByPid(playerId);
        if(field != null){
            SLGPlayer player = field.getPlayerByPid(playerId);
            if(player != null) {
                if(type == 1){
                    list = player.getLeague().genArmyExpendGrantRecord();
                }else{
                    list = player.getLeague().genArmyExpendFetchRecord();
                }
            }else{
                rst = Text.参数异常;
            }
        }else{
            rst = Text.SLG_无参赛资格;
        }
        request.addCallbackParam(rst);
        request.addCallbackParam(list);
    }

    @MessageMethod(description = "领取募兵令")
    private static void departmentReward(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);

        int rst = Text.没有异常;
        int rewardLevel = 0;
        int exp = 0;
        SLGBattleField field = LeagueSLGWorldManager.getInstance().getBattleFieldByPid(playerId);
        List<Integer> rewardLevelList = new ArrayList<>();
        if(field != null){
            SLGPlayer player = field.getPlayerByPid(playerId);
            if(player != null) {
                while(true) {
                    int nowLevel = player.getDepartmentRewardLevel();
                    SLGDepartmentTemplate departmentTemplate = LeagueSLGService.getDepartments().get(nowLevel + 1);
                    if (departmentTemplate != null) {
                        if (player.getLeague().getDepartmentExp() >= departmentTemplate.exp) {
                            player.setDepartmentRewardLevel(nowLevel + 1);
                            rewardLevelList.add(nowLevel + 1);
                        } else {
                            if(rewardLevelList.size() == 0) {
                                rst = Text.SLG_兵案不足;
                            }
                            break;
                        }
                    } else {
                        if(rewardLevelList.size() == 0) {
                            rst = Text.参数异常;
                        }
                        break;
                    }
                }

                exp = player.getLeague().getDepartmentExp();
                rewardLevel = player.getDepartmentRewardLevel();
            }else{
                rst = Text.参数异常;
            }
        }else{
            rst = Text.SLG_无参赛资格;
        }
        request.addCallbackParam(rst);
        request.addCallbackParam(exp);
        request.addCallbackParam(rewardLevel);
        request.addCallbackParam(rewardLevelList);
    }

    @MessageMethod(description = "互助信息")
    private static void helpInfo(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);

        int rst = Text.没有异常;
        Set<Long> helpSet = null;
        SLGBattleField field = LeagueSLGWorldManager.getInstance().getBattleFieldByPid(playerId);
        if(field != null){
            SLGPlayer player = field.getPlayerByPid(playerId);
            if(player != null) {
                helpSet = player.getLeague().getHelpPids();
            }else{
                rst = Text.参数异常;
            }
        }else{
            rst = Text.SLG_无参赛资格;
        }
        request.addCallbackParam(rst);
        if(helpSet != null){
            request.addCallbackParam(helpSet);
        }
    }

    @MessageMethod(description = "互助")
    private static void charge(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);

        SLGBattleField field = LeagueSLGWorldManager.getInstance().getBattleFieldByPid(playerId);
        if(field != null){
            SLGPlayer player = field.getPlayerByPid(playerId);
            if(player != null) {
                Set<Long> helpSet = player.getLeague().getHelpPids();
                helpSet.add(playerId);
            }
        }
    }

    @MessageMethod(description = "互助信息")
    private static void helpReward(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        int helpId = params.getParam(1);

        int rst = Text.参数异常;
        Set<Long> helpSet = null;
        SLGBattleField field = LeagueSLGWorldManager.getInstance().getBattleFieldByPid(playerId);
        if(field != null){
            SLGPlayer player = field.getPlayerByPid(playerId);
            if(player != null) {
                int helpCount = player.getLeague().getHelpPids().size();
                SLGCooperationTemplate helpTemplate = LeagueSLGService.getHelpRewards().get(helpId);
                if(helpTemplate != null){
                    if(helpCount < helpTemplate.count){
                        rst = Text.SLG_互助次数不足;
                    }else{
                        rst = Text.没有异常;
                    }
                }else{
                    rst = Text.参数异常;
                }
            }else{
                rst = Text.参数异常;
            }
        }else{
            rst = Text.SLG_无参赛资格;
        }
        request.addCallbackParam(rst);
    }


    @MessageMethod(description = "战斗")
    private static void fight(ServerCommandRequest request, CommandRequestParams params) {
        int rst = Text.没有异常;
        PbSLG.SLGFight.Builder fightBuilder = null;
        try {
            if(!LeagueSLGWorldManager.getInstance().getStatus().canPlayerOperate()){
                rst = Text.SLG_不在战斗时间;
                return;
            }

            long playerId = params.getParam(0);
            /**
             * 目标玩家id，或者守卫序号。也可能未指定目标，-1 自动进攻 -2 自动清场
             */
            long targetId = params.getParam(1);
            boolean isGuard = params.getParam(2);

            SLGBattleField field = LeagueSLGWorldManager.getInstance().getBattleFieldByPid(playerId);
            if (field != null) {
                SLGPlayer player = field.getPlayerByPid(playerId);
                if (player.isMarching()) {
                    rst = Text.SLG_行军中不能进行该操作;
                    return;
                }

                SLGCity city = field.getCities().get(player.getCityId());
                if(city.isFighting(player)){
                    rst = Text.SLG_自己正在战斗中;
                    return;
                }

                if(targetId < 0){
                    targetId = city.getAutoTarget(player.getLeague().getId(), (int) targetId);
                    if(targetId < 0){
                        isGuard = true;
                        targetId = -targetId;
                    }
                }

                if(isGuard){
                    SLGGuard guard = city.getGuards().get(Integer.valueOf((int) targetId));
                    if(targetId > 0) {
                        if (guard == null || guard.getArmy() <= 0) {
                            //守卫死了
                            rst = Text.SLG_目标守卫已死;
                            return;
                        }
                    }else{
                        //随机选一个
                        if(city.getGuards().size() > 0){
                            List<Integer> list = new ArrayList<>(city.getGuards().keySet());
                            Collections.sort(list);
                            guard = city.getGuards().get(list.get(0));
                        }else {
                            //守卫死了
                            rst = Text.SLG_目标守卫已死;
                            return;
                        }
                    }

                    if(city.isFighting(guard)){
                        rst = Text.SLG_目标正在战斗中;
                    }

                    //不能攻击己方
                    if(player.getLeague().getId() == city.getLid()){
                        rst = Text.SLG_不能攻击己方;
                        return;
                    }

                    SLGFight fight = new SLGFight(player, guard);
                    city.addFight(fight);

                    //战况同步
                    SLGLeague league = field.getLeagues().get(city.getLid());
                    if(league != null){
                        league.addCityAttackedMsg(city.getTid());
                    }
                    fightBuilder = fight.genPb();
                }else{
                    SLGPlayer target = null;
                    if(targetId > 0){
                        target = city.getPlayerByPid(targetId);
                    }else {
                        target = city.getRandomDefencePlayer(player);
                    }
                    if(target == null){
                        rst = Text.SLG_目标不在当前据点;
                        return;
                    }

                    if(city.isFighting(target)){
                        rst = Text.SLG_目标正在战斗中;
                        return;
                    }

                    if(target.isMarching()){
                        rst = Text.SLG_目标正在行军;
                        return;
                    }

                    SLGFight fight = new SLGFight(player, target);
                    city.addFight(fight);

                    //战况同步
                    if(target.getLeague().getId() == city.getLid()){
                        //防守方被攻击才会同步
                        target.getLeague().addCityAttackedMsg(city.getTid());
                    }
                    target.addAttackedMsg();

                    fightBuilder = fight.genPb();
                }
            } else {
                rst = Text.SLG_无参赛资格;
            }
        }catch (Exception e){
            rst = Text.服务器异常;
        }finally {
            PbProtocol.SLGAttackRst.Builder b = PbProtocol.SLGAttackRst.newBuilder();
            b.setResult(Text.genServerRstInfo(rst));
            if(fightBuilder != null){
                b.setFight(fightBuilder);
                GameLogger.slgDebug("fight : " + fightBuilder.toString().replace("\n", ""));
            }
            request.addCallbackParam(b.build());
        }
    }

    @MessageMethod(description = "营地信息")
    private static void homeInfo(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        PbProtocol.SLGHomeInfoRst.Builder builder = PbProtocol.SLGHomeInfoRst.newBuilder();

        int rst = Text.没有异常;
        SLGBattleField field = LeagueSLGWorldManager.getInstance().getBattleFieldByPid(playerId);
        if(field != null){
            SLGPlayer player = field.getPlayerByPid(playerId);
            if(player != null) {
                for(SLGPlayer p : player.getLeague().getMedalOrderedPlayers()){
                    if(builder.getCommandersCount() < 3){
                        //改为展示战功前三玩家
                        builder.addCommanders(p.getPid());
                    }else{
                        break;
                    }
                }
                builder.addAllTargetCityIds(player.getLeague().getTargetCityIds());
            }else{
                rst = Text.SLG_无参赛资格;
            }
        }else{
            rst = Text.SLG_无参赛资格;
        }
        builder.setResult(Text.genServerRstInfo(rst));
        request.addCallbackParam(builder.build());
    }

    @MessageMethod(description = "更换目标")
    private static void changeTarget(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        int cityId = params.getParam(1);
        int order = params.getParam(2);

        int rst = Text.没有异常;
        List<Integer> list = new ArrayList<>();
        SLGBattleField field = LeagueSLGWorldManager.getInstance().getBattleFieldByPid(playerId);
        if(field != null){
            SLGPlayer player = field.getPlayerByPid(playerId);
            if(player != null) {
                if(player.isCommander()){
                    SLGCity city = field.getCities().get(cityId);
                    if(city != null){
                        List<Integer> targetCityIds = player.getLeague().getTargetCityIds();
                        int index = order - 1;
                        if(index <= 1){
                            if(index == 0){
                                if(targetCityIds.size() > 0) {
                                    targetCityIds.set(0, cityId);
                                }else{
                                    targetCityIds.add(cityId);
                                }
                            }else{
                                if(targetCityIds.size() > 1){
                                    targetCityIds.set(1, cityId);
                                }else if(targetCityIds.size() > 0){
                                    targetCityIds.add(cityId);
                                }else{
                                    targetCityIds.add(-1);
                                    targetCityIds.add(cityId);
                                }
                            }

                            list = targetCityIds;
                        }else {
                            rst = Text.参数异常;
                        }

                    }else{
                        rst = Text.参数异常;
                    }
                }else{
                    rst = Text.SLG_不是指挥官;
                }
            }else{
                rst = Text.参数异常;
            }
        }else{
            rst = Text.SLG_无参赛资格;
        }
        request.addCallbackParam(rst);
        request.addCallbackParam(list);
    }

    @MessageMethod(description = "城池信息")
    private static void cityList(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);

        int rst = Text.没有异常;
        List<PbSLG.SLGCityProduce> list = new ArrayList<>();
        SLGBattleField field = LeagueSLGWorldManager.getInstance().getBattleFieldByPid(playerId);
        if(field != null){
            SLGPlayer player = field.getPlayerByPid(playerId);
            if(player != null) {
                for(SLGCity city : field.getCities().values()){
                    if(city.getLid() == player.getLeague().getId()) {
                        list.add(city.genCityProducePb(player.getLastFarmTime(), player.getLeague()).build());
                    }
                }
            }else{
                rst = Text.参数异常;
            }

        }else{
            rst = Text.SLG_无参赛资格;
        }
        request.addCallbackParam(rst);
        request.addCallbackParam(list);
    }

    @MessageMethod(description = "领取税银")
    private static void taxFetch(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);

        int rst = Text.没有异常;
        int tax = 0;
        List<PbSLG.SLGCityProduce> list = new ArrayList<>();
        SLGBattleField field = LeagueSLGWorldManager.getInstance().getBattleFieldByPid(playerId);
        if(field != null){
            SLGPlayer player = field.getPlayerByPid(playerId);
            if(player != null) {
                long lastFarmTime = player.getLastFarmTime();
                player.setLastFarmTime(ServerConstants.getCurrentTimeMillis());
                for(SLGCity city : field.getCities().values()){
                    if(city.getLid() == player.getLeague().getId()) {
                        tax += city.calculateTax(lastFarmTime);
                        list.add(city.genCityProducePb(player.getLastFarmTime(), player.getLeague()).build());
                    }
                }

            }else{
                rst = Text.参数异常;
            }

        }else{
            rst = Text.SLG_无参赛资格;
        }
        request.addCallbackParam(rst);
        request.addCallbackParam(tax);
        request.addCallbackParam(list);
    }

    @MessageMethod(description = "城池战报")
    private static void fightReport(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);

        int rst = Text.没有异常;
        List<PbSLG.SLGFightRecord> list = new ArrayList<>();
        SLGBattleField field = LeagueSLGWorldManager.getInstance().getBattleFieldByPid(playerId);
        if(field != null){
            SLGPlayer player = field.getPlayerByPid(playerId);
            if(player != null) {
                list.addAll(player.getLeague().getFightRecords());
            }else{
                rst = Text.参数异常;
            }

        }else{
            rst = Text.SLG_无参赛资格;
        }
        request.addCallbackParam(rst);
        request.addCallbackParam(list);
    }

    @MessageMethod(description = "个人战功记录")
    private static void achievementRecord(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);

        int rst = Text.没有异常;
        List<PbSLG.SLGAchievementRecord> list = new ArrayList<>();
        SLGBattleField field = LeagueSLGWorldManager.getInstance().getBattleFieldByPid(playerId);
        if(field != null){
            SLGPlayer player = field.getPlayerByPid(playerId);
            if(player != null) {
                list.addAll(player.getFightRecords());
            }else{
                rst = Text.参数异常;
            }

        }else{
            rst = Text.SLG_无参赛资格;
        }
        request.addCallbackParam(rst);
        request.addCallbackParam(list);
    }

    @MessageMethod(description = "个人战功领奖")
    private static void achievementReward(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        Set<Integer> rewardIds = params.getParam(1);

        int rst = Text.没有异常;
        SLGBattleField field = LeagueSLGWorldManager.getInstance().getBattleFieldByPid(playerId);
        if(field != null){
            SLGPlayer player = field.getPlayerByPid(playerId);
            if(player != null) {
                for(int id : rewardIds){
                    SLGCombatMedalRewardTemplate achievement = LeagueSLGService.getMedalRewards().get(id);
                    if(achievement != null){
                        if(player.getCombatMedals() < achievement.medalGoal){
                            rst = Text.SLG_战功不足;
                            break;
                        }
                    }else{
                        rst = Text.参数异常;
                        break;
                    }
                }
            }else{
                rst = Text.参数异常;
            }

        }else{
            rst = Text.SLG_无参赛资格;
        }
        request.addCallbackParam(rst);
        request.addCallbackParam(rewardIds);
    }

    @MessageMethod(description = "谋士情报")
    private static void strategyInfo(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        PbProtocol.SLGStrategyInfoRst.Builder builder = PbProtocol.SLGStrategyInfoRst.newBuilder();
        builder.setResult(Text.genOkServerRstInfo());

        SLGBattleField field = LeagueSLGWorldManager.getInstance().getBattleFieldByPid(playerId);
        if(field != null){
            for(SLGCity city : field.getCities().values()){
                if(city.getStrategyId() > 0) {
                    PbSLG.SLGStrategyInfo.Builder b = PbSLG.SLGStrategyInfo.newBuilder();
                    b.setLeagueId(city.getLid())
                            .setCityId(city.getTid())
                            .setStrategyId(city.getStrategyId());
                    builder.addInfos(b.build());
                }
            }
        }else{
            builder.setResult(Text.genServerRstInfo(Text.SLG_无参赛资格));
        }
        request.addCallbackParam(builder.build());
    }

    @MessageMethod(description = "赛区帮派排行（含领奖记录)")
    private static void areaLeagueRank(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);

        int rst = Text.没有异常;
        List<PbSLG.SLGLeagueRankNode> list = new ArrayList<>();
        SLGBattleField field = LeagueSLGWorldManager.getInstance().getBattleFieldByPid(playerId);
        if(field != null){
            SLGArea area = field.getArea();
            int rank = 1;
            for(SLGLeagueRankNode node : area.getRankNodes()){
                list.add(node.genPb(rank, area));
                rank++;
            }
        }else{
            rst = Text.SLG_无参赛资格;
        }
        request.addCallbackParam(rst);
        request.addCallbackParam(list);
    }

    @MessageMethod(description = "赛区帮派排行-领奖")
    public static void areaRankReward(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        int rewardType = params.getParam(1);

        int rst = Text.没有异常;
        int rank = -1;
        SLGBattleField field = LeagueSLGWorldManager.getInstance().getBattleFieldByPid(playerId);
        if(field != null){
            SLGPlayer player = field.getPlayerByPid(playerId);
            if(player != null) {
                SLGArea area = field.getArea();
                if(area.isSettled()) {
                    if (rewardType == 1) {
                        //个人排名
                        rank = area.getPlayerRank(playerId);
                    } else {
                        //帮派排名
                        rank = area.getLeagueRank(player.getLeague().getId());
                    }
                }else{
                    rst = Text.SLG_结算中请稍后再领奖;
                }
            }else{
                rst = Text.SLG_无参赛资格;
            }
        }else{
            rst = Text.SLG_无参赛资格;
        }
        request.addCallbackParam(rst);
        request.addCallbackParam(rank);
    }

    @MessageMethod(description = "请求个人战功数")
    private static void myFightMedals(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);

        SLGBattleField field = LeagueSLGWorldManager.getInstance().getBattleFieldByPid(playerId);
        if (field != null) {
            SLGPlayer myPlayer = field.getPlayerByPid(playerId);
            if (myPlayer != null) {
                request.addCallbackParam(Text.没有异常);
                request.addCallbackParam(myPlayer.getCombatMedals());
                return;
            }
        }
        request.addCallbackParam(Text.SLG_无参赛资格);
    }

    @MessageMethod(description = "赛区个人排行（含领奖记录)")
    private static void areaPersonalRank(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);

        int rst = Text.没有异常;
        List<PbSLG.SLGPersonalRankNode> list = new ArrayList<>();
        SLGBattleField field = LeagueSLGWorldManager.getInstance().getBattleFieldByPid(playerId);
        if(field != null){
            SLGPlayer myPlayer = field.getPlayerByPid(playerId);
            if(myPlayer != null) {
                SLGArea area = field.getArea();
                List<SLGPlayer> players = area.getPlayerRanks();
                Set<Long> pids = new HashSet<>();
                for (int i = 0; i < players.size() && i < 50; i++) {
                    SLGPlayer player = players.get(i);
                    pids.add(player.getPid());
                }
                Map<Long, MiniGamePlayer> miniPlayers = PlayerHelper.getMiniPlayersForMap(pids);
                int rank = 1;
                for (int i = 0; i < players.size() && i < 50; i++) {
                    SLGPlayer player = players.get(i);
                    MiniGamePlayer miniPlayer = miniPlayers.get(player.getPid());
                    if (miniPlayer != null) {
                        PbSLG.SLGPersonalRankNode.Builder b = PbSLG.SLGPersonalRankNode.newBuilder();
                        b.setUser(miniPlayer.genMinMiniUser())
                                .setLeagueName(player.getLeague().getSimpleLeague().getName())
                                .setServerId(Player.getRealServerId(player.getPid()))
                                .setRank(rank)
                                .setCombatMedals(player.getCombatMedals());
                        list.add(b.build());
                    }
                    rank++;
                }

                int myRank = -1;
                for(int i = 0; i < players.size(); i++){
                    SLGPlayer player = players.get(i);
                    if(player.getPid() == myPlayer.getPid()) {
                        myRank = i + 1;
                        break;
                    }
                }
                request.addCallbackParam(rst);
                request.addCallbackParam(list);
                request.addCallbackParam(myRank);
                request.addCallbackParam(myPlayer.getCombatMedals());
                return;
            }else{
                rst = Text.参数异常;
            }

        }else{
            rst = Text.SLG_无参赛资格;
        }
        request.addCallbackParam(rst);
    }

    @MessageMethod(description = "赛程分组情况")
    private static void groupInfo(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        int fieldIndex = params.getParam(1);
        int areaId = params.getParam(2);

        int rst = Text.没有异常;
        List<PbSLG.SLGLeagueGroupNode> list = new ArrayList<>();
        SLGBattleField field = null;
        if(fieldIndex < 0){
            field = LeagueSLGWorldManager.getInstance().getBattleFieldByPid(playerId);
        }else{
            SLGArea area = LeagueSLGWorldManager.getInstance().getArea(areaId);
            if(area != null){
                field = area.getBattleFields().get(fieldIndex);
            }
        }

        if(field != null){
            field = field.getArea().getBattleFields().get(fieldIndex);
            List<SLGLeague> leagues = new ArrayList<>(field.getLeagues().values());
            Collections.sort(leagues, SLGLeague.scoreComparator);
            int rank = 1;
            for(SLGLeague league : leagues){
                list.add(league.genLeagueRankNode(rank++));
            }
        }else{
            rst = Text.数据异常;
        }
        request.addCallbackParam(rst);
        request.addCallbackParam(list);
        List<Integer> indexList = new ArrayList<>();
        for(SLGBattleField f : field.getArea().getBattleFields()){
            indexList.add(f.getIndex());
        }
        request.addCallbackParam(indexList);
    }

    @MessageMethod(description = "竞猜信息")
    private static void guessInfo(ServerCommandRequest request, CommandRequestParams params) {
        PbProtocol.SLGGuessInfoRst.Builder rst = PbProtocol.SLGGuessInfoRst.newBuilder();
        rst.setResult(Text.genOkServerRstInfo());
        long playerId = params.getParam(0);

        SLGBattleField field = LeagueSLGWorldManager.getInstance().getBattleFieldByPid(playerId);
        if(field != null){
            if(field.getArea().getGuessLeagues().size() > 0) {
                List<SLGLeague> leagues = new ArrayList<>(field.getArea().getGuessLeagues());
                Collections.sort(leagues, SLGLeague.hotComparator);
                for (SLGLeague league : leagues) {
                    PbLeague.SimpleLeague.Builder lb = league.getSimpleLeague().toBuilder();
                    lb.setLeader(PbCommons.MinMiniUser.newBuilder());//置空数据，减小流量开销
                    PbSLG.SLGGuessNode.Builder b = PbSLG.SLGGuessNode.newBuilder();
                    b.setLeagueId(league.getId())
                            .setLeague(lb)
                            .setHot(league.getGuessCount());
                    rst.addInfos(b.build());
                }

                //剩余竞猜时间
                long startTime = DateTimeUtil.toMillis(Battle_00.getEndTime(2));
                long now = ServerConstants.getCurrentTimeMillis();
                if(now < startTime){
                    long countDown = startTime - now;
                    rst.setGuessLeftSeconds((int) (countDown / 1000));
                }else{
                    rst.setGuessLeftSeconds(-1);
                }
            }else {
                //提示开始竞猜的时间
                long startTime = DateTimeUtil.toMillis(SLGNodeType.Battle_2159.getEndTime(1));
                long countDown = startTime - ServerConstants.getCurrentTimeMillis();
                rst.setResult(Text.genServerRstInfo(Text.SLG_无竞猜数据, new TextParamCountdown(countDown)));
            }
        }else{
            rst.setResult(Text.genServerRstInfo(Text.SLG_无参赛资格));
        }
        request.addCallbackParam(rst.build());
    }

    @MessageMethod(description = "竞猜")
    private static void guess(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        List<Long> guessIds = params.getParam(1);
        List<Long> oldGuessIds = params.getParam(2);

        int rst = Text.没有异常;
        SLGBattleField field = LeagueSLGWorldManager.getInstance().getBattleFieldByPid(playerId);
        if(field != null){
            for(Long id : oldGuessIds){
                SLGLeague league = field.getArea().getGuessLeague(id);
                if(league != null) {
                    league.setGuessCount(Math.max(0, league.getGuessCount() - 1));
                }
            }
            for(Long id : guessIds){
                SLGLeague league = field.getArea().getGuessLeague(id);
                if(league != null) {
                    league.setGuessCount(league.getGuessCount() + 1);
                }
            }
        }else{
            rst = Text.SLG_无参赛资格;
        }
        request.addCallbackParam(rst);
    }

    @MessageMethod(description = "战场统计")
    private static void battleStatistics(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        PbProtocol.SLGBattleStatisticsRst.Builder builder = PbProtocol.SLGBattleStatisticsRst.newBuilder();
        builder.setResult(Text.genOkServerRstInfo());

        SLGBattleField field = LeagueSLGWorldManager.getInstance().getBattleFieldByPid(playerId);
        if(field != null){
            SLGArea area = field.getArea();
            //MVP数据
            builder.addAllMvps(area.getMvps());

            SLGPlayer player = field.getPlayerByPid(playerId);
            if(player != null) {
                SLGLeague league = player.getLeague();
                //帮派数据
                CountData cityLoot = league.getCityLootLeague();
                if(cityLoot != null){
                    builder.setLootCityMostLeagueCount(cityLoot.count)
                            .setLootCityMostLeagueName(cityLoot.leagueName);
                }
                CountData homeLoot = league.getHomeLootLeague();
                if(homeLoot != null){
                    builder.setLootHomeMostLeagueCount(homeLoot.count)
                            .setLootHomeMostLeagueName(homeLoot.leagueName);
                }

                builder.setOccupiedCityCount(league.getOccupyCityCount())
                        .setLeagueRank(area.getLeagueRank(league.getId()));

                //个人数据
                builder.setAttackBackCount(player.getFightBackCount())
                        .setAttackBackRank(area.getFightBackRank(player.getPid()))
                        .setArmyExpendGrantCount(player.getGrantArmyExpendCount())
                        .setArmyExpendGrantRank(area.getGrantArmyExpendRank(player.getPid()));

                CountData data = player.getFightBackedPlayer();
                if(data != null){
                    builder.setAttackBackedCount(data.count)
                            .setAttackBackedMostPlayerName(data.leagueName);
                }

            }
        }else{
            builder.setResult(Text.genServerRstInfo(Text.SLG_无参赛资格));
        }
        request.addCallbackParam(builder.build());
    }

    @MessageMethod(description = "主界面")
    private static void mainInfo(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        int areaId = params.getParam(1);
        int fieldIndex = params.getParam(2);

        PbProtocol.SLGMainInfoRst.Builder builder = PbProtocol.SLGMainInfoRst.newBuilder();
        builder.setResult(Text.genOkServerRstInfo());
        SLGBattleField field = null;
        SLGBattleField myField = LeagueSLGWorldManager.getInstance().getBattleFieldByPid(playerId);
        SLGNodeType status = LeagueSLGWorldManager.getInstance().getStatus();
        builder.setBattleStatus(status.generateBattleStatus());

        SLGArea area = LeagueSLGWorldManager.getInstance().getAreas().get(areaId);
        if(myField != null) {
            builder.setMyFieldIndex(myField.getIndex());
        }else{
            builder.setMyFieldIndex(-1);
        }

        if(fieldIndex >= 0){
            if(area != null && fieldIndex < area.getBattleFields().size()){
                field = area.getBattleFields().get(fieldIndex);
            }
            builder.setFieldIndex(fieldIndex);
        }else{
            field = myField;
            builder.setFieldIndex(myField.getIndex());
        }

        if(field != null){
            builder.setLevel(field.getLevel());
            if(status == SLGNodeType.Reward){
                //前三名帮派信息
                int rank = 1;
                for(SLGLeagueRankNode node : area.getRankNodes()){
                    long lid = node.getLid();
                    SLGLeague league = area.getLeague(lid);
                    if(league != null){
                        builder.addTop3Leagues(league.getSimpleLeague());
                    }
                    if(rank >= 3){
                        //只取前三名
                        break;
                    }else{
                        rank++;
                    }
                }

                boolean isMyField = myField == field;
                if (isMyField) {
                    SLGPlayer player = field.getPlayerByPid(playerId);
                    if (player != null) {
                        builder.setLeagueStatus(player.getLeague().genLeagueStatus());
                    }
                }
            }else {
                boolean isMyField = myField == field;
                if (isMyField) {
                    SLGPlayer player = field.getPlayerByPid(playerId);
                    if (player != null) {
                        builder.setMyStatus(player.genMyStatusBuilder());
                        builder.setLeagueStatus(player.getLeague().genLeagueStatus());
                        builder.setIsCommander(player.isCommander());
                    }
                }

                for (SLGLeague league : field.getLeagues().values()) {
                    builder.addLeagues(league.getSimpleLeague());
                }

                for (SLGCity city : field.getCities().values()) {
                    builder.addCities(city.genPb());
                    List<PbSLG.SLGMarch> marchs = city.getMarchPbs();
                    if (marchs != null) {
                        builder.addAllMarches(marchs);
                    }
                }
            }

            builder.setLevel(field.getLevel());
            builder.setFieldSize(area.getBattleFields().size());

        }else {
            builder.setResult(Text.genServerRstInfo(Text.参数异常));
        }

        request.addCallbackParam(builder.build());
    }

    @MessageMethod(description = "据点信息")
    private static void cityInfo(ServerCommandRequest request, CommandRequestParams params) {
        PbProtocol.SLGCityDetailRst.Builder builder = PbProtocol.SLGCityDetailRst.newBuilder();
        builder.setResult(Text.genOkServerRstInfo());
        try {
            long playerId = params.getParam(0);
            int areaId = params.getParam(1);
            int fieldIndex = params.getParam(2);
            int cityId = params.getParam(3);
            //也可能看的不是自己所在场次

            SLGArea area = LeagueSLGWorldManager.getInstance().getAreas().get(areaId);
            SLGBattleField field = area.getBattleFields().get(fieldIndex);
            SLGCity city = field.getCities().get(cityId);
            builder.setCityId(city.getTid());
            for(SLGPlayer player : city.getPlayers()){
                if(player.getLeague().getId() == city.getLid()){
                    //防守队列
                    builder.addUnits(player.genSLGAttackerBuilder(false));
                }else{
                    //进攻队列
                    builder.addUnits(player.genSLGAttackerBuilder(true));
                }
            }
            for(SLGGuard guard : city.getGuards().values()){
                builder.addUnits(guard.generateAttackerBuilder());
            }
            for(SLGFight fight : city.getFights()){
                builder.addFights(fight.genPb());
            }
            field.changePlayerCityWatch(playerId, cityId);
        }catch (Exception e){
            builder.setResult(Text.genServerRstInfo(Text.参数异常));
        }
        request.addCallbackParam(builder.build());
    }

    @MessageMethod(description = "退出据点")
    private static void exitCity(ServerCommandRequest request, CommandRequestParams params) {
        PbProtocol.SLGCityDetailRst.Builder builder = PbProtocol.SLGCityDetailRst.newBuilder();
        builder.setResult(Text.genOkServerRstInfo());
        try {
            long playerId = params.getParam(0);
            SLGBattleField field = LeagueSLGWorldManager.getInstance().getBattleFieldByPid(playerId);
            if(field != null){
                field.getPlayerCityWatch().remove(playerId);
            }
        }catch (Exception e){
            builder.setResult(Text.genServerRstInfo(Text.参数异常));
        }
        request.addCallbackParam(builder.build());
    }

    @MessageMethod(description = "自动状态调整")
    private static void autoMode(ServerCommandRequest request, CommandRequestParams params) {
        int rst = Text.没有异常;
        try {
            long playerId = params.getParam(0);
            int cityId = params.getParam(1);
            int mode = params.getParam(2);
            SLGBattleField field = LeagueSLGWorldManager.getInstance().getBattleFieldByPid(playerId);
            if(field != null){
                SLGPlayer player = field.getPlayerByPid(playerId);
                if(player != null){
                    SLGCity city = field.getCities().get(cityId);
                    if(city != null){
                        //根据模式检查身份是否合法
                        if(mode == 0){
                            //防守
                            if(player.getLeague().getId() != city.getLid()){
                                rst = Text.数据异常;
                            }
                        }else{
                            //进攻
                            if(player.getLeague().getId()  == city.getLid()){
                                rst = Text.数据异常;
                            }

                            if(LeagueSLGWorldManager.getInstance().isShieldCity(city.getCityTemplate().type)){
                                rst = Text.SLG_目标城池处于保护中;
                            }
                        }
                    }

                    player.setAutoMode(mode);
                    player.setAutoModeCityId(cityId);
                    player.setStatusNeedSync(true);
                }else{
                    rst = Text.SLG_无参赛资格;
                }
            }else{
                rst = Text.SLG_无参赛资格;
            }
        }catch (Exception e){
            rst = Text.服务器异常;
        }
        request.addCallbackParam(rst);
    }






}

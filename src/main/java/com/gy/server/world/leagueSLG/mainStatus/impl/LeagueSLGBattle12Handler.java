package com.gy.server.world.leagueSLG.mainStatus.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.battleRule.base.BaseBattleInfo;
import com.gy.server.core.battleRule.base.IBattleStatusDeal;
import com.gy.server.game.leagueSLG.LeagueSLGService;
import com.gy.server.utils.time.DateTimeUtil;
import com.gy.server.world.leagueSLG.LeagueSLGHelper;
import com.gy.server.world.leagueSLG.mainStatus.SLGNodeType;

import java.time.LocalDateTime;

/**
 * @program: tl_game_4
 * @description: 战斗中
 * @author: <PERSON><PERSON>
 * @create: 2024/11/26
 **/
public class LeagueSLGBattle12Handler implements IBattleStatusDeal<BaseBattleInfo> {

    @Override
    public void init(BaseBattleInfo battleInfo) {
        LeagueSLGHelper.notifyStatus2allServers(this.nowStatus());
    }

    @Override
    public void deal(BaseBattleInfo battleInfo) {

    }

    @Override
    public boolean finish(BaseBattleInfo battleInfo) {
        SLGNodeType nodeType = SLGNodeType.getSLGNode(battleInfo.getStatus());
        return nodeType.isEnd();
    }

    @Override
    public int nowStatus() {
        return SLGNodeType.Battle_12.getId();
    }

    @Override
    public int nextStatus(BaseBattleInfo battleInfo) {
        return SLGNodeType.Battle_16.getId();
    }

    @Override
    public long getStatusDurationTime(BaseBattleInfo battleInfo) {
        return -1;
    }
}

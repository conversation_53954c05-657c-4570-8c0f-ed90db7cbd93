package com.gy.server.world.leagueSLG.bean;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.gy.server.game.leagueSLG.LeagueSLGService;
import com.gy.server.game.leagueSLG.template.SLGConstant;
import com.gy.server.packet.PbSLG;

/**
 * @program: tl_game_4
 * @description: 军饷
 * @author: <PERSON><PERSON>
 * @create: 2024/12/13
 **/
public class SLGArmyExpend {

    @Protobuf(order = 1)
    private int id;
    @Protobuf(order = 2)
    private long sendPlayerId;
    @Protobuf(order = 3)
    private int itemId;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public long getSendPlayerId() {
        return sendPlayerId;
    }

    public void setSendPlayerId(long sendPlayerId) {
        this.sendPlayerId = sendPlayerId;
    }

    public int getItemId() {
        return itemId;
    }

    public void setItemId(int itemId) {
        this.itemId = itemId;
    }

    public PbSLG.SLGArmyExpend.Builder genPb(){
        PbSLG.SLGArmyExpend.Builder b = PbSLG.SLGArmyExpend.newBuilder();
        b.setId(id)
                .setItemId(itemId)
                .setPlayerId(sendPlayerId);
        return b;
    }

    public int getExp(){
        Integer rst = LeagueSLGService.getConstant().departmentExpItems.get(itemId);
        if(rst == null){
            return 0;
        }
        return rst;
    }
}

package com.gy.server.world.leagueSLG.bean;

import com.baidu.bjf.remoting.protobuf.annotation.Ignore;
import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.google.protobuf.InvalidProtocolBufferException;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.command.CommandRequest;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.league.League;
import com.gy.server.game.leagueSLG.LeagueSLGService;
import com.gy.server.game.leagueSLG.template.SLGCityTemplate;
import com.gy.server.game.leagueSLG.template.SLGDepartmentTemplate;
import com.gy.server.game.leagueSLG.template.SLGStrategyTemplate;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbLeague;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbSLG;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;

import java.util.*;

/**
 * @program: tl_game_4
 * @description: 军团基本信息
 * @author: Huang.Xia
 * @create: 2024/11/26
 **/
public class SLGLeague {

    /**
     * 战力排序
     */
    public static Comparator<SLGLeague> powerComparator = new SLGLeaguePowerComparator();

    /**
     * 积分排序
     */
    public static Comparator<SLGLeague> scoreComparator = new SLGLeagueScoreComparator();

    /**
     * 热度排序
     */
    public static Comparator<SLGLeague> hotComparator = new SLGLeagueHotComparator();

    @Ignore
    private SLGBattleField field;
    @Protobuf(order = 1)
    private long id;

    @Protobuf(order = 2)
    private long fightingPower;

    /**
     * 淘汰时间，排序用
     */
    @Protobuf(order = 3)
    private long lastLoseTime;

    /**
     * 兵案经验
     */
    @Protobuf(order = 4)
    private int departmentExp;

    @Protobuf(order = 5)
    private Map<Long, SLGPlayer> players = new HashMap<>();

    /**
     * 当前协力人数（充值人数)
     */
    @Protobuf(order = 6)
    private int cooperationCount;

    @Protobuf(order = 7)
    private int homeCity;

    /**
     * 当前占领积分,会保留到下一天战斗开始
     */
    @Protobuf(order = 8)
    private int currentScore;

    @Protobuf(order = 9)
    private Set<Integer> strategies = new HashSet<>();

    /**
     * 结盟的同盟id
     */
    @Protobuf(order = 10)
    private Set<Long> alignmentLeagueIds = new HashSet<>();

    /**
     * 军饷
     */
    @Protobuf(order = 12)
    private List<SLGArmyExpend> armyExpends = new ArrayList<>();

    /**
     * 帮派级别数据同步
     * 战况：联盟城池被攻击
     * 无需存储
     */
    @Ignore
    private List<PbSLG.SLGAttackMsg> attackMsgs = new ArrayList<>();

    /**
     * 同盟城池被攻击时间点
     * 运行时数据，无需存储
     */
    @Ignore
    private Map<Integer, Long> cityAttackedTime = new HashMap<>();

    /**
     * 互助玩家记录
     */
    @Protobuf(order = 13)
    private Set<Long> helpPids = new HashSet<>();

    /**
     * 目标据点id
     */
    @Protobuf(order = 14)
    private List<Integer> targetCityIds = new ArrayList<>();

    /**
     * 竞猜押注数量
     */
    @Protobuf(order = 15)
    private int guessCount;

    /**
     * 累计占领城池数
     * 结算时才累计
     */
    @Protobuf(order = 16)
    private int occupyCityCount;

    /**
     * 城池被抢记录
     */
    @Protobuf(order = 17)
    private Map<Long,Integer> cityOccupiedCount = new HashMap<>();

    /**
     * 大本营被抢记录
     */
    @Protobuf(order = 18)
    private Map<Long,Integer> homeOccupiedCount = new HashMap<>();

    @Protobuf(order = 19)
    private boolean syncStatus = false;

    /**
     * 本帮相关战斗记录
     */
    @Ignore
    private List<PbSLG.SLGFightRecord> fightRecords = new ArrayList<>();

    /**
     * 战斗记录数据
     */
    @Protobuf(order = 20)
    private List<byte[]> fightRecordBytes = new ArrayList<>();

    @Ignore
    private PbLeague.SimpleLeague simpleLeague;

    @Protobuf(order = 21)
    private byte[] simpleLeagueBytes;



    /**
     * 战功前三名
     * 运行时数据，加载后初始化
     */
    @Ignore
    private List<SLGPlayer> medalOrderedPlayers = new ArrayList<>();


    public SLGLeague() {
    }

    public SLGLeague(long id, long fightingPower) {
        this.id = id;
        this.fightingPower = fightingPower;
    }

    public void init(SLGBattleField field){
        this.field = field;
        this.fightRecords = new ArrayList<>(this.fightRecordBytes.size());
        for(byte[] bytes : this.fightRecordBytes){
            PbSLG.SLGFightRecord record = null;
            try {
                record = PbSLG.SLGFightRecord.parseFrom(bytes);
                this.fightRecords.add(record);
            } catch (InvalidProtocolBufferException e) {
                e.printStackTrace();
            }
        }
        try {
            if(this.simpleLeagueBytes != null && this.simpleLeagueBytes.length > 0) {
                this.simpleLeague = PbLeague.SimpleLeague.parseFrom(this.simpleLeagueBytes);
            }
        } catch (InvalidProtocolBufferException e) {
            throw new RuntimeException(e);
        }

        for(SLGPlayer player : this.players.values()){
            player.init(this);
        }

        medalOrderedPlayers = new ArrayList<>(this.players.values());
        Collections.sort(medalOrderedPlayers, SLGPlayer.medalComparator);
    }

    public void newDayCombat(){
        lastLoseTime = Long.MAX_VALUE;
        currentScore = 0;
        strategies.clear();
        alignmentLeagueIds.clear();
        cityAttackedTime.clear();
        targetCityIds.clear();
        fightRecords.clear();
        players.values().stream().forEach(p -> p.newDayCombat());
    }

    public void beforeSave(){
        fightRecordBytes = new ArrayList<>(this.fightRecords.size());
        for(PbSLG.SLGFightRecord record : this.fightRecords){
            fightRecordBytes.add(record.toByteArray());
        }
        if(this.simpleLeague == null){
            simpleLeagueBytes = new byte[0];
        }else {
            simpleLeagueBytes = this.simpleLeague.toByteArray();
        }
        for(SLGPlayer player : this.players.values()){
            player.beforeSave();
        }
    }

    public long getId() {
        return id;
    }

    public int getGuessCount() {
        return guessCount;
    }

    public Map<Integer, Long> getCityAttackedTime() {
        return cityAttackedTime;
    }

    public void setCityAttackedTime(Map<Integer, Long> cityAttackedTime) {
        this.cityAttackedTime = cityAttackedTime;
    }

    public List<byte[]> getFightRecordBytes() {
        return fightRecordBytes;
    }

    public void setFightRecordBytes(List<byte[]> fightRecordBytes) {
        this.fightRecordBytes = fightRecordBytes;
    }

    public byte[] getSimpleLeagueBytes() {
        return simpleLeagueBytes;
    }

    public void setSimpleLeagueBytes(byte[] simpleLeagueBytes) {
        this.simpleLeagueBytes = simpleLeagueBytes;
    }

    /**
     * 帮派城池被攻击同步
     */
    public void addCityAttackedMsg(int cityId){
        Long time = cityAttackedTime.getOrDefault(cityId, 0L);
        if(ServerConstants.getCurrentTimeMillis() > time + LeagueSLGService.getConstant().cityAttackedBroadcastCD * 1000){
            cityAttackedTime.put(cityId, ServerConstants.getCurrentTimeMillis());

            PbSLG.SLGAttackMsg.Builder b = PbSLG.SLGAttackMsg.newBuilder();
            b.setMsgType(PbSLG.SLGAttackMsg.MsgType.LEAGUE_CITY_ATTACKED)
                    .setCityId(cityId);
            this.attackMsgs.add(b.build());
        }
    }

    public void setGuessCount(int guessCount) {
        this.guessCount = guessCount;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getFightingPower() {
        return fightingPower;
    }

    public void setFightingPower(long fightingPower) {
        this.fightingPower = fightingPower;
    }

    public List<PbSLG.SLGFightRecord> getFightRecords() {
        return fightRecords;
    }

    public void addFightRecord(int type, int cityId, long enemyLeagueId){
        PbSLG.SLGFightRecord.Builder b = PbSLG.SLGFightRecord.newBuilder();
        b.setType(type)
                .setCityId(cityId)
                .setTime(ServerConstants.getCurrentTimeMillis())
                .setEnemyLeagueId(enemyLeagueId);
        this.fightRecords.add(b.build());
        if(this.fightRecords.size() > LeagueSLGService.getConstant().cityRecordCount){
            this.fightRecords.remove(0);
        }
    }

    public void setFightRecords(List<PbSLG.SLGFightRecord> fightRecords) {
        this.fightRecords = fightRecords;
    }

    public Map<Long, Integer> getCityOccupiedCount() {
        return cityOccupiedCount;
    }

    public void setCityOccupiedCount(Map<Long, Integer> cityOccupiedCount) {
        this.cityOccupiedCount = cityOccupiedCount;
    }

    public void addCityOccupiedCount(long leagueId){
        Integer count = cityOccupiedCount.getOrDefault(leagueId, 0);
        cityOccupiedCount.put(leagueId, count+1);
    }

    public Map<Long, Integer> getHomeOccupiedCount() {
        return homeOccupiedCount;
    }

    public void setHomeOccupiedCount(Map<Long, Integer> homeOccupiedCount) {
        this.homeOccupiedCount = homeOccupiedCount;
    }

    public void addHomeOccupiedCount(long leagueId){
        Integer count = homeOccupiedCount.getOrDefault(leagueId, 0);
        homeOccupiedCount.put(leagueId, count+1);
    }

    public long getLastLoseTime() {
        return lastLoseTime;
    }

    public int getOccupyCityCount() {
        return occupyCityCount;
    }

    public void setOccupyCityCount(int occupyCityCount) {
        this.occupyCityCount = occupyCityCount;
    }

    public void setLastLoseTime(long lastLoseTime) {
        this.lastLoseTime = lastLoseTime;
    }

    public int getDepartmentExp() {
        return departmentExp;
    }

    public List<Integer> getTargetCityIds() {
        return targetCityIds;
    }

    public void setTargetCityIds(List<Integer> targetCityIds) {
        this.targetCityIds = targetCityIds;
    }

    public void setDepartmentExp(int departmentExp) {
        this.departmentExp = departmentExp;
    }

    public Map<Long, SLGPlayer> getPlayers() {
        return players;
    }

    public void setPlayers(Map<Long, SLGPlayer> players) {
        this.players = players;
    }

    public int getCooperationCount() {
        return cooperationCount;
    }

    public void setCooperationCount(int cooperationCount) {
        this.cooperationCount = cooperationCount;
    }

    public Set<Long> getHelpPids() {
        return helpPids;
    }

    public void setHelpPids(Set<Long> helpPids) {
        this.helpPids = helpPids;
    }

    public void syncPlayer(long pid, SLGPlayerSyncData data){
        SLGPlayer player = players.get(pid);
        if(player == null){
            player = new SLGPlayer();
            player.setPid(pid);
            player.setLeague(this);
            player.setMaxArmy(data.getArmy());
            player.getCamp().setTid(data.getTitleId());
            player.setCommander(data.isCommander());
            players.put(pid, player);

            medalOrderedPlayers.add(player);
        }else{
            if(data.getArmy() > player.getMaxArmy()){
                player.setMaxArmy(data.getArmy());
            }
            if(data.getTitleId() > player.getCamp().getTid()){
                player.getCamp().setTid(data.getTitleId());
            }
            player.setCommander(data.isCommander());
        }
    }

    public void syncPlayerArmy(long pid, SLGPlayerSyncData data){
        SLGPlayer player = players.get(pid);
        if(player != null){
            if(data.getArmy() > player.getMaxArmy()){
                player.setMaxArmy(data.getArmy());
            }
            if(data.getTitleId() > player.getCamp().getTid()){
                player.getCamp().setTid(data.getTitleId());
            }
        }
    }

    private void refreshPower(){
        fightingPower = 0;
        for(SLGPlayer player : players.values()){
            fightingPower += player.getMaxArmy();
        }
    }

    public void refresh(){
        refreshPower();
        for(SLGPlayer player : players.values()){
            player.refresh();
        }
        this.strategies.clear();
        this.lastLoseTime = Long.MAX_VALUE;

        //战功排序
        Collections.sort(medalOrderedPlayers, SLGPlayer.medalComparator);
    }

    public void updateMedalOrder(SLGPlayer player){
        //战功排序
        Collections.sort(medalOrderedPlayers, SLGPlayer.medalComparator);
    }

    /**
     * 是否战功前三
     */
    public boolean isTop3Medal(long pid){
        int rank = 1;
        for(SLGPlayer player : medalOrderedPlayers){
            if(rank > 3){
                break;
            }

            if(player.getPid() == pid){
                return true;
            }
            rank++;
        }
        return false;
    }

    public int getHomeCity() {
        return homeCity;
    }

    public void setHomeCity(int homeCity) {
        this.homeCity = homeCity;
    }

    public void tick(){
        if(this.isFail()){
            //已经淘汰的
            return;
        }

        for(SLGPlayer player : players.values()){
            player.tick();
        }

        if(this.attackMsgs.size() > 0){
            PbProtocol.SLGBattlePushRst.Builder pushRst = PbProtocol.SLGBattlePushRst.newBuilder();

            pushRst.addAllAttackMsgs(this.attackMsgs);
            this.attackMsgs.clear();
            pushRst.setResult(Text.genOkServerRstInfo());

            ServerCommandRequest req = CommandRequests.newServerCommandRequest("LeagueSLGGameCommandService.battlePushLeague");
            TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, League.getRealServerId(this.id),req,
                    Collections.singletonList(id), getField().getIndex(),pushRst.build());
        }

        if(syncStatus){
            PbSLG.SLGLeagueStatus.Builder b = PbSLG.SLGLeagueStatus.newBuilder();
            b.addAllStrategyIds(strategies)
                    .setLeagueId(id)
                    .addAllAlignmentLeagueIds(alignmentLeagueIds);
            PbProtocol.SLGBattlePushRst.Builder pushRst = PbProtocol.SLGBattlePushRst.newBuilder();
            pushRst.setResult(Text.genOkServerRstInfo());
            pushRst.setLeagueStatus(b);

            int sid = League.getRealServerId(id);
            List<Long> lids = Collections.singletonList(id);

            ServerCommandRequest req = CommandRequests.newServerCommandRequest("LeagueSLGGameCommandService.battlePushLeague");
            TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, sid,req,
                    lids, this.field.getIndex(), pushRst.build());

            syncStatus = false;
        }
    }

    public void activeStrategy(int strategy){
        SLGStrategyTemplate template = LeagueSLGService.getStrategies().get(strategy);
        if(template != null){
            template.effect(field, this);
            strategies.add(strategy);
        }

        this.syncStatus = true;
    }

    public void disactiveStrategy(){
        for(int strategy : strategies) {
            SLGStrategyTemplate template = LeagueSLGService.getStrategies().get(strategy);
            if (template != null) {
                template.remove(field, this);
            }
        }
        this.strategies.clear();
        this.syncStatus = true;
    }

    public int marchTimeModify(int timeCost){
        for(int strategy : strategies){
            SLGStrategyTemplate template = LeagueSLGService.getStrategies().get(strategy);
            if(template != null){
                if(template.effectType == SLGStrategyTemplate.EFFECT_TYPE_SPEED_ADD){
                    timeCost += template.effectData * timeCost / 10000;
                }
            }
        }
        return timeCost;
    }

    public int guardCountAdd(){
        int rst = 0;
        for(int strategy : strategies){
            SLGStrategyTemplate template = LeagueSLGService.getStrategies().get(strategy);
            if(template != null){
                if(template.effectType == SLGStrategyTemplate.EFFECT_TYPE_GUARD_ADD){
                    rst += template.effectData;
                }
            }
        }
        return rst;
    }

    public SLGBattleField getField() {
        return field;
    }

    public void setField(SLGBattleField field) {
        this.field = field;
    }

    public boolean isFail(){
        return this.lastLoseTime != Long.MAX_VALUE;
    }

    public void lostCity(SLGCity city, SLGPlayer atk){
        //结算上家的税银并邮件补发
        Map<Long, Integer> taxes = new HashMap<>();

        if (city.getCityTemplate().type != SLGCityTemplate.TYPE_大本营 //大本营只对自家产生税银
                || this.getHomeCity() == city.getLid()) {

            for (SLGPlayer player : getPlayers().values()) {
                int tax = city.calculateTax(player.getLastFarmTime());
                if (tax > 0) {
                    taxes.put(player.getPid(), tax);
                }
            }

        }

        if (taxes.size() > 0) {
            CommandRequest req = CommandRequests.newServerCommandRequest("LeagueSLGGameCommandService.cityProduce");
            TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, League.getRealServerId(this.id), req,
                    city.getTid(), taxes, atk.getLeague().getSimpleLeague().getName());
        }

        //扣减占领积分
        currentScore -= city.getCityTemplate().occupyScores[field.getLevel() - 1];

        //大本营被占领
        if(homeCity == city.getLid()){
            loseHomeCity();
        }

    }

    /**
     * 大本营被爆
     * 标记失败时间
     * 将成员从所有据点移除
     * 占领的据点重置为中立状态
     * 帮派占领积分清零
     * 通知所有玩家
     */
    public void loseHomeCity(){
        for(SLGCity city : field.getCities().values()){
            city.clearLeague(this.id);
        }
        this.currentScore = 0;
        this.tick();
        setLastLoseTime(ServerConstants.getCurrentTimeMillis());

        CommandRequest req = CommandRequests.newServerCommandRequest("LeagueSLGGameCommandService.homeLosePush");
        TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, League.getRealServerId(this.id), req, this.id, this.simpleLeague.getName());
    }

    public void winCity(SLGCity city){
        currentScore += city.getCityTemplate().occupyScores[field.getLevel() - 1];
    }

    public int getCurrentScore() {
        return currentScore;
    }

    public void setCurrentScore(int currentScore) {
        this.currentScore = currentScore;
    }

    public List<SLGPlayer> getMedalOrderedPlayers() {
        return medalOrderedPlayers;
    }

    public void setMedalOrderedPlayers(List<SLGPlayer> medalOrderedPlayers) {
        this.medalOrderedPlayers = medalOrderedPlayers;
    }

    public Set<Integer> getStrategies() {
        return strategies;
    }

    public void setStrategies(Set<Integer> strategies) {
        this.strategies = strategies;
    }

    public Set<Long> getAlignmentLeagueIds() {
        return alignmentLeagueIds;
    }

    public boolean isSyncStatus() {
        return syncStatus;
    }

    public void setSyncStatus(boolean syncStatus) {
        this.syncStatus = syncStatus;
    }

    public void setAlignmentLeagueIds(Set<Long> alignmentLeagueIds) {
        this.alignmentLeagueIds = alignmentLeagueIds;
    }

    public PbLeague.SimpleLeague getSimpleLeague() {
        return simpleLeague;
    }

    public void setSimpleLeague(PbLeague.SimpleLeague simpleLeague) {
        this.simpleLeague = simpleLeague;
    }

    public List<PbSLG.SLGAttackMsg> getAttackMsgs() {
        return attackMsgs;
    }

    public void setAttackMsgs(List<PbSLG.SLGAttackMsg> attackMsgs) {
        this.attackMsgs = attackMsgs;
    }

    public List<SLGArmyExpend> getArmyExpends() {
        return armyExpends;
    }

    public void setArmyExpends(List<SLGArmyExpend> armyExpends) {
        this.armyExpends = armyExpends;
    }

    public SLGArmyExpend getArmyExpend(int id){
        for(SLGArmyExpend expend : armyExpends) {
            if (expend.getId() == id) {
                return expend;
            }
        }
        return null;
    }

    /**
     * 获取帮派军饷发放记录
     */
    public List<PbSLG.SLGArmyExpendRankNode> genArmyExpendGrantRecord(){
        List<PbSLG.SLGArmyExpendRankNode> nodes = new ArrayList<>();
        if(armyExpends.size() == 0){
            return nodes;
        }

        Map<Long, SLGArmyExpendCount> map = new HashMap<>();
        for(SLGArmyExpend expend : armyExpends) {
            SLGArmyExpendCount count = map.getOrDefault(expend.getSendPlayerId(), new SLGArmyExpendCount(expend.getSendPlayerId()));
            count.addCount(expend.getItemId(),1);
             map.put(expend.getSendPlayerId(), count);
        }

        map.values().forEach(count -> count.sortPrepare());
        List<SLGArmyExpendCount> list = new ArrayList<>(map.values());
        Collections.sort(list, SLGArmyExpendCount.comparator);

        int rank = 1;
        for(SLGArmyExpendCount count : list){
            PbSLG.SLGArmyExpendRankNode.Builder node = PbSLG.SLGArmyExpendRankNode.newBuilder();
            node.setRank(rank);
            node.setPlayerId(count.getPlayerId());
            for(int i = 0; i < count.getOrderedCount().length; i++) {
                node.addCounts(count.getOrderedCount()[i]);
            }
            nodes.add(node.build());
            rank++;
        }
        return nodes;
    }

    /**
     * 获取帮派军饷领取记录
     */
    public List<PbSLG.SLGArmyExpendRankNode> genArmyExpendFetchRecord(){
        List<PbSLG.SLGArmyExpendRankNode> nodes = new ArrayList<>();
        if(armyExpends.size() == 0){
            return nodes;
        }

        Map<Long, SLGArmyExpendCount> map = new HashMap<>();
        for(SLGArmyExpend expend : armyExpends) {
            for(SLGPlayer player : getPlayers().values()) {
                if(player.getFetchedArmyExpends().contains(expend.getId())) {
                    SLGArmyExpendCount count = map.getOrDefault(player.getPid(), new SLGArmyExpendCount(player.getPid()));
                    count.addCount(expend.getItemId(), 1);
                    map.put(player.getPid(), count);
                }
            }
        }

        map.values().forEach(count -> count.sortPrepare());
        List<SLGArmyExpendCount> list = new ArrayList<>(map.values());
        Collections.sort(list, SLGArmyExpendCount.comparator);

        int rank = 1;
        for(SLGArmyExpendCount count : list){
            PbSLG.SLGArmyExpendRankNode.Builder node = PbSLG.SLGArmyExpendRankNode.newBuilder();
            node.setRank(rank);
            node.setPlayerId(count.getPlayerId());
            for(int i = 0; i < count.getOrderedCount().length; i++) {
                node.addCounts(count.getOrderedCount()[i]);
            }
            nodes.add(node.build());
            rank++;
        }
        return nodes;
    }

    public PbSLG.SLGLeagueGroupNode genLeagueRankNode(int rank){
        PbSLG.SLGLeagueGroupNode.Builder node = PbSLG.SLGLeagueGroupNode.newBuilder();
        node.setRank(rank)
                .setLeague(simpleLeague)
                .setScore(currentScore)
                .setDepartmentLevel(getDepartLevel());
        return node.build();
    }

    private int getDepartLevel(){
        Map.Entry<Integer, SLGDepartmentTemplate> entry = LeagueSLGService.getOrderedDepartments().floorEntry(this.departmentExp);
        if(entry == null){
            return 0;
        }
        SLGDepartmentTemplate template = entry.getValue();
        if(template == null){
            return 0;
        }
        return template.id;
    }

    /**
     * 获取抢占己方城池最多帮派
     */
    public CountData getCityLootLeague(){
        if(this.cityOccupiedCount.size() > 0){
            int maxCount = 0;
            long maxLeagueId = 0;
            for(Map.Entry<Long, Integer> entry : cityOccupiedCount.entrySet()){
                if(entry.getValue() > maxCount){
                    maxCount = entry.getValue();
                    maxLeagueId = entry.getKey();
                }
            }
            if(maxLeagueId > 0){
                SLGLeague league = field.getArea().getLeague(maxLeagueId);
                return new CountData(league.simpleLeague.getName(), maxCount);
            }
        }

        return null;
    }

    /**
     * 获取抢占己方大本营次数最多的帮派
     */
    public CountData getHomeLootLeague(){
        if(this.homeOccupiedCount.size() > 0){
            int maxCount = 0;
            long maxLeagueId = 0;
            for(Map.Entry<Long, Integer> entry : homeOccupiedCount.entrySet()){
                if(entry.getValue() > maxCount){
                    maxCount = entry.getValue();
                    maxLeagueId = entry.getKey();
                }
            }
            if(maxLeagueId > 0){
                SLGLeague league = field.getArea().getLeague(maxLeagueId);
                return new CountData(league.simpleLeague.getName(), maxCount);
            }
        }

        return null;
    }

    public PbSLG.SLGLeagueStatus.Builder genLeagueStatus(){
        PbSLG.SLGLeagueStatus.Builder status = PbSLG.SLGLeagueStatus.newBuilder();
        status.addAllStrategyIds(strategies)
                .setLeagueId(this.id)
                .addAllAlignmentLeagueIds(alignmentLeagueIds);
        return status;
    }

    private static class SLGLeaguePowerComparator implements Comparator<SLGLeague> {
        @Override
        public int compare(SLGLeague o1, SLGLeague o2) {
            if (o1.fightingPower == o2.fightingPower) {
                return Long.compare(o1.id, o2.id);
            }else{
                return Long.compare(o2.fightingPower, o1.fightingPower);
            }
        }
    }

    private static class SLGLeagueScoreComparator implements Comparator<SLGLeague> {
        @Override
        public int compare(SLGLeague l1, SLGLeague l2) {

            if(l1.getLastLoseTime() != l2.getLastLoseTime()){
                //越小表示越先输，排到最后
                return Long.compare(l2.getLastLoseTime(), l1.getLastLoseTime());
            }else{
                if(l1.getCurrentScore() == l2.getCurrentScore()){
                    return Long.compare(l1.getId(), l2.getId());
                }else{
                    //分数越高越靠前
                    return Long.compare(l2.getCurrentScore(), l1.getCurrentScore());
                }
            }
        }
    }

    private static class SLGLeagueHotComparator implements Comparator<SLGLeague> {
        @Override
        public int compare(SLGLeague l1, SLGLeague l2) {
            if (l1.getGuessCount() == l2.getGuessCount()) {
                return Long.compare(l1.getId(), l2.getId());
            } else {
                //分数越高越靠前
                return Long.compare(l2.getGuessCount(), l1.getGuessCount());
            }
        }
    }
}

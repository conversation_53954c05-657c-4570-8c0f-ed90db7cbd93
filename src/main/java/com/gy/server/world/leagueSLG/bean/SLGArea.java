package com.gy.server.world.leagueSLG.bean;

import com.baidu.bjf.remoting.protobuf.annotation.Ignore;
import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.command.CommandRequest;
import com.gy.server.core.log.SystemLogger;
import com.gy.server.game.event.ServerEvent;
import com.gy.server.game.league.League;
import com.gy.server.game.leagueSLG.LeagueSLGService;
import com.gy.server.game.leagueSLG.template.SLGGroupTemplate;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.game.util.pb.LongList;
import com.gy.server.packet.PbSLG;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import org.omg.CORBA.ServerRequest;

import java.util.*;

/**
 * @program: tl_game_4
 * @description: 分组后的一个赛区
 * @author: Huang.Xia
 * @create: 2024/11/26
 **/
public class SLGArea {
    public static final int AREA_STATE_GROUPED = 1;
    public static final int AREA_STATE_REFRESHED = 2;
    public static final int AREA_STATE_BATTLE = 3;

    public static final long SAVE_INTERVAL = 1000 * 60 * 5;



    /**
     * 赛区id
     */
    @Protobuf(order = 1)
    private int areaId;

    @Protobuf(order = 2)
    private int state = AREA_STATE_GROUPED;

    /**
     * 赛区内所有战场
     */
    @Protobuf(order = 3)
    private List<SLGBattleField> battleFields = new ArrayList<>();

    @Protobuf(order = 4)
    private int planId;

    /**
     * 需要等待刷新数据的区服id，
     * 每个区服只会给一个赛区返回一次批量数据
     */
    @Ignore
    private Set<Integer> needRefreshServerIds = new HashSet<>();

    /**
     * 帮派排行榜-积分榜
     */
    @Protobuf(order = 6)
    private List<SLGLeagueRankNode> rankNodes = new ArrayList<>();

    /**
     * 已经结算，用来限制结算后才能领取排行奖励
     */
    @Protobuf(order = 8)
    private boolean settled = false;

    @Ignore
    private List<SLGLeague> guessLeagues = new ArrayList<>();

    /**
     * 战斗结束后展示用MVP数据
     * 运行时数据，需要时计算出来
     */
    @Ignore
    private List<PbSLG.SLGMvpNode> mvps;

    /**
     * 发放军饷排行榜
     * 运行时数据，需要时计算出来
     */
    @Ignore
    private List<SLGPlayer> grantArmyExpendPlayerRank = new ArrayList<>();

    /**
     * 击退敌人排行榜
     * 运行时数据，需要时计算出来
     */
    @Ignore
    private List<SLGPlayer> fightBackPlayerRank = new ArrayList<>();

    /**
     * 上次保存时间
     * 无需保保存
     */
    @Ignore
    private long lastSaveTime;

    public void init(){
        for(SLGBattleField field : this.battleFields){
            field.init(this);
        }
    }

    public void beforeSave(){
        for(SLGBattleField field : this.battleFields){
            field.beforeSave();
        }

    }

    public List<SLGLeague> getGuessLeagues() {
        return guessLeagues;
    }

    public SLGLeague getGuessLeague(long lid){
        for(SLGLeague league : this.guessLeagues){
            if(league.getId() == lid){
                return league;
            }
        }
        return null;
    }

    public void setGuessLeagues(List<SLGLeague> guessLeagues) {
        this.guessLeagues = guessLeagues;
    }

    public int getAreaId() {
        return areaId;
    }

    public void setAreaId(int areaId) {
        this.areaId = areaId;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public long getLastSaveTime() {
        return lastSaveTime;
    }

    public void setLastSaveTime(long lastSaveTime) {
        this.lastSaveTime = lastSaveTime;
    }

    public List<SLGBattleField> getBattleFields() {
        return battleFields;
    }

    public void setBattleFields(List<SLGBattleField> battleFields) {
        this.battleFields = battleFields;
    }

    public int getPlanId() {
        return planId;
    }

    public void setPlanId(int planId) {
        this.planId = planId;
    }

    private void initBattleFields(){
        for(int i = 0; i < this.battleFields.size(); i++){
            SLGBattleField field = this.battleFields.get(i);
            field.init(this,i);
        }
        printInfo();
    }

    public void tick(){
        if(this.state == AREA_STATE_REFRESHED) {
            if(rankNodes.isEmpty()){
                //第一天战斗
                for(SLGBattleField field : this.battleFields){
                    for(SLGLeague league : field.getLeagues().values()){
                        rankNodes.add(new SLGLeagueRankNode(league.getId(), league.getFightingPower()));
                    }
                }
            }
            initBattleFields();
            this.state = AREA_STATE_BATTLE;
        }else{
            for(SLGBattleField field : this.battleFields){
                field.tick();
            }
        }

        saveCheck();
    }

    public void saveCheck(){
        if(ServerConstants.getCurrentTimeMillis() - this.lastSaveTime > SAVE_INTERVAL){
            this.lastSaveTime = ServerConstants.getCurrentTimeMillis();
            this.beforeSave();

            try {
                byte[] bytes = PbUtilCompress.encode(this);
                String key = GsRedisKey.LeagueSLG.area.getRedisKey(areaId);
                TLBase.getInstance().getRedisAssistant().bytesSetAsync(key, bytes);
            }catch (Exception e){
                e.printStackTrace();
            }
        }
    }

    public void save(){
        this.beforeSave();
        try {
            byte[] bytes = PbUtilCompress.encode(this);
            String key = GsRedisKey.LeagueSLG.area.getRedisKey(areaId);
            TLBase.getInstance().getRedisAssistant().bytesSetAsync(key, bytes);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public void newDayCombat(){
        for(SLGBattleField field : this.battleFields){
            field.newDayCombat();
        }
    }

    public void remove(){
        String key = GsRedisKey.LeagueSLG.area.getRedisKey(areaId);
        TLBase.getInstance().getRedisAssistant().delAsync(key);
    }

    public List<PbSLG.SLGMvpNode> getMvps() {
        if(this.mvps == null || this.mvps.isEmpty()){
            this.genMvps();
        }
        return mvps;
    }

    public void setMvps(List<PbSLG.SLGMvpNode> mvps) {
        this.mvps = mvps;
    }

    private void genMvps(){
        this.mvps = new ArrayList<>();

        //战功MVP
        List<SLGPlayer> players = getPlayerRanks();
        SLGPlayer medalMvp = players.get(0);

        //发放军饷
        this.grantArmyExpendPlayerRank = new ArrayList<>(players);
        Collections.sort(grantArmyExpendPlayerRank,(o1,o2)->{
            if (o2.getGrantArmyExpendCount() == o1.getGrantArmyExpendCount()){
                return Long.compare(o1.getPid(), o2.getPid());
            }
            return Integer.compare(o2.getGrantArmyExpendCount(), o1.getGrantArmyExpendCount());
        });
        SLGPlayer grantMvp = grantArmyExpendPlayerRank.get(0);

        this.fightBackPlayerRank = new ArrayList<>(players);

        //击退敌人MVP
        Collections.sort(fightBackPlayerRank, new SLGFightBackPlayerComparator());
        SLGPlayer fightBackMvp = fightBackPlayerRank.get(0);

        List<Long> pids = new ArrayList<>();
        pids.add(medalMvp.getPid());
        pids.add(grantMvp.getPid());
        pids.add(fightBackMvp.getPid());

        Map<Long, MiniGamePlayer> miniPlayers = PlayerHelper.getMiniPlayersForMap(pids);

        this.mvps.add(genMvp(medalMvp, 1, miniPlayers.get(medalMvp.getPid()), players));
        this.mvps.add(genMvp(grantMvp, 2, miniPlayers.get(grantMvp.getPid()), players));
        this.mvps.add(genMvp(fightBackMvp, 3, miniPlayers.get(fightBackMvp.getPid()), players));
    }

    /**
     * 发放军饷排行榜
     */
    public int getGrantArmyExpendRank(long pid){
        for(int i = 0; i < this.grantArmyExpendPlayerRank.size(); i++) {
            if (this.grantArmyExpendPlayerRank.get(i).getPid() == pid) {
                return i + 1;
            }
        }
        return -1;
    }

    /**
     * 击退敌人排行榜
     */
    public int getFightBackRank(long pid){
        for(int i = 0; i < this.fightBackPlayerRank.size(); i++) {
            if (this.fightBackPlayerRank.get(i).getPid() == pid) {
                return i + 1;
            }
        }
        return -1;
    }



    private PbSLG.SLGMvpNode genMvp(SLGPlayer player, int type, MiniGamePlayer miniPlayer, List<SLGPlayer> rankList){
        PbSLG.SLGMvpNode.Builder b = PbSLG.SLGMvpNode.newBuilder();
        PbSLG.SLGMiniPlayer.Builder ub = PbSLG.SLGMiniPlayer.newBuilder();
        ub.setLeagueId(player.getLeague().getId())
                        .setServerId(Player.getRealServerId(player.getPid()))
                                .setUser(miniPlayer.genMinMiniUser());

        b.setRank(rankList.indexOf(player) + 1)
                .setType(type)
                .setMiniPlayer(ub);

        switch (type){
            case 1:{
                b.setData(player.getCombatMedals());
                break;
            }
            case 2:{
                b.setData(player.getGrantArmyExpendCount());
                break;
            }
            case 3:{
                b.setData(player.getFightBackCount());
                break;
            }
        }

        return b.build();
    }

    private void calcPlanId(SLGRegister registers){
        if(registers.getLeagues().size() >= 108){
            this.planId = 4;
        }else if(registers.getLeagues().size() >= 96){
            this.planId = 3;
        }else if(registers.getLeagues().size() >= 72) {
            this.planId = 2;
        }else {
            this.planId = 1;
        }
    }

    public void regroup(){
        this.settled = false;
        SLGGroupTemplate groupTemplate = LeagueSLGService.getGroups().get(this.planId);
        int dayOfWeek = ServerConstants.getCurrentTimeLocalDateTime().getDayOfWeek().getValue();
        Map<Integer, List<SLGLeague>> data = new HashMap<>();
        for(SLGBattleField field : this.battleFields){
            List<SLGLeague> leagues = new ArrayList<>(field.getLeagues().values());
            Collections.sort(leagues, SLGLeague.scoreComparator);

            if(dayOfWeek == LeagueSLGService.getConstant().regroupDay1){
                for(int i = 0; i < leagues.size(); i++){
                    if(i + 1 <= groupTemplate.day1upgrade){
                        List<SLGLeague> list = data.getOrDefault(field.getLevel() + 1, new ArrayList<>());
                        list.add(leagues.get(i));
                        data.put(field.getLevel() + 1, list);
                    }else{
                        List<SLGLeague> list = data.getOrDefault(field.getLevel(), new ArrayList<>());
                        list.add(leagues.get(i));
                        data.put(field.getLevel(), list);
                    }
                }
            }else if(dayOfWeek == LeagueSLGService.getConstant().regroupDay2){
                for(int i = 0; i < leagues.size(); i++){
                    if(i + 1 <= groupTemplate.day2upgrade){
                        List<SLGLeague> list = data.getOrDefault(field.getLevel() + 1, new ArrayList<>());
                        list.add(leagues.get(i));
                        data.put(field.getLevel() + 1, list);
                    }else if(i + 1 > groupTemplate.day2downgrade){
                        int level = Math.max(1, field.getLevel() - 1);
                        List<SLGLeague> list = data.getOrDefault(level, new ArrayList<>());
                        list.add(leagues.get(i));
                        data.put(level, list);
                    }else{
                        List<SLGLeague> list = data.getOrDefault(field.getLevel(), new ArrayList<>());
                        list.add(leagues.get(i));
                        data.put(field.getLevel(), list);
                    }
                }
            }else{
                throw new IllegalArgumentException("not battle day : " + dayOfWeek);
            }
        }


        battleFields.clear();
        for(Map.Entry<Integer, List<SLGLeague>> entry : data.entrySet()){
            int level = entry.getKey();
            List<SLGLeague> leagues = entry.getValue();
            Collections.shuffle(leagues);
            SLGBattleField field = new SLGBattleField();
            field.setLevel(level);
            battleFields.add(field);
            for(SLGLeague league : leagues){
                if(field.getLeagues().size() >= LeagueSLGService.getConstant().leagueCountPerField){
                    field = new SLGBattleField();
                    field.setLevel(level);
                    battleFields.add(field);
                }

                league.setField(field);
                field.getLeagues().put(league.getId(), league);
            }
        }

    }

    public void group(SLGRegister registers){
        //确定方案
        calcPlanId(registers);

        //分组
        int groupCount = registers.getLeagues().size() / 12;
        if(groupCount * 12 != registers.getLeagues().size()){
            //有不满的分组
            groupCount = groupCount + 1;
        }

        for(int i = 0; i < groupCount; i++){
            this.battleFields.add(new SLGBattleField());
        }

        //所有帮派按照战力排序
        List<SLGLeague> leagues = new ArrayList<>();
        for(Map.Entry<Long,Long> entry : registers.getLeagues().entrySet()){
            leagues.add(new SLGLeague(entry.getKey(),entry.getValue()));
        }
        Collections.sort(leagues, SLGLeague.powerComparator);

        //蛇形填入战场
        Iterator<SLGLeague> iterator = leagues.iterator();
        while(iterator.hasNext()) {
            for (SLGBattleField field : this.battleFields) {
                if (!iterator.hasNext()) {
                    break;
                }

                SLGLeague league = iterator.next();
                league.setField(field);
                field.getLeagues().put(league.getId(), league);
            }
        }

    }

    public Set<Integer> getNeedRefreshServerIds() {
        return needRefreshServerIds;
    }

    public List<SLGPlayer> getGrantArmyExpendPlayerRank() {
        return grantArmyExpendPlayerRank;
    }

    public void setGrantArmyExpendPlayerRank(List<SLGPlayer> grantArmyExpendPlayerRank) {
        this.grantArmyExpendPlayerRank = grantArmyExpendPlayerRank;
    }

    public List<SLGPlayer> getFightBackPlayerRank() {
        return fightBackPlayerRank;
    }

    public void setFightBackPlayerRank(List<SLGPlayer> fightBackPlayerRank) {
        this.fightBackPlayerRank = fightBackPlayerRank;
    }

    public void setNeedRefreshServerIds(Set<Integer> needRefreshServerIds) {
        this.needRefreshServerIds = needRefreshServerIds;
    }

    public void resetNeedRefreshServerIds(){
        for(SLGBattleField field : this.battleFields){
            for(SLGLeague league : field.getLeagues().values()){
                this.needRefreshServerIds.add(League.getRealServerId(league.getId()));
            }
        }
    }

    public void settle(){
        boolean pushGuessResult = this.guessLeagues.size() > 0;
        for(SLGBattleField field : this.battleFields){
            field.settle(this);
        }
        //排序
        Collections.sort(rankNodes, SLGLeagueRankNode.comparator);
        this.settled = true;

        if(pushGuessResult){
            this.pushGuessResult();
        }
    }

    public void pushGuessResult(){
        Set<Integer> serverIds = new HashSet<>();
        for(SLGLeague league : this.guessLeagues){
            serverIds.add(League.getRealServerId(league.getId()));
        }
        List<Long> top3 = new ArrayList<>();
        for(int i = 0; i < 3 && i < this.rankNodes.size(); i++){
            top3.add(this.rankNodes.get(i).getLid());
        }
        CommandRequest request = CommandRequests.newServerCommandRequest("LeagueSLGGameCommandService.guessResultPush");
        TLBase.getInstance().getRpcUtil().sendToNodes(ServerType.GAME, serverIds, request, this.areaId, top3);
    }

    public SLGLeagueRankNode getRankNode(long leagueId){
        for(SLGLeagueRankNode node : this.rankNodes){
            if(node.getLid() == leagueId){
                return node;
            }
        }
        return null;
    }

    public List<SLGLeagueRankNode> getRankNodes() {
        return rankNodes;
    }

    public void  setRankNodes(List<SLGLeagueRankNode> rankNodes) {
        this.rankNodes = rankNodes;
    }

    public SLGLeague getLeague(long leagueId){
        for(SLGBattleField field : this.battleFields){
            SLGLeague league = field.getLeagues().get(leagueId);
            if(league != null) {
                return league;
            }
        }
        return null;
    }

    public List<SLGPlayer> getPlayerRanks(){
        List<SLGPlayer> list = new ArrayList<>();
        for(SLGBattleField field : this.battleFields){
            for(SLGLeague league : field.getLeagues().values()){
                for(SLGPlayer player : league.getPlayers().values()){
                    list.add(player);
                }
            }
        }

        Collections.sort(list, SLGPlayer.medalComparator);
        return list;
    }

    public int getPlayerRank(long pid){
        List<SLGPlayer> players = getPlayerRanks();
        for(int i = 0; i < players.size(); i++){
            if(players.get(i).getPid() == pid){
                return  i + 1;
            }
        }

        return -1;
    }

    public int getLeagueRank(long leagueId){
        for(int i = 0; i < this.rankNodes.size(); i++){
            if(this.rankNodes.get(i).getLid() == leagueId){
                return i + 1;
            }
        }

        return -1;
    }

    public void printInfo(){
        SystemLogger.info("SLG AreaId: " + this.areaId);
        for(SLGBattleField field : this.battleFields){
            field.printInfo();
        }
    }

    public void seasonFinish(){
        Map<Integer, SLGSeasonFinishInfo> data = new HashMap<>();
        //帮派排行数据
        int rank = 1;
        for(SLGLeagueRankNode node : this.rankNodes){
            long leagueId = node.getLid();
            int serverId = League.getRealServerId(leagueId);
            SLGSeasonFinishInfo info = data.get(serverId);
            if(info == null){
                info = new SLGSeasonFinishInfo();
                data.put(serverId, info);
            }
            info.getLeagueRank().put(leagueId, rank);
            rank++;
        }

        //个人排行数据、个人战功数据
        List<SLGPlayer> playerRanks = getPlayerRanks();
        for(int i = 0; i < playerRanks.size(); i++){
            SLGPlayer player = playerRanks.get(i);
            int serverId = Player.getRealServerId(player.getPid());
            SLGSeasonFinishInfo info = data.get(serverId);
            if(info == null){
                info = new SLGSeasonFinishInfo();
                data.put(serverId, info);
            }
            info.getPlayerRank().put(player.getPid(), i + 1);
            info.getPlayerMedals().put(player.getPid(), player.getCombatMedals());
        }

        CommandRequest req = CommandRequests.newServerCommandRequest("LeagueSLGGameCommandService.seasonFinish");
        for(Map.Entry<Integer, SLGSeasonFinishInfo> entry : data.entrySet()){
            int serverId = entry.getKey();
            SLGSeasonFinishInfo info = entry.getValue();

            GameLogger.slgDebug("Season finish areaId: " + this.areaId + " serverId: " + serverId + " info: " + info.toString());
            TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, serverId, req, this.areaId, info);
        }

    }

    public boolean isSettled() {
        return settled;
    }

    public void setSettled(boolean settled) {
        this.settled = settled;
    }

    private static class SLGFightBackPlayerComparator implements Comparator<SLGPlayer> {
        @Override
        public int compare(SLGPlayer o1, SLGPlayer o2) {
            if (o2.getFightBackCount() == o1.getFightBackCount()){
                return Long.compare(o1.getPid(), o2.getPid());
            }
            return Integer.compare(o2.getFightBackCount(), o1.getFightBackCount());
        }
    }
}

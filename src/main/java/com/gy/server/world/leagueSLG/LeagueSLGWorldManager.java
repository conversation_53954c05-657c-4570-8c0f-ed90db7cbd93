package com.gy.server.world.leagueSLG;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.core.Configuration;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.callback.response.CallbackResponse;
import com.gy.server.core.command.CommandRequest;
import com.gy.server.core.log.SystemLogger;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.league.League;
import com.gy.server.game.leagueSLG.template.SLGCityTemplate;
import com.gy.server.packet.PbLeague;
import com.gy.server.utils.function.Ticker;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.gy.server.world.leagueSLG.bean.*;
import com.gy.server.world.leagueSLG.mainStatus.SLGNodeType;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.rpc.TLMessageCallbackTask;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @program: tl_game_4
 * @description: SLG world管理器
 * @author: Huang.Xia
 * @create: 2024/11/28
 **/
public class LeagueSLGWorldManager implements Ticker {

    private static final LeagueSLGWorldManager instance = new LeagueSLGWorldManager();

    private SLGNodeType status;

    /**
     * 赛区数据，各world维护自己负责的赛区数据
     * 存于redis中
     */
    private Map<Integer, SLGArea> areas = new HashMap<>();

    /**
     * 赛区数据需要刷新
     */
    private boolean needRefresh = false;

    /**
     * 上一次刷新请求时间
     * 运行时数据
     */
    private long lastRefreshTime = 0;

    /**
     * 城池护盾状态
     * 运行时数据，重启根据当前时间自动生成
     */
    private Map<Integer, Boolean> cityShielded = new HashMap<>();

    /**
     * 玩家到战场映射
     * 运行时数据，重启根据当前时间自动生成
     */
    private Map<Long, SLGBattleField> pid2BattleField = new HashMap<>();


    /**
     * 自动报名数据，仅主world可能有
     * 区服id -> 区服自动报名数据
     * 运行时数据，不需要保存
     */
    private Map<Integer, SLGRegister> registers = new ConcurrentHashMap<>();

    /**
     * 自动报名数据，仅主world可能有
     * 已经分配到具体执行world的赛区id
     * 运行时数据，不需要保存
     */
    private Set<Integer> allocatedAreaIds = new HashSet<>();


    public static LeagueSLGWorldManager getInstance() {
        return instance;
    }

    private LeagueSLGWorldManager() {

    }

    public void newSeason() {
        for (SLGArea area : this.areas.values()) {
            area.remove();
        }

        lastRefreshTime = 0;
        cityShielded.clear();
        pid2BattleField.clear();
        registers.clear();
        allocatedAreaIds.clear();
    }

    public void updateCombatDayIndex(){
        LeagueSLGWorldGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueWorldSLG);
        globalData.setCombatDayIndex(globalData.getCombatDayIndex() + 1);
    }

    @Override
    public void tick() {

        if (status.isBattle()) {
            //数据刷新处理
            if (this.needRefresh) {
                this.refreshAreaCheck();
                return;
            }

            for (SLGArea area : this.areas.values()) {
                area.tick();
            }
        }else{
            for (SLGArea area : this.areas.values()) {
                area.saveCheck();
            }
        }


    }

    /**
     * 10s刷新一次赛区数据
     * 检查赛区数据，
     * 如果需要刷新，则向各个区服请求刷新数据
     * 若所有区服都刷新完毕，则设置赛区状态为已刷新
     */
    public void refreshAreaCheck() {
        if (ServerConstants.getCurrentTimeMillis() - this.lastRefreshTime > 1000 * 10) {
            this.lastRefreshTime = ServerConstants.getCurrentTimeMillis();
            List<SLGArea> needRefreshAreas = new ArrayList<>();
            for (SLGArea area : this.areas.values()) {
                if (area.getState() == SLGArea.AREA_STATE_GROUPED) {
                    needRefreshAreas.add(area);
                }
            }

            if (needRefreshAreas.size() > 0) {
                //刷新赛区数据<serverId, SLGGroupResult>  以区服为单位归并汇总
                Map<Integer, List<SLGGroupResult>> results = new HashMap<>();
                for (SLGArea area : needRefreshAreas) {
                    for (int i = 0; i < area.getBattleFields().size(); i++) {
                        SLGBattleField field = area.getBattleFields().get(i);
                        for (SLGLeague league : field.getLeagues().values()) {
                            int sid = League.getRealServerId(league.getId());
                            List<SLGGroupResult> list = results.getOrDefault(sid, new ArrayList<>());
                            list.add(new SLGGroupResult(area.getAreaId(), i, league.getId()));
                            results.put(sid, list);
                        }
                    }
                }

                //逐个区服请求刷新赛区数据
                for (int sid : results.keySet()) {
                    CommandRequest request = CommandRequests.newServerCommandRequest("LeagueSLGGameCommandService.refreshLeague");
                    TLBase.getInstance().getRpcUtil().sendToNodeWithCallBack(new RefreshLeagueMessageCallbackTask(), ServerType.GAME, sid, request, results.get(sid));
                }
            } else {
                this.needRefresh = false;
            }
        }
    }

    public void startup() {
        //初始化
        LeagueSLGWorldGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueWorldSLG);
        status = SLGNodeType.valueOf(globalData.getBaseInfo().getStatus());

        //初始化护盾状态
        if(status.getId() < status.Battle_19.getId()){
            this.cityShielded.put(SLGCityTemplate.TYPE_大本营, true);
            this.cityShielded.put(SLGCityTemplate.TYPE_常规城池4, true);
        }else if(status.getId() < status.Battle_20.getId()){
            this.cityShielded.put(SLGCityTemplate.TYPE_常规城池4, true);
        }

        for (int areaId = 1; areaId <= globalData.getAreaCount(); areaId++) {
            if (TLBase.getInstance().getRpcUtil().hashKeyToNodeId(areaId) == Configuration.serverId) {
                //自己负责的赛区
                String key = GsRedisKey.LeagueSLG.area.getRedisKey(areaId);
                byte[] bytes = TLBase.getInstance().getRedisAssistant().bytesGet(key);
                if (bytes != null) {
                    SLGArea area = PbUtilCompress.decode(SLGArea.class, bytes);
                    area.init();
                    areas.put(areaId, area);
                }
            }
        }

    }

    public Map<Integer, SLGArea> getAreas() {
        return areas;
    }

    public void setAreas(Map<Integer, SLGArea> areas) {
        this.areas = areas;
    }

    public Map<Integer, SLGRegister> getRegisters() {
        return registers;
    }

    public void setRegisters(Map<Integer, SLGRegister> registers) {
        this.registers = registers;
    }

    public Set<Integer> getAllocatedAreaIds() {
        return allocatedAreaIds;
    }

    public void setAllocatedAreaIds(Set<Integer> allocatedAreaIds) {
        this.allocatedAreaIds = allocatedAreaIds;
    }

    public SLGNodeType getStatus() {
        return status;
    }

    public void setStatus(SLGNodeType status) {
        this.status = status;
    }

    public void startMatching() {
        this.status = SLGNodeType.Matching;
        this.areas.clear();
    }

    public void startBattle() {
        this.needRefresh = true;

        this.cityShielded.put(SLGCityTemplate.TYPE_大本营, true);
        this.cityShielded.put(SLGCityTemplate.TYPE_常规城池4, true);
        for (SLGArea area : this.areas.values()) {
            if(area.getRankNodes().size() > 0){
                area.regroup();
            }
            area.newDayCombat();
            area.setState(SLGArea.AREA_STATE_GROUPED);
        }
        pid2BattleField.clear();
    }

    public void allocateArea(int areaId, SLGRegister registers) {
        SLGArea area = new SLGArea();
        area.setAreaId(areaId);
        area.group(registers);
        this.areas.put(areaId, area);
    }

    /**
     * 判断城池是否在护盾状态下
     */
    public boolean isShieldCity(int cityType) {
        return this.cityShielded.getOrDefault(cityType, Boolean.FALSE);
    }

    public boolean isNeedRefresh() {
        return needRefresh;
    }

    public Map<Integer, Boolean> getCityShielded() {
        return cityShielded;
    }

    public SLGArea getAreaByPid(long pid) {
        for (SLGArea area : this.areas.values()) {
            for (SLGBattleField field : area.getBattleFields()) {
                for (SLGLeague league : field.getLeagues().values()) {
                    if (league.getPlayers().containsKey(pid)) {
                        return area;
                    }
                }
            }
        }
        return null;
    }

    public SLGArea getArea(int areaId) {
        return this.areas.get(areaId);
    }

    public SLGBattleField getBattleFieldByPid(long pid) {
/*        if (pid2BattleField.isEmpty() && areas.size() > 0) {
            for (SLGArea area : this.areas.values()) {
                for (SLGBattleField field : area.getBattleFields()) {
                    for (SLGLeague league : field.getLeagues().values()) {
                        for (SLGPlayer player : league.getPlayers().values()) {
                            pid2BattleField.put(player.getPid(), field);
                        }
                    }
                }
            }
        }

        return pid2BattleField.get(pid);*/

        for (SLGArea area : this.areas.values()) {
            for (SLGBattleField field : area.getBattleFields()) {
                for (SLGLeague league : field.getLeagues().values()) {
                    for (SLGPlayer player : league.getPlayers().values()) {
                        if(player.getPid() == pid){
                            return field;
                        }
                    }
                }
            }
        }
        return null;
    }

    public void printMatchInfo() {
        for (SLGArea area : this.areas.values()) {
            area.printInfo();
        }
    }

    public void shutdown(){
        for (SLGArea area : this.areas.values()) {
            area.save();
        }
    }

    private class RefreshLeagueMessageCallbackTask extends TLMessageCallbackTask {
        @Override
        public void complete(CallbackResponse response) {
            Map<String, Map<Long, SLGPlayerSyncData>> data = response.getParam(0);
            Map<Long, byte[]> simpleLeagues = response.getParam(1);
            int serverId = response.getRemoteServerId();
            for (Map.Entry<String, Map<Long, SLGPlayerSyncData>> entry : data.entrySet()) {
                SLGGroupResult result = SLGGroupResult.fromSimpleString(entry.getKey());
                SLGLeague league = areas.get(result.getAreaId()).getBattleFields().get(result.getFieldIndex()).getLeagues().get(result.getLid());
                for (Map.Entry<Long, SLGPlayerSyncData> playerEntry : entry.getValue().entrySet()) {
                    SLGPlayerSyncData playerSyncData = playerEntry.getValue();
                    league.syncPlayer(playerEntry.getKey(), playerSyncData);
                }

                try {
                    PbLeague.SimpleLeague simpleLeague = PbLeague.SimpleLeague.parseFrom(simpleLeagues.get(league.getId()));
                    league.setSimpleLeague(simpleLeague);

                    //健壮性保证，如果帮派没有玩家了，则移除该帮派
                    if (league.getPlayers().size() == 0) {
                        areas.get(result.getAreaId()).getBattleFields().get(result.getFieldIndex()).getLeagues().remove(result.getLid());
                        SystemLogger.warn("Leagues SLG league has no player, remove it. Lid : " + result.getLid());
                    }

                    //检查赛区数据是否刷新完毕
                    areas.get(result.getAreaId()).getNeedRefreshServerIds().remove(serverId);
                    if (areas.get(result.getAreaId()).getNeedRefreshServerIds().size() == 0) {
                        areas.get(result.getAreaId()).setState(SLGArea.AREA_STATE_REFRESHED);
                    }
                }catch (Exception e){
                    SystemLogger.error("League SLG refresh league error. Lid : " + result.getLid(), e);
                }
            }
        }

        @Override
        public void timeout() {
            //下次会继续发起请求
        }
    }
}

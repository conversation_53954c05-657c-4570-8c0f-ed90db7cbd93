package com.gy.server.world.leagueSLG;

import com.gy.server.core.command.CommandRequest;
import com.gy.server.core.command.PlayerCommandRequest;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.leagueSLG.LeagueSLGGameGlobalData;
import com.gy.server.game.leagueSLG.bean.BattleSettleInfo;
import com.gy.server.game.leagueSLG.bean.HomeLoseInfo;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.world.leagueSLG.bean.SLGGroupResult;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;

/**
 * @program: tl_game_4
 * @description: 工具类
 * @author: <PERSON>.<PERSON>
 * @create: 2024/12/2
 **/
public class LeagueSLGHelper {

    public static void checkSettlePush(long lid, long pid){
        LeagueSLGGameGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueGameSLG);
        BattleSettleInfo settleInfo = globalData.getBattleSettleInfoMap().get(lid);
        if(settleInfo != null
                && !settleInfo.getPushedPids().contains(pid)
                && globalData.getPid2FieldIndex().containsKey(pid)
        ){
            Player player = PlayerManager.getOnlinePlayer(pid);
            if(player != null) {
                player.sendAsync(PtCode.SLG_BATTLE_FINISH_PUSH_RST, settleInfo.getInfo());
                settleInfo.getPushedPids().add(pid);
            }
        }
    }

    public static void checkHomeLosePush(long lid, long pid){
        LeagueSLGGameGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueGameSLG);
        HomeLoseInfo settleInfo = globalData.getHomeLoseInfoMap().get(lid);
        if(settleInfo != null
                && !settleInfo.getPushedPids().contains(pid)
                && globalData.getPid2FieldIndex().containsKey(pid)
        ){
            Player player = PlayerManager.getOnlinePlayer(pid);
            if(player != null) {
                player.sendAsync(PtCode.SLG_HOME_LOSE_PUSH, settleInfo.getInfo());
                settleInfo.getPushedPids().add(pid);
            }
        }
    }

    /**
     * 同步最新状态到所有节点
     */
    public static void notifyStatus2allServers(int status){
        LeagueSLGWorldGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueWorldSLG);
        int index =globalData.getCombatDayIndex();

        //广播通知world状态变更
        CommandRequest clearReq = CommandRequests.newServerCommandRequest("LeagueSLGWorldCommandService.statusChanged");
        TLBase.getInstance().getRpcUtil().sendToAll(ServerType.WORLD, clearReq, status, index);

        //广播通知game状态变更
        clearReq = CommandRequests.newServerCommandRequest("LeagueSLGGameCommandService.statusChanged");
        TLBase.getInstance().getRpcUtil().sendToAll(ServerType.GAME, clearReq, status, index);
    }

    public static void charge(Player player){
        long leagueId = LeagueManager.getLeagueId(player);
        if(leagueId > 0){
            LeagueSLGGameGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueGameSLG);
            if(globalData != null) {
                SLGGroupResult groupResult = globalData.getGroupData().get(leagueId);
                if (groupResult != null) {
                    PlayerCommandRequest request = CommandRequests.newPlayerCommandRequest("LeagueSLGWorldCommandService.charge",
                            player.getPlayerId(), -1, true);
                    TLBase.getInstance().getRpcUtil().sendToHashNode(ServerType.WORLD, groupResult.getAreaId(), request, player.getPlayerId());
                }
            }
        }
    }

    public static int powerToArmy(long power){
        return  (int)(Math.pow(power*10000, 0.39534)  * 10);
    }

    /**
     * 战力估算
     */
    public static void main(String[] args) {
        for(int i = 1; i <= 12; i++){
            long power = (long) Math.pow(10, i);
            String powerStr = power + "";
            if(power > 10000 *10000){
                powerStr = (power / 10000/10000) + " 亿";
            }else if(power > 10000){
                powerStr = (power / 10000) + " 万";
            }
            System.out.println(powerStr + " -> " + powerToArmy(power));
        }
    }

}

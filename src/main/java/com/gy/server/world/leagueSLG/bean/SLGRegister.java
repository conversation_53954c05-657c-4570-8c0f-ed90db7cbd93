package com.gy.server.world.leagueSLG.bean;

import com.gy.server.packet.PbLeague;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: tl_game_4
 * @description: SLG报名信息
 * @author: <PERSON><PERSON>
 * @create: 2024/11/26
 **/
public class SLGRegister implements Serializable {

    /**
     * <leagueId, leagueFightingPower>
     */
    private Map<Long, Long> leagues = new HashMap<>();

    public SLGRegister() {
    }

    public SLGRegister(List<SLGLeague> leagues) {
        for (SLGLeague league : leagues) {
            this.leagues.put(league.getId(), league.getFightingPower());
        }
    }

    public Map<Long, Long> getLeagues() {
        return leagues;
    }

    public void setLeagues(Map<Long, Long> leagues) {
        this.leagues = leagues;
    }


}

package com.gy.server.world.leagueSLG.bean;

import java.util.HashMap;
import java.util.Map;

/**
 * @program: tl_game_4
 * @description: 活动结算信息
 * @author: <PERSON>.<PERSON>
 * @create: 2024/12/28
 **/
public class SLGSeasonFinishInfo implements java.io.Serializable {
    private static final long serialVersionUID = 23403847231983240L;

    private Map<Long, Integer> leagueRank = new HashMap<>();

    private Map<Long, Integer> playerRank = new HashMap<>();

    private Map<Long, Integer> playerMedals = new HashMap<>();

    public Map<Long, Integer> getLeagueRank() {
        return leagueRank;
    }

    public void setLeagueRank(Map<Long, Integer> leagueRank) {
        this.leagueRank = leagueRank;
    }

    public Map<Long, Integer> getPlayerRank() {
        return playerRank;
    }

    public void setPlayerRank(Map<Long, Integer> playerRank) {
        this.playerRank = playerRank;
    }

    public Map<Long, Integer> getPlayerMedals() {
        return playerMedals;
    }

    public void setPlayerMedals(Map<Long, Integer> playerMedals) {
        this.playerMedals = playerMedals;
    }

    public String toString(){
        String rst = "leagueRank: " + leagueRank + " playerRank: " + playerRank + " playerMedals: " + playerMedals;
        return rst;
    }

}

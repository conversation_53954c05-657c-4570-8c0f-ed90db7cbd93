package com.gy.server.world.leagueSLG;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.gy.server.core.battleRule.base.BaseBattleInfo;
import com.gy.server.core.battleRule.enums.BattleEnums;
import com.gy.server.game.global.GlobalData;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.gy.server.world.leagueSLG.bean.SLGArea;
import com.gy.server.world.leagueSLG.bean.SLGRegister;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @program: tl_game_4
 * @description: SLG全局数据, 只有主节点能存
 * @author: Huang.Xia
 * @create: 2024/11/26
 **/
public class LeagueSLGWorldGlobalData extends GlobalData {
    @Protobuf(order = 1)
    private BaseBattleInfo baseInfo = new BaseBattleInfo(BattleEnums.league_slg);

    @Protobuf(order = 2)
    int areaCount;

    /**
     * 战斗日序号 0,1,2
     */
    @Protobuf(order = 3)
    int combatDayIndex = 0;

    public int getAreaCount() {
        return areaCount;
    }

    public void setAreaCount(int areaCount) {
        this.areaCount = areaCount;
    }

    @Override
    public void readFromPb(byte[] bytes) throws Exception {
        //使用jpb反序列化
        throw new UnsupportedOperationException();
    }

    @Override
    public byte[] writeToPb() {
        baseInfo.beforePb();
        return PbUtilCompress.encode(this);
    }

    public BaseBattleInfo getBaseInfo() {
        return baseInfo;
    }

    public void setBaseInfo(BaseBattleInfo baseInfo) {
        this.baseInfo = baseInfo;
    }

    public int getCombatDayIndex() {
        return combatDayIndex;
    }

    public void setCombatDayIndex(int combatDayIndex) {
        this.combatDayIndex = combatDayIndex;
    }

    @Override
    public void init() {
        baseInfo.init();
        baseInfo.setCrossType(BattleEnums.league_slg);
    }
}

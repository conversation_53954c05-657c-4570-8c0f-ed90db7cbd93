package com.gy.server.world.leagueSLG.bean;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.gy.server.packet.PbSLG;

import java.util.Comparator;

/**
 * @program: tl_game_4
 * @description: 赛区内排名节点
 * @author: <PERSON>.<PERSON>
 * @create: 2024/12/5
 **/
public class SLGLeagueRankNode {
    public static Comparator<SLGLeagueRankNode> comparator = new SLGLeagueRankNodeComparator();


    @Protobuf(order = 1)
    private long lid;
    @Protobuf(order = 2)
    private long fightingPower;
    @Protobuf(order = 3)
    private int score;

    public SLGLeagueRankNode() {}

    public SLGLeagueRankNode(long lid, long fightingPower) {
        this.lid = lid;
        this.fightingPower = fightingPower;
    }

    public long getLid() {
        return lid;
    }

    public void setLid(long lid) {
        this.lid = lid;
    }

    public long getFightingPower() {
        return fightingPower;
    }

    public void setFightingPower(long fightingPower) {
        this.fightingPower = fightingPower;
    }

    public int getScore() {
        return score;
    }

    public void setScore(int score) {
        this.score = score;
    }

    public PbSLG.SLGLeagueRankNode genPb(int rank, SLGArea area){
        PbSLG.SLGLeagueRankNode.Builder b = PbSLG.SLGLeagueRankNode.newBuilder();
        b.setScore(score)
                .setRank(rank)
                .setLeague(area.getLeague(lid).getSimpleLeague());
        return b.build();
    }

    private static class SLGLeagueRankNodeComparator implements Comparator<SLGLeagueRankNode> {
        @Override
        public int compare(SLGLeagueRankNode o1, SLGLeagueRankNode o2) {
            if (o1.score == o2.score) {
                if(o1.fightingPower == o2.fightingPower){
                    return Long.compare(o1.lid, o2.lid);
                }else {
                    return Long.compare(o2.fightingPower, o1.fightingPower);
                }
            }else{
                return Long.compare(o2.score, o1.score);
            }
        }
    }
}

package com.gy.server.world.record;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.record.RecordGlobalData;
import com.gy.server.game.record.combat.CombatRecord;
import com.gy.server.packet.PbCombat;
import com.gy.server.packet.PbCommons;
import com.gy.server.utils.time.DateTimeUtil;

/**
 * <AUTHOR> - [Create on 2020/01/11 10:43]
 */
public enum WorldCombatRecordGenerator {

    //结构可能还有用先放一个默认的保持结构
    None {
        @Override
        protected int getExpireDay() {
            return 1;
        }

        public long getRecordId() {
            return 0L;
        }
    },
    
    UC{
        @Override
        protected int getExpireDay() {
            return 30;
        }

        @Override
        public long getRecordId() {
            RecordGlobalData globalData = GlobalDataManager.getData(GlobalDataType.Record);
            return globalData.createCombatRecordId();
        }
    },

    ;

    /**
     * 返回多少天过期，相对于当前时间来说
     */
    protected abstract int getExpireDay();

    public abstract long getRecordId();

    public WorldCombatRecord generate(AbstractStage stage, PbCommons.MiniUser atkMiniUser,
                                      PbCommons.MiniUser defMiniUser, boolean isPvp) {
        return generate(stage, atkMiniUser, defMiniUser, isPvp, false);
    }

    public WorldCombatRecord generate(AbstractStage stage, PbCommons.MiniUser atkMiniUser,
                                      PbCommons.MiniUser defMiniUser, boolean isPvp, boolean getDefMvp) {
        WorldCombatRecord combatRecord = new WorldCombatRecord();
        combatRecord.setId(getRecordId());
        combatRecord.setType(this.name());
        combatRecord.setExpireTime(DateTimeUtil.toDate(ServerConstants.getCurrentTimeLocalDateTime().plusDays(getExpireDay())));

        PbCombat.CombatRecordData.Builder dataBuilder = PbCombat.CombatRecordData.newBuilder()
                .setStageRecord(stage.getStageRecord().build())
                .setAttacker(atkMiniUser)
                .setIsPvp(isPvp)
                .setAtkMvp(stage.genCombatFinishMvp().build())
                .setIsWin(stage.isWin())
                .addAllCombatFinishDetailData(stage.genCombatFinishDetailDataList())
                .addAllAtkTeamInfos(AbstractStage.genFinishUnitInfo(stage.getAtkCamp().getTeams()))
                ;

        if (getDefMvp) {
            dataBuilder.setDefMvp(stage.genCombatFinishMvp(false).build());
        }

        if (defMiniUser != null) {
            dataBuilder.setDefender(defMiniUser);
        }
        combatRecord.getInfo().setData(dataBuilder.build());

        return combatRecord;
    }

}

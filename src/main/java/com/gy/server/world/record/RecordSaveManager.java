package com.gy.server.world.record;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

import com.gy.server.core.ElapsedTimeStatistics;
import com.gy.server.core.Launcher;
import com.gy.server.core.MainThread;
import com.gy.server.core.log.SystemLogger;
import com.gy.server.game.db.DbManager;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

/**
 * 战报存储器：专门用于周期性存储{@link WorldCombatRecord}数据
 *
 * <AUTHOR> - [Create on 2019/08/24 15:33]
 */
public final class RecordSaveManager {

    private static final long INTERNAL = 20;
    private static final BlockingQueue<WorldCombatRecord> RECORDS_DATA_QUEUE = new LinkedBlockingQueue<>();
    private static volatile boolean running = true;

    private RecordSaveManager() {
    }

    public static void startup() {
        Launcher.launchStartupInfo("RecordSaveManager start");
        ThreadPool.execute(RecordSaveManager::save);
    }

    /**
     * 每过一段时间，就会探测队列内是否有数据
     * 如果有数据，将会取出去重的CombatRecord(覆盖的策略，同一个Record生成的CombatRecord，后来的会覆盖前面的)，进行批量存储
     * 存储时机：每5秒或每200个CombatRecord，会进行批量存储一次
     */
    private static void save() {
        while (MainThread.isRunning()) {
            try {
                if (!RECORDS_DATA_QUEUE.isEmpty()) {
                    long totalStart = System.nanoTime();
                    Map<Long, WorldCombatRecord> combatRecordMap = new HashMap<>();
                    long consume = 0;

                    WorldCombatRecord combatRecord;
                    while ((combatRecord = RECORDS_DATA_QUEUE.poll()) != null) {
                        long start = System.currentTimeMillis();
                        // 去重用的，同一个Record，后来的CombatRecord会覆盖之前的
                        combatRecordMap.put(combatRecord.getId(), combatRecord);
                        consume += System.currentTimeMillis() - start;

                        // 每5秒
                        if (consume >= DateTimeUtil.MillisOfSecond * 5
                                // 每200个CombatRecord
                                || combatRecordMap.size() >= 200) {
                            doSaveAndClear(combatRecordMap);
                            consume = 0;
                        }
                    }

                    doSaveAndClear(combatRecordMap);
                    // 使用Runner日志
                    ElapsedTimeStatistics.addElapsedNanoTimeWithoutMinNano(ElapsedTimeStatistics.Type.Runner, "RecordSaveManager", System.nanoTime() - totalStart);
                }

                Thread.sleep(INTERNAL);
            } catch (Exception e) {
                SystemLogger.error("RecordSaveManager error: " + e);
            }
        }

        running = false;
        Launcher.launchShutdownInfo("RecordSaveManager shutdown");
    }

    private static void doSaveAndClear(Map<Long, WorldCombatRecord> combatRecordMap) {
        if (!combatRecordMap.isEmpty()) {
            List<WorldCombatRecord> combatRecordList = new ArrayList<>(combatRecordMap.values());
            DbManager.updateBatch(combatRecordList);
            combatRecordMap.clear();
        }
    }

    public static void add(WorldCombatRecord combatRecord) {
        RECORDS_DATA_QUEUE.offer(combatRecord);
    }

    public static boolean isRunning() {
        return running;
    }

    public static int queueSize() {
        return RECORDS_DATA_QUEUE.size();
    }

}

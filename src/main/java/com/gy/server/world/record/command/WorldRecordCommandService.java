package com.gy.server.world.record.command;

import com.gy.server.annotation.MessageMethod;
import com.gy.server.annotation.MessageServiceBean;
import com.gy.server.core.MessageServerType;
import com.gy.server.core.command.CommandRequestParams;
import com.gy.server.core.command.PlayerCommandRequest;
import com.gy.server.game.db.DbManager;
import com.gy.server.game.record.Record;
import com.gy.server.game.record.RecordType;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbCombat;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.world.record.WorldCombatRecord;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> - [Create on 2021/09/26 14:29]
 */
@MessageServiceBean(description = "中心服战报记录服务", messageServerType = MessageServerType.world)
public class WorldRecordCommandService {

    @MessageMethod(description = "战报记录")
    public static void recordCombatData(PlayerCommandRequest request, CommandRequestParams params) {
        long combatRecordId = params.getParam(0);
        WorldCombatRecord combatRecord = DbManager.get(WorldCombatRecord.class, combatRecordId);

        int errorCode;
        PbCombat.CombatRecordData recordData;
        if (Objects.isNull(combatRecord)) {
            errorCode = Text.战报数据不存在;
            recordData = null;
        } else {
            errorCode = Text.没有异常;
            recordData = combatRecord.getInfo().getData();
        }

        PlayerCommandRequest r = CommandRequests.newPlayerCommandRequest("RecordCommandService.recordCombatData", request.getPlayerId(), request.getTimestamp());
        TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, request.getServerId(), r, errorCode, recordData);
    }

    @MessageMethod(description = "战报记录")
    public static void recordGet(PlayerCommandRequest request, CommandRequestParams params) {
        PbProtocol.RecordListRst.Builder rst = PbProtocol.RecordListRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        int type = params.getParam(0);
        String subKey = params.getParam(1);
        List<Long> recordIds = params.getParam(2);
        logic:
        {
            RecordType recordType = RecordType.of(type);
            if (Objects.isNull(recordType)) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            if (!recordType.getRecordDeal().checkSubKey(subKey)) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            List<Record> recordList = recordType.getRecordList(null, subKey);
            if(CollectionUtil.isEmpty(recordIds)){
                for (Record record : recordList) {
                    rst.addRecords(record.writeToPb());
                }
            }else{
                for (Record record : recordList) {
                    if (recordIds.contains(record.getCombatRecordId())) {
                        rst.addRecords(record.writeToPb());
                    }
                }
            }
        }

        request.addCallbackParam(rst.build());
    }

}

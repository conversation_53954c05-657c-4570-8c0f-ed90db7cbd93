package com.gy.server.world.record;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.record.combat.CombatRecordInfo;

import org.hibernate.annotations.Type;

/**
 * <AUTHOR> - [Create on 2020/01/11 10:36]
 */
@Entity
@Table(name = "world_combat_record")
public class WorldCombatRecord {

    @Id
    @Column(name = "id")
    private long id;

    @Column(name = "type")
    private String type;

    /**
     * 当前战报创建的时间
     */
    @Column(name = "create_time")
    private Date createTime = ServerConstants.getCurrentTimeDate();

    @Column(name = "expire_time")
    private Date expireTime;

    @Column(name = "info")
    @Type(type = "com.gy.server.game.record.combat.CombatRecordInfo")
    private CombatRecordInfo info = new CombatRecordInfo();

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public CombatRecordInfo getInfo() {
        return info;
    }

    public void setInfo(CombatRecordInfo info) {
        this.info = info;
    }

}

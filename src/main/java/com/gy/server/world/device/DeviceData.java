package com.gy.server.world.device;

import java.util.ArrayList;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArraySet;

import com.gy.server.game.global.GlobalData;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.ttlike.server.tl.baselib.serialize.center.DeviceDataCenterDb;

/**
 * <AUTHOR> - [Created on 2019/2/23 15:21]
 */
public class DeviceData extends GlobalData {

    /**
     * 已注册过的设备id集合
     */
    private Set<String> devices = new CopyOnWriteArraySet<>();

    @Override
    public void readFromPb(byte[] bytes) throws Exception {
        DeviceDataCenterDb db = PbUtilCompress.decode(DeviceDataCenterDb.class, bytes);

        devices.addAll(db.getDeviceIdList());
    }

    @Override
    public byte[] writeToPb() {
        DeviceDataCenterDb pb = new DeviceDataCenterDb();

        pb.setDeviceIdList(new ArrayList<>(devices));

        return PbUtilCompress.encode(pb);
    }

    /**
     * 判断指定设备是否已有注册过的信息，且增加该设备信息
     */
    public boolean hasAndAddDevice(String deviceId) {
        boolean has = hasDevice(deviceId);
        devices.add(deviceId);
        return has;
    }

    /**
     * 判断指定设备是否已有注册过的信息
     */
    public boolean hasDevice(String deviceId) {
        return devices.contains(deviceId);
    }
}

package com.gy.server.world.fix;

import com.gy.server.core.log.CommonLogger;
import com.gy.server.game.global.GlobalData;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.redis.key.BaseRedisKey;

/**
 * <AUTHOR> - [Create on 2020/06/30 10:00]
 */
public class WorldFixDesignerGlobalData extends GlobalData {

    public void checkFix() {
        for (WorldFixDesignerType fixDesignerType : WorldFixDesignerType.values()) {
            // 逐个尝试添加，添加成功即可开始补锅
            if (TLBase.getInstance().getRedisAssistant().setAdd(getFixDesignerKey(), fixDesignerType.name())) {
                fixDesignerType.fix();
                CommonLogger.warn("world global data fix designer: " + fixDesignerType.name());
            }
        }
    }

    public static String getFixDesignerKey() {
        return BaseRedisKey.Commons.WORLD_FIX_DESIGNER.getRedisKey();
    }

}

package com.gy.server.world.smallGroup;

import com.gy.server.game.global.GlobalData;
import com.gy.server.game.smallGroup.bean.marry.MarryCruiseApplyInfo;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.ttlike.server.tl.baselib.serialize.smallGroup.MarryCruiseApplyInfoDb;
import com.ttlike.server.tl.baselib.serialize.smallGroup.SmallGroupWorldGlobalDataDb;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 小团体世界全局数据
 *
 * <AUTHOR> 2025/2/11 16:14
 **/
public class SmallGroupWorldGlobalData extends GlobalData {

    /**
     * 情缘巡游申请信息map
     * key:申请当天的6点   value:
     */
    private Map<Long, MarryCruiseApplyInfo> cruiseApplyInfoMap = new ConcurrentHashMap<>();

    /**
     * 巡游信息通知map
     * key:创建id     value:上次通知时间
     */
    private Map<Long, Long> notifyMap = new HashMap<>();

    private AtomicInteger marryNum = new AtomicInteger(0);

    @Override
    public void readFromPb(byte[] bytes) throws Exception {
        if(bytes == null){
            return;
        }

        SmallGroupWorldGlobalDataDb db = PbUtilCompress.decode(SmallGroupWorldGlobalDataDb.class, bytes);
        for (MarryCruiseApplyInfoDb bean : db.getCruiseApplyInfoDbList()) {
            MarryCruiseApplyInfo cruiseApplyInfo = new MarryCruiseApplyInfo(bean);
            cruiseApplyInfoMap.put(cruiseApplyInfo.getTime(), cruiseApplyInfo);
        }

        notifyMap.putAll(db.getNotifyMap());
    }

    @Override
    public byte[] writeToPb() {
        List<MarryCruiseApplyInfoDb> list = new ArrayList<>();
        cruiseApplyInfoMap.values().forEach(bean -> list.add(bean.genDb()));

        SmallGroupWorldGlobalDataDb db = new SmallGroupWorldGlobalDataDb(list, notifyMap);
        return PbUtilCompress.encode(db);
    }

//    public void cruiseNotify(PbSmallGroup.CruiseInfo cruiseInfo){
//        ThreadPool.execute(() -> {
//            //获得主场景绑定的场景服id
//            String sceneBandKey = BaseRedisKey.Scene.sceneId2SceneServerId.getRedisKey(Configuration.serverId);
//            String sceneServerId = TLBase.getInstance().getRedisAssistant().get(sceneBandKey);
//            ServerCommandRequest request = CommandRequests.newServerCommandRequest("SceneOperationCommandService.getScenePlayerList");
//            TLBase.getInstance().getRpcUtil().sendToNodeWithCallBack(new TLMessageCallbackTask() {
//                @Override
//                public void complete(CallbackResponse response) {
//                    List<Long> playerIdList = response.getParam(0);
//                    if(!playerIdList.isEmpty()){
//                        Map<Integer, Set<Long>> serverPlayerIdMap = new HashMap<>();
//                        for (long playerId : playerIdList) {
//                            int serverId = Player.getServerId(playerId);
//                            serverPlayerIdMap.computeIfAbsent(serverId, set -> new HashSet<>()).add(playerId);
//                        }
//
//                        PbProtocol.SmallGroupMarryCruiseNotify.Builder builder = PbProtocol.SmallGroupMarryCruiseNotify.newBuilder();
//                        builder.setCruise(cruiseInfo);
//                        List<MiniGamePlayer> list = PlayerHelper.getMiniPlayers(cruiseInfo.getJobMap().keySet());
//                        for (MiniGamePlayer bean : list) {
//                            builder.addMember(bean.genMinMiniUser());
//                        }
//
//                        for (Map.Entry<Integer, Set<Long>> entry : serverPlayerIdMap.entrySet()) {
//                            ServerCommandRequest request = CommandRequests.newServerCommandRequest("SmallGroupRstCommandService.marryCruiseNotify");
//                            TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, entry.getKey(), request, entry.getValue(), builder.build());
//                        }
//                    }
//                }
//
//                @Override
//                public void timeout() {
//
//                }
//            }, ServerType.SCENE, Integer.parseInt(sceneServerId), request, SceneMapType.mainCity);
//        });
//    }

    public Map<Long, MarryCruiseApplyInfo> getCruiseApplyInfoMap() {
        return cruiseApplyInfoMap;
    }

    public void setCruiseApplyInfoMap(Map<Long, MarryCruiseApplyInfo> cruiseApplyInfoMap) {
        this.cruiseApplyInfoMap = cruiseApplyInfoMap;
    }

    public Map<Long, Long> getNotifyMap() {
        return notifyMap;
    }

    public void setNotifyMap(Map<Long, Long> notifyMap) {
        this.notifyMap = notifyMap;
    }

    public AtomicInteger getMarryNum() {
        return marryNum;
    }

    public void setMarryNum(AtomicInteger marryNum) {
        this.marryNum = marryNum;
    }
}

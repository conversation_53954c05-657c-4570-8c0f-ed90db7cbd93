package com.gy.server.world.smallGroup.commandService;

import com.gy.server.annotation.MessageMethod;
import com.gy.server.annotation.MessageServiceBean;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.MessageServerType;
import com.gy.server.core.MethodInvokeType;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.command.CommandRequestParams;
import com.gy.server.core.command.PlayerCommandRequest;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.constant.ConstantService;
import com.gy.server.game.constant.ConstantType;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.smallGroup.SmallGroupHelper;
import com.gy.server.game.smallGroup.bean.marry.MarryCruiseApplyInfo;
import com.gy.server.game.smallGroup.bean.marry.SmallGroupMarryCreate;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbSmallGroup;
import com.gy.server.utils.time.DateTimeUtil;
import com.gy.server.world.smallGroup.SmallGroupWorldGlobalData;
import com.ttlike.server.tl.baselib.TLBase;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 小团体消息-world
 * <AUTHOR> 2025/2/11 17:33
 **/
@MessageServiceBean(description = "小团体消息", messageServerType = MessageServerType.world)
public class SmallGroupWorldCommandService {

    @MessageMethod(description = "获取情缘巡游申请信息", invokeType = MethodInvokeType.async)
    private static void cruiseApplyInfo(ServerCommandRequest request, CommandRequestParams params){
        SmallGroupWorldGlobalData worldGlobalData = GlobalDataManager.getData(GlobalDataType.smallGroupWorld);

        long time = ServerConstants.getNowHourTime(CommonUtils.getRefreshTimeHour());
        long endTime = time + 14 * DateTimeUtil.MillisOfDay;

        List<MarryCruiseApplyInfo> resultList = new ArrayList<>();
        for (Map.Entry<Long, MarryCruiseApplyInfo> entry : worldGlobalData.getCruiseApplyInfoMap().entrySet()) {
            if(entry.getKey() >= time && entry.getKey() <= endTime){
                resultList.add(entry.getValue());
            }
        }

        request.addCallbackParam(resultList);
    }

    @MessageMethod(description = "情缘巡游申请", invokeType = MethodInvokeType.async)
    private static void marryCruiseApply(PlayerCommandRequest request, CommandRequestParams params){
        long cruiseTime = params.getParam(0);
        int timeId = params.getParam(1);
        long createId = params.getParam(2);
        long start = params.getParam(3);

        if(ServerConstants.getCurrentTimeMillis() >= start){
            request.addCallbackParam(Text.此时段已过期);
            return;
        }

        SmallGroupWorldGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroupWorld);
        MarryCruiseApplyInfo applyInfo = globalData.getCruiseApplyInfoMap().computeIfAbsent(cruiseTime, bean -> new MarryCruiseApplyInfo(cruiseTime));
        if(applyInfo.getApplyMap().containsKey(timeId)){
            request.addCallbackParam(Text.巡游此时段已被申请);
        }else {
            request.addCallbackParam(Text.没有异常);
            request.addCallbackParam(globalData.getMarryNum().incrementAndGet());
            applyInfo.getApplyMap().put(timeId, createId);
            applyInfo.getStartMap().put(createId, start);
        }
    }

    @MessageMethod(description = "获取进行中情缘巡游", invokeType = MethodInvokeType.async)
    private static void getProgressCruiseInfo(PlayerCommandRequest request, CommandRequestParams params){
        SmallGroupWorldGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroupWorld);
        LocalDateTime time6 = ServerConstants.getOneDayOneHour(ServerConstants.getCurrentTimeLocalDateTime(), CommonUtils.getRefreshTimeHour());
        long time = DateTimeUtil.toMillis(time6);

        long now = ServerConstants.getCurrentTimeMillis();
        if(globalData.getCruiseApplyInfoMap().containsKey(time)){
            MarryCruiseApplyInfo applyInfo = globalData.getCruiseApplyInfoMap().get(time);
            for (Map.Entry<Long, Long> entry : applyInfo.getStartMap().entrySet()) {
                long start = entry.getValue();
                if(now > start && now < start + DateTimeUtil.MillisOfMinute * 5l){
                    long createId = entry.getKey();

                    String redisKey = GsRedisKey.SmallGroup.small_group_create.getRedisKey(createId);
                    SmallGroupMarryCreate marryCreate = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupMarryCreate.class, redisKey);
                    if(marryCreate != null){
                        PbSmallGroup.CruiseInfo cruiseInfo = marryCreate.genCruiseInfoPb();
                        PbProtocol.SmallGroupMarryCruiseNotify notify = SmallGroupHelper.genSmallGroupMarryCruiseNotify(cruiseInfo);
                        request.addCallbackParam(notify);
                    }
                }
            }
        }
    }

}

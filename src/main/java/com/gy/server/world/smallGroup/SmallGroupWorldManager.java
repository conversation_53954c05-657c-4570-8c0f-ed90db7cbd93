package com.gy.server.world.smallGroup;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.core.thread.AbstractRunner;
import com.gy.server.game.constant.ConstantService;
import com.gy.server.game.constant.ConstantType;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.smallGroup.SmallGroupHelper;
import com.gy.server.game.smallGroup.bean.marry.MarryCruiseApplyInfo;
import com.gy.server.game.smallGroup.bean.marry.SmallGroupMarryCreate;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbSmallGroup;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 小团体世界管理器
 *
 * <AUTHOR> 2025/2/11 16:09
 **/
public class SmallGroupWorldManager extends AbstractRunner {

    private static final SmallGroupWorldManager instance = new SmallGroupWorldManager();
    public static SmallGroupWorldManager getInstance(){
        return instance;
    }

    @Override
    public void subRunnerExecute() throws Exception{
        try {
            checkCruiseApplyInfo();
        }catch (Exception e){
            e.printStackTrace();
        }

        try {
            checkCruiseInfo();
        }catch (Exception e){
            e.printStackTrace();
        }
    }



    /**
     * 检查巡游申请信息
     */
    public void checkCruiseApplyInfo(){
        SmallGroupWorldGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroupWorld);

        long nowTime = ServerConstants.getNowHourTime(CommonUtils.getRefreshTimeHour());
        long endTime = nowTime + 14 * DateTimeUtil.MillisOfDay;

        List<Long> removeKey = new ArrayList<>();
        for (Map.Entry<Long, MarryCruiseApplyInfo> entry : globalData.getCruiseApplyInfoMap().entrySet()) {
            if(entry.getKey() < nowTime || entry.getKey() > endTime){
                removeKey.add(entry.getKey());
            }
        }

        if(!removeKey.isEmpty()){
            for (long key : removeKey) {
                globalData.getCruiseApplyInfoMap().remove(key);
            }
        }
    }

    /**
     * 检查巡游信息
     */
    public void checkCruiseInfo(){
        LocalDateTime time6 = ServerConstants.getOneDayOneHour(ServerConstants.getCurrentTimeLocalDateTime(), CommonUtils.getRefreshTimeHour());
        long time = DateTimeUtil.toMillis(time6);

        long nowTemp = ServerConstants.getCurrentTimeMillis();
        SmallGroupWorldGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroupWorld);

        if(globalData.getCruiseApplyInfoMap().containsKey(time)){
            MarryCruiseApplyInfo applyInfo = globalData.getCruiseApplyInfoMap().get(time);

            for (Map.Entry<Long, Long> entry : applyInfo.getStartMap().entrySet()) {
                long start = entry.getValue();
                long differ = start - nowTemp;

                //开始前5分钟
                if(differ > 0l && differ <= DateTimeUtil.MillisOfMinute * 5){
                    if(!globalData.getNotifyMap().containsKey(entry.getKey())){
                        globalData.getNotifyMap().put(entry.getKey(), nowTemp);
                        //通知所有服巡游信息
                        notifyCruiseInfo(entry.getKey(), false);
                    }
                }

                if(differ <= 0l && nowTemp < DateTimeUtil.MillisOfMinute * 5){
                    long temp = globalData.getNotifyMap().getOrDefault(entry.getKey(), 0l);
                    if(temp < start){
                        globalData.getNotifyMap().put(entry.getKey(), nowTemp);
                        //通知所有服巡游信息
                        notifyCruiseInfo(entry.getKey(), true);
                    }
                }
            }
        }

        List<Long> keyList = new ArrayList<>(globalData.getCruiseApplyInfoMap().keySet());
        for (long key : keyList) {
            if(key < time - DateTimeUtil.MillisOfHour * 6){
                globalData.getCruiseApplyInfoMap().remove(key);
            }
        }
    }

    /**
     * 通知巡游信息
     * @param createId
     * @param notifyAllGs 是否通知多有gs
     */
    public void notifyCruiseInfo(long createId, boolean notifyAllGs){
        ThreadPool.execute(() -> {
            String redisKey = GsRedisKey.SmallGroup.small_group_create.getRedisKey(createId);
            SmallGroupMarryCreate marryCreate = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupMarryCreate.class, redisKey);
            if(marryCreate != null){
                PbSmallGroup.CruiseInfo cruiseInfo = marryCreate.genCruiseInfoPb();
                PbProtocol.SmallGroupMarryCruiseNotify notify = SmallGroupHelper.genSmallGroupMarryCruiseNotify(cruiseInfo);

                if(notifyAllGs){
                    ServerCommandRequest request = CommandRequests.newServerCommandRequest("SmallGroupRstCommandService.notifyCruiseInfo");
                    TLBase.getInstance().getRpcUtil().sendToAll(ServerType.GAME, request, notify, new HashSet<>());
                }else {
                    for (Map.Entry<Integer, Set<Long>> entry : marryCreate.getServerMemberMap().entrySet()) {
                        ServerCommandRequest request = CommandRequests.newServerCommandRequest("SmallGroupRstCommandService.notifyCruiseInfo");
                        TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, entry.getKey(), request, notify, entry.getValue());
                    }
                }
            }
        });
    }

    @Override
    public long getRunnerInterval() {
        return 1000l;
    }

    @Override
    public String getRunnerName() {
        return "SmallGroupWorldManager";
    }
}

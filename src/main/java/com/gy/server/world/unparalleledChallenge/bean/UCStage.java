package com.gy.server.world.unparalleledChallenge.bean;

import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.combat.unit.Camp;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.combat.unit.HeroUnitCreator;
import com.gy.server.game.combat.unit.TeamUnit;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.game.record.RecordManager;
import com.gy.server.game.record.RecordType;
import com.gy.server.game.record.combat.CombatRecord;
import com.gy.server.game.unparalleledChallenge.UnparalleledChallengeService;
import com.gy.server.game.unparalleledChallenge.template.UnparalleledChallengeSeasonTemplate;
import com.gy.server.packet.PbRecord;
import com.gy.server.world.record.WorldCombatRecord;
import com.gy.server.world.record.WorldCombatRecordGenerator;
import com.gy.server.world.unparalleledChallenge.WorldUnparalleledChallengeGlobalData;
import com.gy.server.world.unparalleledChallenge.enums.UCPhaseType;
import com.ttlike.server.tl.baselib.serialize.player.MiniPlayer;
import com.ttlike.server.tl.baselib.serialize.record.CombatSectionDetailDataDb;
import com.ttlike.server.tl.baselib.serialize.record.CombatSectionUnitDataDb;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 *
 * @author: gbk
 * @date: 2024-12-10 16:43
 */
public class UCStage extends AbstractStage {

    long ucRecordId;
    UnparalleledChallengePlayerInfo atkInfo;
    UnparalleledChallengePlayerInfo defInfo;
//    int seasonId;
    UnparalleledChallengeRaceInfo raceInfo;
    MiniPlayer atkMiniPlayer;
    MiniPlayer defMiniPlayer;

    public UCStage(UnparalleledChallengeRaceInfo raceInfo, long ucRecordId
            , UnparalleledChallengePlayerInfo atkPlayerInfo, UnparalleledChallengePlayerInfo defPlayerInfo,
        MiniPlayer atkMiniPlayer, MiniPlayer defMiniPlayer){
        this.raceInfo = raceInfo;
        this.ucRecordId = ucRecordId;
        this.atkInfo = atkPlayerInfo;
        this.defInfo = defPlayerInfo;
        this.atkMiniPlayer = atkMiniPlayer;
        this.defMiniPlayer = defMiniPlayer;
    }

    @Override
    public void init() {
        WorldUnparalleledChallengeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.unparalleledChallenge);
        int seasonId = globalData.getSeasonId();
        UnparalleledChallengeSeasonTemplate seasonTemplate = UnparalleledChallengeService.getSeasonTemplates().get(seasonId);
        List<TeamUnit> atkTeamUnits = new ArrayList<>();
        List<TeamUnit> defTeamUnits = new ArrayList<>();
        fillTeamUnits(atkTeamUnits, atkInfo, seasonTemplate.buffId);
        fillTeamUnits(defTeamUnits, defInfo, seasonTemplate.buffId);
        init(seasonTemplate.battleCollectId, seasonTemplate.stageType, atkTeamUnits, defTeamUnits, seasonTemplate.lineupType);
    }

    @Override
    public void afterFinish() {
        WorldUnparalleledChallengeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.unparalleledChallenge);
        int seasonId = globalData.getSeasonId();
        //记录战斗次数
        long winPlayerId;
        long losePlayerId;
        if(isWin){
            winPlayerId = atkInfo.getPlayerId();
            losePlayerId = defInfo.getPlayerId();
        }else{
            winPlayerId = defInfo.getPlayerId();
            losePlayerId = atkInfo.getPlayerId();
        }


        long gmWinPlayerId = 9980000107L;
        if(winPlayerId == gmWinPlayerId || losePlayerId == gmWinPlayerId){
            winPlayerId = gmWinPlayerId;
            losePlayerId = gmWinPlayerId == atkInfo.getPlayerId() ? defInfo.getPlayerId() : atkInfo.getPlayerId();
        }


        GameLogger.ucInfo(raceInfo.getRaceId(), String.format("combat result, atkPlayerId : %s, defPlayerId : %s, isWin : %s", atkInfo.getPlayerId(), defInfo.getPlayerId(), isWin));

        //记录胜负
        UnparalleledChallengePlayerInfo winPlayerInfo = raceInfo.getPlayerInfos().get(winPlayerId);
        UnparalleledChallengePlayerInfo losePlayerInfo = raceInfo.getPlayerInfos().get(losePlayerId);
        winPlayerInfo.addWinTimes();
        losePlayerInfo.addLoseTimes();
        //小组赛不记录排名
        if(raceInfo.getPhase() != UCPhaseType.group){
            //失败者分配排名
            losePlayerInfo.setRank(raceInfo.allocateRank());
            //最终战，给第一分配排名
            if(raceInfo.getPhase() == UCPhaseType.fightFinal){
                winPlayerInfo.setRank(raceInfo.allocateRank());
            }
        }

        UnparalleledChallengePlayerInfo atkPlayerInfo = raceInfo.getPlayerInfos().get(atkInfo.getPlayerId());
        UnparalleledChallengePlayerInfo defPlayerInfo = raceInfo.getPlayerInfos().get(defInfo.getPlayerId());
        //构建战报，记录战报，记录竞猜信息
//        ThreadPool.execute(new UCSaveRecordAsync(seasonId, ucRecordId,this, atkPlayerInfo, defPlayerInfo, raceInfo, atkWinScores));
        saveRecord(seasonId, ucRecordId,this, atkPlayerInfo, defPlayerInfo, raceInfo, atkWinScores);

    }

    private void saveRecord(int seasonId, long ucRecordId, AbstractStage stage,
                            UnparalleledChallengePlayerInfo atkPlayerInfo, UnparalleledChallengePlayerInfo defPlayerInfo,
                            UnparalleledChallengeRaceInfo raceInfo, List<Boolean> atkWinScores){
        WorldCombatRecord record = WorldCombatRecordGenerator.UC.generate(stage, PlayerHelper.genBaseMiniUser(atkMiniPlayer).build()
                , PlayerHelper.genBaseMiniUser(defMiniPlayer).build()
                , true, false);
        long recordId = record.getId();
        //记录战报数据
        UnparalleledChallengeRecordInfo recordInfo = raceInfo.getRecordInfos().get(ucRecordId);
        recordInfo.setFinish(true);

        //增加战报信息
        if(raceInfo.getPhase() == UCPhaseType.group){
            atkPlayerInfo.getGroupRecordIds().add(recordId);
            defPlayerInfo.getGroupRecordIds().add(recordId);
        }else{
            atkPlayerInfo.getAdvanceRecordIds().put(raceInfo.getPhase().getId(), recordId);
            defPlayerInfo.getAdvanceRecordIds().put(raceInfo.getPhase().getId(), recordId);
        }
        //尝试结算竞猜
        trySettlementBet(recordId);

        RecordType recordType = RecordType.unparalleledChallenge;
        //构建战报信息
        UnparalleledChallengeSeasonTemplate seasonTemplate = UnparalleledChallengeService.getSeasonTemplates().get(seasonId);
        PbRecord.RecordPlayer atkRecordPlayer = RecordManager.genRecordPlayer(atkMiniPlayer, seasonTemplate.lineupType, stage.getAtks()).build();
        PbRecord.RecordPlayer defRecordPlayer = RecordManager.genRecordPlayer(defMiniPlayer, seasonTemplate.lineupType, stage.getDefs()).build();
        //存储回放信息
        CombatRecord cr = CombatRecord.create(stage, record.getId(), atkMiniPlayer
                , defMiniPlayer
                , CombatRecord.OverdueTime.UC, false, recordType.getId());
        UCCombatRecord combatRecord = new UCCombatRecord(cr.getCosKey(), stage.isWin(), recordType, recordId, atkRecordPlayer, defRecordPlayer, detailDataDbs);
        RecordManager.addRecord(combatRecord);
        RecordManager.save(cr);
    }


    /**
     * 尝试结算竞猜
     */
    private void trySettlementBet(long recordId){
        UCBetInfo ucBetInfo = raceInfo.getBetInfos().get(raceInfo.getPhase());
        if(Objects.nonNull(ucBetInfo)){
            if(ucBetInfo.getAtkPlayerId() == atkInfo.getPlayerId() && ucBetInfo.getDefPlayerId() == defInfo.getPlayerId()){
                //只记录战报id，暂时不记录胜负，等到展示期间再记录胜负
                ucBetInfo.setRecordId(recordId);
            }
        }
    }

    //三场战斗胜利情况
    List<Boolean> atkWinScores = new ArrayList<>();

    /**
     * 三队胜利条件 胜利场数大于失败场数
     */
    @Override
    protected void judgeWinner() {
        List<TeamUnit> defList = this.getDefs();
        if (defList == null) {
            this.isWin = true;
            return;
        }
        this.isWin = getDefCamp().getCurrentTeam().isAllFail();
    }

    List<CombatSectionDetailDataDb> detailDataDbs = new ArrayList<>();
    @Override
    public void sectionEndDeal() {
        CombatSectionDetailDataDb detailDataDb = new CombatSectionDetailDataDb();
        //记录队伍信息
        TeamUnit atkTeamUnit = getAtkCamp().getCurrentTeam();
        TeamUnit defTeamUnit = getDefCamp().getCurrentTeam();
        for (HeroUnit heroUnit : atkTeamUnit.getFormation()) {
            if(Objects.nonNull(heroUnit)){
                fullUnitData(heroUnit, detailDataDb.getAtkUnitDatas());
            }
        }
        for (HeroUnit heroUnit : defTeamUnit.getFormation()) {
            if(Objects.nonNull(heroUnit)){
                fullUnitData(heroUnit, detailDataDb.getDefUnitDatas());
            }
        }
        detailDataDb.setAtkIsWin(getWinCampId() == getAtkCamp().getId());
        detailDataDbs.add(detailDataDb);
    }

    private void fullUnitData(HeroUnit heroUnit, List<CombatSectionUnitDataDb> unitDatas){
        CombatSectionUnitDataDb unitDataDb = new CombatSectionUnitDataDb();
        unitDataDb.setId(heroUnit.getInstanceId());
        unitDataDb.setTid(heroUnit.getTid());
        unitDataDb.setCurHp(heroUnit.getAttributes().getHp());
        unitDatas.add(unitDataDb);
    }

    /**
     * 小节是否可以初始化
     */
    @Override
    public boolean canInitSection() {
        Camp atkCamp = getAtkCamp();
        int currentTeamIndex = atkCamp.getCurrentTeamIndex();
        List<TeamUnit> teams = atkCamp.getTeams();
        if (currentTeamIndex >= teams.size() - 1) {
            return false;
        }
        return super.canInitSection();
    }

    /**
     * 填充队伍信息
     */
    private void fillTeamUnits(List<TeamUnit> teamUnits, UnparalleledChallengePlayerInfo playerInfo, int buffId){
        for (Map<Integer, HeroUnitCreator> lineupInfo : playerInfo.getLineupInfos()) {
            List<HeroUnit> atkUnits = new ArrayList<>();
            lineupInfo.forEach((k, v) ->{
                HeroUnit heroUnit = v.create();
                heroUnit.setPosIndex(k);
                //增加赛季buff
                if(buffId > 0){
                    heroUnit.addBuff(buffId, 1);
                }
                atkUnits.add(heroUnit);
            });
            TeamUnit atkTeam = new TeamUnit(getNewId(), atkUnits);
            atkTeam.setAutoMode(playerInfo.getPlayerId());
            teamUnits.add(atkTeam);
        }
    }


}
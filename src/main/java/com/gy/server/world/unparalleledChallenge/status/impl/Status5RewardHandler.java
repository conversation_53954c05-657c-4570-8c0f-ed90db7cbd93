package com.gy.server.world.unparalleledChallenge.status.impl;

import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.player.Player;
import com.gy.server.world.unparalleledChallenge.WorldUnparalleledChallengeGlobalData;
import com.gy.server.world.unparalleledChallenge.bean.UnparalleledChallengePlayerInfo;
import com.gy.server.world.unparalleledChallenge.bean.UnparalleledChallengeRaceInfo;
import com.gy.server.world.unparalleledChallenge.status.AbsUCCombatStatus;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import org.apache.commons.lang3.tuple.Pair;

import java.util.HashMap;
import java.util.Map;

/**
 * 发奖
 * @author: gbk
 * @date: 2024-12-09 19:46
 */
public class Status5RewardHandler extends AbsUCCombatStatus {

    @Override
    public void tick(UnparalleledChallengeRaceInfo raceInfo) {
        //开始发奖，并且修改状态

        //serverId：PlayerId：rankId
        Map<Integer, Map<Long, Integer>> rankInfos = new HashMap<>();
        //整合玩家信息
        for (UnparalleledChallengePlayerInfo playerInfo : raceInfo.getPlayerInfos().values()) {
            try{
                int realServerId = Player.getRealServerId(playerInfo.getPlayerId());
                if(!rankInfos.containsKey(realServerId)){
                    rankInfos.put(realServerId, new HashMap<>());
                }
                rankInfos.get(realServerId).put(playerInfo.getPlayerId(), playerInfo.getRank());
            }catch (Exception e){

            }
        }

        GameLogger.ucInfo(raceInfo.getRaceId(), "uc start send Reward !!!");
        for (Integer serverId : rankInfos.keySet()) {
            GameLogger.ucInfo(raceInfo.getRaceId(), "serverId : " + serverId);
            GameLogger.ucInfo(raceInfo.getRaceId(), rankInfos.get(serverId).toString());
        }
        GameLogger.ucInfo(raceInfo.getRaceId(), "uc send Reward finish !!!");


        WorldUnparalleledChallengeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.unparalleledChallenge);
        //开始发奖
        for (Integer serverId : rankInfos.keySet()) {
            Map<Long, Integer> rankInfo = rankInfos.get(serverId);
            ServerCommandRequest request = CommandRequests.newServerCommandRequest("GsUCCommandService.rankReward");
            TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, serverId, request
                    , globalData.getSeasonId(), raceInfo.getRaceId(), rankInfo);
        }

        //结束状态
        raceInfo.nextPhase();
    }


}
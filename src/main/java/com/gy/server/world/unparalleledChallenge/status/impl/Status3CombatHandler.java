package com.gy.server.world.unparalleledChallenge.status.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.unparalleledChallenge.UnparalleledChallengeService;
import com.gy.server.game.unparalleledChallenge.template.UnparalleledChallengeConstant;
import com.gy.server.world.unparalleledChallenge.async.UCMatchAsync;
import com.gy.server.world.unparalleledChallenge.bean.*;
import com.gy.server.world.unparalleledChallenge.enums.UCPhaseType;
import com.gy.server.world.unparalleledChallenge.enums.UCCombatType;
import com.gy.server.world.unparalleledChallenge.status.AbsUCCombatStatus;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.Objects;

/**
 * 战斗阶段
 * @author: gbk
 * @date: 2024-12-09 19:46
 */
public class Status3CombatHandler extends AbsUCCombatStatus {

    @Override
    public void doRightNow(UnparalleledChallengeRaceInfo raceInfo) {

        //开始战斗
        ThreadPool.execute(new UCMatchAsync(raceInfo));
    }

    @Override
    public void tick(UnparalleledChallengeRaceInfo raceInfo) {
//        //检查是否打完战斗
//        boolean isFinish = true;
//        for (UnparalleledChallengeRecordInfo recordInfo : raceInfo.getRecordInfos().values()) {
//            if(!recordInfo.isFinish()){
//                isFinish = false;
//                break;
//            }
//        }
        long canOpenCombatTime = getCanOpenCombatTime(raceInfo);
        //检查是否到公布对战时间
        if(ServerConstants.getCurrentTimeMillis() >= canOpenCombatTime){
            //战斗异常结束，直接切状态
            modifyRaceCombatStatus(raceInfo, UCCombatType.combatAfter);
            //合并胜利失败次数展示
            for (UnparalleledChallengePlayerInfo playerInfo : raceInfo.getPlayerInfos().values()) {
                playerInfo.mergeTimes(raceInfo.getPhase());
            }
            //展示竞猜信息
            UCBetInfo ucBetInfo = raceInfo.getBetInfos().get(raceInfo.getPhase());
            if(Objects.nonNull(ucBetInfo)){
                UnparalleledChallengePlayerInfo atkPlayerInfo = raceInfo.getPlayerInfo(ucBetInfo.getAtkPlayerId());
                ucBetInfo.setWinPlayerId(atkPlayerInfo.getLoseTimes(raceInfo.getPhase().getShowPhase()) > 0 ? ucBetInfo.getDefPlayerId() : ucBetInfo.getAtkPlayerId());
            }
        }
//        if(isFinish){
//            raceInfo.setTickTime(canOpenCombatTime);
//            //战斗结束，切状态
//            modifyRaceCombatStatus(raceInfo, UCCombatType.combatAfter);
//
//        }else{
//            //检查是否到公布对战时间
//            if(ServerConstants.getCurrentTimeMillis() >= canOpenCombatTime){
//                //战斗异常结束，直接切状态
//                modifyRaceCombatStatus(raceInfo, UCCombatType.combatAfter);
//            }
//        }

    }

    /**
     * 获取可以查看战斗时间
     */
    private long getCanOpenCombatTime(UnparalleledChallengeRaceInfo raceInfo){
        UnparalleledChallengeConstant constant = UnparalleledChallengeService.getConstant();
        if(raceInfo.getPhase() == UCPhaseType.group){
            return constant.modifyTime(ServerConstants.getCurrentTimeLocalDateTime(), constant.viewableTime_1, false);
        }else{
            return constant.modifyTime(ServerConstants.getCurrentTimeLocalDateTime(), constant.viewableTime_2, false);
        }
    }

    public static void main(String[] args) {
        int size = 8;
        for (int i = 0; i < size / 2; i++) {
            int start = i;
            int end = size - i - 1;
            System.out.println(start + " : " + end);
        }

        System.out.println(--size);
    }

}
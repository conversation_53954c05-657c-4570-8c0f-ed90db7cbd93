package com.gy.server.world.unparalleledChallenge.status;

import com.gy.server.game.log.GameLogger;
import com.gy.server.world.unparalleledChallenge.bean.UnparalleledChallengeRaceInfo;
import com.gy.server.world.unparalleledChallenge.enums.UCCombatType;

/**
 * @author: gbk
 * @date: 2024-12-09 13:55
 */
public abstract class AbsUCCombatStatus implements IUCCombatStatus {

    @Override
    public void doRightNow(UnparalleledChallengeRaceInfo raceInfo) {

    }

    public void modifyRaceCombatStatus(UnparalleledChallengeRaceInfo raceInfo, UCCombatType combatType){
        GameLogger.ucInfo(raceInfo.getRaceId(), "uc change combatType, oldType:" + raceInfo.getCombatType() + ",  newType:" + combatType);
        raceInfo.setCombatType(combatType);
        raceInfo.setChangeCombatTypeMark(false);
    }

}
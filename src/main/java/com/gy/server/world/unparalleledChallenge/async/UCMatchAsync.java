package com.gy.server.world.unparalleledChallenge.async;

import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.world.unparalleledChallenge.WorldUCCombatManager;
import com.gy.server.world.unparalleledChallenge.bean.*;
import com.gy.server.world.unparalleledChallenge.enums.UCPhaseType;
import com.ttlike.server.tl.baselib.serialize.player.MiniPlayer;

import java.util.*;

/**
 * 异步匹配
 * @author: gbk
 * @date: 2024-12-16 17:54
 */
public class UCMatchAsync extends AsyncCall {

    UnparalleledChallengeRaceInfo raceInfo;

    public UCMatchAsync(UnparalleledChallengeRaceInfo raceInfo){
        this.raceInfo = raceInfo;
    }

    public UCMatchAsync(){

    }
    List<UCStage> allStages = new ArrayList<>();
    Map<Long, UnparalleledChallengeRecordInfo> recordInfos = new HashMap<>();

    long minFightPowerDiff = Long.MAX_VALUE;
    long betAtkPlayerId;
    long betDefPlayerId;

    @Override
    public void asyncExecute() {
        if(raceInfo.getPhase() == UCPhaseType.group){
            //小组赛战斗，需要组内打(小组赛不需要竞猜)
            for (UnparalleledChallengeGroupInfo unparalleledChallengeGroupInfo : raceInfo.getCurPhaseGroupInfo()) {
                //组内打出一个最厉害的
                List<Long> playerInfos = unparalleledChallengeGroupInfo.getPlayerIds();
                Map<Long, MiniGamePlayer> miniPlayersForMap = PlayerHelper.getMiniPlayersForMap(playerInfos);
                if(CollectionUtil.isNotEmpty(playerInfos) && playerInfos.size() > 1){
                    for (int i = 0; i < playerInfos.size(); i++) {
                        UnparalleledChallengePlayerInfo player1 = raceInfo.getPlayerInfo(playerInfos.get(i));
                        MiniPlayer miniPlayer1 = miniPlayersForMap.get(player1.getPlayerId());
                        for (int j = i + 1; j < playerInfos.size(); j++) {
                            UnparalleledChallengePlayerInfo player2 = raceInfo.getPlayerInfo(playerInfos.get(j));
                            MiniPlayer miniPlayer2 = miniPlayersForMap.get(player2.getPlayerId());
                            long allocateId = raceInfo.getAllocateId();
                            if(getPlayerFightPower(player1, miniPlayer1) < getPlayerFightPower(player2, miniPlayer2)){
                                allStages.add(new UCStage(raceInfo, allocateId, player1 ,player2, miniPlayer1, miniPlayer2));
                            }else{
                                allStages.add(new UCStage(raceInfo, allocateId, player2, player1, miniPlayer2, miniPlayer1));
                            }
                            UnparalleledChallengeRecordInfo recordInfo = UnparalleledChallengeRecordInfo.newRecordInfo(allocateId, raceInfo.getPhase());
                            recordInfos.put(recordInfo.getId(), recordInfo);
                        }
                    }
                }
            }
            minFightPowerDiff = -1;
        }else{
            //非小组赛，按组内相邻打架
            for (UnparalleledChallengeGroupInfo unparalleledChallengeGroupInfo : raceInfo.getCurPhaseGroupInfo()) {
                UnparalleledChallengePlayerInfo player1 = null;
                List<Long> playerIds = unparalleledChallengeGroupInfo.getPlayerIds();
                Map<Long, MiniGamePlayer> miniPlayersForMap = PlayerHelper.getMiniPlayersForMap(playerIds);
                for (long playerId : playerIds) {
                    UnparalleledChallengePlayerInfo player2 = raceInfo.getPlayerInfo(playerId);
                    //过滤输掉的玩家
                    if(player2.getLoseTimes(raceInfo.getPhase().getShowPhase()) > 0){
                        continue;
                    }
                    if(Objects.isNull(player1)){
                        player1 = player2;
                    }else{
                        //组队
                        long allocateId = raceInfo.getAllocateId();
                        MiniPlayer miniPlayer1 = miniPlayersForMap.get(player1.getPlayerId());
                        MiniPlayer miniPlayer2 = miniPlayersForMap.get(player2.getPlayerId());
                        if(getPlayerFightPower(player1, miniPlayer1) < getPlayerFightPower(player2, miniPlayer2)){
                            allStages.add(new UCStage(raceInfo, allocateId, player1 ,player2, miniPlayer1, miniPlayer2));
                        }else{
                            allStages.add(new UCStage(raceInfo, allocateId, player2 ,player1, miniPlayer2, miniPlayer1));
//                            tryBecomeBet(player2, player1, miniPlayer2, miniPlayer1);
                        }
                        tryBecomeBet(player1, player2, miniPlayer1, miniPlayer2);
                        UnparalleledChallengeRecordInfo recordInfo = UnparalleledChallengeRecordInfo.newRecordInfo(allocateId, raceInfo.getPhase());
                        recordInfos.put(recordInfo.getId(), recordInfo);
                        player1 = null;
                    }
                }
            }
        }

        GameLogger.ucInfo(raceInfo.getRaceId(), "uc match finish : " + raceInfo.getPhase());
    }

    private void tryBecomeBet(UnparalleledChallengePlayerInfo atkPlayerInfo, UnparalleledChallengePlayerInfo defPlayerInfo
        , MiniPlayer atkMiniPlayer, MiniPlayer defMiniPlayer){
        long fightPowerDiff = Math.abs(getPlayerFightPower(atkPlayerInfo, atkMiniPlayer) - getPlayerFightPower(defPlayerInfo, defMiniPlayer));
        if(fightPowerDiff < minFightPowerDiff){
            minFightPowerDiff = fightPowerDiff;
            betAtkPlayerId = atkPlayerInfo.getPlayerId();
            betDefPlayerId = defPlayerInfo.getPlayerId();
        }
    }

    private long getPlayerFightPower(UnparalleledChallengePlayerInfo playerInfo, MiniPlayer miniPlayer){
        return Objects.isNull(miniPlayer) ? playerInfo.getFightPower() : miniPlayer.getFightingPower();
    }


    @Override
    public void execute() {
        if(minFightPowerDiff < Long.MAX_VALUE){
            //构建竞猜信息
            if(betAtkPlayerId > 0){
                UCPhaseType phase = raceInfo.getPhase();
                UCBetInfo ucBetInfo = UCBetInfo.newInfo(phase, betAtkPlayerId, betDefPlayerId);
                raceInfo.getBetInfos().put(phase, ucBetInfo);
            }
            //增加战斗记录信息
            raceInfo.getRecordInfos().clear();
            raceInfo.getRecordInfos().putAll(recordInfos);
            //开始战斗
            for (UCStage allStage : allStages) {
                WorldUCCombatManager.add(allStage);
            }
            GameLogger.ucInfo(raceInfo.getRaceId(), "uc add to combat, size : " + allStages.size());
        }
    }
}
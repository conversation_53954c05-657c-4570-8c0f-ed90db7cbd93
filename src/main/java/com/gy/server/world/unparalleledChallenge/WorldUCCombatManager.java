package com.gy.server.world.unparalleledChallenge;

import com.gy.server.core.ElapsedTimeStatistics;
import com.gy.server.core.Launcher;
import com.gy.server.core.MainThread;
import com.gy.server.core.log.SystemLogger;
import com.gy.server.game.combat.CombatManager;
import com.gy.server.game.combat.Stage;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * 战斗管理器
 * @author: gbk
 * @date: 2024-12-09 16:40
 */
public class WorldUCCombatManager {

    //战斗忙绿休眠频率
    private static final long busy_rate = 100L;
    //现成每轮休眠频率
    private static final long thread_sleep_rate = 20L;
    //最大处理战斗数量
    private static final long max_stage = 100L;
    private static final BlockingQueue<Stage> stages = new LinkedBlockingQueue<>();
    private static volatile boolean running = true;

    private WorldUCCombatManager() {
    }

    public static void startup() {
        Launcher.launchStartupInfo("WorldUCCombatManager start");
        ThreadPool.execute(WorldUCCombatManager::run);
    }

    private static void run() {
        while (MainThread.isRunning()) {
            try {
                if (!stages.isEmpty()) {
                    long totalStart = System.nanoTime();

                    Stage stage;
                    while ((stage = stages.poll()) != null) {
                        while (CombatManager.getStageSize() >= max_stage) {
                            Thread.sleep(busy_rate);
                        }
                        stage.init();
                        CombatManager.combatStart(stage);
                    }

                    // 使用Runner日志
                    ElapsedTimeStatistics.addElapsedNanoTimeWithoutMinNano(ElapsedTimeStatistics.Type.Runner, "WorldUCCombatManager", System.nanoTime() - totalStart);
                }
                Thread.sleep(thread_sleep_rate);
            } catch (Exception e) {
                SystemLogger.error("WorldUCCombatManager error: " + e);
            }
        }

        running = false;
        Launcher.launchShutdownInfo("WorldUCCombatManager shutdown....");
    }

    public static void add(Stage stage) {
        stages.offer(stage);
    }

    public static boolean isRunning() {
        return running;
    }

    public static int queueSize() {
        return stages.size();
    }

    public static BlockingQueue<Stage> getStages(){
        return stages;
    }

}
package com.gy.server.world.unparalleledChallenge.bean;

import com.gy.server.core.db.AbsDBSaveExecutor;
import com.gy.server.packet.PbLeague;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.world.unparalleledChallenge.enums.UCPhaseType;
import com.gy.server.world.unparalleledChallenge.enums.UCShowPhaseType;
import com.ttlike.server.tl.baselib.serialize.uc.UnparalleledChallengeGroupInfoDb;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 群龙聚首-小组信息-共64组
 * @author: gbk
 * @date: 2024-12-09 13:32
 */
public class UnparalleledChallengeGroupInfo extends AbsDBSaveExecutor<UnparalleledChallengeGroupInfoDb> {

    //组id（赛区内唯一）
    private int groupId;
    //组内玩家列表
    private List<Long> playerIds = new CopyOnWriteArrayList<>();

    public UnparalleledChallengeGroupInfo(){}
    public UnparalleledChallengeGroupInfo(int groupId){
        this.groupId = groupId;
    }

    public int getGroupId() {
        return groupId;
    }

    public void setGroupId(int groupId) {
        this.groupId = groupId;
    }

    public List<Long> getPlayerIds() {
        return playerIds;
    }

    public void setPlayerIds(List<Long> playerIds) {
        this.playerIds = playerIds;
    }

    //获取组内最强者
    public long getStrongest(UnparalleledChallengeRaceInfo raceInfo){
        UCShowPhaseType showPhase = raceInfo.getPhase().getShowPhase();
        List<Long> sortPLayerIds = new ArrayList<>(playerIds);
        if(CollectionUtil.isNotEmpty(sortPLayerIds)){
            Collections.sort(sortPLayerIds, (o1, o2) -> {
                UnparalleledChallengePlayerInfo playerInfo1 = raceInfo.getPlayerInfo(o1);
                UnparalleledChallengePlayerInfo playerInfo2 = raceInfo.getPlayerInfo(o2);
                if(playerInfo2.getWinTimes(showPhase) == playerInfo1.getWinTimes(showPhase)){
                    return (int)(playerInfo2.getFightPower() - playerInfo1.getFightPower());
                }
                return playerInfo2.getWinTimes(showPhase) - playerInfo1.getWinTimes(showPhase);
            });
            return sortPLayerIds.get(0);
        }else{
            return -1;
        }
    }

    public PbLeague.UnparalleledChallengeGroupInfo genPb(UCShowPhaseType showPhaseType, UnparalleledChallengeRaceInfo raceInfo){
        PbLeague.UnparalleledChallengeGroupInfo.Builder builder = PbLeague.UnparalleledChallengeGroupInfo.newBuilder();
        builder.setGroupId(groupId);
        for (Long playerId : playerIds) {
            UnparalleledChallengePlayerInfo playerInfo = raceInfo.getPlayerInfos().get(playerId);
            builder.addUnitInfos(playerInfo.genPb(showPhaseType, raceInfo));
        }
        return builder.build();
    }

    @Override
    public void readFromDB(UnparalleledChallengeGroupInfoDb unparalleledChallengeGroupInfoDb) {
        this.groupId = unparalleledChallengeGroupInfoDb.getGroupId();
        this.playerIds.addAll(unparalleledChallengeGroupInfoDb.getPlayerIds());
    }

    @Override
    public UnparalleledChallengeGroupInfoDb writeToDB() {
        UnparalleledChallengeGroupInfoDb infoDb = new UnparalleledChallengeGroupInfoDb();
        infoDb.setGroupId(this.groupId);
        infoDb.getPlayerIds().addAll(this.playerIds);
        return infoDb;
    }
}
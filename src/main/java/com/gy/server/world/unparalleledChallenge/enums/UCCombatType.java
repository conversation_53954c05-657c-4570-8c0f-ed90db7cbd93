package com.gy.server.world.unparalleledChallenge.enums;

import com.gy.server.world.unparalleledChallenge.bean.UnparalleledChallengeRaceInfo;
import com.gy.server.world.unparalleledChallenge.status.AbsUCCombatStatus;
import com.gy.server.world.unparalleledChallenge.status.impl.*;

/**
 * 群龙聚首战斗状态
 * @author: gbk
 * @date: 2024-12-03 13:44
 */
public enum UCCombatType {

    //等待阶段
    wait(new Status1WaitHandler()),
    //布阵锁定
    lineupLock(new Status2LineupLockHandler()),
    //战斗
    combat(new Status3CombatHandler()),
    //战斗后处理
    combatAfter(new Status4CombatAfterHandler()),
    //奖励
    reward(new Status5RewardHandler()),

    ;
    AbsUCCombatStatus status;
    UCCombatType(AbsUCCombatStatus status){
        this.status = status;
    }

    public void tick(UnparalleledChallengeRaceInfo raceInfo){
        if(!raceInfo.isChangeCombatTypeMark()){
            status.doRightNow(raceInfo);
            raceInfo.setChangeCombatTypeMark(true);
        }else{
            status.tick(raceInfo);
        }
    }

    public int getType(){
        return this.ordinal() + 1;
    }

}
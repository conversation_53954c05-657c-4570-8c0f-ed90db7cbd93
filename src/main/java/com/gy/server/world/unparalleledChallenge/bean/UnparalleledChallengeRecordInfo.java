package com.gy.server.world.unparalleledChallenge.bean;

import com.gy.server.core.db.AbsDBSaveExecutor;
import com.gy.server.world.unparalleledChallenge.enums.UCPhaseType;
import com.ttlike.server.tl.baselib.serialize.uc.UnparalleledChallengeRecordInfoDb;

/**
 * 战斗记录信息,主要记录战斗是否结束
 * @author: gbk
 * @date: 2024-12-10 17:50
 */
public class UnparalleledChallengeRecordInfo extends AbsDBSaveExecutor<UnparalleledChallengeRecordInfoDb> {

    //唯一id
    private long id;
    //阶段
    private UCPhaseType phase;
    //战斗结束
    private boolean isFinish;
    //战报id
    @Deprecated
    private long recordId;

    public static UnparalleledChallengeRecordInfo newRecordInfo(long id, UCPhaseType phase){
        UnparalleledChallengeRecordInfo recordInfo = new UnparalleledChallengeRecordInfo();
        recordInfo.setId(id);
        recordInfo.setPhase(phase);
        return recordInfo;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public UCPhaseType getPhase() {
        return phase;
    }

    public void setPhase(UCPhaseType phase) {
        this.phase = phase;
    }

    public boolean isFinish() {
        return isFinish;
    }

    public void setFinish(boolean finish) {
        isFinish = finish;
    }

    public long getRecordId() {
        return recordId;
    }

    public void setRecordId(long recordId) {
        this.recordId = recordId;
    }

    @Override
    public void readFromDB(UnparalleledChallengeRecordInfoDb unparalleledChallengeRecordInfoDb) {
        this.id = unparalleledChallengeRecordInfoDb.getId();
        this.phase = UCPhaseType.valueOf(unparalleledChallengeRecordInfoDb.getPhase());
        this.isFinish = unparalleledChallengeRecordInfoDb.isFinish();
    }

    @Override
    public UnparalleledChallengeRecordInfoDb writeToDB() {
        UnparalleledChallengeRecordInfoDb infoDb = new UnparalleledChallengeRecordInfoDb();
        infoDb.setId(this.id);
        infoDb.setPhase(this.phase.name());
        infoDb.setFinish(this.isFinish);
        return infoDb;
    }
}
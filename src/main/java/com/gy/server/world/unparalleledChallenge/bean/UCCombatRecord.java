package com.gy.server.world.unparalleledChallenge.bean;

import com.gy.server.game.record.Record;
import com.gy.server.game.record.RecordHelper;
import com.gy.server.game.record.RecordType;
import com.gy.server.packet.PbCombat;
import com.gy.server.packet.PbRecord;
import com.ttlike.server.tl.baselib.serialize.record.CombatSectionDetailDataDb;
import com.ttlike.server.tl.baselib.serialize.record.CombatSectionUnitDataDb;
import com.ttlike.server.tl.baselib.serialize.record.RecordDb;
import com.ttlike.server.tl.baselib.serialize.record.UCCombatRecordDb;

import java.util.*;

/**
 * 群龙聚首战报
 * @author: gbk
 * @date: 2024-12-16 14:32
 */
public class UCCombatRecord extends Record {

    private PbRecord.RecordPlayer atkUser;//进攻者信息
    private PbRecord.RecordPlayer defUser;//对手信息
    List<CombatSectionDetailDataDb> detailDataDbs = new ArrayList<>();

    public UCCombatRecord(){}

    public UCCombatRecord(String cosKey, boolean isWin, RecordType type, long combatRecordId,
                          PbRecord.RecordPlayer atkRecordPlayer,
                          PbRecord.RecordPlayer defRecordPlayer
            ,  List<CombatSectionDetailDataDb> detailDataDbs) {
        super(type, isWin, combatRecordId, cosKey);
        this.atkUser = atkRecordPlayer;
        this.defUser = defRecordPlayer;
        this.detailDataDbs = detailDataDbs;
    }

    @Override
    protected void subReadFromPb(PbRecord.Record record) {
        //不需要
    }

    @Override
    protected void subWriteToPb(PbRecord.Record.Builder builder) {
        builder.setUcRecord(buildPb());
    }

    public PbRecord.UnparalleledChallengeRecord buildPb(){
        PbRecord.UnparalleledChallengeRecord .Builder builder = PbRecord.UnparalleledChallengeRecord .newBuilder();
        builder.setAtk(atkUser);
        if(Objects.nonNull(defUser)){
            builder.setDef(defUser);
        }
        PbCombat.CombatSectionDetailData.Builder detailBuilder = PbCombat.CombatSectionDetailData.newBuilder();
        for (CombatSectionDetailDataDb detailDataDb : detailDataDbs) {
            detailBuilder.clear();
            fillRecordUnitInfos(detailBuilder, true, detailDataDb.getAtkUnitDatas());
            fillRecordUnitInfos(detailBuilder, false, detailDataDb.getDefUnitDatas());
            builder.addSectionDetailDataList(detailBuilder.build());
        }
        return builder.build();
    }

    public void fillRecordUnitInfos(PbCombat.CombatSectionDetailData.Builder detailBuilder, boolean atk,
                                    List<CombatSectionUnitDataDb> unitDatas){
        List<PbCombat.CombatSectionUnitData> unitDataList = new ArrayList<>();
        PbCombat.CombatSectionUnitData.Builder builder = PbCombat.CombatSectionUnitData.newBuilder();
        for (CombatSectionUnitDataDb unitData : unitDatas) {
            builder.clear();
            builder.setId(unitData.getId());
            builder.setTid(unitData.getTid());
            builder.setCurHp(unitData.getCurHp());
            unitDataList.add(builder.build());
        }
        if(atk){
            detailBuilder.addAllAtkUnitDatas(unitDataList);
        }else{
            detailBuilder.addAllDefUnitDatas(unitDataList);
        }
    }

    @Override
    protected Set<PbRecord.RecordPlayer> getDetailRecordSet() {
        return Collections.emptySet();
    }

//    public UCCombatRecord(UCCombatRecordDb db) {
//        super(db.getType(), db.getTime(), db.isWin(), db.getCombatRecordId(), db.getServerId());
//        this.atkUser = RecordHelper.getInstance().genRecordPlayerPb(db.getAtkUser());
//        this.defUser = RecordHelper.getInstance().genRecordPlayerPb(db.getDefUser());
//        this.winScores.addAll(db.getWinScores());
//    }

    public UCCombatRecordDb genUCCombatRecordDb() {
        UCCombatRecordDb db = new UCCombatRecordDb();
        db.setAtkUser(RecordHelper.getInstance().genRecordPlayerDb(atkUser));
        db.setDefUser(RecordHelper.getInstance().genRecordPlayerDb(defUser));
        db.setDetailDataDbs(detailDataDbs);
        return db;
    }

    @Override
    public void readFromDb2(RecordDb db) {
        UCCombatRecordDb bean = db.getUcCombatRecordDb();
        if(bean != null){
            this.atkUser = RecordHelper.getInstance().genRecordPlayerPb(bean.getAtkUser());
            this.defUser = RecordHelper.getInstance().genRecordPlayerPb(bean.getDefUser());
            this.detailDataDbs = new ArrayList<>(bean.getDetailDataDbs());
        }
    }

    @Override
    public void writeToDb(RecordDb db) {
        db.setUcCombatRecordDb(genUCCombatRecordDb());
    }
}
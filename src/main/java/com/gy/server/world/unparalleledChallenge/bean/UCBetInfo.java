package com.gy.server.world.unparalleledChallenge.bean;

import com.gy.server.core.db.AbsDBSaveExecutor;
import com.gy.server.packet.PbLeague;
import com.gy.server.world.unparalleledChallenge.enums.UCPhaseType;
import com.ttlike.server.tl.baselib.serialize.uc.UCBetInfoDb;

import java.util.HashSet;
import java.util.Set;

/**
 * 群龙聚首-竞猜信息
 * @author: gbk
 * @date: 2024-12-16 17:40
 */
public class UCBetInfo extends AbsDBSaveExecutor<UCBetInfoDb> {

    //阶段
    UCPhaseType phaseType;
    //进攻者(左边)
    long atkPlayerId;
    //防守者（右边）
    long defPlayerId;
    //支持进攻方玩家id
    Set<Long> supportAtkPlayerIds = new HashSet<>();
    //支持防守方玩家id
    Set<Long> supportDefPlayerIds = new HashSet<>();
    //胜利玩家id
    public long winPlayerId;
    //战报id
    long recordId;

    public PbLeague.UnparalleledChallengeBetInfo genPb(UCPhaseType phaseType, UnparalleledChallengeRaceInfo raceInfo){
        PbLeague.UnparalleledChallengeBetInfo.Builder betInfo = PbLeague.UnparalleledChallengeBetInfo.newBuilder();
        betInfo.setPhase(phaseType.getId());
        betInfo.setBeforePlayerInfo(raceInfo.getPlayerInfo(atkPlayerId).genPb(phaseType.getShowPhase(), raceInfo));
        betInfo.setAfterPlayerInfo(raceInfo.getPlayerInfo(defPlayerId).genPb(phaseType.getShowPhase(), raceInfo));
        betInfo.addAllGuessBeforePlayerIds(supportAtkPlayerIds);
        betInfo.addAllGuessAfterPlayerIds(supportDefPlayerIds);
        betInfo.setRecordId(winPlayerId > 0 ? recordId : 0);
        betInfo.setWinPlayerId(winPlayerId);
        return betInfo.build();
    }

    public UCBetInfo(){}
    public UCBetInfo(UCPhaseType phaseType, long atkPlayerId, long defPlayerId){
        this.phaseType = phaseType;
        this.atkPlayerId = atkPlayerId;
        this.defPlayerId = defPlayerId;
    }

    public static UCBetInfo newInfo(UCPhaseType phaseType, long atkPlayerId, long defPlayerId){
        return new UCBetInfo(phaseType, atkPlayerId, defPlayerId);
    }

    public UCPhaseType getPhaseType() {
        return phaseType;
    }

    public void setPhaseType(UCPhaseType phaseType) {
        this.phaseType = phaseType;
    }

    public long getAtkPlayerId() {
        return atkPlayerId;
    }

    public void setAtkPlayerId(long atkPlayerId) {
        this.atkPlayerId = atkPlayerId;
    }

    public long getDefPlayerId() {
        return defPlayerId;
    }

    public void setDefPlayerId(long defPlayerId) {
        this.defPlayerId = defPlayerId;
    }

    public Set<Long> getSupportAtkPlayerIds() {
        return supportAtkPlayerIds;
    }

    public void setSupportAtkPlayerIds(Set<Long> supportAtkPlayerIds) {
        this.supportAtkPlayerIds = supportAtkPlayerIds;
    }

    public Set<Long> getSupportDefPlayerIds() {
        return supportDefPlayerIds;
    }

    public void setSupportDefPlayerIds(Set<Long> supportDefPlayerIds) {
        this.supportDefPlayerIds = supportDefPlayerIds;
    }

    public boolean isAtkWin() {
        return winPlayerId == atkPlayerId;
    }

    public long getWinPlayerId() {
        return winPlayerId;
    }

    public void setWinPlayerId(long winPlayerId) {
        this.winPlayerId = winPlayerId;
    }

    public long getRecordId() {
        return recordId;
    }

    public void setRecordId(long recordId) {
        this.recordId = recordId;
    }

    @Override
    public void readFromDB(UCBetInfoDb ucBetInfoDb) {
        this.phaseType = UCPhaseType.valueOf(ucBetInfoDb.getPhaseType());
        this.atkPlayerId = ucBetInfoDb.getAtkPlayerId();
        this.defPlayerId = ucBetInfoDb.getDefPlayerId();
        this.supportAtkPlayerIds.addAll(ucBetInfoDb.getSupportAtkPlayerIds());
        this.supportDefPlayerIds.addAll(ucBetInfoDb.getSupportDefPlayerIds());
        this.recordId = ucBetInfoDb.getRecordId();
        this.winPlayerId = ucBetInfoDb.getWinPlayerId();
    }

    @Override
    public UCBetInfoDb writeToDB() {
        UCBetInfoDb infoDB = new UCBetInfoDb();
        infoDB.setPhaseType(this.phaseType.name());
        infoDB.setAtkPlayerId(atkPlayerId);
        infoDB.setDefPlayerId(defPlayerId);
        infoDB.setSupportAtkPlayerIds(this.supportAtkPlayerIds);
        infoDB.setSupportDefPlayerIds(this.supportDefPlayerIds);
        infoDB.setRecordId(recordId);
        infoDB.setWinPlayerId(winPlayerId);
        return infoDB;
    }
}
package com.gy.server.world.unparalleledChallenge;

import com.gy.server.annotation.MessageMethod;
import com.gy.server.annotation.MessageServiceBean;
import com.gy.server.core.MessageServerType;
import com.gy.server.core.MethodInvokeType;
import com.gy.server.core.command.CommandRequestParams;
import com.gy.server.core.command.PlayerCommandRequest;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.mountainRiverTournament.MountainRiverTournamentService;
import com.gy.server.game.mountainRiverTournament.template.MRTConstant;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbLeague;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.world.mountainRiverTournament.WorldMRTGlobalData;
import com.gy.server.world.unparalleledChallenge.bean.UCBetInfo;
import com.gy.server.world.unparalleledChallenge.bean.UnparalleledChallengeGroupInfo;
import com.gy.server.world.unparalleledChallenge.bean.UnparalleledChallengeRaceInfo;
import com.gy.server.world.unparalleledChallenge.enums.UCPhaseType;
import com.gy.server.world.unparalleledChallenge.enums.UCShowPhaseType;
import com.ttlike.server.tl.baselib.serialize.warZone.WarZoneInfo;

import java.util.*;

/**
 * 群龙聚首
 * @author: gbk
 * @date: 2024-12-18 09:35
 */
@MessageServiceBean(description = "群龙聚首-world", messageServerType = MessageServerType.world)
public class WorldUCCommandService {

    @MessageMethod(description = "主界面", invokeType = MethodInvokeType.sync)
    private static void getMainInfo(PlayerCommandRequest request, CommandRequestParams params) {
        PbProtocol.UnparalleledChallengeMainInfoRst.Builder rst = PbProtocol.UnparalleledChallengeMainInfoRst.newBuilder().setResult(Text.genOkServerRstInfo());

        long playerId = request.getPlayerId();
        int serverId = request.getServerId();

        WorldUnparalleledChallengeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.unparalleledChallenge);
        logic:{
            if(!globalData.isOpen() || CollectionUtil.isEmpty(globalData.getRaceInfos())){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            int raceId = selectRaceId(globalData, serverId);
            UnparalleledChallengeRaceInfo raceInfo = globalData.getRaceInfos().get(raceId);
            PbLeague.UnparalleledChallengeMainInfo.Builder mainInfo = PbLeague.UnparalleledChallengeMainInfo.newBuilder();
            mainInfo.setSeasonId(globalData.getSeasonId());
            mainInfo.setPhase(raceInfo.getPhase().getId());
            Map<UCShowPhaseType, List<UnparalleledChallengeGroupInfo>> groupInfos = raceInfo.getGroupInfos();
            //小组赛信息
            UCShowPhaseType showPhaseType = UCShowPhaseType.group;
            if(groupInfos.containsKey(showPhaseType)){
                fillGroupInfo(mainInfo, groupInfos.get(showPhaseType),
                        raceInfo, playerId, raceId);
            }
            showPhaseType = UCShowPhaseType.advance;
            if(groupInfos.containsKey(showPhaseType)){
                for (UnparalleledChallengeGroupInfo groupInfo : groupInfos.get(showPhaseType)) {
                    mainInfo.addAdvanceInfos(groupInfo.genPb(showPhaseType, raceInfo));
                }
            }

            showPhaseType = UCShowPhaseType.champion;
            if(groupInfos.containsKey(showPhaseType)){
                for (UnparalleledChallengeGroupInfo groupInfo : groupInfos.get(showPhaseType)) {
                    mainInfo.addFinalInfo(groupInfo.genPb(showPhaseType, raceInfo));
                }
            }
            mainInfo.setHasReceiveBet(globalData.getHadReceiveBetRewardPlayerIds().contains(playerId));
            mainInfo.setRaceId(raceInfo.getRaceId());
            mainInfo.setCombatPhase(raceInfo.getCombatType().getType());
            mainInfo.setTickTime(raceInfo.getTickTime());
            mainInfo.setLastLineupTime(raceInfo.getLastLineupTime());
            //竞猜信息
            UCPhaseType nowPhase = raceInfo.getPhase();
            if(nowPhase != UCPhaseType.group){
                UCBetInfo ucBetInfo = raceInfo.getBetInfos().get(nowPhase);
                if(Objects.nonNull(ucBetInfo)){
                    mainInfo.addBetInfos(ucBetInfo.genPb(nowPhase, raceInfo));
                }else{
                    UCPhaseType lastPhase = UCPhaseType.of(nowPhase.getId() - 1);
                    ucBetInfo = raceInfo.getBetInfos().get(lastPhase);
                    if(Objects.nonNull(ucBetInfo)){
                        mainInfo.addBetInfos(ucBetInfo.genPb(lastPhase, raceInfo));
                    }
                }

            }

            rst.setMainInfo(mainInfo.build());
        }
        request.addCallbackParam(rst.build().toByteArray());
    }

    private static void fillGroupInfo(PbLeague.UnparalleledChallengeMainInfo.Builder mainInfo,
                                      List<UnparalleledChallengeGroupInfo> groupInfos,
                                      UnparalleledChallengeRaceInfo raceInfo, long playerId, int serverId){
        WorldUnparalleledChallengeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.unparalleledChallenge);
        WarZoneInfo.WarZoneSingleInfo singleInfo = globalData.getWarZoneInfo().getInfoByServerId(serverId);
        //获取自己小组
        for (UnparalleledChallengeGroupInfo groupInfo : groupInfos) {
            if(groupInfo.getPlayerIds().contains(playerId)){
                mainInfo.addGroupInfos(groupInfo.genPb(UCShowPhaseType.group, raceInfo));
                return;
            }
        }

        if(globalData.getMrtRankInfos().containsKey(singleInfo.getWarZoneId())){
            //获取本战区第一小组
            long nowStrongPlayerId = globalData.getMrtRankInfos().get(singleInfo.getWarZoneId()).get(0);
            for (UnparalleledChallengeGroupInfo groupInfo : groupInfos) {
                if(groupInfo.getPlayerIds().contains(nowStrongPlayerId)){
                    mainInfo.addGroupInfos(groupInfo.genPb(UCShowPhaseType.group, raceInfo));
                    return;
                }
            }
        }else{
            //获取第一小组
            mainInfo.addGroupInfos(groupInfos.get(0).genPb(UCShowPhaseType.group, raceInfo));
        }
    }

    /**
     * 获取赛区id，优先获取本赛区id，如果本赛区不参加获取第一赛区
     */
    private static int selectRaceId(WorldUnparalleledChallengeGlobalData globalData, int serverId){
        WarZoneInfo warZoneInfo = globalData.getWarZoneInfo();
        WarZoneInfo.WarZoneSingleInfo singleInfo = warZoneInfo.getInfoByServerId(serverId);
        if(Objects.nonNull(singleInfo)){
            if(globalData.getRaceInfos().containsKey(singleInfo.getRaceId())){
                //本赛区
                return singleInfo.getRaceId();
            }
        }
        //第一赛区
        return getMinId(globalData.getRaceInfos().keySet());
    }

    private static int getMinId(Collection<Integer> ids){
        return Collections.min(ids);
    }

    @MessageMethod(description = "竞猜信息", invokeType = MethodInvokeType.sync)
    private static void betInfo(PlayerCommandRequest request, CommandRequestParams params) {
        PbProtocol.UnparalleledChallengeBetInfoRst.Builder rst = PbProtocol.UnparalleledChallengeBetInfoRst.newBuilder().setResult(Text.genOkServerRstInfo());
        long playerId = request.getPlayerId();
        int serverId = request.getServerId();
        WorldUnparalleledChallengeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.unparalleledChallenge);
        int raceId = selectRaceId(globalData, serverId);
        UnparalleledChallengeRaceInfo raceInfo = globalData.getRaceInfos().get(raceId);
        for (UCBetInfo betInfo : raceInfo.getBetInfos().values()) {
            if(betInfo.getSupportAtkPlayerIds().contains(playerId) || betInfo.getSupportDefPlayerIds().contains(playerId)){
                rst.addBetInfos(betInfo.genPb(raceInfo.getPhase(), raceInfo));
            }
        }
        request.addCallbackParam(rst.build().toByteArray());
    }

    @MessageMethod(description = "竞猜", invokeType = MethodInvokeType.sync)
    private static void bet(PlayerCommandRequest request, CommandRequestParams params) {
        PbProtocol.UnparalleledChallengeBetRst.Builder rst = PbProtocol.UnparalleledChallengeBetRst.newBuilder().setResult(Text.genOkServerRstInfo());
        boolean isBefore = params.getParam(0);
        logic:{
            long playerId = request.getPlayerId();
            int serverId = request.getServerId();
            WorldUnparalleledChallengeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.unparalleledChallenge);
            int raceId = selectRaceId(globalData, serverId);
            UnparalleledChallengeRaceInfo raceInfo = globalData.getRaceInfos().get(raceId);
            Map<UCPhaseType, UCBetInfo> betInfos = raceInfo.getBetInfos();
            if(!betInfos.containsKey(raceInfo.getPhase())){
                rst.setResult(Text.genServerRstInfo(Text.群龙聚首_竞猜失败));
                break logic;
            }
            UCBetInfo ucBetInfo = betInfos.get(raceInfo.getPhase());
            if(ucBetInfo.getSupportDefPlayerIds().contains(playerId)
                || ucBetInfo.getSupportAtkPlayerIds().contains(playerId)){
                rst.setResult(Text.genServerRstInfo(Text.群龙聚首_不能重复竞猜));
                break logic;
            }
            //竞猜
            if(isBefore){
                ucBetInfo.getSupportAtkPlayerIds().add(playerId);
            }else{
                ucBetInfo.getSupportDefPlayerIds().add(playerId);
            }

            rst.setBetInfo(ucBetInfo.genPb(raceInfo.getPhase(), raceInfo));
        }

        request.addCallbackParam(rst.build().toByteArray());
    }

    @MessageMethod(description = "领取竞猜全中奖励", invokeType = MethodInvokeType.sync)
    private static void receiveBetReward(PlayerCommandRequest request, CommandRequestParams params) {
        PbProtocol.UnparalleledChallengeReceiveBetRewardRst.Builder rst = PbProtocol.UnparalleledChallengeReceiveBetRewardRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:
        {
            long playerId = request.getPlayerId();
            int serverId = request.getServerId();
            WorldUnparalleledChallengeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.unparalleledChallenge);
            int raceId = selectRaceId(globalData, serverId);
            UnparalleledChallengeRaceInfo raceInfo = globalData.getRaceInfos().get(raceId);
            if(raceInfo.getPhase() != UCPhaseType.finish){
                rst.setResult(Text.genServerRstInfo(Text.群龙聚首_竞猜未结束不能领取奖励));
                break logic;
            }
            if(globalData.getHadReceiveBetRewardPlayerIds().contains(playerId)){
                rst.setResult(Text.genServerRstInfo(Text.群龙聚首_不能重复领取奖励));
                break logic;
            }
            //全部猜对
            for (UCBetInfo beInfo : raceInfo.getBetInfos().values()) {
                if(beInfo.isAtkWin() && !beInfo.getSupportAtkPlayerIds().contains(playerId)
                    || !beInfo.isAtkWin() && !beInfo.getSupportDefPlayerIds().contains(playerId)){
                    rst.setResult(Text.genServerRstInfo(Text.群龙聚首_未全部猜对竞猜));
                    break logic;
                }
            }
            globalData.getHadReceiveBetRewardPlayerIds().add(playerId);
        }
        request.addCallbackParam(rst.build().toByteArray());
    }

    @MessageMethod(description = "重置", invokeType = MethodInvokeType.sync)
    private static void ucTestReset(ServerCommandRequest request, CommandRequestParams params) {
        WorldMRTGlobalData mrtGlobalData = GlobalDataManager.getData(GlobalDataType.mountainRiverTournament);
        MRTConstant constant = MountainRiverTournamentService.getConstant();
        mrtGlobalData.setPlayEnums(constant.endType);

        WorldUnparalleledChallengeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.unparalleledChallenge);
        globalData.clear();
        /**
         * 群龙聚首调试步骤：
         * 1.清除剑会群雄赛季信息：/gm mrtClear
         * 2.调整需要上榜的玩家段位：/gm mrtAddPoint 9980000001 1002
         * /gm mrtAddPoint 9980000107 1010
         * 3.初始化机器人，并且进入剑会群雄段位榜:/gm ucRobotInit 99 1000
         * 4.清除群龙聚首赛季信息：/gm ucClear
         * 4.挑时间到下周一
         */


        /**
         * open 周一 6点
         *
         * lockedTimes_1	CS	string	18|30	小组赛锁定阵容时间（hh|mm）
         * viewableTime_1	CS	string	20|30	小组赛可查看对战时间（hh|mm）
         * lockedTimes_2	CS	string	5|0	淘汰赛锁定阵容时间（hh|mm）
         * viewableTime_2	CS	string	20|30	淘汰赛可查看对战时间（hh|mm）
         * guessingTime_1	CS	string	6|0	淘汰赛竞猜开始时间（hh|mm）
         * guessingTime_2	CS	string	20|30	淘汰赛竞猜结速时间（hh|mm）
         */

    }

}
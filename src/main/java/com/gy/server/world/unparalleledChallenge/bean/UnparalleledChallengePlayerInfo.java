package com.gy.server.world.unparalleledChallenge.bean;

import com.gy.server.core.db.AbsDBSaveExecutor;
import com.gy.server.game.combat.unit.HeroUnitCreator;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.packet.PbLeague;
import com.gy.server.world.unparalleledChallenge.enums.UCCombatType;
import com.gy.server.world.unparalleledChallenge.enums.UCPhaseType;
import com.gy.server.world.unparalleledChallenge.enums.UCShowPhaseType;
import com.ttlike.server.tl.baselib.serialize.hero.HeroUnitCreatorDb;
import com.ttlike.server.tl.baselib.serialize.hero.HeroUnitCreatorMapDb;
import com.ttlike.server.tl.baselib.serialize.player.MiniPlayer;
import com.ttlike.server.tl.baselib.serialize.uc.UnparalleledChallengePlayerInfoDb;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 群龙聚首-玩家信息
 * @author: gbk
 * @date: 2024-12-09 10:18
 */
public class UnparalleledChallengePlayerInfo extends AbsDBSaveExecutor<UnparalleledChallengePlayerInfoDb> {

    //玩家id
    private long playerId;
    //胜利次数
    private Map<UCShowPhaseType, Integer> winTimesList = new HashMap<>();
    //失败次数
    private Map<UCShowPhaseType, Integer> loseTimesList = new HashMap<>();
    //布阵信息
    List<Map<Integer, HeroUnitCreator>> lineupInfos = new CopyOnWriteArrayList<>();
    //排名
    private int rank;
    //小组赛战报信息
    private List<Long> groupRecordIds = new ArrayList<>();
    //晋级赛战报信息
    private Map<Integer, Long> advanceRecordIds = new HashMap<>();
    /**
     * 临时胜利次数
     */
    private int tempWinTimes;
    private int tempLoseTimes;

    public PbLeague.UnparalleledChallengeUnitInfo genPb(UCShowPhaseType showPhaseType, UnparalleledChallengeRaceInfo raceInfo){
        PbLeague.UnparalleledChallengeUnitInfo.Builder builder = PbLeague.UnparalleledChallengeUnitInfo.newBuilder();
        builder.setWinTimes(getWinTimes(showPhaseType));
        builder.setLostTime(getLoseTimes(showPhaseType));
        builder.setPlayerId(playerId);
        //战斗状态隐藏当前未展示战报
        if(raceInfo.getCombatType() == UCCombatType.combat){
            if(raceInfo.getPhase() == UCPhaseType.group){
                //小组赛 不需要塞战报
            }else{
                Map<Integer, Long> advanceRecordIdsTemp = new HashMap<>(advanceRecordIds);
                advanceRecordIdsTemp.remove(raceInfo.getPhase().getId());
                builder.putAllAdvanceRecordIds(advanceRecordIdsTemp);
                builder.addAllGroupRecordIds(groupRecordIds);
            }
        }else{
            builder.putAllAdvanceRecordIds(advanceRecordIds);
            builder.addAllGroupRecordIds(groupRecordIds);
        }

        if(!isLose()){
            PbLeague.UnparalleledChallengeUnitLineupInfo.Builder lineupInfoBuilder = PbLeague.UnparalleledChallengeUnitLineupInfo.newBuilder();
            for (Map<Integer, HeroUnitCreator> lineupInfo : lineupInfos) {
                lineupInfoBuilder.clear();

                lineupInfo.forEach((k, v) -> lineupInfoBuilder.putHeroUnitCreators(k, v.writeToPb()));
                builder.addLineupInfos(lineupInfoBuilder.build());
            }
        }
        return builder.build();
    }
    
    public boolean isLose(){
        for (Integer times : loseTimesList.values()) {
            if(times > 0){
                return true;
            }
        }
        return false;
    }

    public List<Long> getGroupRecordIds() {
        return groupRecordIds;
    }

    public void setGroupRecordIds(List<Long> groupRecordIds) {
        this.groupRecordIds = groupRecordIds;
    }

    public Map<Integer, Long> getAdvanceRecordIds() {
        return advanceRecordIds;
    }

    public void setAdvanceRecordIds(Map<Integer, Long> advanceRecordIds) {
        this.advanceRecordIds = advanceRecordIds;
    }

    public int getRank() {
        return rank;
    }

    public void setRank(int rank) {
        this.rank = rank;
    }

    public List<Map<Integer, HeroUnitCreator>> getLineupInfos() {
        return lineupInfos;
    }

    public void setLineupInfos(List<Map<Integer, HeroUnitCreator>> lineupInfos) {
        this.lineupInfos = lineupInfos;
    }

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public int getWinTimes(UCShowPhaseType showPhaseType) {
        return winTimesList.getOrDefault(showPhaseType, 0);
    }

    public int getLoseTimes(UCShowPhaseType showPhaseType) {
        return loseTimesList.getOrDefault(showPhaseType, 0);
    }

    public void addWinTimes(){
        tempWinTimes++;
    }

    public void addLoseTimes(){
        tempLoseTimes++;
    }

    public Map<UCShowPhaseType, Integer> getWinTimesList() {
        return winTimesList;
    }

    public void setWinTimesList(Map<UCShowPhaseType, Integer> winTimesList) {
        this.winTimesList = winTimesList;
    }

    public Map<UCShowPhaseType, Integer> getLoseTimesList() {
        return loseTimesList;
    }

    public void setLoseTimesList(Map<UCShowPhaseType, Integer> loseTimesList) {
        this.loseTimesList = loseTimesList;
    }

    public void mergeTimes(UCPhaseType phaseType){
        winTimesList.put(phaseType.getShowPhase(), winTimesList.getOrDefault(phaseType.getShowPhase(), 0) + tempWinTimes);
        loseTimesList.put(phaseType.getShowPhase(), loseTimesList.getOrDefault(phaseType.getShowPhase(), 0) + tempLoseTimes);
        tempWinTimes = 0;
        tempLoseTimes = 0;
    }

    public long getFightPower(){
        long fightPower = 0L;
        for (Map<Integer, HeroUnitCreator> lineupInfo : lineupInfos) {
            for (HeroUnitCreator creator : lineupInfo.values()) {
                fightPower += creator.getFightingPower();
            }
        }
        return fightPower;
    }

    public static UnparalleledChallengePlayerInfo newPlayerInfo(long playerId){
        UnparalleledChallengePlayerInfo playerInfo = new UnparalleledChallengePlayerInfo();
        playerInfo.setPlayerId(playerId);
        return playerInfo;
    }

    public int getTempWinTimes() {
        return tempWinTimes;
    }

    public void setTempWinTimes(int tempWinTimes) {
        this.tempWinTimes = tempWinTimes;
    }

    public int getTempLoseTimes() {
        return tempLoseTimes;
    }

    public void setTempLoseTimes(int tempLoseTimes) {
        this.tempLoseTimes = tempLoseTimes;
    }

    @Override
    public void readFromDB(UnparalleledChallengePlayerInfoDb unparalleledChallengePlayerInfoDb) {
        this.playerId = unparalleledChallengePlayerInfoDb.getPlayerId();
        for (HeroUnitCreatorMapDb lineupInfoMap : unparalleledChallengePlayerInfoDb.getLineupInfos()) {
            Map<Integer, HeroUnitCreator> lineupInfo = new HashMap<>();
            Map<Integer, HeroUnitCreatorDb> lineups = lineupInfoMap.getLineups();
            for (Integer key : lineups.keySet()) {
                lineupInfo.put(key, new HeroUnitCreator(lineups.get(key)));
            }
            this.lineupInfos.add(lineupInfo);
        }
        this.rank = unparalleledChallengePlayerInfoDb.getRank();
        this.groupRecordIds.addAll(unparalleledChallengePlayerInfoDb.getGroupRecordIds());
        this.advanceRecordIds.putAll(unparalleledChallengePlayerInfoDb.getAdvanceRecordIds());
        this.tempWinTimes = unparalleledChallengePlayerInfoDb.getTempWinTimes();
        this.tempLoseTimes = unparalleledChallengePlayerInfoDb.getTempLoseTimes();
        Map<String, Integer> winTimesListDb = unparalleledChallengePlayerInfoDb.getWinTimesList();
        winTimesListDb.forEach((k, v) -> this.winTimesList.put(UCShowPhaseType.valueOf(k), v));
        Map<String, Integer> loseTimesListDb = unparalleledChallengePlayerInfoDb.getLoseTimesList();
        loseTimesListDb.forEach((k, v) -> this.loseTimesList.put(UCShowPhaseType.valueOf(k), v));

    }

    @Override
    public UnparalleledChallengePlayerInfoDb writeToDB() {
        UnparalleledChallengePlayerInfoDb infoDb = new UnparalleledChallengePlayerInfoDb();
        infoDb.setPlayerId(this.playerId);
        for (Map<Integer, HeroUnitCreator> lineupInfoMap : this.lineupInfos) {
            HeroUnitCreatorMapDb mapDb = new HeroUnitCreatorMapDb();
            lineupInfoMap.forEach((k, v) -> mapDb.getLineups().put(k, v.genDb()));
            infoDb.getLineupInfos().add(mapDb);
        }
        infoDb.setRank(rank);
        infoDb.setGroupRecordIds(this.groupRecordIds);
        infoDb.setAdvanceRecordIds(this.advanceRecordIds);
        infoDb.setTempWinTimes(tempWinTimes);
        infoDb.setTempLoseTimes(tempLoseTimes);
        for (UCShowPhaseType ucShowPhaseType : winTimesList.keySet()) {
            infoDb.getWinTimesList().put(ucShowPhaseType.name(), winTimesList.get(ucShowPhaseType));
        }
        for (UCShowPhaseType ucShowPhaseType : loseTimesList.keySet()) {
            infoDb.getLoseTimesList().put(ucShowPhaseType.name(), loseTimesList.get(ucShowPhaseType));
        }
        return infoDb;
    }
}
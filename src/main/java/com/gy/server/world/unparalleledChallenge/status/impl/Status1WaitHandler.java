package com.gy.server.world.unparalleledChallenge.status.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.world.unparalleledChallenge.bean.UnparalleledChallengeRaceInfo;
import com.gy.server.world.unparalleledChallenge.enums.UCCombatType;
import com.gy.server.world.unparalleledChallenge.status.AbsUCCombatStatus;

/**
 * 准备状态
 * @author: gbk
 * @date: 2024-12-09 19:46
 */
public class Status1WaitHandler extends AbsUCCombatStatus {


    @Override
    public void tick(UnparalleledChallengeRaceInfo raceInfo) {
        //等待布阵锁定阶段
        if(raceInfo.getTickTime() > 0 && ServerConstants.getCurrentTimeMillis() >= raceInfo.getTickTime()){
            modifyRaceCombatStatus(raceInfo, UCCombatType.lineupLock);
        }
    }
}
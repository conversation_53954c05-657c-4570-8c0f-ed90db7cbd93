package com.gy.server.world.unparalleledChallenge;

import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.global.GlobalData;
import com.gy.server.game.player.Player;
import com.gy.server.game.warZone.WarZoneHelper;
import com.gy.server.game.warZone.WarZoneTypeEnums;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.gy.server.world.unparalleledChallenge.bean.UCBetInfo;
import com.gy.server.world.unparalleledChallenge.bean.UnparalleledChallengeRaceInfo;
import com.gy.server.world.unparalleledChallenge.enums.UCPhaseType;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.serialize.base.RedisListLongBean;
import com.ttlike.server.tl.baselib.serialize.uc.UnparalleledChallengeRaceInfoDb;
import com.ttlike.server.tl.baselib.serialize.uc.WorldUnparalleledChallengeGlobalDataDb;
import com.ttlike.server.tl.baselib.serialize.warZone.WarZoneInfo;

import java.util.*;

/**
 * @author: gbk
 * @date: 2024-12-03 11:24
 */
public class WorldUnparalleledChallengeGlobalData extends GlobalData {

    //赛季id
    private int seasonId;
    //是否开启
    private boolean isOpen;
    //是否结束
    private boolean isFinish;
    //赛区信息
    private Map<Integer, UnparalleledChallengeRaceInfo> raceInfos = new HashMap<>();
    //记录战区信息
    private WarZoneInfo warZoneInfo;
    //是否领取竞猜奖励
    private Set<Long> hadReceiveBetRewardPlayerIds = new HashSet<>();
    //战区id：玩家排名
    private Map<Integer, List<Long>> mrtRankInfos = new HashMap<>();
    /**
     * 初始化赛季信息
     * @param pointRankInfos
     */
    public void initSeason(Map<Integer, List<Long>> pointRankInfos){
        //检查补发
        checkReissue();
        mrtRankInfos = new HashMap<>(pointRankInfos);
        seasonId++;
        raceInfos.clear();
//        isOpen = true;
        isFinish = false;
        hadReceiveBetRewardPlayerIds.clear();
        warZoneInfo = WarZoneHelper.getWarZoneInfo(WarZoneTypeEnums.UnparalleledChallenge);
        Map<Integer, List<WarZoneInfo.WarZoneSingleInfo>> infoByRace = warZoneInfo.getInfoByRace();
        for (int raceId : infoByRace.keySet()) {
            //赛区内分组信息
            List<Long> allPlayerIds = new ArrayList<>();
            for (WarZoneInfo.WarZoneSingleInfo warZoneSingleInfo : infoByRace.get(raceId)) {
                int warZoneId = warZoneSingleInfo.getWarZoneId();
                List<Long> playerIds = pointRankInfos.get(warZoneId);
                if(CollectionUtil.isNotEmpty(playerIds)){
                    allPlayerIds.addAll(playerIds);
                }
            }
            //构建赛区信息
            UnparalleledChallengeRaceInfo raceInfo = new UnparalleledChallengeRaceInfo(raceId);
            raceInfo.initGroup(allPlayerIds);
            raceInfos.put(raceInfo.getRaceId(), raceInfo);
        }

    }

    public void clear(){
        mrtRankInfos.clear();
        seasonId = 1;
        raceInfos.clear();
        isOpen = false;
        isFinish = false;
        hadReceiveBetRewardPlayerIds.clear();
        warZoneInfo = WarZoneHelper.getWarZoneInfo(WarZoneTypeEnums.UnparalleledChallenge);
        raceInfos.clear();
        mrtRankInfos.clear();
    }

    /**
     * 检查补发
     */
    private void checkReissue(){
        //检查竞猜奖励补发
        Map<Integer, List<Long>> needReissuePlayerIds = new HashMap<>();
        for (UnparalleledChallengeRaceInfo raceInfo : getRaceInfos().values()) {
            //全部猜对的玩家id
            List<Long> guessSuccessPlayerIds = null;
            for (UCBetInfo ucBetInfo : raceInfo.getBetInfos().values()) {
                Set<Long> successPlayerIds = ucBetInfo.isAtkWin() ? ucBetInfo.getSupportAtkPlayerIds() : ucBetInfo.getSupportDefPlayerIds();
                if(guessSuccessPlayerIds == null){
                    guessSuccessPlayerIds = new ArrayList<>(successPlayerIds);
                }else{
                    guessSuccessPlayerIds.retainAll(successPlayerIds);
                }
                //获取全部都猜对但是没领取玩家id
                guessSuccessPlayerIds.removeAll(hadReceiveBetRewardPlayerIds);
                //记录需要补发玩家id
                for (Long guessSuccessPlayerId : guessSuccessPlayerIds) {
                    int realServerId = Player.getRealServerId(guessSuccessPlayerId);
                    if(!needReissuePlayerIds.containsKey(realServerId)){
                        needReissuePlayerIds.put(realServerId,new ArrayList<>());
                    }
                    needReissuePlayerIds.get(realServerId).add(guessSuccessPlayerId);
                }
            }
        }


        for (Integer serverId : needReissuePlayerIds.keySet()) {
            List<Long> playerIds = needReissuePlayerIds.get(serverId);
            ServerCommandRequest request = CommandRequests.newServerCommandRequest("GsUCCommandService.betReissue");
            TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, serverId, request, playerIds);
        }

    }



    @Override
    public void readFromPb(byte[] bytes) throws Exception {
        WorldUnparalleledChallengeGlobalDataDb ucGlobalDb = PbUtilCompress.decode(WorldUnparalleledChallengeGlobalDataDb.class, bytes);
        if(Objects.nonNull(ucGlobalDb)){
            this.seasonId = ucGlobalDb.getSeasonId();
            this.isOpen = ucGlobalDb.isOpen();
            this.isFinish = ucGlobalDb.isFinish();
            for (UnparalleledChallengeRaceInfoDb raceInfoDb : ucGlobalDb.getRaceInfos()) {
                UnparalleledChallengeRaceInfo raceInfo = new UnparalleledChallengeRaceInfo();
                raceInfo.readFromDB(raceInfoDb);
                raceInfos.put(raceInfo.getRaceId(), raceInfo);
            }
            this.warZoneInfo = ucGlobalDb.getWarZoneInfo();
            this.hadReceiveBetRewardPlayerIds = ucGlobalDb.getHadReceiveBetRewardPlayerIds();
            for (Integer warZoneId : ucGlobalDb.getMrtRankInfos().keySet()) {
                this.mrtRankInfos.put(warZoneId, ucGlobalDb.getMrtRankInfos().get(warZoneId).getList());
            }
        }
    }

    @Override
    public byte[] writeToPb() {
        WorldUnparalleledChallengeGlobalDataDb ucGlobalDb = new WorldUnparalleledChallengeGlobalDataDb();
        ucGlobalDb.setSeasonId(seasonId);
        ucGlobalDb.setOpen(isOpen);
        ucGlobalDb.setFinish(isFinish);
        for (UnparalleledChallengeRaceInfo raceInfo : raceInfos.values()) {
            ucGlobalDb.getRaceInfos().add(raceInfo.writeToDB());
        }
        ucGlobalDb.setWarZoneInfo(warZoneInfo);
        ucGlobalDb.setHadReceiveBetRewardPlayerIds(hadReceiveBetRewardPlayerIds);
        for (Integer warZoneId : mrtRankInfos.keySet()) {
            RedisListLongBean bean = new RedisListLongBean();
            bean.setList(mrtRankInfos.get(warZoneId));
            ucGlobalDb.getMrtRankInfos().put(warZoneId, bean);
        }
        return PbUtilCompress.encode(ucGlobalDb);
    }

    @Override
    public void init() {

    }

    @Override
    public void tick() {
        if(isOpen && !isFinish){
            //状态检测
            boolean isAllFinish = true;
            for (UnparalleledChallengeRaceInfo raceInfo : raceInfos.values()) {
                if(raceInfo.getPhase().getId() >= UCPhaseType.finish.getId()){
                    continue;
                }
                raceInfo.tick();
                isAllFinish = false;
            }

            if(isAllFinish){
                isFinish = true;
            }
        }
    }

    public WarZoneInfo getWarZoneInfo() {
        return warZoneInfo;
    }

    public void setWarZoneInfo(WarZoneInfo warZoneInfo) {
        this.warZoneInfo = warZoneInfo;
    }

    public int getSeasonId() {
        return seasonId;
    }

    public void setSeasonId(int seasonId) {
        this.seasonId = seasonId;
    }

    public boolean isOpen() {
        return isOpen;
    }

    public void setOpen(boolean open) {
        isOpen = open;
    }

    public boolean isFinish() {
        return isFinish;
    }

    public void setFinish(boolean finish) {
        isFinish = finish;
    }

    public Map<Integer, UnparalleledChallengeRaceInfo> getRaceInfos() {
        return raceInfos;
    }

    public void setRaceInfos(Map<Integer, UnparalleledChallengeRaceInfo> raceInfos) {
        this.raceInfos = raceInfos;
    }

    public Set<Long> getHadReceiveBetRewardPlayerIds() {
        return hadReceiveBetRewardPlayerIds;
    }

    public void setHadReceiveBetRewardPlayerIds(Set<Long> hadReceiveBetRewardPlayerIds) {
        this.hadReceiveBetRewardPlayerIds = hadReceiveBetRewardPlayerIds;
    }

    public Map<Integer, List<Long>> getMrtRankInfos() {
        return mrtRankInfos;
    }

    public void setMrtRankInfos(Map<Integer, List<Long>> mrtRankInfos) {
        this.mrtRankInfos = mrtRankInfos;
    }
}
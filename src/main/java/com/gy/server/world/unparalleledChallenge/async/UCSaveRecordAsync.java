package com.gy.server.world.unparalleledChallenge.async;

import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.game.record.RecordGlobalData;
import com.gy.server.game.record.RecordManager;
import com.gy.server.game.record.RecordType;
import com.gy.server.game.record.combat.CombatRecord;
import com.gy.server.game.unparalleledChallenge.UnparalleledChallengeService;
import com.gy.server.game.unparalleledChallenge.template.UnparalleledChallengeSeasonTemplate;
import com.gy.server.packet.PbRecord;
import com.gy.server.world.record.WorldCombatRecord;
import com.gy.server.world.record.WorldCombatRecordGenerator;
import com.gy.server.world.unparalleledChallenge.bean.*;
import com.gy.server.world.unparalleledChallenge.enums.UCPhaseType;
import com.ttlike.server.tl.baselib.serialize.player.MiniPlayer;

import java.util.List;
import java.util.Objects;

/**
 * 增加记录
 * @author: gbk
 * @date: 2024-12-16 19:43
 */
public class UCSaveRecordAsync extends AsyncCall {

    WorldCombatRecord WorldCombatRecord;

    int seasonId;
    long ucRecordId;
    AbstractStage stage;
    UnparalleledChallengePlayerInfo atkPlayerInfo;
    UnparalleledChallengePlayerInfo defPlayerInfo;
    UnparalleledChallengeRaceInfo raceInfo;
    List<Boolean> atkWinScores;

    public UCSaveRecordAsync(int seasonId, long ucRecordId, AbstractStage stage,
                             UnparalleledChallengePlayerInfo atkPlayerInfo, UnparalleledChallengePlayerInfo defPlayerInfo,
                             UnparalleledChallengeRaceInfo raceInfo, List<Boolean> atkWinScores){
        this.seasonId = seasonId;
        this.ucRecordId = ucRecordId;
        this.stage = stage;
        this.atkPlayerInfo = atkPlayerInfo;
        this.defPlayerInfo = defPlayerInfo;
        this.raceInfo = raceInfo;
        this.atkWinScores = atkWinScores;
    }


    @Override
    public void asyncExecute() {
    }

    @Override
    public void execute() {

    }


    /**
     * 尝试结算竞猜
     */
    private void trySettlementBet(long recordId){
        UCBetInfo ucBetInfo = raceInfo.getBetInfos().get(raceInfo.getPhase());
        if(Objects.nonNull(ucBetInfo)){
            if(ucBetInfo.getAtkPlayerId() == atkPlayerInfo.getPlayerId() && ucBetInfo.getDefPlayerId() == defPlayerInfo.getPlayerId()){
                //只记录战报id，暂时不记录胜负，等到展示期间再记录胜负
                ucBetInfo.setRecordId(recordId);
            }
        }
    }

}
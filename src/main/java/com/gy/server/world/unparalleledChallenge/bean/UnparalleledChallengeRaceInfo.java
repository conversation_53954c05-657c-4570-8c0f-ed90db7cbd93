package com.gy.server.world.unparalleledChallenge.bean;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.db.AbsDBSaveExecutor;
import com.gy.server.game.unparalleledChallenge.UnparalleledChallengeService;
import com.gy.server.game.unparalleledChallenge.template.UnparalleledChallengeConstant;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.world.unparalleledChallenge.enums.UCCombatType;
import com.gy.server.world.unparalleledChallenge.enums.UCPhaseType;
import com.gy.server.world.unparalleledChallenge.enums.UCShowPhaseType;
import com.ttlike.server.tl.baselib.serialize.uc.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 群龙聚首-赛区信息
 * @author: gbk
 * @date: 2024-12-09 10:11
 */
public class UnparalleledChallengeRaceInfo extends AbsDBSaveExecutor<UnparalleledChallengeRaceInfoDb> {

    //赛区id
    private int raceId;
    //战斗阶段
    private UCPhaseType phase = UCPhaseType.abort;
    //参与组信息
    //显示阶段类型：组信息
    private Map<UCShowPhaseType, List<UnparalleledChallengeGroupInfo>> groupInfos = new HashMap<>();
    //tick检查时间
    private long tickTime;
    //当前战斗状态
    private UCCombatType combatType = UCCombatType.wait;
    //切换状态标记
    private boolean changeCombatTypeMark;
    //战报信息
    private Map<Long, UnparalleledChallengeRecordInfo> recordInfos = new HashMap<>();
    //需要分配id
    private AtomicInteger allocateId = new AtomicInteger(1);
    //上次布阵时间
    private long lastLineupTime;
    //参与玩家信息
    private Map<Long, UnparalleledChallengePlayerInfo> playerInfos = new HashMap<>();
    //排名id
    private int rankId = 64;
    //竞猜信息
    private Map<UCPhaseType, UCBetInfo> betInfos = new HashMap<>();

    /**
     * 分配一个唯一id
     */
    public long getAllocateId(){
        return allocateId.getAndIncrement();
    }

    //分配排名id
    public int allocateRank(){
        return rankId--;
    }

    public Map<UCPhaseType, UCBetInfo> getBetInfos() {
        return betInfos;
    }

    public void setBetInfos(Map<UCPhaseType, UCBetInfo> betInfos) {
        this.betInfos = betInfos;
    }

    public long getLastLineupTime() {
        return lastLineupTime;
    }

    public void setLastLineupTime(long lastLineupTime) {
        this.lastLineupTime = lastLineupTime;
    }

    public boolean isChangeCombatTypeMark() {
        return changeCombatTypeMark;
    }

    public void setChangeCombatTypeMark(boolean changeCombatTypeMark) {
        this.changeCombatTypeMark = changeCombatTypeMark;
    }

    public UnparalleledChallengeRaceInfo(){}
    public UnparalleledChallengeRaceInfo(int raceId){
        this.raceId = raceId;
    }

    /**
     * 初始化小组赛
     * @param allPlayerIds
     */
    public void initGroup(List<Long> allPlayerIds){
        if(CollectionUtil.isNotEmpty(allPlayerIds)){
            phase = UCPhaseType.group;

            List<UnparalleledChallengeGroupInfo> groupInfoList = new ArrayList<>();
            //初始化64个小组
            int groupCount = 64;
            for (int i = 0; i < groupCount; i++) {
                int groupId = i + 1;
                groupInfoList.add(new UnparalleledChallengeGroupInfo(groupId));
            }
            //开始组内进人
            for (int i = 0; i < allPlayerIds.size(); i++) {
                long playerId = allPlayerIds.get(i);
                int enterIndex = i % groupCount;
                groupInfoList.get(enterIndex).getPlayerIds().add(playerId);
                playerInfos.put(playerId, UnparalleledChallengePlayerInfo.newPlayerInfo(playerId));
            }
            //设置锁定布阵信息
            UnparalleledChallengeConstant constant = UnparalleledChallengeService.getConstant();
            tickTime = constant.modifyTime(ServerConstants.getCurrentTimeLocalDateTime(),constant.lockedTimes_1, false);
            getGroupInfos().put(phase.getShowPhase(), groupInfoList);
        }
    }

    public int getRaceId() {
        return raceId;
    }

    public void setRaceId(int raceId) {
        this.raceId = raceId;
    }

    public UCPhaseType getPhase() {
        return phase;
    }

    public void setPhase(UCPhaseType phase) {
        this.phase = phase;
    }

    public void nextPhase(){
        this.phase = phase.getNextPhase();
    }

    public Map<UCShowPhaseType, List<UnparalleledChallengeGroupInfo>> getGroupInfos() {
        return groupInfos;
    }

    public List<UnparalleledChallengeGroupInfo> getCurPhaseGroupInfo(){
        return groupInfos.get(phase.getShowPhase());
    }

    public void setGroupInfos(Map<UCShowPhaseType, List<UnparalleledChallengeGroupInfo>> groupInfos) {
        this.groupInfos = groupInfos;
    }

    public long getTickTime() {
        return tickTime;
    }

    public void setTickTime(long tickTime) {
        this.tickTime = tickTime;
    }

    public UCCombatType getCombatType() {
        return combatType;
    }

    public void setCombatType(UCCombatType combatType) {
        this.combatType = combatType;
    }

    public Map<Long, UnparalleledChallengeRecordInfo> getRecordInfos() {
        return recordInfos;
    }

    public void setRecordInfos(Map<Long, UnparalleledChallengeRecordInfo> recordInfos) {
        this.recordInfos = recordInfos;
    }

    public Map<Long, UnparalleledChallengePlayerInfo> getPlayerInfos() {
        return playerInfos;
    }

    public UnparalleledChallengePlayerInfo getPlayerInfo(long playerId){
        return playerInfos.get(playerId);
    }

    public void setPlayerInfos(Map<Long, UnparalleledChallengePlayerInfo> playerInfos) {
        this.playerInfos = playerInfos;
    }

    public void tick(){
        //结束状态不用tick了
        if(getPhase().getId() < UCPhaseType.finish.getId()){
            combatType.tick(this);
        }
    }


    @Override
    public void readFromDB(UnparalleledChallengeRaceInfoDb unparalleledChallengeRaceInfoDb) {
        this.raceId = unparalleledChallengeRaceInfoDb.getRaceId();
        this.phase = UCPhaseType.valueOf(unparalleledChallengeRaceInfoDb.getPhase());
        Map<String, UnparalleledChallengeGroupInfoListDb> groupInfosDb = unparalleledChallengeRaceInfoDb.getGroupInfos();
        for (String showPhase : groupInfosDb.keySet()) {
            UCShowPhaseType ucShowPhaseType = UCShowPhaseType.valueOf(showPhase);
            this.groupInfos.put(ucShowPhaseType, new ArrayList<>());
            UnparalleledChallengeGroupInfoListDb unparalleledChallengeGroupInfoListDb = groupInfosDb.get(showPhase);
            for (UnparalleledChallengeGroupInfoDb unparalleledChallengeGroupInfoDb : unparalleledChallengeGroupInfoListDb.getList()) {
                UnparalleledChallengeGroupInfo info = new UnparalleledChallengeGroupInfo();
                info.readFromDB(unparalleledChallengeGroupInfoDb);
                this.groupInfos.get(ucShowPhaseType).add(info);
            }
        }
        this.tickTime = unparalleledChallengeRaceInfoDb.getTickTime();
        this.combatType = UCCombatType.valueOf(unparalleledChallengeRaceInfoDb.getCombatType());
        this.changeCombatTypeMark = unparalleledChallengeRaceInfoDb.isChangeCombatTypeMark();
        for (UnparalleledChallengeRecordInfoDb recordInfo : unparalleledChallengeRaceInfoDb.getRecordInfos()) {
            UnparalleledChallengeRecordInfo info = new UnparalleledChallengeRecordInfo();
            info.readFromDB(recordInfo);
            this.recordInfos.put(info.getId(), info);
        }
        this.allocateId.set(unparalleledChallengeRaceInfoDb.getAllocateId());
        this.lastLineupTime = unparalleledChallengeRaceInfoDb.getLastLineupTime();
        for (UnparalleledChallengePlayerInfoDb playerInfo : unparalleledChallengeRaceInfoDb.getPlayerInfos()) {
            UnparalleledChallengePlayerInfo info = new UnparalleledChallengePlayerInfo();
            info.readFromDB(playerInfo);
            this.playerInfos.put(info.getPlayerId(), info);
        }
        //排名id
        this.rankId = unparalleledChallengeRaceInfoDb.getRankId();
        //竞猜信息
        Map<String, UCBetInfoDb> betInfos = unparalleledChallengeRaceInfoDb.getBetInfos();
        for (String s : betInfos.keySet()) {
            UCBetInfoDb ucBetInfoDb = betInfos.get(s);
            UCPhaseType ucPhaseType = UCPhaseType.valueOf(s);
            UCBetInfo info = new UCBetInfo();
            info.readFromDB(ucBetInfoDb);
            this.betInfos.put(ucPhaseType, info);
        }
    }

    @Override
    public UnparalleledChallengeRaceInfoDb writeToDB() {
        UnparalleledChallengeRaceInfoDb infoDb = new UnparalleledChallengeRaceInfoDb();
        infoDb.setRaceId(raceId);
        infoDb.setPhase(phase.name());

        for (UCShowPhaseType ucShowPhaseType : this.groupInfos.keySet()) {
            UnparalleledChallengeGroupInfoListDb listDb = new UnparalleledChallengeGroupInfoListDb();
            List<UnparalleledChallengeGroupInfo> unparalleledChallengeGroupInfos = this.groupInfos.get(ucShowPhaseType);
            unparalleledChallengeGroupInfos.forEach(info -> listDb.getList().add(info.writeToDB()));
            infoDb.getGroupInfos().put(ucShowPhaseType.name(), listDb);
        }
        infoDb.setTickTime(this.tickTime);
        infoDb.setCombatType(this.combatType.name());
        infoDb.setChangeCombatTypeMark(this.changeCombatTypeMark);
        this.recordInfos.values().forEach(info -> infoDb.getRecordInfos().add(info.writeToDB()));
        infoDb.setAllocateId(allocateId.get());
        infoDb.setLastLineupTime(this.lastLineupTime);
        this.playerInfos.values().forEach(info -> infoDb.getPlayerInfos().add(info.writeToDB()));
        infoDb.setRankId(rankId);
        for (UCPhaseType ucPhaseType : this.betInfos.keySet()) {
            infoDb.getBetInfos().put(ucPhaseType.name(), this.betInfos.get(ucPhaseType).writeToDB());
        }
        return infoDb;
    }
}
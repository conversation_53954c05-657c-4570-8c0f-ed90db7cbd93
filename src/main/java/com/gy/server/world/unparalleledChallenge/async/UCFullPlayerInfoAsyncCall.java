package com.gy.server.world.unparalleledChallenge.async;

import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.world.unparalleledChallenge.WorldUnparalleledChallengeGlobalData;
import com.gy.server.world.unparalleledChallenge.bean.UnparalleledChallengePlayerInfo;
import com.gy.server.world.unparalleledChallenge.bean.UnparalleledChallengeRaceInfo;
import com.ttlike.server.tl.baselib.serialize.player.MiniPlayer;
import com.ttlike.server.tl.baselib.serialize.warZone.WarZoneInfo;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 群龙聚首-填充玩家信息
 * @author: gbk
 * @date: 2025-02-17 22:06
 */
public class UCFullPlayerInfoAsyncCall extends UCAsyncCall {

    List<Long> playerIds;
    int warZoneId;
    Map<Long, MiniPlayer> miniPlayerInfos = new HashMap<>();
    public UCFullPlayerInfoAsyncCall(int warZoneId, List<Long> playerIds){
        this.playerIds = playerIds;
        this.warZoneId = warZoneId;
    }

    @Override
    public void asyncExecute() {
        Map<Long, MiniGamePlayer> miniPlayersForMap = PlayerHelper.getMiniPlayersForMap(playerIds);
        for (Long playerId : playerIds) {
            miniPlayerInfos.put(playerId, miniPlayersForMap.get(playerId));
        }
    }

    @Override
    public void execute() {
        WorldUnparalleledChallengeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.unparalleledChallenge);
        WarZoneInfo.WarZoneSingleInfo infoByWarZone = globalData.getWarZoneInfo().getInfoByWarZone(warZoneId);
        int raceId = infoByWarZone.getRaceId();
        UnparalleledChallengeRaceInfo unparalleledChallengeRaceInfo = globalData.getRaceInfos().get(raceId);
        for (Long playerId : playerIds) {
//            unparalleledChallengeRaceInfo.getPlayerInfos().get(playerId).setMiniPlayer(miniPlayerInfos.get(playerId));
        }
        //检查是否所有玩家信息填充完毕
        for (UnparalleledChallengeRaceInfo raceInfo : globalData.getRaceInfos().values()) {
            for (UnparalleledChallengePlayerInfo playerInfo : raceInfo.getPlayerInfos().values()) {
//                if(Objects.isNull(playerInfo.getMiniPlayer())){
//                    return;
//                }
            }
        }
        globalData.setOpen(true);
    }
}
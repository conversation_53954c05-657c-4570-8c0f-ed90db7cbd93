package com.gy.server.world.unparalleledChallenge.status.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.thread.SimpleHashedThreadPool;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.lineup.LineupHelper;
import com.gy.server.game.lineup.bean.LineupInfoBean;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.game.unparalleledChallenge.UnparalleledChallengeService;
import com.gy.server.game.unparalleledChallenge.template.UnparalleledChallengeSeasonTemplate;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.time.DateTimeUtil;
import com.gy.server.world.unparalleledChallenge.WorldUnparalleledChallengeGlobalData;
import com.gy.server.world.unparalleledChallenge.bean.UnparalleledChallengeGroupInfo;
import com.gy.server.world.unparalleledChallenge.bean.UnparalleledChallengePlayerInfo;
import com.gy.server.world.unparalleledChallenge.bean.UnparalleledChallengeRaceInfo;
import com.gy.server.world.unparalleledChallenge.enums.UCCombatType;
import com.gy.server.world.unparalleledChallenge.status.AbsUCCombatStatus;

import java.util.List;
import java.util.Objects;

/**
 * 拉取布阵信息
 * @author: gbk
 * @date: 2024-12-09 19:46
 */
public class Status2LineupLockHandler extends AbsUCCombatStatus {


    @Override
    public void doRightNow(UnparalleledChallengeRaceInfo raceInfo) {

        WorldUnparalleledChallengeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.unparalleledChallenge);
        int seasonId = globalData.getSeasonId();
        UnparalleledChallengeSeasonTemplate seasonTemplate = UnparalleledChallengeService.getSeasonTemplates().get(seasonId);
        //异步开始拉取布阵信息
        List<UnparalleledChallengeGroupInfo> curPhaseGroupInfo = raceInfo.getCurPhaseGroupInfo();
        for (UnparalleledChallengeGroupInfo unparalleledChallengeGroupInfo : curPhaseGroupInfo) {
            SimpleHashedThreadPool.getInstance().addTask(()->{
                for (long playerId : unparalleledChallengeGroupInfo.getPlayerIds()) {
                    UnparalleledChallengePlayerInfo playerInfo = raceInfo.getPlayerInfo(playerId);
                    if(CollectionUtil.isEmpty(playerInfo.getLineupInfos()) && playerInfo.getLoseTimes(raceInfo.getPhase().getShowPhase()) <= 0){
                        List<LineupInfoBean> crossLineup = LineupHelper.getCrossLineup(playerInfo.getPlayerId(), seasonTemplate.lineupType);
                        for (LineupInfoBean lineupInfoBean : crossLineup) {
                            playerInfo.getLineupInfos().add(lineupInfoBean.getMirrorInfo().getMirrorStandsMap());
                        }
                    }
                }
            }, unparalleledChallengeGroupInfo.getGroupId());
        }
        raceInfo.setLastLineupTime(ServerConstants.getCurrentTimeMillis());

        GameLogger.ucInfo(raceInfo.getRaceId(), "uc lock lineUp finish ");
    }

    //10分钟再次拉取布阵
    private static final long needCheckLineup = 10L * DateTimeUtil.MillisOfMinute;
    @Override
    public void tick(UnparalleledChallengeRaceInfo raceInfo) {
        //检查是否所有阵容都拉取完毕
        boolean isFinish = true;
        List<UnparalleledChallengeGroupInfo> curPhaseGroupInfo = raceInfo.getCurPhaseGroupInfo();
        for (UnparalleledChallengeGroupInfo unparalleledChallengeGroupInfo : curPhaseGroupInfo) {
            for (Long playerId : unparalleledChallengeGroupInfo.getPlayerIds()) {
                UnparalleledChallengePlayerInfo playerInfo = raceInfo.getPlayerInfo(playerId);
                if(CollectionUtil.isEmpty(playerInfo.getLineupInfos()) && playerInfo.getLoseTimes(raceInfo.getPhase().getShowPhase()) <= 0){
                    isFinish = false;
                    break;
                }
            }
        }
        if(isFinish){
            modifyRaceCombatStatus(raceInfo, UCCombatType.combat);
            return;
        }

        //定时检测容错
        if(raceInfo.getLastLineupTime() + needCheckLineup <= ServerConstants.getCurrentTimeMillis()){
            this.doRightNow(raceInfo);
        }

    }
}
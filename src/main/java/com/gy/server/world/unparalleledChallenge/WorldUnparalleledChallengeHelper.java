package com.gy.server.world.unparalleledChallenge;

import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.log.GameLogger;
import com.gy.server.world.unparalleledChallenge.async.UCFullPlayerInfoAsyncCall;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.List;
import java.util.Map;

/**
 * PVP群龙聚首-辅助类
 * @author: gbk
 * @date: 2024-12-03 09:31
 */
public class WorldUnparalleledChallengeHelper {


    /**
     * 开启新赛季
     */
    public static void openNewSeason(Map<Integer, List<Long>> pointRankInfos){
        WorldUnparalleledChallengeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.unparalleledChallenge);
        globalData.initSeason(pointRankInfos);
        //填充玩家信息
//        pointRankInfos.forEach((k, v) -> ThreadPool.execute(new UCFullPlayerInfoAsyncCall(k, v)));
        globalData.setOpen(true);
        GameLogger.ucInfo(-1, "uc open new Season");
        for (Integer warZoneId : pointRankInfos.keySet()) {
            GameLogger.ucInfo(-1, "warZoneId : " + warZoneId + " playerId : " + pointRankInfos.get(warZoneId));
        }
    }

}
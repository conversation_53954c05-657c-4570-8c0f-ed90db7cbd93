package com.gy.server.world.unparalleledChallenge.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 群龙聚首战斗阶段
 * @author: gbk
 * @date: 2024-12-03 13:44
 */
public enum UCPhaseType {


    //小组赛
    abort(99, null, null),

    finish(8, null, null),

    fight<PERSON><PERSON>(7, UCPhaseType.finish, UCShowPhaseType.champion),

    fight4(6, UCPhaseType.fightFinal, UCShowPhaseType.champion),

    fight8(5, UCPhaseType.fight4, UCShowPhaseType.champion),

    fight16(4, UCPhaseType.fight8, UCShowPhaseType.advance),

    fight32(3, UCPhaseType.fight16, UCShowPhaseType.advance),

    fight64(2, UCPhaseType.fight32, UCShowPhaseType.advance),

    group(1, UCPhaseType.fight64, UCShowPhaseType.group),

    ;
    int id;
    UCPhaseType nextPhase;
    UCShowPhaseType showPhase;
    UCPhaseType(int id, UCPhaseType nextPhase, UCShowPhaseType showPhase){
        this.id = id;
        this.nextPhase = nextPhase;
        this.showPhase = showPhase;
    }

    private static Map<Integer, UCPhaseType> enums = new HashMap<>();
    static{
        for (UCPhaseType type : UCPhaseType.values()) {
            enums.put(type.getId(), type);
        }
    }

    public static UCPhaseType of(int id){
        return enums.get(id);
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public UCPhaseType getNextPhase() {
        return nextPhase;
    }

    public void setNextPhase(UCPhaseType nextPhase) {
        this.nextPhase = nextPhase;
    }

    public UCShowPhaseType getShowPhase() {
        return showPhase;
    }

    public void setShowPhase(UCShowPhaseType showPhase) {
        this.showPhase = showPhase;
    }
}
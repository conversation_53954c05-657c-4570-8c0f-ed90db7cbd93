package com.gy.server.world.unparalleledChallenge.status.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.player.Player;
import com.gy.server.game.unparalleledChallenge.UnparalleledChallengeService;
import com.gy.server.game.unparalleledChallenge.template.UnparalleledChallengeConstant;
import com.gy.server.utils.StringUtil;
import com.gy.server.world.unparalleledChallenge.WorldUnparalleledChallengeGlobalData;
import com.gy.server.world.unparalleledChallenge.bean.UCBetInfo;
import com.gy.server.world.unparalleledChallenge.bean.UnparalleledChallengeGroupInfo;
import com.gy.server.world.unparalleledChallenge.bean.UnparalleledChallengePlayerInfo;
import com.gy.server.world.unparalleledChallenge.bean.UnparalleledChallengeRaceInfo;
import com.gy.server.world.unparalleledChallenge.enums.UCCombatType;
import com.gy.server.world.unparalleledChallenge.enums.UCPhaseType;
import com.gy.server.world.unparalleledChallenge.enums.UCShowPhaseType;
import com.gy.server.world.unparalleledChallenge.status.AbsUCCombatStatus;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;
import java.util.stream.Collectors;

/**
 * //战后处理
 * @author: gbk
 * @date: 2024-12-09 19:46
 */
public class Status4CombatAfterHandler extends AbsUCCombatStatus {

    @Override
    public void tick(UnparalleledChallengeRaceInfo raceInfo) {
        //检查能否开始结算本轮战斗
//        if(ServerConstants.getCurrentTimeMillis() < raceInfo.getTickTime()){
//            return;
//        }
        UCPhaseType oldPhase = raceInfo.getPhase();
        //公布/结算竞猜奖励
        settlementBet(raceInfo);
        //结算战斗
        switch (raceInfo.getPhase()){
            case group:{
                //开始64强,每8个一组
                List<UnparalleledChallengeGroupInfo> oldGroupInfos = raceInfo.getGroupInfos().get(oldPhase.getShowPhase());
                UCShowPhaseType nextShowPhaseType = oldPhase.getNextPhase().getShowPhase();
                raceInfo.getGroupInfos().put(nextShowPhaseType, new ArrayList<>());
                List<UnparalleledChallengeGroupInfo> newGroupInfos = raceInfo.getGroupInfos().get(nextShowPhaseType);
                int groupNum = 8;
                //提前创建8个组
                for (int i = 0; i < groupNum; i++) {
                    UnparalleledChallengeGroupInfo newGroupInfo = new UnparalleledChallengeGroupInfo(i + 1);
                    newGroupInfos.add(newGroupInfo);
                }
                //获取小组赛每组最强的玩家
                List<Long> allStrongestPlayerIds = oldGroupInfos.stream().map(oldGroupInfo -> oldGroupInfo.getStrongest(raceInfo))
                        .collect(Collectors.toList());
                //按战力排序
                allStrongestPlayerIds.sort((o1, o2) -> {
                    UnparalleledChallengePlayerInfo playerInfo1 = raceInfo.getPlayerInfo(o1);
                    UnparalleledChallengePlayerInfo playerInfo2 = raceInfo.getPlayerInfo(o2);
                    return (int) (playerInfo2.getFightPower() - playerInfo1.getFightPower());
                });
                /**
                 * 分配到8个组（蛇形）
                 * [1, 16, 24, 32, 40, 48, 56, 64],
                 * [2, 15, 23, 31, 39, 47, 55, 63],
                 * [3, 14, 22, 30, 38, 46, 54, 62],
                 * [4, 13, 21, 29, 37, 45, 53, 61],
                 * [5, 12, 20, 28, 36, 44, 52, 60],
                 * [6, 11, 19, 27, 35, 43, 51, 59],
                 * [7, 10, 18, 26, 34, 42, 50, 58],
                 * [8, 9, 17, 25, 33, 41, 49, 57]]
                 */
                for (int i = 0; i < groupNum; i++) {
                    for (int j = 0; j < groupNum; j++) {
                        newGroupInfos.get(j).getPlayerIds().add(allStrongestPlayerIds.get(i * groupNum + j));
                    }
                }
                //组内打乱顺序
                newGroupInfos.forEach(groupInfo -> Collections.shuffle(groupInfo.getPlayerIds()));

                //进行下次等待，切换等待状态
                UnparalleledChallengeConstant constant = UnparalleledChallengeService.getConstant();
                raceInfo.setTickTime(constant.modifyTime(ServerConstants.getCurrentTimeLocalDateTime(), constant.lockedTimes_2, true));
                modifyRaceCombatStatus(raceInfo, UCCombatType.wait);
                break;
            }
            case fight16:{
                //进入8强,直接把组里的人拿出来放到一个队伍,(不考虑8强人不够的情况)
                List<UnparalleledChallengeGroupInfo> oldGroupInfos = raceInfo.getGroupInfos().get(oldPhase.getShowPhase());
                List<Long> allPlayerInfos = new ArrayList<>();
                for (UnparalleledChallengeGroupInfo oldGroupInfo : oldGroupInfos) {
                    long strongest = oldGroupInfo.getStrongest(raceInfo);
                    clearPlayerLineup(raceInfo, strongest);
                    allPlayerInfos.add(strongest);
                }
                /**
                 * 最终对战表
                 * 1                       2
                 *      -           -
                 * 8          ?            7
                 *          ? - ?
                 * 4                       3
                 *      -           -
                 * 5                       6
                 */
                List<Long> finalPlayerIds = new ArrayList<>();
                finalPlayerIds.add(allPlayerInfos.get(0));
                finalPlayerIds.add(allPlayerInfos.get(7));
                finalPlayerIds.add(allPlayerInfos.get(3));
                finalPlayerIds.add(allPlayerInfos.get(4));

                finalPlayerIds.add(allPlayerInfos.get(1));
                finalPlayerIds.add(allPlayerInfos.get(6));
                finalPlayerIds.add(allPlayerInfos.get(2));
                finalPlayerIds.add(allPlayerInfos.get(5));
                //构建最后一组
                UnparalleledChallengeGroupInfo newGroupInfo = new UnparalleledChallengeGroupInfo(1);
                newGroupInfo.setPlayerIds(finalPlayerIds);
                List<UnparalleledChallengeGroupInfo> newGroupInfos = new ArrayList<>();
                newGroupInfos.add(newGroupInfo);
                raceInfo.getGroupInfos().put(raceInfo.getPhase().getNextPhase().getShowPhase(), newGroupInfos);

                //进行下次等待，切换等待状态
                UnparalleledChallengeConstant constant = UnparalleledChallengeService.getConstant();
                raceInfo.setTickTime(constant.modifyTime(ServerConstants.getCurrentTimeLocalDateTime(), constant.lockedTimes_2, true));
                modifyRaceCombatStatus(raceInfo, UCCombatType.wait);
                break;
            }

            case fightFinal:{
                //进入结算
                modifyRaceCombatStatus(raceInfo, UCCombatType.reward);
                return;
            }
            default:{
                //正常组内战斗，清除所有布阵
                List<UnparalleledChallengeGroupInfo> nowGroupInfos = raceInfo.getGroupInfos().get(oldPhase.getShowPhase());
                for (UnparalleledChallengeGroupInfo nowGroupInfo : nowGroupInfos) {
                    for (Long playerId : nowGroupInfo.getPlayerIds()) {
                        clearPlayerLineup(raceInfo,playerId);
                    }
                }
                UnparalleledChallengeConstant constant = UnparalleledChallengeService.getConstant();
                raceInfo.setTickTime(constant.modifyTime(ServerConstants.getCurrentTimeLocalDateTime(), constant.lockedTimes_2, true));
                modifyRaceCombatStatus(raceInfo, UCCombatType.wait);
                break;
            }
        }

        //切换下阶段
        raceInfo.nextPhase();
        GameLogger.ucInfo(raceInfo.getRaceId(), "uc change phase, oldPhase : " + oldPhase + " newPhase : " + raceInfo.getPhase());

    }

    /**
     * 结算竞猜
     */
    private void settlementBet(UnparalleledChallengeRaceInfo raceInfo){
        UCBetInfo betInfo = raceInfo.getBetInfos().get(raceInfo.getPhase());
        if(Objects.nonNull(betInfo)){
            //serverId : winPlayerIds , losePlayerIds
            Map<Integer, Pair<List<Long>, List<Long>>> betResultMap = new HashMap<>();
            Set<Long> winPlayerIds;
            Set<Long> losePlayerIds;
            //计算最终赔率
            UnparalleledChallengeConstant constant = UnparalleledChallengeService.getConstant();
            int[] guessingOdds = StringUtil.splitToIntArray(constant.guessingOdds, ",");
            double M = guessingOdds[0];
            double N = guessingOdds[1];
            double odds;
            if(betInfo.isAtkWin()){
                winPlayerIds = betInfo.getSupportAtkPlayerIds();
                losePlayerIds = betInfo.getSupportDefPlayerIds();
                odds = M + (1D - (double)betInfo.getSupportAtkPlayerIds().size() / (double)(betInfo.getSupportAtkPlayerIds().size() + betInfo.getSupportDefPlayerIds().size())) * (N - M);
            }else{
                winPlayerIds = betInfo.getSupportDefPlayerIds();
                losePlayerIds = betInfo.getSupportAtkPlayerIds();
                odds = M + (1D - (double)betInfo.getSupportDefPlayerIds().size() / (double)(betInfo.getSupportAtkPlayerIds().size() + betInfo.getSupportDefPlayerIds().size())) * (N - M);
            }
            //填充数据
            fillBetResult(betResultMap, winPlayerIds, losePlayerIds);
            WorldUnparalleledChallengeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.unparalleledChallenge);
            //通知服务器，增加公共邮件
            for (Integer serverId : betResultMap.keySet()) {
                Pair<List<Long>, List<Long>> betResult = betResultMap.get(serverId);
                ServerCommandRequest request = CommandRequests.newServerCommandRequest("GsUCCommandService.betReward");
                TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, serverId, request,
                        globalData.getSeasonId(), raceInfo.getRaceId(),
                        raceInfo.getPhase().name(), betResult.getKey(), betResult.getValue(),
                        odds);
            }
        }

    }

    private void fillBetResult(Map<Integer, Pair<List<Long>, List<Long>>> betResultMap, Set<Long> winPlayerIds, Set<Long> losePlayerIds){
        for (long playerId : winPlayerIds) {
            int realServerId = Player.getRealServerId(playerId);
            if(!betResultMap.containsKey(realServerId)){
                betResultMap.put(realServerId, Pair.of(new ArrayList<>(), new ArrayList<>()));
            }
            //winPlayerIds, losePlayerIds
            Pair<List<Long>, List<Long>> betResult = betResultMap.get(realServerId);
            betResult.getKey().add(playerId);
        }

        for (long playerId : losePlayerIds) {
            int realServerId = Player.getRealServerId(playerId);
            if(!betResultMap.containsKey(realServerId)){
                betResultMap.put(realServerId, Pair.of(new ArrayList<>(), new ArrayList<>()));
            }
            //winPlayerIds, losePlayerIds
            Pair<List<Long>, List<Long>> betResult = betResultMap.get(realServerId);
            betResult.getValue().add(playerId);
        }

    }

    private void clearPlayerLineup(UnparalleledChallengeRaceInfo raceInfo, long playerId){
//        raceInfo.getPlayerInfo(playerId).getLineupInfos().clear();
    }
}
package com.gy.server.world.unparalleledChallenge.async;

import com.gy.server.common.base.AsyncCallBackCall;
import com.gy.server.core.command.PlayerCommandRequest;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.packet.PbLeague;
import com.gy.server.packet.PbProtocol;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 获取主界面信息
 * @author: gbk
 * @date: 2024-12-18 13:55
 */
@Deprecated
public class UCBetAsyncCallTest extends AsyncCallBackCall {

    Map<Long, MiniGamePlayer> players = new HashMap<>();
    PbProtocol.UnparalleledChallengeBetRst.Builder rst;

    public UCBetAsyncCallTest(PbProtocol.UnparalleledChallengeBetRst.Builder rst, PlayerCommandRequest request) {
        super(request);
        this.rst = rst;
    }

    @Override
    public void asyncExecute() {
        Set<Long> playerIds = new HashSet<>();
        PbLeague.UnparalleledChallengeBetInfo betInfo = rst.getBetInfo();
        playerIds.add(betInfo.getAfterPlayerInfo().getPlayerId());
        playerIds.add(betInfo.getBeforePlayerInfo().getPlayerId());
        players.putAll(PlayerHelper.getMiniPlayersForMap(playerIds));

    }

    @Override
    public void syncExecute() {
        PbLeague.UnparalleledChallengeBetInfo.Builder betInfoBuilder = rst.getBetInfoBuilder();
        betInfoBuilder.setBeforePlayerInfo(betInfoBuilder.getBeforePlayerInfoBuilder().setMiniUser(players.get(betInfoBuilder.getBeforePlayerInfo().getPlayerId()).genMinMiniUser()));
        betInfoBuilder.setAfterPlayerInfo(betInfoBuilder.getAfterPlayerInfoBuilder().setMiniUser(players.get(betInfoBuilder.getAfterPlayerInfo().getPlayerId()).genMinMiniUser()));
        rst.setBetInfo(betInfoBuilder.build());
        request.addCallbackParam(rst.build().toByteArray());
    }
}
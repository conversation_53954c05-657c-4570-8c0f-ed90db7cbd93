package com.gy.server.world.common;

public class ServerNode {

    public int nodeId;

    public long lastActiveTime = System.currentTimeMillis();

    public int onlineCount;

    ServerNode(int nodeId){
        this.nodeId = nodeId;
    }

    public int getNodeId() {
        return nodeId;
    }

    public void setNodeId(int nodeId) {
        this.nodeId = nodeId;
    }

    public long getLastActiveTime() {
        return lastActiveTime;
    }

    public void setLastActiveTime(long lastActiveTime) {
        this.lastActiveTime = lastActiveTime;
    }

    public int getOnlineCount() {
        return onlineCount;
    }

    public void setOnlineCount(int onlineCount) {
        this.onlineCount = onlineCount;
    }

    public void syncCount(int onlineCount){
        this.onlineCount = onlineCount;
        this.lastActiveTime = System.currentTimeMillis();
    }

}

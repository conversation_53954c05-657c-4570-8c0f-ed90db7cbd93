package com.gy.server.world.common;

import com.gy.server.annotation.MessageMethod;
import com.gy.server.annotation.MessageServiceBean;
import com.gy.server.core.Configuration;
import com.gy.server.core.MessageServerType;
import com.gy.server.core.MethodInvokeType;
import com.gy.server.core.command.CommandRequestParams;
import com.gy.server.core.command.PlayerCommandRequest;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.core.cos.COSManager;
import com.gy.server.core.log.CommonLogger;
import com.gy.server.game.gm.GmCommandHandler;
import com.gy.server.game.warZone.WarZoneTypeEnums;
import com.ttlike.server.tl.baselib.util.GroovyExecutor;

/**
 * <AUTHOR> - [Create on 2021/03/26 14:39]
 */
@MessageServiceBean(description = "中心服公共", messageServerType = MessageServerType.world)
public class WorldCommonCommandService {

    @MessageMethod(description = "修改时间")
    public static void updateTime(PlayerCommandRequest request, CommandRequestParams params) {
        if (Configuration.runMode.isTest()) {
            String[] array = params.getParam(0);
            GmCommandHandler.time(array);
        }
    }

    @MessageMethod(description = "同步cos时间戳")
    public static void cosTimeSync(ServerCommandRequest request, CommandRequestParams params) {
        if (Configuration.runMode.isTest()) {
            COSManager.getInstance().timeSync();
        }
    }

    @MessageMethod(description = "gs心跳", invokeType = MethodInvokeType.sync)
    public static void gsHeart(ServerCommandRequest request, CommandRequestParams params) {
        int serverId = params.getParam(0);
        int count = params.getParam(1);
        NodeCommonManager.getInstance().modifyServerHeart(serverId, count);
    }

    /**
     * 批量执行groovy脚本
     */
    @MessageMethod(description = "批量执行groovy脚本", invokeType = MethodInvokeType.async)
    private static void worldBatchExecuteGroovy(ServerCommandRequest request, CommandRequestParams params) {
        String code = params.getParam(0);
        CommonLogger.info("execute groovy script success.");
        // 回调返回
        request.addCallbackParam(GroovyExecutor.executeNotNeedResultInfo(code));
    }


    @MessageMethod(description = "刷新战区数据", invokeType = MethodInvokeType.async)
    private static void refreshWarZone(ServerCommandRequest request, CommandRequestParams params) {
        WarZoneTypeEnums type = params.getParam(0);
        type.getSaveType().get().init(type, false);
    }

}

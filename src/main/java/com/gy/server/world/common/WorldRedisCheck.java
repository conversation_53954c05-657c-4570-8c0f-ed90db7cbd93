package com.gy.server.world.common;

import com.gy.server.common.util.CommonUtils;
import com.ttlike.server.tl.baselib.redis.RedisHealthyCanCheck;

/**
 * world服是否进行redis健康检测
 * <AUTHOR> - [Created on 2022-07-21 11:04]
 */
public class WorldRedisCheck implements RedisHealthyCanCheck {
    @Override
    public boolean canCheck() {
        return CommonUtils.isMainWorldServer();
    }

    private static WorldRedisCheck instance = new WorldRedisCheck();
    WorldRedisCheck(){}
    public static WorldRedisCheck getInstance(){
        return instance;
    }
}

package com.gy.server.world.common;

import com.gy.server.core.callback.response.CallbackResponse;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.core.log.CommonLogger;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.core.thread.AbstractRunner;
import com.gy.server.game.drop.Drop;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.rpc.TLMessageCallbackTask;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class PushItemDataManager extends AbstractRunner {


    public static List<String> sqlList = new ArrayList<>();
    public static boolean sendGmFlag = false;

    private static final PushItemDataManager instance = new PushItemDataManager();

    public static PushItemDataManager getInstance() {
        return instance;
    }


    @Override
    public String getRunnerName() {
        return "PushItemDataManager";
    }

    @Override
    protected void subRunnerExecute() throws Exception {
        if (sendGmFlag) {
            return;
        }
        if (sqlList.isEmpty()) {
            try {
                loadSqlList();
            } catch (Exception e) {
                sqlList.clear();
                throw new RuntimeException(e);
            }
        }
        if (!sendGmFlag && !sqlList.isEmpty()) {
            ServerCommandRequest request = CommandRequests.newServerCommandRequest("GsWorldCommandService.itemSqlUpdate");
            TLBase.getInstance().getRpcUtil().sendToAnyNodeWithCallBack(new PushCallbackTask(), ServerType.GM, request, sqlList);
        }
    }

    @Override
    public long getRunnerInterval() {
        return 3 * 60 * 1000;
    }

    public enum ItemType {
        normalItem(Drop.DROP_TYPE_ITEM, "normalItem", "普通物品"),
        gem(Drop.DROP_TYPE_ITEM, "gem", "宝石"),
        currencyItem(Drop.DROP_TYPE_ITEM, "currencyItem", "货币", DropType.currency),
        hero(Drop.DROP_TYPE_STAR_HERO, "hero", "英雄"),
        equipment(Drop.DROP_TYPE_EQUIPMENT, "equipment", "装备"),
        martialArtsScript(Drop.DROP_TYPE_ITEM, "martialArtsScript", "心法"),
        chip(Drop.DROP_TYPE_ITEM, "chip", "碎片"),


//        divinesoul,
//        none,
//        treasure,
//        handCard,
//        head,
//        headFrame,
//        fashion,
//        exTreasure,
        ;

        //    int index;
        private int value;   //
        private String id;    //
        private String text;    //

        private DropType dropType = DropType.item;

        ItemType(int value, String id, String text) {
            this.value = value;
            this.id = id;
            this.text = text;
        }

        ItemType(int value, String id, String text, DropType dropType) {
            this.value = value;
            this.id = id;
            this.text = text;
            this.dropType = dropType;
        }

        public static List<ItemType> getItemTypes(){
            return Arrays.asList(ItemType.values());
        }

        public int getValue() {
            return value;
        }

        public void setValue(int value) {
            this.value = value;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public DropType getDropType() {
            return dropType;
        }

        public void setDropType(DropType dropType) {
            this.dropType = dropType;
        }
    }

    public enum DropType {
        currency,
        item,
    }

    public static String createSqlString(int id, String name, ItemType type, int maxCount){
        return id + "|" + name + "|" + type.value + "|" + type.id + "|" + type.text + "|" + type.dropType + "|" + type + "|" + maxCount;
    }

    public void loadSqlList() throws Exception {
        sqlList.clear();
        //物品插入数据模板
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.hero_hero);
        for (Map<String, String> map : mapList) {
            int id = Integer.parseInt(map.get("id"));
            String name = map.get("name");
            if (name == null) {
                name = "未命名英雄" + id;
            }
            if (name.equals("-1")) {
                continue;
            }

            sqlList.add(createSqlString(id, name, ItemType.hero, Integer.parseInt(map.get("sendingLimit"))));
        }

        mapList = ConfigReader.read(ConfigFile.chip_chip);
        for (Map<String, String> map : mapList) {
            int id = Integer.parseInt(map.get("id"));
            String name = map.get("name");
            if (name == null) {
                name = "未命名碎片" + id;
            }
            if (name.equals("-1")) {
                continue;
            }
            sqlList.add(createSqlString(id, name, ItemType.chip, Integer.parseInt(map.get("sendingLimit"))));
        }

        mapList = ConfigReader.read(ConfigFile.normalItem_normalItem);
        for (Map<String, String> map : mapList) {
            int id = Integer.parseInt(map.get("id"));
            String name = map.get("name");
            if (name == null) {
                name = "未命名道具" + id;
            }
            if (name.equals("-1")) {
                continue;
            }
            sqlList.add(createSqlString(id, name, ItemType.normalItem, Integer.parseInt(map.get("sendingLimit"))));
        }

        mapList = ConfigReader.read(ConfigFile.currencyItem_currency);
        for (Map<String, String> map : mapList) {
            int id = Integer.parseInt(map.get("id"));
            String name = map.get("name");
            if (name == null) {
                name = "未命名货币" + id;
            }
            sqlList.add(createSqlString(id, name, ItemType.currencyItem, Integer.parseInt(map.get("sendingLimit"))));
        }

        mapList = ConfigReader.read(ConfigFile.equipment_equip);
        for (Map<String, String> map : mapList) {
            int id = Integer.parseInt(map.get("id"));
            String name = map.get("name");
            if (name == null) {
                name = "未命名装备" + id;
            }
            sqlList.add(createSqlString(id, name, ItemType.equipment, Integer.parseInt(map.get("sendingLimit"))));
        }

        mapList = ConfigReader.read(ConfigFile.martialArtsScript_martialArtsScript);
        for (Map<String, String> map : mapList) {
            int id = Integer.parseInt(map.get("id"));
            String name = map.get("name");
            if (name == null) {
                name = "未命名秘籍" + id;
            }
            sqlList.add(createSqlString(id, name, ItemType.martialArtsScript, Integer.parseInt(map.get("sendingLimit"))));
        }

        mapList = ConfigReader.read(ConfigFile.gem_gem);
        for (Map<String, String> map : mapList) {
            int id = Integer.parseInt(map.get("id"));
            String name = map.get("name");
            if (name == null) {
                name = "未命名宝石" + id;
            }
            sqlList.add(createSqlString(id, name, ItemType.gem, Integer.parseInt(map.get("sendingLimit"))));
        }

//        mapList = ConfigReader.read("divinesoul_divinesoul.txt");
//        for (Map<String, String> map : mapList) {
//            int id = Integer.parseInt(map.get("id"));
//            String name = map.get("name");
//            if (name == null) {
//                name = "未命名战魂" + id;
//            }
//            sqlList.add(id + "," + DropType.item + "," + ItemType.divinesoul + "," + name);
//        }
    }

    public List<String> getSqlList() {
        return sqlList;
    }

    private static class PushCallbackTask extends TLMessageCallbackTask {
        @Override
        public void complete(CallbackResponse callbackResponse) {
            sendGmFlag = true;
            sqlList.clear();
            CommonLogger.info("GsWorldCommandService.itemSqlUpdate finish");
        }

        @Override
        public void timeout() {

        }
    }

//    public void test() throws Exception {
//        loadSqlList();
//        if (!sendGmFlag && !sqlList.isEmpty()) {
//            ServerCommandRequest request = CommandRequests.newServerCommandRequest("GsWorldCommandService.itemSqlUpdate");
//            TLBase.getInstance().getRpcUtil().sendToAnyNodeWithCallBack(new TLMessageCallbackTask() {
//                @Override
//                public void complete(CallbackResponse callbackResponse) {
//                    sendGmFlag = true;
//                    sqlList.clear();
//                    CommonLogger.info("GsWorldCommandService.itemSqlUpdate finish");
//                }
//
//                @Override
//                public void timeout() {
//
//                }
//            }, ServerType.GM, request, sqlList);
//        }
//    }

}

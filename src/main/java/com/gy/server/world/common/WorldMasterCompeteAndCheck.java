package com.gy.server.world.common;

import com.gy.server.core.Configuration;
import com.gy.server.core.thread.AbstractRunner;
import com.gy.server.game.team.TeamHelper;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.redis.key.BaseRedisKey;

/**
 * world服master节点竞争
 *
 * <AUTHOR> - [Created on 2022-03-22 10:36]
 */
public class WorldMasterCompeteAndCheck extends AbstractRunner {

    private static final int master_overdue_time = 5;
    private static final int live_world_overdue_time = 5;
    private static final int check_cd = 2;

    public void startup() {
    }

    @Override
    protected void subRunnerExecute() throws Exception {
        int serverId = Configuration.serverId;
        //存活world节点续时间
        String liveWorldRedisKey = BaseRedisKey.World.live_world.getRedisKey(serverId);
        TLBase.getInstance().getRedisAssistant().setInt(liveWorldRedisKey, serverId, live_world_overdue_time);


        TeamHelper.teamGsLiveCheckDelete();
    }

    @Override
    public String getRunnerName() {
        return "WorldMasterCompeteAndCheck";
    }

    @Override
    public long getRunnerInterval() {
        return check_cd * 1000L;
    }

    private static WorldMasterCompeteAndCheck instance = new WorldMasterCompeteAndCheck();

    WorldMasterCompeteAndCheck() {
    }

    public static WorldMasterCompeteAndCheck getInstance() {
        return instance;
    }
}

package com.gy.server.world.common;

import com.gy.server.core.Configuration;
import com.gy.server.core.ServerConstants;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.redis.key.BaseRedisKey;

import java.util.*;

public class NodeCommonManager {

    /**
     * 心跳信息
     * 当前服务器为gs:内容为场景服信息
     * 当前服务器为world：内容为gs信息
     * 当前服务器为scene:内容为gs信息
     */
    private Map<Integer, ServerNode> serverHeartInfos = new HashMap<>();


    private long lastCheckTime;
    private int lastCheckCount;

    /**
     * 修改服务器次暖和信息
     * @param serverId
     */
    public void modifyServerHeart(int serverId, int count){
        ServerNode serverNode = serverHeartInfos.get(serverId);
        if(Objects.isNull(serverNode)){
            serverNode = new ServerNode(serverId);
        }
        serverNode.syncCount(count);
        serverHeartInfos.put(serverId, serverNode);
    }

    /**
     * 获取健康gs服务器id
     * @param hour 健康时间
     * @return 服务器列表
     */
    public List<Integer> getHealthyServerId(int hour){
        long now = ServerConstants.getCurrentTimeMillis();
        long hourTime = hour * DateTimeUtil.MillisOfHour;
        List<Integer> healthyGameServerIds = new ArrayList<>();
        serverHeartInfos.forEach((k, v) ->{
            if(now - v.lastActiveTime < hourTime){
                healthyGameServerIds.add(k);
            }
        });
        return healthyGameServerIds;
    }

    public List<Integer> getHealthyServerIdMinute(int minute){
        long now = ServerConstants.getCurrentTimeMillis();
        long minuteTime = minute * DateTimeUtil.MillisOfMinute;
        List<Integer> healthyGameServerIds = new ArrayList<>();
        serverHeartInfos.forEach((k, v) ->{
            if(now - v.lastActiveTime < minuteTime){
                healthyGameServerIds.add(k);
            }
        });
        return healthyGameServerIds;
    }

    public Set<Integer> getAllWorldServerIds() {
        String redisKey = BaseRedisKey.World.all_world.getRedisKey();
        //所有world节点
        Set<String> allWorldIds = TLBase.getInstance().getRedisAssistant().setGetAll(redisKey);
        Set<Integer> rst = new HashSet<>();
        allWorldIds.forEach(id -> {
            try {
                rst.add(Integer.parseInt(id));
            } catch (NumberFormatException e) {
                e.printStackTrace();
            }
        });
        return rst;
    }

    /**
     * 服务器数量是否稳定
     * 数量不变且超过2分钟，认为是稳定
     * 正式模式下还需要验证服务器id是连续的
     */
    public boolean isStableServerCount() {
        if(serverHeartInfos.size() != lastCheckCount || lastCheckCount == 0){
            lastCheckTime = ServerConstants.getCurrentTimeMillis();
            lastCheckCount = serverHeartInfos.size();
            return false;
        }else{
            long now = ServerConstants.getCurrentTimeMillis();
            if(now - lastCheckTime > 2 * DateTimeUtil.MillisOfMinute){
                if(Configuration.runMode.isLive()){
                    //验证服务器id是否连续
                    List<Integer> sortedServerIds = new ArrayList<>(serverHeartInfos.keySet());
                    Collections.sort(sortedServerIds);
                    for(int i=0;i<sortedServerIds.size()-1;i++){
                        if(sortedServerIds.get(i) + 1 != sortedServerIds.get(i+1)){
                            return false;
                        }
                    }
                }
                return true;
            }else{
                return false;
            }
        }
    }

    private static final NodeCommonManager instance = new NodeCommonManager();
    NodeCommonManager(){}
    public static NodeCommonManager getInstance(){
        return instance;
    }
}

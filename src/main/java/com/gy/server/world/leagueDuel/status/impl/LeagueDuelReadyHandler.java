package com.gy.server.world.leagueDuel.status.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.leagueDuel.LeagueDuelHelper;
import com.gy.server.game.leagueDuel.LeagueDuelService;
import com.gy.server.game.leagueDuel.status.DuelNodeType;
import com.gy.server.game.player.Player;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.gy.server.utils.time.DateTimeUtil;
import com.gy.server.world.leagueDuel.status.ILeagueDuelWorldStatusHandler;
import com.gy.server.world.leagueDuel.status.LeagueDuelWorldStatusManager;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.*;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 数据准备阶段
 *
 * <AUTHOR> - [Created on 2023/5/25 14:50]
 */
public class LeagueDuelReadyHandler implements ILeagueDuelWorldStatusHandler {
    @Override
    public DuelStatusType handleStatus() {
        return DuelStatusType.ready;
    }

    @Override
    public void handler(LeagueDuelWorldRedisData leagueWorldRedisData) {
        // TODO 遍历所有战区 查看所有战区是否全部准备完毕
        boolean isAllReady = true;
        Set<Long> warZoneIdSet = leagueWorldRedisData.getWarZoneIdSet();
        logic:
        for (Long zoneId : warZoneIdSet) {
            //获取战区所有服务器
            List<Integer> zoneSeverList = LeagueDuelHelper.getZoneSever(zoneId);
            //检查数据是否准备完毕
            for (Integer zoneServer : zoneSeverList) {
                if (!leagueWorldRedisData.getJoinLeagueMap().containsKey(Player.getMainServer(zoneServer))) {
                    isAllReady = false;
                    break logic;
                }
            }
        }
        Integer duration = LeagueDuelService.getConstant().getOnTimeMap().get(DuelNodeType.ready).getRight();
        LocalDateTime readyEndTime = leagueWorldRedisData.getLocalOpenTime().plusMinutes(duration);
        //全部准备完毕或者超时了
        if (isAllReady || ServerConstants.getCurrentTimeLocalDateTime().isAfter(readyEndTime)) {
            for (Long zoneId : warZoneIdSet) {
                //获取战区所有服务器
                List<Integer> zoneSeverList = LeagueDuelHelper.getZoneSever(zoneId);
                List<LeagueDuelRankNodeInfo> zoneLeagueList = new ArrayList<>();
                for (Integer serverId : zoneSeverList) {
                    LeagueDuelRankInfo leagueDuelRankInfo = leagueWorldRedisData.getJoinLeagueMap().getOrDefault(serverId, new LeagueDuelRankInfo());
                    zoneLeagueList.addAll(leagueDuelRankInfo.getRankNodeInfoList());
                }
                // 战区内报名公会小于两个 活动终止
                if (zoneLeagueList.size() < 2) {
                    //通知服务器活动终止
                    for (Integer serverId : zoneSeverList) {
                        ServerCommandRequest request = CommandRequests.newServerCommandRequest("LeagueDuelGameCommandService.notifyAbort");
                        TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, serverId, request);
                    }
                } else {
                    Collections.sort(zoneLeagueList);
                    Map<Long, Long> matchWorldMap = new HashMap<>();
                    //只有两个的话特殊处理
                    if (zoneLeagueList.size() == 2) {
                        // 添加两条数据 方便查找
                        LeagueDuelRankNodeInfo leftLeague = zoneLeagueList.get(0);
                        LeagueDuelRankNodeInfo rightLeague = zoneLeagueList.get(1);
                        matchWorldMap.put(leftLeague.getLeagueId(), rightLeague.getLeagueId());
                        matchWorldMap.put(rightLeague.getLeagueId(), leftLeague.getLeagueId());
                    } else {
                        //匹配次数
                        int groupCount = zoneLeagueList.size() / 2;
                        for (int i = 1; i <= groupCount; i++) {
                            LeagueDuelRankNodeInfo leftLeague = zoneLeagueList.get(0);
                            //找对手
                            for (int j = 1; j < zoneLeagueList.size(); j++) {
                                LeagueDuelRankNodeInfo rightLeague = zoneLeagueList.get(j);
                                if (leftLeague.getLastMatchLeagueId() != rightLeague.getLeagueId()) {
                                    // 添加两条数据 方便查找
                                    matchWorldMap.put(leftLeague.getLeagueId(), rightLeague.getLeagueId());
                                    matchWorldMap.put(rightLeague.getLeagueId(), leftLeague.getLeagueId());
                                    //移除掉
                                    zoneLeagueList.remove(0);
                                    zoneLeagueList.remove(j);
                                    break;
                                }
                            }
                        }
                    }
                    leagueWorldRedisData.setMatchMap(matchWorldMap);
                }
            }
            Map<Long, Long> matchMap = leagueWorldRedisData.getMatchMap();
            if (!matchMap.isEmpty()) {
                //切换状态
                leagueWorldRedisData.setStatus(DuelStatusType.beforeLineup);
                //通知gs切换到布阵状态
                Map<Integer, LeagueDuelRankInfo> joinLeagueMap = leagueWorldRedisData.getJoinLeagueMap();
                for (Integer serverId : joinLeagueMap.keySet()) {
                    LeagueDuelRankInfo duelRankInfo = joinLeagueMap.get(serverId);
                    //目标服的匹配信息
                    LeagueDuelMatchInfo leagueDuelMatchInfo = new LeagueDuelMatchInfo();
                    for (LeagueDuelRankNodeInfo leagueDuelRankNodeInfo : duelRankInfo.getRankNodeInfoList()) {
                        long leagueId = leagueDuelRankNodeInfo.getLeagueId();
                        leagueDuelMatchInfo.getMatchMap().put(leagueId, matchMap.get(leagueId));
                    }
                    Object[] params = new Object[2];
                    params[0] = DateTimeUtil.toMillis(leagueWorldRedisData.getLocalOpenTime());
                    params[1] = PbUtilCompress.encode(leagueDuelMatchInfo);
                    ServerCommandRequest request = CommandRequests.newServerCommandRequest("LeagueDuelGameCommandService.notifyReadyFinish");
                    TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, serverId, request, params);
                }
            } else {
                changeStatus(DuelStatusType.abort);
            }
        }
    }

    @Override
    public LocalDateTime getEndTime() {
        return LeagueDuelWorldStatusManager.getInstance().getLeagueWorldRedisData().getLocalOpenTime();
    }


}

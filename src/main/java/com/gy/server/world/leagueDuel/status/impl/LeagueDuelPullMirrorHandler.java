package com.gy.server.world.leagueDuel.status.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.leagueDuel.LeagueDuelService;
import com.gy.server.game.leagueDuel.status.DuelNodeType;
import com.gy.server.game.player.Player;
import com.gy.server.world.leagueDuel.status.ILeagueDuelWorldStatusHandler;
import com.gy.server.world.leagueDuel.status.LeagueDuelWorldStatusManager;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.DuelStatusType;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.LeagueDuelRankInfo;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.LeagueDuelWorldRedisData;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 拉取镜像
 *
 * <AUTHOR> - [Created on 2023/5/26 14:52]
 */
public class LeagueDuelPullMirrorHandler implements ILeagueDuelWorldStatusHandler {
    @Override
    public DuelStatusType handleStatus() {
        return DuelStatusType.pullMirror;
    }

    @Override
    public void handler(LeagueDuelWorldRedisData leagueWorldRedisData) {
        boolean isAllReady = true;
        Map<Integer, LeagueDuelRankInfo> joinLeagueMap = leagueWorldRedisData.getJoinLeagueMap();
        for (Integer serverId : joinLeagueMap.keySet()) {
            int mainServer = Player.getMainServer(serverId);
            if (!leagueWorldRedisData.getLineupFinishSet().contains(Player.getMainServer(mainServer))) {
                isAllReady = false;
                break;
            }
        }
        //加10秒保底 超过10秒就切状态 防止镜像一直没上报成功
        LocalDateTime lineupEndTime = getEndTime().plusSeconds(10);
        //全部准备完毕或者超时时间10秒
        if (isAllReady || ServerConstants.getCurrentTimeLocalDateTime().isAfter(lineupEndTime)) {
            //切换状态
            changeStatus(DuelStatusType.beforeBattle);
        }
    }


    @Override
    public LocalDateTime getEndTime() {
        Integer duration = LeagueDuelService.getConstant().getOnTimeMap().get(DuelNodeType.lineup).getRight();
        return LeagueDuelWorldStatusManager.getInstance().getLeagueWorldRedisData().getLocalOpenTime().plusMinutes(duration);
    }
}

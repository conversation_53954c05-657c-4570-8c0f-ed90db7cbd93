package com.gy.server.world.leagueDuel.status.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.leagueDuel.LeagueDuelService;
import com.gy.server.game.leagueDuel.status.DuelNodeType;
import com.gy.server.game.player.Player;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.gy.server.world.leagueDuel.status.ILeagueDuelWorldStatusHandler;
import com.gy.server.world.leagueDuel.status.LeagueDuelWorldStatusManager;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 结算状态
 *
 * <AUTHOR> - [Created on 2023/5/29 13:19]
 */
public class LeagueDuelOverHandler implements ILeagueDuelWorldStatusHandler {
    @Override
    public DuelStatusType handleStatus() {
        return DuelStatusType.over;
    }

    @Override
    public void handler(LeagueDuelWorldRedisData leagueWorldRedisData) {
        boolean isAllReady = true;
        Map<Integer, LeagueDuelRankInfo> joinLeagueMap = leagueWorldRedisData.getJoinLeagueMap();
        for (Integer serverId : joinLeagueMap.keySet()) {
            int mainServer = Player.getMainServer(serverId);
            if (!leagueWorldRedisData.getResultMap().containsKey(Player.getMainServer(mainServer))) {
                isAllReady = false;
                break;
            }
        }
        //加10秒保底 超过10秒就切状态 防止镜像一直没上报成功
        LocalDateTime resultEndTime = getEndTime().plusSeconds(10);
        //全部准备完毕或者超时时间10秒
        if (isAllReady || ServerConstants.getCurrentTimeLocalDateTime().isAfter(resultEndTime)) {
            Map<Long, DuelOverResultInfo> resultInfoMap = new HashMap<>();
            for (DuelOverResultMapInfo duelOverResultMapInfo : leagueWorldRedisData.getResultMap().values()) {
                resultInfoMap.putAll(duelOverResultMapInfo.getResultInfoMap());
            }
            //设置结算结果
            Set<Long> finishLeagueSet = new HashSet<>();
            for (DuelOverResultInfo duelOverResultInfo : resultInfoMap.values()) {
                long leagueId = duelOverResultInfo.getLeagueId();
                if (finishLeagueSet.contains(leagueId)) {
                    continue;
                }
                Long targetLeagueId = leagueWorldRedisData.getMatchMap().get(leagueId);
                DuelOverResultInfo targetResultInfo = resultInfoMap.get(targetLeagueId);
                //左方是否胜利
                boolean leftWin = isLeftWin(duelOverResultInfo, targetResultInfo);
                duelOverResultInfo.setWin(leftWin);
                targetResultInfo.setWin(!leftWin);
                finishLeagueSet.add(leagueId);
                finishLeagueSet.add(targetLeagueId);
            }
            //下发结算结果
            for (Map.Entry<Integer, LeagueDuelRankInfo> entry : joinLeagueMap.entrySet()) {
                Integer serverId = entry.getKey();
                //存放的是敌方数据
                DuelOverResultMapInfo duelOverResultMapInfo = new DuelOverResultMapInfo();
                for (LeagueDuelRankNodeInfo leagueDuelRankNodeInfo : entry.getValue().getRankNodeInfoList()) {
                    long leagueId = leagueDuelRankNodeInfo.getLeagueId();
                    Long targetLeagueId = leagueWorldRedisData.getMatchMap().get(leagueId);
                    DuelOverResultInfo tarResultInfo = resultInfoMap.getOrDefault(targetLeagueId, new DuelOverResultInfo(leagueId));
                    duelOverResultMapInfo.getResultInfoMap().put(leagueId, tarResultInfo);
                }
                //下发游戏服
                Object[] params = new Object[1];
                params[0] = PbUtilCompress.encode(duelOverResultMapInfo);
                ServerCommandRequest request = CommandRequests.newServerCommandRequest("LeagueDuelGameCommandService.notifyResult");
                TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, serverId, request, params);
            }
            //设置下个阶段开启时间并切换状态
            changeStatus(DuelStatusType.sendReward, getEndTime());
        }
    }

    /**
     * 是否左方胜利
     * 先比较星级，星级相同比较星级更新时间，星级更新时间相同比较公会ID
     *
     * @param left  左方
     * @param right 右方
     * @return true 左方胜利
     */
    private boolean isLeftWin(DuelOverResultInfo left, DuelOverResultInfo right) {
        int leftStar = left.getStar();
        int rightStar = right.getStar();
        if (leftStar == rightStar) {
            long leftLastStarUpdateTime = left.getLastStarUpdateTime();
            long rightLastStarUpdateTime = right.getLastStarUpdateTime();
            if (leftLastStarUpdateTime == rightLastStarUpdateTime) {
                long leftLeagueId = left.getLeagueId();
                long rightLeagueId = right.getLeagueId();
                return leftLeagueId > rightLeagueId;
            } else {
                return leftLastStarUpdateTime < rightLastStarUpdateTime;
            }
        } else {
            return leftStar > rightStar;
        }
    }

    @Override
    public LocalDateTime getEndTime() {
        Integer duration = LeagueDuelService.getConstant().getOnTimeMap().get(DuelNodeType.over).getRight();
        return LeagueDuelWorldStatusManager.getInstance().getLeagueWorldRedisData().getLocalOpenTime().plusMinutes(duration);
    }
}

package com.gy.server.world.leagueDuel.status.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.leagueDuel.LeagueDuelService;
import com.gy.server.game.leagueDuel.status.DuelNodeType;
import com.gy.server.world.leagueDuel.status.ILeagueDuelWorldStatusHandler;
import com.gy.server.world.leagueDuel.status.LeagueDuelWorldStatusManager;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.DuelStatusType;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.LeagueDuelWorldRedisData;

import java.time.LocalDateTime;

/**
 * 发奖之前阶段(结算完成未到发奖阶段)
 *
 * <AUTHOR> - [Created on 2023/6/6 13:33]
 */
public class LeagueDuelBeforeSendRewardHandler implements ILeagueDuelWorldStatusHandler {
    @Override
    public DuelStatusType handleStatus() {
        return DuelStatusType.beforeSendReward;
    }

    @Override
    public void handler(LeagueDuelWorldRedisData leagueWorldRedisData) {
        LocalDateTime beforeSendEndTime = getEndTime();
        //等待时间结束
        if (ServerConstants.getCurrentTimeLocalDateTime().isAfter(beforeSendEndTime)) {
            //设置下个阶段开启时间并切换状态
            changeStatus(DuelStatusType.sendReward, beforeSendEndTime);
        }
    }

    @Override
    public LocalDateTime getEndTime() {
        Integer duration = LeagueDuelService.getConstant().getOnTimeMap().get(DuelNodeType.over).getRight();
        return LeagueDuelWorldStatusManager.getInstance().getLeagueWorldRedisData().getLocalOpenTime().plusMinutes(duration);
    }
}

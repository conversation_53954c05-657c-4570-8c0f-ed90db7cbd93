package com.gy.server.world.leagueDuel;

import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.thread.AbstractRunner;
import com.gy.server.game.leagueDuel.status.LeagueDuelStatusManager;
import com.gy.server.utils.time.DateTimeUtil;
import com.gy.server.world.leagueDuel.status.LeagueDuelWorldStatusManager;

public class LeagueDuelWorldManager  extends AbstractRunner {

    LeagueDuelWorldManager(){}
    private static LeagueDuelWorldManager instance = new LeagueDuelWorldManager();
    public static LeagueDuelWorldManager getInstance(){
        return instance;
    }

    @Override
    public String getRunnerName() {
        return "LeagueDuelWorldManager";
    }

    @Override
    protected void subRunnerExecute() throws Exception {
        if (CommonUtils.isMainWorldServer()) {
            LeagueDuelWorldStatusManager.getInstance().worldHandler();
        }
    }

    @Override
    public long getRunnerInterval() {
        return DateTimeUtil.MillisOfSecond;
    }

    public void shutdown() {
        if (CommonUtils.isMainWorldServer()) {
            LeagueDuelWorldStatusManager.getInstance().save();
        }
    }

}

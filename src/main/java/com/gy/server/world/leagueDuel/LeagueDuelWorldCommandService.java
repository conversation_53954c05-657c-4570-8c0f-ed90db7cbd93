package com.gy.server.world.leagueDuel;

import com.gy.server.annotation.MessageMethod;
import com.gy.server.annotation.MessageServiceBean;
import com.gy.server.core.MessageServerType;
import com.gy.server.core.MethodInvokeType;
import com.gy.server.core.command.CommandRequestParams;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.gy.server.world.leagueDuel.status.LeagueDuelWorldStatusManager;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.DuelOverResultMapInfo;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.LeagueDuelRankInfo;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.LeagueDuelWorldRedisData;

/**
 * 帮派对决消息-world
 */
@MessageServiceBean(description = "帮派对决消息", messageServerType = MessageServerType.world)
public class LeagueDuelWorldCommandService {

    /**
     * 上报帮派数据
     */
    @MessageMethod(description = "上报帮派数据", invokeType = MethodInvokeType.async)
    private static void uploadLeague(ServerCommandRequest request, CommandRequestParams params) {
        byte[] bytes = params.getParam(0);
        LeagueDuelRankInfo duelRankInfo = PbUtilCompress.decode(LeagueDuelRankInfo.class, bytes);
        LeagueDuelWorldRedisData leagueWorldRedisData = LeagueDuelWorldStatusManager.getInstance().getLeagueWorldRedisData();
        leagueWorldRedisData.getJoinLeagueMap().put(request.getServerId(), duelRankInfo);
    }

    /**
     * 布防完成
     */
    @MessageMethod(description = "布防完成", invokeType = MethodInvokeType.async)
    private static void lineupFinish(ServerCommandRequest request, CommandRequestParams params) {
        LeagueDuelWorldRedisData leagueWorldRedisData = LeagueDuelWorldStatusManager.getInstance().getLeagueWorldRedisData();
        leagueWorldRedisData.getLineupFinishSet().add(request.getServerId());
    }

    /**
     * 上报结算结果
     */
    @MessageMethod(description = "上报结算结果", invokeType = MethodInvokeType.async)
    private static void uploadDuelResult(ServerCommandRequest request, CommandRequestParams params) {
        byte[] bytes = params.getParam(0);
        DuelOverResultMapInfo duelOverResultMapInfo = PbUtilCompress.decode(DuelOverResultMapInfo.class, bytes);
        LeagueDuelWorldRedisData leagueWorldRedisData = LeagueDuelWorldStatusManager.getInstance().getLeagueWorldRedisData();
        leagueWorldRedisData.getResultMap().put(request.getServerId(), duelOverResultMapInfo);
    }


}

package com.gy.server.world.leagueDuel.status.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.leagueDuel.LeagueDuelService;
import com.gy.server.game.leagueDuel.status.DuelNodeType;
import com.gy.server.world.leagueDuel.status.ILeagueDuelWorldStatusHandler;
import com.gy.server.world.leagueDuel.status.LeagueDuelWorldStatusManager;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.DuelStatusType;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.LeagueDuelWorldRedisData;

import java.time.LocalDateTime;

/**
 * 结束数据清理阶段
 *
 * <AUTHOR> - [Created on 2023/6/6 13:33]
 */
public class LeagueDuelEndAndClearHandler implements ILeagueDuelWorldStatusHandler {
    @Override
    public DuelStatusType handleStatus() {
        return DuelStatusType.endAndClear;
    }

    @Override
    public void handler(LeagueDuelWorldRedisData leagueWorldRedisData) {
        LocalDateTime now = ServerConstants.getCurrentTimeLocalDateTime();
        if (now.isBefore(leagueWorldRedisData.getLocalOpenTime())) {
            return;
        }
        leagueWorldRedisData.getJoinLeagueMap().clear();
        leagueWorldRedisData.getWarZoneIdSet().clear();
        leagueWorldRedisData.getMatchMap().clear();
        leagueWorldRedisData.getLineupFinishSet().clear();
        leagueWorldRedisData.getResultMap().clear();
        //未开启
        changeStatus(DuelStatusType.noStart, calcOpenTime());
    }

    @Override
    public LocalDateTime getEndTime() {
        Integer duration = LeagueDuelService.getConstant().getOnTimeMap().get(DuelNodeType.endAndClear).getRight();
        return LeagueDuelWorldStatusManager.getInstance().getLeagueWorldRedisData().getLocalOpenTime().plusMinutes(duration);
    }
}

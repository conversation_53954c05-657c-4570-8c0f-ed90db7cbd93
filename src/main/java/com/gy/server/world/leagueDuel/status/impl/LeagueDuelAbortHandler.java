package com.gy.server.world.leagueDuel.status.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.leagueDuel.LeagueDuelGlobalData;
import com.gy.server.world.leagueDuel.status.ILeagueDuelWorldStatusHandler;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.DuelStatusType;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.LeagueDuelWorldRedisData;

import java.time.LocalDateTime;

/**
 * 终止状态  由于帮派数据不足
 *
 * <AUTHOR> - [Created on 2023/5/29 13:19]
 */
public class LeagueDuelAbortHandler implements ILeagueDuelWorldStatusHandler {
    @Override
    public DuelStatusType handleStatus() {
        return DuelStatusType.abort;
    }

    @Override
    public void handler(LeagueDuelWorldRedisData leagueWorldRedisData) {
        changeStatus(DuelStatusType.noStart, calcOpenTime());
    }

    @Override
    public LocalDateTime getEndTime() {
        return ServerConstants.getCurrentTimeLocalDateTime();
    }
}

package com.gy.server.world.leagueDuel.status.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.leagueDuel.LeagueDuelService;
import com.gy.server.game.leagueDuel.status.DuelNodeType;
import com.gy.server.world.leagueDuel.status.ILeagueDuelWorldStatusHandler;
import com.gy.server.world.leagueDuel.status.LeagueDuelWorldStatusManager;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.DuelStatusType;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.LeagueDuelWorldRedisData;

import java.time.LocalDateTime;

/**
 * 布阵阶段 下个阶段拉取公会内玩家数据
 *
 * <AUTHOR> - [Created on 2023/5/26 14:25]
 */
public class LeagueDuelLineupHandler implements ILeagueDuelWorldStatusHandler {
    @Override
    public DuelStatusType handleStatus() {
        return DuelStatusType.lineup;
    }

    @Override
    public void handler(LeagueDuelWorldRedisData leagueWorldRedisData) {
        //无需处理 等待时间结束就行
        LocalDateTime now = ServerConstants.getCurrentTimeLocalDateTime();
        LocalDateTime lineupEndTime = getEndTime();
        if (!now.isBefore(lineupEndTime)) {
            changeStatus(DuelStatusType.pullMirror);
        }
    }

    @Override
    public LocalDateTime getEndTime() {
        Integer duration = LeagueDuelService.getConstant().getOnTimeMap().get(DuelNodeType.lineup).getRight();
        return LeagueDuelWorldStatusManager.getInstance().getLeagueWorldRedisData().getLocalOpenTime().plusMinutes(duration);
    }
}

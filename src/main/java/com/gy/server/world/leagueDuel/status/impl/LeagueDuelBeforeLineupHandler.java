package com.gy.server.world.leagueDuel.status.impl;

import com.gy.server.core.Configuration;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.leagueDuel.LeagueDuelGlobalData;
import com.gy.server.game.leagueDuel.LeagueDuelService;
import com.gy.server.game.leagueDuel.status.DuelNodeType;
import com.gy.server.world.leagueDuel.status.ILeagueDuelWorldStatusHandler;
import com.gy.server.world.leagueDuel.status.LeagueDuelWorldStatusManager;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.DuelStatusType;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.LeagueDuelWorldRedisData;

import java.time.LocalDateTime;

/**
 * 布阵前阶段(准备完成等待布阵)
 *
 * <AUTHOR> - [Created on 2023/5/26 14:25]
 */
public class LeagueDuelBeforeLineupHandler implements ILeagueDuelWorldStatusHandler {
    @Override
    public DuelStatusType handleStatus() {
        return DuelStatusType.beforeLineup;
    }

    @Override
    public void handler(LeagueDuelWorldRedisData leagueWorldRedisData) {
        if (readyIsEnd()) {
            //设置下个阶段开启时间并切换状态
            changeStatus(DuelStatusType.lineup, getEndTime());
        }
    }

    @Override
    public LocalDateTime getEndTime() {
        Integer duration = LeagueDuelService.getConstant().getOnTimeMap().get(DuelNodeType.ready).getRight();
        if (Configuration.serverType.isGame()) {
            LeagueDuelGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueDuel);
            return globalData.getOpenTime().plusMinutes(duration);
        } else {
            return LeagueDuelWorldStatusManager.getInstance().getLeagueWorldRedisData().getLocalOpenTime().plusMinutes(duration);
        }
    }


    /**
     * 准备阶段是否结束
     *
     * @return true 结束
     */
    private boolean readyIsEnd() {
        LocalDateTime now = ServerConstants.getCurrentTimeLocalDateTime();
        LocalDateTime readyEndTime = getEndTime();
        return !now.isBefore(readyEndTime);
    }
}

package com.gy.server.world.leagueDuel.status.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.world.leagueDuel.status.ILeagueDuelWorldStatusHandler;
import com.gy.server.world.leagueDuel.status.LeagueDuelWorldStatusManager;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.DuelStatusType;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.LeagueDuelWorldRedisData;

import java.time.LocalDateTime;

/**
 * 帮派对决检查是否开启跨服玩法
 *
 * <AUTHOR> - [Created on 2023/5/25 14:50]
 */
public class LeagueDuelCheckHandler implements ILeagueDuelWorldStatusHandler {
    @Override
    public DuelStatusType handleStatus() {
        return DuelStatusType.checkCross;
    }

    @Override
    public void handler(LeagueDuelWorldRedisData leagueWorldRedisData) {
        LocalDateTime now = ServerConstants.getCurrentTimeLocalDateTime();
        //TODO 战区规则待确定 先写死
        //判断各个战区内的服务器最大开服天数>=21天 true则通知服务器开启跨服玩法 false本服玩法
        //TODO 先取出所有服务器ID
//        Map<Integer, AreaServer> gameAreasFromRedis = TLBase.getInstance().getRuntimeUtil().getGameAreasFromRedis();
        boolean isOpenCross = false;
//        for (AreaServer areaServer : gameAreasFromRedis.values()) {
//            long openTime = areaServer.getOpenTime();
//            LocalDateTime openDateTime = DateTimeUtil.toLocalDateTime(openTime);
//            long days = Duration.between(openDateTime, now).toDays();
//            if (days >= LeagueDuelService.getConstant().getCrossServer()) {
//                isOpenCross = true;
//                break;
//            }
//        }
//        for (Integer serverId : gameAreasFromRedis.keySet()) {
//            ServerCommandRequest request = CommandRequests.newServerCommandRequest("LeagueDuelGameCommandService.checkCross");
//            TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, serverId, request, isOpenCross);
//        }

        if (isOpenCross) {
            changeStatus(DuelStatusType.ready);
        }else {
            changeStatus(DuelStatusType.abort);
        }

    }

    @Override
    public LocalDateTime getEndTime() {
        return LeagueDuelWorldStatusManager.getInstance().getLeagueWorldRedisData().getLocalOpenTime();
    }
}

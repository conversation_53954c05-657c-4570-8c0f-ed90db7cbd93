package com.gy.server.world.leagueDuel.status;

import com.gy.server.core.ServerConstants;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.ReflectionUtil;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.redis.key.BaseRedisKey;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.DuelStatusType;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.LeagueDuelWorldRedisData;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 帮派对决状态管理类
 *
 * <AUTHOR> - [Created on 2023/5/25 13:37]
 */
public class LeagueDuelWorldStatusManager {
    /**
     * 数据是否从redis初始化
     */
    private boolean isWorldInit = false;
    private static volatile boolean isInit = false;
    /**
     * 内存数据
     */
    private LeagueDuelWorldRedisData leagueWorldRedisData;

    private long nextSaveMillions = ServerConstants.getCurrentTimeMillis();

    private static final Map<DuelStatusType, ILeagueDuelWorldStatusHandler> HANDLERS = new HashMap<>();

    public static void init(){
        if(!isInit){
            isInit = true;
            Set<Class<? extends ILeagueDuelWorldStatusHandler>> classSet = ReflectionUtil.getSubClassSetNoInterfaceAndAbstract(ILeagueDuelWorldStatusHandler.class, "com.gy.server.game.leagueDuel.status.impl");
            for (Class<? extends ILeagueDuelWorldStatusHandler> handler : classSet) {
                try {
                    ILeagueDuelWorldStatusHandler instance = handler.newInstance();
                    HANDLERS.put(instance.handleStatus(), instance);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }

    public void worldHandler() {
        if (!isWorldInit) {
            //初始化数据
            String leagueDataKey = BaseRedisKey.World.league_duel_data.getRedisKey();
            LeagueDuelWorldRedisData leagueWorldRedisData = TLBase.getInstance().getRedisAssistant().getBean(LeagueDuelWorldRedisData.class, leagueDataKey);
            if (Objects.isNull(leagueWorldRedisData)) {
                this.leagueWorldRedisData = new LeagueDuelWorldRedisData();
                TLBase.getInstance().getRedisAssistant().setBean(leagueDataKey, this.leagueWorldRedisData);
            }else {
                this.leagueWorldRedisData = leagueWorldRedisData;
            }
            this.isWorldInit = true;
        }
        if(CollectionUtil.isNotEmpty(HANDLERS)){
            HANDLERS.get(leagueWorldRedisData.getStatus()).handler(leagueWorldRedisData);
        }
        checkAndSaveWorldData();
    }

    /**
     * 检查并保存世界服数据 10秒一次
     */
    private void checkAndSaveWorldData() {
        long nowMillions = ServerConstants.getCurrentTimeMillis();
        if (nowMillions >= nextSaveMillions) {
            save();
            this.nextSaveMillions = nowMillions + DateTimeUtil.MillisOfSecond * 10;
        }
    }

    public void save() {
        String leagueDataKey = BaseRedisKey.World.league_duel_data.getRedisKey();
        TLBase.getInstance().getRedisAssistant().setBean(leagueDataKey, this.leagueWorldRedisData);
    }
    public ILeagueDuelWorldStatusHandler getHandler(DuelStatusType duelStatusType) {
        return HANDLERS.get(duelStatusType);
    }

    private static final LeagueDuelWorldStatusManager instance = new LeagueDuelWorldStatusManager();

    private LeagueDuelWorldStatusManager() {
    }

    public static LeagueDuelWorldStatusManager getInstance() {
        return instance;
    }

    public LeagueDuelWorldRedisData getLeagueWorldRedisData() {
        return leagueWorldRedisData;
    }

}

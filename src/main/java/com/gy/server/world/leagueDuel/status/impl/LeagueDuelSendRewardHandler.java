package com.gy.server.world.leagueDuel.status.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.leagueDuel.LeagueDuelService;
import com.gy.server.game.leagueDuel.status.DuelNodeType;
import com.gy.server.world.leagueDuel.status.ILeagueDuelWorldStatusHandler;
import com.gy.server.world.leagueDuel.status.LeagueDuelWorldStatusManager;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.DuelStatusType;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.LeagueDuelWorldRedisData;

import java.time.LocalDateTime;

/**
 * 发奖状态
 *
 * <AUTHOR> - [Created on 2023/6/6 13:33]
 */
public class LeagueDuelSendRewardHandler implements ILeagueDuelWorldStatusHandler {
    @Override
    public DuelStatusType handleStatus() {
        return DuelStatusType.sendReward;
    }

    @Override
    public void handler(LeagueDuelWorldRedisData leagueWorldRedisData) {
        LocalDateTime now = ServerConstants.getCurrentTimeLocalDateTime();
        if (now.isBefore(leagueWorldRedisData.getLocalOpenTime())) {
            return;
        }
        changeStatus(DuelStatusType.beforeEnd);
    }


    @Override
    public LocalDateTime getEndTime() {
        Integer duration = LeagueDuelService.getConstant().getOnTimeMap().get(DuelNodeType.sendReward).getRight();
        return LeagueDuelWorldStatusManager.getInstance().getLeagueWorldRedisData().getLocalOpenTime().plusMinutes(duration);
    }
}

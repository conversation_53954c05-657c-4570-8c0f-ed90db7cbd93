package com.gy.server.world.leagueDuel.status;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.log.CommonLogger;
import com.gy.server.game.leagueDuel.LeagueDuelService;
import com.gy.server.game.leagueDuel.status.DuelNodeType;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.DuelStatusType;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.LeagueDuelWorldRedisData;
import org.apache.commons.lang3.tuple.Pair;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * 帮派对决-状态处理器
 *
 * <AUTHOR> - [Created on 2023/5/25 13:38]
 */
public interface ILeagueDuelWorldStatusHandler {

    /**
     * 可以处理哪个状态
     */
    DuelStatusType handleStatus();

    void handler(LeagueDuelWorldRedisData leagueWorldRedisData);

    /**
     * 获取阶段结束时间
     */
    LocalDateTime getEndTime();

    /**
     * 状态切换
     *
     * @param nextStatus 下个状态
     * @param openTime   下个状态开启时间
     */
    default void changeStatus(DuelStatusType nextStatus, LocalDateTime openTime, boolean isChangeOpenTime) {
        LeagueDuelWorldRedisData leagueWorldRedisData = LeagueDuelWorldStatusManager.getInstance().getLeagueWorldRedisData();
        leagueWorldRedisData.setStatus(nextStatus);
        if (isChangeOpenTime) {
            leagueWorldRedisData.setLocalOpenTime(openTime);
        }
        LocalDateTime startTime = leagueWorldRedisData.getLocalOpenTime();
        CommonLogger.info(String.format("帮派对决状态切换：%s -> %s, 开始时间:%s", handleStatus(), nextStatus, startTime));
    }

    /**
     * 状态切换
     *
     * @param nextStatus 下个状态
     * @param openTime   下个状态开启时间
     */
    default void changeStatus(DuelStatusType nextStatus, LocalDateTime openTime) {
        changeStatus(nextStatus, openTime, true);
    }

    /**
     * 状态切换
     *
     * @param nextStatus 下个状态
     */
    default void changeStatus(DuelStatusType nextStatus) {
        changeStatus(nextStatus, null, false);
    }

    default LocalDateTime calcOpenTime() {
        LocalDateTime now = ServerConstants.getCurrentTimeLocalDateTime();
        List<Integer> onTimeWeekList = LeagueDuelService.getConstant().getOnTimeWeekList();
        LocalDateTime newOpenTime = ServerConstants.getCurrentTimeLocalDateTime();
        Pair<LocalTime, Integer> pairTime = LeagueDuelService.getConstant().getOnTimeMap().get(DuelNodeType.ready);
        LocalTime configOpenTime = pairTime.getLeft().withSecond(0).withNano(0);
        boolean isOpen = false;
        int nowDayOfWeek = now.getDayOfWeek().getValue();
        for (Integer timeWeek : onTimeWeekList) {
            if (nowDayOfWeek < timeWeek) {
                newOpenTime = now.plusDays(timeWeek - nowDayOfWeek).with(configOpenTime);
                isOpen = true;
                break;
            } else if (nowDayOfWeek == timeWeek) {
                // 当前时间小于当天开启时间 则开启时间就是今天
                if (now.toLocalTime().isBefore(configOpenTime)) {
                    newOpenTime = now.with(configOpenTime);
                    isOpen = true;
                    break;
                }
            }
        }
        // 本周未开启 下周开启
        if (!isOpen) {
            Integer timeWeek = onTimeWeekList.get(0);
            //下周配置开启
            newOpenTime = now.plusWeeks(1).plusDays(timeWeek - nowDayOfWeek).with(configOpenTime);
        }
        return newOpenTime;
    }

}

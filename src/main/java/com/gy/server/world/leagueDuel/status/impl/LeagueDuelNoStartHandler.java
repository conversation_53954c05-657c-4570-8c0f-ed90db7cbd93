package com.gy.server.world.leagueDuel.status.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.world.leagueDuel.status.ILeagueDuelWorldStatusHandler;
import com.gy.server.world.leagueDuel.status.LeagueDuelWorldStatusManager;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.DuelStatusType;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.LeagueDuelWorldRedisData;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 帮派对决未开始
 *
 * <AUTHOR> - [Created on 2023/5/25 14:50]
 */
public class LeagueDuelNoStartHandler implements ILeagueDuelWorldStatusHandler {
    @Override
    public DuelStatusType handleStatus() {
        return DuelStatusType.noStart;
    }

    @Override
    public void handler(LeagueDuelWorldRedisData leagueWorldRedisData) {
        if (Objects.isNull(leagueWorldRedisData.getLocalOpenTime())) {
            leagueWorldRedisData.setLocalOpenTime(calcOpenTime());
        }
        LocalDateTime now = ServerConstants.getCurrentTimeLocalDateTime();
        if (!now.isBefore(leagueWorldRedisData.getLocalOpenTime())) {
            changeStatus(DuelStatusType.checkCross);
        }
    }


    @Override
    public LocalDateTime getEndTime() {
        return LeagueDuelWorldStatusManager.getInstance().getLeagueWorldRedisData().getLocalOpenTime();
    }
}

package com.gy.server.world.tournament;

import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.Configuration;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.core.thread.AbstractRunner;
import com.gy.server.game.text.Text;
import com.gy.server.game.tournament.TournamentService;
import com.gy.server.game.tournament.bean.TournamentMatchEnum;
import com.gy.server.game.tournament.template.TournamentConst;
import com.gy.server.game.tournament.template.TournamentMatchingRuleTemplate;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.time.DateTimeUtil;
import com.gy.server.world.tournament.bean.TournamentPlayerMatchInfo;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import org.apache.commons.lang3.tuple.Pair;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 实时pvp管理类
 *
 * <AUTHOR> 2024/10/11 9:29
 **/
public class TournamentWorldManager extends AbstractRunner {

    /***********************************world***********************************************/

    /**
     * 匹配队列  内存
     *  key:星级 分0~61星，62星及以上两段，两段之间互不匹配
     *  value:queue
     */
    private volatile Map<Integer, CopyOnWriteArrayList<Long>> matchQueueMap = new ConcurrentHashMap<>();

    /**
     * 匹配信息 内存
     *  key：玩家id
     *  value：
     */
    private volatile Map<Long, TournamentPlayerMatchInfo> matchInfoMap = new ConcurrentHashMap<>();
    /*************************************************************************************/

    @Override
    public String getRunnerName() {
        return "TournamentWorldManager";
    }

    @Override
    protected void subRunnerExecute() throws Exception {
        if(CommonUtils.isMainWorldServer() && Configuration.isWorldServer()){
            if(!matchInfoMap.isEmpty()){
                checkMatchPoolMatch();
            }
        }
    }

    /**
     * 检查匹配池匹配
     */
    private void checkMatchPoolMatch(){
        List<Integer> stars = new ArrayList<>(matchQueueMap.keySet());
        Collections.sort(stars);

        //王者最小星数
        int minKingStar = TournamentService.getTournamentConst().king + 1;
        TournamentConst tConst = TournamentService.getTournamentConst();

        for (int star : stars) {
            TournamentMatchEnum matchEnum = star >= minKingStar ? TournamentMatchEnum.callUp : TournamentMatchEnum.mate;

            CopyOnWriteArrayList<Long> starPlayerIds = matchQueueMap.get(star);
            if(CollectionUtil.isEmpty(starPlayerIds)){
                continue;
            }

            //待移除玩家id列表
            List<Long> removePlayerIds = new ArrayList<>();
            //待匹配玩家id列表
            List<Long> matchPoolPlayerIds = new ArrayList<>();
            //检查本星内是否能匹配完成
            for (long playerId : starPlayerIds) {
                TournamentPlayerMatchInfo matchInfo = matchInfoMap.get(playerId);
                if(matchInfo == null){
                    matchQueueMap.getOrDefault(star, new CopyOnWriteArrayList<>()).remove(playerId);
                    continue;
                }

                //检查超时
                if((ServerConstants.getCurrentTimeMillis() - matchInfo.getExpectMatchTime()) / DateTimeUtil.MillisOfSecond >= tConst.unMatch){
                    //取消匹配，通知匹配失败
                    matchResultNotifyGs(matchInfo, null, matchEnum, Text.匹配超时);
                    removePlayerIds.add(playerId);
                    continue;
                }

                //到达匹配机器人时间的匹配机器人A
                if(ServerConstants.getCurrentTimeMillis() > matchInfo.getMatchRobotTime() && !matchInfo.isRobotB() && star < minKingStar){
                    matchInfo.setRobotA(true);
                    matchResultNotifyGs(matchInfo, null, matchEnum);
                    removePlayerIds.add(playerId);
                    continue;
                }

                //已达到匹配时间且玩家处于失败保护状态，确定匹配机器人B
//                long now = ServerConstants.getCurrentTimeMillis();
                if(/*now >= matchInfo.getExpectMatchTime() &&*/ matchInfo.isRobotB() && star < minKingStar){
                    matchResultNotifyGs(matchInfo, null, matchEnum);
                    removePlayerIds.add(playerId);
                    continue;
                }

                if(!matchInfo.isRobotB()){
                    matchPoolPlayerIds.add(playerId);
                }
            }

            //同星级匹配
            while (matchPoolPlayerIds.size() >= 2){
                long atkPlayerId = matchPoolPlayerIds.get(0);
                long defPlayerId = matchPoolPlayerIds.get(1);
                matchResultNotifyGs(atkPlayerId, defPlayerId, matchEnum);

                matchPoolPlayerIds.remove(atkPlayerId);
                matchPoolPlayerIds.remove(defPlayerId);
                removePlayerIds.add(atkPlayerId);
                removePlayerIds.add(defPlayerId);
            }

            starPlayerIds.removeAll(removePlayerIds);
            removePlayerIds.forEach(id -> matchInfoMap.remove(id));

            if(CollectionUtil.isEmpty(starPlayerIds) || CollectionUtil.isEmpty(matchPoolPlayerIds)){
                continue;
            }
            removePlayerIds.clear();

            if (matchPoolPlayerIds.isEmpty()) {
                continue;
            }

            boolean isKing = star >= minKingStar;
            TournamentMatchingRuleTemplate matchingRuleTemplate = TournamentService.getMatchingRuleTemplateByStar(star);
            int initChangeStar = matchingRuleTemplate.initialRange;

            //对每个玩家扩大对应匹配池
            for (long matchingPlayerId : matchPoolPlayerIds) {
                TournamentPlayerMatchInfo matchInfo = matchInfoMap.get(matchingPlayerId);
                long now = ServerConstants.getCurrentTimeMillis();
                int changeMaxMatchStar = initChangeStar + now > matchInfo.getExpectMatchTime() ? (int) ((now - matchInfo.getExpectMatchTime()) / (tConst.expandTime * DateTimeUtil.MillisOfSecond) * tConst.eachEnlargement) : 0;
                changeMaxMatchStar = Math.min(changeMaxMatchStar, matchingRuleTemplate.radiusMax);

                int minMatchStar = Math.max(matchInfo.getStar() - changeMaxMatchStar, isKing ? minKingStar : 0);
                int maxMatchStar = isKing ? star + changeMaxMatchStar : Math.min(star + changeMaxMatchStar, minKingStar - 1);

                Pair<Integer, Long> pair = selectMatchPlayerId(minMatchStar, maxMatchStar, matchingPlayerId);
                if (pair != null) {
                    matchResultNotifyGs(matchingPlayerId, pair.getRight(), matchEnum);
                    removePlayerIds.add(matchingPlayerId);
                    matchQueueMap.getOrDefault(pair.getLeft(), new CopyOnWriteArrayList<>()).remove(pair.getRight());
                    matchInfoMap.remove(pair.getRight());
                }
            }

            starPlayerIds.removeAll(removePlayerIds);
            removePlayerIds.forEach(id -> matchInfoMap.remove(id));
            matchPoolPlayerIds.removeAll(removePlayerIds);
        }
    }

    private Pair<Integer, Long> selectMatchPlayerId(int minMatchStar, int maxMatchStar, long matchingPlayerId){
        //寻找匹配对象
        for (int i = minMatchStar; i <= maxMatchStar; i++){
            CopyOnWriteArrayList<Long> queuePlayerIds = matchQueueMap.get(i);
            if(CollectionUtil.isEmpty(queuePlayerIds)){
                continue;
            }

            for (long queuePlayerId : queuePlayerIds) {
                TournamentPlayerMatchInfo otherMatchInfo = matchInfoMap.get(queuePlayerId);
                if(otherMatchInfo.isRobotB()){
                    continue;
                }

                if(otherMatchInfo.getPlayerId() == matchingPlayerId){
                    continue;
                }
                return Pair.of(i, queuePlayerId);
            }
        }
        return null;
    }

    private void matchResultNotifyGs(long atkPlayerId, long defPlayerId, TournamentMatchEnum matchPattern) {
        matchResultNotifyGs(matchInfoMap.get(atkPlayerId), matchInfoMap.get(defPlayerId), matchPattern, Text.没有异常);
    }

    private void matchResultNotifyGs(TournamentPlayerMatchInfo atk, TournamentPlayerMatchInfo def, TournamentMatchEnum matchPattern){
        matchResultNotifyGs(atk, def, matchPattern, Text.没有异常);
    }

    /**
     * 匹配成功通知gs
     * 匹配方为进攻方，被匹配方为防守方
     * def为空时被匹配方为机器人
     * @param atk          NotNull
     * @param def
     * @param matchPattern
     */
    private void matchResultNotifyGs(TournamentPlayerMatchInfo atk, TournamentPlayerMatchInfo def, TournamentMatchEnum matchPattern, int text) {
        ServerCommandRequest commandRequest = CommandRequests.newServerCommandRequest("TournamentRstCommandService.matchResultDeal");
        TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, atk.getServerId(), commandRequest, text, atk, def, matchPattern, ServerConstants.getCurrentTimeMillis());
    }

    @Override
    public long getRunnerInterval() {
        return DateTimeUtil.MillisOfSecond;
    }

    private static TournamentWorldManager instance = new TournamentWorldManager();

    public static TournamentWorldManager getInstance() {
        return instance;
    }

    public Map<Integer, CopyOnWriteArrayList<Long>> getMatchQueueMap() {
        return matchQueueMap;
    }

    public Map<Long, TournamentPlayerMatchInfo> getMatchInfoMap() {
        return matchInfoMap;
    }

}

package com.gy.server.world.tournament.bean;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.tournament.TournamentService;
import com.gy.server.utils.MathUtil;
import com.gy.server.utils.time.DateTimeUtil;

import java.io.Serializable;

/**
 * 实时pvp玩家匹配信息
 *
 * <AUTHOR> 2024/9/11 17:00
 **/
public class TournamentPlayerMatchInfo implements Serializable {

    private static final long serialVersionUID = 2585148277661678257L;

    /**
     * 玩家id
     */
    private long playerId;

    /**
     * 服务器id
     */
    private int serverId;

    /**
     * 开始时间
     */
    private long start;

    /**
     * 星级
     */
    private int star;

    /**
     * 预计匹配时间
     */
    private long expectMatchTime;

    /**
     * 匹配机器人时间
     * 此时间之前真人匹配，此后安排机器人
     * 王者只匹配活人,此时值为0
     */
    private long matchRobotTime;

    /**
     * 是否是机器人A
     * 超时时匹配
     * 人机A：在战区战力排行榜上拉自身战力上下x名玩家的镜像
     */
    private boolean isRobotA = false;

    /**
     * 是否是机器人B
     * 连败匹配
     * 人机B：在战区战力排行榜拉自身战力-x(万分比)上下x名的玩家镜像
     * true 确定匹配机器人
     */
    private boolean isRobotB = false;

    public TournamentPlayerMatchInfo(long playerId, int serverId, int star, int matchUseTime) {
        this.playerId = playerId;
        this.serverId = serverId;
        this.start = ServerConstants.getCurrentTimeMillis();
        this.star = star;
        this.expectMatchTime = this.start + matchUseTime * DateTimeUtil.MillisOfSecond;

        if(star <= TournamentService.getTournamentConst().king){
            this.matchRobotTime = this.expectMatchTime + MathUtil.randomInt(TournamentService.getTournamentConst().releasePeriod.getRight()) * DateTimeUtil.MillisOfSecond;
        }
    }

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public int getServerId() {
        return serverId;
    }

    public void setServerId(int serverId) {
        this.serverId = serverId;
    }

    public long getStart() {
        return start;
    }

    public void setStart(long start) {
        this.start = start;
    }

    public int getStar() {
        return star;
    }

    public void setStar(int star) {
        this.star = star;
    }

    public long getExpectMatchTime() {
        return expectMatchTime;
    }

    public void setExpectMatchTime(long expectMatchTime) {
        this.expectMatchTime = expectMatchTime;
    }

    public long getMatchRobotTime() {
        return matchRobotTime;
    }

    public void setMatchRobotTime(long matchRobotTime) {
        this.matchRobotTime = matchRobotTime;
    }

    public boolean isRobotA() {
        return isRobotA;
    }

    public void setRobotA(boolean robotA) {
        isRobotA = robotA;
    }

    public boolean isRobotB() {
        return isRobotB;
    }

    public void setRobotB(boolean robotB) {
        isRobotB = robotB;
    }
}

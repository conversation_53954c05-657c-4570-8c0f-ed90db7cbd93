package com.gy.server.world.tournament;

import com.gy.server.annotation.MessageMethod;
import com.gy.server.annotation.MessageServiceBean;
import com.gy.server.core.MessageServerType;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.command.CommandRequestParams;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.text.Text;
import com.gy.server.game.tournament.TournamentGlobalData;
import com.gy.server.game.tournament.TournamentService;
import com.gy.server.game.tournament.bean.TournamentPlayerRankInfo;
import com.gy.server.game.tournament.bean.TournamentPositionInfo;
import com.gy.server.game.tournament.bean.TournamentPositionRankInfo;
import com.gy.server.game.tournament.template.TournamentMatchingRuleTemplate;
import com.gy.server.world.tournament.bean.TournamentPlayerMatchInfo;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 实时pvp-world  gs->world
 *
 * <AUTHOR> 2024/9/9 10:01
 **/
@MessageServiceBean(description = "实时pvp消息", messageServerType = MessageServerType.world)
public class TournamentCommandService {

    @MessageMethod(description = "匹配开始")
    private static void matchStart(ServerCommandRequest request, CommandRequestParams params) {
//        Configuration.serverId, player.getPlayerId(), model.getStageWins(), model.getMatchUseTime(), model.getStar().get()
        int serverId = params.getParam(0);
        long playerId = params.getParam(1);
        List<Boolean> stageWins = params.getParam(2);
        int matchUseTime = params.getParam(3);
        int star = params.getParam(4);

        TournamentGlobalData globalData = GlobalDataManager.getData(GlobalDataType.tournament);
        int danId = TournamentService.getDanByStar(star);

        //核对匹配时间
        int hour = ServerConstants.getCurrentTimeLocalDateTime().getHour();
        boolean open = false;
        TournamentMatchingRuleTemplate ruleTemplate = TournamentService.getMatchingRuleMap().get(danId);
        for (Pair<Integer, Integer> openingHour : ruleTemplate.openingHours) {
            if (hour >= openingHour.getLeft() && hour <= openingHour.getRight()) {
                open = true;
            }
        }

        if (!open) {
            //此段位不在开放时间
            request.addCallbackParam(Text.此段位不在开放时间);
        } else {
            TournamentPlayerMatchInfo playerMatchInfo;
            if (TournamentWorldManager.getInstance().getMatchInfoMap().containsKey(playerId)) {
                playerMatchInfo = TournamentWorldManager.getInstance().getMatchInfoMap().get(playerId);
            } else {
                playerMatchInfo = new TournamentPlayerMatchInfo(playerId, serverId, star, matchUseTime);

                //判断是否需要匹配机器人
                if (star <= TournamentService.getTournamentConst().goldMatchingMachine && star <= TournamentService.getTournamentConst().king) {
                    //五输三
                    if (stageWins.size() >= 5) {
                        int failNum = 0;
                        for (Boolean stageWin : stageWins) {
                            if (!stageWin) failNum++;
                        }

                        if (failNum >= 3) {
                            playerMatchInfo.setRobotB(true);
                        }
                    }

                    //连败
                    if (stageWins.size() >= 2) {
                        if (stageWins.get(stageWins.size() - 1) == false && stageWins.get(stageWins.size() - 2) == false) {
                            playerMatchInfo.setRobotB(true);
                        }
                    }
                }
            }

            request.addCallbackParam(Text.没有异常);
            request.addCallbackParam(playerMatchInfo.getStart());
            request.addCallbackParam(globalData.getBattleInfo().getSeasonId());

            TournamentWorldManager.getInstance().getMatchInfoMap().put(playerMatchInfo.getPlayerId(), playerMatchInfo);
            TournamentWorldManager.getInstance().getMatchQueueMap().computeIfAbsent(playerMatchInfo.getStar(), list -> new CopyOnWriteArrayList<>()).add(playerMatchInfo.getPlayerId());
        }
    }

    @MessageMethod(description = "匹配结束")
    private static void matchClose(ServerCommandRequest request, CommandRequestParams params){
        long playerId = params.getParam(0);

        TournamentPlayerMatchInfo matchInfo = TournamentWorldManager.getInstance().getMatchInfoMap().get(playerId);
        if(matchInfo != null){
            CopyOnWriteArrayList<Long> list = TournamentWorldManager.getInstance().getMatchQueueMap().get(matchInfo.getStar());
            if(list != null){
                list.remove(playerId);
            }
            TournamentWorldManager.getInstance().getMatchInfoMap().remove(playerId);
        }

        request.addCallbackParam(Text.没有异常);
    }

    @MessageMethod(description = "匹配结束")
    private static void rankUpdate(ServerCommandRequest request, CommandRequestParams params){
        TournamentPlayerRankInfo rankInfo = params.getParam(0);
        TournamentGlobalData globalData = GlobalDataManager.getData(GlobalDataType.tournament);
        globalData.getBattleInfo().changeTournamentPlayerRankInfo(rankInfo);
    }

    @MessageMethod(description = "获取排行榜")
    private static void getRankInfo(ServerCommandRequest request, CommandRequestParams params){
        TournamentPositionInfo selectPositionInfo = params.getParam(0);

        TournamentGlobalData globalData = GlobalDataManager.getData(GlobalDataType.tournament);
        List<TournamentPlayerRankInfo> rankInfos = globalData.getBattleInfo().getRankInfoByPositionInfo(selectPositionInfo);
        request.addCallbackParam(new TournamentPositionRankInfo(rankInfos));
    }

    @MessageMethod(description = "获取赛季id")
    private static void getSeasonId(ServerCommandRequest request, CommandRequestParams params){
        TournamentGlobalData globalData = GlobalDataManager.getData(GlobalDataType.tournament);
        request.addCallbackParam(globalData.getBattleInfo().getSeasonId());
    }
}

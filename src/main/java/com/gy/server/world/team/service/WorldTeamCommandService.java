package com.gy.server.world.team.service;

import com.google.protobuf.AbstractMessage;
import com.gy.server.annotation.MessageMethod;
import com.gy.server.annotation.MessageServiceBean;
import com.gy.server.core.MessageServerType;
import com.gy.server.core.MethodInvokeType;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.command.CommandRequestParams;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.lineup.LineupHelper;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.lineup.bean.LineupBean;
import com.gy.server.game.lineup.bean.LineupInfoBean;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.team.TeamHelper;
import com.gy.server.game.team.TeamManager;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbCommons;
import com.gy.server.packet.PbLineup;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbSpeak;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.gy.server.world.team.base.TeamInfo;
import com.gy.server.world.team.base.TeamMemberInfo;
import com.ttlike.server.tl.baselib.serialize.lineup.LineupDb;

import java.util.*;

/**
 * 世界服组队消息处理
 * <AUTHOR> - [Created on 2022-05-21 16:41]
 */
@MessageServiceBean(description = "组队相关", messageServerType = MessageServerType.world)
public class WorldTeamCommandService {

    @MessageMethod(description = "队伍列表", invokeType = MethodInvokeType.free)
    private static void teamList(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        int teamType = params.getParam(1);
        int page = params.getParam(2);
        int pageSize = params.getParam(3);
        long time = params.getParam(4);
        TeamHelper.commonGetTeamListLogic(playerId, teamType, page, pageSize, time);
    }

    @MessageMethod(description = "加入队伍", invokeType = MethodInvokeType.free)
    private static void teamJoin(ServerCommandRequest request, CommandRequestParams params) {
        long teamId = params.getParam(0);
        int teamTypeId = params.getParam(1);
        TeamMemberInfo memberInfo = params.getParam(2);
        boolean isInvited = params.getParam(3);
        long time = params.getParam(4);
        TeamHelper.commonJoinTeamLogic(teamId, teamTypeId, memberInfo, isInvited, time);

    }

    @MessageMethod(description = "退出队伍", invokeType = MethodInvokeType.free)
    private static void teamExit(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        long teamId = params.getParam(1);
        long time = params.getParam(2);
        TeamHelper.commonExitTeamLogic(playerId, teamId, time);
    }

    @MessageMethod(description = "创建队伍", invokeType = MethodInvokeType.free)
    private static void teamCreate(ServerCommandRequest request, CommandRequestParams params) {
        TeamMemberInfo memberInfo = params.getParam(0);
        TeamInfo teamInfo = params.getParam(1);
        long time = params.getParam(2);
        TeamHelper.commonCreateTeamLogic(memberInfo, teamInfo, time);
    }

    @MessageMethod(description = "销毁队伍", invokeType = MethodInvokeType.free)
    private static void teamDestroy(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        long teamId = params.getParam(1);
        long time = params.getParam(2);
        TeamHelper.commonDestroyTeamLogic(playerId, teamId, time);
    }

    @MessageMethod(description = "队伍匹配", invokeType = MethodInvokeType.free)
    private static void teamMatch(ServerCommandRequest request, CommandRequestParams params) {
        TeamMemberInfo memberInfo = params.getParam(0);
        int teamTypeId = params.getParam(1);
        long leagueId = params.getParam(2);
        long time = params.getParam(3);
        TeamHelper.commonMatchLogic(memberInfo, teamTypeId, leagueId, time);
    }

    @MessageMethod(description = "发布队伍", invokeType = MethodInvokeType.free)
    private static void teamPublish(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        long teamId = params.getParam(1);
        long time = params.getParam(2);
        TeamHelper.commonPublishLogic(playerId, teamId, time);
    }

    @MessageMethod(description = "邀请队伍", invokeType = MethodInvokeType.free)
    private static void teamInvite(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        long teamId = params.getParam(1);
        long invitedId = params.getParam(2);
        boolean isNotOne = params.getParam(3);
        long time = params.getParam(4);
        TeamHelper.commonTeamInviteLogic(playerId, teamId, invitedId, isNotOne, time);
    }

    @MessageMethod(description = "获取已经邀请信息", invokeType = MethodInvokeType.free)
    private static void teamHadInvited(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        long time = params.getParam(1);
        TeamHelper.commonHadInvitedLogic(playerId, time);

    }

    @MessageMethod(description = "队伍广播消息", invokeType = MethodInvokeType.free)
    private static void teamBroadCast(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        long teamId = params.getParam(1);
        String msg = params.getParam(2);
        long time = params.getParam(3);
        TeamHelper.commonBroadCastLogic(playerId, teamId, msg, time);

    }

    @MessageMethod(description = "队伍设置", invokeType = MethodInvokeType.free)
    private static void teamSetUp(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        long teamId = params.getParam(1);
        int minLevel = params.getParam(2);
        int maxLevel = params.getParam(3);
        int publicAuth = params.getParam(4);
        long time = params.getParam(5);
        TeamHelper.commonTeamSetUpLogic(playerId, teamId, minLevel, maxLevel, publicAuth, time);
    }

    @MessageMethod(description = "队伍转让队长", invokeType = MethodInvokeType.free)
    private static void teamTransferLeader(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        long teamId = params.getParam(1);
        long newLeaderId = params.getParam(2);
        long time = params.getParam(3);
        TeamHelper.commonTransferLeaderLogic(playerId, teamId, newLeaderId, time);
    }

    @MessageMethod(description = "队伍准备", invokeType = MethodInvokeType.free)
    private static void teamPrepare(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        long teamId = params.getParam(1);
        boolean isPrepare = params.getParam(2);
        List<PbCommons.HeroUnitCreator> heroUnitCreators = params.getParam(3);
        long time = params.getParam(4);
        TeamHelper.commonPrepareLogic(playerId, teamId, isPrepare, heroUnitCreators, time);
    }

    @MessageMethod(description = "队伍踢人", invokeType = MethodInvokeType.free)
    private static void teamKickOut(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        long teamId = params.getParam(1);
        long targetPlayerId = params.getParam(2);
        long time = params.getParam(3);
        TeamHelper.commonKickOutLogic(playerId, teamId, targetPlayerId, time);
    }

    @MessageMethod(description = "队伍取消匹配", invokeType = MethodInvokeType.free)
    private static void matchCancel(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        long time = params.getParam(1);
        TeamHelper.commonMatchCancelLogic(playerId, true, time);
    }
    @MessageMethod(description = "直接取消匹配", invokeType = MethodInvokeType.free)
    private static void logOutQueue(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        TeamManager.getInstance().removeMatchQueue(playerId);
    }

    @MessageMethod(description = "组队聊天", invokeType = MethodInvokeType.free)
    private static void chat(ServerCommandRequest request, CommandRequestParams params) {
        long teamId = params.getParam(0);
        PbSpeak.SpeakSyncInfo speakSyncInfo = params.getParam(1);

        Set<Long> memberIds = TeamManager.getInstance().getMembers(teamId);
        TeamHelper.batchWorldSendMsg(memberIds, PtCode.SPEAK_MESSAGE_SYNC, ServerConstants.getCurrentTimeMillis(), speakSyncInfo);
    }

    @MessageMethod(description = "组队广播消息", invokeType = MethodInvokeType.free)
    private static void teamBroadcast(ServerCommandRequest request, CommandRequestParams params) {
        long teamId = params.getParam(0);
        int ptCode = params.getParam(1);
        AbstractMessage message = params.getParam(2);
        TeamHelper.teamBroadcast(teamId, ptCode, message);
    }

    @MessageMethod(description = "组队修改布阵", invokeType = MethodInvokeType.free)
    private static void modifyLineup(ServerCommandRequest request, CommandRequestParams params) {
        //跨服组队
        long teamId = params.getParam(0);
        long playerId = params.getParam(1);
        LineupType lineupType = params.getParam(2);
        LineupDb lineupDb = PbUtilCompress.decode(LineupDb.class, params.getParam(3));
        long time = params.getParam(4);
        List<LineupDb.LineupInfoDb> lineupInfos = lineupDb.getLineupInfoDbList();
        int errorCode;
        List<LineupInfoBean> lineupInfoBeanList = LineupInfoBean.readFromDb(lineupInfos);
        PbLineup.LineupStand lineupStand = LineupBean.genLineupStand(lineupType, lineupInfoBeanList);

        List<PbLineup.LineupFormation> lineupFormations = new ArrayList<>();
        for (PbLineup.LineupWithMirrorFormation lineupWithMirrorFormation : lineupStand.getLineupWithMirrorFormationList()) {
            lineupFormations.add(lineupWithMirrorFormation.getLineupFormation());
        }
        errorCode = LineupHelper.teamCheckLineup(playerId, teamId, lineupType, lineupFormations);

        if (errorCode != Text.没有异常) {
            TeamHelper.worldSendMsg(playerId, PtCode.LINEUP_UPDATE_SERVER, time, PbProtocol.LineupUpdateRst.newBuilder().setResult(Text.genServerRstInfo(errorCode)).build());
        } else {
            LineupHelper.updateTeamLineupLogic(playerId, teamId, lineupType, lineupInfos, time);
        }

    }

    @MessageMethod(description = "组队开始", invokeType = MethodInvokeType.free)
    private static void teamStart(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        long teamId = params.getParam(1);
        List<PbCommons.HeroUnitCreator> creators = params.getParam(2);
        long time = params.getParam(3);
        TeamHelper.commonTeamStart(playerId, teamId, creators, time);
    }

    @MessageMethod(description = "组队布阵准备", invokeType = MethodInvokeType.free)
    private static void teamLineupReady(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        long teamId = params.getParam(1);
        long time = params.getParam(2);
        TeamHelper.commonTeamLineupReady(playerId, teamId, time);
    }

    @MessageMethod(description = "简要队伍信息", invokeType = MethodInvokeType.free)
    private static void miniTeamInfo(ServerCommandRequest request, CommandRequestParams params) {
        long teamId = params.getParam(0);
        int teamTypeId = params.getParam(1);
        PbCommons.MiniTeamInfo miniTeamInfo = TeamHelper.genMiniTeamInfo(teamId, teamTypeId);
        request.addCallbackParam(miniTeamInfo);
    }

    @MessageMethod(description = "申请队长", invokeType = MethodInvokeType.free)
    private static void applyLeader(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        long teamId = params.getParam(1);
        long time = params.getParam(2);
        TeamHelper.commonApplyLeader(playerId, teamId, time);
    }

    @MessageMethod(description = "申请队长结果", invokeType = MethodInvokeType.free)
    private static void applyLeaderResult(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        long memberId = params.getParam(1);
        int teamTypeId = params.getParam(2);
        boolean isAgree = params.getParam(3);
        long time = params.getParam(4);
        TeamHelper.commonApplyLeaderResult(playerId, memberId, teamTypeId, isAgree, time);
    }

    @MessageMethod(description = "转让队长结果", invokeType = MethodInvokeType.free)
    private static void transferResult(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        long teamId = params.getParam(1);
        boolean isAgree = params.getParam(2);
        long time = params.getParam(3);
        TeamHelper.commonTransferLeaderResult(playerId, teamId, isAgree, time);
    }

    @MessageMethod(description = "助战", invokeType = MethodInvokeType.free)
    private static void chessAssistBattle(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        long teamId = params.getParam(1);
        boolean IsAssistBattle = params.getParam(2);
        long time = params.getParam(3);
        TeamHelper.commonChessAssistBattle(playerId, teamId, IsAssistBattle, time);
    }

    @MessageMethod(description = "修改队伍站位", invokeType = MethodInvokeType.free)
    private static void modifyTeamIndex(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        long teamId = params.getParam(1);
        int index1 = params.getParam(2);
        int index2 = params.getParam(3);
        long time = params.getParam(4);
        TeamHelper.commonModifyTeamIndex(playerId, teamId, index1, index2, time);
    }

    @MessageMethod(description = "获取队伍信息", invokeType = MethodInvokeType.free)
    private static void getTeamInfo(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        long teamId = params.getParam(1);
        long time = params.getParam(2);
        TeamHelper.commonGetTeamInfo(playerId, teamId, time);
    }

    @MessageMethod(description = "获取组队相关信息", invokeType = MethodInvokeType.free)
    private static void getTeamRelateInfo(ServerCommandRequest request, CommandRequestParams params){
        long id = params.getParam(0);

        long leaderId = 0;
        List<TeamMemberInfo> memberInfoList = new ArrayList<>();

        TeamInfo teamInfo = TeamManager.getInstance().getTeamInfo(id);
        if (teamInfo != null) {
            leaderId = teamInfo.getLeaderId();
            for (Long memberId : TeamManager.getInstance().getMembers(teamInfo.getTeamId())) {
                TeamMemberInfo memberInfo = TeamManager.getInstance().getMemberInfo(memberId);
                if (memberInfo != null) {
                    memberInfoList.add(memberInfo);
                }
            }
        }

        request.addCallbackParam(leaderId);
        request.addCallbackParam(memberInfoList);
    }
}

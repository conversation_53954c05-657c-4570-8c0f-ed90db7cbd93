package com.gy.server.world.team.base;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;
import com.gy.server.core.Configuration;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.team.TeamHelper;
import com.gy.server.game.team.TeamManager;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbTeam;
import com.gy.server.world.activityTeam.bean.TeamTreasureHuntTeamData;

/**
 * 队伍信息
 * <AUTHOR> - [Created on 2022-05-21 15:54]
 */
@ProtobufClass
public class TeamInfo implements Serializable {

    //队伍唯一id
    @Protobuf(order = 1)
    private long teamId;
    //队伍类型id
    @Protobuf(order = 2)
    private int teamTypeId;
    //公开权限
    @Protobuf(order = 3)
    private int publicAuth;
    //最小等级
    @Protobuf(order = 4)
    private int minLevel = -1;
    //最大等级
    @Protobuf(order = 5)
    private int maxLevel = 9999999;
    //创建时间
    @Protobuf(order = 6)
    private long createTime;
    //能否进入队伍
    @Protobuf(order = 7)
    private boolean canEnter;
    //队伍位置标记
    @Protobuf(order = 8)
    private int teamPositions;
    //是否战斗状态
    @Protobuf(order = 9)
    private boolean isBattle;
    //布阵开始时间
    @Protobuf(order = 10)
    private long lineupStartTime;
    //战斗开始时间
    @Protobuf(order = 11)
    private long battleStartTime;
    //布阵状态是否完成
    @Protobuf(order = 12)
    private boolean isFinishLineup;
    //队长id
    @Protobuf(order = 13)
    private long leaderId;
    //申请队长玩家id
    @Protobuf(order = 14)
    private Map<Long, Long> applyLeaderPlayerId = new HashMap<>();
    //转让队长玩家id
    @Protobuf(order = 15)
    private Map<Long, Long> transferLeaderPlayerIds = new HashMap<>();
    //帮派id
    @Protobuf(order = 16)
    private long leagueId;

    /*********************************玩法信息***********************************/
    //圣兽寻宝信息 activityId:信息
    @Protobuf(order = 17)
    private TeamTreasureHuntTeamData teamTreasureHuntTeamData;

    public TeamTreasureHuntTeamData getTeamTreasureHuntTeamInfoNotNull(){
        if(teamTreasureHuntTeamData == null){
            teamTreasureHuntTeamData = new TeamTreasureHuntTeamData();
        }
        return teamTreasureHuntTeamData;
    }

    public long getTeamId() {
        return teamId;
    }

    public void setTeamId(long teamId) {
        this.teamId = teamId;
    }

    public int getTeamTypeId() {
        return teamTypeId;
    }

    public void setTeamTypeId(int teamTypeId) {
        this.teamTypeId = teamTypeId;
    }

    public int getPublicAuth() {
        return publicAuth;
    }

    public void setPublicAuth(int publicAuth) {
        this.publicAuth = publicAuth;
    }

    public int getMinLevel() {
        return minLevel;
    }

    public void setMinLevel(int minLevel) {
        this.minLevel = minLevel;
    }

    public int getMaxLevel() {
        return maxLevel;
    }

    public void setMaxLevel(int maxLevel) {
        this.maxLevel = maxLevel;
    }

    public long getCreateTime() {
        return createTime;
    }

    public long getLineupStartTime(){
        return lineupStartTime;
    }

    public long getBattleStartTime(){
        return battleStartTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public boolean isCanEnter() {
        return canEnter;
    }
    public boolean notCanEnter() {
        return !canEnter;
    }

    public boolean isFinishLineup() {
        return isFinishLineup;
    }

    public void setFinishLineup(boolean finishLineup) {
        isFinishLineup = finishLineup;
    }

    public void setCanEnter(boolean canEnter) {
        this.canEnter = canEnter;
        if(!canEnter){
            lineupStartTime = ServerConstants.getCurrentTimeMillis();
        }
    }

    public boolean isBattle() {
        return isBattle;
    }

    public void setBattle(boolean battle) {
        isBattle = battle;
        if (battle) {
            battleStartTime = ServerConstants.getCurrentTimeMillis();
        }
    }

    public long getLeaderId() {
        return leaderId;
    }

    public void setLeaderId(long leaderId) {
        this.leaderId = leaderId;
    }

    /**
     * 移交队长
     */
    public void transferLeader(long newLeaderId, long time){
        boolean isLocal = Configuration.isGameServer();
        setLeaderId(newLeaderId);
        applyLeaderPlayerId.clear();
        transferLeaderPlayerIds.clear();

        Set<Long> memberIds = TeamManager.getInstance().getMembers(teamId);
        PbProtocol.TeamTransferLeaderNotify.Builder notify = PbProtocol.TeamTransferLeaderNotify.newBuilder();
        notify.setNewLeaderId(newLeaderId);
        if(isLocal){
            TeamHelper.batchGsSendMsg(memberIds, PtCode.TEAM_TRANSFER_LEADER_NOTIFY, notify.build(), time);
        }else{
            //rpc消息
            TeamHelper.batchWorldSendMsg(memberIds, PtCode.TEAM_TRANSFER_LEADER_NOTIFY, time, notify.build());
        }
    }

    public boolean isLeader(long memberId){
        return leaderId == memberId;
    }

    public int getTeamPositions() {
        return teamPositions;
    }

    public void setTeamPositions(int teamPositions) {
        this.teamPositions = teamPositions;
    }

    public void setLineupStartTime(long lineupStartTime) {
        this.lineupStartTime = lineupStartTime;
    }

    public void setBattleStartTime(long battleStartTime) {
        this.battleStartTime = battleStartTime;
    }

    public Map<Long, Long> getApplyLeaderPlayerId() {
        return applyLeaderPlayerId;
    }

    public void setApplyLeaderPlayerId(Map<Long, Long> applyLeaderPlayerId) {
        this.applyLeaderPlayerId = applyLeaderPlayerId;
    }

    public Map<Long, Long> getTransferLeaderPlayerIds() {
        return transferLeaderPlayerIds;
    }

    public void setTransferLeaderPlayerIds(Map<Long, Long> transferLeaderPlayerIds) {
        this.transferLeaderPlayerIds = transferLeaderPlayerIds;
    }

    public long getLeagueId() {
        return leagueId;
    }

    public void setLeagueId(long leagueId) {
        this.leagueId = leagueId;
    }

    /**
     * 分配一个位置
     * @return
     */
    public int allocatePosition(){
        int position = teamPositions;
        for(int i = 0; i < TeamHelper.maxPlayerId; i++){
            if(((1<<i) | position) != position){
                return i;
            }
        }
        return -1;
    }

    /**
     * 记录位置标记
     */
    public void markPosition(int index){
        teamPositions |= 1 << index;
    }

    /**
     * 释放位置标记（归还）
     */
    public void releasePosition(int index){
        teamPositions &= ~(1 << index);
    }

    public PbTeam.TeamInfo genPb(){
        PbTeam.TeamInfo.Builder teamInfo = PbTeam.TeamInfo.newBuilder();
        teamInfo.setTeamId(teamId);
        teamInfo.setTeamTypeId(teamTypeId);
        teamInfo.setMinLevel(minLevel);
        teamInfo.setMaxLevel(maxLevel);
        teamInfo.setPublicAuth(publicAuth);
        teamInfo.setCreateTime(createTime);
        return teamInfo.build();
    }

    @Override
    public boolean equals(Object obj) {
        if(!(obj instanceof TeamInfo)){
            return false;
        }
        TeamInfo targetTeam = (TeamInfo)obj;
        return targetTeam.getTeamId() == this.getTeamId();
    }

}

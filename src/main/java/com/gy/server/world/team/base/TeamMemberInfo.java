package com.gy.server.world.team.base;

import java.io.Serializable;
import java.util.Objects;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;
import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.Configuration;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.game.timePlayPanel.PlayerTimePlayModel;
import com.gy.server.packet.PbTeam;

/**
 * 队伍成员信息
 * <AUTHOR> - [Created on 2022-05-26 15:18]
 */
@ProtobufClass
public class TeamMemberInfo implements Serializable {

    @Protobuf(order = 1)
    long playerId;
    @Protobuf(order = 2)
    int serverId;
    @Protobuf(order = 3)
    String name;
    @Protobuf(order = 4)
    int level;
    @Protobuf(order = 5)
    int mainHero;//主角形象（暂时没有功能，后续需要改）
    @Protobuf(order = 7)
    boolean isPrepare;//是否准备
    @Protobuf(order = 8)
    long enterTime;//进入队伍时间
    @Protobuf(order = 9)
    int teamPosition;//队伍中的位置
    @Protobuf(order = 10)
    boolean lineupFinish;
    @Protobuf(order = 11)
    boolean isAssistBattle;//棋局助战
    @Protobuf(order = 12)
    long leagueId;//帮派id

    public TeamMemberInfo(){
        
    }

    public TeamMemberInfo(Player player, boolean isLeader, boolean isPrepare){
        this.playerId = player.getPlayerId();
        this.serverId = Player.getRealServerId(player.getPlayerId());
        this.name = player.getName();
        this.level = player.getLevel();
        this.mainHero = player.getTemplate().id;
        this.isPrepare = isPrepare;
        this.leagueId = LeagueManager.getLeagueId(player);
    }

    public long getLeagueId() {
        return leagueId;
    }

    public void setLeagueId(long leagueId) {
        this.leagueId = leagueId;
    }

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public int getServerId() {
        return serverId;
    }

    public void setServerId(int serverId) {
        this.serverId = serverId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public int getMainHero() {
        return mainHero;
    }

    public void setMainHero(int mainHero) {
        this.mainHero = mainHero;
    }

    public boolean isPrepare() {
        return isPrepare;
    }

    public void setPrepare(boolean prepare) {
        isPrepare = prepare;
    }

    public long getEnterTime() {
        return enterTime;
    }

    public void setEnterTime(long enterTime) {
        this.enterTime = enterTime;
    }

    public int getTeamPosition() {
        return teamPosition;
    }

    public void setTeamPosition(int teamPosition) {
        this.teamPosition = teamPosition;
    }

    public boolean isLineupFinish() {
        return lineupFinish;
    }

    public void setLineupFinish(boolean lineupFinish) {
        this.lineupFinish = lineupFinish;
    }

    public PbTeam.TeamMemberInfo genPb(long leaderId){
        PbTeam.TeamMemberInfo.Builder memberInfo = PbTeam.TeamMemberInfo.newBuilder();
        memberInfo.setPlayerId(playerId);
        memberInfo.setLevel(level);
        memberInfo.setMainHero(mainHero);
        memberInfo.setServerId(serverId);
        memberInfo.setName(name);
        memberInfo.setIsLeader(playerId == leaderId);
        memberInfo.setIsPrepare(isPrepare);
        memberInfo.setEnterTime(enterTime);
        memberInfo.setTeamPosition(teamPosition);
        MiniGamePlayer miniPlayer = PlayerHelper.getMiniPlayer(playerId);
        if(Objects.nonNull(miniPlayer)){
            memberInfo.setHeadId(miniPlayer.getHeadImage());
            memberInfo.setHeadFrameId(miniPlayer.getHeadFrame());
            memberInfo.setProfession(miniPlayer.getProfession());
            memberInfo.setFightPower(miniPlayer.getFightingPower());
        }
        return memberInfo.build();
    }

    public PbTeam.TeamMiniMember genMiniPb(){
        PbTeam.TeamMiniMember.Builder miniMember = PbTeam.TeamMiniMember.newBuilder();
        miniMember.setPlayerId(playerId);
        miniMember.setServerId(serverId);
        miniMember.setLevel(level);
        miniMember.setName(name);
        MiniGamePlayer miniPlayer = PlayerHelper.getMiniPlayer(playerId);
        if(Objects.nonNull(miniPlayer)){
            miniMember.setHeadId(miniPlayer.getHeadImage());
            miniMember.setHeadFrameId(miniPlayer.getHeadFrame());
            miniMember.setProfession(miniPlayer.getProfession());
            miniMember.setFightPower(miniPlayer.getFightingPower());
        }
        return miniMember.build();
    }

    public boolean isAssistBattle() {
        return isAssistBattle;
    }

    public void setAssistBattle(boolean assistBattle) {
        isAssistBattle = assistBattle;
    }
}

package com.gy.server.world.db;

import java.util.Date;
import java.util.List;

import com.gy.server.core.ServerConstants;
import com.gy.server.db.relation.hibernate.HibernateUtil;
import com.gy.server.utils.CollectionUtil;

/**
 * <AUTHOR> - [Create on 2020/01/11 11:59]
 */
public final class WorldDbAssistant {

    private WorldDbAssistant() {}

    public static void clearOverdueCombatRecord() {
        Date now = ServerConstants.getCurrentTimeDate();
        String hql = "delete WorldCombatRecord record where record.expireTime <= ?";
        HibernateUtil.executeHql(hql, now);
    }

    public static void deleteUnion(List<Long> unionIds) {
        if (CollectionUtil.isEmpty(unionIds)) {
            return;
        }

        StringBuilder sb = new StringBuilder("delete Union where ");
        for (int i = 0; i < unionIds.size(); i++) {
            Long unionId = unionIds.get(i);
            if (i == 0) {
                sb.append("id = ").append(unionId);
            } else {
                sb.append(" or id = ").append(unionId);
            }
        }

        HibernateUtil.executeHql(sb.toString());
    }

}

package com.gy.server.world;

import com.gy.server.common.NodeStatusSyncRunner;
import com.gy.server.common.distributedlock.DistributedLockUtilManager;
import com.gy.server.common.redis.RedisScript;
import com.gy.server.common.util.CommonUtils;
import com.gy.server.common.util.ServerWaitHelper;
import com.gy.server.core.*;
import com.gy.server.core.battleRule.enums.BattleEnums;
import com.gy.server.core.delay.DelayTaskManager;
import com.gy.server.core.delay.MessageSystemHashInvoke;
import com.gy.server.core.delay.MessageSystemSyncInvoke;
import com.gy.server.core.log.CommonLogger;
import com.gy.server.core.thread.ForkJoinThreadPool;
import com.gy.server.core.thread.SimpleHashedThreadPool;
import com.gy.server.core.thread.SimpleRandomThreadPool;
import com.gy.server.db.nosql.redis.statistics.RedisUtilStatisticsManager;
import com.gy.server.game.combat.CombatManager;
import com.gy.server.game.db.DbManager;
import com.gy.server.game.event.ServerEventManager;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.record.RecordHelper;
import com.gy.server.game.service.ServiceManager;
import com.gy.server.game.smallGroup.SmallGroupManger;
import com.gy.server.game.team.TeamManager;
import com.gy.server.game.time.TimeManager;
import com.gy.server.game.warZone.WarZoneHelper;
import com.gy.server.game.warZone.WarZoneManager;
import com.gy.server.net.netty.ChannelHandlerContextFlusher;
import com.gy.server.utils.runner.RunnerManager;
import com.gy.server.utils.structure.TickerWatcher;
import com.gy.server.world.activity.ActivityWorldManager;
import com.gy.server.world.activityTeam.ActivityTeamManager;
import com.gy.server.world.common.PushItemDataManager;
import com.gy.server.world.common.WorldMasterCompeteAndCheck;
import com.gy.server.world.common.WorldRedisCheck;
import com.gy.server.world.crossData.CrossDataManager;
import com.gy.server.world.db.WorldDbAssistant;
import com.gy.server.world.fix.WorldFixDesignerGlobalData;
import com.gy.server.world.leagueDuel.LeagueDuelWorldManager;
import com.gy.server.world.leagueDuel.status.LeagueDuelWorldStatusManager;
import com.gy.server.world.leagueSLG.LeagueSLGWorldManager;
import com.gy.server.world.live.LiveRoomManager;
import com.gy.server.world.mountainRiverTournament.WorldMRTManager;
import com.gy.server.world.record.RecordSaveManager;
import com.gy.server.world.smallGroup.SmallGroupWorldManager;
import com.gy.server.world.tournament.TournamentWorldManager;
import com.gy.server.world.unparalleledChallenge.WorldUCCombatManager;
import com.ttlike.server.tl.baselib.CommonsConfiguration;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

/**
 * <AUTHOR> - [Created on 2018/9/12 17:24]
 */
public class WorldServer extends MainThread {

    @Override
    protected void init() throws Exception {
        //TLBase初始化
        TLBase.getInstance().init(Configuration.serverId
                , ServerType.WORLD
                , "world"
                , Configuration.startupMode.name()
                , MessageSystemSyncInvoke.getInstance()
                , MessageSystemHashInvoke.getInstance()
                , WorldRedisCheck.getInstance());
        Configuration.setRunMode();

        Configuration.initBillingUrl();

        Launcher.launchStartupInfo("TLBase init");

        waitStartup();

        Launcher.launchStartupInfo("WorldServer is initializing");


        //服务器常量
        Launcher.launchStartupInfo("ServerConstants init");
        ServerConstants.init();

        //db相关初始化
        Launcher.launchStartupInfo("DbManager init");
        DbManager.init();

        RecordHelper.getInstance().init();

        //快速tick
        addFastTicker(CombatManager.getInstance());

        //慢速tick
        addSlowTicker(RunnerManager.getInstance());
        addSlowTicker(ServerEventManager.getInstance());
        addSlowTicker(DelayTaskManager.getInstance());
        addSlowTicker(ElapsedTimeStatistics.getInstance());
        addSlowTicker(GlobalDataManager.getInstance());
        addSlowTicker(TimeManager.getInstance());
        addSlowTicker(WorldMRTManager.getInstance());
        addSlowTicker(LeagueSLGWorldManager.getInstance());
        addSlowTicker(LiveRoomManager.getInstance());
        addSlowTicker(CrossDataManager.getInstance());
        addSlowTicker(ActivityTeamManager.getInstance());
        addSlowTicker(ActivityWorldManager.getInstance());

        //注册service
        Launcher.launchStartupInfo("ServiceManager init");
        ServiceManager.init();

        //RedisLua注册
        RedisScript.init();
        //RedisUtilStatisticsManager 注册
        RedisUtilStatisticsManager.getInstance().init();


        Launcher.launchStartupInfo("WorldServer is initialized");

    }

    @Override
    protected void startup() throws Exception {
        Launcher.launchStartupInfo("WorldServer is starting");
        RunnerManager.addRunner(ServerWaitHelper.getInstance(), ServerWaitHelper.getInstance().getRunnerName(), false);
        //service前置启动
        Launcher.launchStartupInfo("ServiceManager pre startup");
        ServiceManager.preStartup();

        //分布式锁管理器启动
        DistributedLockUtilManager.startup();

        //清除过期的战报
        if (CommonUtils.isMainWorldServer()) {
            Launcher.launchStartupInfo("clear overdue combat record");
            WorldDbAssistant.clearOverdueCombatRecord();
        }

        //service启动
        Launcher.launchStartupInfo("ServiceManager startup");
        ServiceManager.startup();

        //全局数据管理器启动
        Launcher.launchStartupInfo("GlobalDataManager startup");
        GlobalDataManager.startup();

        //战区初始化
        WarZoneHelper.init();

        //时间管理器启动
        Launcher.launchStartupInfo("TimeManager startup");
        TimeManager.startup();

        //跨服数据管理器启动
        Launcher.launchStartupInfo("CrossDataManager startup");
        CrossDataManager.getInstance().startup();

        // 中心服补锅
        WorldFixDesignerGlobalData fixDesignerGlobalData = GlobalDataManager.getData(GlobalDataType.center_fix);
        fixDesignerGlobalData.checkFix();

        RecordSaveManager.startup();

        Launcher.launchStartupInfo("HashedThreadPool startup");
        SimpleHashedThreadPool.getInstance().startup();
        SimpleRandomThreadPool.getInstance().startup();

        Launcher.launchStartupInfo("HashedThreadPool startup");
        WorldMasterCompeteAndCheck.getInstance().startup();

        Launcher.launchStartupInfo("LeagueSLGWorldManager startup");
        LeagueSLGWorldManager.getInstance().startup();

        Launcher.launchStartupInfo("ActivityTeamManager startup");
        ActivityTeamManager.getInstance().start();

        //独立线程启动
        RunnerManager.addRunner(WorldMasterCompeteAndCheck.getInstance(), WorldMasterCompeteAndCheck.getInstance().getRunnerName(), false);
        RunnerManager.addRunner(ForkJoinThreadPool.getInstance(), ForkJoinThreadPool.getInstance().getRunnerName(), false);
        RunnerManager.addRunner(ChannelHandlerContextFlusher.getInstance(), "ChannelHandlerContextFlusher", false);
        RunnerManager.addRunner(TickerWatcher.getInstance(), "TickerWatcher", true);
        RunnerManager.addRunner(TeamManager.getInstance(), TeamManager.getInstance().getRunnerName(), false);
        RunnerManager.addRunner(LeagueDuelWorldManager.getInstance(), LeagueDuelWorldManager.getInstance().getRunnerName(), false);
//        RunnerManager.addRunner(RoomManager.getInstance(), RoomManager.getInstance().getRunnerName(), false);
        RunnerManager.addRunner(TournamentWorldManager.getInstance(), TournamentWorldManager.getInstance().getRunnerName(), false);
        RunnerManager.addRunner(LiveRoomManager.getInstance(), LiveRoomManager.class.getSimpleName(), false);
        RunnerManager.addRunner(ActivityTeamManager.getInstance(), ActivityTeamManager.class.getSimpleName(), false);
        RunnerManager.addRunner(SmallGroupWorldManager.getInstance(), SmallGroupManger.getInstance().getRunnerName(), false);
        RunnerManager.addRunner(ActivityWorldManager.getInstance(), ActivityWorldManager.class.getSimpleName(), false);
        if (CommonUtils.isMainWorldServer()) { //推送item数据都gmt
            RunnerManager.addRunner(PushItemDataManager.getInstance(), PushItemDataManager.getInstance().getRunnerName(), false);
        }
        RunnerManager.addRunner(RedisUtilStatisticsManager.getInstance(), RedisUtilStatisticsManager.class.getSimpleName(), false);
        RunnerManager.addRunner(new NodeStatusSyncRunner(), NodeStatusSyncRunner.class.getSimpleName(), false);
        RunnerManager.addRunner(WarZoneManager.getInstance(), WarZoneManager.getInstance().getRunnerName(), false);

        //状态机启动
        BattleEnums.start();
        LeagueDuelWorldStatusManager.init();

        WorldUCCombatManager.startup();

        //线程启动
        this.start();
        Launcher.launchStartupInfo("MainThread start");

        if(CommonUtils.isMainWorldServer()){
            Launcher.launchStartupInfo("Main WorldServer is started. " + Configuration.serverId);
        }else {
            Launcher.launchStartupInfo("Slave WorldServer is started. " + Configuration.serverId);
        }

        //输出启动成功：http://patorjk.com/software/taag，当前使用字体：Doom
        System.out.println(" _    _            _     _     _____                          \n" +
                "| |  | |          | |   | |   /  ___|                         \n" +
                "| |  | | ___  _ __| | __| |   \\ `--.  ___ _ ____   _____ _ __ \n" +
                "| |/\\| |/ _ \\| '__| |/ _` |    `--. \\/ _ \\ '__\\ \\ / / _ \\ '__|\n" +
                "\\  /\\  / (_) | |  | | (_| |   /\\__/ /  __/ |   \\ V /  __/ |   \n" +
                " \\/  \\/ \\___/|_|  |_|\\__,_|   \\____/ \\___|_|    \\_/ \\___|_|   \n" +
                "                                                              \n" +
                "                                                             ");
    }

    @Override
    protected void shutdown() throws Exception {
        Launcher.launchShutdownInfo("WorldServer start shutdown");
        running = false;

        while (true) {
            if (shutdown
                    && ThreadPool.getInstance().getSize() < 1
                    && !RecordSaveManager.isRunning()) {

                //rpc预关闭
                TLBase.getInstance().preShutdown();
                Launcher.launchShutdownInfo("TLBase preShutdown");
                // service关闭
                ServiceManager.shutdown();
                Launcher.launchShutdownInfo("ServiceManager shutdown");

                // 全局数据管理器关闭
                GlobalDataManager.shutdown();
                Launcher.launchShutdownInfo("GlobalDataManager shutdown");

                LeagueDuelWorldManager.getInstance().shutdown();
                Launcher.launchShutdownInfo("LeagueDuelWorldManager shutdown");

                // SLG世界管理器关闭
                LeagueSLGWorldManager.getInstance().shutdown();
                Launcher.launchShutdownInfo("LeagueSLGWorldManager shutdown");

                //跨服数据管理器关闭
                CrossDataManager.getInstance().shutdown();
                Launcher.launchShutdownInfo("CrossDataManager shutdown");

                //组队数据存储
                ActivityTeamManager.getInstance().shutdown();
                Launcher.launchShutdownInfo("ActivityTeamManager shutdown");

                // 独立线程关闭
                RunnerManager.shutdown();
                Launcher.launchShutdownInfo("RunnerManager shutdown");

                TLBase.getInstance().shutdown();
                Launcher.launchShutdownInfo("TLBase shutdown");

                //HashedThreadPool关闭
                SimpleHashedThreadPool.getInstance().shutdown();
                SimpleRandomThreadPool.getInstance().shutdown();
                Launcher.launchShutdownInfo("HashedThreadPool shutdown");

                // db关闭
                DbManager.shutdown();
                Launcher.launchShutdownInfo("DB shutdown");

                break;
            }

            Thread.sleep(100);
        }

        Launcher.launchShutdownInfo("WorldServer end shutdown");
        System.out.println("                    _     _           _              \n" +
                "                   | |   | |         | |             \n" +
                "__      _____  _ __| | __| |      ___| |_ ___  _ __  \n" +
                "\\ \\ /\\ / / _ \\| '__| |/ _` |     / __| __/ _ \\| '_ \\ \n" +
                " \\ V  V / (_) | |  | | (_| |     \\__ \\ || (_) | |_) |\n" +
                "  \\_/\\_/ \\___/|_|  |_|\\__,_|     |___/\\__\\___/| .__/ \n" +
                "                                              | |    \n" +
                "                                              |_|    ");
    }

    private void waitStartup() {
        while (this.checkNeedWaitStartup()) {
            try {
                Thread.sleep(10000);
            } catch (Exception e) {
                CommonLogger.error(e);
            }
        }
    }

    private boolean checkNeedWaitStartup() {
        if (CommonsConfiguration.getAllWorldServerIds().contains(Configuration.serverId)) {
            return false;
        }
        CommonLogger.info(String.format("world服务器id没有配置，请检查配置文件或者在gm后台添加，服务器id：%s", Configuration.serverId));
        //异常状态，等待下次检查
        return true;
    }

}

package com.gy.server.world.room.base;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;
import com.gy.server.core.Configuration;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.room.bean.RoomTemplate;
import com.gy.server.game.worldBoss.WorldBossHelper;
import com.gy.server.packet.PbRoom;
import com.gy.server.utils.MathUtil;
import com.gy.server.utils.time.DateTimeUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 房间
 *
 * <AUTHOR> 2024/4/18 20:16
 **/
@ProtobufClass
public class RoomInfo {

    /**
     * 唯一id
     */
    @Protobuf(order = 1)
    private long roomId;

    /**
     * 战区id
     * 战区id为0时为全服，为-1时为本服房间
     */
    @Protobuf(order = 2)
    private int warZoneId;

    /**
     * 房间类型
     */
    @Protobuf(order = 3)
    private int roomType;

    /**
     * 条件，例如1代表一种条件，0代表无条件
     */
    @Protobuf(order = 4)
    private int condition;

    /**
     * 现在执行顺序
     * 创建周期是一个时间点没有必要再走一次周期链，因此初始执行顺序为创建周期的下一周期
     */
    @Protobuf(order = 6)
    private int nowIndex;

    /**
     * key:执行顺序     value:周期信息
     */
    @Protobuf(order = 7)
    private Map<Integer, RoomCycleInfo> roomCycleInfoMap = new HashMap<>();


    public RoomInfo() {

    }

    public RoomInfo(int roomType, int condition) {
        this.warZoneId = WorldBossHelper.getWorldBossRoomRedisKeyParam(roomType);
        this.roomType = roomType;
        this.condition = condition;

        String temp = getSign() + Configuration.serverId + "" + MathUtil.randomInt(100) + ServerConstants.allocateRuntimeIdentifier();
        this.roomId = Long.parseLong(temp);
    }

    /**
     * 切换为下一周期（如果存在）
     */
    public void nextCycle(){
        Optional optional = roomCycleInfoMap.keySet().stream().filter(num -> num > nowIndex).min(Integer::compareTo);
        if(optional.isPresent()){
            nowIndex = (int) optional.get();
        }
    }

    /**
     * 调整周期链内周期的开始和结束时间
     * @param newEnd
     */
    public void checkCycleTime(long newEnd) {
        RoomCycleInfo cycleInfo = roomCycleInfoMap.get(nowIndex);
        if(cycleInfo.getEnd() < newEnd){
            return;
        }

        long differ = cycleInfo.getEnd() - newEnd;
        cycleInfo.setEnd(newEnd);

        ArrayList<Integer> list = new ArrayList<>(roomCycleInfoMap.keySet());
        list.sort(Integer::compareTo);

        for (Integer index : list) {
            if(index > cycleInfo.getIndex()){
                RoomCycleInfo bean = roomCycleInfoMap.get(index);
                bean.setStart(cycleInfo.getStart() - differ);
                bean.setEnd(cycleInfo.getEnd() - differ);
            }
        }
    }

    public long getRoomId() {
        return roomId;
    }

    public void setRoomId(long roomId) {
        this.roomId = roomId;
    }

    public int getWarZoneId() {
        return warZoneId;
    }

    public void setWarZoneId(int warZoneId) {
        this.warZoneId = warZoneId;
    }

    public int getRoomType() {
        return roomType;
    }

    public void setRoomType(int roomType) {
        this.roomType = roomType;
    }

    public Map<Integer, RoomCycleInfo> getRoomCycleInfoMap() {
        return roomCycleInfoMap;
    }

    public void setRoomCycleInfoMap(Map<Integer, RoomCycleInfo> roomCycleInfoMap) {
        this.roomCycleInfoMap = roomCycleInfoMap;
    }

    public int getNowIndex() {
        return nowIndex;
    }

    public void setNowIndex(int nowIndex) {
        this.nowIndex = nowIndex;
    }

    public int getCondition() {
        return condition;
    }

    public void setCondition(int condition) {
        this.condition = condition;
    }

    public boolean isCanEnterRoom(RoomTemplate roomTemplate) {
        RoomCycleInfo roomCycleInfo = roomCycleInfoMap.get(nowIndex);

        //最终倒计时内不在能进入
        long temp = ServerConstants.getCurrentTimeMillis() + DateTimeUtil.MillisOfSecond * roomTemplate.finalTime;
        return roomCycleInfo.getRoomCycle() == PbRoom.RoomCycle.roomReadying && temp < roomCycleInfo.getEnd();
    }

    /**
     * 获取匹配标记信息
     * @return
     */
    public String getSign(){
        return warZoneId + "" + roomType + "" + condition;
    }
}

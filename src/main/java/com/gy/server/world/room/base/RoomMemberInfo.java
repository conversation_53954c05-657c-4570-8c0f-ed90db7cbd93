package com.gy.server.world.room.base;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;
import com.gy.server.core.Configuration;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.player.Player;
import com.gy.server.packet.PbRoom;


/**
 * 房间成员信息
 *
 * <AUTHOR> 2024/4/19 10:12
 **/
@ProtobufClass
public class RoomMemberInfo {
    @Protobuf(order = 1)
    private long playerId;

    @Protobuf(order = 2)
    private int serverId;

    /**
     * 进入时间
     * 进入房间后，再更新时间
     */
    @Protobuf(order = 3)
    long enterTime;
//    String name;
//    int level;
//    /**
//     * 主角形象
//     */
//    int mainHero;
//    /**
//     * 是否队长
//     * 房间默认无队长
//     */
//    @Ignore
//    boolean isLeader = false;
//    /**
//     * 是否准备
//     * 默认不准备
//     * 准备周期内判断人数时，确认时所有人数还是完成准备人数
//     */
//    @Ignore
//    boolean isPrepare = false;


    public RoomMemberInfo() {
    }

    public RoomMemberInfo(Player player) {
        this.playerId = player.getPlayerId();
        this.serverId = Configuration.serverId;
        this.enterTime = ServerConstants.getCurrentTimeMillis();
//        this.name = player.getName();
//        this.level = player.getLevel();
//        this.mainHero = player.getTemplate().id;
//        this.isLeader = false;
//        this.isPrepare = false;
    }

    public PbRoom.RoomMemberInfo genPb() {
        PbRoom.RoomMemberInfo.Builder builder = PbRoom.RoomMemberInfo.newBuilder();
        builder.setPlayerId(playerId);
        builder.setServerId(serverId);
        builder.setEnterTime(enterTime);
        return builder.build();
    }

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public int getServerId() {
        return serverId;
    }

    public void setServerId(int serverId) {
        this.serverId = serverId;
    }

//    public String getName() {
//        return name;
//    }
//
//    public void setName(String name) {
//        this.name = name;
//    }
//
//    public int getLevel() {
//        return level;
//    }
//
//    public void setLevel(int level) {
//        this.level = level;
//    }
//
//    public int getMainHero() {
//        return mainHero;
//    }
//
//    public void setMainHero(int mainHero) {
//        this.mainHero = mainHero;
//    }

//    public boolean isLeader() {
//        return isLeader;
//    }
//
//    public void setLeader(boolean leader) {
//        isLeader = leader;
//    }

//    public boolean isPrepare() {
//        return isPrepare;
//    }
//
//    public void setPrepare(boolean prepare) {
//        isPrepare = prepare;
//    }

    public long getEnterTime() {
        return enterTime;
    }

    public void setEnterTime(long enterTime) {
        this.enterTime = enterTime;
    }

    public void upEnterTime() {
        this.enterTime = ServerConstants.getCurrentTimeMillis();
    }
}

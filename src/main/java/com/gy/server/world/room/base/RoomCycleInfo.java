package com.gy.server.world.room.base;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;
import com.gy.server.packet.PbRoom;

/**
 * 房间周期信息
 *
 * <AUTHOR> 2024/4/19 9:36
 **/
@ProtobufClass
public class RoomCycleInfo {

    @Protobuf(order = 1)
    private PbRoom.RoomCycle roomCycle;
    /**
     * 开始时间
     */
    @Protobuf(order = 2)
    private long start;
    /**
     * 结束时间 当周期为时间点时end=start
     */
    @Protobuf(order = 3)
    private long end;

    /**
     * 顺序
     */
    @Protobuf(order = 4)
    private int index;

    public RoomCycleInfo() {
    }

    public RoomCycleInfo(PbRoom.RoomCycle roomCycle, long start, long end, int index) {
        this.roomCycle = roomCycle;
        this.start = start;
        this.end = end;
        this.index = index;
    }

    /**
     * 根据周期创建下一周期
     * @param roomCycleInfo
     * @param nextRoomCycle
     * @param endAddTime
     * @return
     */
    public static RoomCycleInfo createNextRoomCycleInfo(RoomCycleInfo roomCycleInfo, PbRoom.RoomCycle nextRoomCycle, long endAddTime){
        return new RoomCycleInfo(nextRoomCycle, roomCycleInfo.getEnd(), roomCycleInfo.getEnd() + endAddTime, roomCycleInfo.getIndex() + 1);
    }

    public PbRoom.RoomCycleInfo genPb() {
        PbRoom.RoomCycleInfo.Builder builder = PbRoom.RoomCycleInfo.newBuilder();
//        builder.setRoomCycleValue(roomCycle.getCycle());
        builder.setRoomCycle(roomCycle);
        builder.setIndex(index);
        builder.setStart(start);
        builder.setEnd(end);
        return builder.build();
    }

    public RoomCycleInfo clone() {
        return new RoomCycleInfo(roomCycle, start, end, index);
    }

    public PbRoom.RoomCycle getRoomCycle() {
        return roomCycle;
    }

    public void setRoomCycle(PbRoom.RoomCycle roomCycle) {
        this.roomCycle = roomCycle;
    }

    public long getStart() {
        return start;
    }

    public void setStart(long start) {
        this.start = start;
    }

    public long getEnd() {
        return end;
    }

    public void setEnd(long end) {
        this.end = end;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }


}

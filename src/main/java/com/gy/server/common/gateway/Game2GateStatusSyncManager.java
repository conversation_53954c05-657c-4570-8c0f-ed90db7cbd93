package com.gy.server.common.gateway;

import com.gy.server.common.gateway.enums.Game2GateStateSync;
import com.gy.server.core.Configuration;
import com.gy.server.core.command.CommandRequest;
import com.gy.server.core.thread.AbstractRunner;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;

import org.apache.commons.lang3.tuple.Pair;

/**
 * 网关管理器-定时通过rpc给gate同步gs状态
 * <AUTHOR> - [Created on 2022-02-26 15:18]
 */
public class Game2GateStatusSyncManager extends AbstractRunner {

    @Override
    public String getRunnerName() {
        return "Game2GateStatusSyncManager";
    }

    @Override
    protected void subRunnerExecute() throws Exception {
        Pair<String, Object[]> interfaceInfo = Game2GateStateSync.getInterfaceInfo(Configuration.serverType);
        String interfaceName = interfaceInfo.getLeft();
        Object[] params = interfaceInfo.getRight();
        if(!interfaceName.isEmpty()){
            CommandRequest commandRequest = CommandRequests.newServerCommandRequest("GameMsgCommandService." + interfaceName, Configuration.serverId);
            TLBase.getInstance().getRpcUtil().sendToAll(ServerType.GATEWAY, commandRequest, params);
        }

    }

    @Override
    public long getRunnerInterval() {
        return 2 * 1000;
    }

    private static final Game2GateStatusSyncManager instance = new Game2GateStatusSyncManager();
    Game2GateStatusSyncManager(){}
    public static Game2GateStatusSyncManager getInstance(){return instance;}

}

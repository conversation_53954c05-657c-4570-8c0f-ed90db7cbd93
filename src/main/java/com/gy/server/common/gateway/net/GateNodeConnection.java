package com.gy.server.common.gateway.net;

import com.google.protobuf.AbstractMessage;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Parser;
import com.gy.server.common.gateway.GateNodeManager;
import com.gy.server.core.Configuration;
import com.gy.server.core.ElapsedTimeStatistics;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.log.CommonLogger;
import com.gy.server.core.log.LoggerType;
import com.gy.server.core.log.SystemLogger;
import com.gy.server.game.account.Account;
import com.gy.server.game.handler.HandlerManager;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.text.Text;
import com.gy.server.net.netty.NetConnection;
import com.gy.server.packet.PbPacket;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.time.DateTimeUtil;
import io.netty.channel.ChannelHandlerContext;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * <AUTHOR> - [Created on 2022-02-26 15:59]
 */
public class GateNodeConnection extends NetConnection {
    public static Set<Integer> indexNonIncPtCodes = new HashSet<>();
    static {
        indexNonIncPtCodes.add(PtCode.NET_CHECK_CLIENT);//1057
        indexNonIncPtCodes.add(PtCode.LOGIN_HEART_CLIENT);//1017
        indexNonIncPtCodes.add(PtCode.LOGIN_CHECK_CLIENT);//1001
        indexNonIncPtCodes.add(PtCode.REGISTER_CHECK_CLIENT);//1045
        indexNonIncPtCodes.add(PtCode.CREATE_ROLE_REQ);//1055
        indexNonIncPtCodes.add(PtCode.HEART_CLIENT);//1005
        indexNonIncPtCodes.add(PtCode.REGISTER_GET_RANDOM_NAME_CLIENT);//13013

        //以及说有大于200w的协议号，这部分走场景服
    }

    private final static long DISCONNECT_TIME = 4 * 60 * 1000L;//断开连接时间

    /**
     * 上次活跃时间，即上次收到消息时间
     */
    private long lastActiveTime = ServerConstants.getCurrentTimeMillis();

    /**
     * 最近一分钟内收到的数据包的时间戳集合
     */
    private final CopyOnWriteArrayList<Long> lastMinutePacketTimestamps = new CopyOnWriteArrayList<>();

    private PbPacket.Packet currentPacket;


    public GateNodeConnection(ChannelHandlerContext ctx) {
        super(ctx);
    }

    /**
     * 检查连接，若已失效则关闭连接
     * 返回true表示连接正常，false表示连接异常，需要处理
     */
    public boolean checkConnection() {
        if (!isConnect()
                || ServerConstants.getCurrentTimeMillis() - lastActiveTime > DISCONNECT_TIME) {
            CommonLogger.info(String.format("gateNode disconnect, ip : %s", this.getRemoteAddressInfo()));
            //连接失效，断开
            close();
            return false;
        }

        return true;
    }

    @Override
    public void addPacket(AbstractMessage packet) {
        //更新最后一次活跃时间
        lastActiveTime = ServerConstants.getCurrentTimeMillis();

        if(packet instanceof PbPacket.SSPacket){
            PbPacket.SSPacket ssPacket = (PbPacket.SSPacket) packet;
            int ptCode = ssPacket.getPacket().getPtCode();
            if(ptCode == PtCode.GATE_HEART_GS){
                send(0, PtCode.GATE_HEART_GS, null, ServerConstants.getCurrentTimeMillis(), null);
                return;
            }

            if(ptCode == PtCode.NET_CHECK_CLIENT){
                //网络监控协议直接处理
                try {
                    PbProtocol.NetCheckReq req = PbProtocol.NetCheckReq.parseFrom(((PbPacket.Packet) packet).getData());
                    String clientInitVersion = "";
                    if(ssPacket.getPlayerId() > 0){
                        Player player = PlayerManager.getOnlinePlayer(ssPacket.getPlayerId());
                        if(player != null){
                            Account account = player.getAccount();
                            if(account != null){
                                clientInitVersion = account.clientInitVersion;
                            }
                        }
                    }
                    if(req.getLastCost() > 0) {
                        CommonLogger.netCheck(getRemoteAddressIp()+ LoggerType.delimiter + clientInitVersion, req.getLastCost(), ssPacket.getPlayerId());
                    }
                } catch (InvalidProtocolBufferException e) {
                    SystemLogger.error(e);
                }

                PbProtocol.LoginCheckRst rst = PbProtocol.LoginCheckRst.newBuilder().setResult(Text.genOkServerRstInfo()).build();

                send(ssPacket.getPlayerId(),PtCode.NET_CHECK_SERVER,rst);
                //不再进行后续处理
                return;
            }

            Player player = PlayerManager.getOnlinePlayer(ssPacket.getPlayerId());
            if(player != null) {
                if (PlayerManager.checkPacketCache(player, ssPacket.getPacket())) {
                    //使用了缓存的协议包，无需再处理这个包
                    return;
                }
            }

        }
        GameLogger.std("receive msg : " + ((PbPacket.SSPacket) packet).getPacket().getPtCode() +
                "，connection info : " + getRemoteAddressInfo());
        super.addPacket(packet);

        //网关会监听客户端发送数据包集合，所以game和Scene可以不记录

        //非压测模式下更新最近一分钟内收到的数据包的时间戳集合
        this.updateLastMinutePacketTimestamps(lastActiveTime, packet);
//        if (!Configuration.runMode.isPress()) {
//        }
    }

    /**
     * 更新最近一分钟内收到的数据包的时间戳集合
     */
    private void updateLastMinutePacketTimestamps(long timestamp, AbstractMessage message) {
        //指定时间加入集合
        lastMinutePacketTimestamps.add(timestamp);

        //根据指定时间计算一分钟前的时间点
        long timeBeforeOneMinute = timestamp - DateTimeUtil.MillisOfMinute;

        /*
         * 遍历集合移除过期的时间
         * 这里没有使用遍历判断移除的方式，是因为每个remove都涉及到数组copy，分析性能可能不如removeIf
         * 因为removeIf会遍历所有找出要移除的，然后只进行一次数组copy，虽然这样做要遍历所有元素，在这块的逻辑中其实不需要遍历所有
         * 元素，但是相比于遍历，数组copy应该更消耗性能，所以采用removeIf
         */
        lastMinutePacketTimestamps.removeIf(time -> time < timeBeforeOneMinute);

        //检测包太多是否断开连接
        long timeBeforePacketTooManyDisconnect = timestamp - ServerConstants.packetTooManyDisconnectDurationThreshold;
        long tooManyPacketCount = lastMinutePacketTimestamps.stream()
                .filter(time -> time > timeBeforePacketTooManyDisconnect)
                .count();
        //暂时注掉校验，因为网关会有校验
//        if (tooManyPacketCount > ServerConstants.packetTooManyDisconnectCountThreshold
//                && Configuration.runMode != Configuration.RunMode.press) {
//            /*
//             * 阈值时间区间内数据包数量超过了包数量阈值，断开连接
//             * disconnect是为了让主线程能够在本帧或者下一帧检测到该链接已断开，进而处理玩家下线逻辑
//             * close是为了直接断开连接，如果不close，到下次主线程检测断开处理前还是会接受这些包
//             */
//            disconnect();
//            close();
//
//            PbPacket.SSPacket ssPacket = (PbPacket.SSPacket) message;
//            //日志输出
//            String errorMsg = String.format("ClientConnection receive %s packet in %s ms, threshold is %s, ptCode is %s",
//                    tooManyPacketCount,
//                    ServerConstants.packetTooManyDisconnectDurationThreshold,
//                    ServerConstants.packetTooManyDisconnectCountThreshold,
//                    ssPacket.getPacket().getPtCode());
//            CommonLogger.error(errorMsg);
//        }

        //统计
        ElapsedTimeStatistics.addElapsedNanoTimeWithoutMinNano(ElapsedTimeStatistics.Type.ReceivePacketCount, "OneMinute", lastMinutePacketTimestamps.size());
        ElapsedTimeStatistics.addElapsedNanoTimeWithoutMinNano(ElapsedTimeStatistics.Type.ReceivePacketCount, ServerConstants.packetTooManyDisconnectDurationThreshold + "ms", tooManyPacketCount);
        recordPtCodeAndPrint(message);
    }

    @Override
    protected void logReceiveMessage(AbstractMessage packet) {
        PbPacket.SSPacket ssPacket = (PbPacket.SSPacket) packet;
        if (Configuration.showNetMessage
                && (ssPacket.getPacket().getPtCode() != PtCode.HEART_CLIENT && ssPacket.getPacket().getPtCode() != PtCode.GATE_HEART_GS)) {
//            ssPacket.getPacket().getData()
            Parser<?> ptCodeReq = HandlerManager.getInstance().getPtCodeReq(ssPacket.getPacket().getPtCode());
            Object packetData = null;
            if (ptCodeReq != null) {
                try {
                    packetData = ptCodeReq.parseFrom(ssPacket.getPacket().getData());
                } catch (InvalidProtocolBufferException e) {
                    e.printStackTrace();
                }
            }
            CommonLogger.net(String.format("Receive Message : %s -> %s %n%s", ssPacket.getPacket().getPtCode(), getRemoteAddressInfo(), packetData));
        }
        if(Configuration.runMode.isTest()){
            if(CollectionUtil.isEmpty(GateNodeManager.getInstance().getGateNodes())){
                close();
                throw new IllegalArgumentException("receive message is error, gate node is null !! ");
            }
        }

    }

    @Override
    protected void logSendMessage(AbstractMessage packet) {
    }

    public void send(long playerId, int ptCode, AbstractMessage message) {
        send(playerId, ptCode, message, null);
    }

    public void send(long playerId, int ptCode, AbstractMessage message, GatePacketExecutor executor) {
        send(playerId, ptCode, message, 0, executor);
    }

    public void send(long playerId, int ptCode, AbstractMessage message, long timeStamp, GatePacketExecutor executor) {
        send(playerId, 0L, ptCode, message, timeStamp, executor);
    }
    public void send(long playerId, long clientId, int ptCode, AbstractMessage message, long timeStamp, GatePacketExecutor executor) {
        if (isConnect()) {
            PbPacket.Packet.Builder packet = PbPacket.Packet.newBuilder()
                    .setPtCode(ptCode)
                    .setTimeStamp(timeStamp);
            if (message != null) {
                packet.setData(message.toByteString());
            }

            /*
             * timeStamp < 1 表示这次数据发送是服务器处理后的行为，因为如果是使用了协议包缓存进行数据发送的话，timeStamp会被
             * 设置为对应请求包的时间戳。对于服务器处理后发的包，均设置为当前正在处理的包的时间戳
             */
            if (timeStamp < 1 && currentPacket != null) {
                packet.setTimeStamp(currentPacket.getTimeStamp());
            }

            PbPacket.SSPacket.Builder ssPacket = PbPacket.SSPacket.newBuilder();
            ssPacket.setPlayerId(playerId);
            ssPacket.setPacket(packet.build());
            ssPacket.setClientId(clientId);
            send(ssPacket.build());
            if (Configuration.showNetMessage
                    && ptCode != PtCode.HEART_SERVER && ssPacket.getPacket().getPtCode() != PtCode.GATE_HEART_GS) {
                CommonLogger.net(String.format("Send Message : %s -> %s %n%s", ptCode, getRemoteAddressInfo(), message));
            }
        }

        if (executor != null) {
            executor.cachePacketIf(ptCode, message);
        }
    }

    /**
     * 通知服务器罢工
     */
    public void notifyStrike(int ptCode, String cause, int lineNumber) {
        notifyStrike(ptCode, cause + "，行号：" + lineNumber);
    }

    /**
     * 通知服务器罢工-携带错误文本
     */
    public void notifyStrike(int ptCode, String msg) {
        PbProtocol.ServerStrike.Builder builder = PbProtocol.ServerStrike.newBuilder()
                .setPtCode(ptCode);
        if (msg != null) {
            builder.setErrorMsg(msg);
        }
        send(0, PtCode.SERVER_STRIKE_SERVER, builder.build());
    }

    public void setCurrentPacket(PbPacket.Packet currentPacket) {
        this.currentPacket = currentPacket;
    }

    public long getPacketTime() {
        return currentPacket.getTimeStamp();
    }

    private final long console_log_interval = 5 * DateTimeUtil.MillisOfMinute;
    long lastConsoleTime = ServerConstants.getCurrentTimeMillis();
    Map<Integer, Integer> ptCodeTimes = new HashMap<>();
    private void recordPtCodeAndPrint(AbstractMessage message){
        int ptCode = ((PbPacket.SSPacket) message).getPacket().getPtCode();
        ptCodeTimes.put(ptCode, ptCodeTimes.getOrDefault(ptCode, 0) + 1);

        long now = ServerConstants.getCurrentTimeMillis();
        if (now - lastConsoleTime > console_log_interval) {
            lastConsoleTime = now + console_log_interval;
        }
        if (now > lastConsoleTime) {
            lastConsoleTime += console_log_interval;
            int totalCount = 0;
            for (Integer count : ptCodeTimes.values()) {
                totalCount += count;
            }
            CommonLogger.elapsed("fix time record ptCode, total count : " + totalCount);
            CommonLogger.elapsed("fix time record ptCode : " + ptCodeTimes);
            ptCodeTimes.clear();
        }
    }

}

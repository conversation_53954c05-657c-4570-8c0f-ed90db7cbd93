package com.gy.server.common.gateway.net;

import com.google.protobuf.AbstractMessage;
import com.gy.server.common.gateway.GateNode;
import com.gy.server.common.gateway.GateNodeManager;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.utils.packet.PacketExecutor;

import java.util.Objects;

/**
 * <AUTHOR> - [Created on 2018/2/27 10:39]
 */
public interface GatePacketExecutor extends PacketExecutor {

    GateNode getGateNode();
    long getPlayerId();

    /**
     * 发送消息，调用{@link GatePacketExecutor#getGateNode()} ()}获得{@link GateNodeConnection}，调用其send方法
     */
    default void send(int ptCode, AbstractMessage message) {
        GateNodeConnection connection = getGateNode().getConnection();
        if (connection != null) {
            connection.send(getPlayerId(), ptCode, message, this);
        }else{
            Player onlinePlayer = PlayerManager.getOnlinePlayer(getPlayerId());
            GameLogger.error("send error connection is null : " + ptCode + " playerId : " + getPlayerId() + "gateId : " + (Objects.nonNull(onlinePlayer) ? onlinePlayer.getGateId() : 0));
        }
    }

    default void send(long clientId, int ptCode, AbstractMessage message) {
        GateNodeConnection connection = getGateNode().getConnection();
        if (connection != null) {
            connection.send(0, clientId, ptCode, message, ServerConstants.getCurrentTimeMillis(), this);
        }else{
            Player onlinePlayer = PlayerManager.getOnlinePlayer(getPlayerId());
            GameLogger.error("send error connection is null : " + ptCode + " playerId : " + getPlayerId() + "gateId : " + (Objects.nonNull(onlinePlayer) ? onlinePlayer.getGateId() : 0));
        }
    }

    default void send(long playerId, long clientId, int ptCode, AbstractMessage message, long timeStamp) {
        GateNodeConnection connection = getGateNode().getConnection();
        if (connection != null) {
            connection.send(playerId, clientId, ptCode, message, timeStamp, this);
        }else{
            Player onlinePlayer = PlayerManager.getOnlinePlayer(getPlayerId());
            GameLogger.error("send error connection is null : " + ptCode + " playerId : " + getPlayerId() + "gateId : " + (Objects.nonNull(onlinePlayer) ? onlinePlayer.getGateId() : 0));
        }
    }

    default void send(long clientId, int ptCode, AbstractMessage message, long timeStamp) {
        GateNodeConnection connection = getGateNode().getConnection();
        if (connection != null) {
            connection.send(0, clientId, ptCode, message, timeStamp, this);
        }else{
            Player onlinePlayer = PlayerManager.getOnlinePlayer(getPlayerId());
            GameLogger.error("send error connection is null : " + ptCode + " playerId : " + getPlayerId() + "gateId : " + (Objects.nonNull(onlinePlayer) ? onlinePlayer.getGateId() : 0));
        }
    }

    default void send(int ptCode, AbstractMessage message, long timeStamp) {
        GateNodeConnection connection = getGateNode().getConnection();
        if (connection != null) {
            connection.send(getPlayerId(), ptCode, message, timeStamp, this);
        }else{
            Player onlinePlayer = PlayerManager.getOnlinePlayer(getPlayerId());
            GameLogger.error("send error connection is null : " + ptCode + " playerId : " + getPlayerId() + "gateId : " + (Objects.nonNull(onlinePlayer) ? onlinePlayer.getGateId() : 0));
        }
    }

    /**
     * 按需缓存协议包，默认不实现
     */
    default void cachePacketIf(int ptCode, AbstractMessage message) {

    }
}

package com.gy.server.common.gateway.enums;

import com.google.protobuf.AbstractMessage;
import com.gy.server.common.gateway.GateNode;
import com.gy.server.common.gateway.GateNodeManager;
import com.gy.server.core.Configuration;
import com.gy.server.core.ServerType;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.core.log.CommonLogger;
import com.gy.server.game.handler.HandlerManager;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.packet.PbPacket;
import com.gy.server.scene.divisionLine.DivisionLineManager;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 处理协议包
 * <AUTHOR> - [Created on 2022-03-17 19:34]
 */
public enum GameDealPacket {

    deal_game(ServerType.game, (message, basePtCodes, gateNode) -> {
        PbPacket.SSPacket ssPacket = (PbPacket.SSPacket) message;
        try {
            long playerId = ssPacket.getPlayerId();
            PbPacket.Packet packet = ssPacket.getPacket();
            Player onlinePlayer = PlayerManager.getOnlinePlayer(playerId);
            gateNode.getConnection().setCurrentPacket(packet);

            // 根据在不在线判断不行
            if (basePtCodes.contains(packet.getPtCode())) {
                if(packet.getPtCode() == PtCode.GATE_REGISTER_GS || packet.getPtCode() == PtCode.GATE_HEART_GS ){
                    //心跳协议单独响应
                    ThreadPool.execute(()-> HandlerManager.getInstance().handlePacket(gateNode, packet, packet.getPtCode(), packet.getTimeStamp()));
                }else{
                    HandlerManager.getInstance().handlePacket(gateNode, packet, packet.getPtCode(), packet.getTimeStamp());
                }
            } else if (onlinePlayer != null){
                // 剩下的就是玩家协议
                onlinePlayer.addMessage(packet);
            }else{
                throw new IllegalArgumentException("deal game msg player is offLine, playerId : " + playerId + " ptCode : " + packet.getPtCode());

            }
        } catch (Exception e) {
            CommonLogger.error(e.getMessage(), e);
        }
    }),

    deal_scene(ServerType.scene, (message, basePtCodes, gateNode) -> {
        PbPacket.SSPacket ssPacket = (PbPacket.SSPacket) message;
        try {
            long playerId = ssPacket.getPlayerId();
            PbPacket.Packet packet = ssPacket.getPacket();
            //场景服逻辑肯定要玩家id
            if(playerId > 0){
                if(packet.getPtCode() != PtCode.GATE_REGISTER_GS && packet.getPtCode() != PtCode.GATE_HEART_GS){
                    //处理玩家信息
                    DivisionLineManager.getInstance().divisionLine(playerId, gateNode, packet, ssPacket.getClientId());
                }else{
                    ThreadPool.execute(()-> HandlerManager.getInstance().handlePacket(gateNode, packet, packet.getPtCode()));
                }

            }else{
                ThreadPool.execute(()-> HandlerManager.getInstance().handlePacket(gateNode, packet, packet.getPtCode(), packet.getTimeStamp(), playerId));
            }
        } catch (Exception e) {
            CommonLogger.error(e.getMessage(), e);
        }
    }),
    ;

    private ServerType serverType;
    private PacketDeal deal;

    GameDealPacket(ServerType serverType, PacketDeal deal){
        this.serverType = serverType;
        this.deal = deal;
    }

    private interface PacketDeal{
        void deal(AbstractMessage message, List<Integer> baseCodes, GateNode gateNode);
    }

    private final static Map<ServerType, PacketDeal> deals = new HashMap<>();
    static{
        for (GameDealPacket dealPacketEnum : GameDealPacket.values()) {
            deals.put(dealPacketEnum.serverType, dealPacketEnum.deal);
        }
    }

    public static void dealPacket(AbstractMessage message, List<Integer> baseCodes, GateNode gateNode) throws IllegalAccessException {
        if(!deals.containsKey(Configuration.serverType)){
            throw new IllegalAccessException(String.format("%s not config packet deal logic !!!", Configuration.serverType.name()));
        }
        deals.get(Configuration.serverType).deal(message, baseCodes, gateNode);
    }

    public static void activePlayerTime(long playerId){
        ServerCommandRequest commandRequest = CommandRequests.newServerCommandRequest("Scene2GsCommandService.activeTime");
        TLBase.getInstance().getRpcUtil().sendToNode(com.ttlike.server.tl.baselib.ServerType.GAME, Player.getRealServerId(playerId), commandRequest, playerId);
    }

}

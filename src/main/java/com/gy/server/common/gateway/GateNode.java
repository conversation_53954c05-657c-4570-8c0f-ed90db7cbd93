package com.gy.server.common.gateway;

import com.google.common.cache.Cache;
import com.google.protobuf.AbstractMessage;
import com.google.protobuf.ByteString;
import com.google.protobuf.GeneratedMessage;
import com.google.protobuf.InvalidProtocolBufferException;
import com.gy.server.common.gateway.enums.GameDealPacket;
import com.gy.server.common.gateway.net.GateNodeConnection;
import com.gy.server.common.gateway.net.GatePacketExecutor;
import com.gy.server.core.Configuration;
import com.gy.server.core.ElapsedTimeStatistics;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.log.CommonLogger;
import com.gy.server.db.cache.guava.GuavaCacheUtil;
import com.gy.server.game.GameServerStateUpdater;
import com.gy.server.game.account.Account;
import com.gy.server.game.account.login.LoginQueueManger;
import com.gy.server.game.account.login.LoginTask;
import com.gy.server.game.account.login.check.LoginCheckManager;
import com.gy.server.game.account.login.check.LoginCheckTask;
import com.gy.server.game.account.register.CreateRoleAsyncCall;
import com.gy.server.game.account.register.ProfessionType;
import com.gy.server.game.account.register.check.RegisterCheckManager;
import com.gy.server.game.account.register.check.RegisterCheckTask;
import com.gy.server.game.constant.ConstantService;
import com.gy.server.game.constant.ConstantType;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.gm.BannedGameInfo;
import com.gy.server.game.gm.PunishmentGlobalData;
import com.gy.server.game.gm.PunishmentType;
import com.gy.server.game.handler.AbstractPacketHandler;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.log.constant.ClientEventType;
import com.gy.server.game.name.NameManager;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbPacket;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.CollectionUtil;
import com.ttlike.server.tl.baselib.thread.ThreadPool;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * <AUTHOR> - [Created on 2022-02-26 15:47]
 */
public class GateNode extends AbstractPacketHandler<GateNode> implements GatePacketExecutor {

    private static final String TAG = "GateNodeCache";
    private static Cache<Object, Object> cache;

    public static Set<Integer> indexNonIncPtCodes = new HashSet<>();
    static {
        indexNonIncPtCodes.add(PtCode.LOGIN_CHECK_CLIENT);
        indexNonIncPtCodes.add(PtCode.HEART_CLIENT);
        indexNonIncPtCodes.add(PtCode.LOGIN_HEART_CLIENT);
        indexNonIncPtCodes.add(PtCode.COMBAT_NORMAL_SKILL_CLIENT);
    }
    
    private GateNodeConnection connection;
    private static Set<String> loginOnceAccountIds = new CopyOnWriteArraySet<>();
    private static Map<String, long[]> loginTimes = new ConcurrentHashMap<>();

    private volatile int serverId;

    /**
     * 待注册的账号
     */
    private Map<Long, Account> toRegisterAccounts = new ConcurrentHashMap<>();

    private long lastSlowTickTime = 0;

    private static final int SLOW_TICK_INTERVAL = 5000;


    GateNode(GateNodeConnection connection) {
        this.connection = connection;
    }

    public GateNode() {
    }


    /**
     * 获得连接标识信息
     */
    String getConnectInfo() {
        return connection == null ? "null" : connection.getRemoteAddressInfo();
    }

    public static void startUp(){
        GuavaCacheUtil.addCache(TAG, ConstantService.getInt(ConstantType.随机名字间隔, 2), 1000);
        cache = GuavaCacheUtil.getCache(TAG);
    }

    public void send(long playerId, int ptCode, GeneratedMessage message) {
        send(playerId, ptCode, message, ServerConstants.getCurrentTimeMillis());
    }

    public void send(long playerId, int ptCode, GeneratedMessage message, long timeStamp) {
        send(playerId, 0L, ptCode, message, timeStamp);
    }
    public void send(long playerId, long clientId, int ptCode, GeneratedMessage message, long timeStamp) {
        if (connection != null) {
            connection.send(playerId, clientId, ptCode, message, timeStamp, null);
        }
    }

    public int getServerId() {
        return serverId;
    }

    public static void setLoginFlags(Map<String, Boolean> loginFlags) {
        loginFlags = loginFlags;
    }


    public static boolean isLoginOnce(String accountId) {
        return loginOnceAccountIds.contains(accountId);
    }

    public static void loginOnce(String accountId) {
        loginOnceAccountIds.add(accountId);
    }

    public static void updateLoginTime(String playerId, LoginTimePoint point) {
        if (!loginTimes.containsKey(playerId)) {
            long[] times = new long[LoginTimePoint.values().length];
            loginTimes.put(playerId, times);
        }
        loginTimes.get(playerId)[point.ordinal()] = System.nanoTime();
    }

    public static void statisticsLoginTime(String playerId) {
        long finishEndTime = System.nanoTime();
        ElapsedTimeStatistics.addElapsedNanoTimeWithoutMinNano(ElapsedTimeStatistics.Type.LoginTask, "waitCheck", loginTimes.get(playerId)[LoginTimePoint.checkStart.ordinal()] - loginTimes.get(playerId)[LoginTimePoint.checkReady.ordinal()]);
        ElapsedTimeStatistics.addElapsedNanoTimeWithoutMinNano(ElapsedTimeStatistics.Type.LoginTask, "check", loginTimes.get(playerId)[LoginTimePoint.checkEnd.ordinal()] - loginTimes.get(playerId)[LoginTimePoint.checkStart.ordinal()]);
        ElapsedTimeStatistics.addElapsedNanoTimeWithoutMinNano(ElapsedTimeStatistics.Type.LoginTask, "waitLogin", loginTimes.get(playerId)[LoginTimePoint.loginStart.ordinal()] - loginTimes.get(playerId)[LoginTimePoint.checkEnd.ordinal()]);
        ElapsedTimeStatistics.addElapsedNanoTimeWithoutMinNano(ElapsedTimeStatistics.Type.LoginTask, "login", loginTimes.get(playerId)[LoginTimePoint.loginEnd.ordinal()] - loginTimes.get(playerId)[LoginTimePoint.loginStart.ordinal()]);
        ElapsedTimeStatistics.addElapsedNanoTimeWithoutMinNano(ElapsedTimeStatistics.Type.LoginTask, "waitFinish", loginTimes.get(playerId)[LoginTimePoint.finishStart.ordinal()] - loginTimes.get(playerId)[LoginTimePoint.loginEnd.ordinal()]);
        ElapsedTimeStatistics.addElapsedNanoTimeWithoutMinNano(ElapsedTimeStatistics.Type.LoginTask, "clear", finishEndTime - loginTimes.get(playerId)[LoginTimePoint.finishStart.ordinal()]);
        ElapsedTimeStatistics.addElapsedNanoTimeWithoutMinNano(ElapsedTimeStatistics.Type.LoginTask, "total", finishEndTime - loginTimes.get(playerId)[LoginTimePoint.checkReady.ordinal()]);
    }

    private static List<Integer> baseCodes = new ArrayList<>();

    static {
        baseCodes.add(PtCode.LOGIN_CHECK_CLIENT);
        baseCodes.add(PtCode.LOGIN_HEART_CLIENT);
        baseCodes.add(PtCode.TEST_BODY_CLIENT);
        baseCodes.add(PtCode.TEST_NON_BODY_CLIENT);
        baseCodes.add(PtCode.GATE_REGISTER_GS);
        baseCodes.add(PtCode.GATE_HEART_GS);
        baseCodes.add(PtCode.GATE_PLAYER_LOGOUT_GATE);
        baseCodes.add(PtCode.REGISTER_CHECK_CLIENT);
        baseCodes.add(PtCode.REGISTER_GET_RANDOM_NAME_CLIENT);
    }

    /**
     * tick网络协议，如果需要移除（如网络已断开）则返回true，否则返回false
     */
    boolean tickNet() {
        //slow tick
        long currentTimeMillis = ServerConstants.getCurrentTimeMillis();
        if (currentTimeMillis - lastSlowTickTime > SLOW_TICK_INTERVAL) {
            lastSlowTickTime = currentTimeMillis;
            toRegisterAccounts.forEach((k,v)->{
                if(v.isCreateRoleOverdue()){
                    toRegisterAccounts.remove(k);
                    CommonLogger.info("account {} create role overdue, remove it from register list ", k);
                }
            });
        }else if(currentTimeMillis < lastSlowTickTime){
            //时间回退纠正
            lastSlowTickTime = currentTimeMillis;
        }


        if (connection.checkConnection()) {
            List<AbstractMessage> packets = connection.readPackets();
            for (AbstractMessage message : packets) {
                try {
                    GameLogger.std("deal msg : " + ((PbPacket.SSPacket) message).getPacket().getPtCode());
                    GameDealPacket.dealPacket(message, baseCodes, this);
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
            }
            return false;
        }
        return true;
    }

    /**
     * 登录校验
     */
    @Handler(PtCode.LOGIN_CHECK_CLIENT)
    private void loginCheck(GateNode gateNode, PbProtocol.LoginCheckReq req, long timeStamp) throws InvalidProtocolBufferException {
        GameLogger.clientEvent(req, ClientEventType.gs收到登陆请求10001, req.getIp());
        PbProtocol.LoginCheckRst.Builder b = PbProtocol.LoginCheckRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());

        String version = req.getVersion();
        if (StringUtils.isEmpty(version)) {
            b.setLoginCheckResultType(PbProtocol.LoginCheckRst.LoginCheckResultType.VERSION_LOW);
            GameLogger.clientEvent(req, ClientEventType.角色登陆版本过低, req.getIp());
            gateNode.send(PtCode.LOGIN_CHECK_SERVER, b.build(), timeStamp);
            return;
        }

        if (!GameServerStateUpdater.instance.getState().getServerNumbers().contains(req.getServerNum())) {
            b.setLoginCheckResultType(PbProtocol.LoginCheckRst.LoginCheckResultType.SERVER_NUM_CHECK_FAIL);
            GameLogger.clientEvent(req, ClientEventType.区服选择不对, req.getIp());
            gateNode.send(PtCode.LOGIN_CHECK_SERVER, b.build(), timeStamp);
            return;
        }

        // 校验客户端版本
        Map<String, String> versionMap = Configuration.clientMinVersions;
        if (req.getReConnect()) {
            versionMap = Configuration.clientMinVersionsForce;
        }
        if (CollectionUtil.isNotEmpty(versionMap)
                && StringUtils.isNotEmpty(req.getDeviceType())) {
            String clientMinVersion = versionMap.get(req.getDeviceType().toLowerCase());
            /*
            比如现在更新列表上有两个版本，  1.0.1 强制（不填默认强制） 1.0.2 （非强制）
            玩家正常登陆gs时的版本校验， 低于1.0.2都不让近，返回版本过低，同现在逻辑。
            如果是断线重连的登陆（有个字段标记是否断线重连），则1.0.1也能正常进入，1.0.0就不行。
             */
            if (StringUtils.isNotEmpty(clientMinVersion)
                    && Account.versionCompare(clientMinVersion, version) > 0) {
                CommonLogger.info(String.format("guest version too low, guest version is: %s, client min version is: %s",
                        version,
                        clientMinVersion)
                );
                b.setLoginCheckResultType(PbProtocol.LoginCheckRst.LoginCheckResultType.VERSION_LOW);
                gateNode.send(PtCode.LOGIN_CHECK_SERVER, b.build(), timeStamp);
                GameLogger.clientEvent(req, ClientEventType.角色登陆版本过低, req.getIp());
                return;
            }
        }

        CopyOnWriteArrayList<BannedGameInfo> bannedPlayer = new CopyOnWriteArrayList<>();

        PunishmentGlobalData punishmentGlobalData = GlobalDataManager.getData(GlobalDataType.Punishment);
        punishmentGlobalData.getBeingBannedGameInfos().stream().
                filter(infos -> infos.getAccountId().equals(req.getAccountId())).
                filter(infos -> infos.getAccountType() == req.getAccountType()).forEach(bannedPlayer::add);

        if (bannedPlayer.size() != 0) {
            BannedGameInfo bannedPlayerInfo = bannedPlayer.get(0);
            if (bannedPlayerInfo.getPunishmentType() == PunishmentType.bannedGame_Forever) {
                b.setBannedGameEndTime(-1);
            } else {
                b.setBannedGameEndTime(bannedPlayerInfo.getEndTime());
            }

            b.setServerTime(ServerConstants.getCurrentTimeMillis())
                    .setResult(Text.genOkServerRstInfo())
                    .setLoginCheckResultType(PbProtocol.LoginCheckRst.LoginCheckResultType.BANNED_GAME)
                    .setReason(bannedPlayerInfo.getReason());
            gateNode.send(PtCode.LOGIN_CHECK_SERVER, b.build(), timeStamp);

            GameLogger.clientEvent(req, ClientEventType.玩家被封号拒绝连接, req.getIp());
            return;
        }

        if (LoginCheckManager.addTask(new LoginCheckTask(gateNode, req))) {
            GameLogger.clientEvent(req, ClientEventType.进入校验队列, req.getIp());
        }
    }

    /**
     * 创角
     */
    @Handler(PtCode.REGISTER_CHECK_CLIENT)
    private void register(GateNode gateNode, PbProtocol.RegisterCheckReq req) {
        PbProtocol.RegisterRst.Builder rst = PbProtocol.RegisterRst.newBuilder().setResult(Text.genOkServerRstInfo());
        String name = req.getName();
        int sex = req.getSex();
        int profession = req.getProfession();
        ByteString faceData = req.getFaceData();
        PbProtocol.LoginCheckReq loginCheckReq = req.getCheckParam(); req.getAfterLogin();
        logic:
        {
            if (faceData.isEmpty() || (sex != 1 && sex != 2) || ProfessionType.of(profession) == null) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            // 压测模式不检测名字合法性
            if (!Configuration.runMode.isPress()) {
                int result = PlayerHelper.checkNameValid(name);
                if (result != -1) {
                    rst.setResult(Text.genServerRstInfo(result));
                    break logic;
                }
            }
            // 超过了最大的注册数
            if (PlayerManager.isMaxRegister()) {
                rst.setResult(Text.genServerRstInfo(Text.超过最大注册人数上限));
                GameLogger.clientEvent(req, ClientEventType.注册结果本服注册人数过高);
                break logic;
            }
            if (!Player.isSameServer(loginCheckReq.getServerNum())) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                GameLogger.clientEvent(req, ClientEventType.注册非本服账号);
                break logic;
            }
            // 添加到任务队列
            RegisterCheckTask registerCheckTask = new RegisterCheckTask(gateNode, req);
            boolean isSuccess = RegisterCheckManager.addTask(registerCheckTask);
            if (!isSuccess) {
                rst.setResult(Text.genServerRstInfo(Text.注册失败));
                break logic;
            }
            return;
        }
        gateNode.send(loginCheckReq.getClientId(), PtCode.REGISTER_CHECK_SERVER, rst.build(), ServerConstants.getCurrentTimeMillis());
    }

    /**
     * 有包体测试协议
     */
    @Handler(PtCode.TEST_BODY_CLIENT)
    private void testWithBody(GateNode gateNode, PbProtocol.TestBodyReq req, long timeStamp) throws InvalidProtocolBufferException {
        CommonLogger.info("testWithBody text -> " + req.getText());

        gateNode.send(PtCode.TEST_BODY_SERVER, PbProtocol.TestBodyRst.newBuilder().setNum(1).build(), timeStamp);
    }

    /**
     * 无包体测试协议
     */
    @Handler(PtCode.TEST_NON_BODY_CLIENT)
    private void testNonBody(GateNode gateNode, long timeStamp) {
        gateNode.send(PtCode.TEST_NON_BODY_SERVER, null, timeStamp);
    }

    /**
     * gate注册
     */
    @Handler(PtCode.GATE_REGISTER_GS)
    private void gateRegister(GateNode gateNode, PbProtocol.GateRegistReq register, long timeStamp) throws InvalidProtocolBufferException {
        GateNodeManager.getInstance().clearGateNode(register.getGateId());
        gateNode.serverId = register.getGateId();
        gateNode.send(0, PtCode.GATE_REGISTER_GS, null, timeStamp);
        CommonLogger.info(String.format("gateNode connect, id: %s -> %s", gateNode.serverId, gateNode.getConnectInfo()));
    }

    /**
     * gate心跳
     */
    @Handler(PtCode.GATE_HEART_GS)
    private void gateHeart(GateNode gateNode) {
        gateNode.send(0, PtCode.GATE_HEART_GS, null, ServerConstants.getCurrentTimeMillis());
    }

    /**
     * 玩家登出
     */
    @Handler(PtCode.GATE_PLAYER_LOGOUT_GATE)
    private void playerLogout(GateNode gateNode, PbProtocol.
            PlayerLogOutByGate logout) throws InvalidProtocolBufferException {
        Player player = PlayerManager.getOnlinePlayer(logout.getPlayerId());
        if (Objects.nonNull(player)) {
            player.setOnLine(false);
        }
    }

    /**
     * 登陆排队心跳
     */
    @Handler(PtCode.LOGIN_HEART_CLIENT)
    private void loginHeartClient(GateNode gateNode, PbProtocol.LoginHeartClientReq loginHeartClientReq) throws InvalidProtocolBufferException {
        long clientId = loginHeartClientReq.getClientId();
        Map<String, LoginTask> logins = LoginQueueManger.getInstance().getLogins();
        for (LoginTask loginTask : logins.values()) {
            if (loginTask.getClientId() == clientId) {
                loginTask.setLastActiveTime(ServerConstants.getCurrentTimeMillis());
                loginTask.heart();
                return;
            }
        }
        gateNode.send(clientId, PtCode.LOGIN_HEART_SERVER, PbProtocol.HeartRst.newBuilder()
                .setServerTime(-1)
                .setInterval(0)
                .build());
    }

    /**
     * 检查能否刷新名字，若可以刷新并标记时间
     */
    private static boolean canRandomName(long clientId){
        Object lastTime = cache.getIfPresent(clientId);
        if(lastTime == null){
            cache.put(clientId, ServerConstants.getCurrentTimeMillis());
            return true;
        }
        return false;
    }

    /**
     * 创角随机一个玩家名称
     */
    @Handler(PtCode.REGISTER_GET_RANDOM_NAME_CLIENT)
    private void registerRandomName(GateNode gateNode, PbProtocol.RegisterGetRandomNameReq req, long time) {
        PbProtocol.RegisterGetRandomNameRst.Builder builder = PbProtocol.RegisterGetRandomNameRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        logic:
        {
            int gender = req.getGender();
            if (gender != 1 && gender != 2) {
                builder.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            if (!canRandomName(req.getClientId())) {
                builder.setResult(Text.genServerRstInfo(Text.操作太频繁稍后再试));
                break logic;
            }
            builder.setName(NameManager.randomName(gender));
        }

        gateNode.send(req.getClientId(), PtCode.REGISTER_GET_RANDOM_NAME_SERVER, builder.build(), time);
    }

    @Handler(PtCode.CREATE_ROLE_REQ)
    private void createRole(GateNode gateNode, PbProtocol.CreateRoleReq req) {
        PbProtocol.CreateRoleRst.Builder rst = PbProtocol.CreateRoleRst.newBuilder().setResult(Text.genOkServerRstInfo());
        String name = req.getName();
        int sex = req.getSex();
        int profession = req.getProfession();
        ByteString faceData = req.getFaceData();
        logic:
        {
            if (faceData.isEmpty() || (sex != 1 && sex != 2) || ProfessionType.of(profession) == null) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            // 压测模式不检测名字合法性
            if (!Configuration.runMode.isPress()) {
                int result = PlayerHelper.checkNameValid(name);
                if (result != -1) {
                    rst.setResult(Text.genServerRstInfo(result));
                    break logic;
                }
            }
            Account account = toRegisterAccounts.get(req.getClientId());
            if (account == null) {
                rst.setResult(Text.genServerRstInfo(Text.数据异常));
                break logic;
            }

            // 超过了最大的注册数
            if (PlayerManager.isMaxRegister()) {
                rst.setResult(Text.genServerRstInfo(Text.超过最大注册人数上限));
                GameLogger.clientEvent(account, ClientEventType.注册结果本服注册人数过高);
                break logic;
            }
            if (!Player.isSameServer(req.getServerNum())) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                GameLogger.clientEvent(account, ClientEventType.注册非本服账号);
                break logic;
            }

            // 添加到任务队列
            CreateRoleAsyncCall asyncCall = new CreateRoleAsyncCall(account,req, gateNode,req.getClientId());
            ThreadPool.execute(asyncCall);
            return;
        }
        gateNode.send(req.getClientId(), PtCode.CREATE_ROLE_RST, rst.build(), ServerConstants.getCurrentTimeMillis());
    }

    public GateNodeConnection getConnection() {
        return connection;
    }

    @Deprecated
    @Override
    public long getPlayerId() {
        return 0;
    }

    @Override
    public GateNode getGateNode() {
        return this;
    }

    public enum LoginTimePoint {
        checkReady,
        checkStart,
        checkEnd,
        loginStart,
        loginEnd,
        finishStart,
    }

    public Map<Long, Account> getToRegisterAccounts() {
        return toRegisterAccounts;
    }
}

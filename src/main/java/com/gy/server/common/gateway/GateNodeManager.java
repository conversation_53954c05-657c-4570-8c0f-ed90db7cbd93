package com.gy.server.common.gateway;

import com.google.protobuf.AbstractMessage;
import com.gy.server.common.gateway.net.GateNodeConnection;
import com.gy.server.core.log.CommonLogger;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.packet.PtCode;
import com.gy.server.net.netty.NetConnection;
import com.gy.server.net.netty.NetConnectionAcceptor;
import com.gy.server.net.netty.NettyTcpServer;
import com.gy.server.net.util.NetUtil;
import com.gy.server.packet.PbPacket;
import com.gy.server.packet.PbProtocol;
import com.gy.server.scene.base.SceneMessageBroadcaster;
import com.gy.server.utils.function.Ticker;
import io.netty.channel.ChannelHandlerContext;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR> - [Created on 2022-02-26 15:47]
 */
public class GateNodeManager implements NetConnectionAcceptor<NettyTcpServer>, Ticker {

    private static final GateNodeManager instance = new GateNodeManager();
    GateNodeManager() { }
    public static GateNodeManager getInstance() {
        return instance;
    }

    private CopyOnWriteArrayList<GateNode> gateNodes = new CopyOnWriteArrayList<>();
    private Map<Long, Integer> playerId2GateId = new ConcurrentHashMap<>();
    private volatile AtomicBoolean accept = new AtomicBoolean(false);

    private static boolean running = true;


    public static void shutdown() {
        running = false;
    }

    public GateNode getGateNodeByServerId(int serverId) {
        return gateNodes.stream().filter(GateNode -> GateNode.getServerId() > 0)
                .filter(GateNode -> GateNode.getServerId() == serverId)
                .findFirst().orElse(null);
    }


    public List<GateNode> getGateNodes() {
        return Collections.unmodifiableList(gateNodes);
    }

    public Map<Long, Integer> getPlayerId2GateId() {
        return playerId2GateId;
    }
    public int getPlayerId2GateId(long playerId) {
        return playerId2GateId.getOrDefault(playerId, 0);
    }

    public void setPlayerId2GateId(Map<Long, Integer> playerId2GateId) {
        this.playerId2GateId = playerId2GateId;
    }

    public void addPlayerId2GateId(long playerId, int gateId) {
        playerId2GateId.put(playerId, gateId);
    }

    public void removePlayerId2GateId(long playerId){
        playerId2GateId.remove(playerId);
    }

    public void clearGateNode(int serverId){
        for (GateNode gateNode : gateNodes) {
            //先关掉连接
            if(gateNode.getServerId() == serverId){
                gateNode.getConnection().close();
            }
        }
        boolean result = gateNodes.removeIf(next -> next.getServerId() == serverId);
        if(result){
            CommonLogger.info("gateNode clear, serverId : " + serverId);
        }
    }

    /**
     * 所有gate广播
     */
    public void broadcastMsg(AbstractMessage message){
        for (GateNode gateNode : gateNodes) {
//            gateNode.send(PtCode.SCENE_BROADCAST, message);
            gateNode.send(PtCode.SCENE_BROADCAST, message);
        }
    }

    @Deprecated
    public void broadcastMsg(List<Long> playerIds, int ptCode, AbstractMessage message){
        PbProtocol.SceneBroadcastNotify.Builder builder = PbProtocol.SceneBroadcastNotify.newBuilder();

        PbProtocol.SceneBroadcastNotify.SceneBroadcastInfo.Builder infoBuilder = PbProtocol.SceneBroadcastNotify.SceneBroadcastInfo.newBuilder();
        infoBuilder.addAllPlayerIds(playerIds);
        PbPacket.Packet.Builder packet = PbPacket.Packet.newBuilder();
        packet.setPtCode(ptCode);
        packet.setTimeStamp(System.currentTimeMillis());
        packet.setData(message.toByteString());
        infoBuilder.setPacket(packet);

        builder.addInfos(infoBuilder.build());
        broadcastMsg(builder.build());
    }

    public void broadcastMsg(List<Long> broadcastPlayerIds, int ptCode, AbstractMessage message, Long... sendPlayerIds){
        SceneMessageBroadcaster.getInstance().broadcastMsg(broadcastPlayerIds, ptCode, message, sendPlayerIds);
    }

    public void broadcast(Collection<Long> broadcastPlayerIds, int ptCode, AbstractMessage message){

        PbPacket.Packet.Builder packet = PbPacket.Packet.newBuilder();
        packet.setData(message.toByteString());
        packet.setPtCode(ptCode);
        packet.setTimeStamp(System.currentTimeMillis());
        PbProtocol.SceneBroadcastNotify.SceneBroadcastInfo.Builder builder = PbProtocol.SceneBroadcastNotify.SceneBroadcastInfo.newBuilder();
        builder.setPacket(packet);
        builder.addAllPlayerIds(broadcastPlayerIds);
        PbProtocol.SceneBroadcastNotify.Builder notify = PbProtocol.SceneBroadcastNotify.newBuilder();
        notify.addInfos(builder);
        broadcastMsg(notify.build());
    }

    @Override
    public NetConnection accept(ChannelHandlerContext ctx, NettyTcpServer provider) {
        if (!running) {
            return null;
        }

        GateNodeConnection connection = new GateNodeConnection(ctx);
        GateNode node = new GateNode(connection);
        gateNodes.add(node);
        GameLogger.std("add connection : " + NetUtil.getRemoteAddressInfo(ctx));

        return connection;
    }

    public boolean isAccept() {
        return accept.get();
    }

    public void setAccept(boolean accept) {
        this.accept.set(accept);
    }

    @Override
    public void tick() {
        if(accept.get()){
            for (GateNode node : gateNodes) {
                try {
                    boolean remove = node.tickNet();
                    if (remove) {
                        gateNodes.remove(node);
                        CommonLogger.info(String.format("GateNode disconnect, id: %s -> %s", node.getServerId(), node.getConnectInfo()));
                    }
                } catch (Exception e) {
                    CommonLogger.error(e);
                }
            }
        }
    }

}

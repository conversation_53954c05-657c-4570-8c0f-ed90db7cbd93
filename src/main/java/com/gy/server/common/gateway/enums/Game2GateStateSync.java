package com.gy.server.common.gateway.enums;

import java.util.HashMap;
import java.util.Map;

import com.gy.server.core.Configuration;
import com.gy.server.core.ServerType;
import com.gy.server.game.player.PlayerManager;

import org.apache.commons.lang3.tuple.Pair;

import static com.gy.server.core.ServerType.game;
import static com.gy.server.core.ServerType.scene;

/**
 * game项目服务器向gate服务器同步状态接口管理
 * <AUTHOR> - [Created on 2022-03-16 13:46]
 */
public enum Game2GateStateSync {

    gameServer(game, "gsInfoMessage", ()->{
        Object[] param = new Object[4];
        param[0] = Configuration.serverId;
        param[1] = Configuration.serverIp;
        param[2] = Configuration.tcpPort;
        param[3] = PlayerManager.getOnlinePlayers().size();
        return param;
    }),
    sceneServer(scene, "sceneInfoMessage", ()->{
        Object[] param = new Object[3];
        param[0] = Configuration.serverId;
        param[1] = Configuration.serverIp;
        param[2] = Configuration.tcpPort;
        return param;
    }),

    ;
    ServerType serverType;
    String interfaceName;
    GGStateSyncParamGet params;
    Game2GateStateSync(ServerType serverType, String interfaceName, GGStateSyncParamGet params){
        this.serverType = serverType;
        this.interfaceName = interfaceName;
        this.params = params;
    }

    private static Map<ServerType, String> interfaceNames = new HashMap<>();
    private static Map<ServerType, GGStateSyncParamGet> interfaceParams = new HashMap<>();
    static{
        for (Game2GateStateSync value : Game2GateStateSync.values()) {
            interfaceNames.put(value.serverType, value.interfaceName);
            interfaceParams.put(value.serverType, value.params);
        }
    }

    public static Pair<String, Object[]> getInterfaceInfo(ServerType serverType){
        return Pair.of(interfaceNames.getOrDefault(serverType, ""), interfaceParams.containsKey(serverType) ? interfaceParams.get(serverType).getParam() : new Object[0]);
    }

    private interface GGStateSyncParamGet {
        Object[] getParam();
    }

}

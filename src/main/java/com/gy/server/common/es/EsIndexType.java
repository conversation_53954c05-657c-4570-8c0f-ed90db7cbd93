package com.gy.server.common.es;

import java.util.Objects;

import com.gy.server.common.es.test.EsTestInfo;
import com.gy.server.core.Configuration;
import com.gy.server.db.search.ESManager;
import com.gy.server.db.search.IndexDocument;
import com.gy.server.game.league.bean.EsLeagueNameInfo;
import com.gy.server.game.player.name.EsPlayerNameInfo;

/**
 * es index name
 * <AUTHOR> - [Created on 2022-11-09 13:35]
 */
public enum EsIndexType {

    test_info(new EsTestInfo()),
    player_name(new EsPlayerNameInfo()),
    league_name(new EsLeagueNameInfo()),


    ;

    IndexDocument indexDocument;
    EsIndexType(IndexDocument indexDocument){
        this.indexDocument = indexDocument;
    }

    public String[] getChineseField(){
        return Objects.nonNull(indexDocument) ? indexDocument.getChineseField() : null;
    }

    private static final String baseName = "tl-game";
    public String genIndexName(){
        return ESManager.genIndexName(baseName, Configuration.runMode.name(), this.name());
    }

}

package com.gy.server.common.es;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.aggregations.Aggregate;
import co.elastic.clients.elasticsearch._types.aggregations.StatsAggregate;
import co.elastic.clients.elasticsearch._types.mapping.TypeMapping;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryBuilders;
import co.elastic.clients.elasticsearch.core.*;
import co.elastic.clients.elasticsearch.core.bulk.BulkOperation;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.elasticsearch.indices.*;
import co.elastic.clients.elasticsearch.indices.get_mapping.IndexMappingRecord;
import co.elastic.clients.json.JsonpMapper;
import com.gy.server.common.es.test.EsTestInfo;
import com.gy.server.core.Configuration;
import com.gy.server.db.search.ESManager;
import com.gy.server.db.search.IndexDocument;
import com.gy.server.game.player.name.EsPlayerNameInfo;
import jakarta.json.Json;
import jakarta.json.stream.JsonParser;
import org.apache.commons.lang3.tuple.Pair;

import java.io.IOException;
import java.io.StringReader;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * es二次封装
 * 1.速度相对比较慢，不能插入后直接查询
 * 2.id不能重复，会跑错（ps:需要保证IndexDocument的id唯一）
 * http://xy-center.shangua.com:9200
 * http://tl.shangua.com:9200
 * <AUTHOR> - [Created on 2022-11-02 16:20]
 */
public class EsUtil {
//    private static final String default_es_url = "xy-center.shangua.com";
    private static String default_es_url = "tl.shangua.com";
    private static int default_es_port = 9200;

    private static ElasticsearchClient client;

    public static void start(){
        if(Configuration.esUser.isEmpty() || Configuration.esPwd.isEmpty()){
            ESManager.init(Configuration.esUrl.isEmpty() ? default_es_url : Configuration.esUrl, Configuration.esPort <= 0 ? default_es_port : Configuration.esPort);
        }else {
            //需要密码
            ESManager.init(Configuration.esUrl.isEmpty() ? default_es_url : Configuration.esUrl
                    , Configuration.esPort <= 0 ? default_es_port : Configuration.esPort
                    , Configuration.esUser, Configuration.esPwd);
        }

        client = ESManager.getInstance().getClient();

        //初始化中文分词器
        for (EsIndexType indexType : EsIndexType.values()) {
            String indexName = indexType.genIndexName();
            String[] chineseFields = indexType.getChineseField();
            if(Objects.nonNull(chineseFields)){
                for (String chineseField : chineseFields) {
                    try {
                        createIkIndex(indexName, chineseField);
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    public static ElasticsearchClient getClient(){
        return client;
    }

    public static void stop(){
        ESManager.getInstance().shutdown(2000L);
    }

    private static void check(IndexDocument document){
        if(Objects.isNull(client)){
            throw new IllegalArgumentException("ElasticsearchClient need start, please invoke EsUtil:start");
        }
        if(Objects.nonNull(document) && document.getId().isEmpty()){
            throw new IllegalArgumentException("ElasticsearchClient IndexDocument id is null : " + document.getClass().getName());
        }
    }

    /**
     * 插入对象(id不可以重复)
     * @param document 对象
     */
    public static boolean insert(IndexDocument document) {
        check(document);
        CreateRequest.Builder<IndexDocument> builder = new CreateRequest.Builder<>();
        builder.index(document.getIndexName()).id(document.getId()).document(document);
        try {
            CreateResponse createResponse = client.create(builder.build());
            return "created".equals(createResponse.result().jsonValue());
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 修改内容
     */
    public static boolean update(IndexDocument document){
        check(document);
        try {
            UpdateResponse response = client.update(UpdateRequest.of(u->u.index(document.getIndexName()).id(document.getId()).doc(document)), document.getClass());
            return "Updated".equals(response.result().jsonValue());
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 批量插入
     * @param indexName es字典名字
     * @param list 数据列表
     */
    public static <T> boolean insertBatch(String indexName, List<T> list){
        BulkRequest.Builder builder = new BulkRequest.Builder();
        builder.index(indexName).operations(list.stream().map(t -> new BulkOperation.Builder().create(d -> d.id(((IndexDocument)t).getId()).document(t)).build()).collect(Collectors.toList()));
        try {
            client.bulk(builder.build());
            return true;
        } catch (IOException e) {
            return false;
        }
    }


    /**
     * 模糊查询(通配符模糊查询)
     * @param indexName es唯一字典名字
     * @param fieldName 需要查询列名字
     * @param context 内容（类似mysql like）
     * @param clazz 返回类class
     * @param page 页数
     * @param size 数量
     */
    public static <T> List<T> wildcardSearch(String indexName, String fieldName, String context
            , Class<T> clazz, int page, int size) throws IOException {
        check(null);
        SearchRequest.Builder sr = new SearchRequest.Builder();
        sr.index(indexName).query(QueryBuilders.wildcard(builder -> {
            builder.field(fieldName).wildcard(context);
            return builder;
        })).from(page).size(size);

        SearchResponse<T> search = client.search(sr.build(), clazz);

        return search.hits().hits().stream().map(Hit::source).collect(Collectors.toList());
    }

    /**
     * 精准删除
     * @param indexName es唯一字典名字
     * @param id 全局id
     */
    public static boolean accurateDelete(String indexName, String id) throws IOException {
        check(null);
        DeleteRequest.Builder delBuilder = new DeleteRequest.Builder();
        delBuilder.index(indexName).id(id);
        DeleteResponse delete = client.delete(delBuilder.build());
        return "Deleted".equals(delete.result().jsonValue());
    }

    /**
     * 精准查询全局id
     * @param indexName 字典名字
     * @param matches 查询条件 key:fieldName value:content (默认返回)
     * @param clazz 返回类class
     * @param page 页数
     * @param size 数量
     */
    public static <T> List<String> accurateSearchId(String indexName, List<Pair<String, String>> matches, Class<T> clazz
            , int page, int size) throws IOException {
        check(null);
        SearchRequest.Builder sr = new SearchRequest.Builder();
        sr.index(indexName).query(q -> q.match(m -> {
            for (Pair<String, String> match : matches) {
                m.field(match.getKey()).query(match.getValue());
            }
            return m;
        })).from(page).size(size);

        SearchResponse<T> search = client.search(sr.build(), clazz);
        return search.hits().hits().stream().map(Hit::id).collect(Collectors.toList());
    }

    /**
     * 精准匹配未经分词的词项 （直接匹配字段原始值，不分析查询词）
     * @param indexName 字典名字
     * @param fieldName 需要查询列名字
     * @param context 内容（类似mysql like）
     * @param clazz 返回类class
     * @param page 页数
     * @param size 数量
     */
    public static <T> List<T> termSearch(String indexName, String fieldName, String context
            , Class<T> clazz, int page, int size) throws IOException {
        check(null);
        SearchRequest.Builder sr = new SearchRequest.Builder();
        sr.index(indexName).query(q -> q.term(m -> m.field(fieldName).value(context))).from(page).size(size);

        SearchResponse<T> search = client.search(sr.build(), clazz);
        return search.hits().hits().stream().map(Hit::source).collect(Collectors.toList());
    }

    /**
     * 模糊查询（允许拼写错误）
     * @param indexName 字典名字
     * @param fieldName 需要查询列名字
     * @param context 内容
     * @param clazz 返回类class
     * @param page 页数
     * @param size 数量
     * @return 查询结果
     */
    public static <T> List<T> fuzzySearch(String indexName, String fieldName, String context
            , Class<T> clazz, int page, int size) throws IOException {
        check(null);
        SearchRequest.Builder sr = new SearchRequest.Builder();
        sr.index(indexName).query(QueryBuilders.fuzzy(builder -> {
            builder.field(fieldName).value(context);
            return builder;
        })).from(page).size(size);

        SearchResponse<T> search = client.search(sr.build(), clazz);
        return search.hits().hits().stream().map(Hit::source).collect(Collectors.toList());
    }

    /**
     * 全文搜索（对搜索文本分词后匹配）
     * @param indexName 字典名字
     * @param fieldName 需要查询列名字
     * @param context 内容
     * @param clazz 返回类class
     * @param page 页数
     * @param size 数量
     * @return 查询结果
     */
    public static <T> List<T> matchSearch(String indexName, String fieldName, String context
            , Class<T> clazz, int page, int size) throws IOException {
        check(null);
        SearchRequest.Builder sr = new SearchRequest.Builder();
        sr.index(indexName).query(QueryBuilders.matchPhrase(builder -> {
            builder.field(fieldName).query(context);
            return builder;
        })).from(page).size(size);

        SearchResponse<T> search = client.search(sr.build(), clazz);
        return search.hits().hits().stream().map(Hit::source).collect(Collectors.toList());
    }


    /**
     * 打印所有映射关系
     * @param indexName 字典名字
     * @throws IOException
     */
    public static void logAllMapping(String indexName) throws IOException {
        GetMappingRequest.Builder request = new GetMappingRequest.Builder();
        request.index(indexName);
        GetMappingResponse mapping = client.indices().getMapping(request.build());
        Map<String, IndexMappingRecord> mappingRecordMap = mapping.result();
        mappingRecordMap.forEach((k, v)->{
            TypeMapping mappings = v.mappings();
            System.out.println(mappings);
            System.out.println();
        });
    }

    /**
     * 创建ik搜索类型index，并且设置ik搜索列
     * @param indexName 字典名字
     * @param fieldName 列名字
     */
    public static void createIkIndex(String indexName, String fieldName) throws IOException {
        //校验当前index是否已经有fieldName
        boolean hasMapping = true;
        try {
            GetMappingRequest.Builder getMappingRequest = new GetMappingRequest.Builder();
            getMappingRequest.index(indexName);
            GetMappingResponse mapping = client.indices().getMapping(getMappingRequest.build());
            Map<String, IndexMappingRecord> mappingRecordMap = mapping.result();
            IndexMappingRecord indexMappingRecord = mappingRecordMap.get(indexName);
            if(Objects.nonNull(indexMappingRecord)){
                TypeMapping mappings = indexMappingRecord.mappings();
                if(mappings.properties().containsKey(fieldName)){
                    //已经存在field 所以不需要添加
                    return;
                }
            }
        }catch (Exception e){
            //忽略没有index
            hasMapping = false;
        }
        if(!hasMapping){
            //添加index
            String mappingJson = genIkMappingJson(fieldName);
            CreateIndexRequest.Builder request = new CreateIndexRequest.Builder();
            request.index(indexName);
            JsonpMapper mapper = client._transport().jsonpMapper();
            JsonParser parser = Json.createParser(new StringReader(mappingJson));
            request.mappings(TypeMapping._DESERIALIZER.deserialize(parser, mapper));
            client.indices().create(request.build());
        }else{
            addIkIndex(indexName, fieldName);
        }
    }

    /**
     * 增加ik搜索类型列
     * @param indexName 字典名字
     * @param fieldName 列名字
     */
    public static void addIkIndex(String indexName, String fieldName){
        PutMappingRequest.Builder request = new PutMappingRequest.Builder();
        request.index(indexName);
        String mappingJson = genIkMappingJson(fieldName);
        // 设置映射
        request.withJson(new StringReader(mappingJson));
        try {
            client.indices().putMapping(request.build());
        } catch (IOException e) {
            //未存在的index，会报错，可以忽略
        }
    }

    /**
     * 删除整个字典
     * @param indexName 字典名字
     */
    public static void delIndex(String indexName){
        DeleteIndexRequest.Builder request = new DeleteIndexRequest.Builder();
        request.index(indexName);
        try {
            client.indices().delete(request.build());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static String genIkMappingJson(String fieldName){
        return  "{\n" +
                "  \"properties\": {\n" +
                "    \""+fieldName+"\": {\n" +
                "      \"type\": \"text\",\n" +
                "      \"analyzer\": \"ik_smart\",\n" +
                "      \"search_analyzer\": \"ik_smart\"\n" +
                "    }\n" +
                "  }\n" +
                "}";
    }
    

    /**
     * 多列查询
     * @param indexName 字典名字
     * @param accurateFieldName 精准查找列名
     * @param accurateContext 精准查找内容（可多个）
     * @param wildcardFieldName 模糊查找列名
     * @param wildcardContext 模糊查找内容
     * @param clazz 返回类
     * @param page 页数
     * @param size 数量
     * @return 查询数据
     * @throws IOException
     */
    public static <T> List<T> wildcardSearch(String indexName,
                                             String accurateFieldName, List<String> accurateContext,
                                             String wildcardFieldName, String wildcardContext,
                                             Class<T> clazz, int page, int size) throws IOException {
        check(null);
        SearchRequest.Builder sr = new SearchRequest.Builder();

        /**
         * must:必须匹配，类似逻辑与（参与计分）
         * should：可以匹配，类似逻辑或（参与计分）
         * must_not：必须不匹配，类似逻辑非（不参与计分）
         * filter:必须匹配（不参与计分）
         * 打分字段越多，性能越差
         */
        sr.index(indexName)
                .query(qb -> qb.bool( bqb-> {
                    bqb.filter(q -> q.match(m -> {
                        for (String context: accurateContext) {
                            m.field(accurateFieldName).query(context);
                        }
                        return m;
                    })).filter(QueryBuilders.wildcard(builder -> {
                        builder.field(wildcardFieldName).wildcard(wildcardContext);
                        return builder;
                    }));
                    return bqb;
                })).from(page).size(size);

        SearchResponse<T> search = client.search(sr.build(), clazz);

        return search.hits().hits().stream().map(Hit::source).collect(Collectors.toList());
    }


    public static void main(String[] args) throws IOException {
        Configuration.esUrl = "";
        Configuration.esUser = "";
        Configuration.esPwd = "";
        Configuration.runMode = Configuration.RunMode.test;
        EsUtil.start();

//        test();


        EsIndexType indexType = EsIndexType.player_name;
        String indexName = indexType.genIndexName();


        logAllMapping(indexName);
        System.out.println("search finish");

//        EsUtil.delIndex(indexName);

//        int serverId = 990;

//        EsTestInfo tri = new EsTestInfo(3000, "机智的人机", "艾芙琳", 1);
//        EsTestInfo tri1 = new EsTestInfo(3001, "聪明的护士长", "艾芙", 2);
//        EsTestInfo tri2 = new EsTestInfo(10003, "123", "aabdd", 43223);
//        EsTestInfo tri3 = new EsTestInfo(10004, "456", "aabbdd", 654);
//        EsTestInfo tri4 = new EsTestInfo(10005, "456", "aa11bb", 231);
//        EsTestInfo tri5 = new EsTestInfo(10005, "456", "aaaabb",654734);
//
//
//        EsUtil.insert(tri);
//        EsUtil.insert(tri1);

//        String context = "*护士长*";
//        String fieldName = "info1";
//        System.out.println(EsUtil.termSearch(indexName
//                , fieldName, context, EsTestInfo.class, 0, 10));
//        System.out.println(EsUtil.matchSearch(indexName
//                , fieldName, context, EsTestInfo.class, 0, 10));
//        System.out.println(EsUtil.wildcardSearch(indexName
//                , fieldName, context, EsTestInfo.class, 0, 10));
//        System.out.println(EsUtil.fuzzySearch(indexName
//                , fieldName, context, EsTestInfo.class, 0, 10));

//        String context = "*过分四个*";
//        List<EsTestInfo> result = EsUtil.wildcardSearch(EsIndexType.test_info.genIndexName(), "info1", context, EsTestInfo.class, 0, 10);
//        List<EsTestInfo> result = EsUtil.accurateSearch(EsIndexType.test_info.genIndexName(), "info1", context, EsTestInfo.class, 0, 10);
//        System.out.println(result);
//        EsUtil.insert(tri2);
//        EsUtil.insert(tri3);
//        EsUtil.insert(tri4);
//        EsUtil.insert(tri5);

//        indexName = EsIndexType.test_info.genIndexName();
//        List<String> ids = new ArrayList<>();
//        ids.add("456");
//        List<EsTestInfo> esTestInfos = wildcardSearch(indexName, "info1", ids, "info2", "aabb*"
//                , EsTestInfo.class, 0, 10);
//        for (EsTestInfo esTestInfo : esTestInfos) {
//            System.out.println(esTestInfo);
//        }
//        addIkIndex(indexName, "aaa");

//        EsUtil.accurateDelete(indexName, "1003");
//        EsUtil.accurateDelete(indexName, "1004");
//
//        EsPlayerNameInfo nameInfo = new EsPlayerNameInfo();
//        nameInfo.setId("1005");
//        nameInfo.setServerId(1);
//        nameInfo.setPlayerId(11);
//        nameInfo.setName("机智的人机");
////
//        System.out.println(EsUtil.insert(nameInfo));
////
//        EsPlayerNameInfo nameInfo1 = new EsPlayerNameInfo();
//        nameInfo1.setId("1009");
//        nameInfo1.setServerId(2);
//        nameInfo1.setPlayerId(22);
//        nameInfo1.setName("聪明");
//
//        System.out.println(EsUtil.insert(nameInfo1));
//
//        nameInfo1 = new EsPlayerNameInfo();
//        nameInfo1.setId("1008");
//        nameInfo1.setServerId(2);
//        nameInfo1.setPlayerId(22);
//        nameInfo1.setName("明");
//
//        System.out.println(EsUtil.insert(nameInfo1));

//        System.out.println(nameInfo.getIndexName());

//        EsUtil.logAllMapping(indexName);

//        ElasticsearchClient client = EsUtil.getClient();
//        client.index
//        SearchRequest.Builder sr = new SearchRequest.Builder();
//        sr.index(indexName).query(QueryBuilders.fuzzy(builder -> {
//            builder.field("name").fuzziness("*琳*");
//            return builder;
//        })).from(0).size(10);
//
//        SearchResponse<EsPlayerNameInfo> search = client.search(sr.build(), EsPlayerNameInfo.class);
//        System.out.println(search);

//        List<EsPlayerNameInfo> nameInfos = EsUtil.wildcardSearch(EsIndexType.player_name.genIndexName()
//                , "name1", "*艾芙琳*", EsPlayerNameInfo.class, 0, 10);
//        String context = "护士长";
//        List<EsPlayerNameInfo> nameInfos = EsUtil.matchSearch(indexName
//                , "name", context, EsPlayerNameInfo.class, 0, 10);
//        System.out.println(nameInfos);
//
//        nameInfos = EsUtil.wildcardSearch(indexName
//                , "name", context, EsPlayerNameInfo.class, 0, 10);
//        System.out.println(nameInfos);

//        long startTime = System.currentTimeMillis();
//        NameHelper.clearAllName(serverId);
//        System.out.println("cost : " + (System.currentTimeMillis() - startTime));
//
//
//        nameInfos = EsUtil.wildcardSearch(indexName, "id", serverId + "*", EsPlayerNameInfo.class, 0, 20);
//        System.out.println(nameInfos);
//        EsUtil.clear();
    }

    public static void test() throws IOException {
        String indexName = EsIndexType.test_info.genIndexName();


        EsUtil.logAllMapping(indexName);

        SearchRequest.Builder sr = new SearchRequest.Builder();
        sr.index(indexName).query(q->{
            q.fuzzy(fqb->fqb.field("info2").value("aabb").fuzziness("2"));
            return q;
        });
        SearchResponse<EsTestInfo> search = client.search(sr.build(), EsTestInfo.class);
        List<EsTestInfo> collect = search.hits().hits().stream().map(Hit::source).collect(Collectors.toList());
        for (EsTestInfo esTestInfo : collect) {
            System.out.println(esTestInfo);
        }

        System.out.println("------------------------");
        SearchResponse<Map> search1 = client.search(srBuilder ->
                srBuilder.index(indexName)
                .aggregations("maxId", ab->ab.max(mab->mab.field("count")))
                .aggregations("minId", ab->ab.min(mab->mab.field("count")))
                .aggregations("avgId", ab->ab.avg(mab->mab.field("count"))), Map.class);


        Map<String, Aggregate> aggregations1 = search1.aggregations();
        aggregations1.forEach((k, v)-> System.out.println(k + " : " + v));

        System.out.println("------------------------");
        SearchResponse<Map> search2 = client.search(srBuilder ->
                srBuilder.index(indexName)
                        .aggregations("statCount", sb -> sb.stats(stb -> stb.field("count"))), Map.class);
        Map<String, Aggregate> aggregations2 = search2.aggregations();
        Aggregate statCount = aggregations2.get("statCount");
        StatsAggregate stats = statCount.stats();
        System.out.println("count : " + stats.count());
        System.out.println("max : " + stats.max());
        System.out.println("min : " + stats.min());
        System.out.println("avg : " + stats.avg());
        System.out.println("sum : " + stats.sum());

        System.out.println("------------------------");
        


    }

}

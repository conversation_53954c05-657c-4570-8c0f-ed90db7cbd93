package com.gy.server.common.es.test;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.gy.server.common.es.EsIndexType;
import com.gy.server.db.search.IndexDocument;

/**
 * <AUTHOR> - [Created on 2024-02-20 15:37]
 */
public class EsTestInfo extends IndexDocument implements Serializable {
    int id;

    String info1;

    String info2;

    int count;

    public EsTestInfo(){

    }

    public EsTestInfo(int id, String info1, String info2, int count){
        this.id = id;
        this.info1 = info1;
        this.info2 = info2;
        this.count = count;
    }

    @Override
    public String getId() {
        return id + "";
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getInfo1() {
        return info1;
    }

    public void setInfo1(String info1) {
        this.info1 = info1;
    }

    public String getInfo2() {
        return info2;
    }

    public void setInfo2(String info2) {
        this.info2 = info2;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    @Override
    public String getIndexName() {
        return EsIndexType.test_info.genIndexName();
    }

    @Override
    public String toString() {
        return id + " _ " + info1 + " _ " + info2;
    }

    @JsonIgnore
    @Override
    public String[] getChineseField() {
        //需要支持中文分词器的字段名
        return new String[]{"info1", "info2"};
    }
}

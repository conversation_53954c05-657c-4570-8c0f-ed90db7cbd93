package com.gy.server.common.distributedlock;

import com.gy.server.core.log.CommonLogger;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.lock.DistributedLock;

/**
 * <AUTHOR> - [Create on 2021/03/06 15:22]
 */
public class RedissonDistributedLockUtil implements IDistributedLockUtil {

    @Override
    public void operateWithLock(DistributedLockOperator operator, String lockKey) {
        DistributedLock lockUtil = TLBase.getInstance().getLockUtil();
        lockUtil.lock(lockKey);

        try {
            operator.operate();
        } catch (Exception e) {
            CommonLogger.error(e);
        } finally {
            lockUtil.unlock(lockKey);
        }
    }

    @Override
    public boolean operateWithTryLock(DistributedLockOperator operator, String lockKey) {
        DistributedLock lockUtil = TLBase.getInstance().getLockUtil();

        if (lockUtil.lock(lockKey)) {
            try {
                operator.operate();
            } catch (Exception e) {
                CommonLogger.error(e);
            } finally {
                lockUtil.unlock(lockKey);
            }
            return true;
        } else {
            return false;
        }
    }

    @Override
    public void operateWithMultiLock(DistributedLockOperator operator, String... lockKeys) {
        DistributedLock lockUtil = TLBase.getInstance().getLockUtil();

        lockUtil.lock(lockKeys);

        try {
            operator.operate();
        } catch (Exception e) {
            CommonLogger.error(e);
        } finally {
            lockUtil.unlock(lockKeys);
        }
    }

    @Override
    public boolean operateWithTryMultiLock(DistributedLockOperator operator, String... lockKeys) {
        DistributedLock lockUtil = TLBase.getInstance().getLockUtil();


        if (lockUtil.lock(lockKeys)) {
            try {
                operator.operate();
            } catch (Exception e) {
                CommonLogger.error(e);
            } finally {
                lockUtil.unlock(lockKeys);
            }
            return true;
        } else {
            return false;
        }
    }

}

package com.gy.server.common.distributedlock;

import java.util.Objects;

/**
 * 分布式锁管理器
 * 用于消除操作、尝试操作时，加锁/解锁的麻烦，并为分布式锁使用了一致性的操作
 * <AUTHOR> - [Create on 2021/03/06 15:25]
 */
public final class DistributedLockUtilManager {

    private static IDistributedLockUtil lockUtil;

    private DistributedLockUtilManager() {}

    /**
     * 默认使用Redisson分布式锁实现
     */
    public static void startup() {
        startup(new RedissonDistributedLockUtil());
    }

    public static void startup(IDistributedLockUtil lockUtil) {
        DistributedLockUtilManager.lockUtil = lockUtil;
    }

    public static void operate(DistributedLockOperator operator, String... lockKeys) {
        checkLockKeys(lockKeys);
        if (lockKeys.length == 1) {
            lockUtil.operateWithLock(operator, lockKeys[0]);
        } else {
            lockUtil.operateWithMultiLock(operator, lockKeys);
        }
    }

    public static boolean tryOperate(DistributedLockOperator operator, String... lockKeys) {
        checkLockKeys(lockKeys);
        if (lockKeys.length == 1) {
            return lockUtil.operateWithTryLock(operator, lockKeys[0]);
        } else {
            return lockUtil.operateWithTryMultiLock(operator, lockKeys);
        }
    }

    private static void checkLockKeys(String... lockKeys) {
        if (Objects.isNull(lockKeys) || lockKeys.length <= 0) {
            throw new RuntimeException("lock keys is empty.");
        }
    }

}

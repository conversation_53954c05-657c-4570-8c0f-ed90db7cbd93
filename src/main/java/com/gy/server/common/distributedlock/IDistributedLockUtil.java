package com.gy.server.common.distributedlock;

/**
 * <AUTHOR> - [Create on 2021/03/06 15:21]
 */
public interface IDistributedLockUtil {

    /**
     * 单锁 + 操作
     */
    void operateWithLock(DistributedLockOperator operator, String lockKey);

    /**
     * 尝试加锁，并执行操作
     * @return 是否加锁成功，并执行了操作
     */
    boolean operateWithTryLock(DistributedLockOperator operator, String lockKey);

    /**
     * 多锁 + 操作
     */
    void operateWithMultiLock(DistributedLockOperator operator, String... lockKeys);

    /**
     * 尝试加锁，并执行操作的多锁版本
     * @return 是否加锁成功，并执行了操作
     */
    boolean operateWithTryMultiLock(DistributedLockOperator operator, String... lockKeys);

}

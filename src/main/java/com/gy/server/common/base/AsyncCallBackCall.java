package com.gy.server.common.base;

import com.gy.server.core.callback.AbstractActiveCallbackTask;
import com.gy.server.core.command.CommandRequest;
import com.gy.server.core.delay.DelayTaskManager;

/**
 * 异步操作之后，主线程回调处理
 * @author: gbk
 * @date: 2025-03-03 06:50
 */
public abstract class AsyncCallBackCall extends AbstractActiveCallbackTask {

    public AsyncCallBackCall(CommandRequest request) {
        super(request);
    }

    @Override
    public void run() {
        //异步线程处理
        asyncExecute();
        DelayTaskManager.addTask(() -> {
            syncExecute();
            toCallback();
        });
    }

    @Override
    protected void execute() {
        //不需要处理
    }

    public abstract void asyncExecute();
    public abstract void syncExecute();

}
package com.gy.server.common;

import com.gy.server.core.Configuration;
import com.gy.server.core.ServerType;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.GameServerStateUpdater;
import com.gy.server.game.pay.PayCheckManager;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.utils.runner.Runner;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;

import java.util.ArrayList;

/**
 * @program: gs-trunk
 * @description: 节点状态同步器
 * @author: Huang.<PERSON>a
 * @create: 2025/5/14
 **/
public class NodeStatusSyncRunner implements Runner {
    @Override
    public void runnerExecute() throws Exception {
        if(Configuration.serverType == ServerType.game){

            ServerCommandRequest request = CommandRequests.newServerCommandRequest("GsWorldCommandService.gameHeart");
            TLBase.getInstance().getRpcUtil().sendToAll(com.ttlike.server.tl.baselib.ServerType.GM, request, Configuration.serverId,
                    PlayerManager.getOnlinePlayers().size(), PlayerManager.getMiniPlayers().size(), new ArrayList<>(GameServerStateUpdater.instance.getState().getServerNumbers()));

            request = CommandRequests.newServerCommandRequest("GameServerManagerCommandService.heart");
            TLBase.getInstance().getRpcUtil().sendToAll(com.ttlike.server.tl.baselib.ServerType.BILLING, request, Configuration.serverId,
                    PlayerManager.getOnlinePlayers().size(), PlayerManager.getMiniPlayers().size(), PlayerManager.isMaxRegister());
        }else if(Configuration.serverType == ServerType.world){

            ServerCommandRequest request = CommandRequests.newServerCommandRequest("GsWorldCommandService.worldHeart");
            TLBase.getInstance().getRpcUtil().sendToAll(com.ttlike.server.tl.baselib.ServerType.GM, request, Configuration.serverId);
        }else if(Configuration.serverType == ServerType.scene){

            ServerCommandRequest request = CommandRequests.newServerCommandRequest("GsWorldCommandService.sceneHeart");
            TLBase.getInstance().getRpcUtil().sendToAll(com.ttlike.server.tl.baselib.ServerType.GM, request, Configuration.serverId, 0/*在线人数*/);
        }
    }

    @Override
    public long getRunnerInterval() {
        return 2*1000;
    }
}

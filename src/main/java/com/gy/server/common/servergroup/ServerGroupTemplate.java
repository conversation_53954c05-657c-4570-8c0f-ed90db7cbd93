package com.gy.server.common.servergroup;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> - [Create on 2019/06/03 10:27]
 */
public class ServerGroupTemplate {

    private int group;
    private List<Integer> serverIds = new ArrayList<>();
    private int centerId;

    public ServerGroupTemplate(int group, String serverIdStr, int centerId) {
        this.group = group;

        for (String s : serverIdStr.split(",")) {
            serverIds.add(Integer.parseInt(s));
        }

        this.centerId = centerId;
    }

    public int getGroup() {
        return group;
    }

    public List<Integer> getServerIds() {
        return Collections.unmodifiableList(serverIds);
    }

    public int getCenterId() {
        return centerId;
    }

}

package com.gy.server.common.servergroup;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.service.Service;

/**
 * 服务器分组服务
 * <AUTHOR> - [Create on 2019/05/31 18:16]
 */
public class ServerGroupService implements Service {

    /**
     * <group: 模板>
     */
    public static Map<Integer, ServerGroupTemplate> serverGroups = new HashMap<>();

    @Override
    public boolean isWorldServer() {
        return true;
    }

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.servergroup_servergroup);
        Map<Integer, ServerGroupTemplate> serverGroupsTemp = new HashMap<Integer, ServerGroupTemplate>();
        for (Map<String, String> map : mapList) {
            int group = Integer.parseInt(map.get("group"));
            String serverIdStr = map.get("serverid");
            int centerId = Integer.parseInt(map.get("centerid"));

            ServerGroupTemplate template = new ServerGroupTemplate(group, serverIdStr, centerId);
            serverGroupsTemp.put(template.getGroup(), template);
        }
        serverGroups = serverGroupsTemp;
    }

    @Override
    public void clearConfigData() {
        serverGroups.clear();
    }

}

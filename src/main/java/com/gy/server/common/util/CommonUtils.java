package com.gy.server.common.util;

import com.google.protobuf.BoolValue;
import com.google.protobuf.Int32Value;
import com.gy.server.core.Configuration;
import com.gy.server.core.Launcher;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.log.CommonLogger;
import com.gy.server.core.log.SystemLogger;
import com.gy.server.game.combat.behaviour.tree.node.BehaviourTreeNode;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.combat.unit.HeroUnitCreator;
import com.gy.server.game.common.ExpBookCondition;
import com.gy.server.game.constant.ConstantService;
import com.gy.server.game.constant.ConstantType;
import com.gy.server.game.db.DbAssistant;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.hero.HeroService;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.monster.MonsterService;
import com.gy.server.game.monster.MonsterTemplate;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerData;
import com.gy.server.game.player.PlayerSaveManager;
import com.gy.server.packet.PbCommons;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.MathUtil;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.CommonsConfiguration;
import com.ttlike.server.tl.baselib.serialize.scene.ScenePosition3D;
import com.ttlike.server.tl.baselib.thread.ThreadPool;
import org.apache.commons.io.output.ByteArrayOutputStream;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;

import java.io.*;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.function.Predicate;

/**
 * <AUTHOR> - [Created on 2018/2/5 14:44]
 */
public class CommonUtils {

    /**
     * 序列化深拷贝
     */
    @SuppressWarnings("unchecked")
    public static <T extends Serializable> T deepCopy(T obj) {
        T cloneObj = null;
        ObjectOutputStream obs = null;
        ObjectInputStream ois = null;
        try {
            //写入字节流
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            obs = new ObjectOutputStream(out);
            obs.writeObject(obj);
            obs.close();

            //分配内存，写入原始对象，生成新对象
            ByteArrayInputStream ios = new ByteArrayInputStream(out.toByteArray());
            ois = new ObjectInputStream(ios);
            //返回生成的新对象
            cloneObj = (T) ois.readObject();
            ois.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (Objects.nonNull(obs)) {
                try {
                    obs.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

            if (Objects.nonNull(ois)) {
                try {
                    ois.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        return cloneObj;
    }

    /**
     * 根据给予的map的各个权重，随机出一个值
     *
     * @param map <T: weight>
     */
    public static <T> T randomNumForWeight(Map<T, Integer> map) {
        return randomNumForWeight(map, System.nanoTime());
    }

    public static <T> T randomNumForWeight(Map<T, Integer> map, long seed) {
        if (CollectionUtil.isEmpty(map)) {
            return null;
        }

        int weightTotal = 0;
        for (Map.Entry<T, Integer> entry : map.entrySet()) {
            weightTotal += entry.getValue();
        }

        if (weightTotal == 0) {
            SystemLogger.error("total weight is 0, please check data: " + map);
            return null;
        }

        double random = new Random(seed).nextDouble();
        int weightSplit = 0;
        T temp = null;
        for (Map.Entry<T, Integer> entry : map.entrySet()) {
            temp = entry.getKey();
            weightSplit += entry.getValue();
            if (random <= weightSplit / 1.0 / weightTotal) {
                return entry.getKey();
            }
        }

        // 永远无法到达的路径。为了躲避编译器用的
        return temp;
    }

    /**
     * 集合匹配，有交集则返回true
     */
    public static boolean join(Collection<Integer> c1, Collection<Integer> c2) {
        if (c1 == null || c2 == null || c1.contains(-1) || c2.contains(-1)) {
            //无效的集合表示匹配任意
            return true;
        }

        Set<Integer> tmp = new HashSet<>(c1);
        tmp.retainAll(c2);
        return tmp.size() > 0;
    }

    public static String genUUID() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 保留4位小数
     */
    public static String numberFormat4(double d) {
        return numberFormat(d, "#.00");
    }

    public static String numberFormat(double d, String pattern) {
        DecimalFormat df = new DecimalFormat(pattern);
        return df.format(d);
    }

    /**
     * 获取下一个凌晨5点
     * 当前时间小于凌晨5点时，返回当天的凌晨5点；
     * 否则返回第二天的凌晨5点
     */
    public static LocalDateTime getNextClock() {
        int refreshTimeHour = getRefreshTimeHour();
        LocalDateTime now = ServerConstants.getCurrentTimeLocalDateTime();
        LocalDateTime nextClock = now.withHour(refreshTimeHour).withMinute(0).withSecond(0).withNano(0);
        if (now.getHour() >= refreshTimeHour) {
            nextClock = nextClock.plusDays(1);
        }

        return nextClock;
    }

    /**
     * 根据参数exp，计算出兑换的经验书
     * 策略：优先转化为当前经验能达到的最高经验书，然后是次高经验书，...最后是最低级经验书
     * left: List<Reward>, right: 剩余的经验值
     */
    public static Pair<List<Reward>, Double> expToBook(final Player player, double exp, final List<ExpBookCondition> expBookConditions) {
        List<Reward> resultRewards = new ArrayList<>();

        // 只寻找当前玩家等级不小于的约束条件，处理完毕之后立即跳出循环
        for (ExpBookCondition expBookCondition : expBookConditions) {
            if (player.getLevel() < expBookCondition.getNeedLevel()) {
                continue;
            }

            for (Pair<Integer, Integer> idAndExp : expBookCondition.getExpBook()) {
                Integer needExp = idAndExp.getRight();
                if (exp < needExp) {
                    continue;
                }

                int changeNum = (int) (exp / needExp);
                // Reward格式为：0|normalItemId|数量
                resultRewards.add(new Reward(0, idAndExp.getLeft(), changeNum));
                // 扣除兑换的经验值
                exp -= needExp * changeNum;
            }

            break;
        }

        return Pair.of(resultRewards, exp);
    }

    public static HeroUnit[] creatorsToUnits(HeroUnitCreator[] creators) {
        HeroUnit[] units = new HeroUnit[6];

        for (int i = 0; i < units.length && i < creators.length; i++) {
            if (creators[i] != null && creators[i].getCopyAttributes().getHp() > 0) {
                units[i] = creators[i].create();
            }
        }

        return units;
    }

    public static void handleException(Exception e) {
        if (Configuration.runMode.isTest()) {
            CommonLogger.error(e);
        } else {
            throw new RuntimeException(e);
        }
    }

    public static boolean isLessThanTargetVersion(String nowVersion, String targetVersion) {
        String[] nowVersionArr = nowVersion.split("\\.");
        String[] targetVersionArr = targetVersion.split("\\.");

        //如果现在领邮件的时候的版本  比目标版本高 或者等于目标版本  就让领取  大于等于就让  小于不让   如果比目标版本低就不让
        if (nowVersionArr.length >= targetVersionArr.length) {
            for (int i = 0; i < targetVersionArr.length; i++) {
                int nowVersionTemp = Integer.parseInt(nowVersionArr[i]);
                int targetVersionTemp = Integer.parseInt(targetVersionArr[i]);

                if (nowVersionTemp > targetVersionTemp) {
                    return false;
                }
                if (nowVersionTemp < targetVersionTemp) {
                    return true;
                }
            }
            //如果现在的版本比目标版本短  并且到最后
        } else {
            int equalsTimes = 0;
            for (int i = 0; i < nowVersionArr.length; i++) {
                if ((Integer.parseInt(nowVersionArr[i]) < Integer.parseInt(targetVersionArr[i]))) {
                    return true;
                }
                if (Integer.parseInt(nowVersionArr[i]) == Integer.parseInt(targetVersionArr[i])) {
                    equalsTimes += 1;
                }
                if (equalsTimes == nowVersionArr.length && (i == nowVersionArr.length - 1)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 英雄实例ID列表，转换为英雄模板ID列表
     */
    public static List<Integer> heroInstanceIds2TemplateIds(Player player, LineupType lineupType, int[] heroInstanceIds) {
        List<Integer> result = new ArrayList<>();

//        Optional.ofNullable(heroInstanceIds)
//                .ifPresent(instanceIds -> Arrays.stream(instanceIds)
//                        .filter(heroInstanceId -> heroInstanceId != 0)
//                        .forEach(heroInstanceId -> {
//                            Hero hero = player.getBagModel().getHeroById(heroInstanceId);
//                            if (hero != null) {
//                                result.add(hero.getTemplateId());
//                            } else {
//                                if (lineupType == LineupType.Common) {
//                                    HeroUnitCreator heroUnitCreator = player.getTowerModel().getHelpHeroes().get(heroInstanceId);
//                                    if (heroUnitCreator != null) {
//                                        result.add(getTrulyHeroTemplateId(heroUnitCreator.getTemplateId()));
//                                    } else {
//                                        CommonLogger.error(String.format("爬塔阵容有英雄实例无法获得模板ID，英雄实例ID: %s", heroInstanceId));
//                                    }
//                                }
//                            }
//                        })
//                );

        return result;
    }

    public static int getTrulyHeroTemplateId(int templateId) {
        if (HeroService.isHero(templateId)) {
            return templateId;
        }

        MonsterTemplate monsterTemplate = MonsterService.monsterTemplates.get(templateId);
        if (monsterTemplate != null) {
            return monsterTemplate.getBookTemplate().shapId;
        }

        return -1;
    }

    public static BehaviourTreeNode getHeroAINode(HeroUnit heroUnit) {
        int nodeId = 1;
        if (heroUnit.getUnitType() == HeroUnit.UNITY_TYPE_HERO) {
//            nodeId = ((HeroTemplate) ItemService.getItemTemplate(heroUnit.getTid())).skillBehaviourTreeNodeId;
        } else {
//            nodeId = MonsterService.monsterTemplates.get(heroUnit.getTid()).monsterBehaviourTreeNodeId;
        }

        GameLogger.combat(String.format("英雄模型行为树根节点: %s.", nodeId));
        return HeroService.nodes.get(nodeId);
    }

    public static void operateFromAllPlayersByPage(Predicate<PlayerData> predicate, Consumer<Player> operation, String operationName) {
        if (Objects.isNull(operation)) {
            return;
        }

        long startTime = ServerConstants.getCurrentTimeMillis();

        int totalPlayerCount = DbAssistant.getPlayerTotalCount();
        int pageSize = ServerConstants.loadMiniPlayerPageSize;
        int pageCount = totalPlayerCount / pageSize + 1;

        AtomicInteger nowSize = new AtomicInteger(0);
        AtomicBoolean throwException = new AtomicBoolean(false);

        for (int i = 0; i < pageCount; i++) {
            int pageIndex = i;
            ThreadPool.execute(() -> {
                List<PlayerData> list = DbAssistant.getPlayerByPage(pageIndex, pageSize);
                if (CollectionUtil.isNotEmpty(list)) {
                    for (PlayerData playerData : list) {
                        try {
                            if (Objects.nonNull(predicate) && predicate.test(playerData)) {
                                continue;
                            }

                            Player player = new Player(playerData);
                            player.init(true);
                            operation.accept(player);
                            player.saveOnce();
                        } catch (Exception e) {
                            CommonLogger.error(e);
                            throwException.set(true);
                        }
                    }

                    Launcher.launchStartupInfo(String.format("fixed player num: %s", list.size()));
                    nowSize.addAndGet(list.size());
                }
            });
        }

        int printCount = 0;
        while (nowSize.get() < totalPlayerCount) {
            if (throwException.get()) {
                throw new RuntimeException("common util operation error.");
            }

            printCount++;
            if (printCount % 10 == 0) {
                Launcher.launchStartupInfo(String.format("fixed player, total -> (%s), count -> (%s), cost -> (%s)", totalPlayerCount, nowSize.get(), (ServerConstants.getCurrentTimeMillis() - startTime)));
            }

            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                CommonLogger.error(e);
            }
        }

        while (PlayerSaveManager.queueSize() > 0) {
            try {
                Launcher.launchStartupInfo(String.format("%s wait player save 5s.", operationName));
                Thread.sleep(5000);
            } catch (Exception e) {
                CommonLogger.error(e);
            }
        }
    }

    public static List<PbCommons.KeyValueDb> genInteger2KeyValueDb(Map<Integer, Integer> map) {
        PbCommons.KeyValueDb.Builder kv = PbCommons.KeyValueDb.newBuilder();
        List<PbCommons.KeyValueDb> list = new ArrayList<>();
        for (Map.Entry<Integer, Integer> entry : map.entrySet()) {
            kv.setIntKey(entry.getKey());
            kv.setIntValue(entry.getValue());
            list.add(kv.build());
            kv.clear();
        }

        return list;
    }

    public static boolean isValid(int serverId) {
        return serverId > 0;
    }

    public static int getRefreshTimeHour() {
        return ConstantService.getInt(ConstantType.每日统一刷新时间点, 6);
    }

    /**
     * 当前服务器是否为主服务器
     */
    public static boolean isMainWorldServer() {
        return getWorldMasterServerId() == Configuration.serverId;
    }

    public static boolean isMainWorldServer(int serverId){
        return CommonsConfiguration.getWorldMasterServerId() == serverId;
    }

    /**
     * 获得主world节点
     */
    public static int getWorldMasterServerId(){
        return CommonsConfiguration.getWorldMasterServerId();
    }

    /**
     * 获得所有world节点
     */
    public static List<Integer> getAllWorldServerIds(){
        return CommonsConfiguration.getAllWorldServerIds();
    }

    /**
     * hash取一个worldId
     */
    public static int hashWorldServerId(int hash){
        List<Integer> allWorldServerIds = getAllWorldServerIds();
        if(CollectionUtil.isEmpty(allWorldServerIds)){
            return 0;
        }
        return allWorldServerIds.get(hash % allWorldServerIds.size());
    }

    public static BoolValue parseBooleanValue(boolean value){
        return BoolValue.newBuilder().setValue(value).build();
    }

    public static Int32Value parseIntValue(int value){
        return Int32Value.newBuilder().setValue(value).build();
    }


    public static long getNowEpochDay() {
        LocalDateTime now = ServerConstants.getCurrentTimeLocalDateTime();
        long epochDay = now.toLocalDate().toEpochDay();
        int hour = now.getHour();
        return hour >= getRefreshTimeHour() ? epochDay : epochDay - 1;
    }

    /**
     * 根据概率随机
     */
    public static boolean randomProbability(int probability){
        int i1 = MathUtil.randomInt(100);
        return i1 < probability;
    }


    /**
     * 打印当前主线程堆栈信息
     */
    public static void printMailThreadStackTrace(){
        printThreadStackTrace(Launcher.getMainThread());
    }

    /**
     * 打印线程堆栈信息
     */
    public static void printThreadStackTrace(Thread thread){
        StackTraceElement[] stackTraceElements = thread.getStackTrace();
        for (StackTraceElement element : stackTraceElements) {
            GameLogger.error(element.toString());
        }
    }

    private static final String java_file_suffix = ".java";
    private static final String class_file_suffix = ".class";
    /**
     * 打印路径下所有java文件
     */
    public static Map<String, String> getJavaFiles(String directoryPath) {
        return getFiles(directoryPath, java_file_suffix);
    }

    public static Map<String, String> getClassFiles(String directoryPath) {
        return getFiles(directoryPath, class_file_suffix);
    }

    public static Map<String, String> getFiles(String directoryPath, String file_suffix) {
        Map<String, String> javaFilePaths = new HashMap<>();
        File directory = new File(directoryPath);

        // 检查目录是否存在
        if (directory.exists() && directory.isDirectory()) {
            // 获取目录下所有文件和子目录
            File[] files = directory.listFiles();
            if (files != null) {
                // 遍历所有文件和子目录
                for (File file : files) {
                    if (file.isFile() && file.getName().endsWith(file_suffix)) {
                        // 如果是Java文件，将路径添加到列表中
                        javaFilePaths.put(file.getName().replace(file_suffix, ""), file.getAbsolutePath());
                    } else if (file.isDirectory()) {
                        // 如果是目录，递归获取该目录下的Java文件路径
                        javaFilePaths.putAll(getFiles(file.getAbsolutePath(), file_suffix));
                    }
                }
            }
        } else {
            System.err.println("目录不存在或不是一个有效的目录路径。");
        }
        return javaFilePaths;
    }

    /**
     * 根据间距计算矩阵坐标点
     * @param p1 右上点
     * @param p2 坐下点
     * @param distance 间距
     * @return 坐标点列表
     */
    public static List<Triple<Float, Float, Float>> calPointInMatrix(float[] p1, float[] p2, float distance){
        List<Triple<Float, Float, Float>> allPoints = new ArrayList<>();
        float initX = p1[0];
        float curX = initX;
        float curY = p1[1];//y轴是高度，暂时不处理
        float curZ = p1[2];
        do{
            if(p2[0] < curX){
                //下移
                curX = initX;
                curZ -= distance;
            }else{
                //按行记录
                //记录点位
                allPoints.add(Triple.of(curX, curY, curZ));
                curX += distance;
            }
        }while (curZ >= p2[2]);

        return allPoints;
    }

    /**
     * 根据某点和半径，随机获得目标点（随机弧度）
     * @param curPoint 当前坐标
     * @param radius 半径
     * @return
     */
    public static Pair<Double, Double> calTargetPoint(Pair<Double, Double> curPoint, int radius){
        //随机弧度值
        Random r = new Random();
        double radianNum = r.nextDouble() * 2 * Math.PI;
        //计算新坐标点
        return calTargetPoint(curPoint, radianNum, radius);
    }

    /**
     * 根据某个点计算某个弧度下固定距离的点
     * @param curPoint 当前点
     * @param radianNum 弧度
     * @param radius 距离
     * @return 下个点
     */
    public static Pair<Double, Double> calTargetPoint(Pair<Double, Double> curPoint, double radianNum, double radius){
        //当前坐标
        double x = curPoint.getKey();
        double y = curPoint.getValue();
        return Pair.of(x + radius * Math.cos(radianNum), y + radius * Math.sin(radianNum));
    }

    /**
     * 圆内随机坐标点
     * @param curPoint 原点
     * @param maxRadius 最大半径 （1-maxRadius）
     * @return
     */
    public static Pair<Double, Double> randomCalPoint(Pair<Double, Double> curPoint, int maxRadius){
        Random r = new Random();
        int radius = r.nextInt(maxRadius) + 1;
        return calTargetPoint(curPoint, radius);
    }

    /**
     * 计算两点距离
     * @param curPoint 当前位置
     * @param targetPoint 目标位置
     * @return
     */
    public static double calDistance(Pair<Double, Double> curPoint, Pair<Double, Double> targetPoint) {
        double deltaX = curPoint.getKey() - targetPoint.getKey();
        double deltaY = curPoint.getValue() - targetPoint.getValue();
        return Math.sqrt(deltaX * deltaX + deltaY * deltaY);
    }

    /**
     * 根据起点和终点计算速度后下一点
     * @param curPoint 当前点
     * @param targetPoint 目标点
     * @param speed 速度
     * @return 下一点
     */
    public static Pair<Double, Double> calNextPoint(Pair<Double, Double> curPoint, Pair<Double, Double> targetPoint, double speed){
        //计算弧度
        double radianNum = Math.atan2(targetPoint.getValue() - curPoint.getValue(), targetPoint.getKey() - curPoint.getKey());
        return calTargetPoint(curPoint, radianNum, speed);
    }

    /**
     * 获取指定位数二进制1的数量（从左到右）
     * @param num 指定数
     * @param binaryCount 位数
     * @return
     */
    public static int getBinaryOneCount(int num, int binaryCount){
        int result = 0;
        for (int count = binaryCount; count > 0; count--) {
            result += ((num >>> count & 1) > 0 ? 1 : 0);
        }
        return result;
    }

    /**
     * 判断指定位数二进制是否全1（从左到右）
     * @param num 数
     * @param binaryCount 指定位数
     * @return
     */
    public static boolean firstBinaryIsTrue(int num, int binaryCount){
        int compareNum = 0;
        for (int count = binaryCount; count > 0; count--) {
            compareNum |= (1 << count);
        }
        return (num & compareNum) > 0;
    }

    static Random random = new Random();
    public static int random(int minNum, int maxNum) {
        return random.nextInt(maxNum - minNum + 1) + minNum;
    }

    public static ScenePosition3D genPosition(String point){
        ScenePosition3D position3D = new ScenePosition3D();
        float[] ints = splitToFloatArray(point, ",");
        if(ints.length == 2){
            position3D.setX(ints[0]);
            position3D.setZ(ints[1]);
        }else{
            position3D.setX(ints[0]);
            position3D.setY(ints[1]);
            position3D.setZ(ints[2]);
        }
        return position3D;
    }

    public static float[] splitToFloatArray(String str, String separator) {
        String[] strArray = StringUtils.split(str, separator);
        float[] resultArray = new float[strArray.length];
        for (int i = 0; i < resultArray.length; i++) {
            String l = strArray[i];
            resultArray[i] = Float.parseFloat(l);
        }
        return resultArray;
    }


    private static ThreadLocal<Long> startTimeNano;
    private static ThreadLocal<Long> startTimeMs;

    /**
     * 统计两次调用之间的纳秒差
     * 线程隔离
     */
    public static void costTimeNano(String name){
        if(startTimeNano != null && startTimeNano.get()!= null){
            SystemLogger.info("Time cost : "  + name + " : " + (System.nanoTime() - startTimeNano.get()) + " ns");
        }else{
            startTimeNano = new ThreadLocal<>();
        }
        startTimeNano.set(System.nanoTime());
    }

    /**
     * 统计两次调用之间的纳秒差
     * 线程隔离
     */
    public static void costTimeMs(String name){
        if(startTimeMs != null && startTimeMs.get()!= null){
            SystemLogger.info("Time cost : " + name + " : " + (System.currentTimeMillis() - startTimeMs.get()) + " ms");
        }else{
            startTimeMs = new ThreadLocal<>();
        }
        startTimeMs.set(System.currentTimeMillis());
    }

    public static String getUTC() {
        TimeZone timeZone = TimeZone.getDefault();
        String result = "UTC";
        if (timeZone.getRawOffset() > 0) {
            result += "+";
        }
        return result + timeZone.getRawOffset() / 3600000;
    }

    public static boolean isNewDay(long lastTime){
        LocalDateTime now = ServerConstants.getCurrentTimeLocalDateTime();
        LocalDateTime refreshTime = now.withHour(CommonUtils.getRefreshTimeHour()).withMinute(0).withSecond(0).withNano(0);
        return lastTime < DateTimeUtil.toMillis(refreshTime) && now.isAfter(refreshTime);
    }

    public static boolean isNewWeek(long lastTime){
        LocalDateTime now = ServerConstants.getCurrentTimeLocalDateTime();

        int nowDayOfWeek = now.getDayOfWeek().getValue();
        //本周刷新时间点
        LocalDateTime thisWeekMonday = now.minusDays(nowDayOfWeek - 1).
                withHour(CommonUtils.getRefreshTimeHour()).withMinute(0).withSecond(0).withNano(0);

        return lastTime < DateTimeUtil.toMillis(thisWeekMonday) && now.isAfter(thisWeekMonday);
    }

    public static boolean isNewMonth(long lastTime){
        LocalDateTime now = ServerConstants.getCurrentTimeLocalDateTime();
        LocalDateTime refreshTime = now.withDayOfMonth(1).withHour(CommonUtils.getRefreshTimeHour()).withMinute(0).withSecond(0).withNano(0);
        return lastTime < DateTimeUtil.toMillis(refreshTime) && now.isAfter(refreshTime);
    }

    public static long getBetweenWeek(long time){
        return ChronoUnit.WEEKS.between(DateTimeUtil.toLocalDateTime(time), ServerConstants.getCurrentTimeLocalDateTime());
    }

}

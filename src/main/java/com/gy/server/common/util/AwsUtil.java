package com.gy.server.common.util;

import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.zip.DataFormatException;

import com.gy.server.core.Configuration;
import com.gy.server.core.cos.COSManager;
import com.gy.server.game.log.GameLogger;
import com.gy.server.utils.compressor.CompressorUtil;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.model.*;

/**
 * cos辅助类
 * <AUTHOR> - [Created on 2023-07-19 20:07]
 */
public class AwsUtil {

    static COSClient cosClient;
    static AwsConfig config;

    public static void init(){
        config = new AwsConfig();
        config.init();
        //构建客户端

        COSManager.init(config.accessKey, config.secretKey, config.clientRegion, config.bucketName);
        cosClient = COSManager.getInstance().getCosClient();
    }

    public static byte[] getBytes(String fileName, boolean isCompress){
        COSObject object = cosClient.getObject(config.bucketName, fileName);
        if(Objects.nonNull(object)){
            COSObjectInputStream objectContent = object.getObjectContent();
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            try (BufferedInputStream bis = new BufferedInputStream(objectContent)) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = bis.read(buffer)) != -1) {
                    byteArrayOutputStream.write(buffer, 0, bytesRead);
                }
                byte[] recordBytes = byteArrayOutputStream.toByteArray();
                if(isCompress){
                    try {
                        recordBytes = CompressorUtil.deflate().decompress(recordBytes);
                    } catch (DataFormatException e) {
                        e.printStackTrace();
                    }
                }
                return recordBytes;
            } catch (IOException e) {
                e.printStackTrace();
            }finally {
                try {
                    byteArrayOutputStream.close();
                    objectContent.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

    public static void del(String fileName){
        try{
            cosClient.deleteObject(config.bucketName, fileName);
        }catch (Exception e){
            GameLogger.error(e.toString());
        }
    }

    /**
     * 增加文件字符流，fileName存在会覆盖内容
     */
    public static void addBytes(String fileName, byte[] bytes, boolean isCompress){
        if(isCompress){
            try {
                bytes = CompressorUtil.deflate().compress(bytes);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        PutObjectRequest request = new PutObjectRequest(
                config.bucketName,
                fileName,
                new ByteArrayInputStream(bytes),
                buildObjectMetadata(bytes.length));
        cosClient.putObject(request);
    }

    private static ObjectMetadata buildObjectMetadata(long contentLength){
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentType("plain/text");
        metadata.addUserMetadata("x-amz-meta-title", "someTitle");
        metadata.setContentLength(contentLength);
        return metadata;
    }

    public static List<String> getAllKeyNames(){
        ObjectListing objectListing = cosClient.listObjects(config.bucketName);
        List<String> result = new ArrayList<>();
        if(Objects.nonNull(objectListing)){
            for (COSObjectSummary objectSummary : objectListing.getObjectSummaries()) {
                result.add(objectSummary.getKey());
            }
        }
        return result;
    }

    /**
     * 增加字符内容，keyName存在会覆盖内容
     */
    public static void addContent(String keyName, String content){
        cosClient.putObject( config.bucketName, keyName, content);
    }

    public static String getContent(String keyName){
        try {
            COSObject object = cosClient.getObject(config.bucketName, keyName);
            return object.toString();
        }catch (Exception e) {
            return null;
        }
    }

    /**
     * aws配置，暂时根据环境配置在类里
     */
//    static String[] defaultAwsConfig = {"ap-shanghai.myqcloud"
//            , "https://cos.ap-shanghai.myqcloud.com"
//            , "tl-1251093490"
//            , "AKIDBIVaVYRIfBnEUazFx1TFKH8shbvJmdPF"
//            , "41rWHCwpLh7eSHKKBtWzXxU9Fam2Nchc"};
    static String[] defaultAwsConfig = {"ap-shanghai"
            , "https://cos.ap-shanghai.myqcloud.com"
            , "tl-1251093490"
            , "AKID50pX2OtCHDubOIBuPgniPxVr5EEWt7Ei"
            , "jdNtcH0a4PXSDqLcL1XxHVXMArMvoW7c"};
    static String[] checkAwsConfig = defaultAwsConfig;
    static String[] liveAwsConfig = defaultAwsConfig;
    /**
     * aws常量配置
     * 根据运行环境配置aws参数。
     */
    public static class AwsConfig{
        public String clientRegion;
        public String endpoint;
        public String bucketName;
        public String accessKey;
        public String secretKey;

        public void init(){
            String[] curConfig;
            if(Objects.nonNull(Configuration.runMode) && Configuration.runMode.isLive()){
                curConfig = liveAwsConfig;
            }else if(Objects.nonNull(Configuration.runMode) && Configuration.runMode.isCheck()){
                curConfig = checkAwsConfig;
            }else{
                curConfig = defaultAwsConfig;
            }
            this.clientRegion = curConfig[0];
            this.endpoint = curConfig[1];
            this.bucketName = curConfig[2];
            this.accessKey = curConfig[3];
            this.secretKey = curConfig[4];
        }
    }



}

package com.gy.server.common.util;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.github.javaparser.JavaParser;
import com.github.javaparser.ast.CompilationUnit;
import com.github.javaparser.ast.body.EnumDeclaration;
import com.github.javaparser.ast.expr.NameExpr;
import com.github.javaparser.ast.expr.SimpleName;
import com.github.javaparser.ast.visitor.VoidVisitorAdapter;


/**
 * 查询某个枚举调用类
 * <AUTHOR> - [Created on 2024-03-15 15:42]
 */
public class FindUsagesUtil {


    static Map<Path, CompilationUnit> compilationUnitList = new HashMap<>();

    public static List<String> findUsages(String enumsName){
        return findEnumUsages(enumsName);
    }

    public static void init(){
        init(System.getProperty("user.dir") + "/src/main/java/com/gy/server/game/");
    }

    public static void init(String projectPath){
        try {
            Files.walk(new File(projectPath).toPath())
                .filter(Files::isRegularFile)
                .filter(path -> path.toString().endsWith("Service.java"))
                .forEach(path -> {
                    try {
                        compilationUnitList.put(path, JavaParser.parse(path));
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                });
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static List<String> findEnumUsages(String enumConstant) {
        List<String> usages = new ArrayList<>();
        compilationUnitList.forEach((path, cu) -> {
            cu.accept(new FindEnumVoidVoidVisitorAdapter(enumConstant, path, usages), null);
        });

        return usages;
    }

    private static class FindEnumVoidVoidVisitorAdapter extends VoidVisitorAdapter<Void> {
        private final String enumConstant;
        private final Path path;
        private final List<String> usages;

        public FindEnumVoidVoidVisitorAdapter(String enumConstant, Path path, List<String> usages) {
            this.enumConstant = enumConstant;
            this.path = path;
            this.usages = usages;
        }

        @Override
        public void visit(EnumDeclaration enumDeclaration, Void arg) {
            super.visit(enumDeclaration, arg);
            enumDeclaration.getEntries().forEach(entry -> {
                if (entry.getNameAsString().equals(enumConstant)) {
                    String serviceName = path.getFileName().toString().replace(".java", "");
                    usages.add(serviceName);
                }
            });
        }

        @Override
        public void visit(NameExpr nameExpr, Void arg) {
            if (nameExpr.getNameAsString().equals(enumConstant)) {
                String serviceName = path.getFileName().toString().replace(".java", "");
                usages.add(serviceName);
            }
            super.visit(nameExpr, arg);
        }

        @Override
        public void visit(SimpleName simpleName, Void arg) {
            if (simpleName.asString().equals(enumConstant)) {
                String serviceName = path.getFileName().toString().replace(".java", "");
                usages.add(serviceName);
            }
            super.visit(simpleName, arg);
        }
    }
}

package com.gy.server.common.util;

import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;

import com.gy.server.core.thread.AbstractRunner;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

/**
 * <AUTHOR> - [Created on 2023-07-29 11:15]
 */
public class ServerWaitHelper extends AbstractRunner {

    /**
     * 异步队列
     */
    private static Queue<Runnable> tasks = new ConcurrentLinkedQueue<>();
    private static AtomicInteger doingTaskCount = new AtomicInteger(0);

    /**
     * 注册异步任务
     */
    public static void registerTask(Runnable r){
        tasks.offer(r);
        doingTaskCount.incrementAndGet();
    }

    public static void startWait(){
        for(;;){
            if(tasks.size() == 0
                && doingTaskCount.get() == 0){
                return;
            }
        }
    }


    @Override
    public String getRunnerName() {
        return "ServerWaitHelper";
    }

    @Override
    protected void subRunnerExecute() throws Exception {
        Runnable r;
        while((r = tasks.poll()) != null){
            ThreadPool.execute(new WaitTask(r, ()->doingTaskCount.decrementAndGet()));
        }
    }

    @Override
    public long getRunnerInterval() {
        return 10L;
    }

    private static class WaitTask implements Runnable {
        Runnable task;
        Runnable after;
        WaitTask(Runnable task, Runnable after){
            this.task = task;
            this.after = after;
        }

        @Override
        public void run() {
            task.run();
            after.run();
        }
    }

    private static final ServerWaitHelper instance = new ServerWaitHelper();
    ServerWaitHelper(){}
    public static ServerWaitHelper getInstance(){
        return instance;
    }

}

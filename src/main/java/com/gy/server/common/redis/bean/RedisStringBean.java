package com.gy.server.common.redis.bean;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;

import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

/**
 * <AUTHOR> - [Create on 2021/01/26 15:50]
 */
@ProtobufClass
public class RedisStringBean {

    @Protobuf(order = 1)
    private String param;

    public RedisStringBean() {}

    public RedisStringBean(String param) {
        this.param = param;
    }

    public RedisStringBean(Number param) {
        this(String.valueOf(param));
    }

    public String getParam() {
        return param;
    }

    public RedisStringBean setParam(String param) {
        this.param = param;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        RedisStringBean that = (RedisStringBean) o;

        return new EqualsBuilder()
                .append(param, that.param)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(param)
                .toHashCode();
    }

    @Override
    public String toString() {
        return "RedisStringBean{" +
                "param='" + param + '\'' +
                '}';
    }

    public static RedisStringBean newString(String str){
        return new RedisStringBean(str);
    }
    public static RedisStringBean newString(int str){
        return new RedisStringBean(str);
    }

}

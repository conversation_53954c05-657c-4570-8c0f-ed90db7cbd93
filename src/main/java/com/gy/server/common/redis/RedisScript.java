package com.gy.server.common.redis;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import com.ttlike.server.tl.baselib.TLBase;

import org.redisson.api.RScript;

/**
 * Redis lua脚本
 * <p>
 * 注意1：集群模式下，必须确保脚本内所有的key落在同一个slot下，否则将报错。
 * 注意2：编码无法混编，即：使用两种不同的编码方式。如果想混编，必须使用script方式而不是hash码方式调用。
 *
 * <AUTHOR> - [Create on 2021/03/11 13:48]
 */
@SuppressWarnings("unused")
public enum RedisScript {

    None("return 'HelloWorld'; ", true),

    /**
     * 竞技场匹配
     * 当玩家不在榜单上时，返回null
     * 返回 {@link RScript.ReturnType#MULTI}类型
     */
    ArenaToMatch(
            "local curScore = redis.call('ZSCORE', KEYS[1], ARGV[11]); "
                    + "if not curScore then "
                    + "return nil; "
                    + "end; "

                    // 第一档位的匹配人数、分差上限、分差下限
                    + "local firstNum = tonumber(ARGV[1]); "
                    + "local firstHigherScoreLimit = tonumber(ARGV[2]); "
                    + "local firstLowerScoreLimit = tonumber(ARGV[3]); "

                    // 第二档位的匹配人数、分差上限、分差下限
                    + "local secondNum = tonumber(ARGV[4]); "
                    + "local secondHigherScoreLimit = tonumber(ARGV[5]); "
                    + "local secondLowerScoreLimit = tonumber(ARGV[6]); "

                    // 第三档位的匹配人数、分差上限、分差下限
                    + "local thirdNum = tonumber(ARGV[7]); "
                    + "local thirdHigherScoreLimit = tonumber(ARGV[8]); "
                    + "local thirdLowerScoreLimit = tonumber(ARGV[9]); "

                    + "local randomSeed = tonumber(ARGV[10]); "

                    // 必须设置随机种子，否则math.random()将会在多次调用中产生同样的序列
                    + "math.randomseed(randomSeed); "

                    + "local totalNum = firstNum + secondNum + thirdNum; "
                    + "local curPlayer = ARGV[11]; "
                    + "curScore = tonumber(curScore); "
                    + "local highScore, lowScore;"
                    + "local result = {}; "
                    + "local members = {}; "
                    + "local scores = {}; "

                    // 第一档搜索
                    + "highScore = curScore + firstHigherScoreLimit; "
                    + "lowScore = curScore + firstLowerScoreLimit; "
                    + "local range = redis.call('ZREVRANGEBYSCORE', KEYS[1], highScore, lowScore, 'WITHSCORES'); "

                    + "for i,v in ipairs(range) do "
                    + "local curV = tonumber(v); "
                    + "if curV then "
                    + "scores[#scores + 1] = curV; "
                    + "else "
                    + "members[#members + 1] = v; "
                    + "end; "
                    + "end; "

                    // 剔除自己
                    + "if #members > 0 and lowScore <= curScore and curScore <= highScore then "
                    + "for i,v in ipairs(members) do "
                    + "if v == curPlayer then "
                    + "table.remove(members, i); "
                    + "table.remove(scores, i); "
                    + "break; "
                    + "end; "
                    + "end; "
                    + "end; "

                    + "if #members > 0 then "
                    + "for var = 1, firstNum do "
                    + "local randomIndex = math.random(1, #members); "
                    + "result[#result + 1] = members[randomIndex]; "
                    + "result[#result + 1] = scores[randomIndex]; "

                    + "table.remove(members, randomIndex); "
                    + "table.remove(scores, randomIndex); "
                    + "if #members <= 0 then "
                    + "break; "
                    + "end; "
                    + "end; "
                    + "end; "

                    // 第二档搜索
                    + "local firstFetchNum = #result / 2; "
                    + "secondNum = secondNum + (firstNum - firstFetchNum); "
                    + "highScore = curScore + secondHigherScoreLimit; "
                    + "lowScore = curScore + secondLowerScoreLimit; "
                    + "members = {}; "
                    + "scores = {}; "
                    + "range = redis.call('ZREVRANGEBYSCORE', KEYS[1], highScore, lowScore, 'WITHSCORES'); "

                    + "for i,v in ipairs(range) do "
                    + "local curV = tonumber(v); "
                    + "if curV then "
                    + "scores[#scores + 1] = curV; "
                    + "else "
                    + "members[#members + 1] = v; "
                    + "end; "
                    + "end; "

                    // 剔除自己
                    + "if #members > 0 and lowScore <= curScore and curScore <= highScore then "
                    + "for i,v in ipairs(members) do "
                    + "if v == curPlayer then "
                    + "table.remove(members, i); "
                    + "table.remove(scores, i); "
                    + "break; "
                    + "end; "
                    + "end; "
                    + "end; "

                    + "if #members > 0 then "
                    + "for var = 1, secondNum do "
                    + "local randomIndex = math.random(1, #members); "
                    + "result[#result + 1] = members[randomIndex]; "
                    + "result[#result + 1] = scores[randomIndex]; "

                    + "table.remove(members, randomIndex); "
                    + "table.remove(scores, randomIndex); "
                    + "if #members <= 0 then "
                    + "break; "
                    + "end; "
                    + "end; "
                    + "end; "

                    // 第三档搜索
                    + "local high, low, mutiple;"
                    + "repeat "
                    + "if not high then "
                    + "high = curScore + thirdHigherScoreLimit; "
                    + "low = curScore + thirdLowerScoreLimit; "
                    + "mutiple = 2; "
                    + "else "
                    + "high = low - 1; "
                    + "low = curScore + thirdLowerScoreLimit * mutiple; "
                    // 每次总以上一次2倍的速度扩大搜索范围
                    + "mutiple = mutiple * 2; "
                    + "end; "

                    + "members = {}; "
                    + "scores = {}; "
                    + "range = redis.call('ZREVRANGEBYSCORE', KEYS[1], high, low, 'WITHSCORES'); "
                    + "for i,v in ipairs(range) do "
                    + "local curV = tonumber(v); "
                    + "if curV then "
                    + "scores[#scores + 1] = curV; "
                    + "else "
                    + "members[#members + 1] = v; "
                    + "end; "
                    + "end; "

                    // 剔除自己
                    + "if #members > 0 and lowScore <= curScore and curScore <= highScore then "
                    + "for i,v in ipairs(members) do "
                    + "if v == curPlayer then "
                    + "table.remove(members, i); "
                    + "table.remove(scores, i); "
                    + "break; "
                    + "end; "
                    + "end; "
                    + "end; "

                    + "if #members > 0 then "
                    + "local nowFetchNum = #result / 2; "
                    + "local surplusNum = totalNum - nowFetchNum; "

                    + "for var = 1, surplusNum do "
                    + "local randomIndex = math.random(1, #members); "
                    + "result[#result + 1] = members[randomIndex]; "
                    + "result[#result + 1] = scores[randomIndex]; "

                    + "table.remove(members, randomIndex); "
                    + "table.remove(scores, randomIndex); "
                    + "if #members <= 0 then "
                    + "break; "
                    + "end; "
                    + "end; "
                    + "end; "
                    + "until high <= 0 or low <= 0 or #result / 2 >= totalNum; "

                    + "return result; "
            ,
            true
    ),

    /**
     * 竞技场填充战斗记录
     * 主功能，往list数据结构填充；
     * 副功能：限长；
     */
    ArenaPushRecord(
            "redis.call('LPUSH', KEYS[1], ARGV[1]); "

                    // 限制最大长度
                    + "local countLimit = 20; "
                    + "local count = redis.call('LLEN', KEYS[1]); "
                    + "if count > countLimit then "
                    // 当超过长度时，只保留最新的20条
                    + "redis.call('LTRIM', KEYS[1], 0, 19); "
                    + "end; "
            ,
            true
    ),

    /**
     * 获取一个zset结构中指定一个元素的排行和分数
     * 当玩家不在榜上时，返回null
     */
    ZsetGetRankAndScore(
            "local curRank = redis.call('ZREVRANK', KEYS[1], ARGV[1]); "
                    + "if not curRank then "
                    + "return nil;"
                    + "end; "

                    + "local curScore = redis.call('ZSCORE', KEYS[1], ARGV[1]); "
                    + "local array = {}; "
                    + "array[1] = curRank; "
                    + "array[2] = curScore; "
                    + "return array; ",
            true
    ),

    /**
     * {@link RedisScript#ZsetGetRankAndScore}的批量版本
     */
    MultiZsetGetRankAndScore(
            "local array = {}; "
                    + "local rank; "
                    + "local score; "
                    + "local member;"

                    + "for i = 1, #ARGV, 1 do "
                    + "rank = redis.call('ZREVRANK', KEYS[1], ARGV[i]); "
                    + "if rank then "
                    + "score = redis.call('ZSCORE', KEYS[1], ARGV[i]); "
                    + "member = ARGV[i]; "
                    + "array[#array + 1] = rank; "
                    + "array[#array + 1] = score; "
                    + "array[#array + 1] = member; "
                    + "end; "
                    + "end; "

                    + "return array; "
            ,
            true
    ),

    /**
     * 修改排行榜分数
     * return true执行成功
     */
    UpdateRankScore(
            "local rankKey = KEYS[1]; " +
            "local member = ARGV[1]; " +
//            "--增加的值" +
            "local addValue = tonumber(ARGV[2]); " +
//            "--毫秒时间戳" +
            "local mill = tonumber(ARGV[3]); " +
//            "--是否允许目标成员是空" +
            "local isAllowNil = ARGV[4]; " +
//            "--是否通关" +
            "local isPass = ARGV[5]; " +
//            "--排行榜中的分数" +
            "local curScore = redis.call('ZSCORE', rankKey, member); " +
            "if not curScore then " +
            "    if isAllowNil then " +
            "        curScore = 0; " +
            "    else " +
            "        return nil; " +
            "    end " +
            "end " +
//            "--去掉小数位为当前分数" +
            "curScore = math.floor(tonumber(curScore)); " +
            "local baseScore = curScore + addValue; " +
            "local timePart = 1e10 / mill; " +//0.005719801994242908
//            "--转换为带时间戳的小数" +
            "local newScore; " +
            "if isPass == '1' then " +
            "        newScore = baseScore + isPass * 0.1 + timePart; " + //0.105719801994242908
            "    else " +
            "        newScore = baseScore + timePart; " + //0.005719801994242908
            "    end " +
            "redis.call('ZADD', rankKey, newScore, member); " +
            "return 1; ", true),


    ;

    private final String script;
    private String shaDigest;

    /**
     * 是否需要缓存下hash值
     * <p>
     * 如果需要，则该脚本在使用时，一般以hash调用方式，加快通信效率；
     * 如果不需要，则该脚本只能使用原生的script进行调用；
     */
    private final boolean needShaDigest;

    RedisScript(String script, boolean needShaDigest) {
        this.script = script;
        this.needShaDigest = needShaDigest;
    }

    public String getScript() {
        return script;
    }

    public static void init() {
        // 加脚本缓存到redis，并记录下这个脚本的hash值
        Arrays.stream(values())
                .filter(redisScript -> redisScript.needShaDigest)
                .forEach(redisScript -> redisScript.shaDigest = TLBase.getInstance().getRedisAssistant().scriptLoad(redisScript.name(), redisScript.script));
    }

    public final Object evalSha(RScript.ReturnType returnType, List<Object> keys, Object... values) {
        List<Object> objects = new ArrayList<>();
        Collections.addAll(objects, values);
        return TLBase.getInstance().getRedisAssistant().evalSha(shaDigest, keys, objects);
    }

    /**
     * value只能为string的方法
     */
    public final Object evalSha(RScript.ReturnType returnType, List<Object> keys, String... values) {
        List<Object> objects = new ArrayList<>();
        Collections.addAll(objects, values);
        return TLBase.getInstance().getRedisAssistant().evalSha(shaDigest, keys, objects);
    }

    public final Object evalSha(String key, RScript.ReturnType returnType, List<Object> keys, Object... values) {
        List<Object> objects = new ArrayList<>();
        Collections.addAll(objects, values);
        return TLBase.getInstance().getRedisAssistant().evalSha(key, keys, objects);
    }


//    /**
//     * @param stringValues 适用于{@link SafeEncoder#encode(String)}进行编码的参数
//     * @param values 适用于{@link PbUtilCompress#encode(Object)}进行编码的参数
//     */
//    public final <R> R evalSha(RScript.ReturnType returnType, List<Object> stringValues, List<Object> keys, Object... values) {
//        RedissonClient client = RedissonClientManager.getCommonClient();
//        RScript rScript = client.getScript(ByteArrayCodec.INSTANCE);
//
//        byte[][] bytes = new byte[values.length + (CollectionUtil.isEmpty(stringValues) ? 0 : stringValues.size())][];
//        int index = 0;
//
//        if (CollectionUtil.isNotEmpty(stringValues)) {
//            for (; index < stringValues.size(); index++) {
//                bytes[index] = SafeEncoder.encode(String.valueOf(stringValues.get(index)));
//            }
//        }
//
//        for (int i = 0; i < values.length; i++, index++) {
//            bytes[index] = PbUtilCompress.encode(values[i]);
//        }
//
//        return rScript.evalSha(RScript.Mode.READ_WRITE, shaDigest, returnType, keys, (Object[]) bytes);
//    }

}

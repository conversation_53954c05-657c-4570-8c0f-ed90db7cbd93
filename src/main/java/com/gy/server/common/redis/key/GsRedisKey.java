package com.gy.server.common.redis.key;

import com.ttlike.server.tl.baselib.redis.key.KeyType;
import com.ttlike.server.tl.baselib.redis.key.RedisKey;

/**
 * <AUTHOR> - [Created on 2022-03-07 13:58]
 */
public class GsRedisKey {

    public enum DistributedLock {
        FRIEND(new RedisKey(-1, KeyType.string, true, "distributed_lock_friend_%s", "跨服好友模块锁，一个玩家一个锁")),
        FRIEND_GROUP_LIST(new RedisKey(-1, KeyType.string, true, "distributed_lock_friend_group_%s", "好友群聊模块锁，一个玩家一个锁")),

        ;
        private RedisKey redisKey;

        DistributedLock(RedisKey redisKey) {
            this.redisKey = redisKey;
        }

        public String getRedisKey(Object... param) {
            return redisKey.getRedisKey(param);
        }

    }

    public enum Friend {
        FRIEND_INFO(new RedisKey(-1, KeyType.map, false, "FRIEND_INFO_%s", "好友信息")),
        FRIEND_APPLY_INFO(new RedisKey(-1, KeyType.map, false, "FRIEND_APPLY_INFO_%s", "好友邀请信息")),
        FRIEND_BLACK_INFO(new RedisKey(-1, KeyType.map, false, "FRIEND_BLACK_INFO_%s", "好友黑名单信息")),
        FRIEND_GROUP_INFO(new RedisKey(-1, KeyType.string, false, "FRIEND_GROUP_INFO_%s", "好友群聊信息")),
        PLAYER_FRIEND_GROUP_BAND(new RedisKey(-1, KeyType.set, false, "PLAYER_FRIEND_GROUP_BAND_%s", "好友群聊绑定")),
        ID(new RedisKey(-1, KeyType.string, false, "friend_group_id", "好友群组id")),
        FRIEND_SEND_ID(new RedisKey(-1, KeyType.set, false, "FRIEND_SEND_ID_%s", "友情币送出玩家id")),
        FRIEND_HAD_RECEIVE_ID(new RedisKey(-1, KeyType.set, false, "FRIEND_HAD_RECEIVE_ID_%s", "友情币已经接收玩家id")),
        FRIEND_CAN_RECEIVE_ID(new RedisKey(-1, KeyType.set, false, "FRIEND_CAN_RECEIVE_ID_%s", "能接收友情币玩家id")),
        SECRET_CHAT(new RedisKey(-1, KeyType.set, false, "SECRET_CHAT_%s", "密聊")),

        STRANGER_INFO(new RedisKey(-1, KeyType.map, false, "STRANGER_INFO_%s", "陌生人信息")),


        FRIEND_RECOMMEND_FIGHTPOWER(new RedisKey(-1, KeyType.sortSet, false, "FRIEND_RECOMMEND_FIGHTPOWER", "推荐好友-战力")),
        FRIEND_RECOMMEND_LEVEL(new RedisKey(-1, KeyType.set, false, "FRIEND_RECOMMEND_LEVEL", "推荐好友-等级")),
        FRIEND_RECOMMEND_GENDER(new RedisKey(-1, KeyType.set, false, "FRIEND_RECOMMEND_GENDER_%s", "推荐好友-性别")),
        FRIEND_RECOMMEND_CITY(new RedisKey(-1, KeyType.map, false, "FRIEND_RECOMMEND_CITY_%s", "推荐好友-城市")),
        ;
        private RedisKey redisKey;

        Friend(RedisKey redisKey) {
            this.redisKey = redisKey;
        }

        public String getRedisKey(Object... param) {
            return redisKey.getRedisKey(param);
        }

    }

    /**
     * 助战
     */
    public enum AssistFight {
        HERO_ID(new RedisKey(-1, KeyType.map, false, "ASSIST_FIGHT_HERO_ID_%s", "助战英雄id列表")),
        HERO_INFO(new RedisKey(-1, KeyType.map, false, "ASSIST_FIGHT_HERO_INFO_%s", "助战英雄详细信息")),

        ;
        private RedisKey redisKey;

        AssistFight(RedisKey redisKey) {
            this.redisKey = redisKey;
        }

        public String getRedisKey(Object... param) {
            return redisKey.getRedisKey(param);
        }
    }

    public enum MonthTower {
        REFRESH(new RedisKey(1, KeyType.string, false, "MonthTower_refresh", "爬塔刷新")),

        ;
        private RedisKey redisKey;

        MonthTower(RedisKey redisKey) {
            this.redisKey = redisKey;
        }

        public String getRedisKey(Object... param) {
            return redisKey.getRedisKey(param);
        }
    }

    public enum Room {
        room_info(new RedisKey(1, KeyType.map, false, "room_info_%s_%s", "房间信息")), //%s 战区id,0全服，-x本服(x为本服服务器id)  房间类型
        room_member_info(new RedisKey(1, KeyType.map, false, "room_member_info_%s", "房间成员信息信息")),   //%s 房间id
        ;

        private RedisKey redisKey;

        Room(RedisKey redisKey) {
            this.redisKey = redisKey;
        }

        public String getRedisKey(Object... param) {
            return redisKey.getRedisKey(param);
        }
    }

    public enum WorldBoss {
        room_rank(new RedisKey(1, KeyType.map, false, "WB_room_rank_%s", "武林悬赏组队排行数据")),   //%s 房间id
        hurt_rank(new RedisKey(1, KeyType.string, false, "WB_hurt_rank_%s_%s", "武林悬赏伤害排行榜")),   //%s 服务器id/战区id  关卡id
        ;

        private RedisKey redisKey;

        WorldBoss(RedisKey redisKey) {
            this.redisKey = redisKey;
        }

        public String getRedisKey(Object... param) {
            return redisKey.getRedisKey(param);
        }
    }

    public enum WarZone {
        warZone(new RedisKey(1, KeyType.string, false, "warZone_%s", "战区信息")),
        warZoneVersion(new RedisKey(1, KeyType.map, false, "warZoneVersion_%s", "战区版本信息")),
        warZoneVersionIncr(new RedisKey(1, KeyType.string, false, "warZoneVersionIncr", "战区版本自增")),
        firstGsOpenTime(new RedisKey(1, KeyType.string, false, "firstGsOpenTime", "1服gs开启时间")),
        ;
        private RedisKey redisKey;

        WarZone(RedisKey redisKey) {
            this.redisKey = redisKey;
        }

        public String getRedisKey(Object... param) {
            return redisKey.getRedisKey(param);
        }
    }

    public enum MountainRiverTournament {
        season(new RedisKey(1, KeyType.string, false, "MRT_season", "赛季信息（全服一份）")),
        level_rank(new RedisKey(-1, KeyType.sortSet, false, "MRT_level_rank_%s", "段位榜（战区）")),
        point_rank(new RedisKey(-1, KeyType.sortSet, false, "MRT_point_rank_%s", "积分榜（战区）")),
        point_rank_temp(new RedisKey(-1, KeyType.map, false, "MRT_point_rank_temp_%s", "积分榜临时（战区）")),
        match_weight(new RedisKey(-1, KeyType.map, true, "MRT_match_weight", "匹配权重")),
        special_report(new RedisKey(-1, KeyType.map, true, "MRT_special_report_%s", "特殊战报（战区）当前记录")),
        special_report_show(new RedisKey(-1, KeyType.map, true, "MRT_special_report_%s", "特殊战报（战区）显示信息")),
        fengtailu(new RedisKey(-1, KeyType.list, true, "MRT_fengtailu_%s", "风采录（战区）")),
        support_count(new RedisKey(-1, KeyType.string, true, "MRT_support_count_%s", "点赞（个人）")),
        support_count_key(new RedisKey(-1, KeyType.set, true, "MRT_support_count_key", "点赞key")),
        last_level(new RedisKey(-1, KeyType.map, false, "MRT_last_level_%s", "上次段位榜（PlayType）")),
        reissue_Level(new RedisKey(-1, KeyType.map, false, "MRT_reissue_Level", "补发段位奖励")),
        ;
        private RedisKey redisKey;

        MountainRiverTournament(RedisKey redisKey) {
            this.redisKey = redisKey;
        }

        public String getRedisKey(Object... param) {
            return redisKey.getRedisKey(param);
        }
    }

    public enum LeagueSLG {
        area(new RedisKey(1, KeyType.string, false, "SLG_area_%s", "SLG赛区数据"));

        private RedisKey redisKey;

        LeagueSLG(RedisKey redisKey) {
            this.redisKey = redisKey;
        }

        public String getRedisKey(Object... param) {
            return redisKey.getRedisKey(param);
        }
    }

    public enum Tournament {
        tournament_rank(new RedisKey(1, KeyType.string, false, "tournament_rank_%s_%s", "实时pvp排行榜")),   //%s 赛季id   %s 定位key

        live_list(new RedisKey(100, KeyType.map, true, "tournament_live_list", "直播列表")),
        ;

        private RedisKey redisKey;

        Tournament(RedisKey redisKey) {
            this.redisKey = redisKey;
        }

        public String getRedisKey(Object... param) {
            return redisKey.getRedisKey(param);
        }
    }

    public enum Live {
        live_count(new RedisKey(100, KeyType.string, false, "live_count_%s", "直播观众统计")),
        live_id(new RedisKey(1, KeyType.string, false, "live_id", "直播间id生成器")),
        ;

        private RedisKey redisKey;

        Live(RedisKey redisKey) {
            this.redisKey = redisKey;
        }

        public String getRedisKey(Object... param) {
            return redisKey.getRedisKey(param);
        }
    }

    public enum SmallGroup {
        small_group_info(new RedisKey(1, KeyType.string, false, "SG_info_%s", "小团体信息")), //%s 小团体唯一id
        small_group_create(new RedisKey(1, KeyType.string, false, "SG_create_%s", "小团体创建信息")), //%s 组队id
        player_small_group_id_info(new RedisKey(1, KeyType.string, false, "SG_id_info_%s", "玩家小团体id信息")),   //%s 玩家id
        make_acquaintances_info(new RedisKey(1, KeyType.map, false, "SG_MA_info_%s", "结识信息")), //%s 表id
        make_acquaintances_apply_info(new RedisKey(1, KeyType.map, false, "SG_MA_apply_info_%s", "收到的结识申请")), //接受者玩家id
        marry_cruise_apply_info(new RedisKey(1, KeyType.string, false, "SG_CAI_%s_%s_%s", "情缘巡游申请")),
        small_group_group_id(new RedisKey(1, KeyType.string, false, "SG_group_id_%s", "小团体群聊id")),  //群聊id
        ;

        private RedisKey redisKey;

        SmallGroup(RedisKey redisKey) {
            this.redisKey = redisKey;
        }

        public String getRedisKey(Object... param) {
            return redisKey.getRedisKey(param);
        }
    }


    public enum ActivityTeam {
        //活动id统计， 节点类型，节点id
        team_act_ids(new RedisKey(1, KeyType.set, false, "ACT_TEAM_ACTIVITY_IDS_%s_%s", "组队活动id列表")),
        //队伍id自增，活动id、战区id
        team_count(new RedisKey(1, KeyType.string, false, "ACT_TEAM_ID_%s_%s", "队伍最大id")),
        //队伍数据，活动id、战区id、队伍id
        team_data(new RedisKey(1, KeyType.string, false, "ACT_TEAM_DATA_%s_%s_%s", "队伍数据")),
        ;

        private RedisKey redisKey;

        ActivityTeam(RedisKey redisKey) {
            this.redisKey = redisKey;
        }

        public String getRedisKey(Object... param) {
            return redisKey.getRedisKey(param);
        }
    }

    public enum SilkRoad{
        //玩家榜  活动id、分组id
        player_rank(new RedisKey(-1, KeyType.sortSet, false, "Silk_player_rank_%s_%s", "玩家榜")),
        //帮派榜  活动id、分组id
        league_rank(new RedisKey(-1, KeyType.sortSet, false, "Silk_league_rank_%s_%s", "帮派榜"));

        private RedisKey redisKey;
        SilkRoad(RedisKey redisKey){
            this.redisKey = redisKey;
        }
        public String getRedisKey(Object... param){
            return redisKey.getRedisKey(param);
        }
    }

    public enum League{
        mini_league(new RedisKey(-1, KeyType.string, false, "mini_league_%s", "帮派概要数据"));

        private RedisKey redisKey;
        League(RedisKey redisKey){
            this.redisKey = redisKey;
        }
        public String getRedisKey(Object... param){
            return redisKey.getRedisKey(param);
        }
    }

    public enum ChessActivity {
        //象棋积分排行榜 活动ID_战区ID 如果是本服则为0
        chess_activity_rank(new RedisKey(1, KeyType.sortSet, false, "chess_activity_rank_%s_%s", "象棋积分排行榜")),
        ;

        private RedisKey redisKey;

        ChessActivity(RedisKey redisKey) {
            this.redisKey = redisKey;
        }

        public String getRedisKey(Object... param) {
            return redisKey.getRedisKey(param);
        }
    }
}

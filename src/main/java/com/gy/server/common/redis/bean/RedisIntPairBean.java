package com.gy.server.common.redis.bean;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;
import com.ttlike.server.tl.baselib.serialize.hero.RedisIntPairBeanDb;

/**
 * <AUTHOR> - [Create on 2021/09/15 10:10]
 */
@ProtobufClass
public class RedisIntPairBean {

    @Protobuf(order = 1)
    private int left;

    @Protobuf(order = 2)
    private int right;

    public RedisIntPairBean() {
    }

    public RedisIntPairBean(RedisIntPairBeanDb db) {
        this.left = db.getLeft();
        this.right = db.getRight();
    }

    public int getLeft() {
        return left;
    }

    public void setLeft(int left) {
        this.left = left;
    }

    public int getRight() {
        return right;
    }

    public void setRight(int right) {
        this.right = right;
    }

    public static  RedisIntPairBean of(int left, int right) {
        RedisIntPairBean result = new RedisIntPairBean();

        result.left = left;
        result.right = right;

        return result;
    }

    public RedisIntPairBeanDb genDb() {
        return new RedisIntPairBeanDb(left, right);
    }
}

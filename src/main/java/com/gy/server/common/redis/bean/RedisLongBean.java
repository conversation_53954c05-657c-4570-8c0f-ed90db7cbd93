package com.gy.server.common.redis.bean;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;

import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;


@ProtobufClass
public class RedisLongBean {

    @Protobuf(order = 1)
    private long param;

    public RedisLongBean() {}

    public RedisLongBean(long param) {
        this.param = param;
    }

    public long getParam() {
        return param;
    }

    public RedisLongBean setParam(long param) {
        this.param = param;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        RedisLongBean that = (RedisLongBean) o;

        return new EqualsBuilder()
                .append(param, that.param)
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(param)
                .toHashCode();
    }

    @Override
    public String toString() {
        return "RedisLongBean{" +
                "param='" + param + '\'' +
                '}';
    }

}

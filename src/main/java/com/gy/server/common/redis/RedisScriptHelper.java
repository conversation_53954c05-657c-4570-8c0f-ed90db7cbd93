package com.gy.server.common.redis;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import com.gy.server.core.ServerConstants;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.jprotobuf.PbUtilCompress;

import io.netty.util.AsciiString;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.redisson.api.RScript;

/**
 * <AUTHOR> - [Create on 2021/03/23 13:24]
 */
public final class RedisScriptHelper {

    private RedisScriptHelper() {}

    public static <M> Pair<Long, Double> zsetGetRankAndScore(String key, M member) {
        List<Object> list = (List<Object>) RedisScript.ZsetGetRankAndScore.evalSha(RScript.ReturnType.MULTI, Collections.singletonList(key), member);
        if (CollectionUtil.isNotEmpty(list)) {
            long rank = (long) list.get(0) + 1;
            double score = new AsciiString((byte[]) list.get(1)).parseDouble();
            return Pair.of(rank, score);
        }

        return Pair.of(-1L, -1.0);
    }

    /**
     * left: rank, middle: 分数, right: 自定义bean
     */
    @SafeVarargs
    @SuppressWarnings("unchecked")
    public static <M> List<Triple<Long, Double, M>> multiZsetGetRankAndScores(String key, M... members) {
        if (Objects.isNull(members) || members.length <= 0) {
            return Collections.emptyList();
        }

        Class<? extends M> memberClass = (Class<? extends M>) members[0].getClass();
        List<Triple<Long, Double, M>> result = new ArrayList<>();
        List<Object> list = (List<Object>) RedisScript.MultiZsetGetRankAndScore.evalSha(RScript.ReturnType.MULTI, Collections.singletonList(key), (Object[]) members);
        if (CollectionUtil.isNotEmpty(list)) {
            for (int i = 0; i < list.size(); i += 3) {
                long rank = (long) list.get(i) + 1;
                double score = new AsciiString((byte[]) list.get(i + 1)).parseDouble();
                M m = PbUtilCompress.decode(memberClass, (byte[]) list.get(i + 2));
                result.add(Triple.of(rank, score, m));
            }
        }

        return result;
    }

    /**
     * 修改排行榜分数 不允许目标是空的
     *
     * @param rankKey  排行榜Key
     * @param member   成员
     * @param addValue 增加的分数
     * @return true执行成功 false 目标不存在
     */
    public static boolean updateRankScoreNotNull(String rankKey, String member, long addValue) {
        List<Object> redisKeys = new ArrayList<>();
        redisKeys.add(rankKey);
        long currentTimeMillis = ServerConstants.getCurrentTimeMillis();
        Long isResult = (Long) RedisScript.UpdateRankScore.evalSha(RScript.ReturnType.INTEGER, redisKeys, member, String.valueOf(addValue),
                String.valueOf(currentTimeMillis), String.valueOf(false));
        if (isResult == null) {
            return false;
        }
        return isResult == 1;
    }


    /**
     * 修改排行榜分数 允许目标是空的
     *
     * @param rankKey  排行榜Key
     * @param member   成员
     * @param addValue 增加的分数
     * @return true执行成功 false 目标不存在
     */
    public static boolean updateRankScore(String rankKey, String member, long addValue, int isPass) {
        List<Object> redisKeys = new ArrayList<>();
        redisKeys.add(rankKey);
        long currentTimeMillis = ServerConstants.getCurrentTimeMillis();
        Long isResult = (Long) RedisScript.UpdateRankScore.evalSha(RScript.ReturnType.INTEGER, redisKeys, member, String.valueOf(addValue),
                String.valueOf(currentTimeMillis), String.valueOf(true), String.valueOf(isPass));
        if (isResult == null) {
            return false;
        }
        return isResult == 1;
    }

}

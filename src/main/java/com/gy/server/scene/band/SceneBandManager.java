package com.gy.server.scene.band;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.Configuration;
import com.gy.server.core.thread.AbstractRunner;
import com.gy.server.game.player.Player;
import com.gy.server.scene.map.SceneMapManager;
import com.gy.server.scene.map.bean.SceneMapType;
import com.gy.server.utils.CollectionUtil;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.redis.key.BaseRedisKey;

/**
 * 场景服绑定管理
 * <AUTHOR> - [Created on 2022-03-22 20:33]
 */
public class SceneBandManager extends AbstractRunner {
    private static final int key_alive_time = 5;


    @Override
    protected void subRunnerExecute() throws Exception {
//        //获取场景服绑定场景id数量，续时间MessageSystemHashInvoke
//        String sceneServerId2SceneIdRedisKey = BaseRedisKey.Scene.sceneServerId2SceneId.getRedisKey(Configuration.serverId);
//        Set<String> currentServerSceneIds = TLBase.getInstance().getRedisAssistant().setGetAll(sceneServerId2SceneIdRedisKey);
//        List<String> delSceneIds = new ArrayList<>();
//        //检查当前场景id是否存活
//        for (String sceneId : currentServerSceneIds) {
//            String sceneBandKey = BaseRedisKey.Scene.sceneId2SceneServerId.getRedisKey(sceneId);
//            String sceneServerId = TLBase.getInstance().getRedisAssistant().get(sceneBandKey);
//            if(Objects.isNull(sceneServerId)
//                    || sceneServerId.isEmpty()
//                    || !Player.isSameServer(Integer.parseInt(sceneServerId))){
//                delSceneIds.add(sceneId);
//                System.out.println(String.format("sceneId is error : %s , will delete", sceneId));
//            }
//        }
//        //续时间
//        String redisKey = BaseRedisKey.Scene.live_sceneServerId.getRedisKey(Configuration.serverId);
//        TLBase.getInstance().getRedisAssistant().setInt(redisKey, currentServerSceneIds.size() - delSceneIds.size(), key_alive_time);
//
//        //删除没有绑定的sceneId
//        if(CollectionUtil.isNotEmpty(delSceneIds)){
//            TLBase.getInstance().getRedisAssistant().setRemove(sceneServerId2SceneIdRedisKey, delSceneIds.toArray(new String[0]));
//            //移除后操作
//            moveServerAfter(delSceneIds);
//        }
    }

    public void init(){
    }

    /**
     * 删除绑定sceneId后操作
     * @param delSceneIds sceneId列表
     */
    public void moveServerAfter(List<String> delSceneIds){
        //移除场景所有玩家
        for (String delSceneId : delSceneIds) {
            int serverId = Integer.parseInt(delSceneId);
            for (SceneMapType sceneMapType : SceneMapType.values()) {
                SceneMapManager.removeAllPlayer(sceneMapType, serverId);
            }
        }
    }

    @Override
    public long getRunnerInterval() {
        return 2000L;
    }
    @Override
    public String getRunnerName() {
        return "SceneBandManager";
    }
    private static final SceneBandManager instance = new SceneBandManager();
    SceneBandManager(){}
    public static SceneBandManager getInstance(){
        return instance;
    }

}

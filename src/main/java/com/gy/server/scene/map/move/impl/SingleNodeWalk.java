package com.gy.server.scene.map.move.impl;

import java.util.ArrayList;
import java.util.List;

import com.google.protobuf.AbstractMessage;
import com.gy.server.common.gateway.GateNodeManager;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.scene.map.bean.SceneMapType;
import com.gy.server.scene.map.bean.SceneNode;
import com.gy.server.scene.map.move.ASceneWalk;

/**
 * 单格子移动
 * <AUTHOR> - [Created on 2022-03-29 20:32]
 */
public class SingleNodeWalk extends ASceneWalk {


    @Override
    public List<Long> findEnterViewPlayer(SceneMapType mapType, long playerId, List<SceneNode> oldNineNodes, List<SceneNode> newNineNodes) {
        return null;
    }

    @Override
    public List<Long> findLeaveViewPlayer(SceneMapType mapType, long playerId, List<SceneNode> oldNineNodes, List<SceneNode> newNineNodes) {
        return null;
    }

    @Override
    public List<Long> findNinePlayer(SceneMapType mapType, long playerId, List<SceneNode> oldNineNodes, List<SceneNode> newNineNodes) {
        List<Long> playerIds = new ArrayList<>();
        for (SceneNode sceneNode : oldNineNodes) {
            playerIds.addAll(sceneNode.getAllPlayerIds());
        }
        return linePlayerFilter(mapType, playerId, playerIds);
    }

    @Override
    public void myselfAddView(long mainPlayerId, List<Long> playerIds) {

    }

    @Override
    public void myselfRemoveView(long mainPlayerId, List<Long> playerIds) {

    }


}

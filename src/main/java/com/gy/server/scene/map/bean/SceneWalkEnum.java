package com.gy.server.scene.map.bean;

import com.gy.server.scene.map.move.ASceneWalk;
import com.gy.server.scene.map.move.impl.SingleNodeWalk;
import com.gy.server.scene.map.move.impl.TwoNodeWalk;
import com.ttlike.server.tl.baselib.serialize.scene.ScenePosition3D;

/**
 * 场景服行走类型
 * <AUTHOR> - [Created on 2022-03-29 20:21]
 */
public enum SceneWalkEnum {

    single_node_walk(new SingleNodeWalk()),//单个地块行走
    two_node_walk(new TwoNodeWalk()), //跨地块行走
    ;

    private ASceneWalk walk;

    SceneWalkEnum(ASceneWalk walk){
        this.walk = walk;
    }

    public ASceneWalk getWalkLogic() {
        return walk;
    }
}

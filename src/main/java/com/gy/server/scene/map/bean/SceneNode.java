package com.gy.server.scene.map.bean;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

import com.gy.server.common.util.CommonUtils;
import com.gy.server.game.player.Player;
import com.ttlike.server.tl.baselib.serialize.scene.ScenePosition3D;
import com.ttlike.server.tl.baselib.util.BaseCommonUtil;

/**
 * 地块信息
 * <AUTHOR> - [Created on 2022-03-28 14:00]
 */
public class SceneNode {

    public float min_x;
    public float max_x;
    public float min_y;
    public float max_y;
    //地块玩家
    //serverID_playerIdList
    private Map<Integer, CopyOnWriteArraySet<Long>> playerIds = new ConcurrentHashMap<>();


    public SceneNode(float min_x, float max_x, float min_y, float max_y){
        this.min_x = min_x;
        this.max_x = max_x;
        this.min_y = min_y;
        this.max_y = max_y;
    }

    public void addPlayerId(long playerId){
        int serverId = Player.getServerId(playerId);
        playerIds.computeIfAbsent(serverId, k -> new CopyOnWriteArraySet<>());
        playerIds.get(serverId).add(playerId);
    }

    public Set<Long> getPlayerIds(int serverId) {
        return playerIds.getOrDefault(serverId, new CopyOnWriteArraySet<>());
    }

    public Set<Long> getAllPlayerIds(){
        Set<Long> allPlayerIds = new HashSet<>();
        for (CopyOnWriteArraySet<Long> playerIds : playerIds.values()) {
            allPlayerIds.addAll(playerIds);
        }
        return allPlayerIds;
    }

    public void removePlayerId(long playerId){
        int serverId = Player.getServerId(playerId);
        playerIds.getOrDefault(serverId, new CopyOnWriteArraySet<>()).remove(playerId);
    }

    public void removePlayerId(int serverId){
        playerIds.remove(serverId);
    }

    @Override
    public boolean equals(Object obj) {
        if(!(obj instanceof SceneNode)){
            return false;
        }
        SceneNode target = (SceneNode)obj;
        return target.min_x == this.min_x && target.max_x == this.max_x
                && target.min_y == this.min_y && target.max_y == this.max_y;
    }

    @Override
    public String toString() {
        return String.format("SceneNode, min_x : %s, max_x : %s, min_y : %s, max_y : %s", min_x, max_x, min_y, max_y);
    }
}

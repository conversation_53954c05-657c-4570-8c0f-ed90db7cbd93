package com.gy.server.scene.map.move;

import com.google.protobuf.AbstractMessage;
import com.gy.server.common.gateway.GateNodeManager;
import com.gy.server.game.packet.PtCode;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbScene;
import com.gy.server.scene.divisionLine.DivisionLine;
import com.gy.server.scene.divisionLine.DivisionLineManager;
import com.gy.server.scene.map.SceneMapManager;
import com.gy.server.scene.map.SceneMapService;
import com.gy.server.scene.map.bean.*;
import com.gy.server.utils.CollectionUtil;
import com.ttlike.server.tl.baselib.serialize.scene.ScenePosition3D;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 场景移动
 * <AUTHOR> - [Created on 2022-03-29 20:21]
 */
public abstract class ASceneWalk implements ISceneWalk {

    @Override
    public boolean walk(ScenePlayerInfo playerInfo, ScenePosition3D oldPosition, ScenePosition3D newPosition, int speed) {

        PbScene.ScenePlayer scenePlayer = playerInfo.genPb();

        List<SceneNode> oldNineNodes = SceneMapService.getNinNode(playerInfo, oldPosition);
        List<SceneNode> newNineNodes = SceneMapService.getNinNode(playerInfo, newPosition);

        SceneMapType mapType = playerInfo.getMapType();
        //通知移动
        List<Long> walkPlayers = findNinePlayer(mapType, playerInfo.getPlayerId(), oldNineNodes, newNineNodes);
        if(CollectionUtil.isNotEmpty(walkPlayers)){
            msgNotify(walkPlayers, PtCode.SCENE_MOVE_NOTIFY,  PbProtocol.SceneMoveNotify.newBuilder().setPlayer(scenePlayer).build(), true,scenePlayer.getPlayerId());
        }
        //视野增加
        List<Long> enterViewPlayer = findEnterViewPlayer(mapType, playerInfo.getPlayerId(), oldNineNodes, newNineNodes);
        if(CollectionUtil.isNotEmpty(enterViewPlayer)){
            msgNotify(enterViewPlayer, PtCode.SCENE_ENTER_NOTIFY,  PbProtocol.SceneEnterViewNotify.newBuilder().addPlayer(scenePlayer).build(), false, scenePlayer.getPlayerId());
            //自己增加视野玩家
            myselfAddView(playerInfo.getPlayerId(), enterViewPlayer);
        }

        //视野删除
        List<Long> leaveViewPlayer = findLeaveViewPlayer(mapType, playerInfo.getPlayerId(), oldNineNodes, newNineNodes);
        if(CollectionUtil.isNotEmpty(leaveViewPlayer)){
            msgNotify(leaveViewPlayer, PtCode.SCENE_LEAVE_NOTIFY, PbProtocol.SceneLeaveViewNotify.newBuilder().addPlayerId(scenePlayer.getPlayerId()).build(), false, scenePlayer.getPlayerId());
            //自己删除视野玩家
            myselfRemoveView(playerInfo.getPlayerId(), leaveViewPlayer);
        }
        return true;

    }

    /**
     * 移动广播
     */
    public static void msgNotify(List<Long> playerIds, int ptCode, AbstractMessage message, boolean isMerge, Long... messagePlayerIds){
        if(isMerge){
            //通过消息合并器合并处理发送
            GateNodeManager.getInstance().broadcastMsg(playerIds, ptCode, message, messagePlayerIds);
        }else{
            //直接发送消息
            GateNodeManager.getInstance().broadcast(playerIds, ptCode, message);
        }
    }

    /**
     * 分线过滤器
     * 1.过滤本服
     * 2.根据玩法过滤
     */
    public static List<Long> linePlayerFilter(SceneMapType mapType, long playerId, List<Long> allPlayerIds){
        List<Long> playerIds = new ArrayList<>();
        ScenePlayerInfo playerInfo = SceneMapManager.getPlayerInfo(playerId);
        allPlayerIds.remove(playerId);
        SceneWalkMark mark = playerInfo.getMark();
        for (long targetPlayer : allPlayerIds) {
            ScenePlayerInfo targetPlayerInfo = SceneMapManager.getPlayerInfo(targetPlayer);
            if(Objects.nonNull(targetPlayerInfo)
                && mark.sceneWalkMarkCheck(playerInfo, targetPlayerInfo)){
                playerIds.add(targetPlayer);
            }
        }
        return playerIds;
    }

    /**
     * 进入视野的玩家id
     */
    public abstract List<Long> findEnterViewPlayer(SceneMapType mapType, long playerId, List<SceneNode> oldNineNodes, List<SceneNode> newNineNodes);
    /**
     * 离开视野的玩家id
     */
    public abstract List<Long> findLeaveViewPlayer(SceneMapType mapType, long playerId, List<SceneNode> oldNineNodes, List<SceneNode> newNineNodes);

    /**
     * 行走需要通知的玩家id
     */
    public abstract List<Long> findNinePlayer(SceneMapType mapType, long playerId, List<SceneNode> oldNineNodes, List<SceneNode> newNineNodes);

    /**
     * 加载自己视野范围新增
     */
    public abstract void myselfAddView(long mainPlayerId, List<Long> playerIds);

    /**
     * 加载自己视野范围消失
     */
    public abstract void myselfRemoveView(long mainPlayerId, List<Long> playerIds);

    /**
     * 移动逻辑
     */
    public static boolean walkLogic(ScenePlayerInfo playerInfo, ScenePosition3D newPosition, ScenePosition3D targetPosition, int speed, float direction) {
        SceneMapInfo sceneMapInfo = SceneMapService.getSceneMapInfo(playerInfo.getMapType());
        ScenePosition3D oldPosition = playerInfo.getPosition();
        SceneNode oldNode = SceneMapInfo.findNode(playerInfo, oldPosition);
        SceneNode newNode = SceneMapInfo.findNode(playerInfo, newPosition);
        //newNode必须存在，代表可移动地块
        if(Objects.isNull(oldNode) || Objects.isNull(newNode)){
            return false;
        }
        //修改行走信息
        playerInfo.setSeed(speed);
        playerInfo.setPosition(newPosition);
        playerInfo.setTargetPosition(targetPosition);
        playerInfo.setDirection(direction);

        if(oldNode.equals(newNode)){
            SceneWalkEnum.single_node_walk.getWalkLogic().walk(playerInfo, oldPosition, newPosition, speed);
        }else{
            walk(playerInfo.getPlayerId(), oldNode, newNode);
            SceneWalkEnum.two_node_walk.getWalkLogic().walk(playerInfo, oldPosition, newPosition, speed);
        }

        return true;
    }

    /**
     * 同步玩家信息
     * @param playerInfo 玩家信息
     */
    public static void syncPlayerInfo(ScenePlayerInfo playerInfo, PbScene.ScenePlayer playerPb){
        List<SceneNode> oldNineNodes = SceneMapService.getNinNode(playerInfo, playerInfo.getPosition());
        List<Long> walkPlayers = SceneWalkEnum.single_node_walk.getWalkLogic().findNinePlayer(playerInfo.getMapType(), playerInfo.getPlayerId(), oldNineNodes, oldNineNodes);
        if(CollectionUtil.isNotEmpty(walkPlayers)){
            msgNotify(walkPlayers, PtCode.SCENE_CHANGE_PLAYER_INFO_NOTIFY,
                    PbProtocol.SceneChangePlayerInfoNotify.newBuilder().setPlayer(playerPb).build()
                    , false,playerInfo.getPlayerId());
        }
    }

    /**
     * 切格处理
     */
    private static void walk(long playerId, SceneNode oldNode, SceneNode newNode){
        newNode.addPlayerId(playerId);
        oldNode.removePlayerId(playerId);
    }

    public static void removePlayerInNode(long playerId, SceneMapType mapType){
        DivisionLine line = DivisionLineManager.getInstance().getDivisionLine(mapType, playerId);
        if(Objects.nonNull(line)){
            for (SceneNode[] sceneNodes : line.getSceneNodes()) {
                for (SceneNode sceneNode : sceneNodes) {
                    sceneNode.removePlayerId(playerId);
                }
            }
        }
    }

}

package com.gy.server.scene.map.bean;

import com.ttlike.server.tl.baselib.serialize.scene.ScenePosition3D;

/**
 * 场景坐标点信息
 * <AUTHOR> - [Created on 2022-03-18 18:17]
 */
public class SceneSpot {

    //地块下标
    public int nodeIndex;
    //地块X轴
    public float x;
    //地块Y轴
    public float y;
    //地块Z轴
    public float z;
    //是否可行走
    public boolean canWalk;

    public SceneSpot(int nodeIndex, float x, float y, float z, boolean canWalk){
        this.nodeIndex = nodeIndex;
        this.x = x;
        this.y = y;
        this.z = z;
        this.canWalk = canWalk;
    }

    public ScenePosition3D convertScenePosition3D(){
        ScenePosition3D position3D = new ScenePosition3D();
        position3D.setX(x);
        position3D.setY(y);
        position3D.setZ(z);
        return position3D;
    }

    @Override
    public String toString() {
        return String.format("SceneNode { nodeIndex : %s, positionX : %s, positionY : %s, positionZ : %s, canWalk : %s }", nodeIndex, x, y, z, canWalk);
    }

}

package com.gy.server.scene.map.bean;

/**
 * 二维位置信息
 * <AUTHOR> - [Created on 2022-04-19 16:25]
 */
public class ScenePosition {
    private float x;
    private float y;
    public ScenePosition(float x, float y){
        this.x = x;
        this.y = y;
    }

    public float getX() {
        return x;
    }

    public float getY() {
        return y;
    }

    @Override
    public String toString() {
        return String.format("ScenePosition, x : %s, y : %s", x, y);
    }

    @Override
    public boolean equals(Object obj) {
        if(obj instanceof ScenePosition){
            ScenePosition target = (ScenePosition) obj;
            return target.getX() == this.x && target.getY() == this.y;
        }
        return false;
    }


}

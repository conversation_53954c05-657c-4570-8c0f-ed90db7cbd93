package com.gy.server.scene.map;

import com.gy.server.common.gateway.GateNode;
import com.gy.server.common.gateway.GateNodeManager;
import com.gy.server.core.Configuration;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.core.packet.GatePacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.service.Service;
import com.gy.server.game.text.Text;
import com.gy.server.game.util.StringExtUtil;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbScene;
import com.gy.server.scene.divisionLine.DivisionLine;
import com.gy.server.scene.divisionLine.DivisionLineManager;
import com.gy.server.scene.divisionLine.DivisionLineWorker;
import com.gy.server.scene.map.bean.SceneMapInfo;
import com.gy.server.scene.map.bean.SceneMapType;
import com.gy.server.scene.map.bean.SceneNode;
import com.gy.server.scene.map.bean.ScenePlayerInfo;
import com.gy.server.scene.map.move.ASceneWalk;
import com.gy.server.scene.map.move.SceneWalkMark;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.FileUtil;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.serialize.scene.ScenePosition2D;
import com.ttlike.server.tl.baselib.serialize.scene.ScenePosition3D;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 场景服地图加载
 * <AUTHOR> - [Created on 2022-03-18 17:08]
 */
public class SceneMapService extends GatePacketHandler implements Service {

    public static final float side_length = 10;//边长
    public static final int nodeInfoByteSize = 4 + 4 + 4 + 4 + 1;//格子信息大小
    private static Map<SceneMapType, SceneMapInfo> sceneMapInfos = new ConcurrentHashMap<>();
    private static Map<Integer, SceneMapType> sceneId2Types = new HashMap<>();
//    private static List<ScenePosition2D> nineNodeTemplates;
    //九宫格模板x,y
    private static int[][] nineNodeTemplates = {
            {-1, 1},{0, 1},{1, 1},
            {-1, 0},{0, 0},{1, 0},
            {-1, -1},{0, -1},{1, -1},
    };

    public static ScenePosition2D createPointTemplate(int x, int y){
        ScenePosition2D position = new ScenePosition2D();
        position.setX(x);
        position.setY(y);
        return position;
    }

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.SceneInformation_SceneInformation);
        Map<SceneMapType, SceneMapInfo> sceneMapInfosTemp = new HashMap<>();
        Map<Integer, SceneMapType> sceneId2TypeMap = new HashMap<>();
        for (Map<String, String> map : mapList) {
            String mapDes = map.get("MapDes");
            int id = Integer.parseInt(map.get("Id"));

            List<ScenePosition3D> initBornPos = new ArrayList<>();
            String[] posStrs = map.get("MapCoordinate").split("_");
            for (String posStr : posStrs) {
                ScenePosition3D position3D = new ScenePosition3D();
                List<Float> positions = StringExtUtil.string2List(posStr, ",", Float.class);
                if(positions.size() < 3){
                    throw new IllegalArgumentException("SceneInformation_SceneInformation MapCoordinate is error !!");
                }else {
                    position3D.setX(positions.get(0));
                    position3D.setY(positions.get(1));
                    position3D.setZ(positions.get(2));
                }
                initBornPos.add(position3D);
            }
            if(CollectionUtil.isEmpty(initBornPos)){
                throw new IllegalArgumentException("SceneInformation_SceneInformation need MapCoordinate !!");
            }
            //读取地图字节数组
            byte[] mapBytes = FileUtil.read(Configuration.resourcePath + "/" + mapDes);
            SceneMapInfo mapInfo = new SceneMapInfo();
            mapInfo.readMapInfo(mapBytes);
            mapInfo.setInitPositionList(initBornPos);
            mapInfo.setRadius(Integer.parseInt(map.get("MapCoordinateRadius")));
            mapInfo.setFace((int)(Double.parseDouble(map.get("Face")) * 1000D));
            SceneMapType sceneMapType = SceneMapType.valueOf(map.get("SceneName"));
            sceneMapInfosTemp.put(sceneMapType, mapInfo);
            sceneId2TypeMap.put(id, sceneMapType);
        }
        sceneMapInfos = sceneMapInfosTemp;
        sceneId2Types = sceneId2TypeMap;
    }

    /**
     * 获取九宫格2d点
     */
    public static List<ScenePosition2D> getNineNodePoint(ScenePosition2D position2D){
        List<ScenePosition2D> nodes = new ArrayList<>();
        for (int[] nineNodeTemplate : nineNodeTemplates) {
            nodes.add(createPointTemplate(nineNodeTemplate[0] + position2D.getX(), nineNodeTemplate[1] + position2D.getY()));
        }
        return nodes;
    }

    /**
     * 根据坐标点获取九宫格
     */
    public static List<SceneNode> getNinNode(ScenePlayerInfo playerInfo, float x, float y){
        SceneMapType mapType = playerInfo.getMapType();
        SceneMapInfo mapInfo = sceneMapInfos.get(mapType);
        DivisionLine line = DivisionLineManager.getInstance().getDivisionLine(mapType, playerInfo.getPlayerId());
        List<SceneNode> nodes = new ArrayList<>();
        if(Objects.nonNull(line)){
            //根据当前坐标点获取中心地块格
            ScenePosition2D nodePoint = mapInfo.findNodePoint(x, y);
            SceneNode centerNode = SceneMapInfo.getNode(line.getSceneNodes(), nodePoint);
            if(Objects.nonNull(centerNode)){
                //根据中心格获取九宫格
                List<ScenePosition2D> nineNodePoint = getNineNodePoint(nodePoint);
                for (ScenePosition2D position2D : nineNodePoint) {
                    SceneNode node = SceneMapInfo.getNode(line.getSceneNodes(), position2D);
                    if(Objects.nonNull(node)){
                        nodes.add(node);
                    }
                }
            }
        }
        return nodes;
    }

    public static List<SceneNode> getNinNode(ScenePlayerInfo playerInfo, ScenePosition3D position3D){
        return getNinNode(playerInfo, position3D.getX(), position3D.getZ());
    }


    @Override
    public void clearConfigData() {
        sceneMapInfos.clear();
        sceneId2Types.clear();
    }

    public static Map<SceneMapType, SceneMapInfo> getSceneMapInfos() {
        return sceneMapInfos;
    }
    public static SceneMapInfo getSceneMapInfo(SceneMapType mapType) {
        return sceneMapInfos.get(mapType);
    }

    public static SceneMapType getSceneId2Types(int id){
        return sceneId2Types.getOrDefault(id, SceneMapType.mainCity);
    }

    @Override
    public boolean isGameServer() {
        return true;
    }
    @Override
    public boolean isSceneServer() {
        return true;
    }

    @Override
    public boolean isWorldServer() {
        return true;
    }

    /**
     * 离开场景
     */
    @Handler(PtCode.SCENE_LEAVE_REQ)
    private void leaveScene(GateNode gateNode, PbProtocol.SceneLeaveReq req, long time, long playerId1, long clientId) {
        long playerId = req.getPlayerId();
        boolean isExit = req.getIsExit();
        PbProtocol.SceneLeaveRst.Builder rst = PbProtocol.SceneLeaveRst.newBuilder().setResult(Text.genOkServerRstInfo());
        ScenePlayerInfo playerInfo = SceneMapManager.getPlayerInfo(playerId);
        logic:{
            if(Objects.isNull(playerInfo)){
                rst.setResult(Text.genServerRstInfo(Text.场景玩家不存在));
                break logic;
            }
            SceneMapType mapType = playerInfo.getMapType();
            //获得位置信息
            ScenePosition3D scenePosition = playerInfo.getPosition();
            if(Objects.isNull(scenePosition)){
                rst.setResult(Text.genServerRstInfo(Text.场景位置异常));
                break logic;
            }

            //获取九宫格玩家信息
            List<SceneNode> nineNodes = SceneMapService.getNinNode(playerInfo, scenePosition);
            if(CollectionUtil.isEmpty(nineNodes)){
                rst.setResult(Text.genServerRstInfo(Text.场景位置异常));
                break logic;
            }
            //踢掉格子信息
            SceneNode oldNode = SceneMapInfo.findNode(playerInfo, playerInfo.getPosition());
            oldNode.removePlayerId(playerId);
            //位置清除
            if(isExit){
                for (SceneMapType sceneMapType : SceneMapType.values()) {
                    SceneMapManager.correctPlayerPosition(sceneMapType, playerId, null);
                }
            }
            //退出分线
            DivisionLineManager.getInstance().unAllocateLine(mapType, playerId);

            List<Long> otherPlayerIds = new ArrayList<>();
            int serverId = Player.getServerId(playerId);
            for (SceneNode sceneNode : nineNodes) {
                otherPlayerIds.addAll(sceneNode.getPlayerIds(serverId));
            }
            //去除本身
            otherPlayerIds.remove(playerId);
            //通知场景中的玩家
            PbProtocol.SceneLeaveViewNotify.Builder notifyMsg = PbProtocol.SceneLeaveViewNotify.newBuilder();
            notifyMsg.addPlayerId(playerId);
            GateNodeManager.getInstance().broadcast(otherPlayerIds, PtCode.SCENE_LEAVE_NOTIFY, notifyMsg.build());

            if(Objects.nonNull(playerInfo.getMark())){
                rst.setOldMark(PbScene.SceneMark.valueOf(playerInfo.getMark().getMark()));
                playerInfo.setMark(SceneWalkMark.simple);
            }
        }

        //响应消息
        gateNode.send(req.getPlayerId(), clientId, PtCode.SCENE_LEAVE_RST, rst.build(), time);

        //后续处理
        if(rst.getResult().getResult()){
            if(isExit){
                //去除玩家信息
                SceneMapManager.removePlayerInfo(playerId);
            }else{
                //更新玩家位置
                playerInfo.setSeed(0);
                playerInfo.getSceneMapHistoryPosInfos().put(playerInfo.getMapType(), playerInfo.getPosition());
            }
        }

    }

    @Handler(PtCode.SCENE_MOVE_REQ)
    private void sceneMove(GateNode gateNode, PbProtocol.SceneMoveReq req, long time, long playerId1, long clientId) {
        long playerId = req.getPlayerId();
        PbScene.ScenePosition newCurrentPosition = req.getPosition();
        int speed = req.getSpeed();
        float direction = req.getDirection();
        PbScene.ScenePosition targetPosition = req.getTargetPosition();
        PbProtocol.SceneMoveRst.Builder rst = PbProtocol.SceneMoveRst.newBuilder().setResult(Text.genOkServerRstInfo());

        ScenePlayerInfo playerInfo = SceneMapManager.getPlayerInfo(playerId);
        logic:{
            if(Objects.isNull(playerInfo)){
                rst.setResult(Text.genServerRstInfo(Text.场景玩家不存在));
                break logic;
            }

            //获得位置信息
            ScenePosition3D oldCurrentPosition = playerInfo.getPosition();
            if(Objects.isNull(oldCurrentPosition)){
                rst.setResult(Text.genServerRstInfo(Text.场景位置异常));
                break logic;
            }

            DivisionLine line = DivisionLineManager.getInstance().getDivisionLine(playerInfo.getMapType(), playerInfo.getPlayerId());
            if(line == null){
                rst.setResult(Text.genServerRstInfo(Text.数据异常));
                break logic;
            }

            SceneMapInfo sceneMapInfo = SceneMapService.getSceneMapInfo(playerInfo.getMapType());
            ScenePosition3D newPosition3D = convertPosition(newCurrentPosition);
            if(newPosition3D.getX() > 200){
                System.out.println(String.format("player move is Error , playerId  : %s, position : %s", playerId, newPosition3D));
            }
            //检查当前点能否移动
            if(!sceneMapInfo.checkCanWalk(newPosition3D)){
                rst.setResult(Text.genServerRstInfo(Text.场景位置不可移动));
                break logic;
            }

            //移动逻辑
            if(!ASceneWalk.walkLogic(playerInfo, newPosition3D, convertPosition(targetPosition), speed, direction)){
                rst.setResult(Text.genServerRstInfo(Text.场景移动失败));
                break logic;
            }else{
                playerInfo.getSceneMapHistoryPosInfos().put(playerInfo.getMapType(), playerInfo.getPosition());
            }
        }

        if(!rst.getResult().getResult()){
            System.out.println("move error, playerId : " + playerId + " error ptCode : " + rst.getResult().getErrInfo().getMessageId());
        }

        gateNode.send(req.getPlayerId(), clientId, PtCode.SCENE_MOVE_RST, rst.build(), time);

        //检查是否同步位置信息到gs
        if(Objects.nonNull(playerInfo)){
            needSyncPos2Gs(playerInfo);
        }

    }

    private void needSyncPos2Gs(ScenePlayerInfo playerInfo){
        SceneMapType mapType = playerInfo.getMapType();
        if(Objects.nonNull(mapType)){
            if(mapType.isNeedSyncPos2Gs()){
                long playerId = playerInfo.getPlayerId();
                ScenePosition3D position = playerInfo.getPosition();
                ServerCommandRequest commandRequest = CommandRequests.newServerCommandRequest("Scene2GsCommandService.playerMove");
                TLBase.getInstance().getRpcUtil().sendToNode(com.ttlike.server.tl.baselib.ServerType.GAME, Player.getRealServerId(playerId), commandRequest, playerId, mapType.name(), position);
            }
        }

    }

    private ScenePosition3D convertPosition(PbScene.ScenePosition position){
        ScenePosition3D result = new ScenePosition3D();
        result.setX((float)position.getX() / 1000F);
        result.setY((float)position.getY() / 1000F);
        result.setZ((float)position.getZ() / 1000F);
        return result;
    }

    public static PbScene.ScenePosition convertPosition(ScenePosition3D position3D){
        PbScene.ScenePosition.Builder position = PbScene.ScenePosition.newBuilder();
        position.setX((int) position3D.getX());
        position.setY((int) position3D.getY());
        position.setZ((int) position3D.getZ());
        return position.build();
    }

    @Handler(PtCode.SCENE_LINE_LIST_REQ)
    private void sceneLineList(GateNode gateNode, PbProtocol.SceneLineListReq req, long time, long playerId1, long clientId) {
        long playerId = req.getPlayerId();
        PbProtocol.SceneLineListRst.Builder rst = PbProtocol.SceneLineListRst.newBuilder().setResult(Text.genOkServerRstInfo());
        ScenePlayerInfo playerInfo = SceneMapManager.getPlayerInfo(playerId);
        logic:
        {
            if (Objects.isNull(playerInfo)) {
                rst.setResult(Text.genServerRstInfo(Text.场景玩家不存在));
                break logic;
            }

            //获得位置信息
            if (Objects.isNull(playerInfo.getPosition())) {
                rst.setResult(Text.genServerRstInfo(Text.场景位置异常));
                break logic;
            }
            //获取所有分线
            DivisionLineWorker lineWorker = DivisionLineManager.getInstance().getDivisionWorker(playerInfo.getMapType());
            List<DivisionLine> allLine = lineWorker.getDivisionLineInfos()
                    .values()
                    .stream()
                    .filter(w -> w.getStatus() == DivisionLine.Status_Run)
                    .sorted(Comparator.comparingInt(DivisionLine::getLineId))
                    .collect(Collectors.toList());
            for (int i = 0; i < allLine.size(); i++) {
                rst.addLineInfos(DivisionLineManager.genLineWorker(i + 1, allLine.get(i)));
            }
        }
        gateNode.send(playerId, clientId, PtCode.SCENE_LINE_LIST_RST, rst.build(), time);
    }

    @Handler(PtCode.SCENE_CHANGE_LINE_REQ)
    private void sceneChangeLine(GateNode gateNode, PbProtocol.SceneChangeLineReq req, long time, long playerId1, long clientId) {
        long playerId = req.getPlayerId();
        PbProtocol.SceneChangeLineRst.Builder rst = PbProtocol.SceneChangeLineRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            ScenePlayerInfo playerInfo = SceneMapManager.getPlayerInfo(playerId);
            if (Objects.isNull(playerInfo)) {
                rst.setResult(Text.genServerRstInfo(Text.场景玩家不存在));
                break logic;
            }
            DivisionLine newLine = DivisionLineManager.getInstance().getDivisionLine(playerInfo.getMapType(), req.getLineId());
            DivisionLine oldLine = DivisionLineManager.getInstance().getDivisionLine(playerInfo.getMapType(), playerId);
            if(Objects.isNull(oldLine)){
                rst.setResult(Text.genServerRstInfo(Text.场景分线错误));
                break logic;
            }
            //不能切换当前分线
            if(oldLine.getLineId() == req.getLineId()){
                rst.setResult(Text.genServerRstInfo(Text.场景不能切换到当前分线));
                break logic;
            }
            if(Objects.isNull(newLine)){
                rst.setResult(Text.genServerRstInfo(Text.场景分线不存在));
                break logic;
            }
            //检查当前分线人数能否切过去 总人数%85
            if(newLine.getPlayerIds().size() >= DivisionLineManager.max_change_line_player_num){
                rst.setResult(Text.genServerRstInfo(Text.场景分线人数已满));
                break logic;
            }
            //获得显示分线id
            int showLineId = 0;
            DivisionLineWorker lineWorker = DivisionLineManager.getInstance().getDivisionWorker(playerInfo.getMapType());
            List<DivisionLine> allLine = lineWorker.getDivisionLineInfos()
                    .values()
                    .stream()
                    .filter(w -> w.getStatus() == DivisionLine.Status_Run)
                    .sorted(Comparator.comparingInt(DivisionLine::getLineId))
                    .collect(Collectors.toList());
            for (int i = 0; i < allLine.size(); i++) {
                if(allLine.get(i).getLineId() == newLine.getLineId()){
                    showLineId = i + 1;
                    break;
                }
            }
            rst.setLineInfo(DivisionLineManager.genLineWorker(showLineId, newLine));
            //切线
            DivisionLineManager.getInstance().changeLine(lineWorker, newLine, playerId);
            oldLine.exit(playerId);

        }
        //先通知客户端切线
        gateNode.send(playerId, clientId, PtCode.SCENE_CHANGE_LINE_RST, rst.build(), time);

    }

    @Handler(PtCode.HEART_CLIENT)
    private void heart(GateNode gateNode, long time, long playerId) {
        ScenePlayerInfo playerInfo = SceneMapManager.getPlayerInfo(playerId);
        if(Objects.nonNull(playerInfo)){
            //可以不用响应gs会处理
            playerInfo.setLastActiveTime(ServerConstants.getCurrentTimeMillis());
        }
        //忽略玩家没有在此场景服的情况
    }

    public static void main(String[] args) throws Exception {
        Configuration.init("");
        byte[] mapBytes = FileUtil.read(Configuration.resourcePath + "/" + "FB_BANGPAI01.scn.txt");
//        byte[] mapBytes = FileUtil.read(Configuration.resourcePath + "/" + "Test_FHGC01_TA.scn.txt");
        SceneMapInfo mapInfo = new SceneMapInfo();
        mapInfo.readMapInfo(mapBytes);


        ScenePosition3D newPosition3D = new ScenePosition3D();
//        float x = -10.79228401184082F;
//        float y = 0.08800113F;
//        float z = -5.6172146797180176F;
        float x = 20004F;
        float y = 1755F;
        float z = -8898F;
        newPosition3D.setX(x / 1000F);
        newPosition3D.setY(y / 1000F);
        newPosition3D.setZ(z / 1000F);

        System.out.println(mapInfo.findNode(newPosition3D));

        //检查当前点能否移动
        boolean b = mapInfo.checkCanWalk(newPosition3D);

        System.out.println(b);
//        System.out.println(mapInfo.getCanMovePosition().contains(new ScenePosition(1.5F, 0.4F)));
//        System.out.println(mapInfo.getCanMovePosition().contains(new ScenePosition(2.5F, 1.4F)));



    }


}

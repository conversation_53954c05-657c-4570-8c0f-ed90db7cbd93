package com.gy.server.scene.map.move.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbScene;
import com.gy.server.scene.map.SceneMapManager;
import com.gy.server.scene.map.bean.SceneMapType;
import com.gy.server.scene.map.bean.SceneNode;
import com.gy.server.scene.map.bean.ScenePlayerInfo;
import com.gy.server.scene.map.move.ASceneWalk;
import com.gy.server.utils.CollectionUtil;

import org.apache.commons.lang3.tuple.Pair;


/**
 * 双格子移动
 * <AUTHOR> - [Created on 2022-03-29 20:32]
 */
public class TwoNodeWalk extends ASceneWalk {


    @Override
    public List<Long> findEnterViewPlayer(SceneMapType mapType, long playerId, List<SceneNode> oldNineNodes, List<SceneNode> newNineNodes) {
        List<SceneNode> viewAddNodes = findDifferentSet(oldNineNodes, newNineNodes);
        List<Long> playerIds = new ArrayList<>();
        for (SceneNode sceneNode : viewAddNodes) {
            playerIds.addAll(sceneNode.getAllPlayerIds());
        }
        return linePlayerFilter(mapType, playerId, playerIds);
    }

    @Override
    public List<Long> findLeaveViewPlayer(SceneMapType mapType, long playerId, List<SceneNode> oldNineNodes, List<SceneNode> newNineNodes) {
        List<SceneNode> viewRemoveNodes = findDifferentSet(newNineNodes, oldNineNodes);
        List<Long> playerIds = new ArrayList<>();
        for (SceneNode sceneNode : viewRemoveNodes) {
            playerIds.addAll(sceneNode.getAllPlayerIds());
        }
        return linePlayerFilter(mapType, playerId, playerIds);
    }

    @Override
    public List<Long> findNinePlayer(SceneMapType mapType, long playerId, List<SceneNode> oldNineNodes, List<SceneNode> newNineNodes) {
        List<SceneNode> viewSameNodes = findSameSet(newNineNodes, oldNineNodes);
        List<Long> playerIds = new ArrayList<>();
        for (SceneNode sceneNode : viewSameNodes) {
            playerIds.addAll(sceneNode.getAllPlayerIds());
        }
        return linePlayerFilter(mapType, playerId, playerIds);
    }

    @Override
    public void myselfAddView(long mainPlayerId, List<Long> playerIds) {
        Pair<List<PbScene.ScenePlayer>, List<Long>> playerInfos = getPlayerInfos(playerIds);
        if(CollectionUtil.isNotEmpty(playerInfos.getKey())){
            msgNotify(longConvertList(mainPlayerId), PtCode.SCENE_ENTER_NOTIFY, PbProtocol.SceneEnterViewNotify.newBuilder().addAllPlayer(playerInfos.getKey()).build(), false,playerInfos.getValue().toArray(new Long[0]));
        }
    }

    @Override
    public void myselfRemoveView(long mainPlayerId, List<Long> playerIds) {
        msgNotify(longConvertList(mainPlayerId), PtCode.SCENE_LEAVE_NOTIFY, PbProtocol.SceneLeaveViewNotify.newBuilder().addAllPlayerId(playerIds).build(), false, playerIds.toArray(new Long[0]));
    }

    private Pair<List<PbScene.ScenePlayer>, List<Long>> getPlayerInfos(List<Long> playerIds){
        List<PbScene.ScenePlayer> playerInfos = new ArrayList<>();
        List<Long> resultPlayerIds = new ArrayList<>();
        for (long playerId : playerIds) {
            ScenePlayerInfo targetPlayerInfo = SceneMapManager.getPlayerInfo(playerId);
            if (Objects.nonNull(targetPlayerInfo)) {
                playerInfos.add(targetPlayerInfo.genPb());
                resultPlayerIds.add(playerId);
            }
        }
        return Pair.of(playerInfos, resultPlayerIds);
    }

    private List<Long> longConvertList(long playerId){
        List<Long> result = new ArrayList<>();
        result.add(playerId);
        return result;
    }

    /**
     * 单向差集
     */
    private List<SceneNode> findDifferentSet(List<SceneNode> mainNineNodes, List<SceneNode> otherNodes){
        List<SceneNode> nodes = new ArrayList<>();
        for (SceneNode node : otherNodes) {
            if(!mainNineNodes.contains(node)){
                nodes.add(node);
            }
        }
        return nodes;
    }

    /**
     * 交集
     */
    private List<SceneNode> findSameSet(List<SceneNode> fromNineNodes, List<SceneNode> toNineNodes){
        List<SceneNode> nodes = new ArrayList<>();
        for (SceneNode node : toNineNodes) {
            if(fromNineNodes.contains(node)){
                nodes.add(node);
            }
        }
        return nodes;
    }

}

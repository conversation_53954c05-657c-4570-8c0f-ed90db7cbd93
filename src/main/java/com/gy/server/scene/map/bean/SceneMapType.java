package com.gy.server.scene.map.bean;

import com.gy.server.core.command.CommandRequestParams;
import com.gy.server.game.activity.ActivityModule;
import com.gy.server.game.activity.ActivityTemplate;
import com.gy.server.game.activity.ActivityType;
import com.gy.server.game.activity.module.TeamTreasureHuntActivityModule;
import com.gy.server.game.player.Player;
import com.gy.server.game.scene.route.ISceneRouteRule;
import com.gy.server.game.scene.route.impl.SRFunction;
import com.gy.server.game.scene.route.impl.SRLeagueSJZ;
import com.gy.server.game.scene.route.impl.SRLocalServer;
import com.gy.server.game.scene.route.impl.SRTeamTreasureHunt;

import java.util.List;
import java.util.Objects;
import java.util.function.BiFunction;

/**
 * 场景服地图类型
 * <AUTHOR> - [Created on 2022-03-29 11:07]
 */
public enum SceneMapType {

    mainCity, //主城场景
    league,
    leagueMelee,    //帮派乱斗
    leagueMeleeCity,    //帮派乱斗-据点
    leagueEscort(true, new SRLocalServer(), null),       //帮派运镖
    leagueSJZ(false, new SRLeagueSJZ(), (player, mapType) -> new Object[]{player.getPlayerId()}),  //武林悬赏
    marryScene(true, new SRFunction(), (player, mapType) -> new Object[]{mapType}),   //情缘结婚场景

    teamTreasureHunt(true, new SRTeamTreasureHunt(), (player, mapType) -> {
        List<ActivityModule> activityModulesByType = player.getActivityModel().getActivityModulesByType(ActivityType.teamTreasureHunt);
        TeamTreasureHuntActivityModule module = (TeamTreasureHuntActivityModule) activityModulesByType.get(0);
        ActivityTemplate template = module.getTemplate();
        return new Object[]{Player.getRealServerId(player.getPlayerId()), template.crossType};
    }),//圣兽山寻宝
    ;

    BiFunction<Player, SceneMapType, Object[]> function;//接口参数
    boolean needSyncPos2Gs;
    ISceneRouteRule routeRule;//路由规则

    SceneMapType(){
        this(false, new SRLocalServer(), null);
    }

    SceneMapType(boolean needSyncPos2Gs, ISceneRouteRule routeRule, BiFunction<Player, SceneMapType, Object[]> function){
        this.needSyncPos2Gs = needSyncPos2Gs;
        this.routeRule = routeRule;
        this.function = function;
    }

    public boolean isNeedSyncPos2Gs() {
        return needSyncPos2Gs;
    }

    public static String[] valueNames(){
        SceneMapType[] values = SceneMapType.values();
        String[] result = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            result[i] = values[i].name();
        }
        return result;
    }

    public int getRouteSceneId(Player player){
        Object[] param = new Object[0];
        if(Objects.nonNull(function)){
            param = function.apply(player, this);
        }
        return routeRule.routeSceneId(new CommandRequestParams(param));
    }

    public int getRouteSceneId(Object... param){
        return routeRule.routeSceneId(new CommandRequestParams(param));
    }


}

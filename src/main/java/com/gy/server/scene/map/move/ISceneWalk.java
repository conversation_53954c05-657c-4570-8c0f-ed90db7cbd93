package com.gy.server.scene.map.move;

import java.util.List;

import com.google.protobuf.AbstractMessage;
import com.gy.server.scene.map.bean.ScenePlayerInfo;
import com.ttlike.server.tl.baselib.serialize.scene.ScenePosition3D;

/**
 * <AUTHOR> - [Created on 2022-03-29 20:21]
 */
public interface ISceneWalk {

    public boolean walk(ScenePlayerInfo playerInfo, ScenePosition3D newPosition, ScenePosition3D targetPosition, int speed);


}

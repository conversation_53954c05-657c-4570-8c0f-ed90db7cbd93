package com.gy.server.scene.map;

import com.gy.server.game.player.PlayerManager;
import com.gy.server.scene.map.bean.SceneMapType;
import com.gy.server.scene.map.bean.ScenePlayerInfo;
import com.gy.server.scene.map.move.ASceneWalk;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.function.Ticker;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public class ScenePlayerStatusTicker implements Ticker {

    private ScenePlayerStatusTicker(){}
    private static ScenePlayerStatusTicker instance = new ScenePlayerStatusTicker();
    public static ScenePlayerStatusTicker getInstance(){
        return instance;
    }


    @Override
    public void tick() {
        //检测玩家在线情况
        Map<Long, ScenePlayerInfo> playerInfos = SceneMapManager.getPlayerInfos();
        Set<Long> deletePlayerIds = new HashSet<>();
        for (ScenePlayerInfo playerInfo : playerInfos.values()) {
            if(!playerInfo.checkConnection()){
                deletePlayerIds.add(playerInfo.getPlayerId());
            }
        }
        if(CollectionUtil.isNotEmpty(deletePlayerIds)){
            for (Long deletePlayerId : deletePlayerIds) {
                //移除所有位置信息
                for (SceneMapType mapType : SceneMapType.values()) {
                    ASceneWalk.removePlayerInNode(deletePlayerId, mapType);
                }
                //移除场景中玩家信息
                SceneMapManager.removePlayerInfo(deletePlayerId);
            }
        }
    }
}

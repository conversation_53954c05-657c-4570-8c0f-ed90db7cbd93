package com.gy.server.scene.map.bean;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import com.gy.server.common.util.CommonUtils;
import com.gy.server.scene.divisionLine.DivisionLine;
import com.gy.server.scene.divisionLine.DivisionLineManager;
import com.gy.server.scene.map.SceneMapService;
import com.ttlike.server.tl.baselib.serialize.scene.ScenePosition2D;
import com.ttlike.server.tl.baselib.serialize.scene.ScenePosition3D;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.UnpooledByteBufAllocator;
import org.apache.commons.lang3.tuple.Pair;

/**
 * 场景服地图信息
 * <AUTHOR> - [Created on 2022-03-29 11:03]
 */
public class SceneMapInfo {

    private short width;
    private short depth;
    private float nodeSize;
    private int nodeCount;
    private Map<Integer, SceneSpot> spots = new ConcurrentHashMap<>();
    private List<ScenePosition> canMovePosition = new ArrayList<>();
    private List<SceneSpot> randomBornSpots = new ArrayList<>();

    private float min_x;
    private float max_x;
    private float min_y;
    private float max_y;
    private SceneNode[][] sceneNodes;

    /**
     * 初始坐标点
     */
    private List<ScenePosition3D> initPositionList = new ArrayList<>();

    //朝向（放大1000倍）
    private int Face;

    /**
     * 随机半径
     */
    private int radius;

    /**
     * 读取字节数组，构建地图
     */
    public void readMapInfo(byte[] mapBytes){
        //使用byteBuf读取数据
        ByteBuf byteBuf = UnpooledByteBufAllocator.DEFAULT.heapBuffer(mapBytes.length);
        byteBuf.writeBytes(mapBytes);
        //客户端小端序写入文件，所以小端序读取
        width = byteBuf.readShortLE();//宽度
        depth = byteBuf.readShortLE();//高度
        nodeSize = byteBuf.readFloatLE();//格子大小
        nodeCount = byteBuf.readIntLE();//格子数量
        Map<Integer, SceneSpot> spots = new ConcurrentHashMap<>();
        while(byteBuf.readableBytes() >= SceneMapService.nodeInfoByteSize){
            SceneSpot spot = new SceneSpot(byteBuf.readIntLE(), byteBuf.readFloatLE(), byteBuf.readFloatLE(), byteBuf.readFloatLE(), byteBuf.readBoolean());
            spots.put(spot.nodeIndex, spot);
            if(spot.canWalk){
                //注：客户端的y是高低，服务器暂时不用，所以z就是计算位置的y
                canMovePosition.add(new ScenePosition(spot.x, spot.z));
            }

            //记录坐标点，用于构建格子信息
            recordSpot(spot);
        }
        this.spots = spots;
        List<SceneSpot> allSpots = new ArrayList<>(spots.values());
        for(int i = 0; i < allSpots.size(); i++){
            SceneSpot sceneSpot = allSpots.get(i);
            if(sceneSpot.canWalk){
                randomBornSpots.add(sceneSpot);
            }
            if(randomBornSpots.size() >= 100){
                break;
            }
        }

        //构建格子信息
        buildSceneNode();
    }

    private void recordSpot(SceneSpot spot){
        if(spot.canWalk){
            if(min_x == 0){
                min_x = spot.x;
                min_y = spot.z;
            }
            min_x = Float.min(min_x, spot.x);
            min_y = Float.min(min_y, spot.z);
            max_x = Float.max(max_x, spot.x);
            max_y = Float.max(max_y, spot.z);
        }
    }

    public void buildSceneNode(){
        this.sceneNodes = buildSceneNodes();
    }

    public SceneNode[][] buildSceneNodes(){
        float side_length = SceneMapService.side_length;
        //行数（代表y数量）
        int rows = max_x - min_x % side_length == 0 ? (int)((max_x - min_x) / side_length) : (int)((max_x - min_x) / side_length) + 1;
        //列数（代表x数量）
        int columns = (max_y - min_y) % side_length == 0 ? (int)((max_y - min_y) / side_length) : (int)((max_y - min_y) / side_length) + 1;
        SceneNode[][] sceneNodes = new SceneNode[rows][columns];
        for (int i = 0; i < rows; i++) {
            for (int j = 0; j < columns; j++) {
                sceneNodes[i][j] = new SceneNode(min_x + i * side_length, min_x + (i + 1) * side_length
                        , min_y + j * side_length, min_y + (j + 1) * side_length);
            }
        }
        return sceneNodes;
    }

    /**
     * 获得坐标点当前地块
     */
    public static SceneNode findNode(ScenePlayerInfo playerInfo, ScenePosition3D position3D){
        SceneMapType mapType = playerInfo.getMapType();
        SceneMapInfo sceneMapInfo = SceneMapService.getSceneMapInfo(mapType);
        DivisionLine line = DivisionLineManager.getInstance().getDivisionLine(mapType, playerInfo.getPlayerId());
        return getNode(line.getSceneNodes(), sceneMapInfo.findNodePoint(position3D.getX(), position3D.getZ()));
    }

    /**
     * 测试使用
     * @param position3D
     * @return
     */
    @Deprecated
    public SceneNode findNode(ScenePosition3D position3D){
        return getNode(sceneNodes, findNodePoint(position3D.getX(), position3D.getZ()));
    }

    /**
     * 根据当前坐标点获取2d格子点（用于取2d地图块）
     */
    public ScenePosition2D findNodePoint(float x, float y){
        float side_length = SceneMapService.side_length;
        int xx = (int) Math.floor(Math.abs(x - min_x) / side_length);
        int yy = (int) Math.floor(Math.abs(y - min_y) / side_length);
        return SceneMapService.createPointTemplate(xx, yy);
    }

    public ScenePosition2D findNodePoint(ScenePosition3D position3D){
        return findNodePoint(position3D.getX(), position3D.getZ());
    }

    //客户端最小点间隔
    private static final float min_can_move_range = 1.00F;
    private static final int position_scale = 2;
    private static final int roundingMode = BigDecimal.ROUND_HALF_UP;

    /**
     * 检查3D点能否行走
     */
    public boolean checkCanWalk(ScenePosition3D position3D){
        int addedX = (int)((position3D.getX() - min_x) / min_can_move_range);
        int addedY = (int)((position3D.getZ() - min_y) / min_can_move_range);
        float cur_min_x = addedX + min_x;
        float cur_min_y = addedY + min_y;

        float cur_max_x = cur_min_x + min_can_move_range;
        float cur_max_y = cur_min_y + min_can_move_range;

        return getCanMovePosition().contains(new ScenePosition(round(cur_min_x, position_scale, roundingMode, false), round(cur_min_y, position_scale, roundingMode, false)))
                || getCanMovePosition().contains(new ScenePosition(round(cur_max_x, position_scale,roundingMode, false), round(cur_max_y, position_scale,roundingMode, false)));
    }

    public static float round(float value, int scale, int roundingMode, boolean isY) {
//        if(isY && value < 0){
//            //矫正负数
//            value = value - 0.1F;
//        }
        BigDecimal bd = new BigDecimal(value);
        bd = bd.setScale(scale, roundingMode);
        return bd.floatValue();

    }

    public static SceneNode getNode(SceneNode[][] sceneNodes, ScenePosition2D position2D){
        try {
            if(Objects.nonNull(position2D)){
                return sceneNodes[position2D.getX()][position2D.getY()];
            }
        }catch (Exception ignore){
        }
        return null;
    }

    public List<ScenePosition> getCanMovePosition() {
        return canMovePosition;
    }


    public short getWidth() {
        return width;
    }

    public void setWidth(short width) {
        this.width = width;
    }

    public short getDepth() {
        return depth;
    }

    public void setDepth(short depth) {
        this.depth = depth;
    }

    public float getNodeSize() {
        return nodeSize;
    }

    public void setNodeSize(float nodeSize) {
        this.nodeSize = nodeSize;
    }

    public int getNodeCount() {
        return nodeCount;
    }

    public void setNodeCount(int nodeCount) {
        this.nodeCount = nodeCount;
    }

    public Map<Integer, SceneSpot> getSpots() {
        return spots;
    }

    public void setSpots(Map<Integer, SceneSpot> spots) {
        this.spots = spots;
    }

    public float getMin_x() {
        return min_x;
    }

    public void setMin_x(float min_x) {
        this.min_x = min_x;
    }

    public float getMax_x() {
        return max_x;
    }

    public void setMax_x(float max_x) {
        this.max_x = max_x;
    }

    public float getMin_y() {
        return min_y;
    }

    public void setMin_y(float min_y) {
        this.min_y = min_y;
    }

    public float getMax_y() {
        return max_y;
    }

    public void setMax_y(float max_y) {
        this.max_y = max_y;
    }

//    public void setSceneNodes(SceneNode[][] sceneNodes) {
//        this.sceneNodes = sceneNodes;
//    }

    public int getFace() {
        return Face;
    }

    public void setFace(int face) {
        Face = face;
    }

    public List<ScenePosition3D> getInitPositionList() {
        return initPositionList;
    }

    public void setInitPositionList(List<ScenePosition3D> initPositionList) {
        this.initPositionList = initPositionList;
    }

    public SceneSpot randomBorn(){
        Collections.shuffle(initPositionList);
        ScenePosition3D initPosition = initPositionList.get(0);
        SceneSpot sceneSpot = new SceneSpot(1, initPosition.getX(), initPosition.getY(), initPosition.getZ(), true);
        if(radius != 0){
            Pair<Double, Double> pair = CommonUtils.calTargetPoint(Pair.of((double) sceneSpot.x, (double) sceneSpot.z), radius);
            sceneSpot.x = pair.getLeft().longValue();
            sceneSpot.z = pair.getRight().longValue();
        }
        return sceneSpot;
    }

    public int getRadius() {
        return radius;
    }

    public void setRadius(int radius) {
        this.radius = radius;
    }
}

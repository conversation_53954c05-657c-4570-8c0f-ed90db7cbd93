package com.gy.server.scene.map.move;

import com.gy.server.game.player.Player;
import com.gy.server.packet.PbScene;
import com.gy.server.scene.divisionLine.DivisionLine;
import com.gy.server.scene.map.bean.ScenePlayerInfo;

/**
 * 场景移动标记（相同标记才会广播移动通知）
 * <AUTHOR> - [Created on 2023-08-03 10:41]
 */
public enum SceneWalkMark {

    //通用(默认)
    simple(0, (playerInfo1, playerInfo2) -> {
        //同一分线，行动标记一样，同服
        return playerInfo1.getMark() == playerInfo2.getMark()
                && Player.getServerId(playerInfo1.getPlayerId()) == Player.getServerId(playerInfo2.getPlayerId())
                ;
    }),
    //凤凰古城
    leagueMelee(1, (playerInfo1, playerInfo2) -> {
        //同一分线，行动标记一样，跨服
        return playerInfo1.getMark() == playerInfo2.getMark()
                && playerInfo1.getRoomId() == playerInfo2.getRoomId()
                ;
    }),

    //武林悬赏
    worldBoss(2, (playerInfo1, playerInfo2) -> {
        //同一分线，行动标记一样,同房间
        return playerInfo1.getMark() == playerInfo2.getMark()
                && playerInfo1.getRoomId() > 0
                && playerInfo1.getRoomId() == playerInfo2.getRoomId()
                ;
    }),
    //联盟
    league(3, (playerInfo1, playerInfo2) -> {
        return playerInfo1.getMark() == playerInfo2.getMark()
                && playerInfo1.getRoomId() > 0
                && playerInfo1.getRoomId() == playerInfo2.getRoomId();
    }),

    //凤凰古城-据点
    leagueMeleeCity(4, (playerInfo1, playerInfo2) -> {
        //同一分线，行动标记一样，跨服
        return playerInfo1.getMark() == playerInfo2.getMark()
                && playerInfo1.getRoomId() == playerInfo2.getRoomId()
                ;
    }),

    //情缘
    marryScene(5, (playerInfo1, playerInfo2) -> {
        //同一分线，行动标记一样，跨服
        return playerInfo1.getMark() == playerInfo2.getMark()
                && playerInfo1.getRoomId() == playerInfo2.getRoomId()
                ;
    }),
    ;

    public int mark;
    public SceneWalkMarkCheck walkCheck;

    SceneWalkMark(int mark, SceneWalkMarkCheck walkCheck) {
        this.mark = mark;
        this.walkCheck = walkCheck;
    }

    public int getMark() {
        return mark;
    }

    public static SceneWalkMark getSceneWalkMark(PbScene.SceneMark mark){
        switch (mark.getNumber()){
            case 0: return simple;
            case 1: return leagueMelee;
            case 2: return worldBoss;
            case 3: return league;
            case 4: return leagueMeleeCity;
        }
        return simple;
    }

    public boolean sceneWalkMarkCheck(ScenePlayerInfo playerInfo1, ScenePlayerInfo playerInfo2){
        return walkCheck.check(playerInfo1, playerInfo2);
    }

    private static interface SceneWalkMarkCheck{
        public boolean check(ScenePlayerInfo playerInfo1, ScenePlayerInfo playerInfo2);
    }
}

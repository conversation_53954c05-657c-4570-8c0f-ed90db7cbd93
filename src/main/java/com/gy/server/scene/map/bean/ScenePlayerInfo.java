package com.gy.server.scene.map.bean;

import java.io.Serializable;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

import com.baidu.bjf.remoting.protobuf.annotation.Ignore;
import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.player.Player;
import com.gy.server.packet.PbScene;
import com.gy.server.scene.map.move.SceneWalkMark;
import com.ttlike.server.tl.baselib.serialize.scene.ScenePosition3D;

/**
 * 场景玩家信息
 * <AUTHOR> - [Created on 2022-03-29 18:24]
 */
public class ScenePlayerInfo implements Serializable {

    public final static long DISCONNECT_TIME = 5 * 60 * 1000L;
    @Protobuf(order = 1)
    private long playerId;
    @Protobuf(order = 2)
    private ScenePosition3D position;
    @Protobuf(order = 3)
    private int seed;
    @Protobuf(order = 4)
    private ScenePosition3D targetPosition;
    @Protobuf(order = 5)
    private SceneMapType mapType;
    @Protobuf(order = 6)
    private float direction;//方向
    @Protobuf(order = 7)
    private String name;
    @Protobuf(order = 8)
    private int protagonistId;//主角id
    @Ignore
    private SceneWalkMark mark;//场景移动标记
    @Protobuf(order = 9)
    private int heroTitleId;//头衔ID
    @Protobuf(order = 10)
    private int designationId;//称号ID
    @Protobuf(order = 11)
    private int artifactEquipLevel;//神器穿戴等级
    /**
     * 标记，不同功能下为不同内容
     * 例如
     *   武林悬赏时为房间id
     *   凤凰古城时为场景的标记 组/组 * 10000 + 据点id
     */
    @Protobuf(order = 12)
    private long roomId;

    @Protobuf(order = 13)
    private long lastActiveTime;

    @Protobuf(order = 14)
    private long clientId;

    //内存数据,玩家在各个地图的位置
    @Ignore
    private Map<SceneMapType, ScenePosition3D> sceneMapHistoryPosInfos = new ConcurrentHashMap<>();


    public boolean checkConnection() {
        if (ServerConstants.getCurrentTimeMillis() - this.getLastActiveTime() > DISCONNECT_TIME) {
            //连接失效，返回可以踢掉玩家了
            return false;
        }
        return true;
    }

    public Map<SceneMapType, ScenePosition3D> getSceneMapHistoryPosInfos() {
        return sceneMapHistoryPosInfos;
    }

    public long getLastActiveTime() {
        return lastActiveTime;
    }

    public void setLastActiveTime(long lastActiveTime) {
        this.lastActiveTime = lastActiveTime;
    }

    public void setSceneMapHistoryPosInfos(Map<SceneMapType, ScenePosition3D> sceneMapHistoryPosInfos) {
        this.sceneMapHistoryPosInfos = sceneMapHistoryPosInfos;
    }

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public ScenePosition3D getPosition() {
        return position;
    }

    public void setPosition(ScenePosition3D position) {
        this.position = position;
    }

    public int getSeed() {
        return seed;
    }

    public void setSeed(int seed) {
        this.seed = seed;
    }

    public ScenePosition3D getTargetPosition() {
        return targetPosition;
    }

    public void setTargetPosition(ScenePosition3D targetPosition) {
        this.targetPosition = targetPosition;
    }

    public SceneMapType getMapType() {
        return mapType;
    }

    public void setMapType(SceneMapType mapType) {
        this.mapType = mapType;
    }

    public float getDirection() {
        return direction;
    }

    public void setDirection(float direction) {
        this.direction = direction;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getProtagonistId() {
        return protagonistId;
    }

    public void setProtagonistId(int protagonistId) {
        this.protagonistId = protagonistId;
    }

    public SceneWalkMark getMark() {
        return mark;
    }

    public void setMark(SceneWalkMark mark) {
        this.mark = mark;
    }

    public int getHeroTitleId() {
        return heroTitleId;
    }

    public void setHeroTitleId(int heroTitleId) {
        this.heroTitleId = heroTitleId;
    }

    public int getDesignationId() {
        return designationId;
    }

    public void setDesignationId(int designationId) {
        this.designationId = designationId;
    }

    public int getArtifactEquipLevel() {
        return artifactEquipLevel;
    }

    public void setArtifactEquipLevel(int artifactEquipLevel) {
        this.artifactEquipLevel = artifactEquipLevel;
    }

    public long getRoomId() {
        return roomId;
    }

    public void setRoomId(long roomId) {
        this.roomId = roomId;
    }

    public PbScene.ScenePlayer genPb(){
        PbScene.ScenePlayer.Builder playerBuilder = PbScene.ScenePlayer.newBuilder();
        playerBuilder.setPlayerId(playerId);
        playerBuilder.setSpeed(seed);
        playerBuilder.setPosition(PbScene.ScenePosition.newBuilder().setX((int)(position.getX() * 1000F)).setY((int)(position.getY() * 1000F)).setZ((int)(position.getZ() * 1000F)).build());
        if(Objects.nonNull(targetPosition)){
            playerBuilder.setTargetPosition(PbScene.ScenePosition.newBuilder().setX((int)(targetPosition.getX() * 1000F)).setY((int)(targetPosition.getY() * 1000F)).setZ((int)(targetPosition.getZ() * 1000F)).build());
        }
        playerBuilder.setDirection((int)direction);
        playerBuilder.setName(name);
        playerBuilder.setProtagonistId(protagonistId);
        playerBuilder.setHeroTitleId(heroTitleId);
        playerBuilder.setDesignationId(designationId);
        playerBuilder.setArtifactEquipLevel(artifactEquipLevel);
        playerBuilder.setAction(PbScene.SceneAction.none);
        return playerBuilder.build();
    }

    public static ScenePlayerInfo newPlayer(Player player){
        ScenePlayerInfo playerInfo = new ScenePlayerInfo();
        playerInfo.setPlayerId(player.getPlayerId());
        playerInfo.setName(player.getName());
        playerInfo.setDirection(1F);
        playerInfo.setProtagonistId(player.getTemplate().id);
        playerInfo.setHeroTitleId(player.getRoleModel().getHeroTitleId());
        playerInfo.setDesignationId(player.getRoleModel().getDesignationId());
        playerInfo.setArtifactEquipLevel(player.getArtifactModel().getEquipGrade());
        playerInfo.setClientId(player.getClientId());
        return playerInfo;
    }

    public long getClientId() {
        return clientId;
    }

    public void setClientId(long clientId) {
        this.clientId = clientId;
    }
}

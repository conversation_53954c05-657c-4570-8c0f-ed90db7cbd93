package com.gy.server.scene.map;

import com.gy.server.game.player.Player;
import com.gy.server.packet.PbProtocol;
import com.gy.server.scene.divisionLine.DivisionLine;
import com.gy.server.scene.divisionLine.DivisionLineManager;
import com.gy.server.scene.divisionLine.DivisionLineWorker;
import com.gy.server.scene.map.bean.SceneMapType;
import com.gy.server.scene.map.bean.SceneNode;
import com.gy.server.scene.map.bean.ScenePlayerInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 场景服地图信息管理类
 * <AUTHOR> - [Created on 2022-03-28 13:57]
 */
public class SceneMapManager {

    /**
     * 场景服玩家信息map
     * key:玩家id     value:key:场景地图类型 value:场景服玩家信息
     */
    private static volatile Map<Long, ScenePlayerInfo> playerInfos = new ConcurrentHashMap<>();

    public static ScenePlayerInfo getPlayerInfo(long playerId){
        return playerInfos.get(playerId);
    }

    public static void addPlayerInfo(ScenePlayerInfo playerInfo){
        playerInfos.put(playerInfo.getPlayerId(), playerInfo);
    }

    public static void removePlayerInfo(long playerId){
        playerInfos.remove(playerId);
    }


    public static Map<Long, ScenePlayerInfo> getPlayerInfos() {
        return playerInfos;
    }

    /**
     * 矫正玩家位置信息
     * @param sceneMapType 场景类型
     * @param playerId 玩家id
     * @param curNode 所处地块
     */
    public static void correctPlayerPosition(SceneMapType sceneMapType, long playerId, SceneNode curNode){
        PbProtocol.SceneLeaveViewNotify build = PbProtocol.SceneLeaveViewNotify.newBuilder().addPlayerId(playerId).build();
        DivisionLine line = DivisionLineManager.getInstance().getDivisionLine(sceneMapType, playerId);
        if(Objects.nonNull(line)){
            List<Long> otherPlayerIds = new ArrayList<>();
            for (SceneNode[] sceneNodes : line.getSceneNodes()) {
                for (SceneNode sceneNode : sceneNodes) {
                    if(Objects.isNull(curNode) || sceneNode != curNode){
                        sceneNode.removePlayerId(playerId);
                        otherPlayerIds.addAll(sceneNode.getAllPlayerIds());
                    }
                }
            }
        }
        //暂时不用通知，切地块会变化
//        if(CollectionUtil.isNotEmpty(otherPlayerIds)){
//            SceneMessageBroadcaster.getInstance().broadcastMsg(otherPlayerIds, PtCode.SCENE_LEAVE_NOTIFY, build, playerId);
//        }
    }

    /**
     * 移除场景内所有玩家
     * @param sceneMapType 场景类型
     * @param serverId 服务器id
     */
    public static void removeAllPlayer(SceneMapType sceneMapType, int serverId){
        List<Integer> allServer = Player.getAllServer(serverId);
        DivisionLineWorker divisionWorker = DivisionLineManager.getInstance().getDivisionWorker(sceneMapType);
        for (DivisionLine divisionLine : divisionWorker.getDivisionLineInfos().values()) {
            for (SceneNode[] sceneNodes : divisionLine.getSceneNodes()) {
                for (SceneNode sceneNode : sceneNodes) {
                    for (int sId : allServer) {
                        sceneNode.removePlayerId(sId);
                    }
                }
            }
        }
    }

}

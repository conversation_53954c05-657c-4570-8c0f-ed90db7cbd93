package com.gy.server.scene.enter;

import com.google.protobuf.AbstractMessage;
import com.gy.server.annotation.MessageMethod;
import com.gy.server.annotation.MessageServiceBean;
import com.gy.server.common.gateway.GateNodeManager;
import com.gy.server.core.MessageServerType;
import com.gy.server.core.MethodInvokeType;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.command.CommandRequestParams;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.core.log.CommonLogger;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.role.RoleInfoType;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbScene;
import com.gy.server.scene.base.SceneMessageBroadcaster;
import com.gy.server.scene.divisionLine.DivisionLine;
import com.gy.server.scene.divisionLine.DivisionLineManager;
import com.gy.server.scene.divisionLine.DivisionLineWorker;
import com.gy.server.scene.map.SceneMapManager;
import com.gy.server.scene.map.SceneMapService;
import com.gy.server.scene.map.bean.*;
import com.gy.server.scene.map.move.ASceneWalk;
import com.gy.server.scene.map.move.SceneWalkMark;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.ttlike.server.tl.baselib.serialize.leagueMelee.PlayerPositionMap;
import com.ttlike.server.tl.baselib.serialize.scene.ScenePosition3D;
import com.ttlike.server.tl.baselib.util.GroovyExecutor;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 场景服操作协议接口
 * <AUTHOR> - [Created on 2022-03-16 16:50]
 */
@MessageServiceBean(description = "场景服操作相关", messageServerType = MessageServerType.scene)
public class SceneOperationCommandService {

    @MessageMethod(description = "进入场景", invokeType = MethodInvokeType.free)
    public static void enterScene(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        int serverId = params.getParam(1);
        long time = params.getParam(2);
        SceneWalkMark walkMark = params.getParam(3);
        ScenePlayerInfo playerInfo = params.getParam(4);
        long roomId = params.getParam(5);
        String mapTypeId = params.getParam(6);
        SceneMapType mapType = SceneMapType.valueOf(mapTypeId);
        ScenePosition3D scenePosition = params.getParam(7);

        PbProtocol.SceneEnterRst.Builder rst = PbProtocol.SceneEnterRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{

            try {
                //地图信息
                SceneMapInfo sceneMapInfo = SceneMapService.getSceneMapInfo(mapType);

                //获取上次位置
                //优先功能传进来的位置
                ScenePlayerInfo oldPlayerInfo = SceneMapManager.getPlayerInfo(playerId);
                if(Objects.isNull(scenePosition)){
                    if(Objects.nonNull(oldPlayerInfo) && oldPlayerInfo.getSceneMapHistoryPosInfos().containsKey(mapType)){
                        //上次离开位置
                        scenePosition = oldPlayerInfo.getSceneMapHistoryPosInfos().get(mapType);
                    }else{
                        //初始位置
                        final SceneSpot sceneSpot = sceneMapInfo.randomBorn();
                        scenePosition = sceneSpot.convertScenePosition3D();
                    }
                }
                if(Objects.nonNull(oldPlayerInfo)){
                    playerInfo.setSceneMapHistoryPosInfos(oldPlayerInfo.getSceneMapHistoryPosInfos());
                }

                //更新玩家信息
                playerInfo.setPosition(scenePosition);
                playerInfo.setMark(walkMark);
                //更新场景标记
                playerInfo.setMapType(mapType);
                playerInfo.setLastActiveTime(ServerConstants.getCurrentTimeMillis());
                playerInfo.setDirection(sceneMapInfo.getFace());
                playerInfo.setRoomId(roomId);
                SceneMapManager.addPlayerInfo(playerInfo);
                //分线
                DivisionLine line = DivisionLineManager.getInstance().allocateLine(mapType, playerId);

                //进入地块
                SceneNode node = SceneMapInfo.findNode(playerInfo, scenePosition);
                if(Objects.isNull(node)){
                    rst.setResult(Text.genServerRstInfo(Text.场景位置异常));
                    break logic;
                }
                node.addPlayerId(playerId);
                //为了防止模拟器掉线收不到下线通知，先清除地块中的信息
                //矫正地块位置玩家信息
                SceneMapManager.correctPlayerPosition(mapType, playerId, node);

                //九宫格地块
                List<SceneNode> nineNode = SceneMapService.getNinNode(playerInfo, scenePosition);
                //筛选九宫格内本线的其他玩家id
                List<Long> otherPlayerIds = new ArrayList<>();
                for (SceneNode sceneNode : nineNode) {
                    for (long nodePlayerId : sceneNode.getPlayerIds(serverId)) {
                        if(Objects.nonNull(line) && line.getPlayerIds().contains(nodePlayerId)){
                            otherPlayerIds.add(nodePlayerId);
                        }
                    }
                }
                //去除本身
                otherPlayerIds.remove((Long)playerId);
                otherPlayerIds = ASceneWalk.linePlayerFilter(mapType, playerId, otherPlayerIds);
                //广播玩家进入场景
                if(CollectionUtil.isNotEmpty(otherPlayerIds)){
                    PbProtocol.SceneEnterViewNotify.Builder notifyMsg = PbProtocol.SceneEnterViewNotify.newBuilder();
                    notifyMsg.addPlayer(playerInfo.genPb());
                    SceneMessageBroadcaster.getInstance().broadcastMsg(otherPlayerIds, PtCode.SCENE_ENTER_NOTIFY, notifyMsg.build(), playerInfo.getPlayerId());
                    for (long otherPlayerId : otherPlayerIds) {
                        ScenePlayerInfo otherPlayerInfo = SceneMapManager.getPlayerInfo(otherPlayerId);
                        if(Objects.nonNull(otherPlayerInfo)){
                            rst.addPlayers(otherPlayerInfo.genPb());
                        }
                    }
                }

                //构建场景玩家信息
                rst.setMyself(playerInfo.genPb());
                //获得显示分线id
                int showLineId = 0;
                DivisionLineWorker lineWorker = DivisionLineManager.getInstance().getDivisionWorker(playerInfo.getMapType());
                List<DivisionLine> allLine = lineWorker.getDivisionLineInfos()
                        .values()
                        .stream()
                        .filter(w -> w.getStatus() == DivisionLine.Status_Run)
                        .sorted(Comparator.comparingInt(DivisionLine::getLineId))
                        .collect(Collectors.toList());
                for (int i = 0; i < allLine.size(); i++) {
                    if(allLine.get(i).getLineId() == line.getLineId()){
                        showLineId = i + 1;
                        break;
                    }
                }
                rst.setLineInfo(DivisionLineManager.genLineWorker(showLineId, line));
            }catch (Exception e){
                e.printStackTrace();
            }
        }
        request.addCallbackParam(playerId);
        request.addCallbackParam(time);
        request.addCallbackParam(rst.build().toByteArray());

    }

    @MessageMethod(description = "玩家下线", invokeType = MethodInvokeType.free)
    public static void logout(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        for (SceneMapType mapType : SceneMapType.values()) {
            ASceneWalk.removePlayerInNode(playerId, mapType);
        }
    }

    @MessageMethod(description = "获取当前场景坐标", invokeType = MethodInvokeType.free)
    public static void getPosition(ServerCommandRequest request, CommandRequestParams params) {
        List<Long> playerList = params.getParam(0);
        Map<Long, ScenePosition3D> position3DMap = new HashMap<>();
        for (Long playerId : playerList) {
            ScenePlayerInfo playerInfo = SceneMapManager.getPlayerInfo(playerId);
            if (Objects.isNull(playerInfo)) {
                continue;
            }
            //获得位置信息
            ScenePosition3D scenePosition = playerInfo.getPosition();
            if (Objects.isNull(scenePosition)) {
                continue;
            }
            position3DMap.put(playerId, scenePosition);
        }
        PlayerPositionMap playerPositionMap = new PlayerPositionMap();
        playerPositionMap.setPosition3DMap(position3DMap);
        request.addCallbackParam(PbUtilCompress.encode(playerPositionMap));
    }

    @MessageMethod(description = "修改场景上玩家信息", invokeType = MethodInvokeType.free)
    public static void modifyScenePlayerInfo(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        ScenePlayerInfo playerInfo = SceneMapManager.getPlayerInfo(playerId);
        if(Objects.nonNull(playerInfo)){
            int type = params.getParam(1);
            if(type == RoleInfoType.heroTitle){//头衔
                int titleId = params.getParam(2);
                playerInfo.setHeroTitleId(titleId);
            }else if(type == RoleInfoType.designation){//称号
                int designationId = params.getParam(2);
                playerInfo.setDesignationId(designationId);
            }else if(type == RoleInfoType.equipGrade){//神器穿戴等级
                int artifactEquipLevel = params.getParam(2);
                playerInfo.setArtifactEquipLevel(artifactEquipLevel);
            }

            PbScene.ScenePlayer.Builder scenePlayer = playerInfo.genPb().toBuilder();
            //因为动作不需要存到ScenePlayerInfo身上，所以构建出ScenePlayer再修改信息
            if(type == RoleInfoType.sceneAction){
                PbScene.SceneAction sceneAction = params.getParam(2);
                scenePlayer.setAction(sceneAction);
            }

            ASceneWalk.syncPlayerInfo(playerInfo, scenePlayer.build());
        }

    }

    @MessageMethod(description = "修改玩家目标点", invokeType = MethodInvokeType.free)
    public static void notifyScenePosition(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        ScenePosition3D position3D = params.getParam(1);

        ScenePlayerInfo playerInfo = SceneMapManager.getPlayerInfo(playerId);
        if(Objects.nonNull(playerInfo)){
            playerInfo.setPosition(position3D);
            playerInfo.setTargetPosition(position3D);
            //广播
            ASceneWalk.walkLogic(playerInfo, position3D, position3D, playerInfo.getSeed(), 1);
        }
    }

    /**
     * 批量执行groovy脚本
     */
    @MessageMethod(description = "批量执行groovy脚本", invokeType = MethodInvokeType.async)
    private static void sceneBatchExecuteGroovy(ServerCommandRequest request, CommandRequestParams params) {
        String code = params.getParam(0);
        CommonLogger.info("execute groovy script success.");
        // 回调返回
        request.addCallbackParam(GroovyExecutor.executeNotNeedResultInfo(code));
    }

    @MessageMethod(description = "获取场景在线玩家", invokeType = MethodInvokeType.free)
    public static void getScenePlayerList(ServerCommandRequest request, CommandRequestParams params){
        SceneMapType type = params.getParam(0);
        List<Long> list = new ArrayList<>();
        for (ScenePlayerInfo playerInfo : SceneMapManager.getPlayerInfos().values()) {
            if(playerInfo.getMapType() == type){
                list.add(playerInfo.getPlayerId());
            }
        }
        request.addCallbackParam(list);
    }

    @MessageMethod(description = "消息广播", invokeType = MethodInvokeType.free)
    public static void msgNotify(ServerCommandRequest request, CommandRequestParams params){
        SceneMapType mapType = params.getParam(0);
        int ptCode = params.getParam(1);
        AbstractMessage message = params.getParam(2);
        int serverId = params.getParam(3);
        Set<Long> playerIds = new HashSet<>();
        for (ScenePlayerInfo playerInfo : SceneMapManager.getPlayerInfos().values()) {
            long targetPlayerId = playerInfo.getPlayerId();
            if(playerInfo.getMapType() == mapType && (serverId <= 0 || serverId == Player.getRealServerId(targetPlayerId))){
                playerIds.add(targetPlayerId);
            }
        }
        GateNodeManager.getInstance().broadcast(playerIds, ptCode, message);
    }


}

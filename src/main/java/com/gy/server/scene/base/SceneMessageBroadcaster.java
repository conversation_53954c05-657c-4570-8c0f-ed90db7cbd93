package com.gy.server.scene.base;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;

import com.google.protobuf.AbstractMessage;
import com.gy.server.common.gateway.GateNodeManager;
import com.gy.server.core.thread.AbstractRunner;
import com.gy.server.packet.PbPacket;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.CollectionUtil;

import org.apache.commons.lang3.tuple.Pair;

/**
 * 场景服消息广播器
 * <AUTHOR> - [Created on 2022-04-12 9:53]
 */
public class SceneMessageBroadcaster extends AbstractRunner {

    //广播频率（帧）
    private static final long broadcast_rate = 300L;


    private List<SceneMessageBroadcastInfo> notifyMerges = new CopyOnWriteArrayList<>();

    public void broadcastMsg(List<Long> broadcastPlayerIds, int ptCode, AbstractMessage message, Long... sendPlayerIds){
        notifyMerges.add(new SceneMessageBroadcastInfo(ptCode, broadcastPlayerIds, message, sendPlayerIds));
    }

    @Override
    protected void subRunnerExecute() throws Exception {
        if(CollectionUtil.isNotEmpty(notifyMerges)){
            //获取单位时间内广播消息
            List<SceneMessageBroadcastInfo> allInfos = new ArrayList<>(notifyMerges);
            notifyMerges.clear();
            //收集消息
            //key:接受消息玩家id， key:发送玩家id， value:最新消息
            Map<Long, Map<Long, Pair<Integer, AbstractMessage>>> allMessages = new HashMap<>();
            for (SceneMessageBroadcastInfo info : allInfos) {
                for (Long sendPlayerId : info.sendPlayerIds) {
                    for (Long broadcastPlayerId : info.broadcastPlayerIds) {
                        if(!allMessages.containsKey(broadcastPlayerId)){
                            allMessages.put(broadcastPlayerId, new HashMap<>());
                        }
                        //直接替换消息
                        allMessages.get(broadcastPlayerId).put(sendPlayerId, Pair.of(info.ptCode, info.message));
                    }
                }
            }
            //合并消息
            PbPacket.Packet.Builder packet = PbPacket.Packet.newBuilder();
            PbProtocol.SceneBroadcastNotify.SceneBroadcastInfo.Builder builder = PbProtocol.SceneBroadcastNotify.SceneBroadcastInfo.newBuilder();
            PbProtocol.SceneBroadcastNotify.Builder notify = PbProtocol.SceneBroadcastNotify.newBuilder();
            for (Long broadcastPlayerId : allMessages.keySet()) {
                builder.clear();
                Map<Long, Pair<Integer, AbstractMessage>> message = allMessages.get(broadcastPlayerId);
                for (Long sendPlayerId : message.keySet()) {
                    Pair<Integer, AbstractMessage> abstractMessages = message.get(sendPlayerId);
                    packet.clear();
                    packet.setPtCode(abstractMessages.getKey());
                    packet.setTimeStamp(System.currentTimeMillis());
                    packet.setData(abstractMessages.getValue().toByteString());
                    builder.setPacket(packet.build());
                }
                builder.addPlayerIds(broadcastPlayerId);
                notify.addInfos(builder.build());
            }

            //开始发送消息
            GateNodeManager.getInstance().broadcastMsg(notify.build());
        }
    }

    @Override
    public long getRunnerInterval() {
        return broadcast_rate;
    }

    @Override
    public String getRunnerName() {
        return "SceneMessageBroadcaster";
    }

    private static final SceneMessageBroadcaster instance = new SceneMessageBroadcaster();
    SceneMessageBroadcaster(){}
    public static SceneMessageBroadcaster getInstance(){
        return instance;
    }
}

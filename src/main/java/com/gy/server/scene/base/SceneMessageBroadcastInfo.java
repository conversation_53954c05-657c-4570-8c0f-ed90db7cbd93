package com.gy.server.scene.base;

import java.util.List;

import com.google.protobuf.AbstractMessage;
import com.gy.server.packet.PbProtocol;

/**
 * 单个广播消息内容
 * <AUTHOR> - [Created on 2022-04-12 10:21]
 */
public class SceneMessageBroadcastInfo {
    public List<Long> broadcastPlayerIds;
    public AbstractMessage message;
    public Long[] sendPlayerIds;
    public int ptCode;

    public SceneMessageBroadcastInfo(int ptCode
            , List<Long> broadcastPlayerIds
            , AbstractMessage message
            , Long... sendPlayerIds){
        this.broadcastPlayerIds = broadcastPlayerIds;
        this.message = message;
        this.sendPlayerIds = sendPlayerIds;
        this.ptCode = ptCode;
    }
}

package com.gy.server.scene;

import com.gy.server.common.NodeStatusSyncRunner;
import com.gy.server.common.gateway.Game2GateStatusSyncManager;
import com.gy.server.common.gateway.GateNodeManager;
import com.gy.server.core.*;
import com.gy.server.core.delay.DelayTaskManager;
import com.gy.server.core.delay.MessageSystemHashInvoke;
import com.gy.server.core.delay.MessageSystemSyncInvoke;
import com.gy.server.core.log.CommonLogger;
import com.gy.server.core.thread.ForkJoinThreadPool;
import com.gy.server.core.thread.SimpleHashedThreadPool;
import com.gy.server.core.thread.SimpleRandomThreadPool;
import com.gy.server.db.nosql.redis.statistics.RedisUtilStatisticsManager;
import com.gy.server.game.combat.CombatManager;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.handler.HandlerManager;
import com.gy.server.game.service.ServiceManager;
import com.gy.server.game.time.TimeManager;
import com.gy.server.net.netty.ChannelHandlerContextFlusher;
import com.gy.server.net.netty.NettyTcpServer;
import com.gy.server.net.netty.codec.PacketCodecFactory;
import com.gy.server.packet.PbPacket;
import com.gy.server.scene.band.SceneBandManager;
import com.gy.server.scene.base.SceneMessageBroadcaster;
import com.gy.server.scene.divisionLine.DivisionLineManager;
import com.gy.server.scene.map.ScenePlayerStatusTicker;
import com.gy.server.utils.runner.RunnerManager;
import com.gy.server.utils.structure.TickerWatcher;
import com.ttlike.server.tl.baselib.CommonsConfiguration;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

/**
 * 场景服
 * <AUTHOR> - [Created on 2022-03-16 13:25]
 */
public class SceneServer extends MainThread {

    private NettyTcpServer tcpServer;

    @Override
    protected void init() throws Exception {
        //TLBase初始化
        TLBase.getInstance().init(Configuration.serverId, ServerType.SCENE, "scene", Configuration.startupMode.name(), MessageSystemSyncInvoke.getInstance(), MessageSystemHashInvoke.getInstance());
        Configuration.setRunMode();

        Configuration.initBillingUrl();
        Launcher.launchStartupInfo("SceneServer is initializing");

        waitStartup();

        //服务器常量
        Launcher.launchStartupInfo("ServerConstants init");
        ServerConstants.init();

        //db相关初始化
//        Launcher.launchStartupInfo("DbManager init");
//        DbManager.init();


        //快速tick
        addFastTicker(GateNodeManager.getInstance());
        addFastTicker(CombatManager.getInstance());

        //慢速tick
        addSlowTicker(RunnerManager.getInstance());
        addSlowTicker(DelayTaskManager.getInstance());
        addSlowTicker(ElapsedTimeStatistics.getInstance());
        addSlowTicker(TimeManager.getInstance());
        addSlowTicker(ScenePlayerStatusTicker.getInstance());

        //注册service
        Launcher.launchStartupInfo("ServiceManager init");
        ServiceManager.init();

        // 协议号管理器
        Launcher.launchStartupInfo("HandlerManager init");
        HandlerManager.getInstance().init();

        Launcher.launchStartupInfo("RedisUtilStatisticsManager init");
        RedisUtilStatisticsManager.getInstance().init();

        Launcher.launchStartupInfo("SceneBandManager init");
        SceneBandManager.getInstance().init();

        Launcher.launchStartupInfo("SceneServer is initialized");
    }

    @Override
    protected void startup() throws Exception {
        Launcher.launchStartupInfo("SceneServer is starting");

        //service前置启动
        Launcher.launchStartupInfo("ServiceManager pre startup");
        ServiceManager.preStartup();

        //service启动
        Launcher.launchStartupInfo("ServiceManager startup");
        ServiceManager.startup();

        //全局数据管理器启动
        Launcher.launchStartupInfo("GlobalDataManager startup");
        GlobalDataManager.startup();

        //时间管理器启动
//        Launcher.launchStartupInfo("TimeManager startup");
//        TimeManager.startup();


        Launcher.launchStartupInfo("HashedThreadPool startup");
        SimpleHashedThreadPool.getInstance().startup();
        SimpleRandomThreadPool.getInstance().startup();

        //独立线程启动
        RunnerManager.addRunner(ForkJoinThreadPool.getInstance(), ForkJoinThreadPool.getInstance().getRunnerName(), false);
        RunnerManager.addRunner(ChannelHandlerContextFlusher.getInstance(), "ChannelHandlerContextFlusher", false);
        RunnerManager.addRunner(TickerWatcher.getInstance(), "TickerWatcher", true);
        RunnerManager.addRunner(Game2GateStatusSyncManager.getInstance(), Game2GateStatusSyncManager.getInstance().getRunnerName(), false);
        RunnerManager.addRunner(SceneBandManager.getInstance(), SceneBandManager.getInstance().getRunnerName(), false);
        RunnerManager.addRunner(DivisionLineManager.getInstance(), DivisionLineManager.getInstance().getRunnerName(), false);
        RunnerManager.addRunner(SceneMessageBroadcaster.getInstance(), SceneMessageBroadcaster.getInstance().getRunnerName(), false);
        RunnerManager.addRunner(RedisUtilStatisticsManager.getInstance(), RedisUtilStatisticsManager.class.getSimpleName(), false);
        RunnerManager.addRunner(new NodeStatusSyncRunner(), NodeStatusSyncRunner.class.getSimpleName(), false);

        //线程启动
        this.start();
        Launcher.launchStartupInfo("MainThread start");

        //游客接待室打开
        Launcher.launchStartupInfo("GuestManager start");
        GateNodeManager.getInstance().setAccept(true);

        //启动tcp服务
        tcpServer = NettyTcpServer.startServer(Configuration.tcpPort, PbPacket.SSPacket.getDefaultInstance(), GateNodeManager.getInstance(), PacketCodecFactory.common);
        Launcher.launchStartupInfo("TCP Server startup, listen port:" + Configuration.tcpPort);

        Launcher.launchStartupInfo("SceneServer is started");

        //输出启动成功：http://patorjk.com/software/taag，当前使用字体：Doom
        System.out.println(
                " ___  ___ ___ _ __   ___     ___  ___ _ ____   _____ _ __ \n" +
                "/ __|/ __/ _ \\ '_ \\ / _ \\   / __|/ _ \\ '__\\ \\ / / _ \\ '__|\n" +
                "\\__ \\ (_|  __/ | | |  __/   \\__ \\  __/ |   \\ V /  __/ |   \n" +
                "|___/\\___\\___|_| |_|\\___|   |___/\\___|_|    \\_/ \\___|_|   \n" +
                "                                                          \n");
    }

    @Override
    protected void shutdown() throws Exception {

        Launcher.launchShutdownInfo("SceneManager start shutdown");
        running = false;

        while (true) {
            if (shutdown
                    && ThreadPool.getInstance().getSize() < 1) {
                //rpc预关闭
                TLBase.getInstance().preShutdown();
                Launcher.launchShutdownInfo("TLBase preShutdown");
                //游客接待室关闭
                GateNodeManager.getInstance().setAccept(false);
                Launcher.launchShutdownInfo("GateNodeManager shutdown");
                // service关闭
                ServiceManager.shutdown();
                Launcher.launchShutdownInfo("ServiceManager shutdown");

                // 独立线程关闭
                RunnerManager.shutdown();
                Launcher.launchShutdownInfo("RunnerManager shutdown");

                //关闭tlbase
                TLBase.getInstance().shutdown();
                Launcher.launchShutdownInfo("TLBase shutdown");

                //HashedThreadPool关闭
                SimpleHashedThreadPool.getInstance().shutdown();
                SimpleRandomThreadPool.getInstance().shutdown();
                Launcher.launchShutdownInfo("HashedThreadPool shutdown");

//                // db关闭
//                DbManager.shutdown();
//                Launcher.launchShutdownInfo("DB shutdown");

                //tcp服务关闭
                tcpServer.shutdown();
                Launcher.launchShutdownInfo("TcpServer shutdown");

                break;
            }

            Thread.sleep(100);
        }
        Launcher.launchShutdownInfo("SceneManager end shutdown");
        System.out.println("                                  _              \n" +
                "                                 | |             \n" +
                " ___  ___ ___ _ __   ___      ___| |_ ___  _ __  \n" +
                "/ __|/ __/ _ \\ '_ \\ / _ \\    / __| __/ _ \\| '_ \\ \n" +
                "\\__ \\ (_|  __/ | | |  __/    \\__ \\ || (_) | |_) |\n" +
                "|___/\\___\\___|_| |_|\\___|    |___/\\__\\___/| .__/ \n" +
                "                                          | |    \n" +
                "                                          |_|  ");
    }


    private void waitStartup() {
        while (this.checkNeedWaitStartup()) {
            try {
                Thread.sleep(10000);
            } catch (Exception e) {
                CommonLogger.error(e);
            }
        }
    }

    private boolean checkNeedWaitStartup() {
        if (CommonsConfiguration.getAllSceneServerIds().contains(Configuration.serverId)) {
            return false;
        }
        CommonLogger.info(String.format("scene服务器id没有配置，请检查配置文件或者在gm后台添加，服务器id：%s", Configuration.serverId));
        return true;
    }

}

package com.gy.server.scene.divisionLine;

import com.gy.server.common.gateway.GateNode;
import com.gy.server.common.gateway.GateNodeManager;
import com.gy.server.common.gateway.enums.GameDealPacket;
import com.gy.server.core.thread.AbstractRunner;
import com.gy.server.game.handler.HandlerManager;
import com.gy.server.game.packet.PtCode;
import com.gy.server.packet.PbPacket;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbScene;
import com.gy.server.scene.map.SceneMapManager;
import com.gy.server.scene.map.SceneMapService;
import com.gy.server.scene.map.bean.*;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.runner.LongRunner;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 场景服分线管理器
 * <AUTHOR> - [Created on 2022-04-09 15:08]
 */
public class DivisionLineManager extends AbstractRunner implements LongRunner {

    //单线最多玩家数量
    public static final int max_singleLine_player_num = 500;
    //单服可进入的玩家数量 （max_singleLine_player_num * 70%）
    private static final int max_singleLine_canEnter_player_num = max_singleLine_player_num * 70 / 100;
    //可以切线最大玩家（max_singleLine_player_num * 85%）
    public static final int max_change_line_player_num = max_singleLine_player_num / 100 * 85;

    //分线工作器
    private Map<SceneMapType, DivisionLineWorker> lineWorkers = new ConcurrentHashMap<>();

    /**
     * 分线处理
     */
    public void divisionLine(long playerId, GateNode gateNode, PbPacket.Packet packet, long clientId){
        DivisionLineWorker.addTask(()->{
            gateNode.getConnection().setCurrentPacket(packet);
            HandlerManager.getInstance().handlePacket(gateNode, packet, packet.getPtCode(), packet.getTimeStamp(), playerId, clientId);
            GameDealPacket.activePlayerTime(playerId);
        });
    }

    /**
     * 分配一条线
     */
    public synchronized DivisionLine allocateLine(SceneMapType mapType, long playerId){
        try{
            if(!lineWorkers.containsKey(mapType)){
                lineWorkers.put(mapType, new DivisionLineWorker());
            }
            DivisionLineWorker lineWorker = lineWorkers.get(mapType);
            //检查是否存在分线
            if(lineWorker.getPlayerId2LineIds().containsKey(playerId) && lineWorker.getDivisionLineInfos().containsKey(lineWorker.getPlayerId2LineIds().get(playerId))){
                return lineWorker.getDivisionLineInfos().get(lineWorker.getPlayerId2LineIds().get(playerId));
            }
            //获取未满并且人数最高一条线
            Map<Integer, DivisionLine> divisionLineInfos = lineWorker.getDivisionLineInfos();
            List<DivisionLine> allWorkers = new ArrayList<>(divisionLineInfos.values());

            Optional<DivisionLine> targetWorker = allWorkers.stream()
                    .filter(worker -> worker.getStatus() == DivisionLine.Status_Run)
                    .filter(worker -> worker.getPlayerIds().size() < max_singleLine_canEnter_player_num)
                    .max(Comparator.comparingInt(o -> o.getPlayerIds().size()));
            DivisionLine line;
            if(targetWorker.isPresent()){
                line = divisionLineInfos.get(targetWorker.get().getLineId());
            }else{
                //获取九宫格信息
                SceneMapInfo sceneMapInfo = SceneMapService.getSceneMapInfo(mapType);
                //创建新的线
                line = new DivisionLine(lineWorker.allocateLineId(), sceneMapInfo.buildSceneNodes());
                divisionLineInfos.put(line.getLineId(), line);
                log("create new line : " + line.getLineId());
            }
            //绑定分线
            lineWorker.getPlayerId2LineIds().put(playerId, line.getLineId());

            log(String.format("success allocate line, playerId : %s, line id : %s, lineNum : %s", playerId, line.getLineId(), line.getPlayerIds().size()));
            return line;
        }finally {

        }
    }

    /**
     * 退出分线
     */
    public synchronized void unAllocateLine(SceneMapType mapType, long playerId){
        if(lineWorkers.containsKey(mapType)){
            try {
                DivisionLineWorker worker = lineWorkers.get(mapType);
                worker.exit(playerId);
            }finally {
            }
        }
    }

    public synchronized void checkMerge(){
        //合并分线
        try {
            for (DivisionLineWorker lineWorker : lineWorkers.values()) {
                Map<Integer, DivisionLine> divisionLineInfos = lineWorker.getDivisionLineInfos();
                List<DivisionLine> allLineList = new ArrayList<>(divisionLineInfos.values());
                //获取未满 玩家id最少的两条线
                List<DivisionLine> minLineList = allLineList.stream()
                        .filter(worker -> worker.getStatus() == DivisionLine.Status_Run)
                        .filter(worker -> worker.getPlayerIds().size() < max_singleLine_canEnter_player_num)
                        .sorted(Comparator.comparingInt(o -> o.getPlayerIds().size())).collect(Collectors.toList());
                if(minLineList.size() >= 2){
                    DivisionLine one = minLineList.get(minLineList.get(0).getLineId());
                    DivisionLine two = minLineList.get(minLineList.get(1).getLineId());
                    //满足合并需求
                    if(one.getPlayerIds().size() + two.getPlayerIds().size() < max_singleLine_canEnter_player_num){
                        //人数最少的线不再接收任务
                        one.setStatus(DivisionLine.Status_Run, DivisionLine.Status_Stop);
                        //修改玩家进入场景
                        if(one.getLineId() > two.getLineId()){
                            updatePlayerEnterViewAndEnterWorker(lineWorker, two, one.getPlayerIds());
                            one.getPlayerIds().clear();
                        }else{
                            updatePlayerEnterViewAndEnterWorker(lineWorker, one, two.getPlayerIds());
                            two.getPlayerIds().clear();
                        }

                        log(String.format("merge line , one : %s, two : %s, final player size : %s", one.getLineId(), two.getLineId(), one.getPlayerIds().size()));
                    }
                }
                //清除合并过的分线
                for (int lineId : divisionLineInfos.keySet()) {
                    DivisionLine worker = divisionLineInfos.get(lineId);
                    if(Objects.nonNull(worker)
                            && worker.getStatus() == DivisionLine.Status_Stop){
                        lineWorker.getDivisionLineInfos().remove(lineId);
                        log("line worker is free, del : " + lineId);
                    }
                }
            }
        }finally {
        }


    }

    @Override
    protected void subRunnerExecute() throws Exception {
        checkMerge();

    }

    public static void log(String log){
//        System.out.println(log);
    }

    /**
     * 修改玩家视野并进入分线
     */
    private void updatePlayerEnterViewAndEnterWorker(DivisionLineWorker worker, DivisionLine line, Set<Long> playerIds){
        //通知玩家切线
        notifyChangeLine(worker, line, playerIds);
        //切线处理
        for (long playerId : playerIds) {
            changeLine(worker, line, playerId);
        }
    }

    /**
     * 组队切线
     */
    public void teamChangeLine(DivisionLineWorker worker, DivisionLine line, List<Long> teamMemberIds, long leaderId){
        Set<Long> notifyIds = new HashSet<>(teamMemberIds);
        notifyIds.remove(leaderId);
        //通知玩家切线
        notifyChangeLine(worker, line, notifyIds);

        for (long playerId : teamMemberIds) {
            changeLine(worker, line, playerId, teamMemberIds);
        }
    }
    /**
     * 玩家切线
     */
    public void changeLine(DivisionLineWorker worker, DivisionLine line, long playerId){
        changeLine(worker, line, playerId, null);
    }
    public void changeLine(DivisionLineWorker worker, DivisionLine line, long playerId, List<Long> teamMember){
        ScenePlayerInfo playerInfo = SceneMapManager.getPlayerInfo(playerId);
        if(Objects.nonNull(playerInfo)){
            //获取九宫格
            List<SceneNode> ninNode = SceneMapService.getNinNode(playerInfo, playerInfo.getPosition());
            //离开场景玩家id
            List<Long> leaveScenePlayerIds = SceneWalkEnum.single_node_walk.getWalkLogic().findNinePlayer(playerInfo.getMapType(), playerId, ninNode, ninNode);
            //移除组队玩家
            if(CollectionUtil.isNotEmpty(teamMember)){
                leaveScenePlayerIds.removeAll(teamMember);
            }
            //通知离开视野
            if(CollectionUtil.isNotEmpty(leaveScenePlayerIds)){
                SceneWalkEnum.two_node_walk.getWalkLogic().msgNotify(leaveScenePlayerIds, PtCode.SCENE_LEAVE_NOTIFY, PbProtocol.SceneLeaveViewNotify.newBuilder().addPlayerId(playerInfo.getPlayerId()).build(), false, playerInfo.getPlayerId());
                //自己删除视野玩家
                SceneWalkEnum.two_node_walk.getWalkLogic().myselfRemoveView(playerInfo.getPlayerId(), leaveScenePlayerIds);
            }
            //进入新分线
            worker.getPlayerId2LineIds().put(playerInfo.getPlayerId(), line.getLineId());

            //进入场景玩家id
            List<Long> enterScenePlayerIds = SceneWalkEnum.single_node_walk.getWalkLogic().findNinePlayer(playerInfo.getMapType(), playerId, ninNode, ninNode);
            //移除组队玩家
            if(CollectionUtil.isNotEmpty(teamMember)){
                enterScenePlayerIds.removeAll(teamMember);
            }
            //通知进入视野
            if(CollectionUtil.isNotEmpty(enterScenePlayerIds)){
                SceneWalkEnum.two_node_walk.getWalkLogic().msgNotify(enterScenePlayerIds, PtCode.SCENE_ENTER_NOTIFY,  PbProtocol.SceneEnterViewNotify.newBuilder().addPlayer(playerInfo.genPb()).build(), false,playerInfo.getPlayerId());
                //自己增加视野玩家
                SceneWalkEnum.two_node_walk.getWalkLogic().myselfAddView(playerInfo.getPlayerId(), enterScenePlayerIds);
            }

        }else{
            log("updatePlayerEnterViewAndEnterWorker, playerId is not info : " + playerId);
        }
    }

    /**
     * 切线推送
     */
    private void notifyChangeLine(DivisionLineWorker worker, DivisionLine line, Set<Long> playerIds){
        PbProtocol.SceneChangeLineNotify.Builder notify = PbProtocol.SceneChangeLineNotify.newBuilder();
        notify.setLineInfo(genLineWorker(line.getLineId(), line));

        GateNodeManager.getInstance().broadcast(new ArrayList<>(playerIds), PtCode.SCENE_CHANGE_LINE_NOTIFY, notify.build());
    }

    public static PbScene.SceneLineInfo genLineWorker(int showLineId, DivisionLine line){
        PbScene.SceneLineInfo.Builder lineInfo = PbScene.SceneLineInfo.newBuilder();
        lineInfo.setShowLineId(showLineId);
        lineInfo.setLineId(line.getLineId());
        lineInfo.setStatus(line.getShowStatus());
        return lineInfo.build();
    }

    public DivisionLineWorker getDivisionWorker(SceneMapType mapType){
        return lineWorkers.get(mapType);
    }

    public DivisionLine getDivisionLine(SceneMapType mapType, long playerId){
        DivisionLineWorker lineWorker = getDivisionWorker(mapType);
        if(Objects.nonNull(lineWorker) && lineWorker.getPlayerId2LineIds().containsKey(playerId)){
            return lineWorker.getDivisionLineInfos().get(lineWorker.getPlayerId2LineIds().get(playerId));
        }
        return null;
    }

    public DivisionLine getDivisionLine(SceneMapType mapType, int lineId){
        DivisionLineWorker lineWorker = getDivisionWorker(mapType);
        if(Objects.nonNull(lineWorker)){
            return lineWorker.getDivisionLineInfos().get(lineId);
        }
        return null;
    }

    public int getAllOnlinePlayerCount(){
        int allCount = 0;
        for (DivisionLineWorker worker : lineWorkers.values()) {
            for (DivisionLine divisionLine : worker.getDivisionLineInfos().values()) {
                allCount += divisionLine.getPlayerIds().size();
            }
        }
        return allCount;
    }

    private static final DivisionLineManager instance = new DivisionLineManager();
    DivisionLineManager(){}
    public static DivisionLineManager getInstance(){return instance;}

    @Override
    public String getRunnerName() {
        return "DivisionLineManager";
    }

    @Override
    public long getRunnerInterval() {
        return 1000 * 60 * 30;
//        return 1000 * 10;
    }

    @Override
    public long getMaxExecuteTime() {
        return this.getRunnerInterval() + 1000 * 60 * 10;
    }

}

package com.gy.server.scene.divisionLine;

import java.util.*;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.atomic.AtomicInteger;

import com.gy.server.packet.PbScene;
import com.gy.server.scene.map.bean.SceneNode;

/**
 * 分线工作器
 * <AUTHOR> - [Created on 2022-04-09 15:10]
 */
public class DivisionLine{

    public static int Status_Run = 1;
    public static int Status_Stop = 2;
    private int id;
    private SceneNode[][] sceneNodes;
    //当前工作器状态
    private AtomicInteger status;

    protected DivisionLine(int id, SceneNode[][] sceneNodes){
        this.id = id;
        this.status = new AtomicInteger(Status_Run);
        this.sceneNodes = sceneNodes;
    }


    public SceneNode[][] getSceneNodes() {
        return sceneNodes;
    }

    public void exit(long playerId){
        for (SceneNode[] sceneNode : sceneNodes) {
            for (SceneNode node : sceneNode) {
                node.removePlayerId(playerId);
            }
        }
    }

    public int getStatus() {
        return status.get();
    }

    public void setStatus(int oldStatus, int newStatus) {
        this.status.compareAndSet(oldStatus, newStatus);
    }

    public int getLineId(){
        return id;
    }

    public Set<Long> getPlayerIds() {
        Set<Long> playerIds = new HashSet<>();
        for (SceneNode[] sceneNode : sceneNodes) {
            for (SceneNode node : sceneNode) {
                playerIds.addAll(node.getAllPlayerIds());
            }
        }
        return playerIds;
    }

    public PbScene.SceneLineStatus getShowStatus(){
        int linePlayerNum = getPlayerIds().size();
        if(DivisionLineManager.max_singleLine_player_num / 100 * 30 > linePlayerNum){
            return PbScene.SceneLineStatus.status1;
        }else if(DivisionLineManager.max_singleLine_player_num / 100 * 60 > linePlayerNum){
            return PbScene.SceneLineStatus.status2;
        }else if(DivisionLineManager.max_singleLine_player_num / 100 * 75 > linePlayerNum){
            return PbScene.SceneLineStatus.status3;
        }else{
            return PbScene.SceneLineStatus.status4;
        }
    }
}

package com.gy.server.scene.divisionLine;

import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 分线工作器
 * <AUTHOR> - [Created on 2022-05-12 10:52]
 */
public class DivisionLineWorker {

    //分线id自增
    private AtomicInteger nextLineId = new AtomicInteger(0);
    //玩家id和分线id对应关系
    private Map<Long, Integer> playerId2LineIds = new ConcurrentHashMap<>();

    //分线内容
    private Map<Integer, DivisionLine> divisionLineInfos = new ConcurrentHashMap<>();

    public void exit(long playerId){
        int lineId = playerId2LineIds.getOrDefault(playerId, -1);
        if(divisionLineInfos.containsKey(lineId)){
            DivisionLine divisionLine = divisionLineInfos.get(lineId);
            divisionLine.exit(playerId);
            if(divisionLineInfos.size() >= 2 && divisionLine.getPlayerIds().isEmpty()){
                divisionLine.setStatus(DivisionLine.Status_Run, DivisionLine.Status_Stop);
            }
        }
        playerId2LineIds.remove(playerId);
        DivisionLineManager.log(String.format("unAllocate line, playerId : %s, lineId : %s", playerId, lineId));

    }

    public int getPlayerCount(){
        int playerCount = 0;
        for (DivisionLine divisionLine : divisionLineInfos.values()) {
            playerCount += divisionLine.getPlayerIds().size();
        }
        return playerCount;
    }

    public int allocateLineId(){
        return nextLineId.incrementAndGet();
    }

    public AtomicInteger getNextLineId() {
        return nextLineId;
    }

    public void setNextLineId(AtomicInteger nextLineId) {
        this.nextLineId = nextLineId;
    }

    public Map<Long, Integer> getPlayerId2LineIds() {
        return playerId2LineIds;
    }

    public void setPlayerId2LineIds(Map<Long, Integer> playerId2LineIds) {
        this.playerId2LineIds = playerId2LineIds;
    }

    public Map<Integer, DivisionLine> getDivisionLineInfos() {
        return divisionLineInfos;
    }

    public void setDivisionLineInfos(Map<Integer, DivisionLine> divisionLineInfos) {
        this.divisionLineInfos = divisionLineInfos;
    }

    public static void addTask(Runnable task){
        ThreadPool.execute(task);
    }

}

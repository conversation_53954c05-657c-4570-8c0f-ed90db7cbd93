package com.gy.server.combat.bean;

import com.gy.server.game.battleCollect.BattleCollectService;
import com.gy.server.game.battleCollect.template.BattleCollectTemplate;
import com.gy.server.game.combat.StageType;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.hero.Hero;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 战斗任务
 */
public class CombatTask {

    public int battleCollectId;

    public List<HeroUnit> atks = new ArrayList<>();

    public List<HeroUnit> defs = new ArrayList<>();

    public StageType stageType;

    public LineupType lineupType;

    public long playerId;

    private CombatTask(){}

    public static void fillUnitInfo(Player player, int battleCollectId, StageType stageType, LineupType lineupType, List<HeroUnit> units){
        List<Hero> allHeroes = player.getBagModel().getAllHeroes();
        Collections.shuffle(allHeroes);
        boolean canRole = battleCollectId % 2 == 1;
        int heroCount = canRole ? 5 : 6;
        for (int i = 0; i < heroCount; i++) {
            Hero hero = allHeroes.get(i);
            HeroUnit heroUnit = hero.createHeroUnit(stageType, lineupType);
            heroUnit.setPosIndex(i);
            heroUnit.setPlayerId(player.getPlayerId());
            units.add(heroUnit);
        }
        if(canRole){
            HeroUnit heroUnit = player.getRoleModel().createHeroUnit(stageType, lineupType);
            heroUnit.setPosIndex(5);
            heroUnit.setPlayerId(player.getPlayerId());
            units.add(heroUnit);
        }
    }

    public static CombatTask newTask(int battleCollectId){
        //获取战斗配置
        BattleCollectTemplate battleCollectTemplate = BattleCollectService.getBattleCollectTemplateMap().get(battleCollectId);
        //获取布阵和战斗类型
        StageType stageType = StageType.getStageTypeById(battleCollectTemplate.type);
        LineupType lineupType = LineupType.GM;
        //创建战斗任务
        CombatTask task = new CombatTask();
        //开始填充阵容信息
        Player player = getPlayer();
        //进攻方数据
        fillUnitInfo(player, battleCollectId, stageType, lineupType, task.atks);
        //填充防守方数据
        if(stageType.isPvp()){
            fillUnitInfo(player, battleCollectId, stageType, lineupType, task.defs);
        }else{
            task.defs.addAll(battleCollectTemplate.genHeroUnits(-1));
        }
        task.lineupType = lineupType;
        task.stageType = stageType;
        task.playerId = player.getPlayerId();
        task.battleCollectId = battleCollectId;
        return task;
    }

    public static Player getPlayer(){
        return PlayerManager.getOnlinePlayers().stream().findFirst().get();
    }

}

package com.gy.server.combat.bean;

import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.utils.function.Ticker;

public class CombatTicker implements Ticker {

    @Override
    public void tick() {
        for (Player onlinePlayer : PlayerManager.getOnlinePlayers()) {
            onlinePlayer.dataSyncModule.getSyncDataQueue().clear();
        }
    }

    private static CombatTicker instance = new CombatTicker();
    private CombatTicker(){}
    public static CombatTicker getInstance(){
        return instance;
    }
}

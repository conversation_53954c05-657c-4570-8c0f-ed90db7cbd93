package com.gy.server.combat.bean;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.combat.skill.Skill;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.combat.unit.TeamUnit;
import com.ttlike.server.tl.baselib.util.StringConcat;

/**
 * 压测战斗
 */
public class PressCombatStage extends AbstractStage {

    CombatTask task;
    long combatStartTime;

    public PressCombatStage(CombatTask task){
        this.task = task;
    }

    @Override
    public void init() {
        //需要配置stageType，根据stageType战斗，检测是不是pvp
        TeamUnit atkTeam = new TeamUnit(getNewId(), task.atks);
        atkTeam.setAutoMode(task.playerId);
        TeamUnit defTeam = new TeamUnit(getNewId(), task.defs);
        defTeam.setAutoMode(task.playerId);
        init(task.battleCollectId, task.stageType, atkTeam, defTeam, task.lineupType);
        combatStartTime = ServerConstants.getCurrentTimeMillis();
    }

    @Override
    public void afterFinish() {
        long combatFinishTime = ServerConstants.getCurrentTimeMillis();
        System.out.println("isAtk | unitTId | skillInfo");
        for (HeroUnit heroUnit : getAtks().get(0).getFormation()) {
            StringConcat sc = StringConcat.on("_");
            for (Skill skill : heroUnit.getSkills()) {
                sc.add(skill.getTid());
            }
            System.out.println("1 | " + heroUnit.getTid() + " | " + sc);
        }
        for (HeroUnit heroUnit : getDefs().get(0).getFormation()) {
            StringConcat sc = StringConcat.on("_");
            for (Skill skill : heroUnit.getSkills()) {
                sc.add(skill.getTid());
            }
            System.out.println("2 | " + heroUnit.getTid() + " | " + sc);
        }
        System.out.println("combat Cost time : " + (combatFinishTime - combatStartTime));
    }
}

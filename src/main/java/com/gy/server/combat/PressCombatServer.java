package com.gy.server.combat;

import com.gy.server.combat.bean.CombatTicker;
import com.gy.server.common.util.ServerWaitHelper;
import com.gy.server.core.Configuration;
import com.gy.server.core.Launcher;
import com.gy.server.core.MainThread;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.combat.CombatManager;
import com.gy.server.game.db.DbManager;
import com.gy.server.game.player.PlayerSaveManager;
import com.gy.server.game.service.ServiceManager;
import com.gy.server.game.time.TimeManager;
import com.gy.server.game.world.World;
import com.gy.server.utils.runner.RunnerManager;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

public class PressCombatServer extends MainThread {

    @Override
    protected void init() throws Exception {

        Configuration.initConfig();

        //服务器常量
        Launcher.launchStartupInfo("ServerConstants init");
        ServerConstants.init();

//        //db相关初始化
//        Launcher.launchStartupInfo("DbManager init");
//        DbManager.init();

        //快速tick
        Launcher.launchStartupInfo("add fast ticker");
        addFastTicker(CombatManager.getInstance());

        //慢速tick
        Launcher.launchStartupInfo("add slow ticker");
        addSlowTicker(RunnerManager.getInstance());
        addSlowTicker(CombatTicker.getInstance());

        //注册service
        Launcher.launchStartupInfo("ServiceManager init");
        ServiceManager.init();

        Launcher.launchStartupInfo("GameServer is initialized");
    }

    @Override
    protected void startup() throws Exception {
        Launcher.launchStartupInfo("GameServer is starting");

        RunnerManager.addRunner(ServerWaitHelper.getInstance(), ServerWaitHelper.getInstance().getRunnerName(), false);

        //service前置启动
        Launcher.launchStartupInfo("ServiceManager pre startup");
        ServiceManager.preStartup();

        //service启动
        Launcher.launchStartupInfo("ServiceManager startup");
        ServiceManager.startup();

        //独立线程启动
        RunnerManager.addRunner(PressCombatRunner.getInstance(), PressCombatRunner.getInstance().getRunnerName(), false);

        //线程启动
        Launcher.launchStartupInfo("MainThread start");
        this.start();

        PressCombatHelper.dataInit();
        Launcher.launchStartupInfo("PressCombatHelper dataInit ");
        PressCombatHelper.startup();
        Launcher.launchStartupInfo("PressCombatHelper is started");

        Launcher.launchStartupInfo("GameServer is started");

        //输出启动成功：http://patorjk.com/software/taag，当前使用字体：Doom
        System.out.println("                                   /$$                   /$$                                                                                         /$$                           /$$    \n" +
                "                                  | $$                  | $$                                                                                        | $$                          | $$    \n" +
                "  /$$$$$$$  /$$$$$$  /$$$$$$/$$$$ | $$$$$$$   /$$$$$$  /$$$$$$          /$$$$$$$  /$$$$$$   /$$$$$$  /$$    /$$ /$$$$$$   /$$$$$$         /$$$$$$$ /$$$$$$    /$$$$$$   /$$$$$$  /$$$$$$  \n" +
                " /$$_____/ /$$__  $$| $$_  $$_  $$| $$__  $$ |____  $$|_  $$_/         /$$_____/ /$$__  $$ /$$__  $$|  $$  /$$//$$__  $$ /$$__  $$       /$$_____/|_  $$_/   |____  $$ /$$__  $$|_  $$_/  \n" +
                "| $$      | $$  \\ $$| $$ \\ $$ \\ $$| $$  \\ $$  /$$$$$$$  | $$          |  $$$$$$ | $$$$$$$$| $$  \\__/ \\  $$/$$/| $$$$$$$$| $$  \\__/      |  $$$$$$   | $$      /$$$$$$$| $$  \\__/  | $$    \n" +
                "| $$      | $$  | $$| $$ | $$ | $$| $$  | $$ /$$__  $$  | $$ /$$       \\____  $$| $$_____/| $$        \\  $$$/ | $$_____/| $$             \\____  $$  | $$ /$$ /$$__  $$| $$        | $$ /$$\n" +
                "|  $$$$$$$|  $$$$$$/| $$ | $$ | $$| $$$$$$$/|  $$$$$$$  |  $$$$/       /$$$$$$$/|  $$$$$$$| $$         \\  $/  |  $$$$$$$| $$             /$$$$$$$/  |  $$$$/|  $$$$$$$| $$        |  $$$$/\n" +
                " \\_______/ \\______/ |__/ |__/ |__/|_______/  \\_______/   \\___/        |_______/  \\_______/|__/          \\_/    \\_______/|__/            |_______/    \\___/   \\_______/|__/         \\___/  \n" +
                "                                                                                                                                                                                          \n" +
                "                                                                                                                                                                                          \n" +
                "                                                                                                                                                                                         ");

    }

    @Override
    protected void shutdown() throws Exception {
        Launcher.launchShutdownInfo("GameServer start shutdown");
        running = false;

        while (true) {
            if (shutdown
                    && ThreadPool.getInstance().getSize() < 1
                    // 保证之前的Player数据全部入库
                    && !PlayerSaveManager.isRunning()) {

                Launcher.launchShutdownInfo("GuestManager shutdown");

                //rpc预关闭
                TLBase.getInstance().preShutdown();
                Launcher.launchShutdownInfo("TLBase preShutdown");

                //战斗管理器关闭
                CombatManager.shutdown();
                Launcher.launchShutdownInfo("CombatManager shutdown");

                //独立线程关闭
                RunnerManager.shutdown();
                Launcher.launchShutdownInfo("RunnerManager shutdown");
                Launcher.launchShutdownInfo("TLBase shutdown");

                //db关闭
                DbManager.shutdown();
                Launcher.launchShutdownInfo("DB shutdown");

                break;
            }

            Thread.sleep(100);
        }

        Launcher.launchShutdownInfo("GameServer end shutdown");
        System.out.println("                                   /$$                   /$$                                                                                         /$$                        \n" +
                "                                  | $$                  | $$                                                                                        | $$                        \n" +
                "  /$$$$$$$  /$$$$$$  /$$$$$$/$$$$ | $$$$$$$   /$$$$$$  /$$$$$$          /$$$$$$$  /$$$$$$   /$$$$$$  /$$    /$$ /$$$$$$   /$$$$$$         /$$$$$$$ /$$$$$$    /$$$$$$   /$$$$$$ \n" +
                " /$$_____/ /$$__  $$| $$_  $$_  $$| $$__  $$ |____  $$|_  $$_/         /$$_____/ /$$__  $$ /$$__  $$|  $$  /$$//$$__  $$ /$$__  $$       /$$_____/|_  $$_/   /$$__  $$ /$$__  $$\n" +
                "| $$      | $$  \\ $$| $$ \\ $$ \\ $$| $$  \\ $$  /$$$$$$$  | $$          |  $$$$$$ | $$$$$$$$| $$  \\__/ \\  $$/$$/| $$$$$$$$| $$  \\__/      |  $$$$$$   | $$    | $$  \\ $$| $$  \\ $$\n" +
                "| $$      | $$  | $$| $$ | $$ | $$| $$  | $$ /$$__  $$  | $$ /$$       \\____  $$| $$_____/| $$        \\  $$$/ | $$_____/| $$             \\____  $$  | $$ /$$| $$  | $$| $$  | $$\n" +
                "|  $$$$$$$|  $$$$$$/| $$ | $$ | $$| $$$$$$$/|  $$$$$$$  |  $$$$/       /$$$$$$$/|  $$$$$$$| $$         \\  $/  |  $$$$$$$| $$             /$$$$$$$/  |  $$$$/|  $$$$$$/| $$$$$$$/\n" +
                " \\_______/ \\______/ |__/ |__/ |__/|_______/  \\_______/   \\___/        |_______/  \\_______/|__/          \\_/    \\_______/|__/            |_______/    \\___/   \\______/ | $$____/ \n" +
                "                                                                                                                                                                      | $$      \n" +
                "                                                                                                                                                                      | $$      \n" +
                "                                                                                                                                                                      |__/     ");
    }




}
package com.gy.server.combat;

import com.gy.server.core.Configuration;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.account.Account;
import com.gy.server.game.global.GlobalData;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerData;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.qinPalace.QinPalaceGlobalData;
import com.gy.server.game.world.WorldBaseGlobalData;
import com.gy.server.packet.PbProtocol;
import com.ttlike.server.tl.baselib.serialize.player.FaceDataDb;

import java.util.Map;

public class PressCombatHelper {

    public static void dataInit(){
        Map<GlobalDataType, GlobalData> dataMap = GlobalDataManager.getDataMap();
        QinPalaceGlobalData qinPalaceGlobalData = new QinPalaceGlobalData();
        qinPalaceGlobalData.init();
        dataMap.put(GlobalDataType.QinPalace, qinPalaceGlobalData);
        WorldBaseGlobalData worldBaseGlobalData = new WorldBaseGlobalData();
        worldBaseGlobalData.init();
        dataMap.put(GlobalDataType.worldBase, worldBaseGlobalData);
    }

    public static void startup(){
        initRobot();
        nb();
        PressCombatRunner.getInstance().setCanStart(true);
    }

    /**
     * 初始化机器人
     */
    public static void initRobot(){
        int combatRobotNum = 100;
        for (int i = 0; i < combatRobotNum; i++) {
            String name = "TestRobot_" + i;
            // 创建PlayerData并保存
            PlayerData playerData = new PlayerData();
            playerData.setLevel(1);
            playerData.setName(name);
            playerData.setSex(1);
            playerData.setProfession(1);
            playerData.setAccountId(name);
            playerData.setAccountType(1001000);
            playerData.setServerNum(Configuration.serverId);
            playerData.setLanguage("zh_CN");
            playerData.setDeviceType("ios");
            playerData.setLastLogoutTime(ServerConstants.getCurrentTimeDate());
            playerData.setLastLoginTime(ServerConstants.getCurrentTimeDate());
            playerData.setPlayerId(i + 1);
            // 捏脸数据更新
            FaceDataDb faceDataDb = new FaceDataDb();
            faceDataDb.setFaceData(new byte[10]);
            playerData.getPlayerBlob().setFaceDataDb(faceDataDb);
            // 添加miniPlayer
            Player dbPlayer = new Player(playerData);
            PbProtocol.LoginCheckReq.Builder req = PbProtocol.LoginCheckReq.newBuilder();
            req.setAccountId(name);
            req.setAccountType(1000000);
            req.setAnonymousId("anonymousId");
            req.setPayChannel("changyou_all");
            req.setDeviceId("deviceId");
            req.setDeviceType("windows");
            req.setDeviceVersion("deviceVersion");
            req.setDeviceModel("deviceModel");
            req.setDeviceManufacturer("deviceManufacturer");
            req.setDeviceResolution("deviceResolution");
            req.setToken("5bKnASXNC18mtjJiTJRXz0i3e");
            req.setServerNum(1);
            req.setVersion("2.2.2");
            req.setReConnect(false);
            req.setLanguage("1");
            req.setAdChannel("adChannel");
            req.addParams("params");
            req.setSuperLoginPlayerId(0);
            req.setClientInitVersion("clientInitVersion");
            req.setNetType("wifi");
            req.setSdkVersion("sdkVersion");
            req.setSdkDeviceId("sdkDeviceId");
            req.setAge(18);
            Account account = new Account(req.build(), "");
            dbPlayer.setAccount(account);
            dbPlayer.init(true);
            PlayerManager.addOnlinePlayer(dbPlayer);
        }
    }

    /**
     * 一键增强
     */
    public static void nb(){
        for (Player player : PlayerManager.getOnlinePlayers()) {
            PlayerHelper.gmDeal(player, "/gm level 100");
            PlayerHelper.gmDeal(player, "/gm addAllHero");
            PlayerHelper.gmDeal(player, "/gm addAllItem 1000");
            PlayerHelper.gmDeal(player, "/gm nb");
        }
    }

}

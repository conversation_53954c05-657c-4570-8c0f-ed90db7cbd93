package com.gy.server.combat;

import com.gy.server.combat.bean.CombatTask;
import com.gy.server.combat.bean.PressCombatStage;
import com.gy.server.core.Configuration;
import com.gy.server.core.thread.AbstractRunner;
import com.gy.server.game.battleCollect.BattleCollectService;
import com.gy.server.game.battleCollect.template.BattleCollectTemplate;
import com.gy.server.game.combat.CombatManager;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 压测战斗构造器
 */
public class PressCombatRunner extends AbstractRunner {

    private static PressCombatRunner instance = new PressCombatRunner();
    private PressCombatRunner(){}
    public static PressCombatRunner getInstance(){
        return instance;
    }

    @Override
    public String getRunnerName() {
        return "PressCombatRunner";
    }

    AtomicLong combatTimes = new AtomicLong();

    public boolean canStart = false;

    public boolean isCanStart() {
        return canStart;
    }

    public void setCanStart(boolean canStart) {
        this.canStart = canStart;
    }

    @Override
    protected void subRunnerExecute() throws Exception {
        if(!canStart){
            return;
        }
        //根据配置，匹配战斗
        List<Integer> battleCollectIds = BattleCollectService.getStage2BattleCollectIds().get(Configuration.pressCombatType);

        int battleCollectId = battleCollectIds.get((int) Math.abs(combatTimes.getAndIncrement() % battleCollectIds.size()));

        CombatTask combatTask = CombatTask.newTask(battleCollectId);
        PressCombatStage stage = new PressCombatStage(combatTask);
        stage.init();
        CombatManager.combatStart(stage);
    }

    @Override
    public long getRunnerInterval() {
        return 10L;
    }
}

package com.gy.server.game.arenaCross.impl.battle.impl;

import com.gy.server.core.battleRule.base.IBattleStatusDeal;
import com.gy.server.core.battleRule.enums.BattleStep;
import com.gy.server.game.arenaCross.ArenaBattleInfo;

/**
 * <AUTHOR> - [Created on 2022-07-21 17:52]
 */
public class ArenaBattleStatus1MatchHandler implements IBattleStatusDeal<ArenaBattleInfo> {

    @Override
    public void init(ArenaBattleInfo baseBattleInfo) {

    }

    @Override
    public void deal(ArenaBattleInfo baseBattleInfo) {

    }



    @Override
    public boolean finish(ArenaBattleInfo baseBattleInfo) {
        boolean isFinish = false;
//        return isFinish ? nowStatus() : nextStatus();
        return isFinish;
    }

    @Override
    public int nowStatus() {
        return BattleStep.match.getTemplateId();
    }

    @Override
    public int nextStatus(ArenaBattleInfo battleInfo) {
        return BattleStep.battle.getTemplateId();
    }

    @Override
    public long getStatusDurationTime(ArenaBattleInfo battleInfo) {
        return 0;
    }
}

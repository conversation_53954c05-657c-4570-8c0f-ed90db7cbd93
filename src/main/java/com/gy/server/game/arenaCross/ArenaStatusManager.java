package com.gy.server.game.arenaCross;

import com.gy.server.core.battleRule.AbstractBattleManager;
import com.gy.server.core.battleRule.base.BaseBattleInfo;
import com.gy.server.core.battleRule.enums.BattleEnums;
import com.gy.server.core.battleRule.enums.BattleStep;

/**
 * 跨服竞技场
 * <AUTHOR> - [Created on 2022-07-21 13:27]
 */
public class ArenaStatusManager extends AbstractBattleManager<ArenaBattleInfo> {

    public ArenaStatusManager(boolean isRunner) {
        super(isRunner);
    }

    @Override
    public ArenaBattleInfo getBattleInfo() {
        ArenaBattleInfo battleInfo = new ArenaBattleInfo();

        battleInfo.setStatus(BattleStep.init.getTemplateId());
        //取数据
        return battleInfo;
    }

    @Override
    public void saveBattleInfo(ArenaBattleInfo baseBattleInfo) {
        //存储
    }

    @Override
    public void start(){

        //....
        try {
            super.initClazz();
        } catch (IllegalAccessException | InstantiationException e) {
            e.printStackTrace();
        }

        //TODO 根据当前状态或者当前时间设置某些临时数据或者运行时内存数据

    }

    public static void main(String[] args) {
        System.out.println(BattleEnums.arena_cross.getBattleLogic().getRunnerName());

    }

    @Override
    protected void saveSonStatus(BaseBattleInfo baseBattleInfo, int currentStatus) {
        ArenaBattleInfo battleInfo = (ArenaBattleInfo) baseBattleInfo;
        battleInfo.setCombatStatus(currentStatus);
    }

    @Override
    public boolean isGameServer() {
        return false;
    }

    @Override
    public boolean isWorldServer() {
        return false;
    }
}

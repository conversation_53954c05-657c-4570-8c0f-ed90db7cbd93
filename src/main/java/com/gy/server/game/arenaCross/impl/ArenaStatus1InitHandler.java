package com.gy.server.game.arenaCross.impl;

import com.gy.server.core.battleRule.base.IBattleStatusDeal;
import com.gy.server.core.battleRule.enums.BattleEnums;
import com.gy.server.core.battleRule.enums.BattleStep;
import com.gy.server.game.arenaCross.ArenaBattleInfo;
import com.gy.server.game.arenaCross.impl.battle.ArenaBattleManager;

/**
 * <AUTHOR> - [Created on 2022-07-21 14:11]
 */
public class ArenaStatus1InitHandler implements IBattleStatusDeal<ArenaBattleInfo> {

    @Override
    public void init(ArenaBattleInfo baseBattleInfo) {

    }

    @Override
    public void deal(ArenaBattleInfo baseBattleInfo) {
        System.out.println("Status1InitArenaHandler deal ..................");


        baseBattleInfo.setStatus(BattleStep.dataPull.getTemplateId());

        //存数据
        ArenaBattleManager battleLogic = (ArenaBattleManager)BattleEnums.arena_cross.getBattleLogic();
        battleLogic.saveBattleInfo(baseBattleInfo);
    }

    @Override
    public boolean finish(ArenaBattleInfo baseBattleInfo) {
        boolean isFinish = false;
//        return isFinish ? nowStatus() : nextStatus();
        return isFinish;
    }

    @Override
    public int nowStatus() {
        return BattleStep.init.getTemplateId();
    }

    @Override
    public int nextStatus(ArenaBattleInfo arenaBattleInfo) {
        return BattleStep.match.getTemplateId();
    }

    @Override
    public long getStatusDurationTime(ArenaBattleInfo battleInfo) {
        return 0;
    }
}

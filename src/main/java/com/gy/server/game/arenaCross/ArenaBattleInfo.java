package com.gy.server.game.arenaCross;

import com.gy.server.core.battleRule.base.BaseBattleInfo;
import com.gy.server.core.battleRule.enums.BattleEnums;

/**
 * 跨服竞技场
 * <AUTHOR> - [Created on 2022-07-21 14:26]
 */
public class ArenaBattleInfo extends BaseBattleInfo {

    //存自己模块数据
    private int combatStatus;


    public ArenaBattleInfo() {
        super(BattleEnums.arena_cross);
    }

    public int getCombatStatus() {
        return combatStatus;
    }

    public void setCombatStatus(int combatStatus) {
        this.combatStatus = combatStatus;
    }
}

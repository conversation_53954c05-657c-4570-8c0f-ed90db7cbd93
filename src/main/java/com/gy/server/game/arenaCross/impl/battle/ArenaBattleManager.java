package com.gy.server.game.arenaCross.impl.battle;

import com.gy.server.core.battleRule.AbstractBattleManager;
import com.gy.server.core.battleRule.base.BaseBattleInfo;
import com.gy.server.core.battleRule.enums.BattleEnums;
import com.gy.server.game.arenaCross.ArenaBattleInfo;
import com.gy.server.game.arenaCross.ArenaStatusManager;

/**
 * <AUTHOR> - [Created on 2022-07-21 17:39]
 */
public class ArenaBattleManager extends AbstractBattleManager<ArenaBattleInfo> {

    public ArenaBattleManager(boolean isRunner) {
        super(isRunner);
    }

    @Override
    public ArenaBattleInfo getBattleInfo() {
        return (ArenaBattleInfo)BattleEnums.arena_cross.getBattleLogic().getBattleInfo();
    }

    @Override
    public void saveBattleInfo(ArenaBattleInfo baseBattleInfo) {
        ArenaStatusManager battleLogic = (ArenaStatusManager)BattleEnums.arena_cross.getBattleLogic();
        battleLogic.saveBattleInfo(baseBattleInfo);
    }

    @Override
    public void start() {
        try {
            super.initClazz();
        } catch (IllegalAccessException | InstantiationException e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void saveSonStatus(BaseBattleInfo baseBattleInfo, int currentStatus) {
        ArenaBattleInfo battleInfo = (ArenaBattleInfo) baseBattleInfo;
        battleInfo.setCombatStatus(currentStatus);
    }

    @Override
    public boolean isGameServer() {
        return false;
    }

    @Override
    public boolean isWorldServer() {
        return false;
    }
}

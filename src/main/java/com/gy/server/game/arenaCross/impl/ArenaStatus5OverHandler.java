package com.gy.server.game.arenaCross.impl;

import com.gy.server.core.battleRule.base.IBattleStatusDeal;
import com.gy.server.core.battleRule.enums.BattleStep;
import com.gy.server.game.arenaCross.ArenaBattleInfo;

/**
 * <AUTHOR> - [Created on 2022-07-21 14:11]
 */
public class ArenaStatus5OverHandler implements IBattleStatusDeal<ArenaBattleInfo> {

    @Override
    public void init(ArenaBattleInfo baseBattleInfo) {

    }

    @Override
    public void deal(ArenaBattleInfo baseBattleInfo) {
        System.out.println("CrossArenaStatus4SendRewardHandler deal ..................");
    }


    @Override
    public boolean finish(ArenaBattleInfo baseBattleInfo) {
        boolean isFinish = false;
//        return isFinish ? nowStatus() : nextStatus();
        return isFinish;
    }

    @Override
    public int nowStatus() {
        return BattleStep.over.getTemplateId();
    }

    @Override
    public int nextStatus(ArenaBattleInfo arenaBattleInfo) {
        return BattleStep.init.getTemplateId();
    }

    @Override
    public long getStatusDurationTime(ArenaBattleInfo battleInfo) {
        return 0;
    }
}

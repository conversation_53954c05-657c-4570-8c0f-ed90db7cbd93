package com.gy.server.game.arenaCross.impl;

import com.gy.server.core.battleRule.base.IBattleStatusDeal;
import com.gy.server.core.battleRule.enums.BattleEnums;
import com.gy.server.core.battleRule.enums.BattleStep;
import com.gy.server.game.arenaCross.ArenaBattleInfo;
import com.gy.server.game.arenaCross.impl.battle.ArenaBattleManager;

/**
 * <AUTHOR> - [Created on 2022-07-21 14:11]
 */
public class ArenaStatus3BattleHandler implements IBattleStatusDeal<ArenaBattleInfo> {

    @Override
    public void init(ArenaBattleInfo baseBattleInfo) {

    }

    @Override
    public void deal(ArenaBattleInfo battleInfo) {
        System.out.println("CrossArenaStatus3BattleHandler deal ..................");


        ((ArenaBattleManager)BattleEnums.arena_cross_battle.getBattleLogic()).dealIng(battleInfo.getCombatStatus(), battleInfo);

    }


    @Override
    public boolean finish(ArenaBattleInfo baseBattleInfo) {
        /**
         * 1.所有战斗结束
         * 2.轮数完毕
         */

        boolean isFinish = false;
//        return isFinish ? nowStatus() : nextStatus();
        return isFinish;
    }

    @Override
    public int nowStatus() {
        return BattleStep.battle.getTemplateId();
    }

    @Override
    public int nextStatus(ArenaBattleInfo arenaBattleInfo) {
        return BattleStep.sendReward.getTemplateId();
    }

    @Override
    public long getStatusDurationTime(ArenaBattleInfo battleInfo) {
        return 0;
    }
}

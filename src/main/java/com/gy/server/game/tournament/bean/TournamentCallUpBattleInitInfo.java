package com.gy.server.game.tournament.bean;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.lineup.bean.LineupInfoBean;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.tournament.TournamentService;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbRecord;
import com.gy.server.packet.PbTournament;
import com.gy.server.utils.time.DateTimeUtil;
import com.gy.server.world.tournament.bean.TournamentPlayerMatchInfo;

import java.util.List;

/**
 * 实时pvp征召模式战斗初始化信息
 *
 * <AUTHOR> 2024/9/18 20:33
 **/
public class TournamentCallUpBattleInitInfo {

    /**
     * 阵型信息
     */
    private List<LineupInfoBean> atkLineupInfos;
    private List<LineupInfoBean> defLineupInfos;

    private PbRecord.RecordPlayer atkRecordPlayer;
    private PbRecord.RecordPlayer defRecordPlayer;

    /**
     * 匹配信息
     */
    private TournamentPlayerMatchInfo atkMatchInfo;
    private TournamentPlayerMatchInfo defMatchInfo;

    private MiniGamePlayer defMiniPlayer;

    /**
     * 禁用
     */
    private int atkDisable;
    private int defDisable;

    /**
     * 选用
     */
    private int atkSelect;
    private int defSelect;

    /**
     * 禁用完成时间
     */
    private long disableFinish;

    /**
     * 选用完成时间
     */
    private long selectFinish;

    private int atkServerId;
    private int defServerId;


    public TournamentCallUpBattleInitInfo(List<LineupInfoBean> atkLineupInfos, List<LineupInfoBean> defLineupInfos, PbRecord.RecordPlayer atkRecordPlayer, PbRecord.RecordPlayer defRecordPlayer, int atkServerId, int defServerId,
                                          TournamentPlayerMatchInfo atkMatchInfo, TournamentPlayerMatchInfo defMatchInfo, MiniGamePlayer defMiniPlayer) {
        this.atkLineupInfos = atkLineupInfos;
        this.defLineupInfos = defLineupInfos;
        this.atkRecordPlayer = atkRecordPlayer;
        this.defRecordPlayer = defRecordPlayer;
        this.disableFinish = ServerConstants.getCurrentTimeMillis() + TournamentService.getTournamentConst().disabledTime * DateTimeUtil.MillisOfSecond;
        this.selectFinish = this.disableFinish + TournamentService.getTournamentConst().selectionTime * DateTimeUtil.MillisOfSecond;
        this.atkServerId = atkServerId;
        this.defServerId = defServerId;
        this.atkMatchInfo = atkMatchInfo;
        this.defMatchInfo = defMatchInfo;
        this.defMiniPlayer = defMiniPlayer;
    }

    /**
     * 禁用选用处理
     * @param index
     * @param isSelect
     * @param isAtk
     */
    public void disableSelectDeal(int index, boolean isSelect, boolean isAtk){
        if(isSelect){
            if(isAtk){
                //选用己方队伍
                this.atkSelect = index;
            }else {
                this.defSelect = index;
            }
        }else {
            //禁用对方队伍
            if(isAtk){
                this.defDisable = index;
            }else {
                this.atkDisable = index;
            }
        }
    }


    public PbProtocol.TournamentDisableSelectNotify genTournamentDisableSelectNotify(){
        PbProtocol.TournamentDisableSelectNotify.Builder notify = PbProtocol.TournamentDisableSelectNotify.newBuilder();
        notify.addDsInfo(genTournamentDisableSelectInfo(true));
        notify.addDsInfo(genTournamentDisableSelectInfo(false));
        return notify.build();
    }

    private PbTournament.TournamentDisableSelectInfo genTournamentDisableSelectInfo(boolean isAtk){
        PbTournament.TournamentDisableSelectInfo.Builder builder = PbTournament.TournamentDisableSelectInfo.newBuilder();
        builder.setPlayerId(isAtk ? atkRecordPlayer.getId() : defRecordPlayer.getId());
        builder.setDisable(isAtk ? atkDisable : defDisable);
        builder.setSelect(isAtk ? atkSelect : defSelect);
        return builder.build();
    }

    public List<LineupInfoBean> getAtkLineupInfos() {
        return atkLineupInfos;
    }

    public void setAtkLineupInfos(List<LineupInfoBean> atkLineupInfos) {
        this.atkLineupInfos = atkLineupInfos;
    }

    public List<LineupInfoBean> getDefLineupInfos() {
        return defLineupInfos;
    }

    public void setDefLineupInfos(List<LineupInfoBean> defLineupInfos) {
        this.defLineupInfos = defLineupInfos;
    }

    public PbRecord.RecordPlayer getAtkRecordPlayer() {
        return atkRecordPlayer;
    }

    public void setAtkRecordPlayer(PbRecord.RecordPlayer atkRecordPlayer) {
        this.atkRecordPlayer = atkRecordPlayer;
    }

    public PbRecord.RecordPlayer getDefRecordPlayer() {
        return defRecordPlayer;
    }

    public void setDefRecordPlayer(PbRecord.RecordPlayer defRecordPlayer) {
        this.defRecordPlayer = defRecordPlayer;
    }

    public int getAtkDisable() {
        return atkDisable;
    }

    public void setAtkDisable(int atkDisable) {
        this.atkDisable = atkDisable;
    }

    public int getDefDisable() {
        return defDisable;
    }

    public void setDefDisable(int defDisable) {
        this.defDisable = defDisable;
    }

    public int getAtkSelect() {
        return atkSelect;
    }

    public void setAtkSelect(int atkSelect) {
        this.atkSelect = atkSelect;
    }

    public int getDefSelect() {
        return defSelect;
    }

    public void setDefSelect(int defSelect) {
        this.defSelect = defSelect;
    }

    public long getDisableFinish() {
        return disableFinish;
    }

    public void setDisableFinish(long disableFinish) {
        this.disableFinish = disableFinish;
    }

    public long getSelectFinish() {
        return selectFinish;
    }

    public void setSelectFinish(long selectFinish) {
        this.selectFinish = selectFinish;
    }

    public int getAtkServerId() {
        return atkServerId;
    }

    public void setAtkServerId(int atkServerId) {
        this.atkServerId = atkServerId;
    }

    public int getDefServerId() {
        return defServerId;
    }

    public void setDefServerId(int defServerId) {
        this.defServerId = defServerId;
    }

    public TournamentPlayerMatchInfo getAtkMatchInfo() {
        return atkMatchInfo;
    }

    public void setAtkMatchInfo(TournamentPlayerMatchInfo atkMatchInfo) {
        this.atkMatchInfo = atkMatchInfo;
    }

    public TournamentPlayerMatchInfo getDefMatchInfo() {
        return defMatchInfo;
    }

    public void setDefMatchInfo(TournamentPlayerMatchInfo defMatchInfo) {
        this.defMatchInfo = defMatchInfo;
    }

    public MiniGamePlayer getDefMiniPlayer() {
        return defMiniPlayer;
    }

    public void setDefMiniPlayer(MiniGamePlayer defMiniPlayer) {
        this.defMiniPlayer = defMiniPlayer;
    }
}

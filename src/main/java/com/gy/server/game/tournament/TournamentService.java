package com.gy.server.game.tournament;

import com.gy.server.core.Configuration;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.callback.response.CallbackResponse;
import com.gy.server.core.command.PlayerCommandRequest;
import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.common.ConstantConfigReader;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.keyword.KeyWordService;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.reddot.RedDot;
import com.gy.server.game.service.Service;
import com.gy.server.game.text.Text;
import com.gy.server.game.tournament.async.LiveRoomListAsync;
import com.gy.server.game.tournament.async.TournamentGetRankInfoAsync;
import com.gy.server.game.tournament.async.TournamentMatchStartAsync;
import com.gy.server.game.tournament.async.TournamentRankUpdateAsync;
import com.gy.server.game.tournament.bean.*;
import com.gy.server.game.tournament.template.*;
import com.gy.server.packet.PbLive;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbTournament;
import com.gy.server.world.live.LiveHelper;
import com.gy.server.world.live.LiveRoomManager;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.rpc.TLMessageCallbackTask;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.*;

/**
 * 实时pvp服务类
 *
 * <AUTHOR> 2024/9/9 20:05
 **/
public class TournamentService extends PlayerPacketHandler implements Service {

    /**
     * key:段位id
     */
    private static Map<Integer, TournamentMatchingRuleTemplate> matchingRuleMap = new HashMap<>();

    /**
     * key:星数
     */
    private static TreeMap<Integer, TournamentMatchingRuleTemplate> starMatchingRuleMap = new TreeMap<>();

    /**
     * key:段位段最大名词
     */
    private static TreeMap<Integer, TournamentRankingTemplate> rankingMap = new TreeMap<>();

    /**
     * key:奖励类型     value: key:奖励条件类型 value: key:奖励条件参数
     */
    private static Map<TournamentRewardTypeEnum, Map<TournamentRewardConditionTypeEnum, Map<Integer, TournamentRankingRewardTemplate>>> rankingRewardMap = new HashMap<>();

    /**
     * key:id
     */
    private static Map<Integer, TournamentHonoraryTitleTemplate> honoraryTitleMap = new HashMap<>();

    /**
     * 机器人map
     * key:段位id
     */
    private static Map<Integer, TournamentRobotTemplate> robotTemplateMap = new HashMap<>();

    private static TournamentConst tournamentConst;

    /**
     * 最小段位模版
     */
    private static TournamentMatchingRuleTemplate minRuleTemplate;

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.PVPRealTime_MatchingRule);
        Map<Integer, TournamentMatchingRuleTemplate> matchingRuleMapTemp = new HashMap<>();
        TreeMap<Integer, TournamentMatchingRuleTemplate> starMatchingRuleMapTemp = new TreeMap<>();
        for (Map<String, String> map : mapList) {
            TournamentMatchingRuleTemplate template = new TournamentMatchingRuleTemplate(map);
            //0星特殊处理
            if(template.stars.getLeft() <= 0 && template.stars.getRight() >= 0){
                matchingRuleMapTemp.put(0, template);
            }
            matchingRuleMapTemp.put(template.tierId, template);
            starMatchingRuleMapTemp.put(template.stars.getRight(), template);
        }
        matchingRuleMap = matchingRuleMapTemp;
        starMatchingRuleMap = starMatchingRuleMapTemp;
        int minDanId = matchingRuleMap.keySet().stream().min(Integer::compare).get();
        minRuleTemplate = matchingRuleMapTemp.get(minDanId);

        mapList = ConfigReader.read(ConfigFile.PVPRealTime_Ranking);
        TreeMap<Integer, TournamentRankingTemplate> rankingMapTemp = new TreeMap<>();
        for (Map<String, String> map : mapList) {
            TournamentRankingTemplate template = new TournamentRankingTemplate(map);
            rankingMapTemp.put(template.ranking.getRight(), template);
        }
        rankingMap = rankingMapTemp;

        mapList = ConfigReader.read(ConfigFile.PVPRealTime_RankingReward);
        Map<TournamentRewardTypeEnum, Map<TournamentRewardConditionTypeEnum, Map<Integer, TournamentRankingRewardTemplate>>> rankingRewardMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            TournamentRankingRewardTemplate template = new TournamentRankingRewardTemplate(map);
            rankingRewardMapTemp.computeIfAbsent(template.rewardType, map1 -> new HashMap<>()).computeIfAbsent(template.conditionType, map2 -> new HashMap<>()).put(template.condition, template);
        }
        rankingRewardMap = rankingRewardMapTemp;

        mapList = ConfigReader.read(ConfigFile.PVPRealTime_HonoraryTitle);
        Map<Integer, TournamentHonoraryTitleTemplate> honoraryTitleMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            TournamentHonoraryTitleTemplate template = new TournamentHonoraryTitleTemplate(map);
            honoraryTitleMapTemp.put(template.id, template);
        }
        honoraryTitleMap = honoraryTitleMapTemp;

        mapList = ConfigReader.read(ConfigFile.PVPRealTime_robot);
        Map<Integer, TournamentRobotTemplate> robotTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            TournamentRobotTemplate template = new TournamentRobotTemplate(map);
            robotTemplateMapTemp.put(template.points, template);
        }
        robotTemplateMap = robotTemplateMapTemp;

        Map<String, String> map = ConstantConfigReader.read(ConfigFile.PVPRealTime_const);
        tournamentConst = new TournamentConst(map);
    }

    @Override
    public void clearConfigData() {
        matchingRuleMap.clear();
        rankingMap.clear();
        honoraryTitleMap.clear();
        starMatchingRuleMap.clear();
        tournamentConst = null;
        minRuleTemplate = null;
    }



    /**
     * 实时pvp信息
     */
    @Handler(PtCode.TOURNAMENT_INFO_REQ)
    private void tournamentInfoReq(Player player, PbProtocol.TournamentInfoReq req, long time){
        PbProtocol.TournamentInfoRst.Builder rst = PbProtocol.TournamentInfoRst.newBuilder().setResult(Text.genOkServerRstInfo());
        rst.setModel(player.getPlayerTournamentModel().genPb());
        player.send(PtCode.TOURNAMENT_INFO_RST, rst.build(), time);
    }

    /**
     * 实时pvp匹配处理
     */
    @Handler(PtCode.TOURNAMENT_MATCH_REQ)
    private void tournamentMatchReq(Player player, PbProtocol.TournamentMatchReq req, long time){
        ThreadPool.execute(new TournamentMatchStartAsync(player, req, time, true));
    }

    /**
     * pvp禁用选择阵容
     */
    @Handler(PtCode.TOURNAMENT_DISABLE_SELECT_LINEUP_REQ)
    private void tournamentDisableSelectLineupReq(Player player, PbProtocol.TournamentDisableSelectLineupReq req, long time){
        PbProtocol.TournamentDisableSelectLineupRst.Builder rst = PbProtocol.TournamentDisableSelectLineupRst.newBuilder().setResult(Text.genOkServerRstInfo());
        int index = req.getIndex();
        boolean isSelect = req.getIsSelect();

        logic:{
            rst.setIndex(index);
            rst.setIsSelect(isSelect);

            if(index < 1 || index > 3){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }

            CallUpBattleInitInfoIndex indexInfo = TournamentManager.getInstance().getIndexMap().get(player.getPlayerId());
            if(indexInfo == null){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }

            if(indexInfo.getAtkServerId() == Configuration.serverId){
                //进行战斗的服务器是本服
                TournamentCallUpBattleInitInfo initInfo = TournamentManager.getInstance().getCallUpMap().get(indexInfo);
                if(initInfo == null){
                    rst.setResult(Text.genServerRstInfo(Text.参数异常));
                    break logic;
                }

                long finishTime = isSelect ? initInfo.getSelectFinish() : initInfo.getDisableFinish();
                if(ServerConstants.getCurrentTimeMillis() >= finishTime){
                    rst.setResult(Text.genServerRstInfo(isSelect ? Text.选用已超时 : Text.禁用已超时));
                    break logic;
                }

                boolean isAtk = player.getPlayerId() == initInfo.getAtkRecordPlayer().getId();

                //是否已禁用
                if(!isSelect && ((isAtk && initInfo.getDefDisable() != 0) || (!isAtk && initInfo.getAtkDisable() != 0))){
                    rst.setResult(Text.genServerRstInfo(Text.实时pvp已进行禁用));
                    break logic;
                }

                //是否已选用
                if(isSelect && ((isAtk && initInfo.getAtkSelect() != 0) || (!isAtk && initInfo.getDefSelect() != 0))){
                    rst.setResult(Text.genServerRstInfo(Text.实时pvp已进行禁用));
                    break logic;
                }

                //是否选用已被禁用
                if(isSelect && ((isAtk && initInfo.getAtkDisable() == index) || (!isAtk && initInfo.getDefDisable() == index))){
                    rst.setResult(Text.genServerRstInfo(Text.此队伍已被禁用));
                    break logic;
                }

                initInfo.disableSelectDeal(index, isSelect, isAtk);

                //禁选用通知
                TournamentHelper.disableSelectNotify(initInfo);
            }else {
                //进行战斗的服务器是跨服
                PlayerCommandRequest request = CommandRequests.newPlayerCommandRequest("TournamentRstCommandService.callUpDisableSelect", player.getPlayerId());
                TLBase.getInstance().getRpcUtil().sendToNodeWithCallBack(new TournamentDisableCallbackTask(rst), ServerType.GAME, indexInfo.getAtkServerId(), request, indexInfo, player.getPlayerId(), index, isSelect);
            }

            player.send(PtCode.TOURNAMENT_DISABLE_SELECT_LINEUP_RST, rst.build(), time);
            return;
        }

        player.send(PtCode.TOURNAMENT_DISABLE_SELECT_LINEUP_RST, rst.build(), time);
    }

    /**
     * pvp玩家更改定位
     */
    @Handler(PtCode.TOURNAMENT_CHANGE_POSITION_REQ)
    private void tournamentChangePositionReq(Player player, PbProtocol.TournamentChangePositionReq req, long time){
        PbProtocol.TournamentChangePositionRst.Builder rst = PbProtocol.TournamentChangePositionRst.newBuilder().setResult(Text.genOkServerRstInfo());
        PbTournament.PositionInfo position = req.getPosition();

        logic:{
            //TODO 核对地点

            PlayerTournamentModel model = player.getPlayerTournamentModel();
            //核对当前时间能否更改地点
            if(model.getPositionChangeNum() > tournamentConst.positioningRefresh){
                rst.setResult(Text.genServerRstInfo(Text.实时pvp本周更改定位次数已用完));
                break logic;
            }

            TournamentPositionInfo newPosition = new TournamentPositionInfo(position);
            model.setPositionInfo(newPosition);
            model.addPositionChangeNum();

            rst.setPosition(newPosition.genPb());

            //处理排行榜
            TournamentRankUpdateAsync.deal(model);
            model.sync();
        }

        player.send(PtCode.TOURNAMENT_CHANGE_POSITION_RST, rst.build(), time);
    }

    /**
     * pvp玩家更改pvp称号
     */
    @Handler(PtCode.TOURNAMENT_CHANGE_TTITLE_REQ)
    private void tournamentChangeTTitleReq(Player player, PbProtocol.TournamentChangeTTitleReq req, long time){
        PbProtocol.TournamentChangeTTitleRst.Builder rst = PbProtocol.TournamentChangeTTitleRst.newBuilder().setResult(Text.genOkServerRstInfo());
        String key = req.getKey();

        logic:{

            PlayerTournamentModel model = player.getPlayerTournamentModel();
            if(key == null || key.equals("")){
                //脱称号
                if(model.getTitleKey() == null || model.getTitleKey().equals("")){
                    rst.setResult(Text.genServerRstInfo(Text.实时pvp未穿戴称号));
                    break logic;
                }
            }else {
                if(!model.haveTitle(key)){
                    rst.setResult(Text.genServerRstInfo(Text.实时pvp称号不存在));
                    break logic;
                }
            }

            rst.setKey(key);
            model.setTitleKey(key);
            model.sync();
        }
        player.send(PtCode.TOURNAMENT_CHANGE_TTITLE_RST, rst.build(), time);
    }

    /**
     * 实时pvp排行榜查看
     */
    @Handler(PtCode.TOURNAMENT_RANK_INFO_REQ)
    private void tournamentRankInfoReq(Player player, PbProtocol.TournamentRankInfoReq req, long time){
        PbProtocol.TournamentRankInfoRst.Builder rst = PbProtocol.TournamentRankInfoRst.newBuilder().setResult(Text.genOkServerRstInfo());
        int seasonId = req.getSeasonId();
        PbTournament.PositionInfo position = req.getPosition();

        logic:{
            //TODO 核对地点

            PlayerTournamentModel model = player.getPlayerTournamentModel();
            if(seasonId > model.getSeasonId()){
                rst.setResult(Text.genServerRstInfo(Text.实时PVP此赛季尚未开启));
                break logic;
            }

            if (model.getPositionInfo() == null) {
                rst.setResult(Text.genServerRstInfo(Text.尚未进行定位));
                break logic;
            }

            ThreadPool.execute(new TournamentGetRankInfoAsync(player, seasonId, new TournamentPositionInfo(position), time));
            return;
        }

        player.send(PtCode.TOURNAMENT_RANK_INFO_RST, rst.build(), time);
    }

    /**
     * 实时pvp领取段位奖励
     */
    @Handler(PtCode.TOURNAMENT_DRAW_DAN_REWARD_REQ)
    private void tournamentDrawDrawDanRewardReq(Player player, PbProtocol.TournamentDrawDanRewardReq req, long time){
        PbProtocol.TournamentDrawDanRewardRst.Builder rst = PbProtocol.TournamentDrawDanRewardRst.newBuilder().setResult(Text.genOkServerRstInfo());
        int danId = req.getDanId();
        rst.setDanId(danId);

        logic:{
            TournamentMatchingRuleTemplate matchingRuleTemplate = matchingRuleMap.get(danId);
            if(matchingRuleTemplate == null){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }

            PlayerTournamentModel model = player.getPlayerTournamentModel();
            if(model.getReceiveIds().contains(danId)){
                rst.setResult(Text.genServerRstInfo(Text.奖励已领取));
                break logic;
            }

            int nowDanId = getDanByStar(model.getMaxStar());
            if(danId > nowDanId){
                rst.setResult(Text.genServerRstInfo(Text.实时pvp最高段位未达到要求));
                break logic;
            }

            List<Reward> rewards = getRankingRewardByParam(TournamentRewardTypeEnum.dan, TournamentRewardConditionTypeEnum.dan, danId);
            if(rewards.isEmpty()){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }

            model.getReceiveIds().add(danId);
            Reward.merge(rewards);
            Reward.add(rewards, player, BehaviorType.PVPRealTimeDanReward);
            rst.addAllRewards(Reward.writeCollectionToPb(rewards));
            model.sync();

            //红点
            RedDot.realtimeArenaReward.sync(player);
        }

        player.send(PtCode.TOURNAMENT_DRAW_DAN_REWARD_RST, rst.build(), time);
    }

    @Handler(PtCode.TOURNAMENT_LIVE_ROOMS_REQ)
    private void liveRoomList(Player player, PbProtocol.TournamentLiveRoomsReq req, long time){
        AsyncCall call = new LiveRoomListAsync(player, time);
        ThreadPool.execute(call);
    }

    @Handler(PtCode.LIVE_ROOM_ENTER_REQ)
    private void liveRoomEnter(Player player, PbProtocol.LiveRoomEnterReq req, long time){
        long roomId = req.getRoomId();
        PbProtocol.LiveRoomEnterRst.Builder rst = PbProtocol.LiveRoomEnterRst.newBuilder().setResult(Text.genOkServerRstInfo());
        player.send(PtCode.LIVE_ROOM_ENTER_RST, rst.build(), time);

        ThreadPool.execute(()->{
            LiveRoomManager.getInstance().enterRoom(player.getPlayerId(), roomId);
        });
    }

    @Handler(PtCode.LIVE_ROOM_EXIT_REQ)
    private  void liveRoomExit(Player player, PbProtocol.LiveRoomExitReq req, long time){
        PbProtocol.LiveRoomExitRst.Builder rst = PbProtocol.LiveRoomExitRst.newBuilder().setResult(Text.genOkServerRstInfo());
        rst.setResult(Text.genOkServerRstInfo());
        player.send(PtCode.LIVE_ROOM_EXIT_RST, rst.build(), time);

        ThreadPool.execute(()->{
            LiveRoomManager.getInstance().exitRoom(player.getPlayerId());
        });
    }

    @Handler(PtCode.LIVE_CHAT_SEND_REQ)
    private void liveChatSend(Player player, PbProtocol.LiveChatSendReq req, long time){
        PbProtocol.LiveChatSendRst.Builder rst = PbProtocol.LiveChatSendRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:
        {
            long roomId = req.getRoomId();
            String content = req.getContent();
            if (KeyWordService.contains(content)) {
                rst.setResult(Text.genServerRstInfo(Text.弹幕包含非法字符));
                break logic;
            }

            //CD检查
            if(!LiveRoomManager.getInstance().chatCDCheck(player.getPlayerId())){
                rst.setResult(Text.genServerRstInfo(Text.弹幕发送过于频繁));
                break logic;
            }else{
                LiveRoomManager.getInstance().addChatTime(player.getPlayerId());
            }

            PbProtocol.LiveDataPushRst.Builder chat = PbProtocol.LiveDataPushRst.newBuilder();
            chat.setResult(Text.genOkServerRstInfo())
                    .addData(PbLive.LiveData.newBuilder().setType(PbLive.LiveData.LiveDataType.CHAT_MSG).addChatMsg(content));

            ThreadPool.execute(()-> {
                int code =LiveHelper.writeLiveData(roomId, PtCode.LIVE_DATA_PUSH_RST, chat.build());
                rst.setResult(Text.genServerRstInfo(code));
                player.send(PtCode.LIVE_CHAT_SEND_RST, rst.build(), time);
            });
            return;
        }
        player.send(PtCode.LIVE_CHAT_SEND_RST, rst.build(), time);
    }

    public static TournamentMatchingRuleTemplate getMatchingRuleTemplateByStar(int star){
       Integer firstKey = starMatchingRuleMap.tailMap(star).firstKey();
       return firstKey == null ? starMatchingRuleMap.lastEntry().getValue() : starMatchingRuleMap.get(firstKey);
    }

    /**
     * 获取失败保护次数
     * @return
     */
    public static int getFailureProtectionNum(int star){
        return star <= tournamentConst.ordinary ? -1 : star > tournamentConst.failureProtection.getLeft() ? tournamentConst.failureProtection.getRight() : 0;
    }

    public static int getDanByStar(int starNum) {
        return getMatchingRuleTemplateByStar(starNum).tierId;
    }

    /**
     * 根据条件获取对应的奖励
     * @param rewardType
     * @param conditionType
     * @param condition
     * @return
     */
    public static List<Reward> getRankingRewardByParam(TournamentRewardTypeEnum rewardType, TournamentRewardConditionTypeEnum conditionType, int condition){
        List<Reward> rewards = new ArrayList<>();
        int conditionNum = condition;

        if(conditionType == TournamentRewardConditionTypeEnum.rank){
            TournamentRankingTemplate rankingTemplate = getTournamentRankingTemplateByRank(conditionNum);
            if(rankingTemplate == null){
                return rewards;
            }
            conditionNum = rankingTemplate.id;
        }

        TournamentRankingRewardTemplate template = rankingRewardMap.getOrDefault(rewardType, new HashMap<>()).getOrDefault(conditionType, new HashMap<>()).get(conditionNum);
        if(template != null){
            rewards.addAll(RewardTemplate.createRewards(template.rewards));
        }
        return rewards;
    }

    public static TournamentRankingTemplate getTournamentRankingTemplateByRank(int rank){
        Integer firstKey = rankingMap.tailMap(rank).firstKey();
        return rankingMap.get(firstKey);
    }

    /**
     * 获取品质保存数量map
     * @return <品质，保存数量>
     */
    public static Map<Integer, Integer> getQualityNumMap() {
        Map<Integer, Integer> map = new HashMap<>();
        for (TournamentHonoraryTitleTemplate bean : honoraryTitleMap.values()) {
            map.put(bean.quality, bean.rankingNum);
        }
        return map;
    }

    @Override
    public boolean isWorldServer() {
        return true;
    }


    public static Map<Integer, TournamentMatchingRuleTemplate> getMatchingRuleMap() {
        return matchingRuleMap;
    }

    public static TreeMap<Integer, TournamentRankingTemplate> getRankingMap() {
        return rankingMap;
    }

    public static Map<Integer, TournamentHonoraryTitleTemplate> getHonoraryTitleMap() {
        return honoraryTitleMap;
    }

    public static TournamentConst getTournamentConst() {
        return tournamentConst;
    }

    public static TournamentMatchingRuleTemplate getMinRuleTemplate() {
        return minRuleTemplate;
    }

    public static TreeMap<Integer, TournamentMatchingRuleTemplate> getStarMatchingRuleMap() {
        return starMatchingRuleMap;
    }

    public static Map<TournamentRewardTypeEnum, Map<TournamentRewardConditionTypeEnum, Map<Integer, TournamentRankingRewardTemplate>>> getRankingRewardMap() {
        return rankingRewardMap;
    }

    public static Map<Integer, TournamentRobotTemplate> getRobotTemplateMap() {
        return robotTemplateMap;
    }

    public static TournamentRobotTemplate getRobotByDan(int danId){
        if(robotTemplateMap.containsKey(danId)){
            return robotTemplateMap.get(danId);
        }

        int newDanId = robotTemplateMap.keySet().stream().filter(id -> id <= danId).max(Integer::compare).orElse(1);
        return robotTemplateMap.get(newDanId);
    }

    private static class TournamentDisableCallbackTask extends TLMessageCallbackTask {
        private final PbProtocol.TournamentDisableSelectLineupRst.Builder rst;

        public TournamentDisableCallbackTask(PbProtocol.TournamentDisableSelectLineupRst.Builder rst) {
            this.rst = rst;
        }

        @Override
        public void complete(CallbackResponse response) {
            int text = response.getParam(0);
            if(text != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(text));
            }
        }

        @Override
        public void timeout() {
            rst.setResult(Text.genServerRstInfo(Text.匹配超时));
        }
    }
}

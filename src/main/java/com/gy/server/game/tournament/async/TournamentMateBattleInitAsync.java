package com.gy.server.game.tournament.async;

import com.gy.server.core.Configuration;
import com.gy.server.core.command.PlayerCommandRequest;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.combat.CombatManager;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.lineup.bean.LineupInfoBean;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.rank.RankManager;
import com.gy.server.game.rank.RankType;
import com.gy.server.game.record.RecordManager;
import com.gy.server.game.text.Text;
import com.gy.server.game.tournament.TournamentHelper;
import com.gy.server.game.tournament.TournamentManager;
import com.gy.server.game.tournament.bean.TournamentMatchEnum;
import com.gy.server.game.tournament.stage.TournamentStage;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbRecord;
import com.gy.server.utils.time.DateTimeUtil;
import com.gy.server.world.tournament.bean.TournamentPlayerMatchInfo;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;

import java.util.List;

/**
 * 实时pvp匹配模式战斗初始化-异步
 *
 * <AUTHOR> 2024/9/18 11:18
 **/
public class TournamentMateBattleInitAsync extends AsyncCall {

    /**
     * 匹配信息
     */
    private TournamentPlayerMatchInfo atkMatchInfo;
    private TournamentPlayerMatchInfo defMatchInfo;

    /**
     * 错误码
     */
    private int text;

    /**
     * 匹配成功时间点
     */
    private long matchSuccessTime;

    /**
     * 进攻阵容 List<LineupInfoBean> atkLineup, List<LineupInfoBean> defLineup
     */
    private List<LineupInfoBean> atkLineup;
    /**
     * 防守阵容
     */
    private List<LineupInfoBean> defLineup;

    private PbRecord.RecordPlayer atkRecordPlayer;
    private PbRecord.RecordPlayer defRecordPlayer;

    private LineupType lineupType = LineupType.PVPRealTime1;

    private MiniGamePlayer atkMiniPlayer;
    private MiniGamePlayer defMiniPlayer;

    public TournamentMateBattleInitAsync(TournamentPlayerMatchInfo atkMatchInfo, TournamentPlayerMatchInfo defMatchInfo, int text, long matchSuccessTime) {
        this.atkMatchInfo = atkMatchInfo;
        this.defMatchInfo = defMatchInfo;
        this.text = text;
        this.matchSuccessTime = matchSuccessTime;
    }

    @Override
    public void execute() {
        //不管成不成功，gs匹配池清除atk
        TournamentManager.getInstance().getGsMatchPlayerMap().remove(atkMatchInfo.getPlayerId());

        if(text != Text.没有异常){
            TournamentHelper.matchResultNotify(text, TournamentMatchEnum.mate, null, null, lineupType, atkMatchInfo, defMatchInfo, matchSuccessTime, atkLineup, defLineup);
            return;
        }

        if(atkMiniPlayer == null || atkLineup == null || (defMatchInfo != null && (defMiniPlayer == null || defLineup == null))){
            TournamentHelper.matchResultNotify(Text.匹配失败, TournamentMatchEnum.mate, null, null, lineupType, atkMatchInfo, defMatchInfo, matchSuccessTime, atkLineup, defLineup);
            return;
        }

        //TODO
        TournamentStage stage = new TournamentStage(atkMatchInfo, defMatchInfo, atkLineup, defLineup, atkMiniPlayer, defMiniPlayer);
        stage.init();
        CombatManager.combatPrepare(stage);

        atkRecordPlayer = RecordManager.genRecordPlayer(atkMiniPlayer, lineupType, stage.getAtks()).build();
        if(defMiniPlayer != null){
            defRecordPlayer = RecordManager.genRecordPlayer(defMiniPlayer, lineupType, stage.getDefs()).build();
        }

        TournamentHelper.matchResultNotify(text, TournamentMatchEnum.mate, atkRecordPlayer, defRecordPlayer, lineupType, atkMatchInfo, defMatchInfo, matchSuccessTime, atkLineup, defLineup, stage.getRobotTemplate());

        stageInitNotify(stage, atkMatchInfo.getPlayerId(), defMatchInfo, matchSuccessTime);
    }

    @Override
    public void asyncExecute() {
        if(text != Text.没有异常){
            return;
        }

        Player atkPlayer = PlayerManager.getOnlinePlayer(atkMatchInfo.getPlayerId());
        if(atkPlayer == null){
            return;
        }

        atkMiniPlayer = atkPlayer.getMiniPlayer();
        atkLineup = lineupType.getLineup(atkPlayer);


        //机器人
        if(defMatchInfo == null){
            long defPlayerId = RankManager.getLineupByFightPowerRankRank(atkPlayer, atkMatchInfo.isRobotA());
            if(defPlayerId != 0l){
                defLineup = lineupType.getLineup(defPlayerId);
                if(defLineup == null){
                    defLineup = LineupType.Common.getLineup(defPlayerId);
                }
                defMiniPlayer = PlayerHelper.getMiniPlayer(defPlayerId);

//                defRecordPlayer = pair.getLeft();
//                defLineup = pair.getRight();
//                defMiniPlayer = PlayerManager.getMiniPlayer(defMatchInfo.getPlayerId());
            }
        }else {
            //防守方
            defLineup = lineupType.getLineup(defMatchInfo.getPlayerId());
            defMiniPlayer = PlayerHelper.getMiniPlayer(defMatchInfo.getPlayerId());

//            if(defMatchInfo.getServerId() == Configuration.serverId){
//                //本服
//                Player defPlayer = PlayerManager.getPlayer(defMatchInfo.getPlayerId());
//                if(defPlayer != null){
//                    defLineup = lineupType.getLineup(defPlayer);
////                    defLineup = defLineInfoBean.get(0);
//                    defRecordPlayer = RecordManager.genRecordPlayer(defPlayer, false, lineupType, null).build();
//                    defMiniPlayer = PlayerManager.getMiniPlayer(defMatchInfo.getPlayerId());
//                }
//            }else {
//                //跨服
//                PlayerCommandRequest request = CommandRequests.newPlayerCommandRequest("TournamentRstCommandService.getBattleInitInfo", defMatchInfo.getPlayerId());
//                TLBase.getInstance().getRpcUtil().sendToNodeWithCallBack(new ExecuteCallbackTask(), ServerType.GAME, defMatchInfo.getServerId(), request, defMatchInfo.getPlayerId());
//            }
        }
    }

    public static void stageInitNotify(TournamentStage stage, long atkPlayerId, TournamentPlayerMatchInfo defMatchInfo, long matchSuccessTime){
        //通知战斗双方
        PbProtocol.TournamentBattleInitNotify.Builder battleNotify = PbProtocol.TournamentBattleInitNotify.newBuilder().setResult(Text.genOkServerRstInfo());
        battleNotify.setStage(stage.genAbstractPb());

        Player atkPlayer = PlayerManager.getOnlinePlayer(atkPlayerId);
        if(atkPlayer != null){
            atkPlayer.send(PtCode.TOURNAMENT_BATTLE_INIT_NOTIFY, battleNotify.build());
        }

        if(defMatchInfo != null){
            int defMatchUseTime = (int) ((matchSuccessTime - defMatchInfo.getStart()) / DateTimeUtil.MillisOfSecond);
            if(Configuration.serverId == defMatchInfo.getServerId()){
                //本服
                Player defPlayer = PlayerManager.getOnlinePlayer(defMatchInfo.getPlayerId());
                defPlayer.send(PtCode.TOURNAMENT_BATTLE_INIT_NOTIFY, battleNotify.build());
            }else {
                //跨服
                PlayerCommandRequest request = CommandRequests.newPlayerCommandRequest("TournamentRstCommandService.NotifyBattleInit", defMatchInfo.getPlayerId());
                TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, defMatchInfo.getServerId(), request, defMatchInfo.getPlayerId(), battleNotify.build(), defMatchUseTime);
            }
        }
    }

//    private class ExecuteCallbackTask extends TLMessageCallbackTask {
//        @Override
//        public void complete(CallbackResponse response) {
//            defLineup = response.getParam(0);
////                        defLineup = defLineInfoBean.get(0);
//            defRecordPlayer = response.getParam(1);
//            defMiniPlayer = response.getParam(2);
//        }
//
//        @Override
//        public void timeout() {
//
//        }
//    }
}

package com.gy.server.game.tournament.template;

import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.tournament.bean.TournamentRewardConditionTypeEnum;
import com.gy.server.game.tournament.bean.TournamentRewardTypeEnum;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 排行奖励
 *
 * <AUTHOR> 2024/10/14 13:25
 **/
public class TournamentRankingRewardTemplate {

    public int id;

    public TournamentRewardTypeEnum rewardType;

    public TournamentRewardConditionTypeEnum conditionType;

    /**
     * 条件，段位id/排名id
     */
    public int condition;

    public List<RewardTemplate> rewards = new ArrayList<>();

    public TournamentRankingRewardTemplate(Map<String, String> map){
        this.id = Integer.parseInt(map.get("id"));
        this.rewardType = TournamentRewardTypeEnum.getTournamentRewardTypeEnum(Integer.parseInt(map.get("rewardType")));
        this.conditionType = TournamentRewardConditionTypeEnum.getTournamentRewardConditionTypeEnum(Integer.parseInt(map.get("conditionType")));
        this.condition = Integer.parseInt(map.get("rewardCondition"));
        this.rewards = RewardTemplate.readListFromText(map.get("reward"));

        if(rewardType == null || conditionType == null){
            throw new IllegalArgumentException("TournamentRankingRewardTemplate rewardType or conditionType is error, id is " + id);
        }
    }
}

package com.gy.server.game.tournament;

import com.gy.server.game.global.GlobalData;
import com.gy.server.game.tournament.bean.TournamentPlayerRankInfo;
import com.gy.server.game.tournament.status.TournamentBattleInfo;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.ttlike.server.tl.baselib.serialize.tournament.TournamentBattleInfoDb;
import com.ttlike.server.tl.baselib.serialize.tournament.TournamentGlobalDb;
import com.ttlike.server.tl.baselib.serialize.tournament.TournamentPlayerRankInfoDb;
import com.ttlike.server.tl.baselib.serialize.tournament.TournamentTitleSettlementInfoDb;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * 实时pvp
 *
 * <AUTHOR> 2024/9/14 13:19
 **/
public class TournamentGlobalData extends GlobalData {

    private TournamentBattleInfo battleInfo;

    @Override
    public void readFromPb(byte[] bytes) throws Exception {
        TournamentGlobalDb globalDb = PbUtilCompress.decode(TournamentGlobalDb.class, bytes);
        if(Objects.nonNull(globalDb)){
            TournamentBattleInfoDb battleInfoDb = globalDb.getBattleInfoDb();
            if(Objects.nonNull(battleInfoDb)){
                TournamentBattleInfo battleInfoTemp = new TournamentBattleInfo();
                battleInfoTemp.setSeasonId(battleInfoDb.getSeasonId());
                battleInfoTemp.setStatusStartTime(battleInfoDb.getStatusStartTime());
                battleInfoTemp.setStatus(battleInfoDb.getStatus());

                for (TournamentPlayerRankInfoDb bean : battleInfoDb.getCountryRankInfos()) {
                    battleInfoTemp.getCountryRankInfos().add(new TournamentPlayerRankInfo(bean));
                }
                for (TournamentTitleSettlementInfoDb bean : battleInfoDb.getTitleSettlements()) {
                    battleInfoTemp.getTitleSettlementMap().computeIfAbsent(bean.getId(), set -> new CopyOnWriteArraySet<>()).addAll(bean.getDays());
                }
                battleInfo = battleInfoTemp;
            }
        }
    }

    @Override
    public byte[] writeToPb() {
        TournamentGlobalDb db = new TournamentGlobalDb();
        if(battleInfo != null){
            TournamentBattleInfoDb battleInfoDb = new TournamentBattleInfoDb();
            battleInfoDb.setSeasonId(battleInfo.getSeasonId());
            battleInfoDb.setStatusStartTime(battleInfo.getStatusStartTime());
            battleInfoDb.setStatus(battleInfo.getStatus());
            for (TournamentPlayerRankInfo bean : battleInfo.getCountryRankInfos()) {
                battleInfoDb.getCountryRankInfos().add(bean.genDb());
            }
            for (Map.Entry<Integer, CopyOnWriteArraySet<Integer>> entry : battleInfo.getTitleSettlementMap().entrySet()) {
                battleInfoDb.getTitleSettlements().add(new TournamentTitleSettlementInfoDb(entry.getKey(), entry.getValue()));
            }
            db.setBattleInfoDb(battleInfoDb);
        }
        return PbUtilCompress.encode(db);

    }


    public TournamentBattleInfo getBattleInfo() {
        if(battleInfo == null){
            battleInfo = new TournamentBattleInfo();
        }
        return battleInfo;
    }

    public void setBattleInfo(TournamentBattleInfo battleInfo) {
        this.battleInfo = battleInfo;
    }
}

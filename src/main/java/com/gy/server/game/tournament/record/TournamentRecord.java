package com.gy.server.game.tournament.record;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.record.Record;
import com.gy.server.game.record.RecordHelper;
import com.gy.server.game.record.RecordType;
import com.gy.server.game.robot.RobotHelper;
import com.gy.server.game.robot.RobotType;
import com.gy.server.game.robot.bean.BaseRobot;
import com.gy.server.game.tournament.bean.TournamentMatchEnum;
import com.gy.server.game.tournament.template.TournamentRobotTemplate;
import com.gy.server.packet.PbRecord;
import com.gy.server.packet.PbRobot;
import com.ttlike.server.tl.baselib.serialize.record.RecordDb;
import com.ttlike.server.tl.baselib.serialize.record.TournamentRecordDb;
import org.apache.commons.lang3.tuple.Pair;

import java.io.Serializable;
import java.util.Set;

/**
 * 实时pvp匹配模式战报
 *
 * <AUTHOR> 2024/9/19 16:07
 **/
public class TournamentRecord extends Record implements Serializable {

    private static final long serialVersionUID = -3520721959233811401L;

    /**
     * 战斗类型，匹配/征召
     */
    private int matchType;

    /**
     * 攻击方
     */
    private PbRecord.RecordPlayer atkUser;

    /**
     * 防守方
     */
    private PbRecord.RecordPlayer defUser;

    /**
     * 是否是机器人
     */
    private boolean robot;

    /**
     * 机器人信息
     */
    private PbRobot.BaseRobot robotInfo;

    /**
     * 攻击方现在星数
     */
    private int atkNowStar;

    /**
     * 攻击方变化星数
     */
    private int atkChangeStar;

    /**
     * 防守方现在星数
     */
    private int defNowStar;

    /**
     * 防守方变化星数
     */
    private int defChangeStar;

    public TournamentRecord() {

    }

    public TournamentRecord(String cosKey, boolean isWin, long combatRecordId, int serverId, PbRecord.RecordPlayer atkUser, PbRecord.RecordPlayer defUser, LineupType lineupType, TournamentRobotTemplate robotTemplate, Pair<Integer, Integer> atkPair, Pair<Integer, Integer> defPair) {
        super(RecordType.PVPRealTime.getId(), ServerConstants.getCurrentTimeMillis(), isWin, combatRecordId, serverId, cosKey);
        this.atkUser = atkUser;
        this.defUser = defUser;
        this.matchType = lineupType == LineupType.PVPRealTime1 ? TournamentMatchEnum.mate.getType() : TournamentMatchEnum.callUp.getType();
        this.atkNowStar = atkPair.getLeft();
        this.atkChangeStar = atkPair.getRight();
        this.defNowStar = defPair.getLeft();
        this.defChangeStar = defPair.getRight();
        if(robotTemplate != null){
            this.robot = true;

            BaseRobot baseRobot = RobotHelper.getRobotBean(RobotType.ARENA,
                    robotTemplate.robotId);
            this.robotInfo = baseRobot.genPb();
        }
    }

    @Override
    protected void subReadFromPb(PbRecord.Record record) {
        PbRecord.TournamentRecord bean = record.getTournamentRecord();
        this.atkUser = bean.getAtk();
        this.defUser = bean.getDef();
        this.matchType = bean.getMatchType();
    }

    @Override
    protected void subWriteToPb(PbRecord.Record.Builder builder) {
        PbRecord.TournamentRecord.Builder record = PbRecord.TournamentRecord.newBuilder();
        record.setAtk(atkUser);
        if(defUser != null){
            record.setDef(defUser);
        }
        record.setAtkNowStar(atkNowStar);
        record.setAtkChangeStar(atkChangeStar);
        record.setDefNowStar(defNowStar);
        record.setDefChangeStar(defChangeStar);
        record.setMatchType(matchType);
        record.setRobot(robot);
        record.setRobotInfo(robotInfo);
        builder.setTournamentRecord(record);
    }

    public TournamentRecordDb genTournamentRecordDb() {
        return new TournamentRecordDb(RecordHelper.getInstance().genRecordPlayerDb(atkUser), RecordHelper.getInstance().genRecordPlayerDb(defUser), matchType, robot, BaseRobot.genDb(robotInfo), atkNowStar, atkChangeStar, defNowStar, defChangeStar);

    }

    @Override
    public void readFromDb2(RecordDb db) {
        TournamentRecordDb beanDb = db.getTournamentRecordDb();
        if(beanDb != null){
            this.atkUser = RecordHelper.getInstance().genRecordPlayerPb(beanDb.getAtk());
            this.defUser = RecordHelper.getInstance().genRecordPlayerPb(beanDb.getDef());
            this.matchType = beanDb.getMatchType();
            this.robot = beanDb.isRobot();
            this.atkNowStar = beanDb.getAtkNowStar();
            this.atkChangeStar = beanDb.getAtkChangeStar();
            this.defNowStar = beanDb.getDefNowStar();
            this.defChangeStar = beanDb.getDefChangeStar();
            if(this.robot && beanDb.getRobotInfo() != null){
                this.robotInfo = BaseRobot.genPb(beanDb.getRobotInfo());
            }
        }
    }

    @Override
    public void writeToDb(RecordDb db) {
        db.setTournamentRecordDb(genTournamentRecordDb());
    }

    @Override
    protected Set<PbRecord.RecordPlayer> getDetailRecordSet() {
        return null;
    }

    public int getMatchType() {
        return matchType;
    }

    public void setMatchType(int matchType) {
        this.matchType = matchType;
    }

    public PbRecord.RecordPlayer getAtkUser() {
        return atkUser;
    }

    public void setAtkUser(PbRecord.RecordPlayer atkUser) {
        this.atkUser = atkUser;
    }

    public PbRecord.RecordPlayer getDefUser() {
        return defUser;
    }

    public void setDefUser(PbRecord.RecordPlayer defUser) {
        this.defUser = defUser;
    }

    public boolean isRobot() {
        return robot;
    }

    public void setRobot(boolean robot) {
        this.robot = robot;
    }

    public PbRobot.BaseRobot getRobotInfo() {
        return robotInfo;
    }

    public void setRobotInfo(PbRobot.BaseRobot robotInfo) {
        this.robotInfo = robotInfo;
    }

    public int getAtkNowStar() {
        return atkNowStar;
    }

    public void setAtkNowStar(int atkNowStar) {
        this.atkNowStar = atkNowStar;
    }

    public int getAtkChangeStar() {
        return atkChangeStar;
    }

    public void setAtkChangeStar(int atkChangeStar) {
        this.atkChangeStar = atkChangeStar;
    }

    public int getDefNowStar() {
        return defNowStar;
    }

    public void setDefNowStar(int defNowStar) {
        this.defNowStar = defNowStar;
    }

    public int getDefChangeStar() {
        return defChangeStar;
    }

    public void setDefChangeStar(int defChangeStar) {
        this.defChangeStar = defChangeStar;
    }
}

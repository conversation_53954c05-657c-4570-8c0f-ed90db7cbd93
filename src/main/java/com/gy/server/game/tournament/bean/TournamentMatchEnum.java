package com.gy.server.game.tournament.bean;

/**
 * 匹配模式枚举
 *
 * <AUTHOR> 2024/9/9 16:22
 **/
public enum TournamentMatchEnum {

    /**
     * 匹配
     */
    mate(1),

    /**
     * 征召
     */
    callUp(2),
    ;

    private int type;

    TournamentMatchEnum(int type) {
        this.type = type;
    }

    public static TournamentMatchEnum getTournamentMatchPatternEnum(String type){
        return getTournamentMatchPatternEnum(Integer.parseInt(type));
    }

    public static TournamentMatchEnum getTournamentMatchPatternEnum(int type){
        switch (type){
            case 1: return mate;
            case 2: return callUp;
            default : return null;
        }
    }


    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }
}

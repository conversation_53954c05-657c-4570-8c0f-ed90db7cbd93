package com.gy.server.game.tournament.status.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.battleRule.base.IBattleStatusDeal;
import com.gy.server.game.tournament.TournamentService;
import com.gy.server.game.tournament.bean.TournamentStatusEnum;
import com.gy.server.game.tournament.status.TournamentBattleInfo;
import com.gy.server.game.tournament.template.TournamentConst;
import com.gy.server.utils.time.DateTimeUtil;

import java.time.LocalDateTime;

/**
 * 终止
 *
 * <AUTHOR> 2024/10/15 15:30
 **/
public class Tournament6AbortHandler implements IBattleStatusDeal<TournamentBattleInfo> {

    @Override
    public void init(TournamentBattleInfo battleInfo) {

    }

    @Override
    public void deal(TournamentBattleInfo battleInfo) {

    }

    @Override
    public boolean finish(TournamentBattleInfo battleInfo) {
        return true;
    }

    @Override
    public int nowStatus() {
        return TournamentStatusEnum.abort.getType();
    }

    @Override
    public int nextStatus(TournamentBattleInfo battleInfo) {
        return TournamentStatusEnum.noStart.getType();
    }

    @Override
    public long getStatusDurationTime(TournamentBattleInfo battleInfo) {
        TournamentConst tournamentConst = TournamentService.getTournamentConst();
        LocalDateTime start = ServerConstants.getCurrentTimeLocalDateTime().plusMonths(1).withDayOfMonth(tournamentConst.seasonStart).withHour(tournamentConst.openTime).withMinute(0).withSecond(0).withNano(0);
        return DateTimeUtil.toMillis(start) - ServerConstants.getCurrentTimeMillis();
    }
}

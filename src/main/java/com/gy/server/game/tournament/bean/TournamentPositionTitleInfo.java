package com.gy.server.game.tournament.bean;

import com.gy.server.packet.PbTournament;
import com.ttlike.server.tl.baselib.serialize.tournament.TournamentPositionTitleInfoDb;

/**
 * 实时pvp称号信息
 * 比较 品质高大于品质低
 * <AUTHOR> 2024/9/9 19:40
 **/
public class TournamentPositionTitleInfo{

    /**
     * 位置信息
     */
    private TournamentPositionInfo positionInfo;

    /**
     * 排名
     */
    private int rank;

    /**
     * 品质
     */
    private int quality;

    /**
     * 获得赛季
     */
    private int seasonId;

    public TournamentPositionTitleInfo() {

    }

    public TournamentPositionTitleInfo(TournamentPositionInfo positionInfo, int rank, int quality, int seasonId) {
        this.positionInfo = positionInfo;
        this.rank = rank;
        this.quality = quality;
        this.seasonId = seasonId;
    }

    public TournamentPositionTitleInfo(TournamentPositionTitleInfoDb db) {
        this.positionInfo = new TournamentPositionInfo(db.getPositionInfo());
        this.rank = db.getRank();
        this.quality = db.getQuality();
        this.seasonId = db.getSeasonId();
    }

    public PbTournament.TitleInfo genPb(){
        PbTournament.TitleInfo.Builder builder = PbTournament.TitleInfo.newBuilder();
        builder.setPosition(positionInfo.genPb());
        builder.setRank(rank);
        builder.setQuality(quality);
        builder.setSeasonId(seasonId);
        builder.setKey(getKey());
        return builder.build();
    }

    public TournamentPositionTitleInfoDb genDb(){
        TournamentPositionTitleInfoDb db = new TournamentPositionTitleInfoDb();
        db.setPositionInfo(this.positionInfo.genDb());
        db.setRank(this.rank);
        db.setQuality(this.quality);
        db.setSeasonId(this.seasonId);
        return db;
    }

    public String getKey(){
        return seasonId + positionInfo.getKey() + rank + "";
    }

    public TournamentPositionInfo getPositionInfo() {
        return positionInfo;
    }

    public void setPositionInfo(TournamentPositionInfo positionInfo) {
        this.positionInfo = positionInfo;
    }

    public int getSeasonId() {
        return seasonId;
    }

    public void setSeasonId(int seasonId) {
        this.seasonId = seasonId;
    }

    public int getRank() {
        return rank;
    }

    public void setRank(int rank) {
        this.rank = rank;
    }

    public int getQuality() {
        return quality;
    }

    public void setQuality(int quality) {
        this.quality = quality;
    }
}

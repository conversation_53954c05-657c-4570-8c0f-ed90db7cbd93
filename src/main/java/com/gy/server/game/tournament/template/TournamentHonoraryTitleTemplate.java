package com.gy.server.game.tournament.template;

import com.gy.server.game.util.StringExtUtil;

import java.util.List;
import java.util.Map;

/**
 * 结算模板
 * 结算日期发对应的称号,最多每个品质发一个
 * 注意2月29号，如果当月没有29号先结算再转天
 * <AUTHOR> 2024/9/9 17:38
 **/
public class TournamentHonoraryTitleTemplate {

    public int id;

    /**
     * 品质
     */
    public int quality;

    /**
     * 发放排名数量
     */
    public int rankingNum;

    /**
     * 结算日 2月29号特殊处理
     */
    public List<Integer> days;

    public TournamentHonoraryTitleTemplate(Map<String, String> map){
        this.id = Integer.parseInt(map.get("ID"));
        this.quality = Integer.parseInt(map.get("Quality"));
        this.rankingNum = Integer.parseInt(map.get("RankingNum"));
        this.days = StringExtUtil.string2List(map.get("day"), "|", Integer.class);
    }
}

package com.gy.server.game.tournament.bean;

import java.util.HashMap;
import java.util.Map;

/**
 * 实时pvp业务状态
 *
 * <AUTHOR> 2024/10/14 14:17
 **/
public enum TournamentStatusEnum {

    /**
     * 未开启状态
     */
    noStart(0),
    /**
     * 数据准备
     */
    ready(1),

    /**
     * 运行
     */
    run(2),

    /**
     * 结算，处理发奖
     */
    settlement(3),

    /**
     * 结束并数据处理，清空数据，保留需要数据
     */
    endAndClear(4),

    /**
     * 终止
     */
    abort(5),
    ;

    private final int type;
    private static final Map<Integer, TournamentStatusEnum> MAP = new HashMap<>();

    static {
        for (TournamentStatusEnum value : values()) {
            MAP.put(value.getType(), value);
        }
    }

    public static TournamentStatusEnum of(int type) {
        return MAP.get(type);
    }

    TournamentStatusEnum(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }
}

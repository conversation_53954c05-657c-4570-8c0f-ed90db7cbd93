package com.gy.server.game.tournament.stage;

import com.gy.server.core.Configuration;
import com.gy.server.core.callback.response.CallbackResponse;
import com.gy.server.core.command.PlayerCommandRequest;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.combat.StageType;
import com.gy.server.game.combat.skill.Skill;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.combat.unit.TeamUnit;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.lineup.bean.LineupInfoBean;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.record.RecordManager;
import com.gy.server.game.record.RecordType;
import com.gy.server.game.record.combat.CombatRecord;
import com.gy.server.game.robot.RobotHelper;
import com.gy.server.game.robot.RobotType;
import com.gy.server.game.robot.bean.BaseRobot;
import com.gy.server.game.tournament.TournamentHelper;
import com.gy.server.game.tournament.TournamentService;
import com.gy.server.game.tournament.bean.TournamentCallUpBattleInitInfo;
import com.gy.server.game.tournament.bean.TournamentMatchEnum;
import com.gy.server.game.tournament.record.TournamentRecord;
import com.gy.server.game.tournament.template.TournamentRobotTemplate;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbRecord;
import com.gy.server.world.tournament.bean.TournamentPlayerMatchInfo;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.rpc.TLMessageCallbackTask;
import org.apache.commons.lang3.tuple.Pair;

import java.util.ArrayList;
import java.util.List;

/**
 * 实时pvp战斗
 * 匹配，征召
 * <AUTHOR> 2024/10/14 11:21
 **/
public class TournamentStage extends AbstractStage {

    /**
     * 匹配信息
     */
    private TournamentPlayerMatchInfo atkMatchInfo;
    private TournamentPlayerMatchInfo defMatchInfo;

    private PbRecord.RecordPlayer atkRecordPlayer;
    private PbRecord.RecordPlayer defRecordPlayer;

    private MiniGamePlayer atkMiniPlayer;
    private MiniGamePlayer defMiniPlayer;

    /**
     * 进攻阵容
     */
    private LineupInfoBean atkLineup;
    /**
     * 防守阵容
     */
    private LineupInfoBean defLineup;

    private StageType stageType = StageType.PVPRealTime;
    private LineupType lineupType;

    private TournamentMatchEnum matchEnum;

    //----------------------------------------------
    private TournamentRobotTemplate robotTemplate;

    private int atkSelect = 0;
    private int defSelect = 0;

    public TournamentStage(TournamentPlayerMatchInfo atkMatchInfo, TournamentPlayerMatchInfo defMatchInfo, List<LineupInfoBean> atkLineupList, List<LineupInfoBean> defLineupList,
                           MiniGamePlayer atkMiniPlayer, MiniGamePlayer defMiniPlayer) {
        this.atkMatchInfo = atkMatchInfo;
        this.defMatchInfo = defMatchInfo;
//        this.atkRecordPlayer = atkRecordPlayer;
//        this.defRecordPlayer = defRecordPlayer;
        this.atkLineup = atkLineupList.get(0);
        this.defLineup = defLineupList != null ? defLineupList.get(0) : null;
        this.atkMiniPlayer = atkMiniPlayer;
        this.defMiniPlayer = defMiniPlayer;
        this.lineupType = LineupType.PVPRealTime1;
        this.matchEnum = TournamentMatchEnum.mate;
    }

    public TournamentStage(TournamentCallUpBattleInitInfo initInfo){
        //未选择时默认使用第一队
        int atkSelect = initInfo.getAtkSelect() == 0 ? (initInfo.getAtkDisable() == 1 ? 2 : 1) : initInfo.getAtkSelect();
        int defSelect = initInfo.getDefSelect() == 0 ? (initInfo.getDefDisable() == 1 ? 2 : 1) : initInfo.getDefSelect();
        this.atkMatchInfo = initInfo.getAtkMatchInfo();
        this.defMatchInfo = initInfo.getDefMatchInfo();
        this.atkLineup = initInfo.getAtkLineupInfos().get(atkSelect - 1);
        this.defLineup = initInfo.getDefLineupInfos().get(defSelect - 1);
//        this.atkRecordPlayer = initInfo.getAtkRecordPlayer();
//        this.defRecordPlayer = initInfo.getDefRecordPlayer();
        this.defMiniPlayer = initInfo.getDefMiniPlayer();
        this.lineupType = LineupType.PVPRealTime2;
        this.matchEnum = TournamentMatchEnum.callUp;

        this.atkSelect = atkSelect - 1;
        this.defSelect = defSelect - 1;
    }

    @Override
    public void init() {
        init(TournamentService.getTournamentConst().battleScene, stageType, createTeamUnit(atkLineup, atkMiniPlayer, true), createTeamUnit(defLineup, defMiniPlayer, false), lineupType);
        atkRecordPlayer = RecordManager.genRecordPlayer(atkMiniPlayer, lineupType, getAtks()).build();
        if(defMiniPlayer != null){
            defRecordPlayer = RecordManager.genRecordPlayer(defMiniPlayer, lineupType, getDefs()).build();
        }
    }

    private TeamUnit createTeamUnit(LineupInfoBean lineup, MiniGamePlayer miniPlayer, boolean isAtk){
        List<HeroUnit> heroUnit = new ArrayList<>();
        if(lineup == null && !isAtk){
            int dan = TournamentService.getDanByStar(atkMatchInfo.getStar());
            this.robotTemplate = TournamentService.getRobotByDan(dan);
            BaseRobot robotBean = RobotHelper.getRobotBean(RobotType.ARENA, robotTemplate.robotId);
            return robotBean.createTeamUnits(this).get(0);
        }else {
            heroUnit.addAll(lineup.createHeroUnitsByMirror());
        }

        List<Skill> additionalSkillList = new ArrayList<>();
        additionalSkillList.add(new Skill(TournamentService.getTournamentConst().battlefieldBuff1, 1));
        additionalSkillList.add(new Skill(TournamentService.getTournamentConst().battlefieldBuff2, 1));

        heroUnit.forEach(bean -> bean.addSkills(additionalSkillList));
        TeamUnit teamUnit = new TeamUnit(getNewId(), heroUnit);
        if(!isAtk && (atkMatchInfo.isRobotA() || atkMatchInfo.isRobotB())){
            if(miniPlayer != null){
                teamUnit.setAutoMode(miniPlayer.getPlayerId());
            }
        }
        return teamUnit;
    }

    @Override
    public void afterFinish() {
        PbProtocol.CombatSettlementNotify.Builder notify = genCombatSettlement();

        //战斗结束
        Pair<Integer, Integer> atkPair = TournamentHelper.stageFinishGsHandler(atkMatchInfo, isWin, notify, getHeroIdList(getAtks()));
        if(!atkMatchInfo.isRobotA() && !atkMatchInfo.isRobotB()){
            if(defMatchInfo.getServerId() != Configuration.serverId){
                ServerCommandRequest commandRequest = CommandRequests.newServerCommandRequest("TournamentRstCommandService.stageFinishHandler");
                TLBase.getInstance().getRpcUtil().sendToNodeWithCallBack(new StageCallbackTask(atkPair), ServerType.GAME, defMatchInfo.getServerId(), commandRequest, defMatchInfo, !isWin, notify, getHeroIdList(getDefs()));
            }else {
                Pair<Integer, Integer> defPair = TournamentHelper.stageFinishGsHandler(defMatchInfo, !isWin, notify, getHeroIdList(getDefs()));
                // 战报记录
                combatRecord(atkPair, defPair);
            }
            return;
        }

        // 战报记录 打机器人
        combatRecord(atkPair, Pair.of(0, 0));
    }

    public List<Integer> getHeroIdList(List<TeamUnit> teamUnitList){
        List<Integer> list = new ArrayList<>();
        for (HeroUnit unit : teamUnitList.get(0).getUnits()) {
            list.add(unit.getHeroId());
        }
        return list;
    }

    /**
     * 战报记录
     */
    private void combatRecord(Pair<Integer, Integer> atkPair, Pair<Integer, Integer> defPair) {
        // 生成战报
        MiniGamePlayer atkMiniPlayer = PlayerManager.getMiniPlayer(atkMatchInfo.getPlayerId());
        CombatRecord combatRecord = CombatRecord.create(this, atkMiniPlayer, defMiniPlayer, CombatRecord.OverdueTime.Arena, RecordType.PVPRealTime.getId());
        Player atkPlayer = PlayerManager.getPlayer(atkMatchInfo.getPlayerId());
        if(atkPlayer != null){
            TournamentRecord atkTournamentRecord = new TournamentRecord(combatRecord.getCosKey(), isWin, combatRecord.getId(), atkMatchInfo.getServerId(), atkRecordPlayer, defRecordPlayer, lineupType, robotTemplate, atkPair, defPair);
            RecordManager.addRecord(atkTournamentRecord, atkPlayer);
        }

        if(!atkMatchInfo.isRobotA() && !atkMatchInfo.isRobotB() && robotTemplate == null){
            TournamentRecord defTournamentRecord = new TournamentRecord(combatRecord.getCosKey(), !isWin, combatRecord.getId(), defMatchInfo.getServerId(), atkRecordPlayer, defRecordPlayer, lineupType, null, atkPair, defPair);
            if(defMatchInfo.getServerId() == Configuration.serverId){
                Player defPlayer = PlayerManager.getPlayer(defMatchInfo.getPlayerId());
                if(defPlayer != null){
                    RecordManager.addRecord(defTournamentRecord, atkPlayer);
                }
            }else {
                PlayerCommandRequest r = CommandRequests.newPlayerCommandRequest("RecordCommandService.addRecord", defMatchInfo.getPlayerId());
                TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, defMatchInfo.getServerId(), r, defTournamentRecord.writeToPb(), defMatchInfo.getPlayerId());
            }
        }
        // 保存战报战斗信息
        RecordManager.save(combatRecord);
    }

    public TournamentMatchEnum getMatchEnum() {
        return matchEnum;
    }

    public PbRecord.RecordPlayer getAtkRecordPlayer() {
        return atkRecordPlayer;
    }

    public PbRecord.RecordPlayer getDefRecordPlayer() {
        return defRecordPlayer;
    }

    public TournamentPlayerMatchInfo getAtkMatchInfo() {
        return atkMatchInfo;
    }

    public TournamentPlayerMatchInfo getDefMatchInfo() {
        return defMatchInfo;
    }

    public LineupInfoBean getAtkLineup() {
        return atkLineup;
    }

    public LineupInfoBean getDefLineup() {
        return defLineup;
    }

    public int getAtkSelect() {
        return atkSelect;
    }

    public int getDefSelect() {
        return defSelect;
    }

    public TournamentRobotTemplate getRobotTemplate() {
        return robotTemplate;
    }

    public void setRobotTemplate(TournamentRobotTemplate robotTemplate) {
        this.robotTemplate = robotTemplate;
    }

    private class StageCallbackTask extends TLMessageCallbackTask {
        private final Pair<Integer, Integer> atkPair;

        public StageCallbackTask(Pair<Integer, Integer> atkPair) {
            this.atkPair = atkPair;
        }

        @Override
        public void complete(CallbackResponse response) {
            int defNowStar = response.getParam(0);
            int defChangeStar = response.getParam(1);

            Pair<Integer, Integer> defPairTemp = Pair.of(defNowStar, defChangeStar);
            // 战报记录
            combatRecord(atkPair, defPairTemp);
        }

        @Override
        public void timeout() {
            combatRecord(atkPair, Pair.of(0, 0));
        }
    }
}

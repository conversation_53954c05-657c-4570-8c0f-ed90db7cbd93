package com.gy.server.game.tournament.async;

import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.Configuration;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.tournament.PlayerTournamentModel;
import com.gy.server.game.tournament.bean.TournamentPlayerRankInfo;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;

/**
 * 实时pvp排行榜更新
 *
 * <AUTHOR> 2024/10/15 16:14
 **/
public class TournamentRankUpdateAsync {

    public static void deal(PlayerTournamentModel model) {
        int masterWorldId = CommonUtils.getWorldMasterServerId();
        TournamentPlayerRankInfo rankInfo = new TournamentPlayerRankInfo(model.getPlayerId(), model.getPlayer().getName(), Configuration.serverId, model.getStar().get(), model.getPositionInfo());
        if(rankInfo.getDanId() == 0){
            return;
        }

        ServerCommandRequest request = CommandRequests.newServerCommandRequest("TournamentCommandService.rankUpdate");
        TLBase.getInstance().getRpcUtil().sendToNode(ServerType.WORLD, masterWorldId, request, rankInfo);
    }

}

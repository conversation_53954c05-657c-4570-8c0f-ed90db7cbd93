package com.gy.server.game.tournament.bean;

import com.gy.server.game.hero.Hero;
import com.gy.server.game.player.Player;
import com.gy.server.game.role.PlayerRoleService;
import com.gy.server.packet.PbTournament;
import com.ttlike.server.tl.baselib.serialize.tournament.TournamentCommonUseInfoDb;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 实时pvp常用阵容信息
 *
 * <AUTHOR> 2024/10/24 10:07
 **/
public class TournamentCommonUseInfo {

    /**
     * key:英雄id value:英雄星数
     */
    private Map<Integer, Integer> heroIdMap = new HashMap<>();

    /**
     * 主角ID
     */
    private int protagonistId;

    /**
     * 使用次数
     */
    private int num;

    public TournamentCommonUseInfo() {
    }

    public TournamentCommonUseInfo(TournamentCommonUseInfoDb db) {
        this.heroIdMap.putAll(db.getHeroIdMap());
        this.protagonistId = db.getProtagonistId();
        this.num = db.getNum();
    }

    public TournamentCommonUseInfo(List<Integer> heroIdList, Player player) {
        for (int heroId : heroIdList) {
            if(heroId < 0){
                heroIdMap.put(heroId, 0);
                protagonistId = PlayerRoleService.getProtagonistTemplate(player).id;
            }else {
                Hero hero = player.getBagModel().getHeroById(heroId);
                if(hero != null){
                    heroIdMap.put(heroId, 0);
                }
            }
        }
        num = 1;
    }

    public PbTournament.CommonUseInfo genPb(){
        PbTournament.CommonUseInfo.Builder builder = PbTournament.CommonUseInfo.newBuilder();
        builder.putAllCommonUse(heroIdMap);
        builder.setProtagonistId(protagonistId);
        builder.setNum(num);
        return builder.build();
    }

    public TournamentCommonUseInfoDb genDb() {
        TournamentCommonUseInfoDb db = new TournamentCommonUseInfoDb();
        db.getHeroIdMap().putAll(this.heroIdMap);
        db.setProtagonistId(this.protagonistId);
        db.setNum(this.num);
        return db;
    }

    public void addNum(int addNum) {
        num = num + addNum;
    }

    public String getKey(){
        List<Integer> list = new ArrayList<>(heroIdMap.keySet());
        list.sort(Integer::compare);

        StringBuilder builder = new StringBuilder();
        for (int id : list) {
            if(id == 0){
                builder.append(protagonistId);
                continue;
            }
            builder.append(id);
        }
        return builder.toString();
    }

    public Map<Integer, Integer> getHeroIdMap() {
        return heroIdMap;
    }

    public void setHeroIdMap(Map<Integer, Integer> heroIdMap) {
        this.heroIdMap = heroIdMap;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

}

package com.gy.server.game.tournament.status;

import com.gy.server.core.battleRule.base.BaseBattleInfo;
import com.gy.server.core.battleRule.enums.BattleEnums;
import com.gy.server.game.tournament.bean.TournamentPlayerRankInfo;
import com.gy.server.game.tournament.bean.TournamentPositionInfo;
import com.gy.server.game.tournament.bean.TournamentTitleQualityEnum;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * 实时pvp业务数据
 * 赛季 年 * 10 + 月
 * <AUTHOR> 2024/10/14 14:03
 **/
public class TournamentBattleInfo extends BaseBattleInfo {

    /**
     * 全国排行榜
     */
    private volatile CopyOnWriteArrayList<TournamentPlayerRankInfo> countryRankInfos = new CopyOnWriteArrayList<>();

    /**
     * 实时pvp称号结算map
     * key:id  value:已经结算的日子
     */
    private ConcurrentHashMap<Integer, CopyOnWriteArraySet<Integer>> titleSettlementMap = new ConcurrentHashMap<>();

    /**
     * 已经判断过是否结算的天数
     */
    private Set<Integer> days = new HashSet<>();

    public TournamentBattleInfo() {
        super(BattleEnums.tournament);
    }

    public TournamentPlayerRankInfo getTournamentPlayerRankInfoByCountryRankInfos(TournamentPositionInfo positionInfo, long playerId){
        for (TournamentPlayerRankInfo rankInfo : countryRankInfos) {
            if(rankInfo.getPlayerId() == playerId){
                return rankInfo;
            }
        }
        return null;
    }

    public TournamentPlayerRankInfo getTournamentPlayerRankInfoByRankInfoMap(TournamentPositionInfo positionInfo, long playerId){
        for (TournamentPlayerRankInfo rankInfo : countryRankInfos) {
            if(rankInfo.getPlayerId() == playerId){
                return rankInfo;
            }
        }
        return null;
    }

    //排行榜变化处理
    public void changeTournamentPlayerRankInfo(TournamentPlayerRankInfo newRankInfo){
        for(int i = 0; i < countryRankInfos.size(); i++){
            TournamentPlayerRankInfo rankInfo = countryRankInfos.get(i);
            if(rankInfo.getPlayerId() == newRankInfo.getPlayerId()){
                countryRankInfos.set(i, newRankInfo);
                Collections.sort(countryRankInfos);
                return;
            }
        }

        countryRankInfos.add(newRankInfo);
        Collections.sort(countryRankInfos);
    }

    public List<TournamentPlayerRankInfo> getCountryRankInfos() {
        return new ArrayList<>(countryRankInfos);
    }

    /**
     * 获取对应定位的排行旁
     * @param selectPositionInfo
     * @return
     */
    public List<TournamentPlayerRankInfo> getRankInfoByPositionInfo(TournamentPositionInfo selectPositionInfo) {
        List<TournamentPlayerRankInfo> list = new ArrayList<>();
        for (TournamentPlayerRankInfo bean : countryRankInfos) {
            TournamentPositionInfo positionInfo = bean.getPositionInfo();
            //需要加入高级别的行政区
            if(positionInfo.getQuality() >= selectPositionInfo.getQuality()){
                list.add(bean);
            }
        }
        return list;
    }

    /**
     * 根据地点分类获取排行榜
     * @return
     */
    public Map<TournamentPositionInfo, List<TournamentPlayerRankInfo>> getRankInfoGroupByPositionInfo() {
        Map<TournamentPositionInfo, List<TournamentPlayerRankInfo>> map = new HashMap<>();
        if(!countryRankInfos.isEmpty()){
            map.put(new TournamentPositionInfo(0, 0, 0), countryRankInfos);
        }

        for (TournamentPlayerRankInfo bean : countryRankInfos) {
            TournamentPositionInfo positionInfo = bean.getPositionInfo().copy();

            if(positionInfo.getQuality() == TournamentTitleQualityEnum.third.getQuality()){
                map.computeIfAbsent(positionInfo, list -> new ArrayList<>()).add(bean);
                map.computeIfAbsent(new TournamentPositionInfo(positionInfo.getFirst(), positionInfo.getSecond(), 0), list -> new ArrayList<>()).add(bean);
                map.computeIfAbsent(new TournamentPositionInfo(positionInfo.getFirst(), 0, 0), list -> new ArrayList<>()).add(bean);
            }

            if(positionInfo.getQuality() == TournamentTitleQualityEnum.second.getQuality()){
                map.computeIfAbsent(positionInfo, list -> new ArrayList<>()).add(bean);
                map.computeIfAbsent(new TournamentPositionInfo(positionInfo.getFirst(), 0, 0), list -> new ArrayList<>()).add(bean);
            }

            if(positionInfo.getQuality() == TournamentTitleQualityEnum.first.getQuality()){
                map.computeIfAbsent(positionInfo, list -> new ArrayList<>()).add(bean);
            }
        }
        return map;
    }

    public List<TournamentPlayerRankInfo> getRankInfoByServerId(int serverId) {
        List<TournamentPlayerRankInfo> list = new ArrayList<>();
        for (TournamentPlayerRankInfo bean : countryRankInfos) {
            if(bean.getServerId() == serverId){
                list.add(bean);
            }
        }
        return list;
    }

    public void clear() {
        countryRankInfos.clear();
        titleSettlementMap.clear();
        days.clear();
    }

    public ConcurrentHashMap<Integer, CopyOnWriteArraySet<Integer>> getTitleSettlementMap() {
        return titleSettlementMap;
    }

    public void setTitleSettlementMap(ConcurrentHashMap<Integer, CopyOnWriteArraySet<Integer>> titleSettlementMap) {
        this.titleSettlementMap = titleSettlementMap;
    }

    public void setCountryRankInfos(CopyOnWriteArrayList<TournamentPlayerRankInfo> countryRankInfos) {
        this.countryRankInfos = countryRankInfos;
    }

    public Set<Integer> getDays() {
        return days;
    }

    public void setDays(Set<Integer> days) {
        this.days = days;
    }

}

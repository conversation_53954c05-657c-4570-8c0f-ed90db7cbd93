package com.gy.server.game.tournament.bean;

import com.gy.server.packet.PbTournament;
import com.ttlike.server.tl.baselib.serialize.tournament.TournamentPositionInfoDb;

import java.io.Serializable;

/**
 * 实时pvp定位信息
 *
 * <AUTHOR> 2024/9/9 16:56
 **/
public class TournamentPositionInfo implements Serializable {

    private static final long serialVersionUID = -2175872292782317165L;

    /**
     * 一级
     */
    private int first;

    /**
     * 二级
     */
    private int second;

    /**
     * 三级
     */
    private int third;

    public TournamentPositionInfo() {
    }

    public TournamentPositionInfo(int first) {
        this.first = first;
    }

    public TournamentPositionInfo(int first, int second) {
        this.first = first;
        this.second = second;
    }

    public TournamentPositionInfo(int first, int second, int third) {
        this.first = first;
        this.second = second;
        this.third = third;
    }

    public TournamentPositionInfo(PbTournament.PositionInfo positionInfo) {
        this.first = positionInfo.getFirst();
        this.second = positionInfo.getSecond();
        this.third = positionInfo.getThird();
    }

    public TournamentPositionInfo(TournamentPositionInfoDb db) {
        this.first = db.getFirst();
        this.second = db.getSecond();
        this.third = db.getThird();
    }

    public int getQuality(){
        if(first == second && second == third && third == 0){
            return 9;
        }

        if(first != 0 && second == third && third == 0){
            return 7;
        }

        if(first != 0 && second != 0 && third == 0){
            return 5;
        }

        if(first != 0 && second != 0 && third != 0){
            return 3;
        }

        return 0;
    }

    public PbTournament.PositionInfo genPb(){
        PbTournament.PositionInfo.Builder builder = PbTournament.PositionInfo.newBuilder();
        builder.setFirst(first);
        builder.setSecond(second);
        builder.setThird(third);
        return builder.build();
    }

    public TournamentPositionInfoDb genDb(){
        TournamentPositionInfoDb db = new TournamentPositionInfoDb();
        db.setFirst(this.first);
        db.setSecond(this.second);
        db.setThird(this.third);
        return db;
    }

    public String getKey() {
        return first + second + third + "";
    }

    public int getFirst() {
        return first;
    }

    public void setFirst(int first) {
        this.first = first;
    }

    public int getSecond() {
        return second;
    }

    public void setSecond(int second) {
        this.second = second;
    }

    public int getThird() {
        return third;
    }

    public void setThird(int third) {
        this.third = third;
    }

    public TournamentPositionInfo copy() {
        return new TournamentPositionInfo(first, second, third);
    }
}

package com.gy.server.game.tournament.commandService;

import com.gy.server.annotation.MessageMethod;
import com.gy.server.annotation.MessageServiceBean;
import com.gy.server.core.MessageServerType;
import com.gy.server.core.MethodInvokeType;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.command.CommandRequestParams;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.lineup.bean.LineupInfoBean;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.record.RecordManager;
import com.gy.server.game.text.Text;
import com.gy.server.game.tournament.TournamentHelper;
import com.gy.server.game.tournament.TournamentManager;
import com.gy.server.game.tournament.async.TournamentCallUpBattleInitAsync;
import com.gy.server.game.tournament.async.TournamentMateBattleInitAsync;
import com.gy.server.game.tournament.bean.CallUpBattleInitInfoIndex;
import com.gy.server.game.tournament.bean.TournamentCallUpBattleInitInfo;
import com.gy.server.game.tournament.bean.TournamentMatchEnum;
import com.gy.server.game.tournament.bean.TournamentPositionTitleInfo;
import com.gy.server.packet.PbProtocol;
import com.gy.server.world.tournament.bean.TournamentPlayerMatchInfo;
import com.ttlike.server.tl.baselib.thread.ThreadPool;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;

/**
 * 实时pvp-gs处理类
 *
 * <AUTHOR> 2024/9/12 19:30
 **/
@MessageServiceBean(description = "实时pvp消息", messageServerType = MessageServerType.game)
public class TournamentRstCommandService {
    @MessageMethod(description = "匹配成功处理world -> gs", invokeType = MethodInvokeType.sync)
    private static void matchResultDeal(ServerCommandRequest request, CommandRequestParams params){
        //TODO text matchInfoMap.get(atkPlayerId), matchInfoMap.get(defPlayerId), matchPattern, matchSuccessTime
        int text = params.getParam(0);
        TournamentPlayerMatchInfo atk = params.getParam(1);
        TournamentPlayerMatchInfo def = params.getParam(2);
        TournamentMatchEnum matchPattern = params.getParam(3);
        long matchSuccessTime = params.getParam(4); //匹配成功时间点

        switch (matchPattern){
            case mate:{
                //匹配不需要返回双方匹配信息,直接返回战斗初始化信息
                ThreadPool.execute(new TournamentMateBattleInitAsync(atk, def, text, matchSuccessTime));
                break;
            }
            case callUp:{
                //征召,需要返回双方匹配信息，双方禁选后，返回战斗初始化信息
                ThreadPool.execute(new TournamentCallUpBattleInitAsync(atk, def, text, matchSuccessTime));
                break;
            }
        }

    }

    @MessageMethod(description = "获取实时pvp战斗初始化相关信息 gs -> gs", invokeType = MethodInvokeType.sync)
    private static void getBattleInitInfo(ServerCommandRequest request, CommandRequestParams params){
        long playerId = params.getParam(0);
        LineupType lineupType = params.getParam(1);

        Player defPlayer = PlayerManager.getOnlinePlayer(playerId);
        if(defPlayer != null){
            List<LineupInfoBean> defLineInfoBean = lineupType.getLineup(defPlayer);
            request.addCallbackParam(defLineInfoBean);
            request.addCallbackParam(RecordManager.genRecordPlayer(defPlayer, false, lineupType, null));
            request.addCallbackParam(defPlayer.getMiniPlayer());
        }
    }

    @MessageMethod(description = "通知战斗初始化 gs -> gs", invokeType = MethodInvokeType.sync)
    private static void NotifyBattleInit(ServerCommandRequest request, CommandRequestParams params){
        long playerId = params.getParam(0);
        PbProtocol.TournamentBattleInitNotify battleNotify = params.getParam(1);
        int matchUseTime = params.getParam(2);
        Player player = PlayerManager.getOnlinePlayer(playerId);
        if(player != null){
            player.send(PtCode.ROOM_BATTLE_INIT_NOTIFY, battleNotify);
        }

        TournamentManager.getInstance().getGsMatchPlayerMap().remove(player.getPlayerId());
        TournamentHelper.updateMatchSuccessTime(playerId, matchUseTime);
    }

    @MessageMethod(description = "增加战斗输赢情况 gs -> gs", invokeType = MethodInvokeType.sync)
    private static void stageFinishHandler(ServerCommandRequest request, CommandRequestParams params){
        TournamentPlayerMatchInfo defMatchInfo = params.getParam(0);
        boolean isWin = params.getParam(1);
        PbProtocol.CombatSettlementNotify.Builder notify = params.getParam(2);
        List<Integer> heroIdList = params.getParam(3);

        Pair<Integer, Integer> pair = TournamentHelper.stageFinishGsHandler(defMatchInfo, isWin, notify, heroIdList);
        request.addCallbackParam(pair.getLeft());
        request.addCallbackParam(pair.getRight());
    }

    @MessageMethod(description = "匹配成功通知 gs -> gs", invokeType = MethodInvokeType.sync)
    private static void MatchResultNotify(ServerCommandRequest request, CommandRequestParams params){
        TournamentPlayerMatchInfo matchInfo = params.getParam(0);
        PbProtocol.TournamentMatchResultNotify notify = params.getParam(1);
        long matchSuccessTime = params.getParam(2);
        if(params.getParams().length > 3){
            CallUpBattleInitInfoIndex initInfoIndex = params.getParam(3);
            if(initInfoIndex != null){
                TournamentManager.getInstance().addTournamentCallUpBattleInitInfo(initInfoIndex, null);
            }
        }
        TournamentHelper.matchResultNotify(notify, matchInfo, matchSuccessTime);
    }

    @MessageMethod(description = "征召模式禁用选用处理 gs -> gs")
    private static void callUpDisableSelect(ServerCommandRequest request, CommandRequestParams params){
        CallUpBattleInitInfoIndex indexInfo = params.getParam(0);
        long playerId = params.getParam(1);
        int index = params.getParam(2);
        boolean isSelect = params.getParam(3);

        //
        TournamentCallUpBattleInitInfo initInfo = TournamentManager.getInstance().getCallUpMap().get(indexInfo);
        if(initInfo == null){
            request.addCallbackParam(Text.参数异常);
            return;
        }

        long finishTime = isSelect ? initInfo.getSelectFinish() : initInfo.getDisableFinish();
        if(ServerConstants.getCurrentTimeMillis() >= finishTime){
            request.addCallbackParam(Text.genServerRstInfo(isSelect ? Text.选用已超时 : Text.禁用已超时));
            return;
        }

        boolean isAtk = playerId == initInfo.getAtkRecordPlayer().getId();

        //是否已禁用
        if(!isSelect && ((isAtk && initInfo.getDefDisable() != 0) || (!isAtk && initInfo.getAtkDisable() != 0))){
            request.addCallbackParam(Text.genServerRstInfo(Text.实时pvp已进行禁用));
            return;
        }

        //是否已选用
        if(isSelect && ((isAtk && initInfo.getAtkSelect() != 0) || (!isAtk && initInfo.getDefSelect() != 0))){
            request.addCallbackParam(Text.genServerRstInfo(Text.实时pvp已进行禁用));
            return;
        }

        //是否选用已被禁用
        if(isSelect && ((isAtk && initInfo.getAtkDisable() == index) || (!isAtk && initInfo.getDefDisable() == index))){
            request.addCallbackParam(Text.genServerRstInfo(Text.此队伍已被禁用));
            return;
        }

        request.addCallbackParam(Text.没有异常);
        initInfo.disableSelectDeal(index, isSelect, isAtk);

        //禁选用通知
        TournamentHelper.disableSelectNotify(initInfo);
    }

    @MessageMethod(description = "增加实时pvp称号 world -> gs", invokeType = MethodInvokeType.async)
    private static void addTournamentTitle(ServerCommandRequest request, CommandRequestParams params){
        Map<Long, Map<Integer, TournamentPositionTitleInfo>> titleInfoMap = params.getParam(0);
        Set<Integer> qualitySet = params.getParam(1);

        ThreadPool.execute(() -> {
            for (Map.Entry<Long, Map<Integer, TournamentPositionTitleInfo>> entry : titleInfoMap.entrySet()) {
                Player player = PlayerManager.getPlayer(entry.getKey());
                if(player != null){
                    player.getPlayerTournamentModel().addAllTitleInfo(entry.getValue(), qualitySet);
                }
            }
        });
    }

    @MessageMethod(description = "实时pvp新赛季处理 world -> gs", invokeType = MethodInvokeType.async)
    private static void tournamentStarNewSeasonDeal(ServerCommandRequest request, CommandRequestParams params){
        int newSeasonId = params.getParam(0);
        for (MiniGamePlayer miniGamePlayer : PlayerManager.getMiniPlayers()) {
            Player player = PlayerManager.getPlayer(miniGamePlayer.getPlayerId());
            if(player != null){
                player.getPlayerTournamentModel().checkSeasonId(newSeasonId);
            }
        }
    }

    @MessageMethod(description = "实时pvp赛季结束处理 world -> gs", invokeType = MethodInvokeType.async)
    private static void seasonEndDeal(ServerCommandRequest request, CommandRequestParams params){
        Map<Long, Integer> map = params.getParam(0);
        for (MiniGamePlayer miniGamePlayer : PlayerManager.getMiniPlayers()) {
            Player player = PlayerManager.getPlayer(miniGamePlayer.getPlayerId());
            if(player != null){
                player.getPlayerTournamentModel().seasonEndSendRewardDeal(map.getOrDefault(player.getPlayerId(), 0));
            }
        }
    }
}

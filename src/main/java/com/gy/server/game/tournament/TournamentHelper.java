package com.gy.server.game.tournament;

import com.gy.server.core.Configuration;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.command.PlayerCommandRequest;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.lineup.bean.LineupBean;
import com.gy.server.game.lineup.bean.LineupInfoBean;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.text.Text;
import com.gy.server.game.tournament.async.TournamentRankUpdateAsync;
import com.gy.server.game.tournament.bean.CallUpBattleInitInfoIndex;
import com.gy.server.game.tournament.bean.TournamentCallUpBattleInitInfo;
import com.gy.server.game.tournament.bean.TournamentMatchEnum;
import com.gy.server.game.tournament.template.TournamentMatchingRuleTemplate;
import com.gy.server.game.tournament.template.TournamentRobotTemplate;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbRecord;
import com.gy.server.packet.PbTournament;
import com.gy.server.utils.time.DateTimeUtil;
import com.gy.server.world.tournament.bean.TournamentPlayerMatchInfo;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

/**
 * 实时pvp辅助类
 *
 * <AUTHOR> 2024/9/18 11:43
 **/
public class TournamentHelper {

    /**
     * 实时pvp战斗结束gs处理
     * @return left:现在星数 right:变化星数
     */
    public static Pair<Integer, Integer> stageFinishGsHandler(TournamentPlayerMatchInfo matchInfo, boolean isWin, PbProtocol.CombatSettlementNotify.Builder notify, List<Integer> heroIdList){
        Player player = PlayerManager.getOnlinePlayer(matchInfo.getPlayerId());
        if(player == null){
            //匹配双方应该都是在线的，做个保护
            return Pair.of(0, 0);
        }

        PlayerTournamentModel tournamentModel = player.getPlayerTournamentModel();
        int winNum = tournamentModel.getWinningStreakNum();

        int changeStar = 0;
        if(isWin){
            //胜利加星
            changeStar = 1 + (winNum > TournamentService.getTournamentConst().winningStreak ? 1 : 0);
        }else {
            //检查失败保护
            if(matchInfo.getStar() <= TournamentService.getTournamentConst().ordinary){

            }

            Pair<Integer, Integer> pair = TournamentService.getTournamentConst().failureProtection;
            if(matchInfo.getStar() > pair.getLeft()){
                int failureProtectionNum = pair.getRight();
                if(tournamentModel.getFailureProtectionNum() >= failureProtectionNum){
                    changeStar = -1;
                }
            }
        }
        tournamentModel.changeStar(changeStar);
        tournamentModel.dealStageResult(isWin, heroIdList);

        //发送战斗结果
        PbProtocol.TournamentSettlement.Builder settlement = PbProtocol.TournamentSettlement.newBuilder();
        settlement.setNewStarNum(tournamentModel.getStar().get());
        notify.setIsWin(isWin);
        notify.setTournament(settlement);
        player.send(PtCode.COMBAT_SETTLEMENT_NOTIFY, notify.build());

        //进排行榜
        TournamentRankUpdateAsync.deal(tournamentModel);

        return Pair.of(tournamentModel.getStar().get(), changeStar);
    }

    public static void matchResultNotify(int text, TournamentMatchEnum type, PbRecord.RecordPlayer atkRecordPlayer, PbRecord.RecordPlayer defRecordPlayer,
                                         LineupType lineupType, TournamentPlayerMatchInfo atkMatchInfo, TournamentPlayerMatchInfo defMatchInfo, long matchSuccessTime,
                                         List<LineupInfoBean> atkLineup, List<LineupInfoBean> defLineup){
        matchResultNotify(text, type, atkRecordPlayer, defRecordPlayer, lineupType, atkMatchInfo, defMatchInfo, matchSuccessTime,0, 0, atkLineup, defLineup, null, null);
    }

    public static void matchResultNotify(int text, TournamentMatchEnum type, PbRecord.RecordPlayer atkRecordPlayer, PbRecord.RecordPlayer defRecordPlayer,
                                         LineupType lineupType, TournamentPlayerMatchInfo atkMatchInfo, TournamentPlayerMatchInfo defMatchInfo, long matchSuccessTime,
                                         List<LineupInfoBean> atkLineup, List<LineupInfoBean> defLineup, TournamentRobotTemplate robotTemplate){
        matchResultNotify(text, type, atkRecordPlayer, defRecordPlayer, lineupType, atkMatchInfo, defMatchInfo, matchSuccessTime,0, 0, atkLineup, defLineup, null, robotTemplate);
    }

    public static void matchResultNotify(int text, TournamentMatchEnum type, PbRecord.RecordPlayer atkRecordPlayer, PbRecord.RecordPlayer defRecordPlayer,
                                         LineupType lineupType, TournamentPlayerMatchInfo atkMatchInfo, TournamentPlayerMatchInfo defMatchInfo, long matchSuccessTime,
                                         long disableFinish, long selectFinish, List<LineupInfoBean> atkLineup, List<LineupInfoBean> defLineup, CallUpBattleInitInfoIndex initInfoIndex, TournamentRobotTemplate robotTemplate){

        int atkStar = atkMatchInfo != null ? atkMatchInfo.getStar() : 0;
        int defStar = defMatchInfo != null ? defMatchInfo.getStar() : 0;
        PbProtocol.TournamentMatchResultNotify notify = TournamentHelper.genTournamentMatchResultNotify(text, type, atkRecordPlayer, defRecordPlayer, lineupType,
                atkLineup, defLineup, disableFinish, selectFinish, atkStar, defStar, robotTemplate);
        matchResultNotify(notify, atkMatchInfo, matchSuccessTime);
        if(!atkMatchInfo.isRobotA() && !atkMatchInfo.isRobotB()){
            if(defMatchInfo.getServerId() == Configuration.serverId){
                //本服
                matchResultNotify(notify, defMatchInfo, matchSuccessTime);
            }else {
                //跨服
                PlayerCommandRequest request = CommandRequests.newPlayerCommandRequest("TournamentRstCommandService.MatchResultNotify", defMatchInfo.getPlayerId());
                TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, defMatchInfo.getServerId(), request, defMatchInfo.getPlayerId(), notify, matchSuccessTime, initInfoIndex);
            }
        }
    }

    /**
     * 本服通知匹配成功
     * @param notify
     * @param matchInfo
     * @param matchSuccessTime
     */
    public static void matchResultNotify(PbProtocol.TournamentMatchResultNotify notify, TournamentPlayerMatchInfo matchInfo, long matchSuccessTime){
        Player onlinePlayer = PlayerManager.getOnlinePlayer(matchInfo.getPlayerId());
        if(onlinePlayer != null){
            onlinePlayer.send(PtCode.TOURNAMENT_MATCH_RESULT_NOTIFY, notify);
            if(notify.getResult().getErrInfo().getMessageId() != Text.没有异常){
                TournamentHelper.updateMatchSuccessTime(matchInfo, matchSuccessTime);
            }
        }
    }

    public static PbProtocol.TournamentMatchResultNotify genTournamentMatchResultNotify(int text, TournamentMatchEnum type, PbRecord.RecordPlayer atkRecordPlayer, PbRecord.RecordPlayer defRecordPlayer,
                                                                                        LineupType lineupType, List<LineupInfoBean> atkLineup, List<LineupInfoBean> defLineup, long disableFinish, long selectFinish, int atkStar, int defStar, TournamentRobotTemplate robotTemplate){
        PbProtocol.TournamentMatchResultNotify.Builder notify = PbProtocol.TournamentMatchResultNotify.newBuilder().setResult(Text.genServerRstInfo(text));
        notify.setModeType(type.getType());

        notify.addMemberInfo(genBattleMemberInfo(atkRecordPlayer, lineupType, atkLineup, TournamentService.getDanByStar(atkStar), 0));

        boolean isNull = robotTemplate == null;
        notify.addMemberInfo(genBattleMemberInfo(defRecordPlayer, lineupType, defLineup, isNull ? 1 : robotTemplate.points, isNull ? 0 : robotTemplate.robotId));

        notify.setDisableFinish(disableFinish);
        notify.setSelectFinish(selectFinish);
        return notify.build();
    }

    public static PbTournament.BattleMemberInfo genBattleMemberInfo(PbRecord.RecordPlayer recordPlayer, LineupType lineupType, List<LineupInfoBean> lineup, int danId, int robotId){
        PbTournament.BattleMemberInfo.Builder battleMemberInfo = PbTournament.BattleMemberInfo.newBuilder();
        if(recordPlayer != null){
            battleMemberInfo.setRecordPlayer(recordPlayer);
        }
        if(lineup != null){
            battleMemberInfo.setLineupStand(LineupBean.genLineupStand(lineupType, lineup));
        }
        battleMemberInfo.setDanId(danId);
        battleMemberInfo.setRobotId(robotId);
        return battleMemberInfo.build();
    }

    public static void updateMatchSuccessTime(TournamentPlayerMatchInfo matchInfo, long matchSuccessTime){
        int second = (int) ((matchSuccessTime - matchInfo.getStart()) / DateTimeUtil.MillisOfSecond);
        updateMatchSuccessTime(matchInfo.getPlayerId(), second);
    }

    public static void updateMatchSuccessTime(long playerId, int matchUseTime){
        Player player = PlayerManager.getOnlinePlayer(playerId);
        if(player != null){
            PlayerTournamentModel model = player.getPlayerTournamentModel();
            model.setMatchUseTime(matchUseTime);
        }
    }

    /**
     * 禁用选用通知
     */
    public static void disableSelectNotify(TournamentCallUpBattleInitInfo initInfo){
        PbProtocol.TournamentDisableSelectNotify notify = initInfo.genTournamentDisableSelectNotify();
        disableSelectNotify(notify, initInfo.getAtkRecordPlayer().getId(), initInfo.getAtkServerId());
        disableSelectNotify(notify, initInfo.getDefRecordPlayer().getId(), initInfo.getDefServerId());
    }

    public static void disableSelectNotify(PbProtocol.TournamentDisableSelectNotify notify, long playerId, int serverId){
        if(serverId == Configuration.serverId){
            Player onlinePlayer = PlayerManager.getOnlinePlayer(playerId);
            if(onlinePlayer != null){
                onlinePlayer.send(PtCode.TOURNAMENT_DISABLE_SELECT_NOTIFY, notify);
            }
        }else {
            ServerCommandRequest commandRequest = CommandRequests.newServerCommandRequest("TournamentRstCommandService.callUpDisableSelect");
            TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, serverId, commandRequest, notify, playerId);
        }
    }

    /**
     * 是否有段位征召模式正在开启中
     * @param
     */
    public static boolean haveCallUpOpen(){
        int hour = ServerConstants.getCurrentTimeLocalDateTime().getHour();
        for (TournamentMatchingRuleTemplate bean : TournamentService.getMatchingRuleMap().values()) {
            if(bean.gameplayType != TournamentMatchEnum.callUp){
                continue;
            }

            for (Pair<Integer, Integer> openingHour : bean.openingHours) {
                if (hour >= openingHour.getLeft() && hour <= openingHour.getRight()) {
                    return true;
                }
            }
        }

        return false;
    }
}

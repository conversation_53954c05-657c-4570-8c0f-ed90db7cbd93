package com.gy.server.game.tournament.bean;

/**
 * 实时pvp征召模式战斗初始化信息 索引信息
 * <AUTHOR> 2024/10/11 20:01
 **/
public class CallUpBattleInitInfoIndex {

    /**
     * 进行战斗的服务器id
     * 战斗在攻击方服务器进行
     */
    private int atkServerId;
    private int defServerId;

    private long atkPlayerId;
    private long defPlayerId;

    public CallUpBattleInitInfoIndex(int atkServerId, int defServerId, long atkPlayerId, long defPlayerId) {
        this.atkServerId = atkServerId;
        this.defServerId = defServerId;
        this.atkPlayerId = atkPlayerId;
        this.defPlayerId = defPlayerId;
    }

    public int getAtkServerId() {
        return atkServerId;
    }

    public void setAtkServerId(int atkServerId) {
        this.atkServerId = atkServerId;
    }

    public int getDefServerId() {
        return defServerId;
    }

    public void setDefServerId(int defServerId) {
        this.defServerId = defServerId;
    }

    public long getAtkPlayerId() {
        return atkPlayerId;
    }

    public void setAtkPlayerId(long atkPlayerId) {
        this.atkPlayerId = atkPlayerId;
    }

    public long getDefPlayerId() {
        return defPlayerId;
    }

    public void setDefPlayerId(long defPlayerId) {
        this.defPlayerId = defPlayerId;
    }
}

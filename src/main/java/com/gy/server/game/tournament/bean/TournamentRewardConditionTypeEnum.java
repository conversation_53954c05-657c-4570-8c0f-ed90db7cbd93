package com.gy.server.game.tournament.bean;

import com.gy.server.game.tournament.TournamentService;

/**
 * 实时pvp奖励领取条件类型
 *
 * <AUTHOR> 2024/10/14 15:59
 **/
public enum TournamentRewardConditionTypeEnum {

    /**
     * 段位
     */
    dan(1) {
        @Override
        public boolean check(int starNum, int ranking, int condition) {
            return TournamentService.getDanByStar(starNum) >= condition;
        }
    },

    /**
     * 排名
     */
    rank(2) {
        @Override
        public boolean check(int starNum, int ranking, int condition) {
            return ranking >= condition;
        }
    },
    ;

    private int type;

    TournamentRewardConditionTypeEnum(int type) {
        this.type = type;
    }

    public static TournamentRewardConditionTypeEnum getTournamentRewardConditionTypeEnum(int type){
        switch (type){
            case 1: return dan;
            case 2: return rank;
            default : return null;
        }
    }

    public int getType() {
        return type;
    }

    public abstract boolean check(int starNum, int ranking, int condition);

}


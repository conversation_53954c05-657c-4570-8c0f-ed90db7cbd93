package com.gy.server.game.tournament.status.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.battleRule.base.IBattleStatusDeal;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.tournament.TournamentService;
import com.gy.server.game.tournament.bean.TournamentStatusEnum;
import com.gy.server.game.tournament.status.TournamentBattleInfo;
import com.gy.server.game.tournament.template.TournamentConst;
import com.gy.server.game.tournament.template.TournamentHonoraryTitleTemplate;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;

import java.time.LocalDateTime;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * 准备
 *
 * <AUTHOR> 2024/10/14 16:28
 **/
public class Tournament2ReadyHandler implements IBattleStatusDeal<TournamentBattleInfo> {


    @Override
    public void init(TournamentBattleInfo battleInfo) {

    }

    @Override
    public void deal(TournamentBattleInfo battleInfo) {
        TournamentConst tournamentConst = TournamentService.getTournamentConst();
        LocalDateTime firstSeasonTime = tournamentConst.firstSeason;

        LocalDateTime nowLocal = ServerConstants.getCurrentTimeLocalDateTime();
        if(nowLocal.isBefore(firstSeasonTime)){
            return;
        }

        int newSeasonId = getMonthInterval(firstSeasonTime, nowLocal) + 1;
        if(newSeasonId != battleInfo.getSeasonId()){
            //新赛季开启
            battleInfo.setSeasonId(newSeasonId);

            //检查本月第一天结算
            for (TournamentHonoraryTitleTemplate bean : TournamentService.getHonoraryTitleMap().values()) {
                if (bean.days.contains(1)) {
                    //第一天已在上个赛季末结算
                    battleInfo.getTitleSettlementMap().computeIfAbsent(bean.id, set -> new CopyOnWriteArraySet()).add(1);
                }
            }

            //通知游戏服段位处理
            ServerCommandRequest request = CommandRequests.newServerCommandRequest("TournamentRstCommandService.tournamentStarNewSeasonDeal");
            TLBase.getInstance().getRpcUtil().sendToAll(ServerType.GAME, request, newSeasonId);
        }
    }

    public static int getMonthInterval(LocalDateTime before, LocalDateTime after){
        int beforeYear = before.getYear();
        int beforeMonth = before.getMonthValue();

        int afterYear = after.getYear();
        int afterMonth = after.getMonthValue();

        int interval = (afterYear - beforeYear) * 12 + (afterMonth - beforeMonth);
        return Math.max(interval, 0);
    }

    @Override
    public boolean finish(TournamentBattleInfo battleInfo) {
        return true;
    }

    @Override
    public int nowStatus() {
        return TournamentStatusEnum.ready.getType();
    }

    @Override
    public int nextStatus(TournamentBattleInfo battleInfo) {
        return TournamentStatusEnum.run.getType();
    }

    @Override
    public long getStatusDurationTime(TournamentBattleInfo battleInfo) {
        return 0;
    }

    @Override
    public long getStatusStartTime() {
        LocalDateTime nowLocal = ServerConstants.getCurrentTimeLocalDateTime();
        nowLocal.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
        return DateTimeUtil.toMillis(nowLocal);
    }
}

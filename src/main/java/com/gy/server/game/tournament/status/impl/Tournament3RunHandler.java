package com.gy.server.game.tournament.status.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.battleRule.base.IBattleStatusDeal;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.tournament.TournamentService;
import com.gy.server.game.tournament.bean.TournamentPlayerRankInfo;
import com.gy.server.game.tournament.bean.TournamentPositionInfo;
import com.gy.server.game.tournament.bean.TournamentPositionTitleInfo;
import com.gy.server.game.tournament.bean.TournamentStatusEnum;
import com.gy.server.game.tournament.status.TournamentBattleInfo;
import com.gy.server.game.tournament.template.TournamentConst;
import com.gy.server.game.tournament.template.TournamentHonoraryTitleTemplate;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * 运行
 *
 * <AUTHOR> 2024/10/14 16:30
 **/
public class Tournament3RunHandler implements IBattleStatusDeal<TournamentBattleInfo> {

    @Override
    public void init(TournamentBattleInfo battleInfo) {

    }

    @Override
    public void deal(TournamentBattleInfo battleInfo) {
        LocalDateTime localDateTime = ServerConstants.getCurrentTimeLocalDateTime();
        int day = localDateTime.getDayOfMonth();
        if(!battleInfo.getDays().contains(day)){
            //key:服务器id  value:key:玩家id value:key:品质 value:称号信息
            Map<Integer, Map<Long, Map<Integer, TournamentPositionTitleInfo>>> sendMap = new HashMap<>();
            //今日结算品质
            Set<Integer> qualityList = new HashSet<>();
            Map<TournamentPositionInfo, List<TournamentPlayerRankInfo>> rankMap = battleInfo.getRankInfoGroupByPositionInfo();

            Map<Integer, Integer> titleSettlementMap = new HashMap<>();
            for (TournamentHonoraryTitleTemplate bean : TournamentService.getHonoraryTitleMap().values()) {
                CopyOnWriteArraySet set = battleInfo.getTitleSettlementMap().getOrDefault(bean.id, new CopyOnWriteArraySet());
                boolean isSpecial = localDateTime.getMonthValue() == 2 && bean.days.contains(29);
                if((bean.days.contains(day) && !set.contains(day)) || (isSpecial && !set.contains(29))){
                    //称号结算
                    for (Map.Entry<TournamentPositionInfo, List<TournamentPlayerRankInfo>> entry : rankMap.entrySet()) {
                        if(bean.quality == entry.getKey().getQuality()){
                            List<TournamentPlayerRankInfo> rankInfos = entry.getValue();
                            if(!rankInfos.isEmpty()){
                                rankInfos = rankInfos.subList(0, Math.min(bean.rankingNum, rankInfos.size()));

                                for (int i = 0; i < rankInfos.size(); i++){
                                    int rank = i + 1;
                                    TournamentPlayerRankInfo rankInfo = rankInfos.get(i);
                                    TournamentPositionTitleInfo titleInfo = new TournamentPositionTitleInfo(rankInfo.getPositionInfo(), rank, bean.quality, battleInfo.getSeasonId());
                                    sendMap.computeIfAbsent(rankInfo.getServerId(), map1 -> new HashMap<>()).computeIfAbsent(rankInfo.getPlayerId(), map2 -> new HashMap<>()).put(bean.quality, titleInfo);
                                }
                            }
                        }
                    }

                    qualityList.add(bean.quality);
                    titleSettlementMap.put(bean.id, (isSpecial && !set.contains(29)) ? 29 : day);
                }
            }

            battleInfo.getDays().add(day);
            for (Map.Entry<Integer, Integer> entry : titleSettlementMap.entrySet()) {
                battleInfo.getTitleSettlementMap().computeIfAbsent(entry.getKey(), list -> new CopyOnWriteArraySet<>()).add(entry.getValue());
            }

            ThreadPool.execute(() -> {
                for (Map.Entry<Integer, Map<Long, Map<Integer, TournamentPositionTitleInfo>>> entry : sendMap.entrySet()) {
                    ServerCommandRequest request = CommandRequests.newServerCommandRequest("TournamentRstCommandService.addTournamentTitle");
                    TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, entry.getKey(), request, entry.getValue(), qualityList);
                }
            });
        }
    }

    @Override
    public boolean finish(TournamentBattleInfo battleInfo) {
        return false;
    }

    @Override
    public int nowStatus() {
        return TournamentStatusEnum.run.getType();
    }

    @Override
    public int nextStatus(TournamentBattleInfo battleInfo) {
        return TournamentStatusEnum.settlement.getType();
    }

    @Override
    public long getStatusDurationTime(TournamentBattleInfo battleInfo) {
        TournamentConst tournamentConst = TournamentService.getTournamentConst();
        LocalDateTime end = ServerConstants.getCurrentTimeLocalDateTime().withDayOfMonth(tournamentConst.seasonEnd).withHour(tournamentConst.endTime).withMinute(0).withSecond(0).withNano(0);
        return DateTimeUtil.toMillis(end) - getStatusStartTime();
    }

    @Override
    public long getStatusStartTime() {
        TournamentConst tournamentConst = TournamentService.getTournamentConst();
        LocalDateTime nowLocal = ServerConstants.getCurrentTimeLocalDateTime().withDayOfMonth(tournamentConst.seasonStart).withHour(tournamentConst.openTime).withMinute(0).withSecond(0).withNano(0);
        return DateTimeUtil.toMillis(nowLocal);
    }
}

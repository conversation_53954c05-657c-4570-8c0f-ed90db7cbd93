package com.gy.server.game.tournament.template;

import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.tournament.bean.TournamentMatchEnum;
import com.gy.server.game.util.StringExtUtil;
import org.apache.commons.lang3.tuple.Pair;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 段位模版
 *
 * <AUTHOR> 2024/9/9 16:08
 **/
public class TournamentMatchingRuleTemplate {

    /**
     * 段位id
     */
    public int tierId;

    /**
     * 星数
     * left:最小星数    right:最大星数
     */
    public Pair<Integer, Integer> stars;

    /**
     * 初始匹配范围-星数
     */
    public int initialRange;

    /**
     * 最大匹配范围-星数
     */
    public int radiusMax;

    /**
     * 开放时间
     * left:开始时间   right:结束时间
     */
    public List<Pair<Integer, Integer>> openingHours = new ArrayList<>();

    /**
     * 匹配模式
     */
    public TournamentMatchEnum gameplayType;

    /**
     * 段位奖励
     */
    public List<RewardTemplate> danReward;

    /**
     * 赛季奖励
     */
    public List<RewardTemplate> seasonReward;

    /**
     * 重置后段位
     */
    public int resetBit;

    public TournamentMatchingRuleTemplate(Map<String, String> map) {
        this.tierId = Integer.parseInt(map.get("TierId"));
        this.stars = StringExtUtil.string2Pair(map.get("stars"), "|", Integer.class, Integer.class);
        this.initialRange = Integer.parseInt(map.get("InitialRange"));
        this.radiusMax = Integer.parseInt(map.get("RadiusMax"));
        String openHour = map.get("OpeningHours");
        if(openHour.contains("|")){
            List<String> list = StringExtUtil.string2List(openHour, "|", String.class);
            for (String str : list) {
                this.openingHours.add(StringExtUtil.string2Pair(str, "-", Integer.class, Integer.class));
            }
        }else {
            this.openingHours.add(StringExtUtil.string2Pair(openHour, "-", Integer.class, Integer.class));
        }
        this.gameplayType = TournamentMatchEnum.getTournamentMatchPatternEnum(map.get("GameplayType"));
        this.danReward = RewardTemplate.readListFromText(map.get("TierRewards"));
        this.seasonReward = RewardTemplate.readListFromText(map.get("SeasonRewards"));
        this.resetBit = Integer.parseInt(map.get("ResetBit"));
    }
}

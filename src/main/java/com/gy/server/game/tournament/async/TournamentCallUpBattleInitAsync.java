package com.gy.server.game.tournament.async;

import com.gy.server.core.Configuration;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.callback.response.CallbackResponse;
import com.gy.server.core.command.PlayerCommandRequest;
import com.gy.server.core.log.CommonLogger;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.lineup.bean.LineupInfoBean;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.record.RecordManager;
import com.gy.server.game.text.Text;
import com.gy.server.game.tournament.TournamentHelper;
import com.gy.server.game.tournament.TournamentManager;
import com.gy.server.game.tournament.bean.CallUpBattleInitInfoIndex;
import com.gy.server.game.tournament.bean.TournamentCallUpBattleInitInfo;
import com.gy.server.game.tournament.bean.TournamentMatchEnum;
import com.gy.server.packet.PbRecord;
import com.gy.server.utils.time.DateTimeUtil;
import com.gy.server.world.tournament.bean.TournamentPlayerMatchInfo;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.rpc.TLMessageCallbackTask;

import java.util.List;

/**
 * 实时pvp征召模式战斗初始化-异步
 * 征召模式，需要先走匹配成功，随后双方玩家进行禁用选用，完成后进入战斗
 * <AUTHOR> 2024/9/18 11:18
 **/
public class TournamentCallUpBattleInitAsync extends AsyncCall {

    /**
     * 匹配信息
     */
    private TournamentPlayerMatchInfo atkMatchInfo;
    private TournamentPlayerMatchInfo defMatchInfo;

    /**
     * 错误码
     */
    private int text;

    /**
     * 匹配成功时间点
     */
    private long matchSuccessTime;

    /**
     * 进攻阵容
     */
    private List<LineupInfoBean> atkLineup;
    /**
     * 防守阵容
     */
    private List<LineupInfoBean> defLineup;

    private PbRecord.RecordPlayer atkRecordPlayer;
    private PbRecord.RecordPlayer defRecordPlayer;

    private MiniGamePlayer defMiniPlayer;

    private LineupType lineupType = LineupType.PVPRealTime2;

    public TournamentCallUpBattleInitAsync(TournamentPlayerMatchInfo atkMatchInfo, TournamentPlayerMatchInfo defMatchInfo, int text, long matchSuccessTime) {
        this.atkMatchInfo = atkMatchInfo;
        this.defMatchInfo = defMatchInfo;
        this.text = text;
        this.matchSuccessTime = matchSuccessTime;
    }

    @Override
    public void execute() {
        //不管成不成功，gs匹配池清除atk
        TournamentManager.getInstance().getGsMatchPlayerMap().remove(atkMatchInfo.getPlayerId());

        if(text != Text.没有异常){
            TournamentHelper.matchResultNotify(text, TournamentMatchEnum.callUp, atkRecordPlayer, defRecordPlayer, lineupType, atkMatchInfo, defMatchInfo, matchSuccessTime, atkLineup, defLineup);
            return;
        }

        if(atkRecordPlayer == null || defRecordPlayer == null || atkLineup == null || defLineup == null){
            //匹配失败
            TournamentHelper.matchResultNotify(Text.匹配失败, TournamentMatchEnum.callUp, atkRecordPlayer, defRecordPlayer, lineupType, atkMatchInfo, defMatchInfo, matchSuccessTime, atkLineup, defLineup);
            return;
        }

        TournamentCallUpBattleInitInfo callUpBattleInitInfo = new TournamentCallUpBattleInitInfo(atkLineup, defLineup, atkRecordPlayer, defRecordPlayer, atkMatchInfo.getServerId(), defMatchInfo.getServerId(), atkMatchInfo, defMatchInfo, defMiniPlayer);
        CommonLogger.info("匹配成功时间：" + ServerConstants.getCurrentTimeLocalDateTime() + " \n禁用结束时间：" + DateTimeUtil.toLocalDateTime(callUpBattleInitInfo.getDisableFinish()) +
                " \n禁用结束时间：" + DateTimeUtil.toLocalDateTime(callUpBattleInitInfo.getSelectFinish()));

        CallUpBattleInitInfoIndex initInfoIndex = TournamentManager.getInstance().addTournamentCallUpBattleInitInfo(callUpBattleInitInfo);

        TournamentHelper.matchResultNotify(text, TournamentMatchEnum.callUp, atkRecordPlayer, defRecordPlayer, lineupType, atkMatchInfo, defMatchInfo, matchSuccessTime,
                callUpBattleInitInfo.getDisableFinish(), callUpBattleInitInfo.getSelectFinish(), atkLineup, defLineup, initInfoIndex, null);
    }

    @Override
    public void asyncExecute() {
        if(text != Text.没有异常){
            return;
        }

        Player atkPlayer = PlayerManager.getOnlinePlayer(atkMatchInfo.getPlayerId());
        if(atkPlayer == null){
            return;
        }

        atkRecordPlayer = RecordManager.genRecordPlayer(atkPlayer, false, lineupType, null).build();
        atkLineup = lineupType.getLineup(atkPlayer);

        //征召模式没有机器人
        //防守方
        if (defMatchInfo.getServerId() == Configuration.serverId) {
            //本服
            Player defPlayer = PlayerManager.getPlayer(defMatchInfo.getPlayerId());
            if (defPlayer != null) {
                defLineup = lineupType.getLineup(defPlayer);
                defRecordPlayer = RecordManager.genRecordPlayer(defPlayer, false, lineupType, null).build();
                defMiniPlayer = PlayerManager.getMiniPlayer(defMatchInfo.getPlayerId());
            }
        } else {
            //跨服
            PlayerCommandRequest request = CommandRequests.newPlayerCommandRequest("TournamentRstCommandService.getBattleInitInfo", defMatchInfo.getPlayerId());
            TLBase.getInstance().getRpcUtil().sendToNodeWithCallBack(new ExecuteCallbackTask(), ServerType.GAME, defMatchInfo.getServerId(), request, defMatchInfo.getPlayerId());
        }
    }


    private class ExecuteCallbackTask extends TLMessageCallbackTask {
        @Override
        public void complete(CallbackResponse response) {
            defLineup = response.getParam(0);
            defRecordPlayer = response.getParam(1);
            defMiniPlayer = response.getParam(2);
        }

        @Override
        public void timeout() {

        }
    }
}

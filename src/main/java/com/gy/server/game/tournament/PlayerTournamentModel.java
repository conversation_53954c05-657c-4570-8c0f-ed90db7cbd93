package com.gy.server.game.tournament;

import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.callback.response.CallbackResponse;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.mail.MailManager;
import com.gy.server.game.mail.MailType;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModel;
import com.gy.server.game.player.event.PlayerEvent;
import com.gy.server.game.player.event.PlayerEventHandler;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.reddot.RedDot;
import com.gy.server.game.text.Text;
import com.gy.server.game.tournament.async.TournamentMatchStartAsync;
import com.gy.server.game.tournament.bean.*;
import com.gy.server.game.tournament.template.TournamentMatchingRuleTemplate;
import com.gy.server.packet.PbTournament;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.rpc.TLMessageCallbackTask;
import com.ttlike.server.tl.baselib.serialize.player.PlayerBlobDb;
import com.ttlike.server.tl.baselib.serialize.tournament.PlayerTournamentModelDb;
import com.ttlike.server.tl.baselib.serialize.tournament.TournamentCommonUseInfoDb;
import com.ttlike.server.tl.baselib.serialize.tournament.TournamentPositionTitleInfoDb;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 玩家实时pvp模块
 * 战力低的为进攻方
 *
 * <AUTHOR> 2024/9/9 11:46
 **/
public class PlayerTournamentModel extends PlayerModel implements PlayerEventHandler {

    private int seasonId;

    /**
     * 定位信息 可选择
     */
    private TournamentPositionInfo positionInfo;

    /**
     * 定位更改次数，每周
     */
    private int positionChangeNum;

    /**
     * 当前星数
     */
    private AtomicInteger star = new AtomicInteger(0);

    /**
     * 赛季最高星数
     */
    private int seasonMaxStar;

    /**
     * 历史最高星数
     */
    private int maxStar;

    /**
     * 上次匹配时间，秒
     */
    private int matchUseTime;

    /**
     * 实时pvp 最高称号
     * key:称号品质  value:称号信息
     */
    private HashMap<Integer, TournamentPositionTitleInfo> highestTitleMap = new HashMap<>();

    /**
     * 实时pvp 当前称号
     * key:称号品质  value:称号信息
     */
    private HashMap<Integer, TournamentPositionTitleInfo> nowTitleMap = new HashMap<>();

    /**
     * 使用中实时pvp称号的key
     */
    private String titleKey = null;

    /**
     * 已领取最高高段位奖励的段位id集合
     */
    private Set<Integer> receiveIds = new HashSet<>();

    /**
     * 失败已用保护次数
     */
    private int failureProtectionNum;

    /**
     * 最近5场战斗输赢情况
     * 连胜积分额外奖励
     * 连败/5局输3，下次匹配机器人
     */
    private List<Boolean> stageWins = new ArrayList<>();

    /**
     * 赛季胜利次数
     */
    private int winSeasonNum;

    /**
     * 赛季对局
     */
    private int seasonAllNum;

    /**
     * 总胜利对局
     */
    private int winAllNum;

    /**
     * 总对局
     */
    private int allNum;

    /**
     * 上过王者的赛季集合
     */
    private Set<Integer> kingSeasonSet = new HashSet<>();

    /**
     * 常用英雄map
     * 一赛季一清
     */
    Map<String, TournamentCommonUseInfo> commonUseMap = new HashMap<>();

    public PlayerTournamentModel(Player player) {
        super(player);
//        this.matchUseTime = TournamentService.getTournamentConst().ordinaryMatchingTime;
    }

    @Override
    protected void loadData(PlayerBlobDb playerBlob) {
        PlayerTournamentModelDb db = playerBlob.getTournamentModelDb();
        if(db != null){
            this.seasonId = db.getSeasonId();
            if(db.getPositionInfo() != null){
                this.positionInfo = new TournamentPositionInfo(db.getPositionInfo());
            }
            this.positionChangeNum = db.getPositionChangeNum();
            this.star = new AtomicInteger(db.getStar());
            this.seasonMaxStar = db.getSeasonMaxStar();
            this.maxStar = db.getMaxStar();
            this.matchUseTime = db.getMatchUseTime();
            for (TournamentPositionTitleInfoDb bean : db.getHighestTitleList()) {
                TournamentPositionTitleInfo positionTitleInfo = new TournamentPositionTitleInfo(bean);
                this.highestTitleMap.put(positionTitleInfo.getQuality(), positionTitleInfo);
            }
            for (TournamentPositionTitleInfoDb bean : db.getNowTitleMap()) {
                TournamentPositionTitleInfo positionTitleInfo = new TournamentPositionTitleInfo(bean);
                this.nowTitleMap.put(positionTitleInfo.getQuality(), positionTitleInfo);
            }
            if(db.getTitleKey() != null){
                this.titleKey = db.getTitleKey();
            }
            this.receiveIds.addAll(db.getReceiveIds());
            this.failureProtectionNum = db.getFailureProtectionNum();
            this.stageWins.addAll(db.getStageWins());
            this.winSeasonNum = db.getWinSeasonNum();
            this.seasonAllNum = db.getSeasonAllNum();
            this.winAllNum = db.getWinAllNum();
            this.allNum = db.getAllNum();
            this.kingSeasonSet.addAll(db.getKingSeasonSet());
            for (TournamentCommonUseInfoDb bean : db.getCommonUseList()) {
                TournamentCommonUseInfo tournamentCommonUseInfo = new TournamentCommonUseInfo(bean);
                commonUseMap.put(tournamentCommonUseInfo.getKey(), tournamentCommonUseInfo);
            }
        }
    }

    @Override
    protected void saveData(PlayerBlobDb playerBlob) {
        PlayerTournamentModelDb db = new PlayerTournamentModelDb();
        db.setSeasonId(this.seasonId);
        if(this.positionInfo != null){
            db.setPositionInfo(this.positionInfo.genDb());
        }
        db.setPositionChangeNum(this.positionChangeNum);
        db.setStar(this.star.get());
        db.setSeasonMaxStar(this.seasonMaxStar);
        db.setMaxStar(this.maxStar);
        db.setMatchUseTime(this.matchUseTime);
        for (TournamentPositionTitleInfo bean : this.highestTitleMap.values()) {
            db.getHighestTitleList().add(bean.genDb());
        }
        for (TournamentPositionTitleInfo bean : this.nowTitleMap.values()) {
            db.getNowTitleMap().add(bean.genDb());
        }
        if(this.titleKey != null){
            db.setTitleKey(this.titleKey);
        }
        db.getReceiveIds().addAll(this.receiveIds);
        db.setFailureProtectionNum(this.failureProtectionNum);
        db.getStageWins().addAll(this.stageWins);
        db.setWinSeasonNum(this.winSeasonNum);
        db.setSeasonAllNum(this.seasonAllNum);
        db.setWinAllNum(this.winAllNum);
        db.setAllNum(this.allNum);
        db.getKingSeasonSet().addAll(this.kingSeasonSet);
        for (TournamentCommonUseInfo bean : this.commonUseMap.values()) {
            db.getCommonUseList().add(bean.genDb());
        }
        playerBlob.setTournamentModelDb(db);
    }

    public void changeStar(int changeStar){
        if(star == null){
            star = new AtomicInteger(0);
        }
        this.star.set(Math.max(star.get() + changeStar, 0));
        checkMaxStar();

        if(!kingSeasonSet.contains(seasonId) && seasonMaxStar > TournamentService.getTournamentConst().king){
            kingSeasonSet.add(seasonId);
        }
        sync();
    }

    public void checkMaxStar(){
        if(star.get() > seasonMaxStar){
            this.seasonMaxStar = star.get();
        }

        if(star.get() > maxStar){
            this.maxStar = star.get();

            //红点
            RedDot.realtimeArenaReward.sync(getPlayer());
        }
    }

    public int getWinningStreakNum(){
        int num = 0;
        for(int i = stageWins.size() - 1; i >= 0; i--){
            if(stageWins.get(i)){
                num++;
            }else {
                return num;
            }
        }
        return num;
    }

    /**
     * gm下发称号
     * @param rank
     * @param sendSeasonId
     * @param sendPositionInfo
     */
    public void gmSendTitle(int rank, int sendSeasonId, TournamentPositionInfo sendPositionInfo) {
        if(sendPositionInfo == null){
            sendPositionInfo = this.positionInfo == null ? new TournamentPositionInfo(0, 0, 0) : this.positionInfo;
        }
        TournamentPositionTitleInfo positionTitleInfo = new TournamentPositionTitleInfo(sendPositionInfo, rank, sendPositionInfo.getQuality(), sendSeasonId == 0 ? this.seasonId : sendSeasonId);
        addAllTitleInfo(positionTitleInfo);
    }

    private void addAllTitleInfo(TournamentPositionTitleInfo positionTitleInfo) {
        Map<Integer, TournamentPositionTitleInfo> newTitleMap = new HashMap<>();
        newTitleMap.put(positionTitleInfo.getQuality(), positionTitleInfo);
        List<Integer> qualityCollection = new ArrayList<>();
        qualityCollection.add(positionTitleInfo.getQuality());
        addAllTitleInfo(newTitleMap, qualityCollection);
    }

    public void addAllTitleInfo(Map<Integer, TournamentPositionTitleInfo> newTitleMap, Collection<Integer> qualityCollection) {
        //更新结算品质称号
        for (int quality : qualityCollection) {
            TournamentPositionTitleInfo newTitleInfo = newTitleMap.get(quality);
            if(newTitleInfo != null){
                nowTitleMap.put(quality, newTitleInfo);
            }else {
                nowTitleMap.remove(quality);
            }
        }

        //比较历史最高称号
        for (Map.Entry<Integer, TournamentPositionTitleInfo> entry : nowTitleMap.entrySet()) {
            if(!highestTitleMap.containsKey(entry.getKey())){
                highestTitleMap.put(entry.getKey(), entry.getValue());
                continue;
            }

            TournamentPositionTitleInfo oldHighestTitleInfo = highestTitleMap.get(entry.getKey());
            if(entry.getValue().getRank() < oldHighestTitleInfo.getRank()){
                highestTitleMap.put(entry.getKey(), entry.getValue());
            }
        }

        //判断当前称号是否还存在（穿戴当前赛季的称号结算后有可能被替换到）
        if(!highestTitleMap.containsKey(titleKey) && !nowTitleMap.containsKey(titleKey)){
            titleKey = null;
        }

        sync();
    }

    public void checkSeasonId(int checkSeasonId) {
        if(seasonId != checkSeasonId){
            newSeasonDeal(checkSeasonId);
        }
    }

    /**
     * 新赛季季星级处理
     * @param newSeasonId
     */
    public void newSeasonDeal(int newSeasonId) {
        TournamentMatchingRuleTemplate oldTemplate = TournamentService.getMatchingRuleTemplateByStar(star.get());
        TournamentMatchingRuleTemplate newTemplate = TournamentService.getMatchingRuleMap().get(oldTemplate.resetBit);
        if(newTemplate == null){
            star.set(0);
        }else {
            star.set(newTemplate.stars.getLeft());
        }

        seasonId = newSeasonId;
        seasonMaxStar = 0;
        winSeasonNum = 0;
        seasonAllNum = 0;
        commonUseMap.clear();
        receiveIds.clear();

        sync();
    }

    /**
     * 赛季结束发奖处理
     * @param rank 排行
     */
    public void seasonEndSendRewardDeal(int rank) {
        int dan = TournamentService.getDanByStar(star.get());
        if(dan == 0){
            return;
        }

        //赛季奖励
        List<Reward> rewards = new ArrayList<>();
        rewards.addAll(TournamentService.getRankingRewardByParam(TournamentRewardTypeEnum.season, TournamentRewardConditionTypeEnum.dan, dan));
        rewards.addAll(TournamentService.getRankingRewardByParam(TournamentRewardTypeEnum.season, TournamentRewardConditionTypeEnum.rank, rank));

        //比武大会赛季奖励
        if(!rewards.isEmpty()){
            MailType mailType = MailType.PVPRealTime_season_reward;
            MailManager.sendMail(
                    mailType,
                    getPlayerId(),
                    Text.genText(mailType.getTitleId()).build(),
                    Text.genText(mailType.getContentId()).build(),
                    ServerConstants.getCurrentTimeMillis(),
                    rewards
            );
        }

        //排名奖励
        rewards.clear();
        rewards.addAll(TournamentService.getRankingRewardByParam(TournamentRewardTypeEnum.rank, TournamentRewardConditionTypeEnum.dan, dan));
        rewards.addAll(TournamentService.getRankingRewardByParam(TournamentRewardTypeEnum.rank, TournamentRewardConditionTypeEnum.rank, rank));

        //比武大会排名奖励
        if(!rewards.isEmpty()){
            MailType mailType = MailType.PVPRealTime_rank_reward;
            MailManager.sendMail(
                    mailType,
                    getPlayerId(),
                    Text.genText(mailType.getTitleId()).build(),
                    Text.genText(mailType.getContentId()).build(),
                    ServerConstants.getCurrentTimeMillis(),
                    rewards
            );
        }
    }

    public void addPositionChangeNum() {
        positionChangeNum++;
    }

    public void sync(){
        getPlayer().dataSyncModule.genTournamentSyncData();
    }

    @Override
    public PlayerEventType[] getEventTypes() {
        return new PlayerEventType[]{PlayerEventType.login, PlayerEventType.logout, PlayerEventType.week5Refresh};
    }

    @Override
    public void handle(PlayerEvent event) {
        switch (event.getEventType()){
            case login:{
                Player player = event.getSource();
                int masterWorldId = CommonUtils.getWorldMasterServerId();
                ServerCommandRequest request = CommandRequests.newServerCommandRequest("TournamentCommandService.getSeasonId");
                TLBase.getInstance().getRpcUtil().sendToNodeWithCallBack(new LoginUpdateCallbackTask(player), ServerType.WORLD, masterWorldId, request);
                break;
            }
            case logout:{
                long playerId = event.getSource().getPlayerId();
                if(TournamentManager.getInstance().getGsMatchPlayerMap().containsKey(playerId)){
                    TournamentManager.getInstance().getGsMatchPlayerMap().remove(playerId);
                    ThreadPool.execute(new TournamentMatchStartAsync(getPlayer(), false, 0));
                }
                break;
            }
            case week5Refresh:{
                positionChangeNum = 0;
                sync();
            }
        }
    }

    /**
     * 处理战斗结果
     * @param isWin
     */
    public void dealStageResult(boolean isWin, List<Integer> heroIdList) {
        //增加最近5场战斗信息
        stageWins.add(isWin);
        while (stageWins.size() > 5){
            stageWins.remove(0);
        }

        //增加胜场记录
        if(isWin){
            winSeasonNum++;
            winAllNum++;
        }
        seasonAllNum++;
        allNum++;

        //增加阵容记录
        if(!heroIdList.isEmpty()){
            TournamentCommonUseInfo newCommonUseInfo = new TournamentCommonUseInfo(heroIdList, getPlayer());
            String key = newCommonUseInfo.getKey();
            if(commonUseMap.containsKey(key)){
                newCommonUseInfo.addNum(commonUseMap.get(key).getNum());
            }
            commonUseMap.put(key, newCommonUseInfo);
        }
    }

    public boolean haveTitle(String key) {
        return haveTitle(key, highestTitleMap.values()) || haveTitle(key, nowTitleMap.values());
    }

    private boolean haveTitle(String key, Collection<TournamentPositionTitleInfo> collection){
        for (TournamentPositionTitleInfo bean : collection) {
            if(bean.getKey().equals(key)){
                return true;
            }
        }
        return false;
    }

    /**
     * 获取最常用英雄阵容
     * @return
     */
    public TournamentCommonUseInfo getUseMaxCommonUse(){
        return commonUseMap.values().stream().max(Comparator.comparingInt(TournamentCommonUseInfo::getNum)).orElse(null);
    }

    public PbTournament.TournamentModel genPb(){
        PbTournament.TournamentModel.Builder builder = PbTournament.TournamentModel.newBuilder();
        builder.setSeasonId(seasonId);
        if(positionInfo != null){
            builder.setPosition(positionInfo.genPb());
        }
        builder.setPositionChangeNum(positionChangeNum);
        builder.setStar(star.get());
        builder.setSeasonMaxStar(seasonMaxStar);
        builder.setMaxStar(maxStar);
        builder.setMatchUseTime(getMatchUseTime());
        highestTitleMap.values().forEach(bean -> builder.addHighestTitle(bean.genPb()));
        nowTitleMap.values().forEach(bean -> builder.addNowTitle(bean.genPb()));
        if(titleKey != null && !titleKey.equals("")){
            builder.setTitleKey(titleKey);
        }
        builder.addAllReceiveIds(receiveIds);
        builder.setFailureProtectionNum(failureProtectionNum);
        builder.setWinSeasonNum(winSeasonNum);
        builder.setSeasonAllNum(seasonAllNum);
        builder.setWinAllNum(winAllNum);
        builder.setAllNum(allNum);
        builder.setHonorSize(kingSeasonSet.size());
        TournamentCommonUseInfo commonUseInfo = getUseMaxCommonUse();
        if(commonUseInfo != null){
            builder.setUseInfo(commonUseInfo.genPb());
        }
        return builder.build();
    }

    public TournamentPositionInfo getPositionInfo() {
        return positionInfo;
    }

    public void setPositionInfo(TournamentPositionInfo positionInfo) {
        this.positionInfo = positionInfo;
    }

    public int getMatchUseTime() {
        return matchUseTime == 0 ? TournamentService.getTournamentConst().ordinaryMatchingTime : matchUseTime;
    }

    public void setMatchUseTime(int matchUseTime) {
        this.matchUseTime = matchUseTime;
    }

    public HashMap<Integer, TournamentPositionTitleInfo> getHighestTitleMap() {
        return highestTitleMap;
    }

    public void setHighestTitleMap(HashMap<Integer, TournamentPositionTitleInfo> highestTitleMap) {
        this.highestTitleMap = highestTitleMap;
    }

    public HashMap<Integer, TournamentPositionTitleInfo> getNowTitleMap() {
        return nowTitleMap;
    }

    public void setNowTitleMap(HashMap<Integer, TournamentPositionTitleInfo> nowTitleMap) {
        this.nowTitleMap = nowTitleMap;
    }

    public String getTitleKey() {
        return titleKey;
    }

    public void setTitleKey(String titleKey) {
        this.titleKey = titleKey;
    }

    public void setSeasonMaxStar(int seasonMaxStar) {
        this.seasonMaxStar = seasonMaxStar;
    }

    public int getSeasonMaxStar() {
        return seasonMaxStar;
    }

    public int getMaxStar() {
        return maxStar;
    }

    public void setMaxStar(int maxStar) {
        this.maxStar = maxStar;
    }

    public Set<Integer> getReceiveIds() {
        return receiveIds;
    }

    public void setReceiveIds(Set<Integer> receiveIds) {
        this.receiveIds = receiveIds;
    }

    public int getFailureProtectionNum() {
        return failureProtectionNum;
    }

    public void setFailureProtectionNum(int failureProtectionNum) {
        this.failureProtectionNum = failureProtectionNum;
    }

    public List<Boolean> getStageWins() {
        return stageWins;
    }

    public void setStageWins(List<Boolean> stageWins) {
        this.stageWins = stageWins;
    }

    public int getSeasonId() {
        return seasonId;
    }

    public void setSeasonId(int seasonId) {
        this.seasonId = seasonId;
    }

    public AtomicInteger getStar() {
        return star;
    }

    public void setStar(AtomicInteger star) {
        this.star = star;
    }

    public int getPositionChangeNum() {
        return positionChangeNum;
    }

    public void setPositionChangeNum(int positionChangeNum) {
        this.positionChangeNum = positionChangeNum;
    }

    public int getWinSeasonNum() {
        return winSeasonNum;
    }

    public void setWinSeasonNum(int winSeasonNum) {
        this.winSeasonNum = winSeasonNum;
    }

    public int getSeasonAllNum() {
        return seasonAllNum;
    }

    public void setSeasonAllNum(int seasonAllNum) {
        this.seasonAllNum = seasonAllNum;
    }

    public int getWinAllNum() {
        return winAllNum;
    }

    public void setWinAllNum(int winAllNum) {
        this.winAllNum = winAllNum;
    }

    public int getAllNum() {
        return allNum;
    }

    public void setAllNum(int allNum) {
        this.allNum = allNum;
    }

    public Set<Integer> getKingSeasonSet() {
        return kingSeasonSet;
    }

    public void setKingSeasonSet(Set<Integer> kingSeasonSet) {
        this.kingSeasonSet = kingSeasonSet;
    }

    public Map<String, TournamentCommonUseInfo> getCommonUseMap() {
        return commonUseMap;
    }

    public void setCommonUseMap(Map<String, TournamentCommonUseInfo> commonUseMap) {
        this.commonUseMap = commonUseMap;
    }

    private static class LoginUpdateCallbackTask extends TLMessageCallbackTask {
        private final Player player;

        public LoginUpdateCallbackTask(Player player) {
            this.player = player;
        }

        @Override
        public void complete(CallbackResponse response) {
            int seasonId = response.getParam(0);
            player.getPlayerTournamentModel().checkSeasonId(seasonId);
        }

        @Override
        public void timeout() {

        }
    }
}

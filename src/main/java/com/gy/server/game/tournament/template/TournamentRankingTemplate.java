package com.gy.server.game.tournament.template;

import com.gy.server.game.util.StringExtUtil;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Map;

/**
 * 排行信息
 * <AUTHOR> 2024/9/9 17:25
 **/
public class TournamentRankingTemplate {

    /**
     * 排名id
     */
    public int id;

    /**
     * 排名
     * left:最小排名    right:最大排名
     */
    public Pair<Integer, Integer> ranking;

    public TournamentRankingTemplate(Map<String, String> map) {
        this.id = Integer.parseInt(map.get("ID"));
        this.ranking = StringExtUtil.string2Pair(map.get("Ranking"), "|", Integer.class, Integer.class);
    }
}

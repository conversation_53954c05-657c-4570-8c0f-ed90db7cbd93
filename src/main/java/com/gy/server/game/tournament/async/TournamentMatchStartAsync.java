package com.gy.server.game.tournament.async;

import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.Configuration;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.callback.response.CallbackResponse;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.lineup.bean.LineupInfoBean;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.text.Text;
import com.gy.server.game.tournament.PlayerTournamentModel;
import com.gy.server.game.tournament.TournamentManager;
import com.gy.server.game.tournament.TournamentService;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.CollectionUtil;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.rpc.TLMessageCallbackTask;

import java.util.Objects;

/**
 * 匹配操作
 *
 * <AUTHOR> 2024/9/11 15:07
 **/
public class TournamentMatchStartAsync extends AsyncCall {

    private Player player;
    private boolean start;
    private long time;
    private boolean sync = false;
    private boolean isKing = false;

    private LineupInfoBean singleLineInfoBean;

    public TournamentMatchStartAsync(Player player, PbProtocol.TournamentMatchReq req, long time, boolean sync) {
        this.player = player;
        this.start = req.getStart();
        this.time = time;
        this.sync = sync;
        this.isKing = player.getPlayerTournamentModel().getStar().get() > TournamentService.getTournamentConst().king;
    }

    public TournamentMatchStartAsync(Player player, boolean start, long time) {
        this.player = player;
        this.start = start;
        this.time = time;
        this.isKing = player.getPlayerTournamentModel().getStar().get() > TournamentService.getTournamentConst().king;
    }

    @Override
    public void execute() {
        deal();
    }

    @Override
    public void asyncExecute() {
        if(start){
            this.singleLineInfoBean = player.getLineupModel().getSingleLineInfoBean(isKing ? LineupType.PVPRealTime2 : LineupType.PVPRealTime1);
        }
    }

    public void deal() {
        PbProtocol.TournamentMatchRst.Builder rst = PbProtocol.TournamentMatchRst.newBuilder().setResult(Text.genOkServerRstInfo());
        rst.setStart(start);

        if(start){
            int day = ServerConstants.getCurrentTimeLocalDateTime().getDayOfMonth();
            if(day < TournamentService.getTournamentConst().seasonStart){
                rst.setResult(Text.genServerRstInfo(Text.实时PVP此赛季尚未开启));
                player.send(PtCode.TOURNAMENT_MATCH_RST, rst.build(), time);
                return;
            }

            if(day > TournamentService.getTournamentConst().seasonEnd){
                rst.setResult(Text.genServerRstInfo(Text.实时PVP此赛季已结束));
                player.send(PtCode.TOURNAMENT_MATCH_RST, rst.build(), time);
                return;
            }

            //发送世界服开始匹配
            sendWorldMatchStart(rst);
        }else {
            //发送世界服取消匹配
            sendWorldMatchClose(rst);
        }
    }

    /**
     * 发送世界服取消匹配
     * @param rst
     */
    private void sendWorldMatchClose(PbProtocol.TournamentMatchRst.Builder rst) {
        logic:{
            int masterWorldId = CommonUtils.getWorldMasterServerId();
            if(masterWorldId == 0){
                rst.setResult(Text.genServerRstInfo(Text.数据异常));
                break logic;
            }

            if (!TournamentManager.getInstance().getGsMatchPlayerMap().containsKey(player.getPlayerId())) {
                break logic;
            }

            ServerCommandRequest request = CommandRequests.newServerCommandRequest("TournamentCommandService.matchClose");
            TLBase.getInstance().getRpcUtil().sendToNodeWithCallBack(new SendWorldMatchCloseCallbackTask(rst, player, sync, time), ServerType.WORLD, masterWorldId, request, player.getPlayerId());
            return;
        }
        if(sync) player.send(PtCode.TOURNAMENT_MATCH_RST, rst.build(), time);
    }

    /**
     * 想世界服发送匹配开始
     * @param rst
     */
    private void sendWorldMatchStart(PbProtocol.TournamentMatchRst.Builder rst){
        logic:{
            int masterWorldId = CommonUtils.getWorldMasterServerId();
            if(masterWorldId == 0){
                rst.setResult(Text.genServerRstInfo(Text.数据异常));
                break logic;
            }

            //开启时间核对在世界服进行
            if (TournamentManager.getInstance().getGsMatchPlayerMap().containsKey(player.getPlayerId())) {
                rst.setResult(Text.genServerRstInfo(Text.已在匹配队列));
                break logic;
            }

            PlayerTournamentModel model = player.getPlayerTournamentModel();
//            if (model.getPositionInfo() == null) {
//                rst.setResult(Text.genServerRstInfo(Text.尚未进行定位));
//                break logic;
//            }

            if(Objects.isNull(singleLineInfoBean) || CollectionUtil.isEmpty(singleLineInfoBean.getStandsMap())){
                rst.setResult(Text.genServerRstInfo(isKing ? Text.实时pvp多队未布阵 : Text.实时pvp单队未布阵));
                break logic;
            }

            ServerCommandRequest request = CommandRequests.newServerCommandRequest("TournamentCommandService.matchStart");
            TLBase.getInstance().getRpcUtil().sendToNodeWithCallBack(new SendWorldMatchStartCallbackTask(rst, player, sync, time), ServerType.WORLD, masterWorldId, request, Configuration.serverId, player.getPlayerId(), model.getStageWins(), model.getMatchUseTime(), model.getStar().get());
            return;
        }

        if(sync) player.send(PtCode.TOURNAMENT_MATCH_RST, rst.build(), time);
    }

    private static class SendWorldMatchCloseCallbackTask extends TLMessageCallbackTask {
        private final PbProtocol.TournamentMatchRst.Builder rst;
        private final Player player;
        private final boolean sync;
        private final long time;

        public SendWorldMatchCloseCallbackTask(PbProtocol.TournamentMatchRst.Builder rst, Player player, boolean sync, long time) {
            this.rst = rst;
            this.player = player;
            this.sync = sync;
            this.time = time;
        }

        @Override
        public void complete(CallbackResponse response) {
            int text = response.getParam(0);
            rst.setResult(Text.genServerRstInfo(text));
            rst.setMatchSuccess(player.getPlayerTournamentModel().getMatchUseTime());

            TournamentManager.getInstance().getGsMatchPlayerMap().remove(player.getPlayerId());
            if(sync) player.send(PtCode.TOURNAMENT_MATCH_RST, rst.build(), time);
        }

        @Override
        public void timeout() {
            rst.setResult(Text.genServerRstInfo(Text.取消匹配超时));
            if(sync) player.send(PtCode.TOURNAMENT_MATCH_RST, rst.build(), time);

        }
    }

    private static class SendWorldMatchStartCallbackTask extends TLMessageCallbackTask {
        private final PbProtocol.TournamentMatchRst.Builder rst;
        private final Player player;
        private final boolean sync;
        private final long time;

        public SendWorldMatchStartCallbackTask(PbProtocol.TournamentMatchRst.Builder rst, Player player, boolean sync, long time) {
            this.rst = rst;
            this.player = player;
            this.sync = sync;
            this.time = time;
        }

        @Override
        public void complete(CallbackResponse response) {
            int text = response.getParam(0);
            if(text == Text.没有异常){
                long start = response.getParam(1);

                rst.setMatchSuccess(player.getPlayerTournamentModel().getMatchUseTime());
                TournamentManager.getInstance().getGsMatchPlayerMap().put(player.getPlayerId(), start);
            }

            rst.setResult(Text.genServerRstInfo(text));
            if(sync) player.send(PtCode.TOURNAMENT_MATCH_RST, rst.build(), time);
        }

        @Override
        public void timeout() {
            rst.setResult(Text.genServerRstInfo(Text.进入匹配队列超时));
            if(sync) player.send(PtCode.TOURNAMENT_MATCH_RST, rst.build(), time);

        }
    }
}

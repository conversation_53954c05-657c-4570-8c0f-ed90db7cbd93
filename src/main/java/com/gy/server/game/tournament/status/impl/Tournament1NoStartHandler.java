package com.gy.server.game.tournament.status.impl;

import com.gy.server.core.battleRule.base.IBattleStatusDeal;
import com.gy.server.game.tournament.bean.TournamentStatusEnum;
import com.gy.server.game.tournament.status.TournamentBattleInfo;

/**
 * 未开启
 * <AUTHOR> 2024/10/14 14:25
 **/
public class Tournament1NoStartHandler implements IBattleStatusDeal<TournamentBattleInfo> {

    @Override
    public void init(TournamentBattleInfo battleInfo) {

    }

    @Override
    public void deal(TournamentBattleInfo battleInfo) {

    }

    @Override
    public boolean finish(TournamentBattleInfo battleInfo) {
        return true;
    }

    @Override
    public int nowStatus() {
        return TournamentStatusEnum.noStart.getType();
    }

    @Override
    public int nextStatus(TournamentBattleInfo battleInfo) {
        return TournamentStatusEnum.ready.getType();
    }

    @Override
    public long getStatusDurationTime(TournamentBattleInfo battleInfo) {
        return 0;
    }

//    @Override
//    public long getStatusStartTime() {
//        LocalDateTime nowLocal = ServerConstants.getCurrentTimeLocalDateTime();
//        nowLocal.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
//        return DateTimeUtil.toMillis(nowLocal);
//    }
}

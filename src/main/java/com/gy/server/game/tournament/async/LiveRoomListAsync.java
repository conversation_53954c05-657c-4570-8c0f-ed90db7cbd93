package com.gy.server.game.tournament.async;

import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbLive;
import com.gy.server.packet.PbProtocol;
import com.gy.server.world.live.LiveHelper;

import java.util.List;

/**
 * @program: tl_game_4_live
 * @description: 直播间列表
 * @author: Huang.<PERSON>
 * @create: 2025/1/11
 **/
public class LiveRoomListAsync extends AsyncCall {

    private List<PbLive.LiveRoom> liveRoomList;
    private Player player;
    private long timestamp;

    public LiveRoomListAsync(Player player, long timestamp) {
        this.timestamp = timestamp;
        this.player = player;
    }

    @Override
    public void execute() {
        PbProtocol.TournamentLiveRoomsRst.Builder builder = PbProtocol.TournamentLiveRoomsRst.newBuilder();
        builder.setResult(Text.genOkServerRstInfo());
        if (liveRoomList!= null) {
            builder.addAllRooms(liveRoomList);
        }
        player.send(PtCode.TOURNAMENT_LIVE_ROOMS_RST, builder.build(), timestamp);
    }

    @Override
    public void asyncExecute() {
        this.liveRoomList = LiveHelper.getTournamentLiveRooms();
    }
}

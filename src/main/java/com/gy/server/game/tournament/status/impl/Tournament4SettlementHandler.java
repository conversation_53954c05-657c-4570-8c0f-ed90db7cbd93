package com.gy.server.game.tournament.status.impl;

import com.gy.server.core.battleRule.base.IBattleStatusDeal;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.tournament.bean.TournamentPlayerRankInfo;
import com.gy.server.game.tournament.bean.TournamentStatusEnum;
import com.gy.server.game.tournament.status.TournamentBattleInfo;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 结算,发奖
 *
 * <AUTHOR> 2024/10/15 11:18
 **/
public class Tournament4SettlementHandler implements IBattleStatusDeal<TournamentBattleInfo> {

    @Override
    public void init(TournamentBattleInfo battleInfo) {

    }

    @Override
    public void deal(TournamentBattleInfo battleInfo) {
        List<TournamentPlayerRankInfo> rankInfos = battleInfo.getCountryRankInfos();

        //key:服务器id  value:key：玩家id value:排名
        Map<Integer, Map<Long, Integer>> rankingMap = new HashMap<>();

        for (int i = 0; i < rankInfos.size(); i++){
            TournamentPlayerRankInfo rankInfo = rankInfos.get(i);
            rankingMap.computeIfAbsent(rankInfo.getServerId(), map -> new HashMap<>()).put(rankInfo.getPlayerId(), i + 1);
        }

        ThreadPool.execute(() -> {
            for (Map.Entry<Integer, Map<Long, Integer>> entry : rankingMap.entrySet()) {
                ServerCommandRequest request = CommandRequests.newServerCommandRequest("TournamentRstCommandService.seasonEndDeal");
                TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, entry.getKey(), request, entry.getValue());
            }
        });
    }

    @Override
    public boolean finish(TournamentBattleInfo battleInfo) {
        return true;
    }

    @Override
    public int nowStatus() {
        return TournamentStatusEnum.settlement.getType();
    }

    @Override
    public int nextStatus(TournamentBattleInfo battleInfo) {
        return TournamentStatusEnum.endAndClear.getType();
    }

    @Override
    public long getStatusDurationTime(TournamentBattleInfo battleInfo) {
        return 0;
    }
}

package com.gy.server.game.tournament.bean;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.tournament.TournamentService;
import com.gy.server.game.tournament.template.TournamentMatchingRuleTemplate;
import com.gy.server.packet.PbTournament;
import com.ttlike.server.tl.baselib.serialize.tournament.TournamentPlayerRankInfoDb;
import org.jetbrains.annotations.NotNull;

import java.io.Serializable;


/**
 * 排行信息进redis
 * 比较，star,update
 * TODO:后续需要补全内容方案，暂时不处理，等策划排行榜方案
 * <AUTHOR> 2024/9/10 10:22
 **/
public class TournamentPlayerRankInfo implements Comparable<TournamentPlayerRankInfo>, Serializable {

    private static final long serialVersionUID = -1486596205907415073L;

    private long playerId;

    private int serverId;

    private String name;

    /**
     * 段位id
     */
    private int danId;

    /**
     * 星数
     */
    private int star;

    private TournamentPositionInfo positionInfo;

    /**
     * 达成时间
     */
    private long update;

    public TournamentPlayerRankInfo(long playerId, String name, int serverId, int star, TournamentPositionInfo positionInfo) {
        this.playerId = playerId;
        this.serverId = serverId;
        this.name = name;
        this.star = star;
        this.danId = TournamentService.getDanByStar(star);
        this.positionInfo = positionInfo == null ? new TournamentPositionInfo(0,0,0) : positionInfo;
        this.update = ServerConstants.getCurrentTimeMillis();
    }

    public TournamentPlayerRankInfo(TournamentPlayerRankInfoDb db) {
        this.playerId = db.getPlayerId();
        this.serverId = db.getServerId();
        this.name = db.getName();
        this.star = db.getStar();
        this.danId = TournamentService.getDanByStar(star);
        if(db.getPositionInfo() != null){
            this.positionInfo = new TournamentPositionInfo(db.getPositionInfo());
        }
        this.update = db.getUpdate();
    }

    public void reset(){
        TournamentMatchingRuleTemplate ruleTemplate = TournamentService.getMatchingRuleMap().get(danId);
        if(ruleTemplate == null){
            int minDenId = TournamentService.getMatchingRuleMap().keySet().stream().min(Integer::compare).orElse(1);
            ruleTemplate = TournamentService.getMatchingRuleMap().get(minDenId);
        }

        this.danId = ruleTemplate.resetBit;
        ruleTemplate = TournamentService.getMatchingRuleMap().get(danId);
        this.star = ruleTemplate.stars.getLeft() + 1;
    }

    public PbTournament.RankInfo genPb(int rank) {
        PbTournament.RankInfo.Builder builder = PbTournament.RankInfo.newBuilder();
        builder.setRank(rank);
        builder.setServerId(serverId);
        builder.setPlayerId(playerId);
        builder.setName(name);
        builder.setDanId(danId);
        builder.setStar(star);
        return builder.build();
    }

    public TournamentPlayerRankInfoDb genDb(){
        TournamentPlayerRankInfoDb db = new TournamentPlayerRankInfoDb();
        db.setPlayerId(this.playerId);
        db.setServerId(this.serverId);
        db.setName(this.name);
        db.setDanId(this.danId);
        return db;
    }

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public int getServerId() {
        return serverId;
    }

    public void setServerId(int serverId) {
        this.serverId = serverId;
    }

    public int getDanId() {
        return danId;
    }

    public void setDanId(int danId) {
        this.danId = danId;
    }

    public int getStar() {
        return star;
    }

    public void setStar(int star) {
        this.star = star;
    }

    public TournamentPositionInfo getPositionInfo() {
        return positionInfo;
    }

    public void setPositionInfo(TournamentPositionInfo positionInfo) {
        this.positionInfo = positionInfo;
    }

    public long getUpdate() {
        return update;
    }

    public void setUpdate(long update) {
        this.update = update;
    }

    @Override
    public int compareTo(@NotNull TournamentPlayerRankInfo o) {
        return o.star == this.star ? Long.compare(o.update, this.update) : Integer.compare(o.star, this.star);
    }

}

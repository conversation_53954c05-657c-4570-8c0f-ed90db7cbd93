package com.gy.server.game.tournament.bean;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 2024/9/11 11:01
 **/
@ProtobufClass
public class TournamentPositionRankInfo implements Serializable {

    private static final long serialVersionUID = 1434284616587420842L;

    @Protobuf
    List<TournamentPlayerRankInfo> ranks = new ArrayList<>();

    public TournamentPositionRankInfo() {
    }

    public TournamentPositionRankInfo(List<TournamentPlayerRankInfo> ranks) {
        this.ranks = ranks;
    }

    public List<TournamentPlayerRankInfo> getRanks() {
        return ranks;
    }

    public void setRanks(List<TournamentPlayerRankInfo> ranks) {
        this.ranks = ranks;
    }
}

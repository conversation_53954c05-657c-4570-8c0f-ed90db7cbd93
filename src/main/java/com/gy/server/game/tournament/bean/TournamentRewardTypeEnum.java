package com.gy.server.game.tournament.bean;

/**
 * 实时pvp奖励类型
 * 赛季奖励，排名奖励，赛季结束统一发
 * 段位奖励，到达段位后可立即领取
 * <AUTHOR> 2024/10/14 13:28
 **/
public enum TournamentRewardTypeEnum {

    /**
     * 赛季奖励
     */
    season(1),

    /**
     * 段位奖励
     */
    dan(2),

    /**
     * 排名奖励
     */
    rank(3),
    ;

    private int type;

    TournamentRewardTypeEnum(int type) {
        this.type = type;
    }

    public static TournamentRewardTypeEnum getTournamentRewardTypeEnum(int type){
        switch (type){
            case 1: return season;
            case 2: return dan;
            case 3: return rank;
            default : return null;
        }
    }

    public int getType() {
        return type;
    }
}

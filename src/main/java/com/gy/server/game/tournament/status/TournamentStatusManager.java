package com.gy.server.game.tournament.status;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.battleRule.AbstractBattleManager;
import com.gy.server.core.battleRule.base.BaseBattleInfo;
import com.gy.server.core.log.CommonLogger;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.tournament.TournamentGlobalData;
import com.gy.server.game.tournament.bean.TournamentStatusEnum;
import com.gy.server.utils.time.DateTimeUtil;

import java.util.Objects;

/**
 * 实时pvp状态管理器
 *
 * <AUTHOR> 2024/10/14 14:28
 **/
public class TournamentStatusManager extends AbstractBattleManager<TournamentBattleInfo> {

    public TournamentStatusManager(boolean isRunner) {
        super(isRunner);
    }

    @Override
    protected void printChangeStatus(int currentStatus, int nextStatus, long statusStartTime) {
        CommonLogger.info(String.format("实时pvp状态切换：%s -> %s, %s 开始时间: %s, 切换时间: %s", TournamentStatusEnum.of(currentStatus), TournamentStatusEnum.of(nextStatus),
                TournamentStatusEnum.of(currentStatus), DateTimeUtil.toLocalDateTime(statusStartTime), ServerConstants.getCurrentTimeLocalDateTime()));
    }

    @Override
    protected void saveSonStatus(BaseBattleInfo baseBattleInfo, int currentStatus) {

    }

    @Override
    public boolean isGameServer() {
        return false;
    }

    @Override
    public boolean isWorldServer() {
        return true;
    }

    @Override
    public TournamentBattleInfo getBattleInfo() {
        TournamentGlobalData globalData = GlobalDataManager.getData(GlobalDataType.tournament);
        TournamentBattleInfo battleInfo = globalData.getBattleInfo();
        if (Objects.isNull(battleInfo)) {
            battleInfo = new TournamentBattleInfo();
            battleInfo.setStatus(TournamentStatusEnum.noStart.getType());
            globalData.setBattleInfo(battleInfo);
        }
        return globalData.getBattleInfo();
    }

    @Override
    public void saveBattleInfo(TournamentBattleInfo battleInfo) {

    }

    @Override
    public void start() {
        try {
            super.initClazz();
        } catch (IllegalAccessException | InstantiationException e) {
            e.printStackTrace();
        }
    }


}

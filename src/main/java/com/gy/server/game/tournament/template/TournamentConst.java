package com.gy.server.game.tournament.template;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.util.StringExtUtil;
import org.apache.commons.lang3.tuple.Pair;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * pvp常量类
 *
 * <AUTHOR> 2024/9/9 17:54
 **/
public class TournamentConst {

    /**
     * 王者以下预计匹配时间
     */
    public int ordinaryMatchingTime;

    /**
     * 人机投放时间段（1-5秒内投放人机）
     */
    public Pair<Integer, Integer> releasePeriod;

    /**
     * 王者及以上预计匹配时间
     */
    public int kingMatchingTime;

    /**
     * 超时后每x秒扩大一次范围
     */
    public int expandTime;

    /**
     * 每次扩大x星
     */
    public int eachEnlargement;

    /**
     * 黄金段位（x星）及以下失败不掉星
     */
    public int ordinary;

    /**
     * 黄金（x星）以上每天有x次失败保护，失败不掉星。
     */
    public Pair<Integer, Integer> failureProtection;

    /**
     * x星及以下，满足五局内输了三局，下局必定匹配人机B。
     */
    public int goldMatchingMachine;

    /**
     * x星及以下，连续输两局，下局就必定匹配人机B。
     */
    public int loseStreak;

    /**
     * 人机A：在战区战力排行榜上拉自身战力上下x名玩家的镜像
     */
    public int robotA;

    /**
     * 人机B：在战区战力排行榜拉自身战力-x(万分比)上下x名的玩家镜像
     */
    public Pair<Integer, Integer> robotB;

    /**
     * 禁用阶段持续时间（秒）
     */
    public int disabledTime;

    /**
     * 选择阶段持续时间（秒）
     */
    public int selectionTime;

    /**
     * 如果双方确认出战阵容，后剩余时间大于x秒则立即进入x秒倒计时
     */
    public Pair<Integer, Integer> waitingTime;

    /**
     * 战绩最多保存数量
     */
    public int saveQuantity;

    /**
     * 本服排行榜最多展示数量
     */
    public int displayQuantity;

    /**
     * 直播后端初始抓取数
     */
    public int grabQuantity;

    /**
     * 回放抓取战区含有各个服务器排行榜前x名玩家
     */
    public int grabRank;

    /**
     * 最多显示数量
     */
    public int maximumDisplayNum;

    /**
     * 赛季奖励结算日
     */
    public int settlementDate;

    /**
     * 战斗中每名角色出招思考时间（秒）
     */
    public int thinkingTime;

    /**
     * 超过x秒后取消匹配
     */
    public int unMatch;

    /**
     * 竞技场buff：第4回合开始时，伤害增加30%
     */
    public int battlefieldBuff1;

    /**
     * 竞技场buff：第5回合以及后续每个回合开始时，伤害增加20%
     */
    public int battlefieldBuff2;

    /**
     * 战斗场景id
     */
    public int battleScene;

    /**
     * 星数大于x时为王者段位
     */
    public int king;

    /**
     * 连续胜利场数大于x时，战斗结束额外获得1颗星
     */
    public int winningStreak;

    /**
     * 每周x次定位更改次数
     */
    public int positioningRefresh;

    /**
     * 赛季开启时间，每月
     */
    public int seasonStart;

    /**
     * 赛季结束时间，每月
     */
    public int seasonEnd;

    /**
     * 开启时间点
     */
    public int openTime;

    /**
     * 结束时间点
     */
    public int endTime;

    /**
     * 第一赛季开启时间
     */
    public LocalDateTime firstSeason;

    /**
     * 最大直播间数量
     */
    public int liveMaxCount = 20;
    /**
     * 弹幕发送冷却时间（毫秒）
     */
    public long chatSendCD = 5 * 1000;
    /**
     * 直播间最大持续时间（毫秒）
     */
    public long liveRoomMaxTime = 60 * 60* 1000;


    public TournamentConst(Map<String, String> map) {
        this.ordinaryMatchingTime = Integer.parseInt(map.get("OrdinaryMatchingTime"));
        this.releasePeriod = StringExtUtil.string2Pair(map.get("ReleasePeriod"), "|", Integer.class, Integer.class);
        this.kingMatchingTime = Integer.parseInt(map.get("KingMatchingTime"));
        this.expandTime = Integer.parseInt(map.get("ExpandTime"));
        this.eachEnlargement = Integer.parseInt(map.get("EachEnlargement"));
        this.ordinary = Integer.parseInt(map.get("Ordinary"));
        this.failureProtection = StringExtUtil.string2Pair(map.get("FailureProtection"), "|", Integer.class, Integer.class);
        this.goldMatchingMachine = Integer.parseInt(map.get("GoldMatchingMachine"));
        this.loseStreak = Integer.parseInt(map.get("LoseStreak"));
        this.robotA = Integer.parseInt(map.get("RobotA"));
        this.robotB = StringExtUtil.string2Pair(map.get("RobotB"), "|", Integer.class, Integer.class);
        this.disabledTime = Integer.parseInt(map.get("DisabledTime"));
        this.selectionTime = Integer.parseInt(map.get("SelectionTime"));
        this.waitingTime = StringExtUtil.string2Pair(map.get("WaitingTime"), "|", Integer.class, Integer.class);
        this.saveQuantity = Integer.parseInt(map.get("SaveQuantity"));
        this.displayQuantity = Integer.parseInt(map.get("DisplayQuantity"));
        this.grabQuantity = Integer.parseInt(map.get("GrabQuantity"));
        this.grabRank = Integer.parseInt(map.get("GrabRank"));
        this.maximumDisplayNum = Integer.parseInt(map.get("MaximumDisplayNum"));
        this.settlementDate = Integer.parseInt(map.get("SettlementDate"));
        this.thinkingTime = Integer.parseInt(map.get("ThinkingTime"));
        this.unMatch = Integer.parseInt(map.get("unmatch"));
        this.battlefieldBuff1 = Integer.parseInt(map.get("BattlefieldBuff1"));
        this.battlefieldBuff2 = Integer.parseInt(map.get("BattlefieldBuff2"));
        this.battleScene = Integer.parseInt(map.get("BattleScene"));
        this.king = Integer.parseInt(map.get("King"));
        this.winningStreak = Integer.parseInt(map.get("WinningStreak"));
        this.positioningRefresh = Integer.parseInt(map.get("PositioningRefresh"));
        this.seasonStart = Integer.parseInt(map.get("seasonStar"));
        this.seasonEnd = Integer.parseInt(map.get("seasonEnd"));
        this.openTime = Integer.parseInt(map.get("openTime"));
        this.endTime = Integer.parseInt(map.get("endTime"));

        this.liveMaxCount = Integer.parseInt(map.get("liveMaxCount"));
        this.chatSendCD = Long.parseLong(map.get("sendCD"))*1000;
        this.liveRoomMaxTime = Long.parseLong(map.get("maximumTime"))*60*1000;

        String temp = map.get("firstSeason");
        String[] str = temp.split("\\|");
        int year = Integer.parseInt(str[0]);
        int month = Integer.parseInt(str[1]);
        LocalDateTime nowLocal = ServerConstants.getCurrentTimeLocalDateTime().withYear(year).withMonth(month).withDayOfMonth(seasonStart).withHour(openTime).withMinute(0).withSecond(0).withNano(0);
        this.firstSeason = nowLocal;
    }
}

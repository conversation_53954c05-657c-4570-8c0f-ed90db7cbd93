package com.gy.server.game.tournament;

import com.gy.server.core.Configuration;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.core.thread.AbstractRunner;
import com.gy.server.game.combat.CombatManager;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.text.Text;
import com.gy.server.game.tournament.bean.CallUpBattleInitInfoIndex;
import com.gy.server.game.tournament.bean.TournamentCallUpBattleInitInfo;
import com.gy.server.game.tournament.bean.TournamentMatchEnum;
import com.gy.server.game.tournament.stage.TournamentStage;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.time.DateTimeUtil;
import com.gy.server.world.tournament.bean.TournamentPlayerMatchInfo;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 实时pvp管理类
 *
 * <AUTHOR> 2024/10/11 9:29
 **/
public class TournamentManager extends AbstractRunner {

    /*******************************gs************************************/

    /**
     * key:玩家id
     */
    private Map<Long, CallUpBattleInitInfoIndex> indexMap = new ConcurrentHashMap<>();

    private Map<CallUpBattleInitInfoIndex, TournamentCallUpBattleInitInfo> callUpMap= new ConcurrentHashMap();

    /**
     * 正在进行匹配的玩家 内存中
     * key:玩家id     value:开始匹配时间戳
     */
    private Map<Long, Long> gsMatchPlayerMap = new ConcurrentHashMap<>();

    /*******************************gs************************************/

    public void addTournamentCallUpBattleInitInfo(CallUpBattleInitInfoIndex initInfoIndex, TournamentCallUpBattleInitInfo bean){
        if(Configuration.serverId == initInfoIndex.getAtkServerId()){
            indexMap.put(bean.getAtkRecordPlayer().getId(), initInfoIndex);
            if(bean != null){
                callUpMap.put(initInfoIndex, bean);
            }
        }

        if(Configuration.serverId == initInfoIndex.getDefServerId()){
            indexMap.put(bean.getDefRecordPlayer().getId(), initInfoIndex);
        }
    }

    public CallUpBattleInitInfoIndex addTournamentCallUpBattleInitInfo(TournamentCallUpBattleInitInfo bean){
        CallUpBattleInitInfoIndex initInfoIndex = new CallUpBattleInitInfoIndex(bean.getAtkServerId(), bean.getDefServerId(), bean.getAtkRecordPlayer().getId(), bean.getDefRecordPlayer().getId());
        addTournamentCallUpBattleInitInfo(initInfoIndex, bean);
        return initInfoIndex;
    }

    @Override
    public String getRunnerName() {
        return "TournamentManager";
    }

    @Override
    protected void subRunnerExecute() throws Exception {
        if(Configuration.isGameServer()){
            checkCallUpStage();
        }
    }

    private void checkCallUpStage(){
        List<CallUpBattleInitInfoIndex> removeList = new ArrayList<>();
        for (CallUpBattleInitInfoIndex bean : callUpMap.keySet()) {
            TournamentCallUpBattleInitInfo initInfo = callUpMap.get(bean);
            if(initInfo == null){
                indexMap.remove(bean.getAtkPlayerId());
                indexMap.remove(bean.getDefPlayerId());
            }

            if(ServerConstants.getCurrentTimeMillis() >= initInfo.getSelectFinish()){
                //初始化战斗
                TournamentStage stage = new TournamentStage(initInfo);
                stage.init();
                CombatManager.combatPrepare(stage);

                // PVP
                removeList.add(bean);

                PbProtocol.TournamentBattleInitNotify.Builder battleNotify = PbProtocol.TournamentBattleInitNotify.newBuilder().setResult(Text.genOkServerRstInfo());
                battleNotify.setStage(stage.genAbstractPb());
                battleInitNotify(initInfo.getAtkRecordPlayer().getId(), battleNotify.build());
                battleInitNotify(initInfo.getDefRecordPlayer().getId(), battleNotify.build());
            }else {
                if(initInfo.getAtkSelect() != 0 && initInfo.getDefSelect() != 0){
                    if(initInfo.getSelectFinish() - ServerConstants.getCurrentTimeMillis() > TournamentService.getTournamentConst().waitingTime.getLeft() * DateTimeUtil.MillisOfSecond){
                        initInfo.setSelectFinish(ServerConstants.getCurrentTimeMillis() + TournamentService.getTournamentConst().waitingTime.getRight() * DateTimeUtil.MillisOfSecond);
                        TournamentHelper.disableSelectNotify(initInfo);
                    }
                }
            }

        }

        removeList.forEach(bean -> callUpMap.remove(bean));
    }

    public void battleInitNotify(long playerId, PbProtocol.TournamentBattleInitNotify notify){
        Player player = PlayerManager.getOnlinePlayer(playerId);
        if(player != null){
            player.send(PtCode.TOURNAMENT_BATTLE_INIT_NOTIFY, notify);
        }
    }

    /**
     * 匹配成功通知gs
     * 匹配方为进攻方，被匹配方为防守方
     * def为空时被匹配方为机器人
     * @param atk          NotNull
     * @param def
     * @param matchPattern
     */
    private void matchResultNotifyGs(TournamentPlayerMatchInfo atk, TournamentPlayerMatchInfo def, TournamentMatchEnum matchPattern, int text) {
        ServerCommandRequest commandRequest = CommandRequests.newServerCommandRequest("TournamentRstCommandService.matchResultDeal");
        TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, atk.getServerId(), commandRequest, text, atk, def, matchPattern, ServerConstants.getCurrentTimeMillis());
    }

    @Override
    public long getRunnerInterval() {
        return DateTimeUtil.MillisOfSecond;
    }

    private static TournamentManager instance = new TournamentManager();

    public static TournamentManager getInstance() {
        return instance;
    }

    public Map<Long, CallUpBattleInitInfoIndex> getIndexMap() {
        return indexMap;
    }

    public void setIndexMap(Map<Long, CallUpBattleInitInfoIndex> indexMap) {
        this.indexMap = indexMap;
    }

    public Map<CallUpBattleInitInfoIndex, TournamentCallUpBattleInitInfo> getCallUpMap() {
        return callUpMap;
    }

    public void setCallUpMap(Map<CallUpBattleInitInfoIndex, TournamentCallUpBattleInitInfo> callUpMap) {
        this.callUpMap = callUpMap;
    }

    public Map<Long, Long> getGsMatchPlayerMap() {
        return gsMatchPlayerMap;
    }

}

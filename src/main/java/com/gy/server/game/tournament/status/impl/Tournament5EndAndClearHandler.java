package com.gy.server.game.tournament.status.impl;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.core.battleRule.base.IBattleStatusDeal;
import com.gy.server.game.tournament.TournamentService;
import com.gy.server.game.tournament.bean.*;
import com.gy.server.game.tournament.status.TournamentBattleInfo;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.*;

/**
 * 结束并处理数据
 *
 * <AUTHOR> 2024/10/15 15:27
 **/
public class Tournament5EndAndClearHandler implements IBattleStatusDeal<TournamentBattleInfo> {

    @Override
    public void init(TournamentBattleInfo battleInfo) {

    }

    @Override
    public void deal(TournamentBattleInfo battleInfo) {
        //留本赛季排行
        List<TournamentPlayerRankInfo> rankInfos = battleInfo.getCountryRankInfos();
        if(!rankInfos.isEmpty()){
            Map<TournamentPositionInfo, List<TournamentPlayerRankInfo>> rankMap = new HashMap<>();
            for (int i = 0; i < rankInfos.size(); i++){
                TournamentPlayerRankInfo rankInfo = rankInfos.get(i);
                TournamentPositionInfo positionInfo = rankInfo.getPositionInfo().copy();

                if(positionInfo.getQuality() == TournamentTitleQualityEnum.third.getQuality()){
                    rankMap.computeIfAbsent(positionInfo, list -> new ArrayList<>()).add(rankInfo);
                    positionInfo.setThird(0);
                    rankMap.computeIfAbsent(positionInfo, list -> new ArrayList<>()).add(rankInfo);
                    positionInfo.setSecond(0);
                    rankMap.computeIfAbsent(positionInfo, list -> new ArrayList<>()).add(rankInfo);
                }

                if(positionInfo.getQuality() == TournamentTitleQualityEnum.second.getQuality()){
                    rankMap.computeIfAbsent(positionInfo, list -> new ArrayList<>()).add(rankInfo);
                    positionInfo.setSecond(0);
                    rankMap.computeIfAbsent(positionInfo, list -> new ArrayList<>()).add(rankInfo);
                }

                if(positionInfo.getQuality() == TournamentTitleQualityEnum.first.getQuality()){
                    rankMap.computeIfAbsent(positionInfo, list -> new ArrayList<>()).add(rankInfo);
                }
            }

            Map<Integer, Integer> qualityNum = TournamentService.getQualityNumMap();
            TournamentPositionInfo countryPosition = new TournamentPositionInfo(0, 0, 0);
            rankMap.put(countryPosition, rankInfos.subList(0, Math.min(qualityNum.get(countryPosition.getQuality()), rankInfos.size())));

            for (Map.Entry<TournamentPositionInfo, List<TournamentPlayerRankInfo>> entry : rankMap.entrySet()) {
                List<TournamentPlayerRankInfo> List = entry.getValue();
                List<TournamentPlayerRankInfo> newList = List.subList(0, Math.min(qualityNum.get(countryPosition.getQuality()), rankInfos.size()));
                if(!newList.isEmpty()){
                    rankMap.put(entry.getKey(), newList);
                }
            }

            //本赛季排行进入排行榜
            ThreadPool.execute(() -> {
                for (Map.Entry<TournamentPositionInfo, List<TournamentPlayerRankInfo>> entry : rankMap.entrySet()) {
                    List<TournamentPlayerRankInfo> List = entry.getValue();
                    if(!List.isEmpty()){
                        String tournamentKey = GsRedisKey.Tournament.tournament_rank.getRedisKey(battleInfo.getSeasonId(), entry.getKey().getKey());
                        TLBase.getInstance().getRedisAssistant().setBean(tournamentKey, new TournamentPositionRankInfo(List));
                    }
                }
            });

            //清空数据
            battleInfo.clear();
        }

    }

    @Override
    public boolean finish(TournamentBattleInfo battleInfo) {
        return true;
    }

    @Override
    public int nowStatus() {
        return TournamentStatusEnum.endAndClear.getType();
    }

    @Override
    public int nextStatus(TournamentBattleInfo battleInfo) {
        return TournamentStatusEnum.abort.getType();
    }

    @Override
    public long getStatusDurationTime(TournamentBattleInfo battleInfo) {
        return 0;
    }
}

package com.gy.server.game.tournament.template;

import java.util.Map;

/**
 * 实时pvp机器人表
 *
 * <AUTHOR> 2024/12/2 16:50
 **/
public class TournamentRobotTemplate {

    public int id;

    /**
     * 段位id
     */
    public int points;

    /**
     * 机器人id
     */
    public int robotId;

    public TournamentRobotTemplate(Map<String, String> map) {
        this.id = Integer.parseInt(map.get("Id"));
        this.points = Integer.parseInt(map.get("points"));
        this.robotId = Integer.parseInt(map.get("robotId"));
    }
}

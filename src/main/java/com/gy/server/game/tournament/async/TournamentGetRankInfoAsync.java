package com.gy.server.game.tournament.async;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.callback.response.CallbackResponse;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.text.Text;
import com.gy.server.game.tournament.bean.TournamentPlayerRankInfo;
import com.gy.server.game.tournament.bean.TournamentPositionInfo;
import com.gy.server.game.tournament.bean.TournamentPositionRankInfo;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbTournament;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.rpc.TLMessageCallbackTask;

import java.util.List;

/**
 * 获取排行榜 异步操作
 *
 * <AUTHOR> 2024/10/15 17:10
 **/
public class TournamentGetRankInfoAsync extends AsyncCall {

    /**
     * 玩家
     */
    private Player player;

    /**
     * 查询赛季
     */
    private int selectSeasonId;
    /**
     * 查询位置
     */
    private TournamentPositionInfo selectPositionInfo;

    private long time;

    private int masterWorldId;

    private List<PbTournament.RankInfo> rankInfoList;

    private PbTournament.RankInfo myRankInfo;

    private TournamentPositionRankInfo positionRankInfo;

    public TournamentGetRankInfoAsync(Player player, int selectSeasonId, TournamentPositionInfo selectPositionInfo, long time) {
        this.player = player;
        this.selectSeasonId = selectSeasonId;
        this.selectPositionInfo = selectPositionInfo;
        this.time = time;
    }

    @Override
    public void execute() {
        PbProtocol.TournamentRankInfoRst.Builder rst = PbProtocol.TournamentRankInfoRst.newBuilder().setResult(Text.genOkServerRstInfo());
        if(player.getPlayerTournamentModel().getSeasonId() == selectSeasonId){
            //询问世界服
            ServerCommandRequest request = CommandRequests.newServerCommandRequest("TournamentCommandService.getRankInfo");
            TLBase.getInstance().getRpcUtil().sendToNodeWithCallBack(new ExecuteCallbackTask(rst), ServerType.WORLD, masterWorldId, request, selectPositionInfo);
        }

        if(positionRankInfo != null){
            List<TournamentPlayerRankInfo> rankInfos = positionRankInfo.getRanks();
            for(int i = 0; i < rankInfos.size(); i++){
                PbTournament.RankInfo rankInfo = rankInfos.get(i).genPb(i + 1);
                rst.addRankInfo(rankInfo);
                if(rankInfo.getPlayerId() == player.getPlayerId()){
                    rst.setMyRankInfo(rankInfo);
                }
            }
        }

        player.send(PtCode.TOURNAMENT_RANK_INFO_RST, rst.build(), time);
    }

    @Override
    public void asyncExecute() {
        if(player.getPlayerTournamentModel().getSeasonId() == selectSeasonId){
            masterWorldId = CommonUtils.getWorldMasterServerId();
        }else {
            //查询redis
            String tournamentKey = GsRedisKey.Tournament.tournament_rank.getRedisKey(selectSeasonId, selectPositionInfo.getKey());
            positionRankInfo = TLBase.getInstance().getRedisAssistant().getBean(TournamentPositionRankInfo.class, tournamentKey);
        }
    }

    private class ExecuteCallbackTask extends TLMessageCallbackTask {
        private final PbProtocol.TournamentRankInfoRst.Builder rst;

        public ExecuteCallbackTask(PbProtocol.TournamentRankInfoRst.Builder rst) {
            this.rst = rst;
        }

        @Override
        public void complete(CallbackResponse response) {
            positionRankInfo = response.getParam(0);
        }

        @Override
        public void timeout() {
            rst.setResult(Text.genServerRstInfo(Text.实时pvp获取排行榜超时));
        }
    }
}

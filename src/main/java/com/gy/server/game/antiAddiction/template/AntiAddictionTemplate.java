package com.gy.server.game.antiAddiction.template;

import org.apache.commons.lang3.tuple.Pair;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 防沉迷模板类
 *
 * <AUTHOR> - [Created on 2023/3/22 11:14]
 */
public class AntiAddictionTemplate {

    public int id;

    public int age;
    /**
     * 周几可在线
     */
    public List<DayOfWeek> weekList = new ArrayList<>();
    /**
     * 是否包含法定节假日
     */
    public boolean isHoliday;
    /**
     * 可在线时间段
     */
    public Pair<LocalTime, LocalTime> allowedTime;
    /**
     * 累计可玩时长
     */
    public int maximumTime;
    /**
     * 单笔充值最大值
     */
    public int singleRecharge;
    /**
     * 每月累计最大值
     */
    public int monthlyRecharge;

}

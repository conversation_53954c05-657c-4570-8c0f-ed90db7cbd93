package com.gy.server.game.antiAddiction;

import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.game.player.PlayerModel;
import com.gy.server.game.player.event.PlayerEvent;
import com.gy.server.game.player.event.PlayerEventHandler;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.packet.PbUser;
import com.ttlike.server.tl.baselib.serialize.player.PlayerBlobDb;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

/**
 * 防沉迷Model
 *
 * <AUTHOR> - [Created on 2023/3/23 17:28]
 */
public class PlayerAntiAddictionModel extends PlayerModel implements PlayerEventHandler {


    public PlayerAntiAddictionModel(Player player) {
        super(player);
    }

    @Override
    protected void loadData(PlayerBlobDb playerBlob) {

    }

    @Override
    protected void saveData(PlayerBlobDb playerBlob) {

    }


    public PbUser.AntiAddictionInfo genPb() {
        return PbUser.AntiAddictionInfo
                .newBuilder()
                .setIsAntiAddiction(getPlayer().isAntiAddiction())
                .setKickOutTime(getPlayer().getAntiLogoutTime())
                .setMonthChargePrices(getPlayer().getTotalRecharge()).build();
    }

    /**
     * 登出异步操作
     */
    @Override
    public void asyncLogoutOperation() {
        // 通知防沉迷增加在线时间
        long addMillTime = PlayerHelper.calcPlayerOnline(getPlayer());
        AntiAddictionManager.loginOutAddOnlineTime(getPlayer().getAccountId(), addMillTime);
    }

    @Override
    public PlayerEventType[] getEventTypes() {
        return new PlayerEventType[]{
                PlayerEventType.charge,
        };
    }

    @Override
    public void handle(PlayerEvent event) {
        switch (event.getEventType()) {
            case charge: {
                if (getPlayer().isAntiAddiction()) {
                    int price = event.getParam(3);
                    ThreadPool.execute(() -> AntiAddictionManager.addChargePrice(getPlayer(), price));
                }
                break;
            }
        }
    }
}

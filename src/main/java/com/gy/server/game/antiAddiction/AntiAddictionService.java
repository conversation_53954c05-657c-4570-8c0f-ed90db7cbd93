package com.gy.server.game.antiAddiction;

import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.antiAddiction.template.AntiAddictionTemplate;
import com.gy.server.game.service.Service;
import com.gy.server.utils.time.DateTimeUtil;
import org.apache.commons.lang3.tuple.Pair;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 防沉迷服务类
 *
 * <AUTHOR> - [Created on 2023/3/22 11:09]
 */
public class AntiAddictionService implements Service {

    /**
     * 节假日
     */
    private static Set<LocalDate> holidaySet = new HashSet<>();
    /**
     * 防沉迷 Key:age
     */
    private static TreeMap<Integer, AntiAddictionTemplate> addictionTemplateMap = new TreeMap<>();
    /**
     * 最大防沉迷年龄
     */
    private static int maxAddictionAge;


    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.antiAddictionSystem_config);
        TreeMap<Integer, AntiAddictionTemplate> addictionTemplateMapTemp = new TreeMap();
        for (Map<String, String> map : mapList) {
            AntiAddictionTemplate antiAddictionTemplate = new AntiAddictionTemplate();
            antiAddictionTemplate.id = Integer.parseInt(map.get("id"));
            antiAddictionTemplate.age = Integer.parseInt(map.get("age"));
            if(!"-1".equals(map.get("week"))){
                antiAddictionTemplate.weekList = Arrays.stream(map.get("week").split(",")).map(week -> DayOfWeek.of(Integer.parseInt(week))).collect(Collectors.toList());
            }
            antiAddictionTemplate.isHoliday = Integer.parseInt(map.get("holiday")) == 1;
            if(!"-1".equals(map.get("allowedTimePeriod"))){
                String[] allowedTimeArr = map.get("allowedTimePeriod").split("-");
                antiAddictionTemplate.allowedTime = Pair.of(LocalTime.parse(allowedTimeArr[0]), LocalTime.parse(allowedTimeArr[1]));
            }
            antiAddictionTemplate.maximumTime = Integer.parseInt(map.get("maximumTime"));
            antiAddictionTemplate.singleRecharge = Integer.parseInt(map.get("singleRecharge"));
            antiAddictionTemplate.monthlyRecharge = Integer.parseInt(map.get("monthlyRecharge"));
            addictionTemplateMapTemp.put(antiAddictionTemplate.age, antiAddictionTemplate);
        }
        addictionTemplateMap = addictionTemplateMapTemp;
        maxAddictionAge = Collections.max(addictionTemplateMap.keySet());

        mapList = ConfigReader.read(ConfigFile.antiAddictionSystem_holiday);
        Set<LocalDate> holidaySetTemp = new HashSet<LocalDate>();
        for (Map<String, String> map : mapList) {
            LocalDate holiday = LocalDate.parse(map.get("date"));
            holidaySetTemp.add(holiday);
        }
        holidaySet = holidaySetTemp;
    }

    @Override
    public void clearConfigData() {
        addictionTemplateMap.clear();
        holidaySet.clear();
    }

    public static Set<LocalDate> getHolidaySet() {
        return holidaySet;
    }

    public static TreeMap<Integer, AntiAddictionTemplate> getAddictionTemplateMap() {
        return addictionTemplateMap;
    }

    public static int getMaxAddictionAge() {
        return maxAddictionAge;
    }
}

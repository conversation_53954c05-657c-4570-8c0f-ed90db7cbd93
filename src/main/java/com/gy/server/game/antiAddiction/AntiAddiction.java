package com.gy.server.game.antiAddiction;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;
import com.gy.server.core.ServerConstants;

/**
 * 防沉迷VO
 *
 * <AUTHOR> - [Created on 2023/3/22 10:57]
 */
@ProtobufClass
public class AntiAddiction {

    /**
     * 今日在线时长
     */
    @Protobuf(order = 1)
    private long todayOnlineTime;
    /**
     * 本月充值金额
     */
    @Protobuf(order = 2)
    private int monthChargePrices;
    /**
     * 每日数据清理时间点
     */
    @Protobuf(order = 3)
    private long dailyDataClearTime = ServerConstants.getCurrentTimeMillis();

    public long getTodayOnlineTime() {
        return todayOnlineTime;
    }

    public int getMonthChargePrices() {
        return monthChargePrices;
    }

    public void addPlayerOnlineTime(long time) {
        this.todayOnlineTime += time;
    }

    public void setTodayOnlineTime(long todayOnlineTime) {
        this.todayOnlineTime = todayOnlineTime;
    }

    public void setMonthChargePrices(int monthChargePrices) {
        this.monthChargePrices = monthChargePrices;
    }

    public long getDailyDataClearTime() {
        return dailyDataClearTime;
    }

    public void setDailyDataClearTime(long dailyDataClearTime) {
        this.dailyDataClearTime = dailyDataClearTime;
    }

    public void addChargePrice(int chargePrice) {
        this.monthChargePrices += chargePrice;
    }
}

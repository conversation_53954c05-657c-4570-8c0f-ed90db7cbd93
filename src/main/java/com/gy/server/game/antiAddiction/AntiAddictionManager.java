package com.gy.server.game.antiAddiction;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Set;
import java.util.TreeMap;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.antiAddiction.template.AntiAddictionTemplate;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.pay.PayItem;
import com.gy.server.game.pay.PayService;
import com.gy.server.game.player.Player;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.gy.server.utils.time.DateTimeDurationType;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.CommonsConfiguration;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.redis.key.BaseRedisKey;

/**
 * 防沉迷管理类
 *
 * <AUTHOR> - [Created on 2023/3/22 10:55]
 */
public class AntiAddictionManager {

    /**
     * 判断是否防沉迷系统禁止登录
     *
     * @return true 禁止登陆
     */
    public static boolean isAntiAddictionBanLogin(int age, String accountId) {
        if (CommonsConfiguration.runMode.isPress()) {
            return false;
        }
        if (!isAntiAddiction(age)) {
            return false;
        }
        Set<LocalDate> holidaySet = AntiAddictionService.getHolidaySet();
        AntiAddictionTemplate addictionTemplate = getAntiAddictionTemplate(age);
        // 周几可在线 或者是法定节假日
        LocalDateTime now = ServerConstants.getCurrentTimeLocalDateTime();
        DayOfWeek nowDayOfWeek = now.getDayOfWeek();
        if (!addictionTemplate.weekList.contains(nowDayOfWeek)) {
            if (addictionTemplate.isHoliday) {
                if (!holidaySet.contains(now.toLocalDate())) {
                    return true;
                }
            } else {
                return true;
            }
        }
        // 可在线时间段
        LocalTime nowTime = now.toLocalTime();
        if (nowTime.isBefore(addictionTemplate.allowedTime.getLeft()) || nowTime.isAfter(addictionTemplate.allowedTime.getRight())) {
            return true;
        }
        // 本日在线时长配置
        long todayOnlineTime = getTodayOnlineTime(accountId);
        return todayOnlineTime >= addictionTemplate.maximumTime * DateTimeUtil.MillisOfMinute;
    }

    /**
     * 年龄是否满足防沉迷
     *
     * @return true 防沉迷装填
     */
    public static boolean isAntiAddiction(int age) {
        int maxAddictionAge = AntiAddictionService.getMaxAddictionAge();
        return age < maxAddictionAge;
    }

    /**
     * 获取AntiAddictionTemplate
     */
    public static AntiAddictionTemplate getAntiAddictionTemplate(int age) {
        TreeMap<Integer, AntiAddictionTemplate> addictionTemplateMap = AntiAddictionService.getAddictionTemplateMap();
        if(CollectionUtil.isEmpty(addictionTemplateMap.tailMap(age, false))){
            //找不到合适年龄，默认用最大年龄，就是最后一条数据
            return addictionTemplateMap.lastEntry().getValue();
        }
        return addictionTemplateMap.tailMap(age, false).firstEntry().getValue();
    }


    /**
     * 通过账号ID 获取防沉迷信息
     * 分布式锁
     *
     * @return 防沉迷信息
     */
    public static AntiAddiction getAntiAdditionByAccountId(String accountId) {
        String key = BaseRedisKey.Commons.ANTI_ADDICTION.getRedisKey(accountId);
        byte[] bytes = TLBase.getInstance().getRedisAssistant().bytesGet(key);
        AntiAddiction antiAddiction;
        if (bytes != null) {
            antiAddiction = PbUtilCompress.decode(AntiAddiction.class, bytes);
        } else {
            antiAddiction = new AntiAddiction();
            TLBase.getInstance().getRedisAssistant().bytesSet(key, PbUtilCompress.encode(antiAddiction));
        }
        if (antiAddiction != null) {
            LocalDateTime now = ServerConstants.getCurrentTimeLocalDateTime();
            LocalDateTime lashClearTime = DateTimeUtil.toLocalDateTime(antiAddiction.getDailyDataClearTime());
            if (!DateTimeUtil.inSameType(DateTimeDurationType.day, now, lashClearTime)) {
                boolean isMonth = !DateTimeUtil.inSameType(DateTimeDurationType.month, now, lashClearTime);
                antiAddiction.setTodayOnlineTime(0);
                if (isMonth) {
                    antiAddiction.setMonthChargePrices(0);
                }
                antiAddiction.setDailyDataClearTime(ServerConstants.getCurrentTimeMillis());
            }
        }
        return antiAddiction;
    }

    /**
     * 获取今日在线时间
     *
     * @param accountId 账号ID
     */
    public static long getTodayOnlineTime(String accountId) {
        return getAntiAdditionByAccountId(accountId).getTodayOnlineTime();
    }

    /**
     * 获取本月充值
     *
     * @param accountId 账号ID
     */
    public static int getMonthChargePrices(String accountId) {
        return getAntiAdditionByAccountId(accountId).getMonthChargePrices();
    }

    /**
     * 检查玩家当前是否有充值限制
     */
    public static boolean checkPlayerIsLimitCharge(Player player, int goodsId) {
        int age = player.getAccount().getAge();
        if (!isAntiAddiction(player.getAccount().getAge())) {
            return false;
        }
        AntiAddictionTemplate antiAddictionTemplate = getAntiAddictionTemplate(age);
        int monthChargePrices = getMonthChargePrices(player.getAccountId());
        //查看当前是否已到限制充值阶段
        if (monthChargePrices > antiAddictionTemplate.monthlyRecharge) {
            return true;
        }
        //检查充值商品后是否超过限额
        PayItem payItem = PayService.getPayItem(player.getAccount().payChannel, goodsId);
        if (payItem != null) {
            // 检查单笔充值
            if (payItem.price > antiAddictionTemplate.singleRecharge) {
                return true;
            }
            return monthChargePrices + payItem.price > antiAddictionTemplate.monthlyRecharge;
        }
        return false;
    }

    /**
     * 退出增加在线时间
     *
     * @param accountId 账号ID
     * @param millTime  本次在线时间 单位毫秒
     */
    public static void loginOutAddOnlineTime(String accountId, long millTime) {
        String lockKey = BaseRedisKey.Commons.ANTI_ADDICTION_LOCK.getRedisKey(accountId);
        boolean lock = TLBase.getInstance().getLockUtil().lock(lockKey);
        if (lock) {
            try {
                AntiAddiction antiAddiction = getAntiAdditionByAccountId(accountId);
                //添加今日在线时长
                antiAddiction.addPlayerOnlineTime(millTime);
                String key = BaseRedisKey.Commons.ANTI_ADDICTION.getRedisKey(accountId);
                TLBase.getInstance().getRedisAssistant().bytesSet(key, PbUtilCompress.encode(antiAddiction));
            } finally {
                TLBase.getInstance().getLockUtil().unlock(lockKey);
            }
        }
    }

    /**
     * 推送防沉迷数据
     * @param player 玩家信息
     * @param todayOnlineTime 今日在线时间
     * @param monthChargePrices 月度充值
     */
    public static void syncAntiAddition(Player player, long todayOnlineTime, long monthChargePrices){
        PbProtocol.AntiAddictionInfoSync.Builder sync = PbProtocol.AntiAddictionInfoSync.newBuilder();
        sync.setMonthChargePrices(monthChargePrices);
        sync.setTodayOnlineTime(todayOnlineTime);
        player.send(PtCode.ANTI_ADDICTION_INFO_SYNC, sync.build());
    }


    /**
     * 增加每月充值
     *
     * @param player   玩家信息
     * @param chargePrice 充值价格
     */
    public static void addChargePrice(Player player, int chargePrice) {
        String accountId = player.getAccountId();
        String lockKey = BaseRedisKey.Commons.ANTI_ADDICTION_LOCK.getRedisKey(accountId);
        boolean lock = TLBase.getInstance().getLockUtil().lock(lockKey);
        if (lock) {
            try {
                AntiAddiction antiAddiction = getAntiAdditionByAccountId(accountId);
                //添加本月充值额度
                antiAddiction.addChargePrice(chargePrice);
                String key = BaseRedisKey.Commons.ANTI_ADDICTION.getRedisKey(accountId);
                TLBase.getInstance().getRedisAssistant().bytesSet(key, PbUtilCompress.encode(antiAddiction));

                syncAntiAddition(player, -1, antiAddiction.getMonthChargePrices());
            } finally {
                TLBase.getInstance().getLockUtil().unlock(lockKey);
            }
        }
    }
}

package com.gy.server.game.equipmentMission;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.combat.CombatManager;
import com.gy.server.game.common.ConstantConfigReader;
import com.gy.server.game.cond.CondManager;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.equipmentMission.stage.EquipmentMissionStage;
import com.gy.server.game.equipmentMission.template.EquipmentMissionConst;
import com.gy.server.game.equipmentMission.template.EquipmentMissionEquipInMissionTemplate;
import com.gy.server.game.equipmentMission.template.EquipmentMissionEquipMapTemplate;
import com.gy.server.game.function.Function;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.reddot.RedDot;
import com.gy.server.game.service.Service;
import com.gy.server.game.text.Text;
import com.gy.server.game.trusteeshipTask.TrusteeshipTaskGlobalData;
import com.gy.server.game.trusteeshipTask.bean.TrusteeshipTaskEnums;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbTrusteeship;

import java.util.*;

/**
 * 装备副本服务类
 *
 * <AUTHOR> 2024/1/29 13:54
 **/
public class EquipmentMissionService extends PlayerPacketHandler implements Service {

    private static EquipmentMissionConst emConst;

    /**
     * 装逼副本地图map
     * key：地图id value：
     */
    private static Map<Integer, EquipmentMissionEquipMapTemplate> mapMap = new HashMap<>();

    /**
     * 装备副本关卡map
     * key: 地图id value: key: 关卡id value:
     */
    private static Map<Integer, Map<Integer, EquipmentMissionEquipInMissionTemplate>> missionMap = new HashMap<>();

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {

        Map<String, String> constantMap = ConstantConfigReader.read(ConfigFile.equipmentmission_const);
        emConst = new EquipmentMissionConst(constantMap);

        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.equipmentMission_equipMap);
        Map<Integer, EquipmentMissionEquipMapTemplate> mapMapTemp = new HashMap<>();
        for(Map<String, String> map : mapList){
            EquipmentMissionEquipMapTemplate template = new EquipmentMissionEquipMapTemplate(map);
            mapMapTemp.put(template.id, template);
        }
        mapMap = mapMapTemp;

        mapList = ConfigReader.read(ConfigFile.equipmentMission_equipInMission);
        Map<Integer, Map<Integer, EquipmentMissionEquipInMissionTemplate>> missionMapTemp = new HashMap<>();
        for(Map<String, String> map : mapList){
            EquipmentMissionEquipInMissionTemplate template = new EquipmentMissionEquipInMissionTemplate(map);

            Map<Integer, EquipmentMissionEquipInMissionTemplate> subMap = missionMap.computeIfAbsent(template.mapId, map1 -> new HashMap<>());
            subMap.put(template.id, template);
            missionMapTemp.putIfAbsent(template.mapId, subMap);
        }
        missionMap = missionMapTemp;
    }

    @Override
    public void clearConfigData() {
        mapMap.clear();
        missionMap.clear();
    }

    /**
     * 装备副本信息
     * @param player
     * @param req
     */
    @Handler(PtCode.EQUIPMENT_MISSION_INFO_REQ)
    private void equipmentMissionInfo(Player player, PbProtocol.EquipmentMissionInfoReq req, long time){
        PbProtocol.EquipmentMissionInfoRst.Builder rst = PbProtocol.EquipmentMissionInfoRst.newBuilder().setResult(Text.genOkServerRstInfo());

        logic:{
            if(!Function.equipMission.isOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }

            PlayerEquipmentMissionModel model = player.getEquipmentMissionModel();
            rst.setEquipmentMission(model.genPb());

            PbTrusteeship.TrusteeshipEquipmentMission equipmentMission = model.genTrusteeshipEquipmentMission();
            List<Reward> rewardList = new ArrayList<>(model.getTrusteeshipRewardList());
            if(!rewardList.isEmpty() && equipmentMission.getTrusteeshipMapId() == 0){
                model.clearTrusteeshipReward();
                model.getPassMissionIdSet().clear();

                Reward.add(rewardList, player, BehaviorType.trusteeshipEquipmentMission);
            }
            rst.setTrusteeship(equipmentMission);
        }

        player.send(PtCode.EQUIPMENT_MISSION_INFO_RST, rst.build(), time);
    }

    /**
     * 装备副本挑战
     * @param player
     * @param req
     */
    @Handler(PtCode.EQUIPMENT_MISSION_CHALLENGE_REQ)
    private void equipmentMissionChallengeReq(Player player, PbProtocol.EquipmentMissionChallengeReq req, long time){
        PbProtocol.EquipmentMissionChallengeRst.Builder rst = PbProtocol.EquipmentMissionChallengeRst.newBuilder().setResult(Text.genOkServerRstInfo());
//        int mapId = req.getMapId();

        logic:{
            if(!Function.equipMission.isOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }

            PlayerEquipmentMissionModel model = player.getEquipmentMissionModel();
            int nextMissionId = 0;
            if(model.getMapLevelMap().isEmpty()){
                int mapId = mapMap.keySet().stream().min(Integer::compareTo).orElse(0);
                EquipmentMissionEquipMapTemplate mapTemplate = mapMap.get(mapId);
                if(mapTemplate == null){
                    rst.setResult(Text.genServerRstInfo(Text.对应的模板数据找不到));
                    break logic;
                }

                if(CondManager.checkNotCond(player, mapTemplate.unlock)){
                    rst.setResult(Text.genServerRstInfo(Text.章节未解锁));
                    break logic;
                }

                nextMissionId = mapTemplate.mission;
            }else {
                int mapId = model.getMapLevelMap().keySet().stream().max(Integer::compareTo).get();
                nextMissionId = model.getMapNextMissionId(mapId);
                if(nextMissionId == -1){
                    int mapIdTemp = mapMap.keySet().stream().filter(tempId -> tempId > mapId).min(Integer::compareTo).orElse(0);
                    if(mapIdTemp == 0){
                        rst.setResult(Text.genServerRstInfo(Text.所有章节都已通关));
                        break logic;
                    }

                    EquipmentMissionEquipMapTemplate mapTemplate = mapMap.get(mapIdTemp);
                    if(CondManager.checkNotCond(player, mapTemplate.unlock)){
                        rst.setResult(Text.genServerRstInfo(Text.章节未解锁));
                        break logic;
                    }

                    EquipmentMissionEquipMapTemplate mapTemplateTemp = mapMap.get(mapIdTemp);
                    nextMissionId = mapTemplateTemp.mission;
                }
            }

            EquipmentMissionEquipInMissionTemplate missionTemplate = getInMissionTemplate(nextMissionId);
            if(missionTemplate == null){
                rst.setResult(Text.genServerRstInfo(Text.对应的模板数据找不到));
                break logic;
            }

            TrusteeshipTaskGlobalData globalData = GlobalDataManager.getData(GlobalDataType.trusteeshipTask);
            if(!globalData.canAddTask(player.getPlayerId(), TrusteeshipTaskEnums.EquipmentMission.getType())){
                rst.setResult(Text.genServerRstInfo(Text.托管中));
                break logic;
            }

            EquipmentMissionStage stage = new EquipmentMissionStage(player, missionTemplate);
            stage.init();

            rst.setMapId(missionTemplate.mapId);
            rst.setChallengeMissionId(missionTemplate.id);
            rst.setStage(stage.genAbstractPb());

            CombatManager.combatPrepare(stage);

            model.setMapId(missionTemplate.mapId);
        }

        player.send(PtCode.EQUIPMENT_MISSION_CHALLENGE_RST, rst.build(), time);
    }

    /**
     * 装备副本挂机奖励领取
     * @param player
     * @param req
     */
    @Handler(PtCode.EQUIPMENT_MISSION_ON_HOOK_REWARD_RECEIVE_REQ)
    private void equipmentMissionOnHookRewardReceiveReq(Player player, PbProtocol.EquipmentMissionOnHookRewardReceiveReq req, long time){
        PbProtocol.EquipmentMissionOnHookRewardReceiveRst.Builder rst = PbProtocol.EquipmentMissionOnHookRewardReceiveRst.newBuilder().setResult(Text.genOkServerRstInfo());

        logic:{
            if(!Function.equipMission.isOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }

            PlayerEquipmentMissionModel model = player.getEquipmentMissionModel();
            List<Reward> rewards = new ArrayList<>(model.getCountRewards());
            if(rewards.isEmpty()){
                rst.setResult(Text.genServerRstInfo(Text.挂机奖励没有));
                break logic;
            }

            long now = ServerConstants.getCurrentTimeMillis();
            model.setReceiveTime(now);
            model.setCountTime(now);

            List<Reward> rRewards = model.getReallyRewards(true);
            Reward.add(rRewards, player, BehaviorType.equipmentMissionHandUp);
            rst.addAllRewards(Reward.writeCollectionToPb(rRewards));

            player.dataSyncModule.syncEquipmentMission();
            RedDot.equipInMissionHangUpTwoHours.sync(player);
            RedDot.equipInMissionHangUpFull.sync(player);
        }

        player.send(PtCode.EQUIPMENT_MISSION_ON_HOOK_REWARD_RECEIVE_RST, rst.build(), time);
    }

    /**
     * 装备副本挂机奖励快速领取
     * @param player
     * @param req
     */
    @Handler(PtCode.EQUIPMENT_MISSION_ON_HOOK_REWARD_QUICK_REQ)
    private void equipmentMissionOnHookRewardQuickReq(Player player, PbProtocol.EquipmentMissionOnHookRewardQuickReq req, long time){
        PbProtocol.EquipmentMissionOnHookRewardQuickRst.Builder rst = PbProtocol.EquipmentMissionOnHookRewardQuickRst.newBuilder().setResult(Text.genOkServerRstInfo());

        logic: {
            if(!Function.equipMission.isOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }

            PlayerEquipmentMissionModel model = player.getEquipmentMissionModel();
            int quickNum = model.getQuickNum();

            int freeTimes = emConst.fastHangUpFreeTimes;
            int payTimes = emConst.fastHangUpPayTimes;
            if(quickNum >= freeTimes + payTimes){
                rst.setResult(Text.genServerRstInfo(Text.当前已达最大次数));
                break logic;
            }

            int newQuickNum = quickNum + 1;
            //是否是付费次数
            if(newQuickNum > freeTimes){
                //计算花费
                int num = countCostByTimes(newQuickNum - freeTimes);
                List<Integer> costCurrencyList = emConst.fastHangUpCurrencyItemList;

                Reward cost = new Reward(costCurrencyList.get(0), -1 , num);
                if(cost.check(player) != -1){
                    cost = new Reward(costCurrencyList.get(1), -1 , num);
                    if(cost.check(player) != -1){
                        rst.setResult(Text.genServerRstInfo(Text.消耗不足));
                        break logic;
                    }
                }

                cost.remove(player, BehaviorType.equipmentMissionQuickHandUpCost);
            }

            model.setQuickNum(newQuickNum);

            List<Reward> rewards = model.countQuickOnHookReward();
            Reward.add(rewards, player, BehaviorType.equipmentMissionQuickHandUp);

            rst.setQuickNum(model.getQuickNum());
            rst.addAllRewards(Reward.writeCollectionToPb(rewards));

            RedDot.equipInMissionFastHangUpFree.sync(player);
            player.postEvent(PlayerEventType.equipmentMissionFastHangUp);
        }

        player.send(PtCode.EQUIPMENT_MISSION_ON_HOOK_REWARD_QUICK_RST, rst.build(), time);
    }

    /**
     * 快速挂机 根据次数计算花费数量
     * @param times
     * @return
     */
    private int countCostByTimes(int times){
        List<Integer> costList = emConst.fastHangUpCostList;
        if(times > costList.size()){
            times = costList.size();
        }
        return costList.get(times - 1);
    }

    public static EquipmentMissionConst getEmConst() {
        return emConst;
    }

    public static Map<Integer, EquipmentMissionEquipMapTemplate> getMapMap() {
        return mapMap;
    }

    public static Map<Integer, Map<Integer, EquipmentMissionEquipInMissionTemplate>> getMissionMap() {
        return missionMap;
    }

    public static EquipmentMissionEquipInMissionTemplate getInMissionTemplate(int missionId) {
        for (Map<Integer, EquipmentMissionEquipInMissionTemplate> map : missionMap.values()) {
            if(map.containsKey(missionId)){
                return map.get(missionId);
            }
        }
        return null;
    }
}

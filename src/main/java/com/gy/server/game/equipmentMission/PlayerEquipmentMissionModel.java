package com.gy.server.game.equipmentMission;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.equipmentMission.template.EquipmentMissionConst;
import com.gy.server.game.equipmentMission.template.EquipmentMissionEquipInMissionTemplate;
import com.gy.server.game.equipmentMission.template.EquipmentMissionEquipMapTemplate;
import com.gy.server.game.function.Function;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModel;
import com.gy.server.game.player.event.PlayerEvent;
import com.gy.server.game.player.event.PlayerEventHandler;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.reddot.RedDot;
import com.gy.server.game.trusteeshipTask.TrusteeshipTaskGlobalData;
import com.gy.server.game.trusteeshipTask.bean.TrusteeshipTaskEnums;
import com.gy.server.game.trusteeshipTask.task.AbsTrusteeshipTask;
import com.gy.server.game.trusteeshipTask.task.impl.EquipmentMissionTask;
import com.gy.server.packet.PbCommons;
import com.gy.server.packet.PbEquipmentMission;
import com.gy.server.packet.PbSync;
import com.gy.server.packet.PbTrusteeship;
import com.gy.server.utils.MathUtil;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.serialize.equipmentMission.PlayerEquipmentMissionModelDb;
import com.ttlike.server.tl.baselib.serialize.player.PlayerBlobDb;

import java.util.*;

import static com.gy.server.game.player.event.PlayerEventType.*;

/**
 * 玩家装备副本模块  山河探险
 *
 * <AUTHOR> 2024/1/29 9:43
 **/
public class PlayerEquipmentMissionModel extends PlayerModel implements PlayerEventHandler {

    /**
     * 地图关卡
     * key:地图id value:已通过最大关卡id
     */
    private Map<Integer, Integer> mapLevelMap = new HashMap<>();

    /**
     * 挂机奖励，快速使用次数
     */
    private int quickNum;

    /**
     * 上次领取挂机奖励时间
     */
    private long receiveTime;

    /**
     * 上次计算挂机奖励时间
     * 领奖时为receiveTime
     */
    private long countTime;

    /**
     * 已计算的挂机奖励
     * 奖励数量*10000 正常使用需要除去10000
     */
    private List<Reward> countRewards = new ArrayList<>();

    /**
     * 展示产量，只在内存中
     */
    private List<PbCommons.PbReward> showRewards = new ArrayList<>();

    /******************************托管******************************/

    /**
     * 最近 手动/托管 战斗章节id
     */
    private int mapId;

    /**
     * 托管奖励
     */
    private List<Reward> trusteeshipRewardList = new ArrayList<>();

    /**
     * 通关关卡列表
     */
    private Set<Integer> passMissionIdSet = new HashSet<>();

    public PlayerEquipmentMissionModel(Player player) {
        super(player);
    }

    @Override
    protected void loadData(PlayerBlobDb playerBlob) {
        PlayerEquipmentMissionModelDb db = playerBlob.getEquipmentMissionModelDb();
        if(db == null){
            return;
        }
        this.mapLevelMap = db.getMapLevelMap();
        this.quickNum = db.getQuickNum();
        this.receiveTime = db.getReceiveTime();
        this.countTime = db.getCountTime();
        this.countRewards.addAll(Reward.readFromDb(db.getRewardDbs()));
        this.mapId = db.getMapId();
        this.trusteeshipRewardList.addAll(Reward.readFromDb(db.getTrusteeshipRewardList()));
        this.passMissionIdSet.addAll(db.getPassMissionIdList());
    }

    @Override
    protected void saveData(PlayerBlobDb playerBlob) {
        PlayerEquipmentMissionModelDb db = new PlayerEquipmentMissionModelDb();
        db.setMapLevelMap(mapLevelMap);
        db.setQuickNum(quickNum);
        db.setReceiveTime(receiveTime);
        db.setCountTime(countTime);

        countRewards.forEach(bean -> db.getRewardDbs().add(bean.genDb()));
        db.setMapId(mapId);
        trusteeshipRewardList.forEach(bean -> db.getTrusteeshipRewardList().add(bean.genDb()));
        db.getPassMissionIdList().addAll(passMissionIdSet);
        playerBlob.setEquipmentMissionModelDb(db);
    }

    @Override
    public PlayerEventType[] getEventTypes() {
        return new PlayerEventType[]{PlayerEventType.login, PlayerEventType.functionCheck, day5Refresh};
    }

    @Override
    public void handle(PlayerEvent event) {
        if(event.getEventType() != functionCheck && receiveTime == 0){
            return;
        }

        switch (event.getEventType()){
            case functionCheck:{
                if(receiveTime != 0){
                    return;
                }

                if(Function.equipMission.isOpen(event.getSource())){
                    open();
                }
                break;
            }
            case login:{
                checkAndAddReward();
                //增加检测周期任务
                addPeriodTask(DateTimeUtil.MillisOfSecond, this::checkAndAddReward);
                countShowOneHourReward();
                break;
            }
            case day5Refresh:{
                refresh();
                break;
            }
        }
    }

    private void checkAndAddReward() {
        EquipmentMissionConst emConst = EquipmentMissionService.getEmConst();
        long maxMillis = emConst.hangUpUpperLimit * DateTimeUtil.MillisOfMinute;
        if(countTime - receiveTime >= maxMillis){   //已经计算到最大时长
            return;
        }

        long now = ServerConstants.getCurrentTimeMillis();
        if(now - (countTime + DateTimeUtil.MillisOfMinute * EquipmentMissionService.getEmConst().hangUpSettle) > 0){
            countAndGetReward(now, maxMillis);
            RedDot.equipInMissionHangUpTwoHours.sync(getPlayer());
            RedDot.equipInMissionHangUpFull.sync(getPlayer());
        }
    }

    /**
     * 计算并获取奖励 奖励*10000
     *
     * @param nowMillTime 当前时间
     */
    private void countAndGetReward(long nowMillTime, long maxMillis) {
        long betweenTime = nowMillTime - countTime;
        //是否达到最大时长
        if(nowMillTime - receiveTime > maxMillis){
            betweenTime = maxMillis - Math.max(countTime - receiveTime, 0);
        }

        long remain = Math.max(betweenTime % (EquipmentMissionService.getEmConst().hangUpSettle * DateTimeUtil.MillisOfMinute), 0);
        countTime = nowMillTime - remain;

        List<Reward> rewards = countRewardTemplatesByTime(betweenTime);
        addCountRewards(nowMillTime, rewards);
    }

    private List<Reward> countRewardTemplatesByTime(long betweenTime){
        return countRewardTemplatesByTime(betweenTime, false);
    }

    /**
     * 根据时间计算挂机奖励
     * @param betweenTime 毫秒
     * @return
     */
    private List<Reward> countRewardTemplatesByTime(long betweenTime, boolean isShow){

        EquipmentMissionEquipMapTemplate mapTemplate = getNowMaxMapTemplate();
        Map<Integer, EquipmentMissionEquipInMissionTemplate> missionTemplateMap = EquipmentMissionService.getMissionMap().get(mapTemplate.id);

        int levelId = mapLevelMap.getOrDefault(mapTemplate.id, 0);
        //加成数量map
        Map<Integer, Integer> bonusMap = new HashMap<>();
        for (EquipmentMissionEquipInMissionTemplate bean : missionTemplateMap.values()) {
            if(bean.id <= levelId){
                bonusMap.put(bean.bonusType, bonusMap.getOrDefault(bean.bonusType, 0) + bean.bonusNum);
            }
        }

        //通关加成
        int addition = countOnHookAddition();

        EquipmentMissionEquipInMissionTemplate missionTemplate = missionTemplateMap.get(levelId);
        Map<List<RewardTemplate>, Integer> weightMap = getWeightMap(mapTemplate, missionTemplate == null ? null : missionTemplate.weightList);

        int materialYield = mapTemplate.yield + bonusMap.getOrDefault(1, 0);
        List<Reward> strengthenYield = RewardTemplate.createRewards(mapTemplate.strengthenYield);
        List<Reward> coinYield = RewardTemplate.createRewards(mapTemplate.coinYield);

        int dropNum = countDropNum(betweenTime);
        List<Reward> rewardList = new ArrayList<>();

        //材料奖励
        for (int i = 0; i < dropNum * materialYield; i++){
            if(isShow){
                RewardTemplate rewardTemplate = mapTemplate.rewardTemplates1.get(0);
                rewardList.add(rewardTemplate.createReward());
            }else {
                //材料随机产出
                List<RewardTemplate> rewardTemplateList = MathUtil.weightRandom(weightMap);
                List<Reward> randomRewardList = RewardTemplate.createRewards(rewardTemplateList);
                if(!randomRewardList.isEmpty()){
                    Collections.shuffle(randomRewardList);
                    rewardList.add(MathUtil.random(randomRewardList));
                }
            }
        }

        //强化石奖励
        for (Reward bean : strengthenYield) {
            bean.setValue((bean.getValue() + bonusMap.getOrDefault(2, 0)) * dropNum);
            rewardList.add(bean);
        }

        //货币产量
        for (Reward bean : coinYield) {
            bean.setValue((bean.getValue() + bonusMap.getOrDefault(3, 0)) * dropNum);
            rewardList.add(bean);
        }

        Reward.merge(rewardList);
        rewardList.sort((Comparator.comparingInt(Reward::getItemTemplateId)));
        rewardList.forEach(bean -> {
            long newValue = bean.getValue() * (10000 + addition);
            bean.setValue(newValue);
        });

        return rewardList;
    }

    /**
     * 计算掉落次数
     * @param time
     * @return
     */
    private int countDropNum(long time){
        int baseTime = EquipmentMissionService.getEmConst().hangUpSettle;

        //掉落次数
        return Math.max((int) (time / (baseTime * DateTimeUtil.MillisOfMinute)), 1);
    }

    /**
     * 获取当前最高章节，基础产量计算当前最高章节
     * @return
     */
    private EquipmentMissionEquipMapTemplate getNowMaxMapTemplate(){
        Map<Integer, EquipmentMissionEquipMapTemplate> mapMap = EquipmentMissionService.getMapMap();
        int mapId = mapLevelMap.isEmpty() ? mapMap.keySet().stream().min(Integer::compareTo).get() : mapLevelMap.keySet().stream().max(Integer::compareTo).get();
        return mapMap.get(mapId);
    }

    /**
     * 获取地图现在所处关卡的模板
     * @param mapId
     * @return
     */
    private EquipmentMissionEquipInMissionTemplate getNowMissionTemplate(int mapId){
        Map<Integer, Map<Integer, EquipmentMissionEquipInMissionTemplate>> missionMap = EquipmentMissionService.getMissionMap();

        if(!mapLevelMap.isEmpty()){
            int missionId = mapLevelMap.get(mapId);
            return missionMap.get(mapId).get(missionId);
        }
        return null;
    }

    /**
     * 计算挂机加成 万分比
     * @return
     */
    private int countOnHookAddition(){
        int addition = 0;
        for (Map.Entry<Integer, Map<Integer, EquipmentMissionEquipInMissionTemplate>> entry : EquipmentMissionService.getMissionMap().entrySet()) {
            int maxLevelId = entry.getValue().keySet().stream().max(Integer::compare).orElse(0);
            if(mapLevelMap.getOrDefault(entry.getKey(), 0) >= maxLevelId){
                addition = addition + EquipmentMissionService.getEmConst().clearBonus;
            }
        }
        return addition;
    }

    private void addCountRewards(long nowMillTime, List<Reward> addRewards){
        this.countTime = nowMillTime;
        countRewards.addAll(addRewards);
        Reward.merge(countRewards);
    }

    /**
     * 计算展示挂机奖励
     */
    private void countShowOneHourReward() {
        List<Reward> rewardList = countRewardTemplatesByTime(EquipmentMissionService.getEmConst().hangUpSettle * DateTimeUtil.MillisOfMinute, true);
        rewardList.forEach(reward -> {
            long newValue = reward.getValue() / 10000;
            reward.setValue(newValue);
        });

        showRewards = Reward.writeCollectionToPb(rewardList);
    }

    /**
     * 计算快速挂机奖励 {@link #countRewards}
     * @return
     */
    public List<Reward> countQuickOnHookReward(){
        List<Reward> rewards = countRewardTemplatesByTime(EquipmentMissionService.getEmConst().fastHangUpRewardTime * DateTimeUtil.MillisOfMinute);
        List<Reward> reallyRewards = new ArrayList<>();
        rewards.forEach(bean -> {
            long removeValue = bean.getValue();
            long newValue = removeValue / 10000;
            if(newValue > 0){
                Reward newReward = new Reward(bean.getType(), bean.getItemTemplateId(), newValue);
                reallyRewards.add(newReward);
            }
        });
        return reallyRewards;
    }

    /**
     * 获取权重map
     * @param mapTemplate
     * @param weightList 如果个数不对，配表有问题
     * @return
     */
    private Map<List<RewardTemplate>, Integer> getWeightMap(EquipmentMissionEquipMapTemplate mapTemplate, List<Integer> weightList) {
        if(weightList == null || (weightList != null && weightList.isEmpty())){
            weightList = mapTemplate.weightList;
        }
        Map<List<RewardTemplate>, Integer> rmap = new HashMap<>();
        rmap.put(mapTemplate.rewardTemplates1, weightList.get(0));
        rmap.put(mapTemplate.rewardTemplates2, weightList.get(1));
        rmap.put(mapTemplate.rewardTemplates3, weightList.get(2));
        rmap.put(mapTemplate.rewardTemplates4, weightList.get(3));
        rmap.put(mapTemplate.rewardTemplates5, weightList.get(4));
        return rmap;
    }

    /**
     * 转天刷新
     */
    private void refresh() {
        quickNum = 0;

        RedDot.equipInMissionFastHangUpFree.sync(getPlayer());
        getPlayer().dataSyncModule.syncEquipmentMission();
    }

    /**
     * 开启
     */
    private void open() {
        long now = ServerConstants.getCurrentTimeMillis();
        this.receiveTime = now;
        this.countTime = now;
        this.quickNum = 0;

        //增加检测周期任务
        addPeriodTask(DateTimeUtil.MillisOfSecond, this::checkAndAddReward);
        countShowOneHourReward();
        getPlayer().dataSyncModule.syncEquipmentMission();
    }

    public void dealChallenge(EquipmentMissionEquipInMissionTemplate missionTemplate, List<Reward> rewards) {
        if(missionTemplate.id > mapLevelMap.getOrDefault(missionTemplate.mapId, 0)){
            mapLevelMap.put(missionTemplate.mapId, missionTemplate.id);

            //发奖
            Reward.add(rewards, getPlayer(), BehaviorType.equipmentMissionChallenge);

            //重新计算一小时产量
            countShowOneHourReward();
        }
    }

    public PbEquipmentMission.EquipmentMissionModel genPb(){
        PbEquipmentMission.EquipmentMissionModel.Builder builder = PbEquipmentMission.EquipmentMissionModel.newBuilder();
        mapLevelMap.forEach((key, value) -> {
            if(value != 0){
                PbEquipmentMission.MapInfo.Builder mapInfoBuilder = PbEquipmentMission.MapInfo.newBuilder();
                mapInfoBuilder.setMapId(key);
                mapInfoBuilder.setMissionId(value);
                mapInfoBuilder.setNextMissionId(EquipmentMissionService.getMissionMap().get(key).get(value).nextMission);
                builder.addMapInfo(mapInfoBuilder.build());
            }
        });

        PbEquipmentMission.OnHookInfo.Builder onHokInfoBuilder = PbEquipmentMission.OnHookInfo.newBuilder();
        onHokInfoBuilder.setReceiveTime(receiveTime);
        onHokInfoBuilder.setQuickNum(quickNum);
        onHokInfoBuilder.addAllReward(Reward.writeCollectionToPb(getReallyRewards(false)));
        onHokInfoBuilder.addAllShow(showRewards);
        builder.setOnHookInfo(onHokInfoBuilder.build());
        return builder.build();
    }

    public PbSync.EquipmentMissionData genEquipmentMissionData(){
        PbSync.EquipmentMissionData.Builder builder = PbSync.EquipmentMissionData.newBuilder();
        builder.setEquipmentMission(genPb());
        return builder.build();
    }

    public PbTrusteeship.TrusteeshipEquipmentMission genTrusteeshipEquipmentMission(){
        PbTrusteeship.TrusteeshipEquipmentMission.Builder builder = PbTrusteeship.TrusteeshipEquipmentMission.newBuilder();
        builder.addAllTrusteeshipRewards(Reward.writeCollectionToPb(trusteeshipRewardList));
        builder.addAllPassMissionId(passMissionIdSet);
        TrusteeshipTaskGlobalData globalData = GlobalDataManager.getData(GlobalDataType.trusteeshipTask);
        List<AbsTrusteeshipTask> list = globalData.getTaskByPlayerId(getPlayerId());
        for (AbsTrusteeshipTask bean : list) {
            if(bean.getType() == TrusteeshipTaskEnums.EquipmentMission.getType()){
                EquipmentMissionTask task = (EquipmentMissionTask) bean;
                int missionId = task.getNowMissionId();
                if(missionId != 0){
                    EquipmentMissionEquipInMissionTemplate missionTemplate = EquipmentMissionService.getInMissionTemplate(missionId);
                    if(missionTemplate != null){
                        builder.setTrusteeshipMapId(missionTemplate.mapId);
                    }
                }
            }
        }
        return builder.build();
    }

    /**
     * 获取真实的挂机奖励
     * 如果领取奖励，会去掉countRewards的要领取部分，保留小数部分
     * @param isDraw 是否领取
     * @return
     */
    public List<Reward> getReallyRewards(boolean isDraw) {
        List<Reward> removeRewards = new ArrayList<>();
        List<Reward> reallyRewards = new ArrayList<>();
        this.countRewards.forEach(bean -> {
            long removeValue = bean.getValue();
            long newValue = removeValue / 10000;
            if(newValue > 0){
                Reward newReward = new Reward(bean.getType(), bean.getItemTemplateId(), newValue);
                reallyRewards.add(newReward);
            }
            Reward removeReward = new Reward(bean.getType(), bean.getItemTemplateId(), removeValue);
            removeRewards.add(removeReward);
        });

        if(isDraw){
            Reward.removeRewardByOtherReward(this.countRewards, removeRewards);
        }
        return reallyRewards;
    }

    /**
     * gm通关到指定装备副本关卡
     * 指定章节下全部通关
     * @param instanceId
     * @param missionId
     */
    public void gmPass(int instanceId, int missionId){
        boolean change = false;
        Map<Integer, EquipmentMissionEquipMapTemplate> map = EquipmentMissionService.getMapMap();
        Map<Integer, Map<Integer, EquipmentMissionEquipInMissionTemplate>> mission = EquipmentMissionService.getMissionMap();
        for (Map.Entry<Integer, EquipmentMissionEquipMapTemplate> entry : map.entrySet()) {
            Map<Integer, EquipmentMissionEquipInMissionTemplate> temp = mission.get(entry.getKey());

            if(entry.getKey() < instanceId){
                mapLevelMap.put(entry.getKey(), temp.keySet().stream().max(Integer::compareTo).get());
                change = true;
            }

            if(entry.getKey() == instanceId){
                if(temp.containsKey(missionId)){
                    mapLevelMap.put(instanceId, missionId);
                    change = true;
                }
            }
        }

        if(change){
            //重新计算一小时产量
            countShowOneHourReward();
            getPlayer().dataSyncModule.syncEquipmentMission();
        }
    }

    /**
     * gm重置挂机次数
     */
    public void gmResetTimes() {
        this.quickNum = 0;
    }

    /**
     * 获取托管下一关卡,只在本章节寻找
     * @return
     */
    public int getTrusteeshipMissionId(boolean isWin) {
        if(isWin){
            //本地图
            return getMapNextMissionId(mapId);
        }
        return -1;
    }

    /**
     * 获取本地图下一关卡
     * @param mapId
     * @return
     */
    public int getMapNextMissionId(int mapId){
        Map<Integer, EquipmentMissionEquipInMissionTemplate> map = EquipmentMissionService.getMissionMap().get(mapId);
        int missionId = mapLevelMap.getOrDefault(mapId, 0);
        if(missionId == 0){
            missionId = map.keySet().stream().min(Integer::compare).orElse(0);
        }

        EquipmentMissionEquipInMissionTemplate missionTemplate = map.get(missionId);
        if(missionTemplate != null){
            return missionTemplate.nextMission;
        }

        return -1;
    }

    /**
     * 托管战斗完成处理
     *
     * @param nowMissionId
     * @param isWin
     */
    public void dealTrusteeshipCombatResult(int nowMissionId, boolean isWin) {
        EquipmentMissionEquipInMissionTemplate missionTemplate = EquipmentMissionService.getInMissionTemplate(nowMissionId);
        if(missionTemplate != null){
            if(isWin){
                if(nowMissionId > mapLevelMap.getOrDefault(missionTemplate.mapId, 0)){
                    mapLevelMap.put(missionTemplate.mapId, nowMissionId);
                }
                passMissionIdSet.add(nowMissionId);

                //同步功能数据
                getPlayer().dataSyncModule.syncEquipmentMission();
                getPlayer().postEvent(PlayerEventType.equipmentMissionFinish, missionTemplate.id);
            }
        }
    }

    public void addTrusteeshipReward(List<RewardTemplate> rewardTemplateList) {
        trusteeshipRewardList.addAll(Reward.templateCollectionToReward(rewardTemplateList));
    }

    public void clearTrusteeshipReward(){
        trusteeshipRewardList.clear();
    }

    public Map<Integer, Integer> getMapLevelMap() {
        return mapLevelMap;
    }

    public void setMapLevelMap(Map<Integer, Integer> mapLevelMap) {
        this.mapLevelMap = mapLevelMap;
    }

    public int getQuickNum() {
        return quickNum;
    }

    public void setQuickNum(int quickNum) {
        this.quickNum = quickNum;
    }

    public long getReceiveTime() {
        return receiveTime;
    }

    public void setReceiveTime(long receiveTime) {
        this.receiveTime = receiveTime;
    }

    public long getCountTime() {
        return countTime;
    }

    public void setCountTime(long countTime) {
        this.countTime = countTime;
    }

    public List<Reward> getCountRewards() {
        return countRewards;
    }

    public void setCountRewards(List<Reward> countRewards) {
        this.countRewards = countRewards;
    }

    public List<PbCommons.PbReward> getPbRewards() {
        return showRewards;
    }

    public void setPbRewards(List<PbCommons.PbReward> showRewards) {
        this.showRewards = showRewards;
    }

    public int getMapId() {
        return mapId;
    }

    public void setMapId(int mapId) {
        this.mapId = mapId;
    }

    public List<Reward> getTrusteeshipRewardList() {
        return trusteeshipRewardList;
    }

    public void setTrusteeshipRewardList(List<Reward> trusteeshipRewardList) {
        this.trusteeshipRewardList = trusteeshipRewardList;
    }

    public List<PbCommons.PbReward> getShowRewards() {
        return showRewards;
    }

    public void setShowRewards(List<PbCommons.PbReward> showRewards) {
        this.showRewards = showRewards;
    }

    public Set<Integer> getPassMissionIdSet() {
        return passMissionIdSet;
    }

    public void setPassMissionIdSet(Set<Integer> passMissionIdSet) {
        this.passMissionIdSet = passMissionIdSet;
    }
}

package com.gy.server.game.equipmentMission.template;

import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.util.StringExtUtil;

import java.util.List;
import java.util.Map;

/**
 * 装备副本地图模板
 *
 * <AUTHOR> 2024/1/29 15:07
 **/
public class EquipmentMissionEquipMapTemplate {

    /**
     * 章节id
     */
    public int id;

    /**
     * 开启条件
     */
    public String unlock;

    /**
     * 关卡起始ID
     */
    public int mission;

    /**
     * 基础产量
     */
    public int yield;

    /**
     * 强化石基础产量
     */
    public List<RewardTemplate> strengthenYield;

    /**
     * 交子基础产量
     */
    public List<RewardTemplate> coinYield;

    /**
     * 关卡数量
     */
    public int instanceNum;

    /**
     * 基础权重
     */
    public List<Integer> weightList;

    /**
     * 白色材料
     */
    public List<RewardTemplate> rewardTemplates1;

    /**
     * 绿色材料
     */
    public List<RewardTemplate> rewardTemplates2;

    /**
     * 蓝色材料
     */
    public List<RewardTemplate> rewardTemplates3;

    /**
     * 紫色材料
     */
    public List<RewardTemplate> rewardTemplates4;

    /**
     * 橙色材料
     */
    public List<RewardTemplate> rewardTemplates5;

    public EquipmentMissionEquipMapTemplate(Map<String, String> map) {
        this.id = Integer.parseInt(map.get("id"));
        this.unlock = map.get("openLevel");
        this.mission = Integer.parseInt(map.get("mission"));

        this.yield = Integer.parseInt(map.get("yield"));
        this.strengthenYield = RewardTemplate.readListFromText(map.get("StrengthenYield"));
        this.coinYield = RewardTemplate.readListFromText(map.get("coinYield"));

        this.instanceNum = Integer.parseInt(map.get("instanceNum"));
        this.weightList = StringExtUtil.string2List(map.get("weight"), ",", Integer.class);;
        this.rewardTemplates1 = RewardTemplate.readListFromText(map.get("normalitem1"));
        this.rewardTemplates2 = RewardTemplate.readListFromText(map.get("normalitemid2"));
        this.rewardTemplates3 = RewardTemplate.readListFromText(map.get("normalitemid3"));
        this.rewardTemplates4 = RewardTemplate.readListFromText(map.get("normalitemid4"));
        this.rewardTemplates5 = RewardTemplate.readListFromText(map.get("normalitemid5"));
    }

}

package com.gy.server.game.equipmentMission.stage;

import com.gy.server.game.battleCollect.BattleCollectService;
import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.combat.StageType;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.combat.unit.TeamUnit;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.equipmentMission.template.EquipmentMissionEquipInMissionTemplate;
import com.gy.server.game.liberty.LibertyHelper;
import com.gy.server.game.liberty.effect.LibertyType;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.packet.PbProtocol;

import java.util.ArrayList;
import java.util.List;

/**
 * 装备副本战斗类
 *
 * <AUTHOR> 2024/1/30 10:54
 **/
public class EquipmentMissionStage extends AbstractStage {
    private final Player atkPlayer;
    private final EquipmentMissionEquipInMissionTemplate missionTemplate;
    private LineupType lineupType = LineupType.equipMission;

    private StageType stageType = StageType.equipMission;

    public EquipmentMissionStage(Player atkPlayer, EquipmentMissionEquipInMissionTemplate missionTemplate) {
        this.atkPlayer = atkPlayer;
        this.missionTemplate = missionTemplate;
    }

    @Override
    public void init() {
        //进攻方阵容初始化
        List<HeroUnit> atkUnitList = atkPlayer.getLineupModel().createHeroUnits(stageType, lineupType);
        TeamUnit atkTeamUnit = new TeamUnit(getNewId(), atkUnitList);

        //防守方阵容初始化
        List<HeroUnit> defUnits = BattleCollectService.createHeroUnits(missionTemplate.battleCollectId);
        TeamUnit defenderTeam = new TeamUnit(getNewId(), defUnits);

        init(missionTemplate.battleCollectId, stageType, atkTeamUnit, defenderTeam, lineupType);
    }

    @Override
    public void afterFinish() {
        //
        PbProtocol.CombatSettlementNotify.Builder notify = genCombatSettlement();
        if(isWin){
            List<Reward> rewardList = new ArrayList<>();
            rewardList.addAll(Reward.templateCollectionToReward(missionTemplate.allReward));

            Player player = PlayerManager.getPlayer(atkPlayer.getPlayerId());
            LibertyHelper.checkMaterialRewardLiberty(LibertyType.equipMissionRate, player, rewardList);

            notify.addAllRewards(Reward.writeCollectionToPb(rewardList));

            player.getEquipmentMissionModel().dealChallenge(missionTemplate, rewardList);
            player.postEvent(PlayerEventType.equipmentMissionFinish, missionTemplate.id);

            atkPlayer.getEquipmentMissionModel().countQuickOnHookReward();
            //功能同步
            player.dataSyncModule.syncEquipmentMission();
        }
        notifyCombatSettlement(atkPlayer, notify.build());

        GameLogger.equipmentMission(atkPlayer, atkPlayer.getFightingPower(), missionTemplate.id, lineupType);
    }

    public EquipmentMissionEquipInMissionTemplate getMissionTemplate() {
        return missionTemplate;
    }
}

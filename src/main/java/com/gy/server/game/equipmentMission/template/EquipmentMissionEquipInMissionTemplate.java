package com.gy.server.game.equipmentMission.template;

import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.util.StringExtUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 装备副本关卡模板
 *
 * <AUTHOR> 2024/1/29 15:23
 **/
public class EquipmentMissionEquipInMissionTemplate {

    /**
     * 关卡id
     */
    public int id;

    /**
     * 所属地图
     */
    public int mapId;

    /**
     * 后置关卡id
     */
    public int nextMission;

    /**
     * 通关奖励
     */
    private List<RewardTemplate> passReward;

    /**
     * 稀有奖励
     */
    private List<RewardTemplate> rareReward;

    /**
     * 加成类型
     * 1、打造材料
     * 2、强化精华
     * 3、交子
     */
    public int bonusType;

    /**
     * 加成数量
     */
    public int bonusNum;

    /**
     * 掉落品质权重
     */
    public List<Integer> weightList;

    /**
     * 怪物id
     */
    public int battleCollectId;

    public List<RewardTemplate> allReward = new ArrayList<>();

    public EquipmentMissionEquipInMissionTemplate(Map<String, String> map) {
        this.id = Integer.parseInt(map.get("id"));
        this.mapId = Integer.parseInt(map.get("mapId"));
        this.nextMission = Integer.parseInt(map.get("nextMission"));
        this.passReward = RewardTemplate.readListFromText(map.get("passReward"));
        this.rareReward = RewardTemplate.readListFromText(map.get("rareReward"));
        this.bonusType = Integer.parseInt(map.get("bonusType"));
        this.bonusNum = Integer.parseInt(map.get("bonusNum"));
        this.weightList = StringExtUtil.string2List(map.get("weight"), ",", Integer.class);
        this.battleCollectId = Integer.parseInt(map.get("battleCollect"));

        allReward.addAll(passReward);
        allReward.addAll(rareReward);
    }
}

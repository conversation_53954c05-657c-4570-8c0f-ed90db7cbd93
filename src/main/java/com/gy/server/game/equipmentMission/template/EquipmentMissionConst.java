package com.gy.server.game.equipmentMission.template;

import com.gy.server.game.util.StringExtUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 装备副本常量类
 *
 * <AUTHOR> 2024/1/29 14:24
 **/
public class EquipmentMissionConst {

    /**
     * 快速挂机获得X分钟的奖励
     */
    public int fastHangUpRewardTime;

    /**
     * 每日免费挂机次数
     */
    public int fastHangUpFreeTimes;

    /**
     * 挂机付费次数
     */
    public int fastHangUpPayTimes;

    /**
     * 快速挂机消耗，次数超过均按最后一个值消耗
     */
    public List<Integer> fastHangUpCostList = new ArrayList<>();

    /**
     * 快速挂机资源消耗类型：优先消耗|不足消耗
     */
    public List<Integer> fastHangUpCurrencyItemList = new ArrayList<>();

    /**
     * 挂机奖励结算频率（每X分钟结算一次）
     */
    public int hangUpSettle;

    /**
     * 挂机奖励累计时长上限（分钟）
     */
    public int hangUpUpperLimit;

    /**
     * 章通关加成，万分比
     */
    public int clearBonus;


    public EquipmentMissionConst(Map<String, String> constantMap) {
        this.fastHangUpRewardTime = Integer.parseInt(constantMap.get("fastHangUpRewardTime"));
        this.fastHangUpFreeTimes = Integer.parseInt(constantMap.get("fastHangUpFreeTimes"));
        this.fastHangUpPayTimes = Integer.parseInt(constantMap.get("fastHangUpPayTimes"));
        this.fastHangUpCostList = StringExtUtil.string2List(constantMap.get("fastHangUpCost"), "|", Integer.class);
        this.fastHangUpCurrencyItemList = StringExtUtil.string2List(constantMap.get("fastHangUpCurrencyItem"), "|", Integer.class);
        this.hangUpSettle = Integer.parseInt(constantMap.get("hangUpSettle"));
        this.hangUpUpperLimit = Integer.parseInt(constantMap.get("hangUpUpperLimit"));
        this.clearBonus = Integer.parseInt(constantMap.get("clearBonus"));
    }
}

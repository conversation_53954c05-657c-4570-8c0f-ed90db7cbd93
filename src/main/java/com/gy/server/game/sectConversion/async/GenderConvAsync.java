package com.gy.server.game.sectConversion.async;

import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.lineup.bean.LineupInfoBean;
import com.gy.server.game.lineup.bean.StandHeroBean;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.role.template.ProtagonistTemplate;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 性别转换
 * @author: gbk
 * @date: 2024-08-20 11:13
 */
public class GenderConvAsync extends AsyncCall {

    private final Player player;
    int gender;
    Map<LineupType, List<LineupInfoBean>> lineupInfoMap = new HashMap<>();

    long time;

    public GenderConvAsync(Player player, int gender, long time) {
        this.player = player;
        this.gender = gender;
        this.time = time;
    }

    @Override
    public void asyncExecute() {
        //部分数据在redis存储 需要异步读取
        for (LineupType lineupType : LineupType.values()) {
            if (lineupType.getFormationType().getPlayerNum() > 1) {
                continue;
            }
            List<LineupInfoBean> lineupList = lineupType.getLineup(player);
            lineupInfoMap.put(lineupType, lineupList);
        }
    }

    @Override
    public void execute() {
        // 修改性别
        player.updateSexAndSect(gender, player.getProfession());

        ProtagonistTemplate template = player.getTemplate();
        // 移除布阵的主角技能和队长副队技能
        updateLineup(template.id);
        // 日志
        GameLogger.genderSectChange(player);
        player.dataSyncModule.syncRoleInfo();
        PbProtocol.GenderConversionRst.Builder rst = PbProtocol.GenderConversionRst.newBuilder()
                .setResult(Text.genOkServerRstInfo())
                .setGender(gender);
        player.send(PtCode.SECT_CONVERSION_SERVER, rst.build(), time);
        player.postEvent(PlayerEventType.genderChange);
    }

    private void updateLineup(int protagonistId) {
        for (Map.Entry<LineupType, List<LineupInfoBean>> entry : lineupInfoMap.entrySet()) {
            LineupType lineupType = entry.getKey();
            List<LineupInfoBean> lineupList = entry.getValue();
            if (Objects.isNull(lineupList)) {
                continue;
            }
            for (LineupInfoBean lineupInfoBean : lineupList) {
                lineupInfoBean.setCaptainSkill(0);
                lineupInfoBean.setViceCaptainSkill(0);
                // 主角技能清空
                lineupInfoBean.getSkillIds().clear();
                // 站位信息修改
                Map<Integer, StandHeroBean> standsMap = lineupInfoBean.getStandsMap();
                for (StandHeroBean standHeroBean : standsMap.values()) {
                    // 主角
                    if (standHeroBean.getHeroId() == -1) {
                        standHeroBean.setHeroTemplateId(protagonistId);
                    }
                }
            }
            lineupType.save(player, lineupList);
        }
    }


}

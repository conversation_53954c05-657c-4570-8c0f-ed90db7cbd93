package com.gy.server.game.sectConversion;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.function.Function;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.game.role.PlayerRoleService;
import com.gy.server.game.role.template.ProtagonistTemplate;
import com.gy.server.game.sectConversion.async.GenderConvAsync;
import com.gy.server.game.sectConversion.async.SectConvAsync;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.List;
import java.util.Objects;

/**
 * 门派转换服务类
 *
 * <AUTHOR> - [Created on 2023/4/3 11:30]
 */
public class SectConversionService extends PlayerPacketHandler {


    /**
     * 门派转换
     */
    @Handler(PtCode.SECT_CONVERSION_CLIENT)
    private void sectConversion(Player player, PbProtocol.SectConversionReq req, long time) {
        PbProtocol.SectConversionRst.Builder rst = PbProtocol.SectConversionRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        logic:
        {
            if (Function.sectChange.isNotOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            int protagonistId = req.getProtagonistId();
            ProtagonistTemplate newProtagonistTemplate = PlayerRoleService.getProtagonistTemplate(protagonistId);
            if(Objects.isNull(newProtagonistTemplate)){
                rst.setResult(Text.genServerRstInfo(Text.数据异常));
                break logic;
            }
            int oldProtagonistId = PlayerRoleService.getProtagonistTemplate(player).id;
            ProtagonistTemplate oldProtagonistTemplate = PlayerRoleService.getProtagonistTemplate(oldProtagonistId);
            if(Objects.isNull(oldProtagonistTemplate)){
                rst.setResult(Text.genServerRstInfo(Text.数据异常));
                break logic;
            }
            if(oldProtagonistTemplate.gender != newProtagonistTemplate.gender){
                rst.setResult(Text.genServerRstInfo(Text.门派转换id错误));
                break logic;
            }
            if (oldProtagonistId == protagonistId) {
                rst.setResult(Text.genServerRstInfo(Text.当前已经是该门派不需要转换));
                break logic;
            }
            PlayerSectConversionModel playerSectConversionModel = player.getModel(PlayerModelEnums.sectConversion);
            //检查cd
            if((playerSectConversionModel.getLastChangeSectTime() + PlayerRoleService.getConstTemplate().sectSwitchCD * DateTimeUtil.MillisOfSecond)
                    > ServerConstants.getCurrentTimeMillis()){
                rst.setResult(Text.genServerRstInfo(Text.门派转换在cd中));
                break logic;
            }

            //检查免费次数
            if(playerSectConversionModel.getChangeTimes() >= PlayerRoleService.getConstTemplate().sectSwitchFreeTimes){
                if (Reward.check(player, getConversionCost()) != -1) {
                    rst.setResult(Text.genServerRstInfo(Text.消耗不足));
                    break logic;
                }
                Reward.remove(getConversionCost(), player, BehaviorType.sectConversionCost);
            }else{

                //增加免费次数
                playerSectConversionModel.addChangeTimes();
            }
            playerSectConversionModel.setLastChangeSectTime(ServerConstants.getCurrentTimeMillis());
            ThreadPool.execute(new SectConvAsync(player, protagonistId, time));
            return;
        }
        player.send(PtCode.SECT_CONVERSION_SERVER, rst.build(), time);
    }

    @Handler(PtCode.GENDER_CONVERSION_REQ)
    private void genderConversion(Player player, PbProtocol.GenderConversionReq req, long time) {
        PbProtocol.SectConversionRst.Builder rst = PbProtocol.SectConversionRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        logic:
        {
            if (Function.sectChange.isNotOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            int gender = req.getGender();
            if (gender == player.getSex()) {
                rst.setResult(Text.genServerRstInfo(Text.当前已经是该性别不需要转换));
                break logic;
            }

            //检查cd
            PlayerSectConversionModel playerSectConversionModel = player.getModel(PlayerModelEnums.sectConversion);
            if((playerSectConversionModel.getLastChangeSectTime() + PlayerRoleService.getConstTemplate().genderSwitchCD * DateTimeUtil.MillisOfSecond)
                    > ServerConstants.getCurrentTimeMillis()){
                rst.setResult(Text.genServerRstInfo(Text.性别转换在cd中));
                break logic;
            }

            if (Reward.check(player, getGenderConversionCost()) != -1) {
                rst.setResult(Text.genServerRstInfo(Text.消耗不足));
                break logic;
            }

            Reward.remove(getConversionCost(), player, BehaviorType.genderConversionCost);

            playerSectConversionModel.setLastChangeGenderTime(ServerConstants.getCurrentTimeMillis());
            ThreadPool.execute(new GenderConvAsync(player, gender, time));
            return;
        }
        player.send(PtCode.GENDER_CONVERSION_RST, rst.build(), time);
    }

    /**
     * 门派创建
     */
//    @Handler(PtCode.SECT_CREATE_CLIENT)
//    private void sectCreate(Player player, PbProtocol.SectCreateReq req, long time) {
//        PbProtocol.SectCreateRst.Builder rst = PbProtocol.SectCreateRst.newBuilder()
//                .setResult(Text.genOkServerRstInfo());
//        logic:
//        {
//            if (Function.sectChange.isNotOpen(player)) {
//                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
//                break logic;
//            }
//            int index = req.getIndex();
//            Map<Integer, String> positionUnlockMap = getPositionUnlockMap();
//            if (!positionUnlockMap.containsKey(index)) {
//                rst.setResult(Text.genServerRstInfo(Text.参数异常));
//                break logic;
//            }
//            PlayerSectConversionModel playerSectConversionModel = player.getModel(PlayerModelEnums.sectConversion);
//            Map<Integer, SectInfo> sectInfoMap = playerSectConversionModel.getSectInfoMap();
//            int oldProtagonistId = PlayerRoleService.getProtagonistTemplate(player).id;
//            if (sectInfoMap.containsKey(index)) {
//                rst.setResult(Text.genServerRstInfo(Text.参数异常));
//                break logic;
//            }
//            if (oldProtagonistId == req.getProtagonistId()) {
//                rst.setResult(Text.genServerRstInfo(Text.参数异常));
//                break logic;
//            }
//            String unlock = positionUnlockMap.get(index);
//            if (CondManager.checkNotCond(player, unlock)) {
//                rst.setResult(Text.genServerRstInfo(Text.门派转换槽位未解锁));
//                break logic;
//            }
//            ProtagonistTemplate protagonistTemplate = PlayerRoleService.getProtagonistTemplate(req.getProtagonistId());
//            if (Objects.isNull(protagonistTemplate)) {
//                rst.setResult(Text.genServerRstInfo(Text.参数异常));
//                break logic;
//            }
//            // 门派是否已创建
//            if (playerSectConversionModel.isCreated(protagonistTemplate.sectId)) {
//                rst.setResult(Text.genServerRstInfo(Text.门派已创建));
//                break logic;
//            }
//            SectInfo sectInfo = new SectInfo();
//            sectInfo.setProtagonistId(req.getProtagonistId());
//            sectInfoMap.put(index, sectInfo);
//            rst.setIndex(index);
//            rst.setProtagonistId(sectInfoMap.get(index).getProtagonistId());
//            RedDot.sectConversion.sync(player);
//        }
//        player.send(PtCode.SECT_CREATE_SERVER, rst.build(), time);
//    }


    public static List<Reward> getConversionCost() {
        return Reward.templateCollectionToReward(RewardTemplate.readListFromText(PlayerRoleService.getConstTemplate().sectSwitchCost));
    }

    public static List<Reward> getGenderConversionCost() {
        return Reward.templateCollectionToReward(RewardTemplate.readListFromText(PlayerRoleService.getConstTemplate().genderSwitchCost));
    }

}

package com.gy.server.game.sectConversion.async;

import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.attribute.AttributeSourceType;
import com.gy.server.game.combataddition.CombatAdditionFunction;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.lineup.bean.LineupInfoBean;
import com.gy.server.game.lineup.bean.StandHeroBean;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.role.BookInfoBean;
import com.gy.server.game.role.PlayerRoleModel;
import com.gy.server.game.role.PlayerRoleService;
import com.gy.server.game.role.template.ProtagonistTemplate;
import com.gy.server.game.sectConversion.PlayerSectConversionModel;
import com.gy.server.game.sectConversion.SectInfo;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;

import java.util.*;

/**
 * 门派转换异步处理
 *
 * <AUTHOR> - [Created on 2023/11/9 19:57]
 */
public class SectConvAsync extends AsyncCall {

    private final Player player;
    private final int newProtagonistId;

    private final Map<LineupType, List<LineupInfoBean>> lineupInfoMap = new HashMap<>();

    private long time;

    public SectConvAsync(Player player, int protagonistId, long time) {
        this.player = player;
        this.newProtagonistId = protagonistId;
        this.time = time;
    }

    @Override
    public void asyncExecute() {
        //部分数据在redis存储 需要异步读取
        for (LineupType lineupType : LineupType.values()) {
            if (lineupType.getFormationType().getPlayerNum() > 1) {
                continue;
            }
            List<LineupInfoBean> lineupList = lineupType.getLineup(player);
            lineupInfoMap.put(lineupType, lineupList);
        }
    }

    @Override
    public void execute() {
        PlayerSectConversionModel conversionModel = player.getModel(PlayerModelEnums.sectConversion);
        Map<Integer, SectInfo> sectInfoMap = conversionModel.getSectInfoMap();
        int oldProtagonistId = PlayerRoleService.getProtagonistTemplate(player).id;
        // 切换门派
        SectInfo sectInfo = sectInfoMap.getOrDefault(newProtagonistId, new SectInfo(newProtagonistId));
        // 转换前的门派信息
        SectInfo oldSectInfo = sectInfoMap.getOrDefault(oldProtagonistId, new SectInfo(oldProtagonistId));
        // 记录当前的功法信息
        PlayerRoleModel roleModel = player.getRoleModel();
        Map<Integer, BookInfoBean> bookMap = roleModel.getBookMap();
        oldSectInfo.setBookMap(new HashMap<>(bookMap));
        oldSectInfo.setRealmLv(roleModel.getRealmLv());
//        oldSectInfo.setRealmExp(roleModel.getRealmExp());
        // 记录计算功法重置的返还奖励
        List<Reward> totalCostReward = roleModel.calcBookTotalCost();
        oldSectInfo.setBookRewardList(new ArrayList<>(totalCostReward));
        if (!totalCostReward.isEmpty()) {
            // 先返还本次奖励
            Reward.add(totalCostReward, player, BehaviorType.sectConversionBookReward);
        }


        // 再消耗功法还原消耗
        List<Reward> bookRewardList = sectInfo.getBookRewardList();
        if (!bookRewardList.isEmpty()) {
            //需要升级
            if (Reward.check(player, bookRewardList) != -1) {
                // 消耗不足,不再提升
                roleModel.getBookMap().clear();
                roleModel.setRealmLv(1);
//                roleModel.setRealmExp(0);
                //重新记录
                oldSectInfo.setBookMap(new HashMap<>(bookMap));
                oldSectInfo.setRealmLv(roleModel.getRealmLv());
//                oldSectInfo.setRealmExp(roleModel.getRealmExp());
                bookRewardList.clear();

            } else {
                roleModel.setBookMap(sectInfo.getBookMap());
                roleModel.setRealmLv(sectInfo.getRealmLv());
//                roleModel.setRealmExp(sectInfo.getRealmExp());

                Reward.remove(bookRewardList, player, BehaviorType.sectConversionBookCost);
            }
        }else{
            //不需要升级,直接记录
            roleModel.getBookMap().clear();
            roleModel.setRealmLv(1);
//            roleModel.setRealmExp(0);
        }

        sectInfoMap.put(sectInfo.getProtagonistId(), sectInfo);
        sectInfoMap.put(oldSectInfo.getProtagonistId(), oldSectInfo);
        // 移除布阵的主角技能和队长副队技能
        updateLineup(newProtagonistId);
        // 修改性别和门派信息
        ProtagonistTemplate protagonistTemplate = PlayerRoleService.getProtagonistTemplate(newProtagonistId);
        player.updateSexAndSect(protagonistTemplate.gender, protagonistTemplate.sectId);
        // 恢复功法神器增加的全局属性
        player.getCombatAdditionModel().refresh(false, CombatAdditionFunction.book, CombatAdditionFunction.artifact);
        // 日志
        GameLogger.protagonistSectChange(player);
        // 主角信息同步
        player.getRoleModel().refreshAttributes(true, true, AttributeSourceType.英雄模板属性,
                AttributeSourceType.功法, AttributeSourceType.战斗属性培养, AttributeSourceType.神器主角);
        player.dataSyncModule.syncRoleInfo();
        // 返还奖励移除掉消耗奖励
        if (!totalCostReward.isEmpty() && !bookRewardList.isEmpty()) {
            Reward.removeRewardByOtherReward(totalCostReward, bookRewardList);
        }
        PbProtocol.SectConversionRst.Builder rst = PbProtocol.SectConversionRst.newBuilder()
                .setResult(Text.genOkServerRstInfo())
                .addAllRewards(Reward.writeCollectionToPb(totalCostReward))
                .setProtagonistId(newProtagonistId);
        player.send(PtCode.SECT_CONVERSION_SERVER, rst.build(), time);
        player.postEvent(PlayerEventType.roleSectConversion);
    }

    private void updateLineup(int protagonistId) {
        for (Map.Entry<LineupType, List<LineupInfoBean>> entry : lineupInfoMap.entrySet()) {
            LineupType lineupType = entry.getKey();
            List<LineupInfoBean> lineupList = entry.getValue();
            if (Objects.isNull(lineupList)) {
                continue;
            }
            for (LineupInfoBean lineupInfoBean : lineupList) {
                lineupInfoBean.setCaptainSkill(0);
                lineupInfoBean.setViceCaptainSkill(0);
                // 主角技能清空
                lineupInfoBean.getSkillIds().clear();
                // 站位信息修改
                Map<Integer, StandHeroBean> standsMap = lineupInfoBean.getStandsMap();
                for (StandHeroBean standHeroBean : standsMap.values()) {
                    // 主角
                    if (standHeroBean.getHeroId() == -1) {
                        standHeroBean.setHeroTemplateId(protagonistId);
                    }
                }
            }
            lineupType.save(player, lineupList);
        }
    }


}

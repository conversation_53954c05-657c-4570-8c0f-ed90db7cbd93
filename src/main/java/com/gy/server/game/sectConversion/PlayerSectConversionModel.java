package com.gy.server.game.sectConversion;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.face.PlayerFaceModel;
import com.gy.server.game.player.*;
import com.gy.server.game.player.event.PlayerEvent;
import com.gy.server.game.player.event.PlayerEventHandler;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.reddot.RedDot;
import com.gy.server.game.role.PlayerRoleService;
import com.gy.server.game.role.template.ProtagonistTemplate;
import com.ttlike.server.tl.baselib.serialize.player.PlayerBlobDb;
import com.ttlike.server.tl.baselib.serialize.player.SectConversionModelDb;
import com.ttlike.server.tl.baselib.serialize.player.SectInfoDb;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 门派转换模块
 *
 * <AUTHOR> - [Created on 2023/4/3 11:33]
 */
public class PlayerSectConversionModel extends PlayerModel implements PlayerEventHandler {

    /**
     * 已创建的门派信息
     * Key:槽位信息 Value:门派信息
     */
    private final Map<Integer, SectInfo> sectInfoMap = new HashMap<>();

    //转换次数（按月重置）
    private int changeTimes;

    //上次门派转换时间
    private long lastChangeSectTime;
    //上次性别转换时间
    private long lastChangeGenderTime;

    public long getLastChangeGenderTime() {
        return lastChangeGenderTime;
    }

    public void setLastChangeGenderTime(long lastChangeGenderTime) {
        this.lastChangeGenderTime = lastChangeGenderTime;
    }

    public long getLastChangeSectTime() {
        return lastChangeSectTime;
    }

    public void setLastChangeSectTime(long lastChangeSectTime) {
        this.lastChangeSectTime = lastChangeSectTime;
    }

    public int getChangeTimes() {
        return changeTimes;
    }

    public void addChangeTimes(){
        changeTimes++;
    }

    public void setChangeTimes(int changeTimes) {
        this.changeTimes = changeTimes;
    }



    public PlayerSectConversionModel(Player player) {
        super(player);
    }

    @Override
    protected void loadData(PlayerBlobDb playerBlob) {
        SectConversionModelDb sectConversionModelDb = playerBlob.getSectConversionModelDb();
        if (Objects.nonNull(sectConversionModelDb)) {
            for (Map.Entry<Integer, SectInfoDb> dbEntry : sectConversionModelDb.getSectInfoMap().entrySet()) {
                Integer index = dbEntry.getKey();
                SectInfoDb sectInfoDb = dbEntry.getValue();
                SectInfo sectInfo = new SectInfo(sectInfoDb);
                sectInfoMap.put(index, sectInfo);
            }
            changeTimes = sectConversionModelDb.getChangeTimes();
            lastChangeSectTime = sectConversionModelDb.getLastChangeSectTime();
            lastChangeGenderTime = sectConversionModelDb.getLastChangeGenderTime();
        }
    }

    @Override
    protected void saveData(PlayerBlobDb playerBlob) {
        SectConversionModelDb sectConversionModelDb = new SectConversionModelDb();
        for (Map.Entry<Integer, SectInfo> entry : sectInfoMap.entrySet()) {
            sectConversionModelDb.getSectInfoMap().put(entry.getKey(), entry.getValue().genDb());
        }
        sectConversionModelDb.setChangeTimes(changeTimes);
        sectConversionModelDb.setLastChangeSectTime(lastChangeSectTime);
        sectConversionModelDb.setLastChangeGenderTime(lastChangeGenderTime);
        playerBlob.setSectConversionModelDb(sectConversionModelDb);
    }

    @Override
    public PlayerEventType[] getEventTypes() {
        return new PlayerEventType[]{
                PlayerEventType.register,
                PlayerEventType.levelUp,
                PlayerEventType.passMission,
                PlayerEventType.day5Refresh,
                PlayerEventType.roleSectConversion,
                PlayerEventType.genderChange,
        };
    }

    @Override
    public void handle(PlayerEvent event) {
        switch (event.getEventType()) {
            case passMission:
            case levelUp: {
                RedDot.sectConversion.sync(getPlayer());
                break;
            }
            case roleSectConversion: {
                //同步miniPlayer
                Player player = getPlayer();
                Optional.ofNullable(PlayerManager.getMiniPlayer(getPlayerId()))
                        .ifPresent(miniPlayer -> miniPlayer.change(BaseInfoSyncType.roleSect, player.getProfession()));
                break;
            }
            case genderChange:{
                Player player = getPlayer();
                Optional.ofNullable(PlayerManager.getMiniPlayer(getPlayerId()))
                        .ifPresent(miniPlayer -> miniPlayer.change(BaseInfoSyncType.gender, player.getSex()));
                break;
            }
            case day5Refresh: {
                if(ServerConstants.getCurrentTimeLocalDateTime().getDayOfMonth() == 1){
                    setChangeTimes(0);
                }
                break;
            }
        }
    }

    public Map<Integer, SectInfo> getSectInfoMap() {
        return sectInfoMap;
    }

}

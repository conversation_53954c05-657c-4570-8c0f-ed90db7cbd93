package com.gy.server.game.sectConversion;

import com.gy.server.game.drop.Reward;
import com.gy.server.game.role.BookInfoBean;
import com.ttlike.server.tl.baselib.serialize.player.SectInfoDb;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 门派信息
 *
 * <AUTHOR> - [Created on 2023/4/3 11:35]
 */
public class SectInfo {
    /**
     * 主角ID
     */
    private int protagonistId;
    /**
     * 功法Map Key 功法ID  Value:功法信息
     */
    private Map<Integer, BookInfoBean> bookMap = new HashMap<>();
    /**
     * 功法境界等级
     */
    private int realmLv = 1;
//    /**
//     * 功法境界经验
//     */
//    private long realmExp;
    /**
     * 功法返还资源
     */
    private List<Reward> bookRewardList = new ArrayList<>();
    /**
     * 捏脸数据
     */
    private byte[] faceDataDb;


    public SectInfo() {
    }

    public SectInfo(int protagonistId) {
        this.protagonistId = protagonistId;
    }

    public SectInfo(SectInfoDb sectInfoDb) {
        this.protagonistId = sectInfoDb.getProtagonistId();
        sectInfoDb.getBookInfoMap().forEach((key, value) -> {
            bookMap.put(key, new BookInfoBean(value));
        });

        this.realmLv = sectInfoDb.getRealmLv();
//        this.realmExp = sectInfoDb.getRealmExp();

        sectInfoDb.getBookRewards().forEach(bean -> {
            this.bookRewardList.add(Reward.create(bean));
        });

        this.faceDataDb = sectInfoDb.getFaceDataDb();
    }

    public SectInfoDb genDb() {
        SectInfoDb db = new SectInfoDb();
        db.setProtagonistId(protagonistId);

        for (Map.Entry<Integer, BookInfoBean> entry : bookMap.entrySet()) {
            db.getBookInfoMap().put(entry.getKey(), entry.getValue().genDb());
        }

        db.setRealmLv(realmLv);
//        db.setRealmExp(realmExp);
        for (Reward reward : bookRewardList) {
            db.getBookRewards().add(reward.genDb());
        }
        db.setFaceDataDb(faceDataDb);
        return db;
    }


    public int getProtagonistId() {
        return protagonistId;
    }

    public void setProtagonistId(int protagonistId) {
        this.protagonistId = protagonistId;
    }

    public Map<Integer, BookInfoBean> getBookMap() {
        return bookMap;
    }

    public void setBookMap(Map<Integer, BookInfoBean> bookMap) {
        this.bookMap = bookMap;
    }

    public int getRealmLv() {
        return realmLv;
    }

    public void setRealmLv(int realmLv) {
        this.realmLv = realmLv;
    }

//    public long getRealmExp() {
//        return realmExp;
//    }

//    public void setRealmExp(long realmExp) {
//        this.realmExp = realmExp;
//    }

    public List<Reward> getBookRewardList() {
        return bookRewardList;
    }

    public void setBookRewardList(List<Reward> bookRewardList) {
        this.bookRewardList = bookRewardList;
    }

    public byte[] getFaceDataDb() {
        return faceDataDb;
    }

    public void setFaceDataDb(byte[] faceDataDb) {
        this.faceDataDb = faceDataDb;
    }
}

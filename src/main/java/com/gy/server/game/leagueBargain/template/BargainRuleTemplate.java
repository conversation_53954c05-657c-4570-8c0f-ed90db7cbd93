package com.gy.server.game.leagueBargain.template;


import java.util.Map;

/**
 * 砍价规则
 *
 * <AUTHOR> 2024/11/12 13:44
 **/
public class BargainRuleTemplate {

    public int id;

    /**
     * 商品id
     */
    public int bargainItem;

    /**
     * 累计参与人数
     */
    public int bargainAmount;

    /**
     * 砍价上限，千分比，最低砍价的价格为1，千分比
     */
    public Integer bargainScale;

    /**
     * 总比例 千分比
     */
    public int allScale;

    public BargainRuleTemplate(Map<String, String> map) {
        this.id = Integer.parseInt(map.get("id"));
        this.bargainItem = Integer.parseInt(map.get("BargainItem"));
        this.bargainAmount = Integer.parseInt(map.get("BargainAmount"));
        this.bargainScale = Integer.parseInt(map.get("BargainScale"));
        this.allScale = Integer.parseInt(map.get("AllScale"));
    }
}

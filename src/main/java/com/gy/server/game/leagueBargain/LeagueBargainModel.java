package com.gy.server.game.leagueBargain;

import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.league.*;
import com.gy.server.game.league.event.LeagueEvent;
import com.gy.server.game.league.event.LeagueEventHandler;
import com.gy.server.game.league.event.LeagueEventType;
import com.gy.server.game.league.log.LeagueLogListType;
import com.gy.server.game.league.log.LeagueLogType;
import com.gy.server.game.leagueBargain.template.BargainItemTemplate;
import com.gy.server.game.leagueBargain.template.BargainRuleTemplate;
import com.gy.server.game.mail.MailManager;
import com.gy.server.game.mail.MailType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.game.reddot.RedDot;
import com.gy.server.game.text.Text;
import com.gy.server.game.world.World;
import com.gy.server.packet.PbLeague;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.MathUtil;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.serialize.league.LeagueBlobDb;
import com.ttlike.server.tl.baselib.serialize.leagueBargain.LeagueBargainModelDb;
import com.ttlike.server.tl.baselib.serialize.reward.RewardDb;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.time.DayOfWeek;
import java.util.*;

/**
 * 帮派砍一刀模块
 * <AUTHOR> 2024/11/7 20:18
 **/
public class LeagueBargainModel extends LeagueModel implements LeagueEventHandler, LeagueDataInterface {

    /**
     * 砍价表id
     */
    private int id;

    /**
     * 减少价格
     */
    private int cutAmount;

    /**
     * 已经砍价人数
     */
    private int cutNum;

    /**
     * 折扣map
     * 只算正向打折，不计算负向和保底负数
     * key:顺序   value:折扣
     */
    private Map<Integer, Integer> indexDiscountMap = new HashMap<>();

    /**
     * 已经砍价的成员id集合
     */
    private Set<Long> cutPlayerIdList = new HashSet<>();

    /**
     * 已经购买的成员id集合
     */
    private Map<Long, Reward> buyMap = new HashMap<>();

    //------------------------------保底参数-----------------------------

    /**
     * 本轮开始时间
     */
    private long start;

    /**
     * 今天是否触发保底
     * 触发时砍价人数到达配置数，将折扣变为保底的范围
     */
    private boolean isFloor;

    /**
     * 第x周是否触发负值
     * key:第几周  value:负值触发几次
     */
    private Map<Integer, Integer> weekMap = new HashMap<>();

    public LeagueBargainModel(League league) {
        super(league);
        refresh();
    }

    @Override
    protected void loadData(LeagueBlobDb leagueBlobDb) {
        LeagueBargainModelDb db = leagueBlobDb.getBargainModelDb();
        if(db != null){
            this.id = db.getId();
            this.cutAmount = db.getCutAmount();
            this.cutNum = db.getCutNum();
            this.indexDiscountMap.putAll(db.getIndexDiscountMap());
            this.cutPlayerIdList.addAll(db.getCutPlayerIdList());
            for (Map.Entry<Long, RewardDb> entry : db.getBuyMap().entrySet()) {
                this.buyMap.put(entry.getKey(), Reward.create(entry.getValue()));
            }
            this.start = db.getStart();
            this.isFloor = db.isFloor();
            this.weekMap.putAll(db.getWeekMap());
        }
    }

    @Override
    protected void saveData(LeagueBlobDb leagueBlobDb) {
        LeagueBargainModelDb db = new LeagueBargainModelDb();
        db.setId(this.id);
        db.setCutAmount(this.cutAmount);
        db.setCutNum(this.cutNum);
        db.getIndexDiscountMap().putAll(this.indexDiscountMap);
        db.getCutPlayerIdList().addAll(this.cutPlayerIdList);
        for (Map.Entry<Long, Reward> entry : this.buyMap.entrySet()) {
            db.getBuyMap().put(entry.getKey(), entry.getValue().genDb());
        }
        db.setStart(this.start);
        db.setFloor(this.isFloor);
        db.getWeekMap().putAll(this.weekMap);
        leagueBlobDb.setBargainModelDb(db);
    }

    @Override
    public LeagueEventType[] getEventTypes() {
        return new LeagueEventType[]{LeagueEventType.day5Refresh};
    }

    @Override
    public void handle(LeagueEvent event) {
        switch (event.getEventType()){
            case day5Refresh:{
                refresh();
                break;
            }
        }
    }

    private void refresh(){
        dayRefresh();
        if(ServerConstants.getCurrentTimeLocalDateTime().getDayOfWeek() == DayOfWeek.MONDAY){
            weekRefresh();
        }
    }

    public PbLeague.LeagueBargainInfo genPb(Player player) {
        PbLeague.LeagueBargainInfo.Builder builder = PbLeague.LeagueBargainInfo.newBuilder();
        builder.setId(id);
        builder.setCutAmount(cutAmount);
        builder.setCutNum(cutNum);

        PlayerLeagueModel leagueModel = player.getModel(PlayerModelEnums.league);
        builder.setHadCut(leagueModel.isHadCut());
        builder.setHadBuy(leagueModel.isHadBuy());
        getLeague().getLeagueLogList(LeagueLogListType.bargain).forEach(bean -> builder.addLog(bean.writeToPb()));
        return builder.build();
    }

    private void dayRefresh() {
        //补差价
        BargainItemTemplate bargainItemTemplate = LeagueBargainService.getItemMap().get(id);
        if(bargainItemTemplate != null){
            Reward cost = bargainItemTemplate.cost.createReward();

            for (Map.Entry<Long, Reward> entry : buyMap.entrySet()) {
                if(entry.getValue().getValue() <= 0){
                    continue;
                }

                int finallyNum = Math.max((int)cost.getValue() - cutAmount, 0);
                int diff = Math.max((int)entry.getValue().getValue(), 0) - finallyNum;
                if(diff > 0){
                    Reward temp = cost.copy();
                    temp.setValue(diff);

                    //补发邮件
                    MailType mailType = MailType.bargainReturn;
                    List<Reward> list = new ArrayList<>();
                    list.add(temp);
                    MailManager.sendMail(mailType, entry.getKey(), Text.genText(mailType.getTitleId()).build(), Text.genText(mailType.getContentId()).build(), ServerConstants.getCurrentTimeMillis(), list);
                }
            }
        }

        BargainItemTemplate itemTemplate = LeagueBargainService.getBargainItemTemplate();
        this.id = itemTemplate == null ? 0 : itemTemplate.id;
        this.cutAmount = 0;
        this.cutNum = 0;
        this.indexDiscountMap.clear();

        this.cutPlayerIdList.clear();
        this.buyMap.clear();
        //清理日志
        getLeague().getLeagueLogList(LeagueLogListType.bargain).clear();

        if(this.id != 0){
            //开始处理折扣
            Map<Integer, BargainRuleTemplate> ruleMap = LeagueBargainService.getRuleMap().get(this.id);
            List<Integer> numList = new ArrayList<>(ruleMap.keySet());
            numList.sort(Integer::compare);

            List<Integer> randomList = new ArrayList<>();

            int id = 0;
            int lastNum = 0;
            for (int num : numList) {
                BargainRuleTemplate template = ruleMap.get(num);
                int count = template.bargainAmount;
                if(id == template.bargainItem){
                    count = template.bargainAmount - lastNum;
                }else {
                    id = template.bargainItem;
                }
                lastNum = template.bargainAmount;
                randomList.addAll(createRandomBySumAndRange(template.allScale, count, 1, template.bargainScale, template.id));
            }

            for(int i = 0; i < randomList.size(); i++){
                int index = i + 1;
                indexDiscountMap.put(index, randomList.get(i));
            }
        }

        if(World.getOpenServerDay() > 7){
            checkIsFloor();
            weekRefresh();
        }

        //红点处理
        for (long playerId : getLeague().getMemberMap().keySet()) {
            Player player = PlayerManager.getOnlinePlayer(playerId);
            if(player != null){
                RedDot.leagueBargainFreeTime.sync(player);
            }
        }

    }

    private void weekRefresh() {
        if(ServerConstants.getCurrentTimeMillis() >= start + LeagueService.getConstant().bargainGuaranteeWeek * DateTimeUtil.MillisOfDay * 7){
            weekMap.clear();
            isFloor = false;
            start = ServerConstants.getNowHourTime(CommonUtils.getRefreshTimeHour());
        }
    }

    /**
     * 检查是否触发负价保底
     */
    private void checkIsFloor(){
        if(weekMap.containsValue(true)){
            return;
        }

        //本轮最后一天
        if(ServerConstants.getCurrentTimeMillis() + DateTimeUtil.MillisOfDay >= start + LeagueService.getConstant().bargainGuaranteeWeek * DateTimeUtil.MillisOfDay * 7){
            isFloor = true;
        }
    }

    /**
     * 产出固定数量，和值固定，且在一定范围内的数目确定的随机数
     * @param sum
     * @param count
     * @param min
     * @param max
     * @param id 表id
     * @return
     */
    public static List<Integer> createRandomBySumAndRange(int sum, int count, int min, int max, int id){
        List<Integer> list = new ArrayList<>();

        if(min * count > sum || max * count < sum){
            throw new IllegalArgumentException("random data error, sum is " + sum + ", count is " + count + ", min is " + min + ", max is " + max + ", id is " + id);
        }

        int sumTemp = sum;
        for(int i = 1; i <= count - 1; i++){
            int num = MathUtil.randomInt(max - min + 1) + min;
            while (sumTemp - num < (count - i) * min || sumTemp - num > (count - i) * max){
                num = MathUtil.randomInt(max - min + 1) + min;
            }

            sumTemp = sumTemp - num;
            list.add(num);
        }

        list.add(sumTemp);

        return list;
    }

    public void addCutAmount(int addCutAmount) {
        cutNum++;
        cutAmount = cutAmount + addCutAmount;

        if(cutAmount < 0){
            int week = (int) ((ServerConstants.getCurrentTimeMillis() - start) / (7 * DateTimeUtil.MillisOfDay) + 1);
            weekMap.put(week, weekMap.getOrDefault(week, 0) + 1);
        }
    }

    /**
     * 同步
     */
    public void sync(){
        League league = getLeague();
        ThreadPool.execute(()->{
            for (long playerId : league.getMemberMap().keySet()) {
                Player onlinePlayer = PlayerManager.getOnlinePlayer(playerId);
                if(Objects.nonNull(onlinePlayer)){
                    onlinePlayer.send(PtCode.LEAGUE_BARGAIN_NOTIFY, PbProtocol.LeagueBargainNotify.newBuilder().setInfo(genPb(onlinePlayer)).build());
                }
            }
        });
    }

    /**
     * 计算本次折扣并处理
     * @return
     */
    public int countCutAmountAndDeal(Player player) {
        int newCut = 0;
        if (cutPlayerIdList.size() <= getLeague().getMemberLimit()) {
            //触发保底
            if (isFloor && cutNum + 1 == LeagueService.getConstant().bargainGuaranteeNum) {
                BargainItemTemplate template = LeagueBargainService.getItemMap().get(id);
                newCut = -1 * (MathUtil.randomInt(template.minusPrice.getRight() - template.minusPrice.getLeft()) + template.minusPrice.getLeft());
                int num = (int) template.cost.value;
                newCut = newCut - num + cutAmount;
            } else {
                newCut = indexDiscountMap.getOrDefault(cutNum + 1, 0);
                int week = (int) ((ServerConstants.getCurrentTimeMillis() - start) / (7 * DateTimeUtil.MillisOfDay) + 1);
                if (weekMap.getOrDefault(week, 0) >= LeagueService.getConstant().bargainWeeklyMinusTime) {
                    newCut = 0;
                }
            }
        }

        cutPlayerIdList.add(player.getPlayerId());
        addCutAmount(newCut);

        //记录联盟日志
        getLeague().addLeagueLog(LeagueLogType.bargain, player.getName(), newCut + "");
        sync();

        return newCut;
    }

    public void buyAndDeal(Player player, Reward cost, PlayerLeagueModel leagueModel) {
        leagueModel.setHadBuy(true);
        buyMap.put(player.getPlayerId(), cost);
        //记录联盟日志
        getLeague().addLeagueLog(LeagueLogType.bargainBuy, player.getName(), cost.getValue() + "");
        sync();
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getCutAmount() {
        return cutAmount;
    }

    public void setCutAmount(int cutAmount) {
        this.cutAmount = cutAmount;
    }

    public int getCutNum() {
        return cutNum;
    }

    public void setCutNum(int cutNum) {
        this.cutNum = cutNum;
    }

    public Map<Integer, Integer> getIndexDiscountMap() {
        return indexDiscountMap;
    }

    public void setIndexDiscountMap(Map<Integer, Integer> indexDiscountMap) {
        this.indexDiscountMap = indexDiscountMap;
    }

    public Set<Long> getCutPlayerIdList() {
        return cutPlayerIdList;
    }

    public void setCutPlayerIdList(Set<Long> cutPlayerIdList) {
        this.cutPlayerIdList = cutPlayerIdList;
    }

    public Map<Long, Reward> getBuyMap() {
        return buyMap;
    }

    public void setBuyMap(Map<Long, Reward> buyMap) {
        this.buyMap = buyMap;
    }

    public boolean isFloor() {
        return isFloor;
    }

    public void setFloor(boolean floor) {
        isFloor = floor;
    }

    public long getStart() {
        return start;
    }

    public void setStart(long start) {
        this.start = start;
    }

    public Map<Integer, Integer> getWeekMap() {
        return weekMap;
    }

    public void setWeekMap(Map<Integer, Integer> weekMap) {
        this.weekMap = weekMap;
    }

    @Override
    public boolean canMerge(long leagueId) {
        return true;
    }

    @Override
    public void merge(League selfLeague, League targetLeague) {
//        合并后以主体帮会奖励内容为准						每日砍一刀和购买次数绑定个人

    }

    @Override
    public void removeLeague(League league) {

    }

    @Override
    public void quitLeague(long pid) {

    }
}

package com.gy.server.game.leagueBargain;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.function.Function;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.league.LeagueModelEnums;
import com.gy.server.game.league.PlayerLeagueModel;
import com.gy.server.game.leagueBargain.template.BargainItemTemplate;
import com.gy.server.game.leagueBargain.template.BargainRuleMinusTemplate;
import com.gy.server.game.leagueBargain.template.BargainRuleTemplate;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.game.reddot.RedDot;
import com.gy.server.game.service.Service;
import com.gy.server.game.text.Text;
import com.gy.server.game.world.World;
import com.gy.server.packet.PbProtocol;
import org.jetbrains.annotations.Nullable;

import java.util.*;

import static com.gy.server.game.leagueBargain.LeagueBargainModel.createRandomBySumAndRange;

/**
 * 帮派砍一刀服务类
 *
 * <AUTHOR> 2024/11/8 10:05
 **/
public class LeagueBargainService extends PlayerPacketHandler implements Service {

    /**
     * key:id
     */
    private static Map<Integer, BargainItemTemplate> itemMap = new HashMap<>();

    /**
     * key:商品id     value:key:累计参与人数 value:
     */
    private static Map<Integer, Map<Integer, BargainRuleTemplate>> ruleMap = new HashMap<>();

    /**
     * key:商品id     value:key:累计参与人数 value:
     */
    private static Map<Integer, Map<Integer, BargainRuleMinusTemplate>> ruleMinusMap = new HashMap<>();

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.league_bargainItem);
        List<Integer> check = new ArrayList<>();
        for (Map<String, String> map : mapList) {
            BargainItemTemplate template = new BargainItemTemplate(map);
            itemMap.put(template.id, template);
            if(template.shelfTimeInitial != -1){
                check.add(template.shelfTimeInitial);
            }
        }

        for(int i = 1; i <= 7; i++){
            if(!check.contains(i)){
                throw new IllegalArgumentException("BargainItemTemplate is null, day is " + i);
            }
        }

        mapList = ConfigReader.read(ConfigFile.league_bargainRule);
        for (Map<String, String> map : mapList) {
            BargainRuleTemplate template = new BargainRuleTemplate(map);
            ruleMap.computeIfAbsent(template.bargainItem, bean -> new HashMap<>()).put(template.bargainAmount, template);
        }
        ruleCheck();

        mapList = ConfigReader.read(ConfigFile.league_bargainRuleMinus);
        for (Map<String, String> map : mapList) {
            BargainRuleMinusTemplate template = new BargainRuleMinusTemplate(map);
            ruleMinusMap.computeIfAbsent(template.bargainItem, bean -> new HashMap<>()).put(template.bargainAmount, template);
        }
    }


    public void ruleCheck(){
        for (int itemId : itemMap.keySet()) {
            //开始处理折扣
            Map<Integer, BargainRuleTemplate> ruleMap = LeagueBargainService.getRuleMap().get(itemId);
            List<Integer> numList = new ArrayList<>(ruleMap.keySet());
            numList.sort(Integer::compare);

            int id = 0;
            int lastNum = 0;
            for (int num : numList) {
                BargainRuleTemplate temp = ruleMap.get(num);
                int count = temp.bargainAmount;
                if(id == temp.bargainItem){
                    count = temp.bargainAmount - lastNum;
                }else {
                    id = temp.bargainItem;
                }
                lastNum = temp.bargainAmount;
                try {
                    List<Integer> list = createRandomBySumAndRange(temp.allScale, count, 1, temp.bargainScale, temp.id);
//                    System.out.println("LeagueBargainService itemId is " + itemId + ", rule id is " + temp.id + ", cut list:" + list);
                }catch (Exception e){
                    e.printStackTrace();
                }

            }
        }
    }

    @Override
    public void clearConfigData() {
        itemMap.clear();
        ruleMap.clear();
        ruleMinusMap.clear();
    }

    /**
     * 帮派砍一刀信息
     */
    @Handler(PtCode.LEAGUE_BARGAIN_INFO_REQ)
    private void leagueBargainInfoReq(Player player, PbProtocol.LeagueBargainInfoReq req, long time){
        PbProtocol.LeagueBargainInfoRst.Builder rst = PbProtocol.LeagueBargainInfoRst.newBuilder().setResult(Text.genOkServerRstInfo());

        logic:{
            int text = commonCheck(player);
            if(text != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(text));
                break logic;
            }

            League league = LeagueManager.getLeagueByPlayer(player);
            if(league == null){
                rst.setResult(Text.genServerRstInfo(Text.未加入帮派));
                break logic;
            }

            LeagueBargainModel model = league.getModel(LeagueModelEnums.bargain);
            rst.setInfo(model.genPb(player));
        }

        player.send(PtCode.LEAGUE_BARGAIN_INFO_RST, rst.build(), time);
    }

    /**
     * 帮派砍一刀砍价
     */
    @Handler(PtCode.LEAGUE_BARGAIN_CUT_REQ)
    private void leagueBargainCutReq(Player player, PbProtocol.LeagueBargainCutReq req, long time){
        PbProtocol.LeagueBargainCutRst.Builder rst = PbProtocol.LeagueBargainCutRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            int text = commonCheck(player);
            if(text != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(text));
                break logic;
            }

            PlayerLeagueModel leagueModel = player.getModel(PlayerModelEnums.league);
            if(leagueModel.isHadBuy()){
                rst.setResult(Text.genServerRstInfo(Text.折扣商品已经购买));
                break logic;
            }
            if(leagueModel.isHadCut()){
                rst.setResult(Text.genServerRstInfo(Text.折扣商品已经砍价));
                break logic;
            }

            League league = LeagueManager.getLeagueByPlayer(player);
            LeagueBargainModel model = league.getModel(LeagueModelEnums.bargain);

            if(model.getBuyMap().containsKey(player.getPlayerId())){
                rst.setResult(Text.genServerRstInfo(Text.折扣商品已经购买));
                break logic;
            }

            if(model.getCutPlayerIdList().contains(player.getPlayerId())){
                rst.setResult(Text.genServerRstInfo(Text.折扣商品已经砍价));
                break logic;
            }

            leagueModel.setHadCut(true);
            int cutAmount = model.countCutAmountAndDeal(player);
            rst.setCutAmount(cutAmount);

            RedDot.leagueBargainFreeTime.sync(player);
        }
        player.send(PtCode.LEAGUE_BARGAIN_CUT_RST, rst.build(), time);
    }

    /**
     * 帮派砍一刀购买
     */
    @Handler(PtCode.LEAGUE_BARGAIN_BUY_REQ)
    private void leagueBargainBuyReq(Player player, PbProtocol.LeagueBargainBuyReq req, long time){
        PbProtocol.LeagueBargainBuyRst.Builder rst = PbProtocol.LeagueBargainBuyRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            int text = commonCheck(player);
            if(text != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(text));
                break logic;
            }

            PlayerLeagueModel leagueModel = player.getModel(PlayerModelEnums.league);
            if(leagueModel.isHadBuy()){
                rst.setResult(Text.genServerRstInfo(Text.折扣商品已经购买));
                break logic;
            }

            League league = LeagueManager.getLeagueByPlayer(player);
            LeagueBargainModel model = league.getModel(LeagueModelEnums.bargain);

            if(model.getBuyMap().containsKey(player.getPlayerId())){
                rst.setResult(Text.genServerRstInfo(Text.折扣商品已经购买));
                break logic;
            }

            BargainItemTemplate template = itemMap.get(model.getId());

            Reward cost = template.cost.createReward();
            int cutAmount = model.getCutAmount();
            cost.setValue(cost.getValue() - cutAmount);

            if(cost.getValue() < 0){
                model.buyAndDeal(player, cost, leagueModel);

                Reward copy = cost.copy();
                copy.setValue(-1 * cost.getValue());
                copy.add(player, BehaviorType.leagueBargain);
            }else {
                if(cost.check(player) != -1){
                    rst.setResult(Text.genServerRstInfo(Text.消耗不足));
                    break logic;
                }

                model.buyAndDeal(player, cost, leagueModel);
                cost.remove(player, BehaviorType.leagueBargain);
            }

            List<Reward> rewardList = Reward.templateCollectionToReward(template.item);
            Reward.add(rewardList, player, BehaviorType.leagueBargain);

            rst.addAllRewards(Reward.writeCollectionToPb(rewardList));
        }
        player.send(PtCode.LEAGUE_BARGAIN_BUY_RST, rst.build(), time);
    }

    @Nullable
    private static int commonCheck(Player player) {
        //检查帮派限制
        League league = LeagueManager.getLeagueByPlayer(player);
        if(Objects.isNull(league)){
            return Text.未加入帮派;
        }

        //检查帮派限制
        if(Function.leagueBargain.isNotOpen(player)){
            return Text.功能未开启;
        }

        if(LeagueManager.canNotPlayLeague(player)){
            return Text.您退出帮派未满足24小时无法参与帮派活动;
        }

        if(LeagueManager.canNotPlayLeague(player)){
            return Text.您退出帮派未满足24小时无法参与帮派活动;
        }
        return Text.没有异常;
    }

    /**
     *
     * @return BargainItemTemplate
     */
    public static BargainItemTemplate getBargainItemTemplate(){
        long openDay = World.getOpenServerDay();
        int dayOfWeek = ServerConstants.getCurrentTimeLocalDateTime().getDayOfWeek().getValue();

        //读表时已核对
        for (BargainItemTemplate bean : itemMap.values()){
            if((openDay <= 7 && openDay == bean.shelfTimeInitial) || (openDay > 7 && dayOfWeek == bean.shelfTimeWeek)){
                return bean;
            }
        }
        return null;
    }

    public static Map<Integer, BargainItemTemplate> getItemMap() {
        return itemMap;
    }

    public static Map<Integer, Map<Integer, BargainRuleTemplate>> getRuleMap() {
        return ruleMap;
    }

    public static Map<Integer, Map<Integer, BargainRuleMinusTemplate>> getRuleMinusMap() {
        return ruleMinusMap;
    }

}

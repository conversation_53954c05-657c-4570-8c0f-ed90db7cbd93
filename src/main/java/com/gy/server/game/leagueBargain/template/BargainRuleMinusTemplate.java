package com.gy.server.game.leagueBargain.template;

import com.gy.server.game.util.StringExtUtil;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Map;

/**
 * 负价砍价表
 *
 * <AUTHOR> 2024/11/12 14:32
 **/
public class BargainRuleMinusTemplate {

    public int id;

    /**
     * 商品ID
     */
    public int bargainItem;

    /**
     * 参与人数
     */
    public int bargainAmount;

    /**
     * 负价格砍价比例
     */
    public Pair<Integer, Integer> bargainScaleMinus;

    public BargainRuleMinusTemplate(Map<String, String> map) {
        this.id = Integer.parseInt(map.get("id"));
        this.bargainItem = Integer.parseInt(map.get("BargainItem"));
        this.bargainAmount = Integer.parseInt(map.get("BargainAmount"));
        this.bargainScaleMinus = StringExtUtil.string2Pair(map.get("BargainScaleMinus"), ",", Integer.class, Integer.class);
    }
}

package com.gy.server.game.leagueBargain.template;

import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.util.StringExtUtil;
import org.apache.commons.lang3.tuple.Pair;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 砍价商品信息
 *
 * <AUTHOR> 2024/11/11 11:53
 **/
public class BargainItemTemplate {

    public int id;

    public List<RewardTemplate> item = new ArrayList<>();

    /**
     * 上架时间-周几
     */
    public int shelfTimeWeek;

    /**
     * 上架时间-开服天数
     */
    public int shelfTimeInitial;

    /**
     * 消耗货币
     */
    public RewardTemplate cost;

    /**
     * 是否可以触发负价格
     */
    public boolean isMinus;

    /**
     * 保底负数价格一刀范围 千分比
     */
    public Pair<Integer, Integer> minusPrice;

    public BargainItemTemplate(Map<String, String> map){
        this.id = Integer.parseInt(map.get("id"));
        this.item = RewardTemplate.readListFromText(map.get("item"));
        this.shelfTimeWeek = Integer.parseInt(map.get("shelfTimeWeek"));
        this.shelfTimeInitial = Integer.parseInt(map.get("shelfTimeInitial"));
        this.cost = RewardTemplate.readListFromText(map.get("Cost")).get(0);
        this.isMinus = map.get("isMinus").equals("1");
        this.minusPrice = StringExtUtil.string2Pair(map.get("minusPrice"), ",", Integer.class, Integer.class);
    }

}

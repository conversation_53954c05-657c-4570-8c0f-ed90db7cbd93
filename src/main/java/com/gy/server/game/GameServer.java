package com.gy.server.game;

import com.gy.server.common.NodeStatusSyncRunner;
import com.gy.server.common.distributedlock.DistributedLockUtilManager;
import com.gy.server.common.es.EsUtil;
import com.gy.server.common.gateway.Game2GateStatusSyncManager;
import com.gy.server.common.gateway.GateNode;
import com.gy.server.common.gateway.GateNodeManager;
import com.gy.server.common.redis.RedisScript;
import com.gy.server.common.util.CommonUtils;
import com.gy.server.common.util.ServerWaitHelper;
import com.gy.server.core.*;
import com.gy.server.core.battleRule.enums.BattleEnums;
import com.gy.server.core.delay.DelayTaskManager;
import com.gy.server.core.delay.MessageSystemHashInvoke;
import com.gy.server.core.delay.MessageSystemSyncInvoke;
import com.gy.server.core.log.CommonLogger;
import com.gy.server.core.log.zipkin.ZipkinHelper;
import com.gy.server.core.thread.ForkJoinThreadPool;
import com.gy.server.core.thread.SimpleHashedThreadPool;
import com.gy.server.core.thread.SimpleRandomThreadPool;
import com.gy.server.db.nosql.redis.statistics.RedisUtilStatisticsManager;
import com.gy.server.game.account.login.LoginQueueManger;
import com.gy.server.game.account.login.check.LoginCheckManager;
import com.gy.server.game.account.register.check.RegisterCheckManager;
import com.gy.server.game.activity.silk.SilkRoadRankSyncRunner;
import com.gy.server.game.arena.ArenaHelper;
import com.gy.server.game.arenaHigh.ArenaHighHelper;
import com.gy.server.game.combat.CombatManager;
import com.gy.server.game.common.GameCommonHelper;
import com.gy.server.game.db.DbManager;
import com.gy.server.game.escort.EscortRankManager;
import com.gy.server.game.event.ServerEventManager;
import com.gy.server.game.fix.designer.world.FixDesignerGlobalData;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.handler.HandlerManager;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.leagueDuel.LeagueDuelManager;
import com.gy.server.game.leagueEscort.LeagueEscortManager;
import com.gy.server.game.leagueTradingCompany.TradingCompanyManger;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.mail.MailManager;
import com.gy.server.game.mail.cycle.GmCycleMailNoticeRunner;
import com.gy.server.game.mail.global.GmGlobalMailNoticeRunner;
import com.gy.server.game.pay.PayCheckManager;
import com.gy.server.game.player.BaseInfoSyncManager;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.player.PlayerSaveManager;
import com.gy.server.game.rank.RankManager;
import com.gy.server.game.record.RecordDeleteManager;
import com.gy.server.game.record.RecordHelper;
import com.gy.server.game.redBear.RedBearManager;
import com.gy.server.game.redBearRevenge.RedBearRevengeManager;
import com.gy.server.game.role.image.HeadChangeRunner;
import com.gy.server.game.room.RoomManager;
import com.gy.server.game.secretRealm.SecretRealmManager;
import com.gy.server.game.service.ServiceManager;
import com.gy.server.game.silentUpdate.SilentUpdateManager;
import com.gy.server.game.smallGroup.SmallGroupManger;
import com.gy.server.game.speak.SpeakMessageManager;
import com.gy.server.game.statistics.PlayerOperationCountManager;
import com.gy.server.game.team.TeamCheckManager;
import com.gy.server.game.team.TeamManager;
import com.gy.server.game.thiefInvasion.LeagueThiefInvasionManager;
import com.gy.server.game.time.TimeManager;
import com.gy.server.game.tournament.TournamentManager;
import com.gy.server.game.voice.VoiceCheckManager;
import com.gy.server.game.warZone.WarZoneHelper;
import com.gy.server.game.warZone.WarZoneManager;
import com.gy.server.game.world.WorldServerManager;
import com.gy.server.game.worldBoss.WorldBossCycleManager;
import com.gy.server.net.netty.ChannelHandlerContextFlusher;
import com.gy.server.net.netty.NettyTcpServer;
import com.gy.server.net.netty.codec.PacketCodecFactory;
import com.gy.server.packet.PbPacket;
import com.gy.server.utils.runner.RunnerManager;
import com.gy.server.utils.structure.TickerWatcher;
import com.gy.server.utils.time.DateTimeFormatterType;
import com.gy.server.utils.time.DateTimeUtil;
import com.gy.server.world.activityTeam.ActivityTeamManager;
import com.gy.server.world.live.LiveRoomManager;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.serialize.server.GameServerState;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.Objects;


/**
 * <AUTHOR> - [Created on 2018/1/26 16:03]
 */
public class GameServer extends MainThread {

    private NettyTcpServer tcpServer;

    @Override
    protected void init() throws Exception {

        //TLBase初始化
        TLBase.getInstance().init(Configuration.serverId, ServerType.GAME, "game", Configuration.startupMode.name(), MessageSystemSyncInvoke.getInstance(), MessageSystemHashInvoke.getInstance());
        Configuration.setRunMode();
        Configuration.initConfig();

        //game 需要提前初始化一下billing，用于检查开启状态
        Configuration.initBillingUrl();

        Launcher.launchStartupInfo("TLBase init");
        //延迟开服检测
        waitStartup();

        Launcher.launchStartupInfo("GameServer is initializing");

        //服务器常量
        Launcher.launchStartupInfo("ServerConstants init");
        ServerConstants.init();

        //db相关初始化
        Launcher.launchStartupInfo("DbManager init");
        DbManager.init();


        //PlayerManager
        Launcher.launchStartupInfo("PlayerManager init");
        PlayerManager.init();

        RecordHelper.getInstance().init();

        //快速tick
        Launcher.launchStartupInfo("add fast ticker");
        addFastTicker(GateNodeManager.getInstance());
        addFastTicker(PlayerManager.fastTicker);
        addFastTicker(CombatManager.getInstance());

        //慢速tick
        Launcher.launchStartupInfo("add slow ticker");
        addSlowTicker(ServerEventManager.getInstance());
        addSlowTicker(RunnerManager.getInstance());
        addSlowTicker(DelayTaskManager.getInstance());
        addSlowTicker(PlayerManager.slowTicker);
        addSlowTicker(TimeManager.getInstance());
        addSlowTicker(ElapsedTimeStatistics.getInstance());
        addSlowTicker(GlobalDataManager.getInstance());
        addSlowTicker(LoginQueueManger.getInstance());
        addSlowTicker(TeamManager.getInstance());
        addSlowTicker(LeagueManager.getInstance());
        addSlowTicker(LeagueDuelManager.getInstance());
        addSlowTicker(RedBearManager.getInstance());
        addSlowTicker(LeagueThiefInvasionManager.getInstance());
        addSlowTicker(RedBearRevengeManager.getInstance());
        addSlowTicker(LeagueEscortManager.getInstance());
        addSlowTicker(WorldServerManager.getInstance());
        addSlowTicker(LiveRoomManager.getInstance());
        addSlowTicker(ActivityTeamManager.getInstance());
        addSlowTicker(PayCheckManager.getInstance());
        addSlowTicker(VoiceCheckManager.getInstance());

        //注册service
        Launcher.launchStartupInfo("ServiceManager init");
        ServiceManager.init();

        // 协议号管理器
        Launcher.launchStartupInfo("HandlerManager init");
        HandlerManager.getInstance().init();

        //RedisLua注册
        RedisScript.init();

        //加载静默更新数据
        SilentUpdateManager.init();

        Launcher.launchStartupInfo("RedisUtilStatisticsManager init");
        RedisUtilStatisticsManager.getInstance().init();

        Launcher.launchStartupInfo("SceneCheckManager init");
//        SceneCheckManager.getInstance().initMainSceneId();

        Launcher.launchStartupInfo("ZipkinHelper init");
        ZipkinHelper.initTracing();

        Launcher.launchStartupInfo("GameCommonHelper init");
        GameCommonHelper.init();

        Launcher.launchStartupInfo("TickerWatcher init");
        TickerWatcher.getInstance().set(5L * DateTimeUtil.MillisOfSecond
                , 1L * DateTimeUtil.MillisOfSecond
                , () -> {
                    GameLogger.error("main thread is delay, current thread stack trace:");
                    CommonUtils.printMailThreadStackTrace();
//                    if (!Configuration.runMode.isPress()){
//                    }
                });

        Launcher.launchStartupInfo("GameServer is initialized");
    }

    @Override
    protected void startup() throws Exception {
        Launcher.launchStartupInfo("GameServer is starting");

        RunnerManager.addRunner(ServerWaitHelper.getInstance(), ServerWaitHelper.getInstance().getRunnerName(), false);

        //service前置启动
        Launcher.launchStartupInfo("ServiceManager pre startup");
        ServiceManager.preStartup();

        DistributedLockUtilManager.startup();

        //全局数据管理器启动
        Launcher.launchStartupInfo("GlobalDataManager startup");
        GlobalDataManager.startup();

        //战区初始化
        WarZoneHelper.init();

        //service启动
        Launcher.launchStartupInfo("ServiceManager startup");
        ServiceManager.startup();

        Launcher.launchStartupInfo("ES startup");
        EsUtil.start();

        //Player存储管理器启动
        PlayerSaveManager.startup();

        //RankManager
        Launcher.launchStartupInfo("load Rank");
        RankManager.loadRank();
        //LeagueManager启动
        Launcher.launchStartupInfo("LeagueManager init");
        LeagueManager.startup();
        // 启动初始化机器人并加入排行榜
        ArenaHelper.initRobot();
        //高阶竞技场初始化所有机器人
        ArenaHighHelper.init();
        //前补锅处理
        FixDesignerGlobalData fixDesignerModel = GlobalDataManager.getData(GlobalDataType.FixDesigner);
        fixDesignerModel.checkPreFix();


        //PlayerManager启动
        Launcher.launchStartupInfo("PlayerManager startup");
        PlayerManager.startup();

        //时间管理器启动
        Launcher.launchStartupInfo("TimeManager startup");
        TimeManager.startup();

        //后补锅处理
        fixDesignerModel.checkFix();

        // 基本信息同步器启动
        Launcher.launchStartupInfo("BaseInfoSyncManager startup");
        BaseInfoSyncManager.startup();

        Launcher.launchStartupInfo("HashedThreadPool startup");
        SimpleHashedThreadPool.getInstance().startup();
        SimpleRandomThreadPool.getInstance().startup();

        Launcher.launchStartupInfo("ActivityTeamManager startup");
        ActivityTeamManager.getInstance().start();

        //语音检测服务器开启
        VoiceCheckManager.getInstance().start();

        GateNode.startUp();

        //独立线程启动
        RunnerManager.addRunner(ForkJoinThreadPool.getInstance(), ForkJoinThreadPool.getInstance().getRunnerName(), false);
        RunnerManager.addRunner(ChannelHandlerContextFlusher.getInstance(), "ChannelHandlerContextFlusher", false);
        RunnerManager.addRunner(LoginCheckManager.getInstance(), LoginCheckManager.getInstance().getRunnerName(), false);
        RunnerManager.addRunner(RegisterCheckManager.getInstance(), RegisterCheckManager.getInstance().getRunnerName(), false);
        RunnerManager.addRunner(MailManager.getInstance(), MailManager.getInstance().getRunnerName(), false);
        RunnerManager.addRunner(RankManager.getInstance(), RankManager.getInstance().getRunnerName(), false);
        RunnerManager.addRunner(TickerWatcher.getInstance(), "TickerWatcher", true);
        RunnerManager.addRunner(PlayerManager.getInstance(), PlayerManager.getInstance().getRunnerName(), true);
        RunnerManager.addRunner(PayCheckManager.getInstance(), PayCheckManager.getInstance().getRunnerName(), true);
//        RunnerManager.addRunner(AppStorePayCallbackRunner.getInstance(), AppStorePayCallbackRunner.getInstance().getRunnerName(), true);
        RunnerManager.addRunner(GmGlobalMailNoticeRunner.getInstance(), GmGlobalMailNoticeRunner.getInstance().getRunnerName(), false);
        RunnerManager.addRunner(GmCycleMailNoticeRunner.getInstance(), GmCycleMailNoticeRunner.getInstance().getRunnerName(), false);
        RunnerManager.addRunner(LeagueManager.getInstance(), LeagueManager.getInstance().getRunnerName(), false);
        RunnerManager.addRunner(PlayerOperationCountManager.getInstance(), PlayerOperationCountManager.getInstance().getRunnerName(), false);
        RunnerManager.addRunner(RecordDeleteManager.getInstance(), RecordDeleteManager.getInstance().getRunnerName(), false);
        RunnerManager.addRunner(BaseInfoSyncManager.getInstance(), BaseInfoSyncManager.getInstance().getRunnerName(), false);
        RunnerManager.addRunner(Game2GateStatusSyncManager.getInstance(), Game2GateStatusSyncManager.getInstance().getRunnerName(), false);
        RunnerManager.addRunner(LoginQueueManger.getInstance(), LoginQueueManger.getInstance().getRunnerName(), false);
        RunnerManager.addRunner(SpeakMessageManager.getInstance(), SpeakMessageManager.getInstance().getRunnerName(), false);
//        RunnerManager.addRunner(SceneCheckManager.getInstance(), SceneCheckManager.getInstance().getRunnerName(), false);
        RunnerManager.addRunner(TeamCheckManager.getInstance(), TeamCheckManager.getInstance().getRunnerName(), false);
        RunnerManager.addRunner(HeadChangeRunner.getInstance(), HeadChangeRunner.getInstance().getRunnerName(), false);
        RunnerManager.addRunner(SecretRealmManager.getInstance(), SecretRealmManager.getInstance().getRunnerName(), false);
        RunnerManager.addRunner(EscortRankManager.getInstance(), EscortRankManager.getInstance().getRunnerName(), false);
        RunnerManager.addRunner(RoomManager.getInstance(), RoomManager.getInstance().getRunnerName(), false);
        RunnerManager.addRunner(WorldBossCycleManager.getInstance(), WorldBossCycleManager.getInstance().getRunnerName(), false);
        RunnerManager.addRunner(TournamentManager.getInstance(), TournamentManager.getInstance().getRunnerName(), false);
        RunnerManager.addRunner(WorldServerManager.getInstance(), WorldServerManager.getInstance().getRunnerName(), false);
        RunnerManager.addRunner(LiveRoomManager.getInstance(), LiveRoomManager.class.getSimpleName(), false);
        RunnerManager.addRunner(SmallGroupManger.getInstance(), SmallGroupManger.getInstance().getRunnerName(), false);
        RunnerManager.addRunner(ActivityTeamManager.getInstance(), ActivityTeamManager.class.getSimpleName(), false);
        RunnerManager.addRunner(SilkRoadRankSyncRunner.getInstance(), SilkRoadRankSyncRunner.class.getSimpleName(), false);
        RunnerManager.addRunner(RedisUtilStatisticsManager.getInstance(), RedisUtilStatisticsManager.class.getSimpleName(), false);
        RunnerManager.addRunner(new NodeStatusSyncRunner(), NodeStatusSyncRunner.class.getSimpleName(), false);
        RunnerManager.addRunner(new GameServerStateUpdater(), GameServerStateUpdater.class.getSimpleName(), false);
        RunnerManager.addRunner(new TradingCompanyManger(), TradingCompanyManger.class.getSimpleName(), false);
        RunnerManager.addRunner(WarZoneManager.getInstance(), WarZoneManager.getInstance().getRunnerName(), false);

        //状态机启动
        BattleEnums.start();

        //线程启动
        Launcher.launchStartupInfo("MainThread start");
        this.start();

        Launcher.launchStartupInfo("WarZoneManager init.....");
        WarZoneManager.getInstance().init();

        Launcher.launchStartupInfo("wait ServerWaitHelper.....");
        ServerWaitHelper.startWait();

        //游客接待室打开
        Launcher.launchStartupInfo("GuestManager start");
        GateNodeManager.getInstance().setAccept(true);

        //启动tcp服务
        tcpServer = NettyTcpServer.startServer(Configuration.tcpPort, PbPacket.SSPacket.getDefaultInstance(), GateNodeManager.getInstance(), PacketCodecFactory.common);
        Launcher.launchStartupInfo("TCP Server startup, listen port:" + Configuration.tcpPort);

        Launcher.launchStartupInfo("GameServer is started");

        //输出启动成功：http://patorjk.com/software/taag，当前使用字体：Doom
        System.out.println(" _____                                  _____  _                 _              _   _ \n" +
                "/  ___|                                /  ___|| |               | |            | | | |\n" +
                "\\ `--.   ___  _ __ __   __  ___  _ __  \\ `--. | |_   __ _  _ __ | |_   ___   __| | | |\n" +
                " `--. \\ / _ \\| '__|\\ \\ / / / _ \\| '__|  `--. \\| __| / _` || '__|| __| / _ \\ / _` | | |\n" +
                "/\\__/ /|  __/| |    \\ V / |  __/| |    /\\__/ /| |_ | (_| || |   | |_ |  __/| (_| | |_|\n" +
                "\\____/  \\___||_|     \\_/   \\___||_|    \\____/  \\__| \\__,_||_|    \\__| \\___| \\__,_| (_)\n" +
                "                                                                                      \n" +
                "                                                                                      ");

    }

    @Override
    protected void shutdown() throws Exception {
        Launcher.launchShutdownInfo("GameServer start shutdown");
        running = false;

        while (true) {
            if (shutdown
                    && ThreadPool.getInstance().getSize() < 1
                    // 保证之前的Player数据全部入库
                    && !PlayerSaveManager.isRunning()) {

                //游客接待室关闭
                GateNodeManager.getInstance().setAccept(false);
                Launcher.launchShutdownInfo("GuestManager shutdown");

                //rpc预关闭
                TLBase.getInstance().preShutdown();
                Launcher.launchShutdownInfo("TLBase preShutdown");

                //战斗管理器关闭
                CombatManager.shutdown();
                Launcher.launchShutdownInfo("CombatManager shutdown");

                //service关闭
                ServiceManager.shutdown();
                Launcher.launchShutdownInfo("ServiceManager shutdown");

                //全局数据管理器关闭
                GlobalDataManager.shutdown();
                Launcher.launchShutdownInfo("GlobalDataManager shutdown");

                //帮派数据存储
                LeagueManager.shutdown();
                Launcher.launchShutdownInfo("LeagueManager shutdown");

                //组队数据存储
                ActivityTeamManager.getInstance().shutdown();
                Launcher.launchShutdownInfo("ActivityTeamManager shutdown");

                //独立线程关闭
                RunnerManager.shutdown();
                Launcher.launchShutdownInfo("RunnerManager shutdown");

                TLBase.getInstance().shutdown();
                Launcher.launchShutdownInfo("TLBase shutdown");

                //HashedThreadPool关闭
                SimpleHashedThreadPool.getInstance().shutdown();
                Launcher.launchShutdownInfo("HashedThreadPool shutdown");
                SimpleRandomThreadPool.getInstance().shutdown();
                Launcher.launchShutdownInfo("SimpleRandomThreadPool shutdown");

                //排行榜数据存储
                RankManager.update();
                Launcher.launchShutdownInfo("RankManager shutdown");


                //tcp服务关闭
                if(Objects.nonNull(tcpServer)){
                    tcpServer.shutdown();
                }
                Launcher.launchShutdownInfo("TcpServer shutdown");

                //db关闭
                DbManager.shutdown();
                Launcher.launchShutdownInfo("DB shutdown");

                break;
            }

            Thread.sleep(100);
        }

        Launcher.launchShutdownInfo("GameServer end shutdown");
        System.out.println(" _____                                  _____  _                            _   _ \n" +
                "/  ___|                                /  ___|| |                          | | | |\n" +
                "\\ `--.   ___  _ __ __   __  ___  _ __  \\ `--. | |_   ___   _ __    ___   __| | | |\n" +
                " `--. \\ / _ \\| '__|\\ \\ / / / _ \\| '__|  `--. \\| __| / _ \\ | '_ \\  / _ \\ / _` | | |\n" +
                "/\\__/ /|  __/| |    \\ V / |  __/| |    /\\__/ /| |_ | (_) || |_) ||  __/| (_| | |_|\n" +
                "\\____/  \\___||_|     \\_/   \\___||_|    \\____/  \\__| \\___/ | .__/  \\___| \\__,_| (_)\n" +
                "                                                          | |                     \n" +
                "                                                          |_|                     ");
    }

    /**
     * 等待启动，向gate请求服务器开启时间标记，比较当前时间，直到当前时间晚于开启时间，方法结束
     */
    private void waitStartup() {
        while (this.checkNeedWaitStartup()) {
            try {
                Thread.sleep(10000);
            } catch (Exception e) {
                CommonLogger.error(e);
            }
        }
    }

    /**
     * 检查是否需要等待开始，true表示需要继续等待，false表示无需再等待
     */
    private boolean checkNeedWaitStartup() {
        try {
            GameServerStateUpdater.instance.runnerExecute();

            GameServerState state = GameServerStateUpdater.instance.getState();
            long openTime = state.getOpenTime();

            //没有配置服务器信息
            if (state == null || state.getServerNumbers().size() == 0) {
                CommonLogger.info(String.format("gmt 后台没有配置服务器信息，serverId = %s", Configuration.serverId));
                return true;
            }
            if (openTime > ServerConstants.getCurrentTimeMillis()) {
                CommonLogger.info(String.format("服务器将于 %s 开启", DateTimeUtil.toString(DateTimeUtil.toLocalDateTime(openTime), DateTimeFormatterType.date_time)));
                // 等待开服状态
                return true;
            } else {
                // 正常启动状态
                return false;
            }
        } catch (Exception e) {
            CommonLogger.error(e);
        }

        //异常状态，等待下次检查
        return true;
    }

}

package com.gy.server.game.artifactDrawing.template;

import com.gy.server.game.drop.RewardTemplate;

import java.util.List;
import java.util.Map;

/**
 * 神器图纸模版
 *
 * <AUTHOR> 2025/2/25 11:32
 **/
public class ArtifactDrawingTemplate {

    /**
     * 图纸id
     */
    public int id;

    /**
     * 解锁奖励
     */
    public List<RewardTemplate> rewardTemplateList;

    /**
     * 解锁条件
     */
    public int goal;

    public ArtifactDrawingTemplate(Map<String, String> map){
        this.id = Integer.parseInt(map.get("id"));
        this.rewardTemplateList = RewardTemplate.readListFromText(map.get("reward"));
        this.goal = Integer.parseInt(map.get("goal"));
    }

}

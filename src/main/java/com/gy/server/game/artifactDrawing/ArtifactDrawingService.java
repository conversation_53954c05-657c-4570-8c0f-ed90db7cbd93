package com.gy.server.game.artifactDrawing;

import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.artifactDrawing.template.ArtifactDrawingTemplate;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.service.Service;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 神奇图纸服务类
 *
 * <AUTHOR> 2025/2/25 11:38
 **/
public class ArtifactDrawingService extends PlayerPacketHandler implements Service {

    /**
     * 神奇图纸模板map
     * key:id
     */
    private static Map<Integer, ArtifactDrawingTemplate> artifactDrawingTemplateMap = new HashMap<>();

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.artifactdrawing_artifactdrawing);
        Map<Integer, ArtifactDrawingTemplate> artifactDrawingTemplateMapTemp = new HashMap<>();
        for (Map<String, String> bean : mapList) {
            ArtifactDrawingTemplate template = new ArtifactDrawingTemplate(bean);
            artifactDrawingTemplateMapTemp.put(template.id, template);
        }
        artifactDrawingTemplateMap = artifactDrawingTemplateMapTemp;
    }

    /**
     * 神器碎片信息
     */
    @Handler(PtCode.ARTIFACT_DRAWING_INFO_REQ)
    private void artifactDrawingInfoReq(Player player, PbProtocol.ArtifactDrawingInfoReq req, long time){
        PbProtocol.ArtifactDrawingInfoRst.Builder rst = PbProtocol.ArtifactDrawingInfoRst.newBuilder().setResult(Text.genOkServerRstInfo());
        rst.setModel(player.getArtifactDrawingModel().genPb());
        player.send(PtCode.ARTIFACT_DRAWING_INFO_RST, rst.build(), time);
    }

    /**
     * 神器碎片领奖
     */
    @Handler(PtCode.ARTIFACT_DRAWING_RECEIVE_REQ)
    public void artifactDrawingReceiveReq(Player player, PbProtocol.ArtifactDrawingReceiveReq req, long time){
        PbProtocol.ArtifactDrawingReceiveRst.Builder rst = PbProtocol.ArtifactDrawingReceiveRst.newBuilder().setResult(Text.genOkServerRstInfo());
        int id = req.getId();

        logic:{
            ArtifactDrawingTemplate artifactDrawingTemplate = getArtifactDrawingTemplateMap().get(id);
            if(artifactDrawingTemplate == null){
                rst.setResult(Text.genServerRstInfo(Text.神器碎片参数错误));
                break logic;
            }

            PlayerArtifactDrawingModel model = player.getArtifactDrawingModel();
            if(model.getReceiveIdSet().contains(id)){
                rst.setResult(Text.genServerRstInfo(Text.奖励已领取));
                break logic;
            }

            if(!model.getIdSet().contains(id)){
                rst.setResult(Text.genServerRstInfo(Text.神器碎片未解锁));
                break logic;
            }

            model.receiveReward(id);
            List<Reward> rewardList = Reward.templateCollectionToReward(artifactDrawingTemplate.rewardTemplateList);
            Reward.add(rewardList, player, BehaviorType.artifactDrawingReward);
            rst.addAllReward(Reward.writeCollectionToPb(rewardList));
        }

        player.send(PtCode.ARTIFACT_DRAWING_RECEIVE_RST, rst.build(), time);
    }

    @Override
    public void clearConfigData() {
        artifactDrawingTemplateMap.clear();
    }

    public static Map<Integer, ArtifactDrawingTemplate> getArtifactDrawingTemplateMap() {
        return artifactDrawingTemplateMap;
    }
}

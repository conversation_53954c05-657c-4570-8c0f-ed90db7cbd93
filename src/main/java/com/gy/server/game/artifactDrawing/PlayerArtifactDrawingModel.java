package com.gy.server.game.artifactDrawing;

import com.gy.server.game.artifactDrawing.template.ArtifactDrawingTemplate;
import com.gy.server.game.constant.ConstantService;
import com.gy.server.game.constant.ConstantType;
import com.gy.server.game.function.Function;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModel;
import com.gy.server.game.player.event.PlayerEvent;
import com.gy.server.game.player.event.PlayerEventHandler;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.packet.PbArtifactDrawing;
import com.ttlike.server.tl.baselib.serialize.artifactDrawing.PlayerArtifactDrawingModelDb;
import com.ttlike.server.tl.baselib.serialize.player.PlayerBlobDb;

import java.util.HashSet;
import java.util.Set;

/**
 * 神器碎片模块
 *
 * <AUTHOR> 2025/2/25 11:43
 **/
public class PlayerArtifactDrawingModel extends PlayerModel implements PlayerEventHandler{

    /**
     * 已获得图纸id集合
     */
    private Set<Integer> idSet = new HashSet<>();

    /**
     * 已经领奖的图纸id集合
     */
    private Set<Integer> receiveIdSet = new HashSet<>();

    /**
     * 是否展示
     * 领完奖励且完成对话任务后不再展示
     */
    private boolean isShow;

    public PlayerArtifactDrawingModel(Player player) {
        super(player);
    }

    @Override
    protected void loadData(PlayerBlobDb playerBlob) {
        PlayerArtifactDrawingModelDb db = playerBlob.getArtifactDrawingModelDb();
        if(db != null){
            this.idSet.addAll(db.getIdList());
            this.receiveIdSet.addAll(db.getReceiveIdList());
            this.isShow = db.isShow();
        }
    }

    @Override
    protected void saveData(PlayerBlobDb playerBlob) {
        PlayerArtifactDrawingModelDb db = new PlayerArtifactDrawingModelDb();
        db.getIdList().addAll(idSet);
        db.getReceiveIdList().addAll(receiveIdSet);
        db.setShow(isShow);
        playerBlob.setArtifactDrawingModelDb(db);
    }

    @Override
    public PlayerEventType[] getEventTypes() {
        return new PlayerEventType[]{PlayerEventType.goalFinish, PlayerEventType.functionCheck};
    }

    @Override
    public void handle(PlayerEvent event) {
        switch (event.getEventType()){
            case goalFinish:{
                int goldId = event.getParam(0);
                for (ArtifactDrawingTemplate bean : ArtifactDrawingService.getArtifactDrawingTemplateMap().values()) {
                    if(bean.goal == goldId && !receiveIdSet.contains(goldId)){
                        //增加图纸
                        idSet.add(bean.id);
                        //同步
                        getPlayer().dataSyncModule.syncArtifactDrawing();
                        //红点
                    }
                }

                if(ArtifactDrawingService.getArtifactDrawingTemplateMap().size() == receiveIdSet.size()){
                    checkFinish(true);
                }
                break;
            }
            case functionCheck:{
                if(Function.artifactPreheat.isOpen(getPlayer())){
                    if(!isShow && idSet.isEmpty() && receiveIdSet.isEmpty()){
                        //注册目标
                        for (ArtifactDrawingTemplate bean : ArtifactDrawingService.getArtifactDrawingTemplateMap().values()) {
                            getPlayer().getGoalModel().register(bean.goal);
                        }

                        //注册结束目标
                        int endGoalId = ConstantService.getInt(ConstantType.神器预热活动结束条件);
                        getPlayer().getGoalModel().register(endGoalId);
                        isShow = true;

                        getPlayer().dataSyncModule.syncArtifactDrawing();
                    }
                }
            }
        }
    }

    public void receiveReward(int id){
        receiveIdSet.add(id);

        if(ArtifactDrawingService.getArtifactDrawingTemplateMap().size() == receiveIdSet.size()){
            checkFinish(false);
        }

        //同步
        getPlayer().dataSyncModule.syncArtifactDrawing();
        //红点
    }

    public PbArtifactDrawing.ArtifactDrawingModel genPb(){
        PbArtifactDrawing.ArtifactDrawingModel.Builder builder = PbArtifactDrawing.ArtifactDrawingModel.newBuilder();
        builder.addAllId(idSet);
        builder.addAllReceiveId(receiveIdSet);
        builder.setIsShow(isShow);
        return builder.build();
    }

    public void checkFinish(boolean sync){
        //检查结束目标是否完成，碎片奖励是否领完
        int endGoalId = ConstantService.getInt(ConstantType.神器预热活动结束条件);
        if(getPlayer().getGoalModel().isFinish(endGoalId)){
            isShow = false;

            if(sync){
                //同步
                getPlayer().dataSyncModule.syncArtifactDrawing();
            }
            Function.artifact.syncFunction(getPlayer());
        }
    }

    public Set<Integer> getIdSet() {
        return idSet;
    }

    public void setIdSet(Set<Integer> idSet) {
        this.idSet = idSet;
    }

    public Set<Integer> getReceiveIdSet() {
        return receiveIdSet;
    }

    public void setReceiveIdSet(Set<Integer> receiveIdSet) {
        this.receiveIdSet = receiveIdSet;
    }

    public boolean isShow() {
        return isShow;
    }

    public void setShow(boolean show) {
        isShow = show;
    }
}

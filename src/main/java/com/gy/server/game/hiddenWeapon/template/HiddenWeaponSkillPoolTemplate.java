package com.gy.server.game.hiddenWeapon.template;

import java.util.*;
import com.gy.server.common.base.AbsTemplate;
import com.gy.server.utils.StringUtil;

public class HiddenWeaponSkillPoolTemplate implements AbsTemplate {

    public int id; // ID
    public int skillId; // 技能ID_等级
    public int skillLevel; // 技能ID_等级
    public int group; // 组别（用于判断互斥）
    public int quality; // 品质 
    public int weight; // 权重 

    @Override
    public void readTxt(Map<String, String> map) {
        id = Integer.parseInt(map.get("id"));
        int[] ints = StringUtil.splitToIntArray(map.get("skill"), "_");
        skillId = ints[0];
        skillLevel = ints[1];
        group = Integer.parseInt(map.get("group"));
        quality = Integer.parseInt(map.get("quality"));
        weight = Integer.parseInt(map.get("weight"));
    }

    @Override
    public String toString() {
        return "HiddenWeaponSkillPoolTemplate{"
                + "id=" + id
                + "skillId=" + skillId
                + "skillLevel=" + skillLevel
                + "group=" + group
                + "quality=" + quality
                + "weight=" + weight
                + '}';
    }
}

package com.gy.server.game.hiddenWeapon.template;

import com.gy.server.common.base.AbsTemplate;
import com.gy.server.game.drop.RewardTemplate;
import com.ttlike.server.tl.baselib.serialize.combatAddition.CombatAdditions;

import java.util.List;
import java.util.Map;

public class HiddenWeaponStrengthenTemplate implements AbsTemplate {

    public int id; // ID 
    public int level; // 等级 
    public int point; // 节点 
    public List<RewardTemplate> cost; // 消耗
    public CombatAdditions addition = new CombatAdditions();// 加成

    @Override
    public void readTxt(Map<String, String> map) {
        id = Integer.parseInt(map.get("id"));
        level = Integer.parseInt(map.get("level"));
        point = Integer.parseInt(map.get("point"));
        cost = RewardTemplate.readListFromText(map.get("cost"));
        addition = CombatAdditions.readFromStr(map.get("addition"));
    }

    @Override
    public String toString() {
        return "HiddenWeaponStrengthenTemplate{"
                + "id=" + id
                + "level=" + level
                + "point=" + point
                + "cost=" + cost
                + "addition=" + addition
                + '}';
    }
}

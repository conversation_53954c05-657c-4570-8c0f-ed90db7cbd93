package com.gy.server.game.hiddenWeapon.template;

import java.util.*;
import com.gy.server.common.base.AbsTemplate;

public class HiddenWeaponTriggerModeTemplate implements AbsTemplate {

    public int id; // id 
    public int skillId; // 服务器绑定技能 

    @Override
    public void readTxt(Map<String, String> map) {
        id = Integer.parseInt(map.get("id"));
        skillId = Integer.parseInt(map.get("skillId"));
    }

    @Override
    public String toString() {
        return "HiddenWeaponTriggerModeTemplate{"
                + "id=" + id
                + "skillId=" + skillId
                + '}';
    }
}

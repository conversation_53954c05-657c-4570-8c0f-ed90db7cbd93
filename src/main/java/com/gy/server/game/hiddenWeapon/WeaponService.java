package com.gy.server.game.hiddenWeapon;

import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.common.ConstantConfigReader;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.function.Function;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.hiddenWeapon.template.*;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.service.Service;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.MathUtil;
import com.ttlike.server.tl.baselib.serialize.player.WeaponSkillSchemeInfoDb;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;

/**
 * 暗器
 * @author: gbk
 * @date: 2025-03-13 15:46
 */
public class WeaponService extends PlayerPacketHandler implements Service {

    //常量配置
    private static HiddenWeaponConstTemplate constTemplate;
    //暗器配置
    private static Map<Integer, HiddenWeaponTemplate> weaponTemplateMap = new HashMap<>();
    //暗器触发方式配置
    private static Map<Integer, HiddenWeaponTriggerModeTemplate> triggerModeMap = new HashMap<>();
    //暗器强化配置
    private static Map<Integer, HiddenWeaponStrengthenTemplate> strengthenTemplateMap = new HashMap<>();
    //暗器升阶配置
    private static Map<Integer, HiddenWeaponUpGradeTemplate> upGradeTemplateMap = new HashMap<>();
    private static List<Pair<Integer, Integer>> unGradeList = new ArrayList<>();
    private static int maxUpGradeExp;
    //暗器技能池配置
    private static Map<Integer, List<Integer>> skillPoolGroupId2IdMap = new HashMap<>();
    private static Map<Integer, HiddenWeaponSkillPoolTemplate> skillPoolMap = new HashMap<>();

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.hiddenWeapon_hiddenWeapon);
        for (Map<String, String> map : mapList) {
            HiddenWeaponTemplate template = new HiddenWeaponTemplate();
            template.readTxt(map);
            weaponTemplateMap.put(template.id, template);
        }
        mapList = ConfigReader.read(ConfigFile.hiddenWeapon_triggerMode);
        for (Map<String, String> map : mapList) {
            HiddenWeaponTriggerModeTemplate template = new HiddenWeaponTriggerModeTemplate();
            template.readTxt(map);
            triggerModeMap.put(template.id, template);
        }
        mapList = ConfigReader.read(ConfigFile.hiddenWeapon_strengthen);
        for (Map<String, String> map : mapList) {
            HiddenWeaponStrengthenTemplate template = new HiddenWeaponStrengthenTemplate();
            template.readTxt(map);
            strengthenTemplateMap.put(template.id, template);
        }
        mapList = ConfigReader.read(ConfigFile.hiddenWeapon_upGrade);
        for (Map<String, String> map : mapList) {
            HiddenWeaponUpGradeTemplate template = new HiddenWeaponUpGradeTemplate();
            template.readTxt(map);
            unGradeList.add(Pair.of(template.maxExp, template.grade));
            maxUpGradeExp = Math.max(maxUpGradeExp, template.maxExp);
            upGradeTemplateMap.put(template.grade, template);
        }
        mapList = ConfigReader.read(ConfigFile.hiddenWeapon_skillPool);
        for (Map<String, String> map : mapList) {
            HiddenWeaponSkillPoolTemplate template = new HiddenWeaponSkillPoolTemplate();
            template.readTxt(map);
            if (!skillPoolMap.containsKey(template.id)) {
                skillPoolGroupId2IdMap.put(template.id, new ArrayList<>());
            }
            skillPoolGroupId2IdMap.get(template.id).add(template.id);
            skillPoolMap.put(template.id, template);
        }
        Map<String, String> map = ConstantConfigReader.read(ConfigFile.hiddenWeapon_const);
        HiddenWeaponConstTemplate template = new HiddenWeaponConstTemplate();
        template.readTxt(map);
        constTemplate = template;
    }

    @Override
    public void clearConfigData() {
         weaponTemplateMap.clear();
         triggerModeMap.clear();
         strengthenTemplateMap.clear();
         upGradeTemplateMap.clear();
         skillPoolMap.clear();
        unGradeList.clear();
    }

    public static HiddenWeaponConstTemplate getConstTemplate() {
        return constTemplate;
    }

    public static Map<Integer, HiddenWeaponTemplate> getWeaponTemplateMap() {
        return weaponTemplateMap;
    }

    public static Map<Integer, HiddenWeaponTriggerModeTemplate> getTriggerModeMap() {
        return triggerModeMap;
    }

    public static Map<Integer, HiddenWeaponStrengthenTemplate> getStrengthenTemplateMap() {
        return strengthenTemplateMap;
    }

    public static Map<Integer, HiddenWeaponUpGradeTemplate> getUpGradeTemplateMap() {
        return upGradeTemplateMap;
    }

    public static List<Pair<Integer, Integer>> getUnGradeList() {
        return unGradeList;
    }

    public static int getMaxUpGradeExp() {
        return maxUpGradeExp;
    }

    public static Map<Integer, List<Integer>> getSkillPoolGroupId2IdMap() {
        return skillPoolGroupId2IdMap;
    }

    public static Map<Integer, HiddenWeaponSkillPoolTemplate> getSkillPoolMap() {
        return skillPoolMap;
    }

    /**
     * 根据升阶经验获取升阶等级
     */
    public static int getUpGradeLevel(int upGradeExp){
        if(upGradeExp >= maxUpGradeExp){
            return unGradeList.get(unGradeList.size() - 1).getRight();
        }
        for (int i = unGradeList.size() - 1; i >= 0; i--) {
            if(upGradeExp >= unGradeList.get(i).getLeft()){
                return unGradeList.get(i).getRight() + 1;
            }
        }
        return 1;
    }

    /**
     * 暗器升级（修炼）
     */
    @Handler(PtCode.WEAPON_LEVEL_UP_REQ)
    private void weaponLevelUp(Player player, PbProtocol.WeaponLevelUpReq req, long time){
        PbProtocol.WeaponLevelUpRst.Builder rst = PbProtocol.WeaponLevelUpRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            //检查function是否开启
            if(Function.hiddenWeapon.isNotOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            PlayerHiddenWeaponModel hiddenWeaponModel = player.getHiddenWeaponModel();
            HiddenWeaponTemplate hiddenWeaponTemplate = getWeaponTemplateMap().get(hiddenWeaponModel.getTid());
            HiddenWeaponStrengthenTemplate strengthenTemplate = getStrengthenTemplateMap().get(hiddenWeaponModel.getLevelId() + 1);
            if(Objects.isNull(strengthenTemplate)){
                rst.setResult(Text.genServerRstInfo(Text.暗器已达到最大等级));
                break logic;
            }
            //检查等级是否达到上限
            if(strengthenTemplate.level >= hiddenWeaponTemplate.maxLevel
                || !getStrengthenTemplateMap().containsKey(hiddenWeaponModel.getLevelId() + 1)){
                rst.setResult(Text.genServerRstInfo(Text.暗器已达到最大等级));
                break logic;
            }
            List<Reward> costs = RewardTemplate.createRewards(strengthenTemplate.cost);
            if(Reward.check(player, costs) != -1){
                rst.setResult(Text.genServerRstInfo(Text.消耗不足));
                break logic;
            }
            Reward.remove(costs, player, BehaviorType.HiddenWeaponLevelUp);
            hiddenWeaponModel.setLevelId(hiddenWeaponModel.getLevelId() + 1);
            player.postEvent(PlayerEventType.hiddenWeaponLevelUp);
            rst.setLevelId(hiddenWeaponModel.getLevelId());
        }
        player.send(PtCode.WEAPON_LEVEL_UP_RST, rst.build(), time);
    }

    /**
     * 暗器打造
     */
    @Handler(PtCode.WEAPON_CREATE_REQ)
    private void weaponCreate(Player player, PbProtocol.WeaponCreateReq req, long time){
        PbProtocol.WeaponCreateRst.Builder rst = PbProtocol.WeaponCreateRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            //检查function是否开启
            if(Function.hiddenWeapon.isNotOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            PlayerHiddenWeaponModel hiddenWeaponModel = player.getHiddenWeaponModel();
            HiddenWeaponTemplate hiddenWeaponTemplate = getWeaponTemplateMap().get(hiddenWeaponModel.getTid() + 1);
            if(Objects.isNull(hiddenWeaponTemplate)){
                rst.setResult(Text.genServerRstInfo(Text.暗器不能继续打造));
                break logic;
            }
            List<Reward> costs = RewardTemplate.createRewards(hiddenWeaponTemplate.createCost);
            if(Reward.check(player, costs) != -1){
                rst.setResult(Text.genServerRstInfo(Text.消耗不足));
                break logic;
            }
            Reward.remove(costs, player, BehaviorType.HiddenWeaponCreate);
            hiddenWeaponModel.setTid(hiddenWeaponModel.getTid() + 1);
            player.postEvent(PlayerEventType.hiddenWeaponCreate);
            rst.setTid(hiddenWeaponModel.getTid());
        }
        player.send(PtCode.WEAPON_CREATE_RST, rst.build(), time);
    }

    /**
     * 暗器进阶（升阶）
     */
    @Handler(PtCode.WEAPON_GROWTH_UP_REQ)
    private void weaponGrowthUp(Player player, PbProtocol.WeaponGrowthUpReq req, long time){
        PbProtocol.WeaponGrowthUpRst.Builder rst = PbProtocol.WeaponGrowthUpRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            //检查function是否开启
            if(Function.hiddenWeapon.isNotOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            PlayerHiddenWeaponModel hiddenWeaponModel = player.getHiddenWeaponModel();
            //检查等阶是否最大
            int growthUpExp = hiddenWeaponModel.getGrowthUpExp();
            int upGradeLevel = getUpGradeLevel(growthUpExp);
            HiddenWeaponTemplate hiddenWeaponTemplate = getWeaponTemplateMap().get(hiddenWeaponModel.getTid());
            HiddenWeaponUpGradeTemplate upGradeTemplate = getUpGradeTemplateMap().get(upGradeLevel);
            if(hiddenWeaponTemplate.gradeId <= upGradeLevel
                && upGradeTemplate.maxExp <= growthUpExp){
                rst.setResult(Text.genServerRstInfo(Text.暗器品阶达到最大));
                break logic;
            }
            List<Reward> costs = RewardTemplate.createRewards(upGradeTemplate.upgradeCost);
            if(Reward.check(player, costs) != -1){
                rst.setResult(Text.genServerRstInfo(Text.消耗不足));
                break logic;
            }
            Reward.remove(costs, player, BehaviorType.HiddenWeaponGrowthUp);

            String addedUpgradeExp = MathUtil.weightRandom(upGradeTemplate.upgradeExpMap);
            String[] str = addedUpgradeExp.split(":");
            //随机品检值
            int minExp = Integer.parseInt(str[0]);
            int maxExp = Integer.parseInt(str[1]);
            int addedGrowthUpExp = CommonUtils.random(minExp, maxExp);
            hiddenWeaponModel.setGrowthUpExp(Math.min(hiddenWeaponModel.getGrowthUpExp() + addedGrowthUpExp, maxUpGradeExp));

            player.postEvent(PlayerEventType.hiddenWeaponGrowthUp);
            rst.setGrowthUpExp(hiddenWeaponModel.getGrowthUpExp());
        }
        player.send(PtCode.WEAPON_GROWTH_UP_RST, rst.build(), time);
    }

    /**
     * 暗器洗练技能
     */
    @Handler(PtCode.WEAPON_SKILL_PROMOTION_GET_REQ)
    private void weaponSkillPromotionGet(Player player, PbProtocol.WeaponSkillPromotionGetReq req, long time){
        PbProtocol.WeaponSkillPromotionGetRst.Builder rst = PbProtocol.WeaponSkillPromotionGetRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            //检查function是否开启
            if(Function.hiddenWeapon.isNotOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            int schemeId = req.getSchemeId();
            PlayerHiddenWeaponModel hiddenWeaponModel = player.getHiddenWeaponModel();
            Map<Integer, WeaponSkillSchemeInfoDb> skillSchemeInfoMap = hiddenWeaponModel.getSkillSchemeInfoMap();
            if(!skillSchemeInfoMap.containsKey(schemeId)){
                rst.setResult(Text.genServerRstInfo(Text.暗器栏位未解锁));
                break logic;
            }
            WeaponSkillSchemeInfoDb weaponSkillSchemeInfo = skillSchemeInfoMap.get(schemeId);
//            if(CollectionUtil.isNotEmpty(weaponSkillSchemeInfo.getSelectedSkillIds())){
//                rst.setResult(Text.genServerRstInfo(Text.暗器技能还有待选择的技能));
//                break logic;
//            }
            HiddenWeaponConstTemplate constTemplate = getConstTemplate();
            List<Reward> costs = RewardTemplate.createRewards(constTemplate.refreshCost);
            if(Reward.check(player, costs) != -1){
                rst.setResult(Text.genServerRstInfo(Text.消耗不足));
                break logic;
            }
            Reward.remove(costs, player, BehaviorType.HiddenWeaponSkillPromotion);
            //开始随机技能
            List<Integer> skillIds = randomSkill();
            weaponSkillSchemeInfo.setSelectedSkillIds(skillIds);
            rst.setSchemeId(schemeId);
            rst.addAllSkillIds(skillIds);
        }
        player.send(PtCode.WEAPON_SKILL_PROMOTION_GET_RST, rst.build(), time);
    }

    /**
     * 随机技能
     */
    private List<Integer> randomSkill(){
        List<Integer> skillList = new ArrayList<>();
        Integer randomGroupId = -1;
        HiddenWeaponConstTemplate constTemplate = getConstTemplate();
        Map<Integer, List<Integer>> skillPoolGroupId2IdMap = new HashMap<>(getSkillPoolGroupId2IdMap());
        for (int i = 0; i < constTemplate.skillNum; i++) {
            //删除随机的组id
            skillPoolGroupId2IdMap.remove(randomGroupId);
            Map<Integer, Integer> skillWeights = new HashMap<>();
            for (List<Integer> skillIds : skillPoolGroupId2IdMap.values()) {
                for (Integer skillId : skillIds) {
                    skillWeights.put(skillId, getSkillPoolMap().get(skillId).weight);
                }
            }
            Integer skillId = MathUtil.weightRandom(skillWeights);
            skillList.add(skillId);
            randomGroupId = getSkillPoolMap().get(skillId).group;
        }

        return skillList;
    }

    /**
     * 暗器洗练技能确定
     */
    @Handler(PtCode.WEAPON_SKILL_PROMOTION_SURE_REQ)
    private void weaponSkillPromotionSure(Player player, PbProtocol.WeaponSkillPromotionSureReq req, long time){
        PbProtocol.WeaponSkillPromotionSureRst.Builder rst = PbProtocol.WeaponSkillPromotionSureRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            //检查function是否开启
            if(Function.hiddenWeapon.isNotOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            int schemeId = req.getSchemeId();
            PlayerHiddenWeaponModel hiddenWeaponModel = player.getHiddenWeaponModel();
            Map<Integer, WeaponSkillSchemeInfoDb> skillSchemeInfoMap = hiddenWeaponModel.getSkillSchemeInfoMap();
            if(!skillSchemeInfoMap.containsKey(schemeId)){
                rst.setResult(Text.genServerRstInfo(Text.暗器栏位未解锁));
                break logic;
            }
            WeaponSkillSchemeInfoDb weaponSkillSchemeInfo = skillSchemeInfoMap.get(schemeId);
            if(CollectionUtil.isEmpty(weaponSkillSchemeInfo.getSelectedSkillIds())){
                rst.setResult(Text.genServerRstInfo(Text.暗器技能没有待选择的技能));
                break logic;
            }
            if(req.getIsSure()){
                weaponSkillSchemeInfo.setSkillIds(new ArrayList<>(weaponSkillSchemeInfo.getSelectedSkillIds()));
            }
            weaponSkillSchemeInfo.getSelectedSkillIds().clear();
            rst.setSchemeId(schemeId);
            rst.addAllSkillIds(weaponSkillSchemeInfo.getSkillIds());
        }
        player.send(PtCode.WEAPON_SKILL_PROMOTION_SURE_RST, rst.build(), time);
    }

    /**
     * 暗器解锁栏位
     */
    @Handler(PtCode.WEAPON_SKILL_SCHEME_UNLOCK_REQ)
    private void weaponSkillSchemeUnlock(Player player, PbProtocol.WeaponSkillSchemeUnlockReq req, long time){
        PbProtocol.WeaponSkillSchemeUnlockRst.Builder rst = PbProtocol.WeaponSkillSchemeUnlockRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            //检查function是否开启
            if(Function.hiddenWeapon.isNotOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            HiddenWeaponConstTemplate constTemplate = WeaponService.getConstTemplate();
            PlayerHiddenWeaponModel hiddenWeaponModel = player.getHiddenWeaponModel();
            Map<Integer, WeaponSkillSchemeInfoDb> skillSchemeInfoMap = hiddenWeaponModel.getSkillSchemeInfoMap();
            if(skillSchemeInfoMap.size() >= constTemplate.projectNum){
                rst.setResult(Text.genServerRstInfo(Text.暗器栏位已满));
                break logic;
            }
            int schemeId = req.getSchemeId();
            if(skillSchemeInfoMap.containsKey(schemeId)){
                rst.setResult(Text.genServerRstInfo(Text.暗器栏位已解锁));
                break logic;
            }
            int costIndex = skillSchemeInfoMap.size() - constTemplate.projectDefaultNum;
            RewardTemplate rewardTemplate = constTemplate.projectCost.get(costIndex);
            Reward cost = rewardTemplate.createReward();
            if(cost.check(player) != -1){
                rst.setResult(Text.genServerRstInfo(Text.消耗不足));
                break logic;
            }
            cost.remove(player, BehaviorType.HiddenWeaponSkillSchemeUnlock);
            WeaponSkillSchemeInfoDb skillSchemeInfo = new WeaponSkillSchemeInfoDb();
            skillSchemeInfo.setSchemeId(schemeId);
            skillSchemeInfoMap.put(skillSchemeInfo.getSchemeId(), skillSchemeInfo);
            rst.setSchemeId(schemeId);
        }
        player.send(PtCode.WEAPON_SKILL_SCHEME_UNLOCK_RST, rst.build(), time);
    }

    /**
     * 暗器栏位修改名字
     */
    @Handler(PtCode.WEAPON_SKILL_SCHEME_NAME_REQ)
    private void weaponSkillSchemeName(Player player, PbProtocol.WeaponSkillSchemeNameReq req, long time){
        PbProtocol.WeaponSkillSchemeNameRst.Builder rst = PbProtocol.WeaponSkillSchemeNameRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            //检查function是否开启
            if(Function.hiddenWeapon.isNotOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            PlayerHiddenWeaponModel hiddenWeaponModel = player.getHiddenWeaponModel();
            Map<Integer, WeaponSkillSchemeInfoDb> skillSchemeInfoMap = hiddenWeaponModel.getSkillSchemeInfoMap();
            int schemeId = req.getSchemeId();
            String name = req.getName();
            if(!skillSchemeInfoMap.containsKey(schemeId)){
                rst.setResult(Text.genServerRstInfo(Text.暗器栏位未解锁));
                break logic;
            }
            if(Text.没有异常 != PlayerHelper.checkNameValid(name)){
                rst.setResult(Text.genServerRstInfo(Text.取名格式非法));
                break logic;
            }
            skillSchemeInfoMap.get(schemeId).setName(name);
            rst.setSchemeId(schemeId);
            rst.setName(name);
        }
        player.send(PtCode.WEAPON_SKILL_SCHEME_NAME_RST, rst.build(), time);
    }

    /**
     * 暗器栏位修改触发方式
     */
    @Handler(PtCode.WEAPON_SCHEME_TRIGGER_REQ)
    private void weaponSchemeTrigger(Player player, PbProtocol.WeaponSchemeTriggerReq req, long time){
        PbProtocol.WeaponSchemeTriggerRst.Builder rst = PbProtocol.WeaponSchemeTriggerRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            //检查function是否开启
            if(Function.hiddenWeapon.isNotOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            int triggerId = req.getTriggerId();
            if(!getTriggerModeMap().containsKey(triggerId)){
                rst.setResult(Text.genServerRstInfo(Text.数据异常));
                break logic;
            }
            PlayerHiddenWeaponModel hiddenWeaponModel = player.getHiddenWeaponModel();
            Map<Integer, WeaponSkillSchemeInfoDb> skillSchemeInfoMap = hiddenWeaponModel.getSkillSchemeInfoMap();
            int schemeId = req.getSchemeId();
            if(!skillSchemeInfoMap.containsKey(schemeId)){
                rst.setResult(Text.genServerRstInfo(Text.暗器栏位未解锁));
                break logic;
            }
            skillSchemeInfoMap.get(schemeId).setTriggerId(triggerId);
            rst.setTriggerId(triggerId);
            rst.setSchemeId(schemeId);
        }
        player.send(PtCode.WEAPON_SCHEME_TRIGGER_RST, rst.build(), time);
    }




}

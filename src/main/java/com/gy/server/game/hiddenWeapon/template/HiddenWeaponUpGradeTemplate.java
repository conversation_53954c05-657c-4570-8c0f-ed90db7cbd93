package com.gy.server.game.hiddenWeapon.template;

import com.gy.server.common.base.AbsTemplate;
import com.gy.server.game.attribute.Attributes;
import com.gy.server.game.drop.RewardTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class HiddenWeaponUpGradeTemplate implements AbsTemplate {

    public int grade; // 品阶 
    public int maxExp; // 品鉴值上限
    public Map<String, Integer> upgradeExpMap = new HashMap<>(); // 每次提升品鉴值数值
    public List<RewardTemplate> upgradeCost; // 单次升阶消耗
    public Attributes upgradeArribute = new Attributes();// 每1点品鉴值加成

    @Override
    public void readTxt(Map<String, String> map) {
        grade = Integer.parseInt(map.get("grade"));
        maxExp = Integer.parseInt(map.get("maxExp"));
        String[] upgradeExpStr = map.get("upgradeExp").split(",");
        for (String s : upgradeExpStr) {
            String[] split = s.split("\\|");
            upgradeExpMap.put(split[0], Integer.parseInt(split[1]));
        }
        upgradeCost = RewardTemplate.readListFromText(map.get("upgradeCost"));
        upgradeArribute = Attributes.readFromStr(map.get("upgradeArribute"));
    }

    @Override
    public String toString() {
        return "HiddenWeaponUpGradeTemplate{"
                + "grade=" + grade
                + "maxExp=" + maxExp
                + "upgradeExp=" + upgradeExpMap
                + "upgradeCost=" + upgradeCost
                + "upgradeArribute=" + upgradeArribute
                + '}';
    }
}

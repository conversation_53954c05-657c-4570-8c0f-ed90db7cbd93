package com.gy.server.game.hiddenWeapon;

import com.gy.server.game.attribute.AttributeSourceType;
import com.gy.server.game.attribute.Attributes;
import com.gy.server.game.function.Function;
import com.gy.server.game.hero.Hero;
import com.gy.server.game.hiddenWeapon.template.HiddenWeaponConstTemplate;
import com.gy.server.game.hiddenWeapon.template.HiddenWeaponStrengthenTemplate;
import com.gy.server.game.hiddenWeapon.template.HiddenWeaponTemplate;
import com.gy.server.game.hiddenWeapon.template.HiddenWeaponUpGradeTemplate;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModel;
import com.gy.server.game.player.event.PlayerEvent;
import com.gy.server.game.player.event.PlayerEventHandler;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.packet.PbHiddenWeapon;
import com.gy.server.packet.PbUser;
import com.ttlike.server.tl.baselib.serialize.combatAddition.CombatAdditions;
import com.ttlike.server.tl.baselib.serialize.lineup.LineupHiddenWeaponInfo;
import com.ttlike.server.tl.baselib.serialize.player.PlayerBlobDb;
import com.ttlike.server.tl.baselib.serialize.player.PlayerHiddenWeaponModelDb;
import com.ttlike.server.tl.baselib.serialize.player.WeaponSkillSchemeInfoDb;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 暗器模块
 * @author: gbk
 * @date: 2025-03-13 20:18
 */
public class PlayerHiddenWeaponModel extends PlayerModel implements PlayerEventHandler {

    public PlayerHiddenWeaponModel(Player player) {
        super(player);
    }
    //暗器id
    private int tid;
    //暗器等级ID（）
    private int levelId;
    //暗器等阶值（品阶值）
    private int growthUpExp;
    //暗器栏位技能信息
    private Map<Integer, WeaponSkillSchemeInfoDb> skillSchemeInfoMap = new HashMap<>();

    public int getTid() {
        return tid;
    }

    public void setTid(int tid) {
        this.tid = tid;
    }

    public int getLevelId() {
        return levelId;
    }

    public void setLevelId(int levelId) {
        this.levelId = levelId;
    }

    public int getGrowthUpExp() {
        return growthUpExp;
    }

    public void setGrowthUpExp(int growthUpExp) {
        this.growthUpExp = growthUpExp;
    }

    public Map<Integer, WeaponSkillSchemeInfoDb> getSkillSchemeInfoMap() {
        return skillSchemeInfoMap;
    }

    public void setSkillSchemeInfoMap(Map<Integer, WeaponSkillSchemeInfoDb> skillSchemeInfoMap) {
        this.skillSchemeInfoMap = skillSchemeInfoMap;
    }

    public CombatAdditions getLevelCombatAddition(){
        CombatAdditions combatAdditions = new CombatAdditions();
        HiddenWeaponTemplate hiddenWeaponTemplate = WeaponService.getWeaponTemplateMap().get(getTid());
        for (int id = levelId; id > 0; id--) {
            HiddenWeaponStrengthenTemplate strengthenTemplate = WeaponService.getStrengthenTemplateMap().get(id);
            CombatAdditions copy = strengthenTemplate.addition.copy();
            copy.doubleValue((double)hiddenWeaponTemplate.attributeCoe / 10000D);
            CombatAdditions.mergeCombatAdditions(combatAdditions, copy);
        }
        return combatAdditions;
    }

    public Attributes getGrowthUpAttribute(){
        Attributes attributes = new Attributes();
        int exp = growthUpExp;
        int gradeLevel = WeaponService.getUpGradeLevel(growthUpExp);
        for (int grade = gradeLevel; grade >= 1; grade--) {
            HiddenWeaponUpGradeTemplate hiddenWeaponUpGradeTemplate = WeaponService.getUpGradeTemplateMap().get(grade);
            int addedNum = exp - hiddenWeaponUpGradeTemplate.maxExp;
            if(addedNum > 0){
                Attributes copy = hiddenWeaponUpGradeTemplate.upgradeArribute.copy();
                copy.doubleValue(addedNum);
                Attributes.mergeAttributes(attributes, copy);
            }
            exp -= addedNum;
        }
        return attributes;
    }

    public LineupHiddenWeaponInfo genLineupHiddenWeaponInfo(int heroId, int schemeId){
        LineupHiddenWeaponInfo lineupHiddenWeaponInfo = new LineupHiddenWeaponInfo();
        lineupHiddenWeaponInfo.setTid(tid);
        lineupHiddenWeaponInfo.setLevelId(levelId);
        lineupHiddenWeaponInfo.setGrowthUpExp(growthUpExp);
        lineupHiddenWeaponInfo.setSkillSchemeInfoDb(skillSchemeInfoMap.get(schemeId));
        lineupHiddenWeaponInfo.setWearHeroId(heroId);
        return lineupHiddenWeaponInfo;
    }

    public PbHiddenWeapon.HiddenWeaponInfo genPb(){
        PbHiddenWeapon.HiddenWeaponInfo.Builder builder = PbHiddenWeapon.HiddenWeaponInfo.newBuilder();
        builder.setTid(tid);
        builder.setLevelId(levelId);
        builder.setGrowthUpExp(growthUpExp);
        for (WeaponSkillSchemeInfoDb infoDb : skillSchemeInfoMap.values()) {
            builder.addSchemeInfos(genWeaponSkillSchemeInfoPb(infoDb));
        }
        return builder.build();
    }

    public PbHiddenWeapon.HiddenWeaponSkillSchemeInfo genWeaponSkillSchemeInfoPb(WeaponSkillSchemeInfoDb info){
        PbHiddenWeapon.HiddenWeaponSkillSchemeInfo.Builder builder = PbHiddenWeapon.HiddenWeaponSkillSchemeInfo.newBuilder();
        builder.setSchemeId(info.getSchemeId());
        builder.setName(info.getName() == null ? "" : info.getName());
        builder.addAllSkillIds(info.getSkillIds());
        builder.setTriggerId(info.getTriggerId());
        builder.addAllSelectedSkillIds(info.getSelectedSkillIds());
        return builder.build();
    }

    @Override
    protected void loadData(PlayerBlobDb playerBlob) {
        PlayerHiddenWeaponModelDb hiddenWeaponModelDb = playerBlob.getHiddenWeaponModelDb();
        if(Objects.nonNull(hiddenWeaponModelDb)){
            this.tid = hiddenWeaponModelDb.getTid();
            this.levelId = hiddenWeaponModelDb.getLevelId();
            this.growthUpExp = hiddenWeaponModelDb.getGrowthUpExp();
            this.skillSchemeInfoMap.putAll(hiddenWeaponModelDb.getSkillSchemeInfoMap());
        }
    }

    @Override
    protected void saveData(PlayerBlobDb playerBlob) {
        PlayerHiddenWeaponModelDb db = new PlayerHiddenWeaponModelDb();
        db.setTid(tid);
        db.setLevelId(levelId);
        db.setGrowthUpExp(growthUpExp);
        db.setSkillSchemeInfoMap(skillSchemeInfoMap);

        playerBlob.setHiddenWeaponModelDb(db);

    }

    @Override
    public PlayerEventType[] getEventTypes() {
        return new PlayerEventType[]{
                PlayerEventType.registerAfter,
                PlayerEventType.hiddenWeaponGrowthUp,
                PlayerEventType.functionCheck,
        };
    }

    @Override
    public void handle(PlayerEvent event) {
        Player player = event.getSource();
        switch (event.getEventType()) {
            case registerAfter:{
                //玩家注册后
                HiddenWeaponConstTemplate constTemplate = WeaponService.getConstTemplate();
                for (int i = 1; i <= constTemplate.projectDefaultNum; i++) {
                    WeaponSkillSchemeInfoDb info = new WeaponSkillSchemeInfoDb();
                    info.setSchemeId(i);
                    skillSchemeInfoMap.put(i, info);
                }
                break;
            }
            case hiddenWeaponGrowthUp:{
                for (Hero hero : player.getBagModel().getAllHeroes()) {
                    hero.refreshBagAttributes(true, false, AttributeSourceType.暗器升阶);
                }
                player.getRoleModel().refreshAttributes(true, false, AttributeSourceType.暗器升阶);

                break;
            }
            case functionCheck:{
                if(Function.hiddenWeapon.isOpen(player) && tid <= 0){
                    tid = Collections.min(WeaponService.getWeaponTemplateMap().keySet());
                }
                break;
            }
        }
    }
}
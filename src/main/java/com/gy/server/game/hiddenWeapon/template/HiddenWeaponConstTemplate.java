package com.gy.server.game.hiddenWeapon.template;

import java.util.*;
import com.gy.server.common.base.AbsTemplate;
import com.gy.server.game.drop.RewardTemplate;

public class HiddenWeaponConstTemplate implements AbsTemplate {

    public int projectNum; // 保存方案的数量 
    public int projectDefaultNum; // 默认解锁的方案数量 
    public List<RewardTemplate> projectCost; // 解锁方案消耗，阶梯配置 
    public int skillNum; // 每个方案的技能数量 
    public List<RewardTemplate> refreshCost; // 洗练消耗 

    @Override
    public void readTxt(Map<String, String> map) {
        projectNum = Integer.parseInt(map.get("projectNum"));
        projectDefaultNum = Integer.parseInt(map.get("projectDefaultNum"));
        projectCost = RewardTemplate.readListFromText(map.get("projectCost"));
        skillNum = Integer.parseInt(map.get("skillNum"));
        refreshCost = RewardTemplate.readListFromText(map.get("refreshCost"));
    }

    @Override
    public String toString() {
        return "HiddenWeaponConstTemplate{"
                + "projectNum=" + projectNum
                + "projectDefaultNum=" + projectDefaultNum
                + "projectCost=" + projectCost
                + "skillNum=" + skillNum
                + "refreshCost=" + refreshCost
                + '}';
    }
}

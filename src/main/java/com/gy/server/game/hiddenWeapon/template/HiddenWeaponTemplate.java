package com.gy.server.game.hiddenWeapon.template;

import java.util.*;
import com.gy.server.common.base.AbsTemplate;
import com.gy.server.game.drop.RewardTemplate;

public class HiddenWeaponTemplate implements AbsTemplate {

    public int id; // id 
    public int maxLevel; // 等级上限 
    public int gradeId; // 品鉴值上限
    public int attributeCoe; // 属性系数 
    public int quality; // 品质 
    public List<RewardTemplate> createCost; // 打造消耗 
    public int shapeId; // 外观 

    @Override
    public void readTxt(Map<String, String> map) {
        id = Integer.parseInt(map.get("id"));
        maxLevel = Integer.parseInt(map.get("maxLevel"));
        gradeId = Integer.parseInt(map.get("gradeId"));
        attributeCoe = Integer.parseInt(map.get("attributeCoe"));
        quality = Integer.parseInt(map.get("quality"));
        createCost = RewardTemplate.readListFromText(map.get("createCost"));
        shapeId = Integer.parseInt(map.get("shapeId"));
    }

    @Override
    public String toString() {
        return "HiddenWeaponTemplate{"
                + "id=" + id
                + "maxLevel=" + maxLevel
                + "identify=" + gradeId
                + "attributeCoe=" + attributeCoe
                + "quality=" + quality
                + "createCost=" + createCost
                + "shapeId=" + shapeId
                + '}';
    }
}

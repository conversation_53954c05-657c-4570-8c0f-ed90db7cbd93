package com.gy.server.game.leagueBoss.template;

import com.gy.server.game.util.StringExtUtil;

import java.util.Map;

public class LeagueBossConstant {

    /**
     * 每日BOSS回合数
     */
    public final int todayChallengeSecond;
    /**
     * 击败N个首领获得一个宝箱
     */
    public final int killBossGetBoxNum;
    /**
     * 随机宝箱掉落id（随机掉落1个）
     */
    public final int randomReward;
    /**
     * 副本默认倍数
     */
    public final int automaticMultiple;
    /**
     * 门客堂提交道具
     * Key:道具ID  Value:获得经验
     */
    public final Map<Integer, Integer> leagueBossExpMap;

    public LeagueBossConstant(Map<String, String> map) {
        this.todayChallengeSecond = Integer.parseInt(map.get("leagueBossTimes"));
        this.killBossGetBoxNum = Integer.parseInt(map.get("killBossGetBoxNum"));
        this.randomReward = Integer.parseInt(map.get("randomReward"));
        this.automaticMultiple = Integer.parseInt(map.get("automaticMultiple"));
        this.leagueBossExpMap = StringExtUtil.string2Map(map.get("leagueBossExp"), ",", "|", Integer.class, Integer.class);
    }
}

package com.gy.server.game.leagueBoss.bean;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.hero.Hero;
import com.gy.server.game.player.Player;
import com.gy.server.game.role.PlayerRoleService;
import com.gy.server.game.role.template.ProtagonistTemplate;
import com.gy.server.packet.PbLeagueBoss;
import com.ttlike.server.tl.baselib.serialize.leagueBoss.HeroHurtInfoDb;

/**
 * 英雄伤害信息
 */
@ProtobufClass
public class HeroHurtInfo {

    /**
     * 实例ID -1代表主角
     */
    @Protobuf(order = 1)
    private int instanceId;
    /**
     * instanceId = -1时这个是主角表ID
     */
    @Protobuf(order = 2)
    private int heroTemplateId;
    @Protobuf(order = 3)
    private int heroQuality;
    @Protobuf(order = 4)
    private int heroStar;
    /**
     * 本次造成伤害
     */
    @Protobuf(order = 5)
    private long hurt;

    public long getHurt() {
        return hurt;
    }

    public void setHurt(long hurt) {
        this.hurt = hurt;
    }

    public HeroHurtInfo() {
    }

    public HeroHurtInfo(Hero hero, long hurt) {
        this.instanceId = hero.getItemId();
        this.heroTemplateId = hero.getTemplateId();
        this.heroQuality = hero.getQuality();
        this.heroStar = hero.getStar();
        this.hurt = hurt;
    }

    public HeroHurtInfo(Player player, long hurt) {
        ProtagonistTemplate protagonistTemplate = PlayerRoleService.getProtagonistTemplate(player.getProfession(), player.getSex());
        this.instanceId = -1;
        this.heroTemplateId = protagonistTemplate.id;
        this.heroQuality = player.getQuality();
        this.hurt = hurt;
    }

    public HeroHurtInfo(HeroUnit heroUnit, long hurt){
        this.instanceId = heroUnit.getInstanceId();
        this.heroTemplateId = heroUnit.getHeroId();
        this.heroQuality = heroUnit.getQuality();
        this.heroStar = heroUnit.getStar();
        this.hurt = hurt;
    }

    public HeroHurtInfo(HeroHurtInfoDb db) {
        this.heroTemplateId = db.getHeroTemplateId();
        this.instanceId = db.getInstanceId();
        this.heroQuality = db.getHeroQuality();
        this.heroStar = db.getHeroStar();
        this.hurt = db.getHurt();
    }

    public HeroHurtInfoDb genDb() {
        HeroHurtInfoDb db = new HeroHurtInfoDb();
        db.setInstanceId(instanceId);
        db.setHeroTemplateId(heroTemplateId);
        db.setHeroQuality(heroQuality);
        db.setHeroStar(heroStar);
        db.setHurt(hurt);
        return db;
    }

    public PbLeagueBoss.HeroHurtInfo genPb() {
        return PbLeagueBoss.HeroHurtInfo.newBuilder()
                .setInstanceId(instanceId)
                .setHeroTemplateId(heroTemplateId)
                .setHeroQuality(heroQuality)
                .setHeroStar(heroStar)
                .setHurt(hurt).build();
    }
}

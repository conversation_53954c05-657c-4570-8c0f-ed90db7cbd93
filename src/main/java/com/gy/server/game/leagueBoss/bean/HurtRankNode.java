package com.gy.server.game.leagueBoss.bean;

import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.packet.PbLeagueBoss;
import com.ttlike.server.tl.baselib.serialize.leagueBoss.HurtRankNodeDb;
import org.jetbrains.annotations.NotNull;

/**
 * 帮派BOSS伤害排行
 */
public class HurtRankNode implements Comparable<HurtRankNode> {

    private long playerId;
    // 个人累计伤害
    private long hurt;
    // 战斗中的伤害(内存数据)
    private long fightingHurt;


    public HurtRankNode() {
    }

    public HurtRankNode(HurtRankNodeDb hurtRankNodeDb) {
        this.playerId = hurtRankNodeDb.getPlayerId();
        this.hurt = hurtRankNodeDb.getHurt();
    }

    public HurtRankNodeDb genDb() {
        HurtRankNodeDb db = new HurtRankNodeDb();
        db.setPlayerId(playerId);
        db.setHurt(hurt);
        return db;
    }

    public PbLeagueBoss.HurtRankNode.Builder genPb() {
        MiniGamePlayer miniPlayer = PlayerManager.getMiniPlayer(playerId);
        return PbLeagueBoss.HurtRankNode.newBuilder()
                .setPlayerId(playerId)
                .setMiniUser(miniPlayer.genMinMiniUser())
                .setHurt(hurt);
    }

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public void addHurt(long addHurt) {
        this.hurt += addHurt;
    }

    public long getHurt() {
        return hurt;
    }

    public long getTotalHurt() {
        return hurt + fightingHurt;
    }

    public void setHurt(long hurt) {
        this.hurt = hurt;
    }

    public long getFightingHurt() {
        return fightingHurt;
    }

    public void setFightingHurt(long fightingHurt) {
        this.fightingHurt = fightingHurt;
    }

    public HurtRankNode(long playerId, long hurt) {
        this.playerId = playerId;
        this.hurt = hurt;
    }

    public HurtRankNode(long playerId) {
        this.playerId = playerId;
    }


    @Override
    public int compareTo(@NotNull HurtRankNode o) {
        return Long.compare(o.hurt + o.fightingHurt, this.hurt + this.fightingHurt);
    }
}

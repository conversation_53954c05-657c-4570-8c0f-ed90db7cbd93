package com.gy.server.game.leagueBoss.bean;

import com.gy.server.game.attribute.AttributeKey;
import com.gy.server.game.attribute.Attributes;
import com.gy.server.game.battleCollect.BattleCollectService;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.leagueBoss.LeagueBossService;
import com.gy.server.game.leagueBoss.LeagueBossStage;
import com.gy.server.game.leagueBoss.template.LeagueBossTemplate;
import com.gy.server.game.monster.MonsterService;
import com.gy.server.game.monster.MonsterTemplate;
import com.gy.server.game.world.World;
import com.gy.server.packet.PbLeagueBoss;
import com.ttlike.server.tl.baselib.serialize.leagueBoss.LeagueBossInfoDb;

/**
 * 帮派BOSS信息
 */
public class LeagueBossInfo {
    /**
     * BossId
     */
    private int bossId;
    /**
     * BOSS初始等级
     */
    private int initLevel;
    /**
     * BOSS真实等级
     */
    private int level;
    /**
     * 当前血量
     */
    private long currentHp;
    /**
     * Boss死亡次数
     */
    private int deadCount;

    public LeagueBossInfo(LeagueBossInfoDb value) {
        this.bossId = value.getBossId();
        this.initLevel = value.getInitLevel();
        this.level = value.getLevel();
        this.currentHp = value.getCurrentHp();
        this.deadCount = value.getDeadCount();
    }

    public LeagueBossInfo(int bossId, int initLevel) {
        LeagueBossTemplate leagueBossTemplate = LeagueBossService.getLeagueBossTemplateMap().get(bossId);
        int monsterId = BattleCollectService.findBossId(leagueBossTemplate.battleCollect);
        MonsterTemplate monsterTemplate = MonsterService.monsterTemplates.get(monsterId);
        this.bossId = bossId;
        this.initLevel = initLevel;
        this.level = initLevel + 1;
        HeroUnit heroUnit = monsterTemplate.genUnit(World.getWorldLevel());
        LeagueBossStage.initHeroUnit(heroUnit, level, -1);
        this.currentHp = heroUnit.getAttributes().getHp();
    }

    public LeagueBossInfoDb genDb() {
        LeagueBossInfoDb db = new LeagueBossInfoDb();
        db.setBossId(bossId);
        db.setInitLevel(initLevel);
        db.setLevel(level);
        db.setCurrentHp(currentHp);
        db.setDeadCount(deadCount);
        return db;
    }

    public PbLeagueBoss.LeagueBossInfo genPb() {
        LeagueBossTemplate leagueBossTemplate = LeagueBossService.getLeagueBossTemplateMap().get(bossId);
        int bossTemplateId = BattleCollectService.findBossId(leagueBossTemplate.battleCollect);
        MonsterTemplate monsterTemplate = MonsterService.monsterTemplates.get(bossTemplateId);
        HeroUnit heroUnit = monsterTemplate.genUnit(World.getWorldLevel());
        LeagueBossStage.initHeroUnit(heroUnit, level, -1);
        return PbLeagueBoss.LeagueBossInfo.newBuilder()
                .setBossId(this.bossId)
                .setBossLv(getShowLevel())
                .setMaxHp(heroUnit.getAttributes().getValue(AttributeKey.最大生命))
                .setRemainHp(currentHp).build();
    }

    //获取宝箱数量
    public int getBoxCount() {
        return deadCount / LeagueBossService.getConstant().killBossGetBoxNum;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public long getCurrentHp() {
        return currentHp;
    }

    public void setCurrentHp(long currentHp) {
        this.currentHp = currentHp;
    }

    public int getDeadCount() {
        return deadCount;
    }

    public void setDeadCount(int deadCount) {
        this.deadCount = deadCount;
    }

    public void addDeadCount(int addCount) {
        this.deadCount += addCount;
    }

    /**
     * 客户端显示等级
     *
     * @return 真实等级-初始等级
     */
    public int getShowLevel() {
        return level - initLevel;
    }
}

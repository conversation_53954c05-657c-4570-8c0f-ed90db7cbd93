package com.gy.server.game.leagueBoss;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.combataddition.CombatAdditionFunction;
import com.gy.server.game.drop.DropService;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.league.LeagueModelEnums;
import com.gy.server.game.leagueBoss.bean.LeagueBossInfo;
import com.gy.server.game.leagueBoss.template.LeagueBossConstant;
import com.gy.server.game.leagueBoss.template.LeagueBossTemplate;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.mail.MailManager;
import com.gy.server.game.mail.MailType;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModel;
import com.gy.server.game.player.event.PlayerEvent;
import com.gy.server.game.player.event.PlayerEventHandler;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.text.Text;
import com.gy.server.game.text.TextParamText;
import com.gy.server.packet.PbCommons;
import com.gy.server.packet.PbLeagueBoss;
import com.ttlike.server.tl.baselib.serialize.leagueBoss.LeagueBossModelDb;
import com.ttlike.server.tl.baselib.serialize.player.PlayerBlobDb;

import java.util.*;

import static com.gy.server.game.player.event.PlayerEventType.*;

/**
 * 帮派BOSS
 */
public class PlayerLeagueBossModel extends PlayerModel implements PlayerEventHandler {

    private static PlayerEventType[] eventTypes = new PlayerEventType[]{day5Refresh, week5Refresh, leagueQuit, joinLeague};
    /**
     * 今日已挑战的BossId
     */
    private int todayBossId;
    /**
     * 每日剩余挑战回合数
     */
    private int remainChallengeSecond;
    /**
     * 今日挑战伤害
     */
    private long todayHurt;
    /**
     * 已领取的宝箱个数 每周重置
     */
    private int hasBoxRewardCount;

    /**
     * 是否初始化
     */
    private boolean isInit;

    /**
     * 上次退出帮派已经领取的周伤害奖励
     * 退出帮派 填充该数据
     * 加入新帮派 该数据清空
     */
    private Set<Long> lastQuitLeagueHasWeekReward = new HashSet<>();

    public PlayerLeagueBossModel(Player player) {
        super(player);
    }

    @Override
    public void init() {
        if (!isInit) {
            resetTodaySecond();
            this.isInit = true;
        }
    }

    public PbLeagueBoss.LeagueBossMainInfo genMainPb(int bossId) {
        League league = LeagueManager.getLeagueByPlayer(getPlayer());
        LeagueBossModel leagueBossModel = league.getModel(LeagueModelEnums.boss);
        LeagueBossInfo bossInfo = leagueBossModel.getBossInfo(bossId);
        PbLeagueBoss.LeagueBossInfo leagueBossInfo = bossInfo.genPb();
        return PbLeagueBoss.LeagueBossMainInfo.newBuilder()
                .setBossId(bossId)
                .setRemainChallengeSecond(remainChallengeSecond)
                .setBossLv(bossInfo.getShowLevel())
                .setRemainHp(bossInfo.getCurrentHp())
                .setMaxHp(leagueBossInfo.getMaxHp())
                .build();

    }

    /**
     * 生成门客堂主界面Pb
     */
    public PbLeagueBoss.LeagueBossPalMainInfo genPalMainPb() {
        League league = LeagueManager.getLeagueByPlayer(getPlayer());
        LeagueBossModel leagueBossModel = league.getModel(LeagueModelEnums.boss);
        Map<Integer, LeagueBossTemplate> leagueBossTemplateMap = LeagueBossService.getLeagueBossTemplateMap();
        PbLeagueBoss.LeagueBossPalMainInfo.Builder builder = PbLeagueBoss.LeagueBossPalMainInfo.newBuilder()
                .setPalLevel(leagueBossModel.getPalLevel())
                .setPalExp(leagueBossModel.getPalExp())
                .setTodayBossId(todayBossId)
                .setRemainChallengeSecond(remainChallengeSecond)
                .setTotalBoxCount(leagueBossModel.getTotalBoxCount())
                .setHasDrawBoxCount(hasBoxRewardCount)
                .setWeekHurt(leagueBossModel.getWeekHurt(getPlayerId()))
                .setTodayHurt(todayHurt)
                .setMarkingBossId(leagueBossModel.getMarkingBossId())
                .addAllHasWeekReward(leagueBossModel.getWeekRewardMap().getOrDefault(getPlayerId(), new HashSet<>()));
        for (Integer bossId : leagueBossTemplateMap.keySet()) {
            LeagueBossInfo bossInfo = leagueBossModel.getBossInfo(bossId);
            builder.putBossInfo(bossId, bossInfo.genPb());
        }
        return builder.build();

    }

    /**
     * 扣除剩余挑战时间
     *
     * @param costSecond 扣除回合数
     */
    public void costChallengeTime(int costSecond) {
        int remain = this.remainChallengeSecond - costSecond;
        this.remainChallengeSecond = Math.max(0, remain);
    }

    public void addHasBoxRewardCount(int addCount) {
        this.hasBoxRewardCount += addCount;
    }

    public void setHasBoxRewardCount(int hasBoxRewardCount) {
        this.hasBoxRewardCount = hasBoxRewardCount;
    }

    public void addTodayHurt(long addHurt) {
        this.todayHurt += addHurt;
    }

    @Override
    protected void loadData(PlayerBlobDb playerBlob) {
        LeagueBossModelDb leagueBossModelDb = playerBlob.getLeagueBossModelDb();
        if (Objects.nonNull(leagueBossModelDb)) {
            this.todayBossId = leagueBossModelDb.getTodayBossId();
            this.remainChallengeSecond = leagueBossModelDb.getRemainChallengeSecond();
            this.todayHurt = leagueBossModelDb.getTodayHurt();
            this.hasBoxRewardCount = leagueBossModelDb.getHasBoxRewardCount();
            this.isInit = leagueBossModelDb.isInit();
            this.lastQuitLeagueHasWeekReward = leagueBossModelDb.getLastQuitLeagueHasWeekReward();
        }
    }

    @Override
    protected void saveData(PlayerBlobDb playerBlob) {
        LeagueBossModelDb leagueBossModelDb = new LeagueBossModelDb();
        leagueBossModelDb.setTodayBossId(todayBossId);
        leagueBossModelDb.setRemainChallengeSecond(remainChallengeSecond);
        leagueBossModelDb.setTodayHurt(todayHurt);
        leagueBossModelDb.setHasBoxRewardCount(hasBoxRewardCount);
        leagueBossModelDb.setInit(isInit);
        leagueBossModelDb.setLastQuitLeagueHasWeekReward(lastQuitLeagueHasWeekReward);
        playerBlob.setLeagueBossModelDb(leagueBossModelDb);
    }

    @Override
    public PlayerEventType[] getEventTypes() {
        return eventTypes;
    }

    @Override
    public void handle(PlayerEvent event) {
        switch (event.getEventType()) {
            case day5Refresh:
                day5Refresh();
                break;
            case week5Refresh:
                this.hasBoxRewardCount = 0;
                break;
            case leagueQuit:
                long leagueId = event.getParam(0);
                League quitLeague = LeagueManager.getLeagueById(leagueId);
                if (Objects.nonNull(quitLeague)) {
                    LeagueBossModel bossModel = quitLeague.getModel(LeagueModelEnums.boss);
                    this.lastQuitLeagueHasWeekReward = bossModel.getWeekRewardMap().getOrDefault(getPlayerId(), new HashSet<>());
                }
                this.hasBoxRewardCount = 0;
                this.todayHurt = 0;
                getPlayer().getCombatAdditionModel().refresh(false, CombatAdditionFunction.leagueBossPal);
                break;
            case joinLeague:
                League league = LeagueManager.getLeagueByPlayer(getPlayer());
                LeagueBossModel bossModel = league.getModel(LeagueModelEnums.boss);
                this.hasBoxRewardCount = bossModel.getTotalBoxCount();
                bossModel.getWeekRewardMap().put(getPlayerId(), lastQuitLeagueHasWeekReward);
                this.lastQuitLeagueHasWeekReward.clear();
                getPlayer().getCombatAdditionModel().refresh(false, CombatAdditionFunction.leagueBossPal);
                break;
        }
    }

    private void day5Refresh() {
        resetTodaySecond();
        this.todayHurt = 0;
        this.todayBossId = 0;
    }

    public void resetTodaySecond() {
        LeagueBossConstant constant = LeagueBossService.getConstant();
        this.remainChallengeSecond = constant.todayChallengeSecond;
    }

    // getter setter


    public int getRemainChallengeSecond() {
        return remainChallengeSecond;
    }

    public void setRemainChallengeSecond(int remainChallengeSecond) {
        this.remainChallengeSecond = remainChallengeSecond;
    }

    public long getTodayHurt() {
        return todayHurt;
    }

    public int getHasBoxRewardCount() {
        return hasBoxRewardCount;
    }

    public int getTodayBossId() {
        return todayBossId;
    }

    public void setTodayBossId(int todayBossId) {
        this.todayBossId = todayBossId;
    }
}

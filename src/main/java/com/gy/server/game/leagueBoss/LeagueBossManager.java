package com.gy.server.game.leagueBoss;

import com.gy.server.core.thread.AbstractRunner;
import com.gy.server.game.combat.CombatManager;
import com.gy.server.game.combat.script.CbtRecordGeter;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.league.LeagueModelEnums;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.packet.PbLeagueBoss;
import com.gy.server.packet.PbProtocol;

import java.util.*;

/**
 * 世界BOSS管理类
 */
public class LeagueBossManager extends AbstractRunner {

    private static final LeagueBossManager instance = new LeagueBossManager();

    public static LeagueBossManager getInstance() {
        return instance;
    }

    @Override
    public String getRunnerName() {
        return "LeagueBossManager";
    }

    @Override
    protected void subRunnerExecute() throws Exception {
        List<League> leagues = LeagueManager.getLeagues();
        for (League league : leagues) {
            LeagueBossModel leagueBossModel = league.getModel(LeagueModelEnums.boss);
            Map<Integer, Set<Long>> fightingPlayerMap = leagueBossModel.getFightingPlayerMap();
            for (Map.Entry<Integer, Set<Long>> entry : fightingPlayerMap.entrySet()) {
                Integer bossId = entry.getKey();
                List<PbLeagueBoss.FightingHurtSync> syncList = new ArrayList<>();
                for (Long fightPlayerId : entry.getValue()) {
                    long fightingHurt = CombatManager.getStageRecordByPlayerId(fightPlayerId, true, CbtRecordGeter.getAtk.getId());
                    if (fightingHurt <= 0) {
                        continue;
                    }
                    leagueBossModel.addFightHurt(fightPlayerId, bossId, fightingHurt);
                    Player player = PlayerManager.getOnlinePlayer(fightPlayerId);
                    if (Objects.isNull(player)) {
                        continue;
                    }
                    PbLeagueBoss.FightingHurtSync.Builder syncBuilder = PbLeagueBoss.FightingHurtSync.newBuilder()
                            .setHurt(leagueBossModel.getTodayHurtRankNode(fightPlayerId, bossId).getTotalHurt())
                            .setName(player.getName())
                            .setPlayerId(fightPlayerId);
                    syncList.add(syncBuilder.build());
                }
                PbProtocol.LeagueBossFightHurtChangeSync.Builder rst = PbProtocol.LeagueBossFightHurtChangeSync.newBuilder();
                rst.addAllHurtSyncInfo(syncList);
                Set<Long> fightingPlayerSet = leagueBossModel.getFightingPlayer(bossId);
                for (Long fightPlayerId : fightingPlayerSet) {
                    Player fightPlayer = PlayerManager.getOnlinePlayer(fightPlayerId);
                    if (Objects.isNull(fightPlayer)) {
                        continue;
                    }
                    fightPlayer.send(PtCode.LEAGUE_BOSS_FIGHT_HURT_CHANGE_SYNC, rst.build());
                }
            }
        }
    }

    @Override
    public long getRunnerInterval() {
        return 5000L;
    }
}

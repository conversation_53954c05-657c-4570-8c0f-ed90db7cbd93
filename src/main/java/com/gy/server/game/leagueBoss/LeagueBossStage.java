package com.gy.server.game.leagueBoss;

import com.gy.server.common.util.CommonUtils;
import com.gy.server.game.attribute.AttributeKey;
import com.gy.server.game.attribute.Attributes;
import com.gy.server.game.battleCollect.BattleCollectService;
import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.combat.CbtRecord;
import com.gy.server.game.combat.StageType;
import com.gy.server.game.combat.cb.Combat;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.combat.unit.TeamUnit;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.hero.Hero;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.league.LeagueModelEnums;
import com.gy.server.game.league.LeagueService;
import com.gy.server.game.league.log.LeagueLogType;
import com.gy.server.game.league.template.LeagueTGTypeEnums;
import com.gy.server.game.leagueBoss.bean.HeroHurtInfo;
import com.gy.server.game.leagueBoss.bean.HurtRankDetailNode;
import com.gy.server.game.leagueBoss.bean.LeagueBossInfo;
import com.gy.server.game.leagueBoss.template.LeagueBossTemplate;
import com.gy.server.game.liberty.LibertyHelper;
import com.gy.server.game.liberty.effect.LibertyType;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.monster.MonsterService;
import com.gy.server.game.monster.MonsterTemplate;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.reddot.RedDot;
import com.gy.server.game.world.World;
import com.gy.server.game.world.WorldLevelUseTypeEnum;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.CollectionUtil;

import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 帮派BOSS战场
 */
public class LeagueBossStage extends AbstractStage {

    private final Player player;
    /**
     * BossId
     */
    private final int bossId;
    private int bossLv;
    private final LineupType lineupType;
    League league;

    public LeagueBossStage(Player player, int bossId) {
        this.player = player;
        this.bossId = bossId;
        league = LeagueManager.getLeagueByPlayer(player);
        LeagueBossModel leagueBossModel = league.getModel(LeagueModelEnums.boss);
        LeagueBossInfo bossInfo = leagueBossModel.getBossInfo(bossId);
        this.bossLv = bossInfo.getLevel();
        LeagueBossTemplate leagueBossTemplate = LeagueBossService.getLeagueBossTemplateMap().get(bossId);
        lineupType = LineupType.getType(leagueBossTemplate.lineupId);
    }

    @Override
    public void init() {
        LeagueBossTemplate leagueBossTemplate = LeagueBossService.getLeagueBossTemplateMap().get(bossId);
        List<HeroUnit> atkUnits = player.getLineupModel().createHeroUnits(StageType.leagueBoss, lineupType);
        //增加伤害
        String tgAddedBuff = league.getTGAddedNumStr(LeagueTGTypeEnums.门客试炼中侠客伤害增加);
        if(!tgAddedBuff.isEmpty()){
            String[] buffInfos = tgAddedBuff.split(":");
            for (HeroUnit atkUnit : atkUnits) {
                atkUnit.addBuff(Integer.parseInt(buffInfos[0]), Integer.parseInt(buffInfos[1]));
            }
        }

        TeamUnit attackerTeam = new TeamUnit(getNewId(), atkUnits);
        List<TeamUnit> atkTeams = new ArrayList<>();
        atkTeams.add(attackerTeam);
        League league = LeagueManager.getLeagueByPlayer(player);
        LeagueBossModel leagueBossModel = league.getModel(LeagueModelEnums.boss);
        LeagueBossInfo bossInfo = leagueBossModel.getBossInfo(bossId);
        int worldLevel = WorldLevelUseTypeEnum.leagueBoss.getWorldLevel();
        List<TeamUnit> defenderTeams = BattleCollectService.createTeamUnits(this, worldLevel, leagueBossTemplate.battleCollect);
        HeroUnit bossUnit = BattleCollectService.findBossUnit(defenderTeams);
        if (Objects.nonNull(bossUnit)) {
            initHeroUnit(bossUnit, bossInfo.getLevel(), bossInfo.getCurrentHp());
        }
        init(leagueBossTemplate.battleCollect, StageType.leagueBoss, atkTeams, defenderTeams, lineupType);
        leagueBossModel.addFightingPlayer(player, bossId);
    }

    public static void initHeroUnit(HeroUnit bossUnit, int level, long curHp){
        bossUnit.setLevel(level);
        //计算血量加成
        long hpRate = LeagueBossService.getHpRate(level);
        Attributes attributes = bossUnit.getAttributes();
        Long maxHp = attributes.getValue(AttributeKey.最大生命);
        long finalHp = maxHp + maxHp * hpRate / 10000L;
        if(curHp < 0){
            curHp = finalHp;
        }
        attributes.setHp(curHp);
        attributes.setValue(AttributeKey.最大生命, finalHp);

    }

    @Override
    public void afterFinish() {

        // 不用管失败胜利,计算累计伤害
        List<TeamUnit> atkList = getAtks();
        List<TeamUnit> defList = getDefs();
        if (Objects.isNull(atkList) || Objects.isNull(defList)) {
            return;
        }
        TeamUnit atkUnit = atkList.get(0);
        long totalHurt = 0;
        List<HeroHurtInfo> heroHurtInfoList = new ArrayList<>();
        for (HeroUnit unit : atkUnit.getUnitsExtendTeamSoul()) {
            if (Objects.nonNull(unit)) {
                CbtRecord cbtRecord = getCbtRecord(atkUnit.getTeamId(), unit.getId());
                long hurt = cbtRecord.getAtk();
                totalHurt += hurt;
                int heroId = unit.getInstanceId();
                HeroHurtInfo heroHurtInfo;
                if (heroId == -1) {
                    heroHurtInfo = new HeroHurtInfo(player, hurt);
                } else {
                    Hero hero = player.getBagModel().getHeroById(heroId);
                    heroHurtInfo = new HeroHurtInfo(hero, hurt);
                }
                heroHurtInfoList.add(heroHurtInfo);
            }
        }
        League league = LeagueManager.getLeagueByPlayer(player);
        LeagueBossModel leagueBossModel = league.getModel(LeagueModelEnums.boss);
        // 移除战斗中的数据,增加排行榜数据
        leagueBossModel.addHurt(player, totalHurt, bossId);
        RedDot.leagueBossHurtRewardDot.sync(player);
        // 添加阵容数据
        Map<Long, Map<Integer, HurtRankDetailNode>> lastHurtInfoMap = leagueBossModel.getLastHurtInfoMap();
        Map<Integer, HurtRankDetailNode> hurtRankDetailNodeMap = lastHurtInfoMap.computeIfAbsent(player.getPlayerId(), map1 -> new HashMap<>());
        hurtRankDetailNodeMap.put(bossId, new HurtRankDetailNode(player, bossId, heroHurtInfoList));
        // 减去战斗回合数
        PlayerLeagueBossModel playerLeagueBossModel = player.getModel(PlayerModelEnums.leagueBoss);
        playerLeagueBossModel.costChallengeTime(getTimer().roundIndex);

        HeroUnit defUnit = BattleCollectService.findBossUnit(defList);
        if (Objects.nonNull(defUnit)) {
            // 记录死亡次数
            LeagueBossInfo currentBossInfo = leagueBossModel.getBossInfo(bossId);
            int oldLevel = currentBossInfo.getLevel();
            //根据伤害计算等级
            calNewLevel(currentBossInfo, totalHurt);
            int newLv = currentBossInfo.getLevel();
            // 新等级大于当前当前才变化避免重复变化
            if (newLv > oldLevel) {
                currentBossInfo.addDeadCount(newLv - oldLevel);
                //帮派玩家同步红点
                for (Long memberId : league.getAllMember()) {
                    Player onlinePlayer = PlayerManager.getOnlinePlayer(memberId);
                    if(Objects.nonNull(onlinePlayer)){
                        RedDot.leagueBossRewardDot.sync(onlinePlayer);
                    }
                }
                //记录日志
                league.addLeagueLog(LeagueLogType.bossLv, bossId + "", currentBossInfo.getShowLevel() + "");
            }
        }
        // 记录挑战伤害
        if (totalHurt > 0) {
            league.addLeagueLog(LeagueLogType.bossHurt , player.getName(), bossId + "", totalHurt + "");
        }
        PbProtocol.CombatSettlementNotify.Builder rst = genCombatSettlement();
        // 挑战时间完毕发奖
        if (playerLeagueBossModel.getRemainChallengeSecond() <= 0) {

            //额外奖励
            int tgAddedNumInt = league.getTGAddedNumInt(LeagueTGTypeEnums.门客试炼结束后有固定概率获得1个帮派宝箱最多触发X次, 0);
            List<Reward> extraRewards = new ArrayList<>();
            for(int i = 0; i < tgAddedNumInt; i++){
                if(CommonUtils.randomProbability(LeagueService.getConstant().TechnologyFifteenNum)){
                    extraRewards.add(LeagueService.getConstant().LeagueBox.createReward());
                }
            }
            if(CollectionUtil.isNotEmpty(extraRewards)){
                Reward.merge(extraRewards);

                //特权
                LibertyHelper.checkCurrencyRewardLiberty(LibertyType.leagueRate, player, extraRewards);

                Reward.add(extraRewards, player, BehaviorType.leagueBossChallengeDropExtr);
//                rst.addAllExtraRewards(Reward.writeCollectionToPb(extraRewards));
                rst.addAllRewards(Reward.writeCollectionToPb(extraRewards));
            }
        }

//        RedDot.leagueBossFight.sync(player);
        rst.setLeagueBoss(PbProtocol.LeagueBossSettlement.newBuilder().setBossInfo(playerLeagueBossModel.genMainPb(bossId)).build());

        notifyCombatSettlement(player, rst.build());
        leagueBossModel.removeFightingPlayer(player, bossId);

        player.postEvent(PlayerEventType.leagueBoss);
    }

    /**
     * 根据伤害和当前boss信息，计算最终等级和血量
     */
    private void calNewLevel(LeagueBossInfo currentBossInfo, long hurt){
        if(currentBossInfo.getCurrentHp() > hurt){
            //能扣掉伤害，直接扣
            currentBossInfo.setCurrentHp(currentBossInfo.getCurrentHp() - hurt);
        }else{
            //开始根据伤害计算等级
            //减去伤害
            hurt = hurt - currentBossInfo.getCurrentHp();
            //升级并且满血
            currentBossInfo.setLevel(currentBossInfo.getLevel() + 1);
            int monsterTemplateId = BattleCollectService.findBossId(LeagueBossService.getLeagueBossTemplateMap().get(this.bossId).battleCollect);
            MonsterTemplate monsterTemplate = MonsterService.monsterTemplates.get(monsterTemplateId);
            Attributes attributes = monsterTemplate.getAttributes(currentBossInfo.getLevel());
            long hpRate = LeagueBossService.getHpRate(currentBossInfo.getLevel());
            Long maxHp = attributes.getValue(AttributeKey.最大生命);
            long finalHp = maxHp + maxHp * hpRate / 10000L;
            currentBossInfo.setCurrentHp(finalHp);
            calNewLevel(currentBossInfo, hurt);
        }
    }

    @Override
    public void unitLevelDeal(HeroUnit unit, Combat context) {
        bossLv++;
        //恢复满血
        initHeroUnit(unit, bossLv, -1);
    }

    @Override
    public List<Long> needSyncDefHpPlayerId() {
        League league = LeagueManager.getLeagueByPlayer(player);
        LeagueBossModel leagueBossModel = league.getModel(LeagueModelEnums.boss);
        return new CopyOnWriteArrayList<>(leagueBossModel.getFightingPlayer(bossId));
    }

    @Override
    public int getMax_round() {
        PlayerLeagueBossModel playerLeagueBossModel = player.getModel(PlayerModelEnums.leagueBoss);
        return playerLeagueBossModel.getRemainChallengeSecond();
    }

    @Override
    public boolean isAutoReviveMode() {
        return true;
    }
}

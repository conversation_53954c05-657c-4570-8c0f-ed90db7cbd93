package com.gy.server.game.leagueBoss;

import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.battleCollect.BattleCollectService;
import com.gy.server.game.combat.CombatManager;
import com.gy.server.game.combat.Stage;
import com.gy.server.game.common.ConstantConfigReader;
import com.gy.server.game.currency.Currency;
import com.gy.server.game.drop.DropService;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.function.Function;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.item.ItemService;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.league.LeagueModelEnums;
import com.gy.server.game.league.enums.LeagueJobTypeEnums;
import com.gy.server.game.league.log.LeagueLogType;
import com.gy.server.game.league.template.LeagueTGTypeEnums;
import com.gy.server.game.leagueBoss.bean.HurtRankDetailNode;
import com.gy.server.game.leagueBoss.bean.HurtRankNode;
import com.gy.server.game.leagueBoss.template.LeagueBossConstant;
import com.gy.server.game.leagueBoss.template.LeagueBossPalTemplate;
import com.gy.server.game.leagueBoss.template.LeagueBossTemplate;
import com.gy.server.game.liberty.LibertyHelper;
import com.gy.server.game.liberty.effect.LibertyType;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.game.reddot.RedDot;
import com.gy.server.game.service.Service;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbLeagueBoss;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.runner.RunnerManager;
import com.ttlike.server.tl.baselib.serialize.combatAddition.CombatAdditions;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 帮派BOSS
 */
public class LeagueBossService extends PlayerPacketHandler implements Service {

    private static LeagueBossConstant constant;

    /**
     * 个人伤害日奖励
     * Key:伤害 Value:奖励模板
     */
    private static TreeMap<Long, List<RewardTemplate>> dailyHurtRewardMap = new TreeMap<>();
    /**
     * BOSSMap
     */
    private static Map<Integer, LeagueBossTemplate> leagueBossTemplateMap = new HashMap<>();
    /**
     * 门客堂等级表
     */
    private static Map<Integer, LeagueBossPalTemplate> leagueBossPalTemplateMap = new HashMap<>();
    /**
     * 门客堂最高等级
     */
    private static int maxPalLevel;

    /**
     * boss血量加成
     */
    private static Map<Integer, Integer> bossLevelHpInfos = new HashMap<>();
    private static int maxHpLevel;

    private int beforeCheck(Player player) {
        if (!Function.leagueBoss.isOpen(player)) {
            return Text.功能未开启;
        }
        // 是否加入帮派
        if (LeagueManager.isNotJoinLeague(player)) {
            return Text.未加入帮派;
        }
        return Text.没有异常;
    }

    /**
     * 排行榜信息
     */
    @Handler(PtCode.LEAGUE_BOSS_RANK_CLIENT)
    private void bossRank(Player player, PbProtocol.LeagueBossRankReq req, long time) {
        PbProtocol.LeagueBossRankRst.Builder rst = PbProtocol.LeagueBossRankRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        logic:
        {
            int type = req.getType();
            if (type != 1 && type != 2) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            int bossId = req.getBossId();
            if (bossId != -1 && !leagueBossTemplateMap.containsKey(bossId)) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            int result = beforeCheck(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            LeagueBossModel leagueBossModel = league.getModel(LeagueModelEnums.boss);
            List<HurtRankNode> rankList;
            if (type == 1) {
                if (bossId == -1) {
                    rankList = new ArrayList<>(leagueBossModel.getTodayHurtMap().values());
                } else {
                    rankList = leagueBossModel.getTodayRankMap().getOrDefault(bossId, new ArrayList<>());
                }
            } else {
                if (bossId == -1) {
                    rankList = new ArrayList<>(leagueBossModel.getTotalWeekHurtMap().values());
                } else {
                    rankList = leagueBossModel.getWeekRankMap().getOrDefault(bossId, new ArrayList<>());
                }
            }
            Collections.sort(rankList);
            int rank = 1;
            for (HurtRankNode node : rankList) {
                PbLeagueBoss.HurtRankNode nodePb = node.genPb().setRank(rank++).build();
                rst.addRankNode(nodePb);
                if (node.getPlayerId() == player.getPlayerId()) {
                    rst.setMyNode(nodePb);
                }
            }
            rst.setType(type);
        }
        player.send(PtCode.LEAGUE_BOSS_RANK_SERVER, rst.build(), time);
    }

    /**
     * 排行榜详细战况信息
     */
    @Handler(PtCode.LEAGUE_BOSS_RANK_DETAIL_CLIENT)
    private void bossRankDetail(Player player, PbProtocol.LeagueBossRankDetailReq req, long time) {
        PbProtocol.LeagueBossRankDetailRst.Builder rst = PbProtocol.LeagueBossRankDetailRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        logic:
        {
            int result = beforeCheck(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }
            int bossId = req.getBossId();
            if (!leagueBossTemplateMap.containsKey(bossId)) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            LeagueBossModel leagueBossModel = league.getModel(LeagueModelEnums.boss);
            List<HurtRankNode> rankList = leagueBossModel.getTodayRankMap().getOrDefault(bossId, new ArrayList<>());
            Collections.sort(rankList);
            Map<Long, Map<Integer, HurtRankDetailNode>> lastHurtInfoMap = leagueBossModel.getLastHurtInfoMap();
            int rank = 1;
            for (HurtRankNode node : rankList) {
                Map<Integer, HurtRankDetailNode> hurtRankDetailNodeMap = lastHurtInfoMap.get(node.getPlayerId());
                if (hurtRankDetailNodeMap == null) {
                    continue;
                }
                HurtRankDetailNode hurtRankDetailNode = hurtRankDetailNodeMap.get(bossId);
                PbLeagueBoss.HurtRankDetailNode hurtRankDetailNodePb = hurtRankDetailNode.genPb(rank++, node.getHurt());
                rst.addRankNode(hurtRankDetailNodePb);
                if (node.getPlayerId() == player.getPlayerId()) {
                    rst.setMyNode(hurtRankDetailNodePb);
                }
            }
        }
        player.send(PtCode.LEAGUE_BOSS_RANK_DETAIL_SERVER, rst.build(), time);
    }

    /**
     * 挑战
     */
    @Handler(PtCode.LEAGUE_BOSS_FIGHT_CLIENT)
    private void fight(Player player, PbProtocol.LeagueBossFightReq req, long time) {
        PbProtocol.LeagueBossFightRst.Builder rst = PbProtocol.LeagueBossFightRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        Stage stage = null;
        logic:
        {
            int result = beforeCheck(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }
            int bossId = req.getBossId();
            if (!leagueBossTemplateMap.containsKey(bossId)) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            // 刷新点前X分钟不能挑战避免跨天挑战
            LocalDateTime now = ServerConstants.getCurrentTimeLocalDateTime();
            LocalDateTime refreshTime = now.withHour(CommonUtils.getRefreshTimeHour()).withMinute(0).withSecond(0).withNano(0);
            LocalDateTime settlementTime = refreshTime.plusSeconds(-constant.todayChallengeSecond);
            if (!now.isBefore(settlementTime) && !now.isAfter(refreshTime)) {
                rst.setResult(Text.genServerRstInfo(Text.不在挑战时间内));
                break logic;
            }
            PlayerLeagueBossModel playerLeagueBossModel = player.getModel(PlayerModelEnums.leagueBoss);
            if (playerLeagueBossModel.getRemainChallengeSecond() <= 0) {
                rst.setResult(Text.genServerRstInfo(Text.今日无挑战时间));
                break logic;
            }
            int todayBossId = playerLeagueBossModel.getTodayBossId();
            if (todayBossId != 0 && todayBossId != bossId) {
                rst.setResult(Text.genServerRstInfo(Text.每天只能挑战一个BOSS));
                break logic;
            }
            LeagueBossTemplate leagueBossTemplate = leagueBossTemplateMap.get(bossId);
            int bossMonsterId = BattleCollectService.findBossId(leagueBossTemplate.battleCollect);
            if (bossMonsterId == -1) {
                rst.setResult(Text.genServerRstInfo(Text.帮派BOSS不存在));
                break logic;
            }
            LineupType lineupType = LineupType.getType(leagueBossTemplate.lineupId);
            // 判断阵容
            if (Objects.isNull(lineupType) || lineupType.nullCheck(player)) {
                rst.setResult(Text.genServerRstInfo(Text.帮派BOSS进攻阵容为空));
                break logic;
            }
            stage = new LeagueBossStage(player, bossId);
            stage.init();
            rst.setStage(stage.getStageRecord());
            playerLeagueBossModel.setTodayBossId(bossId);
        }
        player.send(PtCode.LEAGUE_BOSS_FIGHT_SERVER, rst.build(), time);
        if (Objects.nonNull(stage)) {
            long leagueId = LeagueManager.getLeagueId(player);
            // 根据帮派ID指定战斗线程运行
            CombatManager.combatPrepare(stage, leagueId);
        }
    }

    /**
     * 领取宝箱奖励
     */
    @Handler(PtCode.LEAGUE_BOSS_BOX_REWARD_CLIENT)
    private void boxReward(Player player, long time) {
        PbProtocol.LeagueBossBoxRewardRst.Builder rst = PbProtocol.LeagueBossBoxRewardRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        logic:
        {
            int result = beforeCheck(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            LeagueBossModel leagueBossModel = league.getModel(LeagueModelEnums.boss);
            int totalBoxCount = leagueBossModel.getTotalBoxCount();
            if (totalBoxCount <= 0) {
                rst.setResult(Text.genServerRstInfo(Text.没有可领取的宝箱));
                break logic;
            }
            PlayerLeagueBossModel playerLeagueBossModel = player.getModel(PlayerModelEnums.leagueBoss);
            int hasBoxRewardCount = playerLeagueBossModel.getHasBoxRewardCount();
            if (hasBoxRewardCount >= totalBoxCount) {
                rst.setResult(Text.genServerRstInfo(Text.没有可领取的宝箱));
                break logic;
            }
            playerLeagueBossModel.setHasBoxRewardCount(totalBoxCount);
            List<Reward> allReward = new ArrayList<>();
            for (int i = 0; i < totalBoxCount - hasBoxRewardCount; i++) {
                List<Reward> reward = DropService.executeDrop(player, constant.randomReward, BehaviorType.leagueBossBoxReward, false, false);
                allReward.addAll(reward);
            }
            Reward.merge(allReward);
            //特权
            LibertyHelper.checkCurrencyRewardLiberty(LibertyType.leagueRate, player, allReward);

            Reward.add(allReward, player, BehaviorType.leagueBossBoxReward);

            RedDot.leagueBossRewardDot.sync(player);

            rst.addAllReward(Reward.writeCollectionToPb(allReward))
                    .setHasDrawBoxCount(playerLeagueBossModel.getHasBoxRewardCount());
        }
        player.send(PtCode.LEAGUE_BOSS_BOX_REWARD_SERVER, rst.build(), time);
    }

    public static void rewardTgAdded(League league, List<Reward> rewards){

        int medalAddedNum = Objects.isNull(league) ? 0 : league.getTGAddedNumInt(LeagueTGTypeEnums.增加门客试炼中勋章的获取数量, 0);
        if(medalAddedNum > 0){
            rewards.add(Reward.create(Currency.medal.getId(), -1, medalAddedNum));
        }
    }

    /**
     * 门课堂主界面信息
     */
    @Handler(PtCode.LEAGUE_BOSS_PAL_MAIN_INFO_CLIENT)
    private void bossPalMainInfo(Player player, long time) {
        PbProtocol.LeagueBossPalMainInfoRst.Builder rst = PbProtocol.LeagueBossPalMainInfoRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        logic:
        {
            int result = beforeCheck(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }
            if(LeagueManager.canNotPlayLeague(player)){
                rst.setResult(Text.genServerRstInfo(Text.您退出帮派未满足24小时无法参与帮派活动));
                break logic;
            }
            PlayerLeagueBossModel playerLeagueBossModel = player.getModel(PlayerModelEnums.leagueBoss);
            rst.setMainInfo(playerLeagueBossModel.genPalMainPb());
        }
        player.send(PtCode.LEAGUE_BOSS_PAL_MAIN_INFO_SERVER, rst.build(), time);
    }

    /**
     * 门客堂提交道具
     */
    @Handler(PtCode.LEAGUE_BOSS_PAL_HAND_ITEM_CLIENT)
    private void bossPalHandItem(Player player, PbProtocol.LeagueBossPalHandItemReq req, long time) {
        PbProtocol.LeagueBossPalHandItemRst.Builder rst = PbProtocol.LeagueBossPalHandItemRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        logic:
        {
            int result = beforeCheck(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }
            Map<Integer, Integer> itemMapMap = req.getItemMapMap();
            //检测参数
            for (Map.Entry<Integer, Integer> entry : itemMapMap.entrySet()) {
                Integer itemId = entry.getKey();
                Integer itemCount = entry.getValue();
                if (!constant.leagueBossExpMap.containsKey(itemId)) {
                    rst.setResult(Text.genServerRstInfo(Text.参数异常));
                    break logic;
                }
                if (itemCount <= 0 || Objects.isNull(ItemService.getItemTemplate(itemId))) {
                    rst.setResult(Text.genServerRstInfo(Text.参数异常));
                    break logic;
                }
                if (itemCount > player.getBagModel().getItemCount(itemId)) {
                    rst.setResult(Text.genServerRstInfo(Text.消耗不足));
                    break logic;
                }
            }
            //判断等级
            League league = LeagueManager.getLeagueByPlayer(player);
            LeagueBossModel leagueBossModel = league.getModel(LeagueModelEnums.boss);
            int palLevel = leagueBossModel.getPalLevel();
            //策划说肯定不会满级 这边做个限制满级不能提交了
            if (palLevel >= maxPalLevel) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            long totalAddExp = 0;
            int totalCount = 0;
            //扣除道具
            for (Map.Entry<Integer, Integer> entry : itemMapMap.entrySet()) {
                Integer itemId = entry.getKey();
                Integer itemCount = entry.getValue();
                player.getBagModel().removeItem(itemId, itemCount, BehaviorType.leagueBossHandItem);
                totalAddExp += (long) constant.leagueBossExpMap.get(itemId) * itemCount;
                totalCount += itemCount;
            }
            //增加经验
            leagueBossModel.addPalExp(totalAddExp);
            rst.setPalExp(leagueBossModel.getPalExp())
                    .setPalLevel(leagueBossModel.getPalLevel());
            //记录日志
            league.addLeagueLog(LeagueLogType.bossHandItem, player.getName(), totalCount + "");
        }
        player.send(PtCode.LEAGUE_BOSS_PAL_HAND_ITEM_SERVER, rst.build(), time);
    }

    /**
     * 门客堂标记BOSS
     */
    @Handler(PtCode.LEAGUE_BOSS_MARKING_CLIENT)
    private void bossMarking(Player player, PbProtocol.LeagueBossMarkingReq req, long time) {
        PbProtocol.LeagueBossMarkingRst.Builder rst = PbProtocol.LeagueBossMarkingRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        logic:
        {
            int result = beforeCheck(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }
            int bossId = req.getBossId();
            if (!leagueBossTemplateMap.containsKey(bossId)) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            //判断等级
            League league = LeagueManager.getLeagueByPlayer(player);
            if (!league.hasAuth(player.getPlayerId(), LeagueJobTypeEnums.玩法指挥)) {
                rst.setResult(Text.genServerRstInfo(Text.标记帮派BOSS权限不足));
                break logic;
            }
            LeagueBossModel leagueBossModel = league.getModel(LeagueModelEnums.boss);
            if (req.getIsMarking()) {
                leagueBossModel.setMarkingBossId(bossId);
            } else {
                leagueBossModel.setMarkingBossId(-1);
            }
            rst.setBossId(bossId)
                    .setIsMarking(req.getIsMarking());
        }
        player.send(PtCode.LEAGUE_BOSS_MARKING_SERVER, rst.build(), time);
    }

    /**
     * 门客堂周伤害奖励领取
     */
    @Handler(PtCode.LEAGUE_BOSS_WEEK_REWARD_CLIENT)
    private void weekReward(Player player, PbProtocol.LeagueBossWeekRewardReq req, long time) {
        PbProtocol.LeagueBossWeekRewardRst.Builder rst = PbProtocol.LeagueBossWeekRewardRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        logic:
        {
            int result = beforeCheck(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }
            long hurt = req.getHurt();
            if (!dailyHurtRewardMap.containsKey(hurt)) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            LeagueBossModel leagueBossModel = league.getModel(LeagueModelEnums.boss);
            long weekHurt = leagueBossModel.getWeekHurt(player.getPlayerId());
            if (weekHurt < hurt) {
                rst.setResult(Text.genServerRstInfo(Text.伤害不足不可以领取奖励));
                break logic;
            }
            Map<Long, Set<Long>> weekRewardMap = leagueBossModel.getWeekRewardMap();
            Set<Long> rewardSet = weekRewardMap.computeIfAbsent(player.getPlayerId(), set1 -> new HashSet<>());
            if (rewardSet.contains(hurt)) {
                rst.setResult(Text.genServerRstInfo(Text.周伤害奖励已领取));
                break logic;
            }
            List<Reward> rewardList = new ArrayList<>();
            //一键领取
            for (Long rewardHurt : dailyHurtRewardMap.keySet()) {
                if(weekHurt >= rewardHurt && !rewardSet.contains(rewardHurt)){
                    rewardSet.add(rewardHurt);
                    List<RewardTemplate> rewardTemplates = dailyHurtRewardMap.get(rewardHurt);
                    List<Reward> weekReward = Reward.templateCollectionToReward(rewardTemplates);
                    rewardTgAdded(league, weekReward);
                    rewardList.addAll(weekReward);
                }
            }
            //特权
            LibertyHelper.checkCurrencyRewardLiberty(LibertyType.leagueRate, player, rewardList);
            Reward.merge(rewardList);
            Reward.add(rewardList, player, BehaviorType.leagueBossWeekReward);
            rst.addAllReward(Reward.writeCollectionToPb(rewardList))
                    .setHurt(hurt)
                    .addAllHadReceiveHurt(rewardSet);
            RedDot.leagueBossHurtRewardDot.sync(player);
        }
        player.send(PtCode.LEAGUE_BOSS_WEEK_REWARD_SERVER, rst.build(), time);
    }


    @Override
    public void startup() throws Exception {
        // 启动线程
        LeagueBossManager instance = LeagueBossManager.getInstance();
        RunnerManager.addRunner(instance, instance.getRunnerName(), false);
    }

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        Map<String, String> constantMap = ConstantConfigReader.read(ConfigFile.leagueBoss_constant);
        constant = new LeagueBossConstant(constantMap);
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.leagueBoss_hurtReward);
        TreeMap<Long, List<RewardTemplate>> weekHurtRewardMapTemp = new TreeMap<>();
        for (Map<String, String> map : mapList) {
            weekHurtRewardMapTemp.put(Long.parseLong(map.get("id")), RewardTemplate.readListFromText(map.get("reward")));
        }
        dailyHurtRewardMap = weekHurtRewardMapTemp;
        mapList = ConfigReader.read(ConfigFile.leagueBoss_leagueBoss);
        Map<Integer, LeagueBossTemplate> leagueBossTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            LeagueBossTemplate leagueBossTemplate = new LeagueBossTemplate();
            leagueBossTemplate.id = Integer.parseInt(map.get("id"));
            leagueBossTemplate.battleCollect = Integer.parseInt(map.get("battleCollect"));
            leagueBossTemplate.lineupId = Integer.parseInt(map.get("lineUpId"));
            leagueBossTemplateMapTemp.put(leagueBossTemplate.id, leagueBossTemplate);
        }
        leagueBossTemplateMap = leagueBossTemplateMapTemp;

        mapList = ConfigReader.read(ConfigFile.leagueBoss_leagueBossPal);
        Map<Integer, LeagueBossPalTemplate> leagueBossPalTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            LeagueBossPalTemplate leagueBossPalTemplate = new LeagueBossPalTemplate();
            leagueBossPalTemplate.lv = Integer.parseInt(map.get("id"));
            leagueBossPalTemplate.exp = Integer.parseInt(map.get("shikigamiPalExp"));
            leagueBossPalTemplate.combatAdditions = CombatAdditions.readFromStr(map.get("combatAdditions"));
            leagueBossPalTemplateMapTemp.put(leagueBossPalTemplate.lv, leagueBossPalTemplate);
        }
        leagueBossPalTemplateMap = leagueBossPalTemplateMapTemp;
        maxPalLevel = Collections.max(leagueBossPalTemplateMap.keySet());

        mapList = ConfigReader.read(ConfigFile.leagueBoss_BossLevelHp);
        Map<Integer, Integer> bossLevelHpTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            bossLevelHpTemp.put(Integer.parseInt(map.get("id")), Integer.parseInt(map.get("hp")));
        }
        bossLevelHpInfos = bossLevelHpTemp;
        maxHpLevel = Collections.max(bossLevelHpTemp.keySet());
    }

    @Override
    public void clearConfigData() {
        dailyHurtRewardMap.clear();
        leagueBossTemplateMap.clear();
        leagueBossPalTemplateMap.clear();
    }

    public static long getHpRate(int level){
        return bossLevelHpInfos.get(Math.min(maxHpLevel, level));
    }

    // getter
    public static LeagueBossConstant getConstant() {
        return constant;
    }

    public static TreeMap<Long, List<RewardTemplate>> getDailyHurtRewardMap() {
        return dailyHurtRewardMap;
    }

    public static Map<Integer, LeagueBossTemplate> getLeagueBossTemplateMap() {
        return leagueBossTemplateMap;
    }

    public static Map<Integer, LeagueBossPalTemplate> getLeagueBossPalTemplateMap() {
        return leagueBossPalTemplateMap;
    }

    public static int getMaxPalLevel() {
        return maxPalLevel;
    }
}

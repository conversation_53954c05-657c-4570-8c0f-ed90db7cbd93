package com.gy.server.game.leagueBoss;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CopyOnWriteArraySet;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.combataddition.CombatAdditionFunction;
import com.gy.server.game.combataddition.PlayerCombatAdditionModel;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.league.*;
import com.gy.server.game.league.bean.LeagueMember;
import com.gy.server.game.league.event.LeagueEvent;
import com.gy.server.game.league.event.LeagueEventHandler;
import com.gy.server.game.league.event.LeagueEventType;
import com.gy.server.game.league.log.LeagueLogType;
import com.gy.server.game.leagueBoss.bean.HurtRankDetailNode;
import com.gy.server.game.leagueBoss.bean.HurtRankNode;
import com.gy.server.game.leagueBoss.bean.LeagueBossInfo;
import com.gy.server.game.mail.MailManager;
import com.gy.server.game.mail.MailType;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.game.text.Text;
import com.gy.server.game.world.World;
import com.gy.server.game.world.WorldLevelUseTypeEnum;
import com.gy.server.packet.PbCommons;
import com.gy.server.utils.MathUtil;
import com.ttlike.server.tl.baselib.serialize.league.LeagueBlobDb;
import com.ttlike.server.tl.baselib.serialize.leagueBoss.*;

/**
 * 帮派Boss模块
 *
 * <AUTHOR> - [Created on 2023/4/20 10:44]
 */
public class LeagueBossModel extends LeagueModel implements LeagueEventHandler, LeagueDataInterface {

    /**
     * 今日伤害排行榜数据
     * Key:BossId  Value:排行榜数据
     */
    private Map<Integer, List<HurtRankNode>> todayRankMap = new ConcurrentHashMap<>();

    /**
     * 今日伤害数据
     * Key:playerId Value:伤害信息
     */
    private Map<Long, HurtRankNode> todayHurtMap = new ConcurrentHashMap<>();
    /**
     * 本周伤害排行榜数据
     * Key:BossId  Value:排行榜数据
     */
    private Map<Integer, List<HurtRankNode>> weekRankMap = new ConcurrentHashMap<>();
    /**
     * 本周伤害数据
     * Key:playerId Value:Map.key:bossId,Map.Value:伤害数据
     */
    private Map<Long, Map<Integer, HurtRankNode>> weekHurtMap = new ConcurrentHashMap<>();
    /**
     * 日累计总伤害数据
     * Key:playerId Value:伤害信息
     */
    private Map<Long, HurtRankNode> totalWeekHurtMap = new ConcurrentHashMap<>();
    /**
     * 最后一次所用阵容造成伤害数据
     * Key:playerId Value:Key:bossId Value:伤害阵容数据
     */
    private Map<Long, Map<Integer, HurtRankDetailNode>> lastHurtInfoMap = new ConcurrentHashMap<>();
    /**
     * BOSS 信息
     * Key:BossId  Value:BOSS信息
     */
    private Map<Integer, LeagueBossInfo> bossInfoMap = new ConcurrentHashMap<>();
    /**
     * 门客堂等级
     */
    private int palLevel = 1;
    /**
     * 门客堂经验
     */
    private long palExp;

    /**
     * 标记的bossId
     */
    private int markingBossId = -1;

    /**
     * 本周伤害奖励领取数据
     * Key:playerId Value:领取的伤害奖励
     */
    private Map<Long, Set<Long>> weekRewardMap = new ConcurrentHashMap<>();

    /**
     * 内存数据
     * 正在战斗的玩家
     * Key:BossId  Value:战斗的玩家信息
     */
    private Map<Integer, Set<Long>> fightingPlayer = new ConcurrentHashMap<>();


    public LeagueBossModel(League league) {
        super(league);
    }

    /**
     * 添加正在战斗的玩家
     *
     * @param player 玩家信息
     */
    public void addFightingPlayer(Player player, int bossId) {
        Set<Long> fightingSet = fightingPlayer.computeIfAbsent(bossId, set1 -> new CopyOnWriteArraySet<>());
        fightingSet.add(player.getPlayerId());
    }

    /**
     * 移除正在战斗的玩家
     *
     * @param player 玩家信息
     */
    public void removeFightingPlayer(Player player, int bossId) {
        Set<Long> fightingSet = fightingPlayer.computeIfAbsent(bossId, set1 -> new CopyOnWriteArraySet<>());
        fightingSet.remove(player.getPlayerId());
    }

    public Set<Long> getFightingPlayer(int bossId) {
        return fightingPlayer.getOrDefault(bossId, new HashSet<>());
    }

    public Map<Integer, Set<Long>> getFightingPlayerMap() {
        return fightingPlayer;
    }

    /**
     * 获取BOSS信息
     *
     * @param bossId bossId
     * @return BOSS 信息 如果没有则初始化一个
     */
    public LeagueBossInfo getBossInfo(int bossId) {
        LeagueBossInfo leagueBossInfo = bossInfoMap.get(bossId);
        if (Objects.isNull(leagueBossInfo)) {
            leagueBossInfo = new LeagueBossInfo(bossId, World.getWorldLevel());
            bossInfoMap.put(bossId, leagueBossInfo);
        }
        return leagueBossInfo;
    }

    /**
     * 获取总的宝箱开启数量
     *
     * @return 宝箱个数
     */
    public int getTotalBoxCount() {
        int totalCount = 0;
        for (LeagueBossInfo leagueBossInfo : bossInfoMap.values()) {
            totalCount += leagueBossInfo.getBoxCount();
        }
        return totalCount;
    }

    /**
     * 获取玩家本周累计伤害
     *
     * @param playerId 玩家ID
     * @return 本周累计伤害
     */
    public long getWeekHurt(long playerId) {
        Map<Integer, HurtRankNode> longHurtRankNodeMap = weekHurtMap.get(playerId);
        if (Objects.isNull(longHurtRankNodeMap)) {
            return 0;
        }
        long hurt = 0;
        for (HurtRankNode value : longHurtRankNodeMap.values()) {
            hurt += value.getHurt();
        }
        return hurt;
    }


    /**
     * 获取今日伤害排行榜
     *
     * @param bossId BossId
     * @return 排行榜信息
     */
    public List<HurtRankNode> getTodayRankList(int bossId) {
        return todayRankMap.computeIfAbsent(bossId, list1 -> new CopyOnWriteArrayList<>());
    }

    /**
     * 增加伤害
     *
     * @param player  玩家
     * @param addHurt 增加伤害
     */
    public void addHurt(Player player, long addHurt, int bossId) {
        long playerId = player.getPlayerId();
        HurtRankNode hurtRankNode = getTodayHurtRankNode(playerId, bossId);
        hurtRankNode.addHurt(addHurt);
        // 清空战斗中抛出来的伤害数据
        hurtRankNode.setFightingHurt(0);
        // 增加每周累计伤害
        HurtRankNode weekHurtRankNode = getWeekHurtRankNode(playerId, bossId);
        weekHurtRankNode.addHurt(addHurt);
        // 增加每周累计总伤害
        HurtRankNode totalWeekHurtRankNode = getTotalWeekHurtRankNode(playerId);
        totalWeekHurtRankNode.addHurt(addHurt);
        // 个人模块同步增加
        PlayerLeagueBossModel leagueBossModel = player.getModel(PlayerModelEnums.leagueBoss);
        leagueBossModel.addTodayHurt(addHurt);

    }

    public synchronized HurtRankNode getTodayHurtRankNode(long playerId, int bossId) {
        List<HurtRankNode> rankList = getTodayRankList(bossId);
        HurtRankNode hurtRankNode = todayHurtMap.get(playerId);
        if (hurtRankNode == null) {
            hurtRankNode = new HurtRankNode(playerId);
            todayHurtMap.put(playerId, hurtRankNode);
            rankList.add(hurtRankNode);
        }
        return hurtRankNode;
    }

    public HurtRankNode getWeekHurtRankNode(long playerId, int bossId) {
        List<HurtRankNode> rankList = weekRankMap.computeIfAbsent(bossId, list1 -> new ArrayList<>());
        Map<Integer, HurtRankNode> rankNodeMap = weekHurtMap.computeIfAbsent(playerId, map1 -> new HashMap<>());
        HurtRankNode hurtRankNode = rankNodeMap.get(bossId);
        if (hurtRankNode == null) {
            hurtRankNode = new HurtRankNode(playerId);
            rankNodeMap.put(bossId, hurtRankNode);
            rankList.add(hurtRankNode);
        }
        return hurtRankNode;
    }

    public HurtRankNode getTotalWeekHurtRankNode(long playerId) {
        HurtRankNode hurtRankNode = totalWeekHurtMap.get(playerId);
        if (hurtRankNode == null) {
            hurtRankNode = new HurtRankNode(playerId);
            totalWeekHurtMap.put(playerId, hurtRankNode);
        }
        return hurtRankNode;
    }

    /**
     * 增加伤害
     *
     * @param playerId     玩家ID
     * @param bossId       BossId
     * @param fightingHurt 战斗中的伤害
     */
    public void addFightHurt(long playerId, int bossId, long fightingHurt) {
        HurtRankNode hurtRankNode = getTodayHurtRankNode(playerId, bossId);
        hurtRankNode.setFightingHurt(fightingHurt);
    }

    @Override
    public LeagueEventType[] getEventTypes() {
        return new LeagueEventType[]{
                LeagueEventType.day5Refresh,
                LeagueEventType.quitLeague,
        };
    }

    @Override
    public void handle(LeagueEvent event) {
        switch (event.getEventType()) {
            case day5Refresh: {
                day5Refresh();
                break;
            }
            case quitLeague: {
                long playerId = event.getParam(0);
                quit(playerId);
                break;
            }
        }
    }

    /**
     * 退出帮派清除数据
     *
     * @param playerId 玩家ID
     */
    public void quit(long playerId) {
        //每日数据清空
        HurtRankNode remove = todayHurtMap.remove(playerId);
        if (Objects.nonNull(remove)) {
            for (List<HurtRankNode> hurtRankNodes : todayRankMap.values()) {
                if (hurtRankNodes.contains(remove)) {
                    hurtRankNodes.remove(remove);
                    break;
                }
            }
        }
        //每周数据清空
        Map<Integer, HurtRankNode> removeWeekHurtMap = weekHurtMap.remove(playerId);
        if (Objects.nonNull(removeWeekHurtMap)) {
            for (Map.Entry<Integer, HurtRankNode> nodeEntry : removeWeekHurtMap.entrySet()) {
                Integer bossId = nodeEntry.getKey();
                HurtRankNode entryValue = nodeEntry.getValue();
                List<HurtRankNode> hurtRankNodes = weekRankMap.get(bossId);
                if (Objects.nonNull(hurtRankNodes)) {
                    hurtRankNodes.remove(entryValue);
                }
            }
        }
        totalWeekHurtMap.remove(playerId);
        lastHurtInfoMap.remove(playerId);
    }

    public void day5Refresh() {
        // 每日清空排行榜
        this.todayRankMap.clear();
        this.todayHurtMap.clear();
        this.fightingPlayer.clear();
        //日奖励发送
        sendDailyReward();
        this.totalWeekHurtMap.clear();
        this.weekRewardMap.clear();
        this.weekHurtMap.clear();
        LocalDateTime nowTime = ServerConstants.getCurrentTimeLocalDateTime();
        // 周一刷新
        if (nowTime.getDayOfWeek() == DayOfWeek.MONDAY) {
            this.weekRankMap.clear();
            this.bossInfoMap.clear();

            WorldLevelUseTypeEnum.leagueBoss.refreshWorldLevel();
        }
    }

    /**
     * 周奖励发送
     */
    public void sendDailyReward() {
        TreeMap<Long, List<RewardTemplate>> weekHurtRewardMap = LeagueBossService.getDailyHurtRewardMap();
        for (Map.Entry<Long, HurtRankNode> entry : totalWeekHurtMap.entrySet()) {
            List<RewardTemplate> allReward = new ArrayList<>();
            Long playerId = entry.getKey();
            Set<Long> hasRewardSet = weekRewardMap.computeIfAbsent(playerId, set1 -> new HashSet<>());
            long hurt = entry.getValue().getHurt();
            List<Reward> rewards = new ArrayList<>();
            for (Map.Entry<Long, List<RewardTemplate>> templateEntry : weekHurtRewardMap.entrySet()) {
                Long templateHurt = templateEntry.getKey();
                // 伤害未达标
                if (hurt < templateHurt) {
                    continue;
                }
                //奖励已领取
                if (hasRewardSet.contains(templateHurt)) {
                    continue;
                }
                hasRewardSet.add(templateHurt);
                allReward.addAll(templateEntry.getValue());
                LeagueBossService.rewardTgAdded(LeagueManager.getLeagueByPlayerId(playerId), rewards);
            }

            if (!allReward.isEmpty()) {
                rewards.addAll(Reward.templateCollectionToReward(allReward));
                Reward.merge(rewards);
                // 发奖
                MailType mailType = MailType.leagueBoss_pro_week_reward;
                PbCommons.PbText title = Text.genText(mailType.getTitleId()).build();
                PbCommons.PbText content = Text.genText(mailType.getContentId()).build();
                MailManager.sendMail(
                        mailType,
                        playerId,
                        title,
                        content,
                        ServerConstants.getCurrentTimeMillis(),
                        rewards);
            }
        }
    }

    @Override
    protected void loadData(LeagueBlobDb leagueBlobDb) {
        LeagueBossDb leagueBossDb = leagueBlobDb.getLeagueBossDb();
        if (Objects.nonNull(leagueBossDb)) {
            //本日伤害数据
            Map<Long, HurtRankNodeDb> todayHurtDbMap = leagueBossDb.getTodayHurtMap();
            for (Map.Entry<Long, HurtRankNodeDb> entry : todayHurtDbMap.entrySet()) {
                Long playerId = entry.getKey();
                HurtRankNodeDb hurtRankNodeDb = entry.getValue();
                HurtRankNode node = new HurtRankNode(hurtRankNodeDb);
                todayHurtMap.put(playerId, node);
            }
            //本日伤害排行榜 使用同一个对象
            Map<Integer, HurtRankNodeListDb> todayRankDbMap = leagueBossDb.getTodayRankMap();
            for (Map.Entry<Integer, HurtRankNodeListDb> dbEntry : todayRankDbMap.entrySet()) {
                Integer bossId = dbEntry.getKey();
                HurtRankNodeListDb hurtRankNodeListDb = dbEntry.getValue();
                for (HurtRankNodeDb hurtRankNodeDb : hurtRankNodeListDb.getRankNodeDbList()) {
                    List<HurtRankNode> hurtRankNodeList = todayRankMap.computeIfAbsent(bossId, list1 -> new CopyOnWriteArrayList<>());
                    hurtRankNodeList.add(todayHurtMap.get(hurtRankNodeDb.getPlayerId()));
                }
            }

            //本周伤害数据
            Map<Long, HurtRankNodeBossDb> weekHurtDbMap = leagueBossDb.getWeekHurtMap();
            for (Map.Entry<Long, HurtRankNodeBossDb> entry : weekHurtDbMap.entrySet()) {
                Long playerId = entry.getKey();
                HurtRankNodeBossDb hurtRankNodeBossDb = entry.getValue();
                for (Map.Entry<Integer, HurtRankNodeDb> dbEntry : hurtRankNodeBossDb.getWeekHurtMap().entrySet()) {
                    Integer bossId = dbEntry.getKey();
                    HurtRankNodeDb hurtRankNodeDb = dbEntry.getValue();
                    HurtRankNode node = new HurtRankNode(hurtRankNodeDb);
                    Map<Integer, HurtRankNode> hurtRankNodeMap = weekHurtMap.computeIfAbsent(playerId, map1 -> new HashMap<>());
                    hurtRankNodeMap.put(bossId, node);
                }
            }

            //本周伤害排行榜 使用同一个对象
            Map<Integer, HurtRankNodeListDb> weekRankDbMap = leagueBossDb.getWeekRankMap();
            for (Map.Entry<Integer, HurtRankNodeListDb> dbEntry : weekRankDbMap.entrySet()) {
                Integer bossId = dbEntry.getKey();
                HurtRankNodeListDb hurtRankNodeListDb = dbEntry.getValue();
                for (HurtRankNodeDb hurtRankNodeDb : hurtRankNodeListDb.getRankNodeDbList()) {
                    List<HurtRankNode> hurtRankNodeList = weekRankMap.computeIfAbsent(bossId, list1 -> new CopyOnWriteArrayList<>());

                    if(weekHurtMap.containsKey(hurtRankNodeDb.getPlayerId())){
                        Map<Integer, HurtRankNode> mapTemp = weekHurtMap.get(hurtRankNodeDb.getPlayerId());
                        if(mapTemp.containsKey(bossId)){
                            hurtRankNodeList.add(mapTemp.get(bossId));
                        }
                    }
                }
            }

            //最后一次阵容造成的伤害数据
            Map<Long, HurtRankDetailNodeBossDb> lastHurtInfoDbMap = leagueBossDb.getLastHurtInfoMap();
            for (Map.Entry<Long, HurtRankDetailNodeBossDb> entry : lastHurtInfoDbMap.entrySet()) {
                Long playerId = entry.getKey();
                HurtRankDetailNodeBossDb hurtRankDetailNodeBossDb = entry.getValue();
                for (Map.Entry<Integer, HurtRankDetailNodeDb> dbEntry : hurtRankDetailNodeBossDb.getLastHurtInfoMap().entrySet()) {
                    Integer bossId = dbEntry.getKey();
                    HurtRankDetailNodeDb hurtRankDetailNodeDb = dbEntry.getValue();
                    HurtRankDetailNode hurtRankDetailNode = new HurtRankDetailNode(hurtRankDetailNodeDb);
                    Map<Integer, HurtRankDetailNode> hurtRankDetailNodeMap = lastHurtInfoMap.computeIfAbsent(playerId, map1 -> new HashMap<>());
                    hurtRankDetailNodeMap.put(bossId, hurtRankDetailNode);
                }
            }
            //Boss信息
            Map<Integer, LeagueBossInfoDb> bossInfoDbMap = leagueBossDb.getBossInfoMap();
            for (Map.Entry<Integer, LeagueBossInfoDb> entry : bossInfoDbMap.entrySet()) {
                bossInfoMap.put(entry.getKey(), new LeagueBossInfo(entry.getValue()));
            }
            //门客堂
            this.palLevel = leagueBossDb.getPalLevel();
            //本周总伤害数据
            Map<Long, HurtRankNodeDb> totalWeekHurtMap = leagueBossDb.getTotalWeekHurtMap();
            for (Map.Entry<Long, HurtRankNodeDb> entry : totalWeekHurtMap.entrySet()) {
                Long playerId = entry.getKey();
                HurtRankNodeDb hurtRankNodeDb = entry.getValue();
                HurtRankNode node = new HurtRankNode(hurtRankNodeDb);
                this.totalWeekHurtMap.put(playerId, node);
            }
            this.palExp = leagueBossDb.getPalExp();
            this.markingBossId = leagueBossDb.getMarkingBossId();
            Map<Long, LeagueWeekRewardDb> weekRewardMap = leagueBossDb.getWeekRewardMap();
            for (Map.Entry<Long, LeagueWeekRewardDb> entry : weekRewardMap.entrySet()) {
                Long playerId = entry.getKey();
                LeagueWeekRewardDb weekRewardDb = entry.getValue();
                this.weekRewardMap.put(playerId, weekRewardDb.getHurtSet());
            }
        }
    }

    @Override
    protected void saveData(LeagueBlobDb leagueBlobDb) {
        LeagueBossDb leagueBossDb = new LeagueBossDb();
        //今日伤害排行榜
        for (Map.Entry<Integer, List<HurtRankNode>> entry : todayRankMap.entrySet()) {
            Integer bossId = entry.getKey();
            HurtRankNodeListDb hurtRankNodeListDb = new HurtRankNodeListDb();
            List<HurtRankNode> hurtRankNodeList = entry.getValue();
            for (HurtRankNode hurtRankNode : hurtRankNodeList) {
                hurtRankNodeListDb.getRankNodeDbList().add(hurtRankNode.genDb());
            }
            leagueBossDb.getTodayRankMap().put(bossId, hurtRankNodeListDb);
        }
        //今日伤害数据
        for (Map.Entry<Long, HurtRankNode> entry : todayHurtMap.entrySet()) {
            Long playerId = entry.getKey();
            HurtRankNode hurtRankNode = entry.getValue();
            leagueBossDb.getTodayHurtMap().put(playerId, hurtRankNode.genDb());
        }
        //本周伤害排行榜数据
        for (Map.Entry<Integer, List<HurtRankNode>> entry : weekRankMap.entrySet()) {
            Integer bossId = entry.getKey();
            List<HurtRankNode> hurtRankNodeList = entry.getValue();
            HurtRankNodeListDb hurtRankNodeListDb = new HurtRankNodeListDb();
            for (HurtRankNode hurtRankNode : hurtRankNodeList) {
                hurtRankNodeListDb.getRankNodeDbList().add(hurtRankNode.genDb());
            }
            leagueBossDb.getWeekRankMap().put(bossId, hurtRankNodeListDb);
        }
        //本周伤害数据
        for (Map.Entry<Long, Map<Integer, HurtRankNode>> entry : weekHurtMap.entrySet()) {
            Long playerId = entry.getKey();
            HurtRankNodeBossDb hurtRankNodeBossDb = new HurtRankNodeBossDb();
            for (Map.Entry<Integer, HurtRankNode> dbEntry : entry.getValue().entrySet()) {
                Integer bossId = dbEntry.getKey();
                HurtRankNode hurtRankNode = dbEntry.getValue();
                hurtRankNodeBossDb.getWeekHurtMap().put(bossId, hurtRankNode.genDb());
            }
            leagueBossDb.getWeekHurtMap().put(playerId, hurtRankNodeBossDb);
        }
        //最后一次所用阵容数据
        for (Map.Entry<Long, Map<Integer, HurtRankDetailNode>> entry : lastHurtInfoMap.entrySet()) {
            Long playerId = entry.getKey();
            HurtRankDetailNodeBossDb detailNodeBossDb = new HurtRankDetailNodeBossDb();
            for (Map.Entry<Integer, HurtRankDetailNode> dbEntry : entry.getValue().entrySet()) {
                Integer bossId = dbEntry.getKey();
                HurtRankDetailNode rankDetailNode = dbEntry.getValue();
                detailNodeBossDb.getLastHurtInfoMap().put(bossId, rankDetailNode.genDb());
            }
            leagueBossDb.getLastHurtInfoMap().put(playerId, detailNodeBossDb);
        }
        //Boss信息
        for (Map.Entry<Integer, LeagueBossInfo> entry : bossInfoMap.entrySet()) {
            Integer bossId = entry.getKey();
            leagueBossDb.getBossInfoMap().put(bossId, entry.getValue().genDb());
        }
        //门客堂信息
        leagueBossDb.setPalLevel(palLevel);
        //今日伤害数据
        for (Map.Entry<Long, HurtRankNode> entry : totalWeekHurtMap.entrySet()) {
            Long playerId = entry.getKey();
            HurtRankNode hurtRankNode = entry.getValue();
            leagueBossDb.getTotalWeekHurtMap().put(playerId, hurtRankNode.genDb());
        }
        //门客堂信息
        leagueBossDb.setPalExp(palExp);
        leagueBossDb.setMarkingBossId(markingBossId);
        //本周伤害奖励领取规则
        for (Map.Entry<Long, Set<Long>> entry : weekRewardMap.entrySet()) {
            LeagueWeekRewardDb leagueWeekRewardDb = new LeagueWeekRewardDb();
            leagueWeekRewardDb.setHurtSet(entry.getValue());
            leagueBossDb.getWeekRewardMap().put(entry.getKey(), leagueWeekRewardDb);
        }
        leagueBlobDb.setLeagueBossDb(leagueBossDb);
    }


    public Map<Integer, List<HurtRankNode>> getTodayRankMap() {
        return todayRankMap;
    }

    public Map<Long, HurtRankNode> getTodayHurtMap() {
        return todayHurtMap;
    }

    public Map<Integer, List<HurtRankNode>> getWeekRankMap() {
        return weekRankMap;
    }

    public Map<Long, Map<Integer, HurtRankNode>> getWeekHurtMap() {
        return weekHurtMap;
    }

    public Map<Long, Map<Integer, HurtRankDetailNode>> getLastHurtInfoMap() {
        return lastHurtInfoMap;
    }

    public Map<Integer, LeagueBossInfo> getBossInfoMap() {
        return bossInfoMap;
    }

    public Map<Integer, Set<Long>> getFightingPlayer() {
        return fightingPlayer;
    }

    public int getPalLevel() {
        return palLevel;
    }

    public void setPalLevel(int palLevel) {
        this.palLevel = palLevel;
    }

    public long getPalExp() {
        return palExp;
    }

    /**
     * 增加门客堂经验
     *
     * @param totalAddExp 增加经验
     */
    public void addPalExp(long totalAddExp) {
        if (totalAddExp <= 0) {
            return;
        }
        long totalExp = MathUtil.plusLong(palExp, totalAddExp);
        foreachLv(totalExp);
    }

    private void foreachLv(long totalExp) {
        int count = 500;//防止死循环
        while (count-- > 0) {
            if (isReachMaxLevel()) {
                // 达到满级经验可以一直存着
                this.palExp = totalExp;
                break;
            } else {
                int needExp = LeagueBossService.getLeagueBossPalTemplateMap().get(palLevel).exp;
                if (totalExp >= needExp) {
                    totalExp -= needExp;
                    levelUp();
                } else {
                    this.palExp = totalExp;
                    break;
                }
            }
        }
    }

    private void levelUp() {
        this.palLevel = palLevel + 1;
        //全队属性增加
        //获取全部帮派成员 刷新在线成员的信息
        Map<Long, LeagueMember> memberMap = getLeague().getMemberMap();
        for (Long playerId : memberMap.keySet()) {
            Player player = PlayerManager.getOnlinePlayer(playerId);
            if (Objects.nonNull(player)) {
                PlayerCombatAdditionModel combatAdditionModel = player.getCombatAdditionModel();
                combatAdditionModel.refresh(false, CombatAdditionFunction.leagueBossPal);
            }
        }
        //记录日志
        getLeague().addLeagueLog(LeagueLogType.bossPalLv, palLevel + "");
    }

    /**
     * 是否已达最大等级
     */
    public boolean isReachMaxLevel() {
        return this.palLevel >= LeagueBossService.getMaxPalLevel();
    }

    public Map<Long, HurtRankNode> getTotalWeekHurtMap() {
        return totalWeekHurtMap;
    }

    public int getMarkingBossId() {
        return markingBossId;
    }

    public void setMarkingBossId(int markingBossId) {
        this.markingBossId = markingBossId;
    }

    public Map<Long, Set<Long>> getWeekRewardMap() {
        return weekRewardMap;
    }

    @Override
    public boolean canMerge(long lid) {
        return true;
    }

    @Override
    public void merge(League selfLeague, League targetLeague) {

    }

    @Override
    public void removeLeague(League league) {

    }

    @Override
    public void quitLeague(long pid) {

    }
}

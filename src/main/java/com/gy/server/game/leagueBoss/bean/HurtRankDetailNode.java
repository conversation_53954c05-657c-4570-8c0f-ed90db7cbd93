package com.gy.server.game.leagueBoss.bean;

import com.gy.server.game.leagueBoss.LeagueBossService;
import com.gy.server.game.leagueBoss.template.LeagueBossTemplate;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.role.template.ProtagonistTemplate;
import com.gy.server.packet.PbLeagueBoss;
import com.ttlike.server.tl.baselib.serialize.leagueBoss.HurtRankDetailNodeDb;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 详细战报数据
 */
public class HurtRankDetailNode {

    private long playerId;
    private int bossId;
    private int lv;
    private int headFrameId;
    private String head;
    private String name;
    /**
     * 阵容战力
     */
    private long fightPower;
    /**
     * 阵容伤害信息
     */
    private List<HeroHurtInfo> lineupHurtList;

    public HurtRankDetailNode(HurtRankDetailNodeDb value) {
        this.playerId = value.getPlayerId();
        this.bossId = value.getBossId();
        this.lv = value.getLv();
        this.headFrameId = value.getHeadFrameId();
        this.head = value.getHead();
        this.name = value.getName();
        this.fightPower = value.getFightPower();
        this.lineupHurtList = value.getHeroHurtInfoDbList().stream().map(HeroHurtInfo::new).collect(Collectors.toList());
    }

    public HurtRankDetailNode(Player player, int bossId, List<HeroHurtInfo> heroHurtInfoList) {
        this.playerId = player.getPlayerId();
        this.bossId = bossId;
        this.lv = player.getLevel();
        this.headFrameId = player.getRoleModel().getHeadFrameId();
        if(player.getRoleModel().getHeadImage().isEmpty()){
            ProtagonistTemplate template = player.getTemplate();
            this.head = template.id + "";
        }else{
            this.head = player.getRoleModel().getHeadImage();
        }
        this.name = player.getName();
        LeagueBossTemplate leagueBossTemplate = LeagueBossService.getLeagueBossTemplateMap().get(bossId);
        this.fightPower = player.getLineupModel().calculateFightingPower(LineupType.getType(leagueBossTemplate.lineupId));
        this.lineupHurtList = heroHurtInfoList;
    }

    public HurtRankDetailNodeDb genDb() {
        HurtRankDetailNodeDb db = new HurtRankDetailNodeDb();
        db.setHeroHurtInfoDbList(this.lineupHurtList.stream().map(HeroHurtInfo::genDb).collect(Collectors.toList()));
        db.setPlayerId(playerId);
        db.setBossId(bossId);
        db.setLv(lv);
        db.setHeadFrameId(headFrameId);
        db.setHead(head);
        db.setName(name);
        db.setFightPower(fightPower);
        return db;
    }

    public PbLeagueBoss.HurtRankDetailNode genPb(int rank, long hurt) {
        MiniGamePlayer miniPlayer = PlayerManager.getMiniPlayer(playerId);
        PbLeagueBoss.HurtRankNode.Builder hurtBuild = PbLeagueBoss.HurtRankNode.newBuilder()
                .setMiniUser(miniPlayer.genMinMiniUser())
                .setRank(rank)
                .setPlayerId(playerId)
//                .setHeadFrameId(headFrameId)
//                .setLv(lv)
//                .setHead(head)
//                .setHeadFrameId(headFrameId)
//                .setName(name)
                .setHurt(hurt);
        return PbLeagueBoss.HurtRankDetailNode.newBuilder()
                .setRankNode(hurtBuild)
                .setFightPower(fightPower)
                .addAllHurtInfo(lineupHurtList.stream().map(HeroHurtInfo::genPb).collect(Collectors.toList())).build();
    }

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public int getLv() {
        return lv;
    }

    public void setLv(int lv) {
        this.lv = lv;
    }

    public int getHeadFrameId() {
        return headFrameId;
    }

    public void setHeadFrameId(int headFrameId) {
        this.headFrameId = headFrameId;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public long getFightPower() {
        return fightPower;
    }

    public void setFightPower(long fightPower) {
        this.fightPower = fightPower;
    }

    public List<HeroHurtInfo> getLineupHurtList() {
        return lineupHurtList;
    }

    public void setLineupHurtList(List<HeroHurtInfo> lineupHurtList) {
        this.lineupHurtList = lineupHurtList;
    }
}

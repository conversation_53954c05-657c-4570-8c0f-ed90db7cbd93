package com.gy.server.game.experience.bean;

import com.gy.server.packet.PbExperience;

import java.util.HashSet;
import java.util.Set;

/**
 * 进行中节点信息
 *
 * <AUTHOR> 2024/6/19 10:54
 **/
public class ExperienceNowNodeInfo {
    /**
     * Experience id
     */
    private int experienceId;
    /**
     * 进行中节点id
     */
    private int nodeId;
    /**
     * 进行中回答id
     */
    private int talkId;
    /**
     * 已回答回答id
     */
    private Set<Integer> answerIds = new HashSet<>();

    public ExperienceNowNodeInfo() {
    }

    public ExperienceNowNodeInfo(int experienceId) {
        this.experienceId = experienceId;
    }

    public ExperienceNowNodeInfo(int experienceId, int nodeId, int talkId, Set<Integer> answerIds) {
        this.experienceId = experienceId;
        this.nodeId = nodeId;
        this.talkId = talkId;
        this.answerIds = answerIds;
    }

    public int getExperienceId() {
        return experienceId;
    }

    public void setExperienceId(int experienceId) {
        this.experienceId = experienceId;
    }

    public int getNodeId() {
        return nodeId;
    }

    public void setNodeId(int nodeId) {
        this.nodeId = nodeId;
    }

    public int getTalkId() {
        return talkId;
    }

    public void setTalkId(int talkId) {
        this.talkId = talkId;
    }

    public Set<Integer> getAnswerIds() {
        return answerIds;
    }

    public void setAnswerIds(Set<Integer> answerIds) {
        this.answerIds = answerIds;
    }

    public PbExperience.NowTalkInfo genPb() {
        PbExperience.NowTalkInfo.Builder builder = PbExperience.NowTalkInfo.newBuilder();
        builder.setNodeId(nodeId);
        builder.setTalkId(talkId);
        builder.addAllAnswerId(answerIds);
        return builder.build();
    }
}

package com.gy.server.game.experience;

import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.cond.CondManager;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.experience.bean.ExperienceNowNodeInfo;
import com.gy.server.game.experience.template.ExperienceNodeTemplate;
import com.gy.server.game.experience.template.ExperienceRewardTemplate;
import com.gy.server.game.experience.template.ExperienceTemplate;
import com.gy.server.game.function.Function;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.service.Service;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbExperience;
import com.gy.server.packet.PbProtocol;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 角色游历服务类
 *
 * <AUTHOR> 2024/5/15 17:59
 **/
public class ExperienceService extends PlayerPacketHandler implements Service {

    private static Map<Integer, ExperienceTemplate> experienceTemplateMap = new HashMap<>();

    private static Map<Integer, ExperienceNodeTemplate> experienceNodeTemplateMap = new HashMap<>();

    private static Map<Integer, ExperienceRewardTemplate> experienceRewardTemplateHashMap = new HashMap<>();

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.Branchplot_Experience);
        Map<Integer, ExperienceTemplate> experienceTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            ExperienceTemplate template = new ExperienceTemplate(map);
            experienceTemplateMapTemp.put(template.id, template);
        }
        experienceTemplateMap = experienceTemplateMapTemp;

        mapList = ConfigReader.read(ConfigFile.Branchplot_ExperienceNode);
        Map<Integer, ExperienceNodeTemplate> experienceNodeTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            ExperienceNodeTemplate template = new ExperienceNodeTemplate(map);
            experienceNodeTemplateMapTemp.put(template.nodeId, template);
        }
        experienceNodeTemplateMap = experienceNodeTemplateMapTemp;

        mapList = ConfigReader.read(ConfigFile.Branchplot_ExperienceReward);
        Map<Integer, ExperienceRewardTemplate> experienceRewardTemplateHashMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            ExperienceRewardTemplate template = new ExperienceRewardTemplate(map);
            experienceRewardTemplateHashMapTemp.put(template.rewardId, template);
        }
        experienceRewardTemplateHashMap = experienceRewardTemplateHashMapTemp;
    }

    @Override
    public void clearConfigData() {
        experienceTemplateMap.clear();
        experienceNodeTemplateMap.clear();
        experienceRewardTemplateHashMap.clear();
    }

    /**
     * 游历-进行
     */
    @Handler(PtCode.EXPERIENCE_HAVE_REQ)
    private void experienceHaveReq(Player player, PbProtocol.ExperienceHaveReq req, long time){
        PbProtocol.ExperienceHaveRst.Builder rst = PbProtocol.ExperienceHaveRst.newBuilder().setResult(Text.genOkServerRstInfo());
        int id = req.getId();
        int nodeId = req.getNodeId();
        PbExperience.ExperienceNodeType type = req.getType();
        int talkId = req.getTalkId();
        int answerId = req.getAnswerId();

        logic:{
            if(!Function.Branchplot.isOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }

            if(type != PbExperience.ExperienceNodeType.unlock && type != PbExperience.ExperienceNodeType.finish){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }

            ExperienceTemplate experienceTemplate = experienceTemplateMap.get(id);
            if(Objects.isNull(experienceTemplate)){
                rst.setResult(Text.genServerRstInfo(Text.对应的模板数据找不到));
                break logic;
            }

            String condition = experienceTemplate.condition;
            if(CondManager.checkNotCond(player, condition)){
                rst.setResult(Text.genServerRstInfo(Text.游历未解锁));
                break logic;
            }

            ExperienceNodeTemplate experienceNodeTemplate = experienceNodeTemplateMap.get(nodeId);
            if(Objects.isNull(experienceNodeTemplate)){
                rst.setResult(Text.genServerRstInfo(Text.对应的模板数据找不到));
                break logic;
            }

            PlayerExperienceModel model = player.getExperienceModel();
            if(!(experienceTemplate.prologueNodeId == nodeId || experienceTemplate.plotNodeId.contains(nodeId) || experienceTemplate.outcomeNodeId.contains(nodeId))){
                rst.setResult(Text.genServerRstInfo(Text.游历参数异常));
                break logic;
            }

            if(!model.getNowNodeIdMap().containsKey(experienceTemplate.id)){
                //重新开始可以从中间节点开始，只在首次时判断是不是初始id
                if(!model.getTypeMap().containsKey(experienceTemplate.prologueNodeId)){
                    //首次
                    if(experienceTemplate.prologueNodeId != nodeId){
                        rst.setResult(Text.genServerRstInfo(Text.游历参数异常));
                        break logic;
                    }
                }
            }else {
                ExperienceNowNodeInfo nowNodeInfo = model.getNowNodeIdMap().get(experienceTemplate.id);
                ExperienceNodeTemplate nowExperienceNodeTemplate = experienceNodeTemplateMap.get(nowNodeInfo.getNodeId());
                if(Objects.isNull(nowExperienceNodeTemplate)){
                    rst.setResult(Text.genServerRstInfo(Text.数据异常));
                    break logic;
                }

                if(type == PbExperience.ExperienceNodeType.unlock && model.getTypeMap().getOrDefault(nodeId, PbExperience.ExperienceNodeType.unlock) == PbExperience.ExperienceNodeType.unlock){
                    if(nodeId == nowNodeInfo.getNodeId()){
                          if(!nowExperienceNodeTemplate.talkIds.contains(talkId)){
                              rst.setResult(Text.genServerRstInfo(Text.游历参数异常));
                              break logic;
                          }
                    }

                    if(nodeId != nowNodeInfo.getNodeId()){
                        //检查前置节点是否解锁/完成
                        if(!nowExperienceNodeTemplate.posPoints.contains(nodeId)){
                            ExperienceNodeTemplate nodeTemplateTemp = experienceNodeTemplateMap.get(nodeId);
                            if(!model.getTypeMap().containsKey(nodeTemplateTemp.frontPoint)){
                                rst.setResult(Text.genServerRstInfo(Text.游历参数异常));
                                break logic;
                            }
                        }
                    }
                }
            }

            model.unlockNode(id, nodeId, talkId, answerId, type);
            player.dataSyncModule.syncExperience();
        }

        player.send(PtCode.EXPERIENCE_HAVE_RST, rst.build(), time);
    }

    /**
     * 游历-领奖
     */
    @Handler(PtCode.EXPERIENCE_DRAW_REQ)
    private void experienceDrawReq(Player player, PbProtocol.ExperienceDrawReq req, long time){
        PbProtocol.ExperienceDrawRst.Builder rst = PbProtocol.ExperienceDrawRst.newBuilder().setResult(Text.genOkServerRstInfo());
        int rewardId = req.getRewardId();

        logic:{
            if(!Function.Branchplot.isOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }

            ExperienceRewardTemplate experienceRewardTemplate = experienceRewardTemplateHashMap.get(rewardId);
            if(Objects.isNull(experienceRewardTemplate)){
                rst.setResult(Text.genServerRstInfo(Text.游历参数异常));
                break logic;
            }

            PlayerExperienceModel model = player.getExperienceModel();
            PbExperience.ExperienceNodeType type = model.getTypeMap().getOrDefault(experienceRewardTemplate.outcomeNodeId, PbExperience.ExperienceNodeType.unlock);
            if(type != PbExperience.ExperienceNodeType.finish){
                rst.setResult(Text.genServerRstInfo(Text.游历结局未达成));
                break logic;
            }

            if(model.getDrawNodeIds().contains(rewardId)){
                rst.setResult(Text.genServerRstInfo(Text.奖励已领取));
                break logic;
            }

            model.getDrawNodeIds().add(rewardId);
            List<Reward> rewards = Reward.templateCollectionToReward(experienceRewardTemplate.rewardTemplates);
            Reward.add(rewards, player, BehaviorType.experience);
            rst.addAllReward(Reward.writeCollectionToPb(rewards));

            player.dataSyncModule.syncExperience();
        }

        player.send(PtCode.EXPERIENCE_DRAW_RST, rst.build(), time);
    }

    /**
     * 游历-重新开始
     */
    @Handler(PtCode.EXPERIENCE_RESTART_REQ)
    private void experienceRestartReq(Player player, PbProtocol.ExperienceRestartReq req, long time){
        PbProtocol.ExperienceRestartRst.Builder rst = PbProtocol.ExperienceRestartRst.newBuilder().setResult(Text.genOkServerRstInfo());
        int id = req.getId();

        logic:{
            if(!Function.Branchplot.isOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }

            ExperienceTemplate experienceTemplate = experienceTemplateMap.get(id);
            if(Objects.isNull(experienceTemplate)){
                rst.setResult(Text.genServerRstInfo(Text.对应的模板数据找不到));
                break logic;
            }

            String condition = experienceTemplate.condition;
            if(CondManager.checkNotCond(player, condition)){
                rst.setResult(Text.genServerRstInfo(Text.游历未解锁));
                break logic;
            }

            PlayerExperienceModel model = player.getExperienceModel();
            model.getNowNodeIdMap().remove(experienceTemplate.id);
            player.dataSyncModule.syncExperience();
        }

        player.send(PtCode.EXPERIENCE_RESTART_RST, rst.build(), time);
    }

    public static Map<Integer, ExperienceTemplate> getExperienceTemplateMap() {
        return experienceTemplateMap;
    }

    public static Map<Integer, ExperienceNodeTemplate> getExperienceNodeTemplateMap() {
        return experienceNodeTemplateMap;
    }

    public static Map<Integer, ExperienceRewardTemplate> getExperienceRewardTemplateHashMap() {
        return experienceRewardTemplateHashMap;
    }
}

package com.gy.server.game.experience.template;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 游历事件表
 *
 * <AUTHOR> 2024/5/15 17:39
 **/
public class ExperienceNodeTemplate {
    /**
     * 事件id
     */
    public int nodeId;
    /**
     * 前置id
     */
    public int frontPoint;
    /**
     * 后置id
     */
    public List<Integer> posPoints = new ArrayList<>();

    public List<Integer> talkIds = new ArrayList<>();

    public ExperienceNodeTemplate(Map<String, String> map){
        this.nodeId = Integer.parseInt(map.get("NodeId"));
        this.frontPoint = Integer.parseInt(map.get("FrontPoint"));
        if(!map.get("PosPoint").equals("-1")) this.posPoints = Arrays.asList(map.get("PosPoint").split(",")).stream().map(Integer::new).collect(Collectors.toList());
        this.talkIds = Arrays.asList(map.get("TalkId").split(",")).stream().map(Integer::new).collect(Collectors.toList());
    }
}

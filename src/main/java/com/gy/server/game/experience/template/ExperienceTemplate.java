package com.gy.server.game.experience.template;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 角色游历表
 *
 * <AUTHOR> 2024/5/15 17:27
 **/
public class ExperienceTemplate {
    public int id;
    /**
     * 序章事件id
     */
    public int prologueNodeId;
    /**
     * 剧情事件
     */
    public List<Integer> plotNodeId = new ArrayList<>();
    /**
     * 结局事件
     */
    public List<Integer> outcomeNodeId = new ArrayList<>();
    /**
     * 解锁条件
     */
    public String condition;

    public ExperienceTemplate(Map<String, String> map){
        this.id = Integer.parseInt(map.get("id"));
        this.prologueNodeId = Integer.parseInt(map.get("PrologueNodeId"));
        this.plotNodeId = Arrays.asList(map.get("PlotNodeId").split(",")).stream().map(Integer::new).collect(Collectors.toList());
        this.outcomeNodeId = Arrays.asList(map.get("OutcomeNodeId").split(",")).stream().map(Integer::new).collect(Collectors.toList());
        this.condition = map.get("Condition");
    }
}

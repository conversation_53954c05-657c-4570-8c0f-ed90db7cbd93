package com.gy.server.game.experience.template;

import com.gy.server.game.drop.RewardTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 游历结局达成奖励表
 *
 * <AUTHOR> 2024/5/16 9:33
 **/
public class ExperienceRewardTemplate {
    public int rewardId;

    /**
     * 游历id
     */
    public int experienceId;
    /**
     * 结局id
     */
    public int outcomeNodeId;
    /**
     * 奖励
     */
    public List<RewardTemplate> rewardTemplates = new ArrayList<>();

//    RewardId	ExperienceId	OutcomeNodeId	RewardDec
    public ExperienceRewardTemplate(Map<String, String> map){
        this.rewardId = Integer.parseInt(map.get("RewardId"));
        this.experienceId = Integer.parseInt(map.get("ExperienceId"));
        this.outcomeNodeId = Integer.parseInt(map.get("OutcomeNodeId"));
        this.rewardTemplates = RewardTemplate.readListFromText(map.get("RewardDec"));
    }
}

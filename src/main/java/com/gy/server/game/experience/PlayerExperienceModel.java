package com.gy.server.game.experience;

import com.gy.server.game.experience.bean.ExperienceNowNodeInfo;
import com.gy.server.game.experience.template.ExperienceTemplate;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModel;
import com.gy.server.game.player.event.PlayerEvent;
import com.gy.server.game.player.event.PlayerEventHandler;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.packet.PbExperience;
import com.ttlike.server.tl.baselib.serialize.experience.PlayerExperienceModelDb;
import com.ttlike.server.tl.baselib.serialize.experience.bean.ExperienceNowNodeInfoDb;
import com.ttlike.server.tl.baselib.serialize.player.PlayerBlobDb;

import java.util.*;

/**
 * 玩家游历模块
 *
 * <AUTHOR> 2024/5/15 15:28
 **/
public class PlayerExperienceModel extends PlayerModel implements PlayerEventHandler {

    /**
     * 当前事件
     * key:ExperienceNode表id value:当前正在进行事件id
     */
    private Map<Integer, ExperienceNowNodeInfo> nowNodeIdMap = new HashMap<>();
    /**
     * key:nodeId value:节点信息
     */
    private Map<Integer, PbExperience.ExperienceNodeType> typeMap = new HashMap<>();

    /**
     * 已领取的游历id（结局）奖励  ExperienceReward
     */
    private Set<Integer> drawNodeIds = new HashSet<>();

    public PlayerExperienceModel(Player player) {
        super(player);
    }

    /**
     * 解锁节点
     * @param id
     * @param nodeId
     * @param talkId
     * @param answerId
     * @param type
     */
    public void unlockNode(int id, int nodeId, int talkId, int answerId, PbExperience.ExperienceNodeType type){
        typeMap.put(nodeId, type);
        if(type == PbExperience.ExperienceNodeType.finish){
            return;
        }

        ExperienceNowNodeInfo experienceNowNodeInfo = nowNodeIdMap.computeIfAbsent(id, bean -> new ExperienceNowNodeInfo(id));
        if(experienceNowNodeInfo.getNodeId() == nodeId){
            if(experienceNowNodeInfo.getTalkId() == talkId){
                experienceNowNodeInfo.getAnswerIds().add(answerId);
            }
        }else {
            experienceNowNodeInfo.setNodeId(nodeId);
            experienceNowNodeInfo.setTalkId(talkId);
            experienceNowNodeInfo.getAnswerIds().clear();
            experienceNowNodeInfo.getAnswerIds().add(answerId);
        }
    }

    @Override
    protected void loadData(PlayerBlobDb playerBlob) {
        PlayerExperienceModelDb db = playerBlob.getExperienceModelDb();
        if(db != null){
            db.getNowNodeInfoDbs().forEach(bean -> {
                nowNodeIdMap.put(bean.getExperienceId(), new ExperienceNowNodeInfo(bean.getExperienceId(), bean.getNodeId(), bean.getTalkId(), new HashSet<>(bean.getAnswerIds())));
            });
            db.getTypeMap().forEach((key, value) -> {
                typeMap.put(key, PbExperience.ExperienceNodeType.forNumber(value));
            });
            drawNodeIds.addAll(db.getDrawNodeIds());
        }
    }

    @Override
    protected void saveData(PlayerBlobDb playerBlob) {
        PlayerExperienceModelDb db = new PlayerExperienceModelDb();
        nowNodeIdMap.values().forEach(bean -> {
            db.getNowNodeInfoDbs().add(new ExperienceNowNodeInfoDb(bean.getExperienceId(), bean.getNodeId(), bean.getTalkId(), new ArrayList<>(bean.getAnswerIds())));
        });
        typeMap.forEach((key, value) -> {
            db.getTypeMap().put(key, value.getNumber());
        });
        db.getDrawNodeIds().addAll(drawNodeIds);
        playerBlob.setExperienceModelDb(db);
    }


    public boolean checkFinishOneResult(ExperienceTemplate template) {
        for (int resultId : template.outcomeNodeId) {
            if (typeMap.getOrDefault(resultId, PbExperience.ExperienceNodeType.unlock) == PbExperience.ExperienceNodeType.finish) {
                return true;
            }
        }

        return false;
    }

    public PbExperience.ExperienceModel genPb(){
        PbExperience.ExperienceModel.Builder builder = PbExperience.ExperienceModel.newBuilder();
        nowNodeIdMap.forEach((key, value) -> {
            builder.putNowNodeIdMap(key, value.genPb());
        });

        typeMap.forEach((key, value) -> {
            builder.putTypeMap(key, value);
        });
        builder.addAllDrawNodeIds(drawNodeIds);
        return builder.build();
    }

    public Map<Integer, ExperienceNowNodeInfo> getNowNodeIdMap() {
        return nowNodeIdMap;
    }

    public void setNowNodeIdMap(Map<Integer, ExperienceNowNodeInfo> nowNodeIdMap) {
        this.nowNodeIdMap = nowNodeIdMap;
    }

    public Map<Integer, PbExperience.ExperienceNodeType> getTypeMap() {
        return typeMap;
    }

    public void setTypeMap(Map<Integer, PbExperience.ExperienceNodeType> typeMap) {
        this.typeMap = typeMap;
    }

    public Set<Integer> getDrawNodeIds() {
        return drawNodeIds;
    }

    public void setDrawNodeIds(Set<Integer> drawNodeIds) {
        this.drawNodeIds = drawNodeIds;
    }

    private static final PlayerEventType[] EVENT_TYPES = new PlayerEventType[]{};

    @Override
    public PlayerEventType[] getEventTypes() {
        return EVENT_TYPES;
    }

    @Override
    public void handle(PlayerEvent event) {

    }
}

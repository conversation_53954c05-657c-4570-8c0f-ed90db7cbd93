package com.gy.server.game.player;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import com.gy.server.core.ElapsedTimeStatistics;
import com.gy.server.core.Launcher;
import com.gy.server.core.MainThread;
import com.gy.server.core.log.SystemLogger;
import com.gy.server.game.db.DbManager;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

/**
 * 用户存储器：专门用于周期性存储{@link Player}数据
 *
 * <AUTHOR> - [Create on 2019/08/24 15:33]
 */
public final class PlayerSaveManager {

    private static final long INTERNAL = 20;
    private static volatile boolean saveNow = false;

    /**
     * 区服, 玩家ID，玩家数据
     * 待入库玩家
     */
    private volatile static Map<Integer,Map<Long, PlayerData>> SAVE_PLAYERS = new ConcurrentHashMap<>();
    private static long lastSaveTime = System.currentTimeMillis();
    /**
     * 区服，玩家ID，玩家数据
     * 入库失败玩家，重试中
     */
    private volatile static Map<Integer,Map<Long, PlayerData>> SAVE_FAILED_PLAYERS = new ConcurrentHashMap<>();
    private static AtomicBoolean running = new AtomicBoolean(false);
    private static final long SAVE_INTERNAL = 10000;

    private PlayerSaveManager() {
    }

    public static void startup() {
        if (running.compareAndSet(false, true)) {
            Launcher.launchStartupInfo("PlayerSaveManager startup");
            ThreadPool.execute(PlayerSaveManager::save);
        }
    }

    /**
     * 每过一段时间，就会探测队列内是否有数据
     * 如果有数据，将会取出去重的PlayerData(覆盖的策略，同一个Player生成的PlayerData，后来的会覆盖前面的)，进行批量存储
     * 存储时机：每5秒或每200个PlayerData，会进行批量存储一次
     */
    private static void save() {
        while (MainThread.isRunning()) {
            innerSave();
        }

        running.set(false);
        Launcher.launchShutdownInfo("PlayerSaveManager shutdown");
    }

    private static int countMap(Map<Integer,Map<Long, PlayerData>> map){
        int count = 0;
        for(Map<Long, PlayerData> playerDataMap : map.values()){
            count += playerDataMap.size();
        }
        return count;
    }

    public static void innerSave(){
        try {
            Thread.sleep(INTERNAL);

            if ((countMap(SAVE_PLAYERS) > 0 || countMap(SAVE_FAILED_PLAYERS) > 0)
                    && (saveNow || !MainThread.isRunning() || System.currentTimeMillis() - lastSaveTime >= SAVE_INTERNAL)) {

                lastSaveTime = System.currentTimeMillis();

                //重置立即存储开关
                if (saveNow) {
                    saveNow = false;
                }

                long totalStart = System.nanoTime();

                for(Map.Entry<Integer,Map<Long, PlayerData>> entry : SAVE_PLAYERS.entrySet()){
                    int serverNumber = entry.getKey();
                    Map<Long, PlayerData> playerDataMap = entry.getValue();
                    if(playerDataMap.size() > 0){
                        SAVE_PLAYERS.put(serverNumber, new ConcurrentHashMap<>());
                        doSave(serverNumber, new ArrayList<>(playerDataMap.values()));
                    }

                }

                for(Map.Entry<Integer,Map<Long, PlayerData>> entry : SAVE_FAILED_PLAYERS.entrySet()){
                    int serverNumber = entry.getKey();
                    Map<Long, PlayerData> playerDataMap = entry.getValue();
                    if(playerDataMap.size() > 0){
                        SAVE_FAILED_PLAYERS.put(serverNumber, new ConcurrentHashMap<>());
                        doSave(serverNumber, new ArrayList<>(playerDataMap.values()));
                    }
                }

                // 使用Runner日志
                ElapsedTimeStatistics.addElapsedNanoTimeWithoutMinNano(ElapsedTimeStatistics.Type.Runner, "PlayerSaveManager", System.nanoTime() - totalStart);
            }

        } catch (Exception e) {
            SystemLogger.error("PlayerSaveManager error: " + e);
        }
    }

    private static void doSave(int serverNumber, List<PlayerData> list) {
        if (!list.isEmpty()) {
            //一次最多存储200条
            if (list.size() > 200) {
                List<PlayerData> list1 = list.subList(0, list.size() / 2);
                List<PlayerData> list2 = list.subList(list.size() / 2, list.size());
                doSave(serverNumber, new ArrayList<>(list1));
                doSave(serverNumber, new ArrayList<>(list2));
                return;
            }

            try {
                DbManager.updatePlayerBatch(serverNumber,list);
            } catch (Exception e) {
                SystemLogger.error("PlayerSaveManager error: " + e);
                SystemLogger.warn("PlayerSaveManager error. Put back to queue.");
                SystemLogger.warn("PlayerSaveManager error, playerIds: " + list.stream().map(PlayerData::getPlayerId).collect(Collectors.toList()));

                list.forEach(p -> {
                    Map<Long, PlayerData> map = SAVE_FAILED_PLAYERS.get(p.getPlayerId());
                    if (map == null) {
                        map = new ConcurrentHashMap<>();
                        SAVE_FAILED_PLAYERS.put(p.getServerNum(), map);
                    }
                    map.putIfAbsent(p.getPlayerId(), p);
                });//ConcurrentHashMap的putIfAbsent是原子性操作，确保不会覆盖新数据。当有更更新数据，无需放回去，防止回档
            }
        }
    }

    public static void save(PlayerData playerData) {
        Map<Long, PlayerData> map = SAVE_PLAYERS.get(playerData.getServerNum());
        if (map == null) {
            synchronized (SAVE_PLAYERS) {
                map = SAVE_PLAYERS.get(playerData.getServerNum());
                if (map == null) {
                    map = new ConcurrentHashMap<>();
                    SAVE_PLAYERS.put(playerData.getServerNum(), map);
                }
            }
        }
        map.put(playerData.getPlayerId(), playerData);
    }

    public static boolean isRunning() {
        return running.get();
    }

    public static int queueSize() {
        return SAVE_PLAYERS.size();
    }

    public static void saveNow(PlayerData playerData) {
        save(playerData);
        saveNow = true;
    }

    public static Map<Integer, Map<Long, PlayerData>> getSavePlayers() {
        return SAVE_PLAYERS;
    }

    public static Map<Integer, Map<Long, PlayerData>> getSaveFailedPlayers() {
        return SAVE_FAILED_PLAYERS;
    }

    /**
     * 获取正在存储中的玩家数据
     * @param playerId
     * @return
     */
    public static PlayerData getPlayerData(long playerId) {
        int serverNumber = Player.getServerId(playerId);
        Map<Long, PlayerData> map = SAVE_PLAYERS.get(serverNumber);
        if(map != null && map.containsKey(playerId)){
            return map.get(playerId);
        }
        map = SAVE_FAILED_PLAYERS.get(serverNumber);
        if(map != null && map.containsKey(playerId)){
            return map.get(playerId);
        }
        return null;
    }
}

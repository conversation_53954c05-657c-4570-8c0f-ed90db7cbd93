package com.gy.server.game.quicklybuy;

import com.gy.server.game.drop.RewardTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> - [Create on 2019/04/19 11:46]
 */
public class QuicklyBuyTemplate {

    //唯一id
    private int id;
    //购买资源
    private RewardTemplate buyResource;
    //购买价格
    private List<Map<Integer, Integer>> costs = new ArrayList<>();
    //消耗资源id列表
    private List<Integer> costTypeList;
    //最大购买次数
    private int maxBuyTimes;
    //每次最大购买次数
    private int buyTimes;
    //购买次数不足提示
    private int tipsSys;


//    public QuicklyBuyTemplate(int id, int costType, String costStr, int maxBuyTimes, int vipRelatedInt) {
//        this.id = id;
//        this.costType = costType;
//
//        String[] costStrArr = costStr.split(",");
//        for (int i = 0; i < costStrArr.length; i++) {
//            costMap.put(i + 1, Integer.parseInt(costStrArr[i]));
//        }
//
//        this.maxBuyTimes = maxBuyTimes;
//
//        vipRelated = vipRelatedInt != 0;
//
//        if (vipRelatedInt == 1) {//和vip有关
//            for(Map.Entry<Integer, PrivilegeTrainTemplate> entry : PrivilegeTrainService.templates.entrySet()) {
//                vipEffectMap.put(entry.getKey(),entry.getValue().effects);
//            }
//        }
//    }


    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public RewardTemplate getBuyResource() {
        return buyResource;
    }

    public void setBuyResource(RewardTemplate buyResource) {
        this.buyResource = buyResource;
    }

    public List<Map<Integer, Integer>> getCosts() {
        return costs;
    }

    public void setCosts(List<Map<Integer, Integer>> costs) {
        this.costs = costs;
    }

    public List<Integer> getCostTypeList() {
        return costTypeList;
    }

    public void setCostTypeList(List<Integer> costTypeList) {
        this.costTypeList = costTypeList;
    }

    public int getMaxBuyTimes() {
        return maxBuyTimes;
    }

    public void setMaxBuyTimes(int maxBuyTimes) {
        this.maxBuyTimes = maxBuyTimes;
    }

    public int getBuyTimes() {
        return buyTimes;
    }

    public void setBuyTimes(int buyTimes) {
        this.buyTimes = buyTimes;
    }

    public int getTipsSys() {
        return tipsSys;
    }

    public void setTipsSys(int tipsSys) {
        this.tipsSys = tipsSys;
    }

    /**
     * 根据购买的次数，计算出本次需要的消耗值。当超过最大值时，获取最后1个值
     */
    public int calCostByTimes(int costType, int times) {
        int index = costTypeList.indexOf(costType);
        Map<Integer, Integer> costMap = costs.get(index);
        if (times <= maxBuyTimes && costMap.size() >= times ) {
            return costMap.get(times);
        }
        return costMap.get(costMap.size());
    }

}

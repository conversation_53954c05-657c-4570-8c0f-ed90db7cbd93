package com.gy.server.game.quicklybuy;

import com.google.common.collect.Sets;
import com.google.protobuf.InvalidProtocolBufferException;
import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.currency.Currency;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.item.ItemService;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.service.Service;
import com.gy.server.game.text.Text;
import com.gy.server.game.util.StringExtUtil;
import com.gy.server.packet.PbProtocol;

import java.util.*;

/**
 * <AUTHOR> - [Create on 2019/04/19 11:46]
 */
public class QuicklyBuyService extends PlayerPacketHandler implements Service {

    /**
     * <normalItemId: template>
     */
    private static Map<Integer, QuicklyBuyTemplate> quicklyBuyMap = new HashMap<>();

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.quicklyBuy_quicklyBuy);
        Map<Integer, QuicklyBuyTemplate> quicklyBuyMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            int id = Integer.parseInt(map.get("id"));
            if (quicklyBuyMapTemp.containsKey(id)) {
                throw new RuntimeException("duplicate quickly buy id: " + id);
            }

            QuicklyBuyTemplate template = new QuicklyBuyTemplate();
            template.setId(Integer.parseInt(map.get("id")));
            template.setBuyResource(RewardTemplate.readListFromText(map.get("buyId")).get(0));
            List<Integer> costTypeList = StringExtUtil.string2List(map.get("costType"), ",", Integer.class);
            template.setCostTypeList(costTypeList);
            List<Map<Integer, Integer>> tmpList = new ArrayList<>();
            for (int i = 1; i <= costTypeList.size(); i++) {
                Map<Integer, Integer> tmpMap = new HashMap<>();
                String[] costs = map.get("cost" + i).split(",");
                for (int j = 0; j < costs.length; j++) {
                    tmpMap.put(j + 1, Integer.parseInt(costs[j]));
                }
                tmpList.add(tmpMap);
            }
            template.setCosts(tmpList);
            template.setMaxBuyTimes(Integer.parseInt(map.get("maxBuytimes")));
            template.setBuyTimes(Integer.parseInt(map.get("buytimes")));
            template.setTipsSys(Integer.parseInt(map.get("tipsSys")));

            quicklyBuyMapTemp.put(id, template);
        }
        quicklyBuyMap = quicklyBuyMapTemp;
    }

    @Override
    public void clearConfigData() {
        quicklyBuyMap.clear();
    }

    @SuppressWarnings("unchecked")
    @Override
    public Set<Class<? extends Service>> preServices() {
        return Sets.newHashSet(ItemService.class);
    }


    /**
     * 快捷购买
     */
    @Handler(PtCode.QUICKLY_BUY_CLIENT)
    private void quicklyBuy(Player player, PbProtocol.QuicklyBuyReq req, long time) throws InvalidProtocolBufferException {
        PbProtocol.QuicklyBuyRst.Builder rst = PbProtocol.QuicklyBuyRst.newBuilder().setResult(Text.genOkServerRstInfo());

        int itemId = req.getItemId();
        int num = req.getNum();
        int costType = req.getCostType();
        logic:
        {
            QuicklyBuyTemplate quicklyBuyTemplate = quicklyBuyMap.get(itemId);
            if (quicklyBuyTemplate == null) {
                rst.setResult(Text.genServerRstInfo(Text.物品不存在));
                break logic;
            }
            if (!quicklyBuyTemplate.getCostTypeList().contains(costType)) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            if (num <= 0 || num > quicklyBuyTemplate.getBuyTimes()) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            int maxTimes = quicklyBuyTemplate.getMaxBuyTimes();

            Map<Integer, Integer> buyMap = player.getQuicklyBuyModel().getBuyMap();
            Integer lastBuyTimes = buyMap.get(itemId);
            if (lastBuyTimes != null && lastBuyTimes + num > maxTimes) {
                rst.setResult(Text.genServerRstInfo(Text.购买次数达到上限));
                break logic;
            }

            Currency costCurrency = Currency.getCurrencyById(costType);
            if (costCurrency == null) {
                rst.setResult(Text.genServerRstInfo(Text.对应的模板数据找不到));
                break logic;
            }

            int last = lastBuyTimes != null ? lastBuyTimes : 0;
            int buyTimes = last + num;
            int costNum = 0;
            for (int i = last + 1; i <= buyTimes; i++) {
                costNum += quicklyBuyTemplate.calCostByTimes(costType, i);
            }

            Reward cost = Reward.create(costCurrency, costNum);
            if (cost.check(player) != -1) {
                rst.setResult(Text.genServerRstInfo(Text.消耗不足));
                break logic;
            }

            // 能来到这，说明啥问题都没了。开始扣费
            cost.remove(player, BehaviorType.quicklyBuy);
            // 存下购买次数
            buyMap.put(itemId, buyTimes);

            // 发奖
            List<RewardTemplate> rewards = new ArrayList<>();
            for (int i = 0; i < num; i++) {
                RewardTemplate buyResource = quicklyBuyTemplate.getBuyResource();
                rewards.add(buyResource);
            }

            List<Reward> rewardList = Reward.templateCollectionToReward(rewards);
            //合并奖励
            Reward.merge(rewardList);
            Reward.add(rewardList, player, BehaviorType.quicklyBuy);

            rst.addAllObtain(Reward.writeCollectionToPb(rewardList));

            //发送快速购买事件
            player.postEvent(PlayerEventType.quicklyBuy, rewardList);
        }

        player.send(PtCode.QUICKLY_BUY_SERVER, rst.build(), time);
    }

    /**
     * 查询物品购买数量
     */
    @Handler(PtCode.QUICKLY_QUERY_BUY_CLIENT)
    public void quicklyBuyNum(Player player, PbProtocol.QuicklyBuyNumReq req, long time) throws InvalidProtocolBufferException {
        PbProtocol.QuicklyBuyNumRst.Builder rst = PbProtocol.QuicklyBuyNumRst.newBuilder().setResult(Text.genOkServerRstInfo());
        int itemId = req.getTemplateId();
        rst.setNum(player.getQuicklyBuyModel().getBuyMap().getOrDefault(itemId, 0));

        player.send(PtCode.QUICKLY_QUERY_BUY_SERVER, rst.build(), time);
    }

}

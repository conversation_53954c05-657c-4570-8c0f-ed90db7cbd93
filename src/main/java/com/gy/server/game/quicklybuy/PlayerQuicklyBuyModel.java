package com.gy.server.game.quicklybuy;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerBlob;
import com.gy.server.game.player.PlayerModel;
import com.gy.server.game.player.event.PlayerEvent;
import com.gy.server.game.player.event.PlayerEventHandler;
import com.gy.server.game.player.event.PlayerEventType;
import com.ttlike.server.tl.baselib.serialize.player.PlayerBlobDb;
import com.ttlike.server.tl.baselib.serialize.player.QuicklyBuyDb;

import static com.gy.server.game.player.event.PlayerEventType.day5Refresh;
import static com.gy.server.game.player.event.PlayerEventType.login;

/**
 * <AUTHOR> - [Create on 2019/04/19 14:17]
 */
public class PlayerQuicklyBuyModel extends PlayerModel implements PlayerEventHandler {

    private static final PlayerEventType[] eventTypes = new PlayerEventType[]{day5Refresh, login};

    /**
     * 购买数量集合, <itemId: num>, 每天进行清空
     */
    private Map<Integer, Integer> buyMap = new HashMap<>();

    public PlayerQuicklyBuyModel(Player player) {
        super(player);
    }

    public Map<Integer, Integer> getBuyMap() {
        return buyMap;
    }

    @Override
    protected void loadData(PlayerBlobDb playerBlob) {
        QuicklyBuyDb db = playerBlob.getQuicklyBuyModel();
        if (db != null) {
            List<QuicklyBuyDb.BuyItem> buyItemList = db.getBuyItemsList();
            for (QuicklyBuyDb.BuyItem buyItem : buyItemList) {
                buyMap.put(buyItem.getItemId(), buyItem.getNum());
            }
        }
    }

    @Override
    protected void saveData(PlayerBlobDb playerBlob) {
        QuicklyBuyDb builder = new QuicklyBuyDb();
        for (Map.Entry<Integer, Integer> entry : buyMap.entrySet()) {
            QuicklyBuyDb.BuyItem b = new QuicklyBuyDb.BuyItem();
            b.setItemId(entry.getKey());
            b.setNum(entry.getValue());

            builder.getBuyItemsList().add(b);
        }
        playerBlob.setQuicklyBuyModel(builder);
    }

    @Override
    public PlayerEventType[] getEventTypes() {
        return eventTypes;
    }

    @Override
    public void handle(PlayerEvent event) {
        switch (event.getEventType()) {
            case day5Refresh: {
                buyMap.clear();
                break;
            }
        }
    }

}

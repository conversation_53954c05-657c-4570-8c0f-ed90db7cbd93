package com.gy.server.game.leagueBanquet;

import com.gy.server.core.Configuration;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueModel;
import com.gy.server.game.league.event.LeagueEvent;
import com.gy.server.game.league.event.LeagueEventHandler;
import com.gy.server.game.league.event.LeagueEventType;
import com.gy.server.game.leagueBanquet.bean.LeagueBanquetRankBean;
import com.gy.server.game.leagueBanquet.stage.AbsBanquetStage;
import com.gy.server.game.leagueBanquet.stage.BanquetStageType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.timePlayPanel.TimePlayType;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.serialize.league.LeagueBanquetModelDb;
import com.ttlike.server.tl.baselib.serialize.league.LeagueBanquetRankBeanDb;
import com.ttlike.server.tl.baselib.serialize.league.LeagueBlobDb;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 帮派宴会Model
 *
 * <AUTHOR> - [Created on 2023/5/17 11:19]
 */
public class LeagueBanquetModel extends LeagueModel implements LeagueEventHandler {
    /**
     * 轮次Id
     */
    private int roundId;
    /**
     * 第几阶段
     * 默认初始化阶段
     */
    private BanquetStageType stage = BanquetStageType.init;
    /**
     * 状态处理是否完成
     */
    private boolean stageDealing;
    /**
     * 状态开始时间
     */
    private long stageStartTime;

    /**
     * 排行榜Map
     */
    private Map<Long, LeagueBanquetRankBean> rankMap = new HashMap<>();
    /**
     * 格子图案 -1为空图案
     */
    private List<Integer> girdsPic = new ArrayList<>();
    /**
     * 答案图片Id
     */
    private Integer answerPicId;
    /**
     * 今天活动是否结束
     */
    private boolean isEnd;
    /**
     * 玩家所站格子
     * Key:玩家ID Value:格子ID
     */
    private Map<Long, Integer> moveMap = new HashMap<>();
    /*-----------------------------内存数据----------------------------------*/
    /**
     * stage各个状态处理类
     */
    private Map<BanquetStageType, AbsBanquetStage> stageMap = new HashMap<>();

    /**
     * GM开启 GM开启时间 Gm结束时间
     */
    private boolean gmOpen;
    private LocalDateTime gmStartTime;
    private LocalDateTime gmEndTime;

    public void gmOpen() {
        if (Configuration.runMode.isTest()) {
            LocalDateTime now = ServerConstants.getCurrentTimeLocalDateTime();
            this.gmOpen = true;
            this.gmStartTime = now.plusMinutes(1);
            this.gmEndTime = now.plusHours(1);
            this.isEnd = false;
        }
    }

    public void gmClose() {
        this.gmOpen = false;
    }

    public boolean isOpenNow(Player player) {
        if (isEnd) {
            return false;
        }
        if (gmOpen) {
            LocalDateTime now = ServerConstants.getCurrentTimeLocalDateTime();
            return now.isAfter(gmStartTime) && now.isBefore(gmEndTime);
        }
        return TimePlayType.leagueBanquet.isOpenNow(player);
    }


    /**
     * 状态检测并切换
     */
    private void stageCheckAndChange() {
        //结束了
        if (!isOpenNow(null)) {
            return;
        }
        AbsBanquetStage absBanquetStage = stageMap.get(stage);
        if (!absBanquetStage.isDealFinish()) {
            absBanquetStage.deal();
            syncStageAll();
            absBanquetStage.clear();
        }
        if (absBanquetStage.isDealFinish()) {
            long currentTimeMillis = ServerConstants.getCurrentTimeMillis();
            int duration = absBanquetStage.duration();
            if (currentTimeMillis > stageStartTime + duration * DateTimeUtil.MillisOfSecond) {
                // 切换下个状态
                this.stage = absBanquetStage.nextStage();
                this.stageStartTime = currentTimeMillis;
                this.stageDealing = false;
            }
        }
    }

    @Override
    public void init() {
        for (BanquetStageType stageType : BanquetStageType.values()) {
            stageMap.put(stageType, stageType.create(getLeague()));
        }
        addPeriodTask(DateTimeUtil.MillisOfSecond, this::stageCheckAndChange);
    }

    public LeagueBanquetModel(League league) {
        super(league);
    }

    @Override
    protected void loadData(LeagueBlobDb leagueBlobDb) {
        LeagueBanquetModelDb leagueBanquetModelDb = leagueBlobDb.getLeagueBanquetModelDb();
        if (Objects.nonNull(leagueBanquetModelDb)) {
            this.roundId = leagueBanquetModelDb.getRoundId();
            this.stage = BanquetStageType.of(leagueBanquetModelDb.getStage());
            this.stageDealing = leagueBanquetModelDb.isStageDealing();
            this.stageStartTime = leagueBanquetModelDb.getStageStartTime();
            for (LeagueBanquetRankBeanDb leagueBanquetRankBeanDb : leagueBanquetModelDb.getRankMap().values()) {
                LeagueBanquetRankBean leagueBanquetRankBean = new LeagueBanquetRankBean(leagueBanquetRankBeanDb);
                this.rankMap.put(leagueBanquetRankBean.getPlayerId(), leagueBanquetRankBean);
            }
            this.girdsPic = leagueBanquetModelDb.getGirdsPic();
            this.answerPicId = leagueBanquetModelDb.getAnswerPicId();
            this.isEnd = leagueBanquetModelDb.isEnd();
            this.moveMap = leagueBanquetModelDb.getMoveMap();
        }
    }

    @Override
    protected void saveData(LeagueBlobDb leagueBlobDb) {
        LeagueBanquetModelDb leagueBanquetModelDb = new LeagueBanquetModelDb();
        leagueBanquetModelDb.setRoundId(roundId);
        leagueBanquetModelDb.setStage(stage.getType());
        leagueBanquetModelDb.setStageDealing(stageDealing);
        leagueBanquetModelDb.setStageStartTime(stageStartTime);
        for (LeagueBanquetRankBean banquetRankBean : rankMap.values()) {
            leagueBanquetModelDb.getRankMap().put(banquetRankBean.getPlayerId(), banquetRankBean.genDb());
        }
        leagueBanquetModelDb.setGirdsPic(girdsPic);
        leagueBanquetModelDb.setAnswerPicId(answerPicId);
        leagueBanquetModelDb.setEnd(isEnd);
        leagueBanquetModelDb.setMoveMap(moveMap);
        leagueBlobDb.setLeagueBanquetModelDb(leagueBanquetModelDb);
    }

    public int getRoundId() {
        return roundId;
    }

    public void setRoundId(int roundId) {
        this.roundId = roundId;
    }

    public BanquetStageType getStage() {
        return stage;
    }

    public void setStage(BanquetStageType stage) {
        this.stage = stage;
    }

    public Map<Long, LeagueBanquetRankBean> getRankMap() {
        return rankMap;
    }

    public List<Integer> getGirdsPic() {
        return girdsPic;
    }

    public void setGirdsPic(List<Integer> girdsPic) {
        this.girdsPic = girdsPic;
    }

    public boolean isStageDealing() {
        return stageDealing;
    }

    public void setStageDealing(boolean stageDealing) {
        this.stageDealing = stageDealing;
    }

    public long getStageStartTime() {
        return stageStartTime;
    }

    public void setStageStartTime(long stageStartTime) {
        this.stageStartTime = stageStartTime;
    }

    public Integer getAnswerPicId() {
        return answerPicId;
    }

    public void setAnswerPicId(Integer answerPicId) {
        this.answerPicId = answerPicId;
    }

    public Map<BanquetStageType, AbsBanquetStage> getStageMap() {
        return stageMap;
    }

    public void setStageMap(Map<BanquetStageType, AbsBanquetStage> stageMap) {
        this.stageMap = stageMap;
    }

    public void setEnd(boolean end) {
        this.isEnd = end;
    }

    public Map<Long, Integer> getMoveMap() {
        return moveMap;
    }

    public void setMoveMap(Map<Long, Integer> moveMap) {
        this.moveMap = moveMap;
    }

    public boolean isEnd() {
        return isEnd;
    }

    @Override
    public LeagueEventType[] getEventTypes() {
        return new LeagueEventType[]{
                LeagueEventType.day5Refresh,
                LeagueEventType.enterScene,
                LeagueEventType.quitLeague,
        };
    }

    @Override
    public void handle(LeagueEvent event) {
        switch (event.getEventType()) {
            case day5Refresh: {
                this.isEnd = false;
                break;
            }
            case enterScene: {
                // 活动正在开启 同步活动当前阶段
                if (isOpenNow(null)) {
                    Long memberId = event.getParam(0);
                    if (PlayerManager.isOnline(memberId)) {
                        Player member = PlayerManager.getOnlinePlayer(memberId);
                        syncStage(member);
                        syncRank(member);
                    }
                }
                break;
            }
            case quitLeague: {
                long playerId = event.getParam(0);
                moveMap.remove(playerId);
                rankMap.remove(playerId);
                break;
            }
        }
    }

    /**
     * 同步当前状态
     */
    public void syncStage(Player member) {
        AbsBanquetStage absBanquetStage = stageMap.get(stage);
        if (absBanquetStage.isSync()) {
            member.send(PtCode.BANQUET_STAGE_SYNC, absBanquetStage.buildStageSync(member));
        }
    }

    /**
     * 同步排行榜信息
     */
    public void syncRank(Player member) {
        List<LeagueBanquetRankBean> rankList = new ArrayList<>(rankMap.values());
        Collections.sort(rankList);
        PbProtocol.BanquetRankSync.Builder builder = PbProtocol.BanquetRankSync.newBuilder();
        for (int i = 0; i < rankList.size(); i++) {
            int rank = i + 1;
            LeagueBanquetRankBean leagueBanquetRankBean = rankList.get(i);
            PbProtocol.BanquetRankSync.BanquetRank banquetRank = leagueBanquetRankBean.genPb(rank);
            builder.addNode(banquetRank);
            if (leagueBanquetRankBean.getPlayerId() == member.getPlayerId()) {
                builder.setMyNode(banquetRank);
            }
        }
        member.send(PtCode.BANQUET_RANK_SYNC, builder.build());

    }

    /**
     * 同步排行榜信息
     */
    public void syncRankAll() {
        List<LeagueBanquetRankBean> rankList = new ArrayList<>(rankMap.values());
        Collections.sort(rankList);
        PbProtocol.BanquetRankSync.Builder builder = PbProtocol.BanquetRankSync.newBuilder();
        Map<Long, PbProtocol.BanquetRankSync.BanquetRank> rankPbMap = new HashMap<>();
        for (int i = 0; i < rankList.size(); i++) {
            int rank = i + 1;
            LeagueBanquetRankBean leagueBanquetRankBean = rankList.get(i);
            PbProtocol.BanquetRankSync.BanquetRank banquetRank = leagueBanquetRankBean.genPb(rank);
            builder.addNode(banquetRank);
            rankPbMap.put(leagueBanquetRankBean.getPlayerId(), banquetRank);
        }
        for (Long memberId : getLeague().getScenePlayerIdSet()) {
            if (PlayerManager.isOnline(memberId)) {
                if (rankPbMap.containsKey(memberId)) {
                    builder.setMyNode(rankPbMap.get(memberId));
                } else {
                    builder.clearMyNode();
                }
                PlayerManager.getOnlinePlayer(memberId).send(PtCode.BANQUET_RANK_SYNC, builder.build());
            }
        }
    }

    /**
     * 同步当前状态
     */
    public void syncStageAll() {
        AbsBanquetStage absBanquetStage = stageMap.get(stage);
        if (absBanquetStage.isSync()) {
            for (Long memberId : getLeague().getScenePlayerIdSet()) {
                if (PlayerManager.isOnline(memberId)) {
                    Player onlinePlayer = PlayerManager.getOnlinePlayer(memberId);
                    onlinePlayer.send(PtCode.BANQUET_STAGE_SYNC, absBanquetStage.buildStageSync(onlinePlayer));
                }
            }
        }

    }

}

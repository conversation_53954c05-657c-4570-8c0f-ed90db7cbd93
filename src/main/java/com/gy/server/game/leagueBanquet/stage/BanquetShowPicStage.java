package com.gy.server.game.leagueBanquet.stage;

import com.gy.server.core.log.CommonLogger;
import com.gy.server.game.league.League;
import com.gy.server.game.leagueBanquet.LeagueBanquetModel;
import com.gy.server.game.leagueBanquet.LeagueBanquetService;
import com.gy.server.game.leagueBanquet.template.LeagueBanquetTemplate;
import com.gy.server.game.player.Player;
import com.gy.server.packet.PbProtocol;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 显示图片阶段
 *
 * <AUTHOR> - [Created on 2023/5/17 15:54]
 */
public class BanquetShowPicStage extends AbsBanquetStage {


    protected BanquetShowPicStage(BanquetStageType nowStage, League league) {
        super(nowStage, league);
    }

    @Override
    public void deal() {
        //随机图案
        LeagueBanquetTemplate leagueBanquetTemplate = getLeagueBanquetTemplate();
        List<Integer> picList = new ArrayList<>();
        for (int i = 0; i < leagueBanquetTemplate.picNumList.size(); i++) {
            int picId = i + 1;
            Integer picNum = leagueBanquetTemplate.picNumList.get(i);
            for (int j = 0; j < picNum; j++) {
                picList.add(picId);
            }
        }
        //格子数量
        int gridsNum = LeagueBanquetService.getConstant().gridsNum;
        // 用空图案补全
        int picSize = picList.size();
        if (picSize < gridsNum) {
            for (int i = 0; i < gridsNum - picSize; i++) {
                picList.add(-1);
            }
        } else if (picSize > gridsNum) {
            //这种情况策划配错表了！加个保底
            CommonLogger.error("图案数量错误LeagueBanquetTemplate id:" + getLeagueBanquetModel().getRoundId());
            picList = new ArrayList<>(picList.subList(0, gridsNum - 1));
        }
        Collections.shuffle(picList);
        getLeagueBanquetModel().setGirdsPic(picList);
        // 结束标志
        finish();
    }

    @Override
    public BanquetStageType nextStage() {
        return BanquetStageType.move;
    }

    @Override
    public int duration() {
        return getLeagueBanquetTemplate().stageTime1;
    }

    @Override
    public PbProtocol.BanquetStageSync buildStageSync(Player player) {
        LeagueBanquetModel leagueBanquetModel = getLeagueBanquetModel();
        return PbProtocol.BanquetStageSync.newBuilder()
                .setRoundId(leagueBanquetModel.getRoundId())
                .setStage(leagueBanquetModel.getStage().getType())
                .setStageEndTime(leagueBanquetModel.getStageStartTime() + duration())
                .addAllPicId(leagueBanquetModel.getGirdsPic()).build();
    }

    @Override
    public boolean isSync() {
        return true;
    }
}

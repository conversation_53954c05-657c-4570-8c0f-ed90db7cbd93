package com.gy.server.game.leagueBanquet.stage;

import com.gy.server.game.league.League;

import java.util.HashMap;
import java.util.Map;
import java.util.function.BiFunction;

/**
 * 宴会阶段类型
 *
 * <AUTHOR> - [Created on 2023/5/17 15:49]
 */
public enum BanquetStageType {
    /**
     * 初始化stage
     */
    init(0, BanquetInitStage::new),
    /**
     * 显示图片阶段
     */
    showPic(1, BanquetShowPicStage::new),
    /**
     * 玩家移动阶段
     */
    move(2, BanquetMoveStage::new),
    /**
     * 显示答案阶段
     */
    showAnswer(3, BanquetShowAnswerStage::new),
    /**
     * 数据清理
     */
    dataClear(4, BanquetDataClearStage::new),

    ;
    private final int type;
    private final BiFunction<BanquetStageType, League, AbsBanquetStage> biFunction;

    private static final Map<Integer, BanquetStageType> map = new HashMap<>();

    public AbsBanquetStage create(League league) {
        return biFunction.apply(this, league);
    }

    BanquetStageType(int type, BiFunction<BanquetStageType, League, AbsBanquetStage> biFunction) {
        this.type = type;
        this.biFunction = biFunction;
    }

    public int getType() {
        return type;
    }

    static {
        for (BanquetStageType value : values()) {
            map.put(value.getType(), value);
        }
    }

    public static BanquetStageType of(int type) {
        return map.get(type);
    }
}

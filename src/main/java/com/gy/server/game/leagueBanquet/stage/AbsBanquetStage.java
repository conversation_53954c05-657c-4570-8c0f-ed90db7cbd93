package com.gy.server.game.leagueBanquet.stage;

import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueModelEnums;
import com.gy.server.game.leagueBanquet.LeagueBanquetModel;
import com.gy.server.game.leagueBanquet.LeagueBanquetService;
import com.gy.server.game.leagueBanquet.template.LeagueBanquetTemplate;
import com.gy.server.game.player.Player;
import com.gy.server.packet.PbProtocol;

/**
 * 抽象宴会阶段
 *
 * <AUTHOR> - [Created on 2023/5/17 15:55]
 */
public abstract class AbsBanquetStage {

    protected final League league;
    protected final BanquetStageType nowStage;

    protected AbsBanquetStage(BanquetStageType nowStage, League league) {
        this.nowStage = nowStage;
        this.league = league;
    }

    /**
     * 当前阶段
     */
    public BanquetStageType nowStage() {
        return nowStage;
    }

    /**
     * 阶段处理
     */
    public abstract void deal();

    /**
     * 清除数据
     */
    public void clear() {

    }

    /**
     * 阶段是否完成
     */
    public void finish() {
        getLeagueBanquetModel().setStageDealing(true);
    }

    /**
     * 阶段是否处理完成
     */
    public boolean isDealFinish() {
        return getLeagueBanquetModel().isStageDealing();
    }

    /**
     * 下个阶段
     */
    public abstract BanquetStageType nextStage();

    /**
     * 持续时间(秒)
     */
    public abstract int duration();

    /**
     * 构造阶段同步消息
     */
    public abstract PbProtocol.BanquetStageSync buildStageSync(Player player);

    /**
     * 该阶段是否同步客户端
     */
    public abstract boolean isSync();

    protected LeagueBanquetModel getLeagueBanquetModel() {
        return league.getModel(LeagueModelEnums.banquet);
    }

    protected LeagueBanquetTemplate getLeagueBanquetTemplate() {
        return LeagueBanquetService.getLeagueBanquetTemplateMap().get(getLeagueBanquetModel().getRoundId());
    }

}

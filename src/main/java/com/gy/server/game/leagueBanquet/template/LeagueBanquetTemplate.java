package com.gy.server.game.leagueBanquet.template;

import java.util.ArrayList;
import java.util.List;

/**
 * 帮派宴会关卡表
 *
 * <AUTHOR> - [Created on 2023/5/17 13:55]
 */
public class LeagueBanquetTemplate {

    public int id;
    /**
     * 阶段1时间
     */
    public int stageTime1;
    /**
     * 阶段2时间
     */
    public int stageTime2;
    /**
     * 阶段3时间
     */
    public int stageTime3;
    /**
     * 图案数量
     * index+1 = 图案ID
     * value=数量
     */
    public List<Integer> picNumList = new ArrayList<>();
    /**
     * 答对掉落库
     */
    public int correctItem;
    /**
     * 答对掉落库
     */
    public int errorItem;
    /**
     * 答对积分
     */
    public int correctIntegral;
    /**
     * 答错积分
     */
    public int errorIntegral;

}

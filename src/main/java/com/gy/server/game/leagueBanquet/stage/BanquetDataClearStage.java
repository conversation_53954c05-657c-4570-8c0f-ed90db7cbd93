package com.gy.server.game.leagueBanquet.stage;

import com.gy.server.game.league.League;
import com.gy.server.game.leagueBanquet.LeagueBanquetModel;
import com.gy.server.game.player.Player;
import com.gy.server.packet.PbProtocol;

/**
 * 数据清理阶段 等待下次开启
 *
 * <AUTHOR> - [Created on 2023/5/19 14:48]
 */
public class BanquetDataClearStage extends AbsBanquetStage {

    protected BanquetDataClearStage(BanquetStageType nowStage, League league) {
        super(nowStage, league);
    }

    @Override
    public void deal() {
        LeagueBanquetModel leagueBanquetModel = getLeagueBanquetModel();
        // 数据清理
        leagueBanquetModel.setRoundId(0);
        leagueBanquetModel.getRankMap().clear();
        // 今天的活动状态置为结束
        leagueBanquetModel.setEnd(true);
        finish();

    }

    @Override
    public BanquetStageType nextStage() {
        return BanquetStageType.init;
    }

    @Override
    public int duration() {
        return 0;
    }

    @Override
    public PbProtocol.BanquetStageSync buildStageSync(Player player) {
        return null;
    }

    @Override
    public boolean isSync() {
        return false;
    }
}

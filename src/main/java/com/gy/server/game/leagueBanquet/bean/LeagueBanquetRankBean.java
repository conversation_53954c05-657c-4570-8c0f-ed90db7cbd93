package com.gy.server.game.leagueBanquet.bean;

import com.gy.server.game.player.Player;
import com.gy.server.packet.PbProtocol;
import com.ttlike.server.tl.baselib.serialize.league.LeagueBanquetRankBeanDb;
import org.jetbrains.annotations.NotNull;

/**
 * 帮派宴会排行积分
 *
 * <AUTHOR> - [Created on 2023/5/17 15:30]
 */
public class LeagueBanquetRankBean implements Comparable<LeagueBanquetRankBean> {

    private long playerId;

    private String name;

    private long score;

    public LeagueBanquetRankBean(LeagueBanquetRankBeanDb leagueBanquetRankBeanDb) {
        this.playerId = leagueBanquetRankBeanDb.getPlayerId();
        this.name = leagueBanquetRankBeanDb.getName();
        this.score = leagueBanquetRankBeanDb.getScore();
    }

    public LeagueBanquetRankBeanDb genDb() {
        LeagueBanquetRankBeanDb leagueBanquetRankBeanDb = new LeagueBanquetRankBeanDb();
        leagueBanquetRankBeanDb.setPlayerId(playerId);
        leagueBanquetRankBeanDb.setName(name);
        leagueBanquetRankBeanDb.setScore(score);
        return leagueBanquetRankBeanDb;
    }

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public long getScore() {
        return score;
    }

    public void setScore(long score) {
        this.score = score;
    }

    public void addScore(long score) {
        this.score += score;
    }

    public LeagueBanquetRankBean(Player member) {
        this.playerId = member.getPlayerId();
        this.name = member.getName();
    }

    @Override
    public int compareTo(@NotNull LeagueBanquetRankBean o) {
        return Long.compare(o.score, this.score);
    }

    public PbProtocol.BanquetRankSync.BanquetRank genPb(int rank) {
        return PbProtocol.BanquetRankSync.BanquetRank.newBuilder()
                .setPlayerId(playerId)
                .setName(name)
                .setScore(score)
                .setRank(rank).build();
    }

}

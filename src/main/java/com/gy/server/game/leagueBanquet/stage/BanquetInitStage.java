package com.gy.server.game.leagueBanquet.stage;

import com.gy.server.game.league.League;
import com.gy.server.game.leagueBanquet.LeagueBanquetModel;
import com.gy.server.game.player.Player;
import com.gy.server.packet.PbProtocol;

/**
 * 初始化阶段
 *
 * <AUTHOR> - [Created on 2023/5/17 15:54]
 */
public class BanquetInitStage extends AbsBanquetStage {


    protected BanquetInitStage(BanquetStageType nowStage, League league) {
        super(nowStage, league);
    }

    @Override
    public void deal() {
        LeagueBanquetModel leagueBanquetModel = getLeagueBanquetModel();
        if (leagueBanquetModel.isOpenNow(null)) {
            //初始化轮次ID
            int roundId = leagueBanquetModel.getRoundId();
            leagueBanquetModel.setRoundId(++roundId);
            leagueBanquetModel.getGirdsPic().clear();
            leagueBanquetModel.getMoveMap().clear();
            finish();
        }
    }

    @Override
    public BanquetStageType nextStage() {
        //场景内没有玩家 直接终止
        if (league.getScenePlayerIdSet().isEmpty()) {
            return BanquetStageType.dataClear;
        }
        return BanquetStageType.showPic;
    }

    @Override
    public int duration() {
        return 0;
    }

    @Override
    public PbProtocol.BanquetStageSync buildStageSync(Player player) {
        return null;
    }

    @Override
    public boolean isSync() {
        return false;
    }
}

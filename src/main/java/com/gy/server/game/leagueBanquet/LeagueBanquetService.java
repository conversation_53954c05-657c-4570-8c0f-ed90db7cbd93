package com.gy.server.game.leagueBanquet;

import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.common.ConstantConfigReader;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.league.LeagueModelEnums;
import com.gy.server.game.leagueBanquet.stage.BanquetStageType;
import com.gy.server.game.leagueBanquet.template.LeagueBanquetConstant;
import com.gy.server.game.leagueBanquet.template.LeagueBanquetTemplate;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.service.Service;
import com.gy.server.game.text.Text;
import com.gy.server.game.util.StringExtUtil;
import com.gy.server.packet.PbProtocol;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 帮派宴会服务类
 *
 * <AUTHOR> - [Created on 2023/5/17 11:16]
 */
@Deprecated //功能废弃
public class LeagueBanquetService extends PlayerPacketHandler implements Service {

    /**
     * 常量表
     */
    private static LeagueBanquetConstant constant;
    /**
     * 轮次信息
     */
    private static Map<Integer, LeagueBanquetTemplate> leagueBanquetTemplateMap = new HashMap<>();
    /**
     * 最大轮次ID
     */
    private static int maxRoundId;


    /**
     * 宴会移动
     */
    @Handler(PtCode.BANQUET_MOVE_CLIENT)
    private void bossRank(Player player, PbProtocol.BanquetMoveReq req, long time) {
        PbProtocol.BanquetMoveRst.Builder rst = PbProtocol.BanquetMoveRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        logic:
        {
            int id = req.getId();
            if (id < 1 || id > constant.gridsNum) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            if (LeagueManager.isNotJoinLeague(player)) {
                rst.setResult(Text.genServerRstInfo(Text.未加入帮派));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            LeagueBanquetModel banquetModel = league.getModel(LeagueModelEnums.banquet);
            if (!banquetModel.isOpenNow(player)) {
                rst.setResult(Text.genServerRstInfo(Text.宴会活动暂未开启));
                break logic;
            }
            if (banquetModel.getStage() != BanquetStageType.move) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            banquetModel.getMoveMap().put(player.getPlayerId(), id);
        }
        player.send(PtCode.BANQUET_MOVE_SERVER, rst.build(), time);
    }


    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        Map<String, String> constantMap = ConstantConfigReader.read(ConfigFile.leagueBanquet_constant);
        constant = new LeagueBanquetConstant(constantMap);
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.leagueBanquet_banquetLevel);
        Map<Integer, LeagueBanquetTemplate> leagueBanquetTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            LeagueBanquetTemplate leagueBanquetTemplate = new LeagueBanquetTemplate();
            leagueBanquetTemplate.id = Integer.parseInt(map.get("id"));
            leagueBanquetTemplate.stageTime1 = Integer.parseInt(map.get("stageTime1"));
            leagueBanquetTemplate.stageTime2 = Integer.parseInt(map.get("stageTime2"));
            leagueBanquetTemplate.stageTime3 = Integer.parseInt(map.get("stageTime3"));
            leagueBanquetTemplate.picNumList = StringExtUtil.string2List(map.get("picNum"), ",", Integer.class);
            leagueBanquetTemplate.correctItem = Integer.parseInt(map.get("correctItem"));
            leagueBanquetTemplate.errorItem = Integer.parseInt(map.get("errorItem"));
            leagueBanquetTemplate.correctIntegral = Integer.parseInt(map.get("correctIntegral"));
            leagueBanquetTemplate.errorIntegral = Integer.parseInt(map.get("errorIntegral"));
            leagueBanquetTemplateMapTemp.put(leagueBanquetTemplate.id, leagueBanquetTemplate);
        }
        leagueBanquetTemplateMap = leagueBanquetTemplateMapTemp;
        maxRoundId = Collections.max(leagueBanquetTemplateMap.keySet());
    }

    @Override
    public void clearConfigData() {
        leagueBanquetTemplateMap.clear();
    }

    public static LeagueBanquetConstant getConstant() {
        return constant;
    }

    public static Map<Integer, LeagueBanquetTemplate> getLeagueBanquetTemplateMap() {
        return leagueBanquetTemplateMap;
    }

    public static int getMaxRoundId() {
        return maxRoundId;
    }
}

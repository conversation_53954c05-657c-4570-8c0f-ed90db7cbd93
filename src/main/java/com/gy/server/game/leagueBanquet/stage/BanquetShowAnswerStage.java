package com.gy.server.game.leagueBanquet.stage;

import com.gy.server.game.drop.DropService;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.league.League;
import com.gy.server.game.leagueBanquet.LeagueBanquetModel;
import com.gy.server.game.leagueBanquet.LeagueBanquetService;
import com.gy.server.game.leagueBanquet.bean.LeagueBanquetRankBean;
import com.gy.server.game.leagueBanquet.template.LeagueBanquetTemplate;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.packet.PbProtocol;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 显示答案阶段
 *
 * <AUTHOR> - [Created on 2023/5/17 15:54]
 */
public class BanquetShowAnswerStage extends AbsBanquetStage {


    protected BanquetShowAnswerStage(BanquetStageType nowStage, League league) {
        super(nowStage, league);
    }

    //奖励信息
    private final Map<Long, List<Reward>> rewardMap = new HashMap<>();
    private final Map<Long, Integer> scoreMap = new HashMap<>();

    @Override
    public void deal() {
        //积分结算
        LeagueBanquetModel leagueBanquetModel = getLeagueBanquetModel();
        Map<Long, Integer> moveMap = leagueBanquetModel.getMoveMap();
        List<Integer> girdsPic = leagueBanquetModel.getGirdsPic();
        Integer answerPicId = leagueBanquetModel.getAnswerPicId();
        LeagueBanquetTemplate leagueBanquetTemplate = getLeagueBanquetTemplate();
        for (Map.Entry<Long, Integer> entry : moveMap.entrySet()) {
            Long memberId = entry.getKey();
            Player onlinePlayer = PlayerManager.getOnlinePlayer(memberId);
            if (Objects.isNull(onlinePlayer)) {
                continue;
            }
            int gridId = entry.getValue();
            int picId = girdsPic.get(gridId - 1);
            int dropId = picId == answerPicId ? leagueBanquetTemplate.correctItem : leagueBanquetTemplate.errorItem;
            int score = picId == answerPicId ? leagueBanquetTemplate.correctIntegral : leagueBanquetTemplate.errorIntegral;
            //获得奖励
            List<Reward> rewardList = DropService.executeDrop(onlinePlayer, dropId, BehaviorType.banquetReward);
            rewardMap.put(memberId, rewardList);
            scoreMap.put(memberId, score);
            Map<Long, LeagueBanquetRankBean> rankMap = leagueBanquetModel.getRankMap();
            LeagueBanquetRankBean leagueBanquetRankBean = rankMap.computeIfAbsent(memberId, v -> new LeagueBanquetRankBean(onlinePlayer));
            leagueBanquetRankBean.addScore(score);
        }
        //更新排行榜 同步所有排行榜消息
        getLeagueBanquetModel().syncRankAll();
        // 结束标志
        finish();
    }

    @Override
    public BanquetStageType nextStage() {
        int roundId = getLeagueBanquetModel().getRoundId();
        int maxRoundId = LeagueBanquetService.getMaxRoundId();
        if (roundId >= maxRoundId) {
            return BanquetStageType.dataClear;
        } else {
            return BanquetStageType.init;
        }
    }

    @Override
    public int duration() {
        return getLeagueBanquetTemplate().stageTime3;
    }


    @Override
    public PbProtocol.BanquetStageSync buildStageSync(Player player) {
        LeagueBanquetModel leagueBanquetModel = getLeagueBanquetModel();
        PbProtocol.BanquetStageSync.Builder builder = PbProtocol.BanquetStageSync.newBuilder()
                .setRoundId(leagueBanquetModel.getRoundId())
                .setStage(leagueBanquetModel.getStage().getType())
                .setStageEndTime(leagueBanquetModel.getStageStartTime() + duration())
                .setAnswerPicId(leagueBanquetModel.getAnswerPicId())
                .addAllPicId(leagueBanquetModel.getGirdsPic())
                .setAddScore(scoreMap.getOrDefault(player.getPlayerId(), 0));
        if (rewardMap.containsKey(player.getPlayerId())) {
            builder.addAllReward(Reward.writeCollectionToPb(rewardMap.get(player.getPlayerId())));
        }
        return builder.build();
    }

    @Override
    public boolean isSync() {
        return true;
    }

    @Override
    public void clear() {
        rewardMap.clear();
        scoreMap.clear();
    }
}

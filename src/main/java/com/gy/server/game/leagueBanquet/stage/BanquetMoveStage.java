package com.gy.server.game.leagueBanquet.stage;

import com.gy.server.game.league.League;
import com.gy.server.game.leagueBanquet.LeagueBanquetModel;
import com.gy.server.game.player.Player;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.MathUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * 移动阶段
 *
 * <AUTHOR> - [Created on 2023/5/17 15:54]
 */
public class BanquetMoveStage extends AbsBanquetStage {


    protected BanquetMoveStage(BanquetStageType nowStage, League league) {
        super(nowStage, league);
    }

    @Override
    public void deal() {
        // 随机答案图案
        List<Integer> girdsPic = new ArrayList<>(getLeagueBanquetModel().getGirdsPic());
        // 移出空图案
        girdsPic.removeIf(v -> v == -1);
        Integer answerPicId = MathUtil.random(girdsPic);
        getLeagueBanquetModel().setAnswerPicId(answerPicId);
        // 结束标志
        finish();
    }

    @Override
    public BanquetStageType nextStage() {
        return BanquetStageType.showAnswer;
    }

    @Override
    public int duration() {
        return getLeagueBanquetTemplate().stageTime2;
    }

    @Override
    public PbProtocol.BanquetStageSync buildStageSync(Player player) {
        LeagueBanquetModel leagueBanquetModel = getLeagueBanquetModel();
        return PbProtocol.BanquetStageSync.newBuilder()
                .setRoundId(leagueBanquetModel.getRoundId())
                .setStage(leagueBanquetModel.getStage().getType())
                .setStageEndTime(leagueBanquetModel.getStageStartTime() + duration())
                .setAnswerPicId(leagueBanquetModel.getAnswerPicId()).build();
    }

    @Override
    public boolean isSync() {
        return true;
    }
}

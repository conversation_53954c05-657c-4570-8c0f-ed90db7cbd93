package com.gy.server.game.worldBoss;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModel;
import com.gy.server.game.player.event.PlayerEvent;
import com.gy.server.game.player.event.PlayerEventHandler;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.reddot.RedDot;
import com.gy.server.game.room.RoomHelper;
import com.gy.server.game.worldBoss.bean.WorldBossDanRankInfo;
import com.gy.server.game.worldBoss.template.WorldBossConstantTemplate;
import com.gy.server.packet.PbWorldBoss;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.serialize.player.PlayerBlobDb;
import com.ttlike.server.tl.baselib.serialize.worldBoss.PlayerWorldBossModelDb;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

import static com.gy.server.game.player.event.PlayerEventType.*;

/**
 * 武林悬赏令玩家存储
 * <AUTHOR> - [Created on 2023-08-15 9:47]
 */
public class PlayerWorldBossModel extends PlayerModel implements PlayerEventHandler {

    private static final PlayerEventType[] EVENT_TYPES = new PlayerEventType[]{day5Refresh, login, logout};

    /**
     * 挑战次数 按组队结算扣次数
     */
    private int collectTimes;

    /**
     * 最高段位
     */
    private int maxDan;

    /**
     * 已领取的段位奖励
     */
    private Set<Integer> danIds = new HashSet<>();

    /**
     * 下次可挑战时间
     */
    private long nextChallengeTime;

    public PlayerWorldBossModel(Player player) {
        super(player);
    }

    public void addCollectTimes(){
        this.collectTimes++;
        updateNextChallengeTime();
    }

    public void updateNextChallengeTime() {
        WorldBossConstantTemplate constant = WorldBossService.getConstant();
        this.nextChallengeTime = ServerConstants.getCurrentTimeMillis() + constant.worldBossChallengeCD * DateTimeUtil.MillisOfSecond;
    }

    public PbWorldBoss.WorldBossModel genPb(){
        PbWorldBoss.WorldBossModel.Builder builder = PbWorldBoss.WorldBossModel.newBuilder();
        builder.setStart(WorldBossGlobalData.getStart());
        builder.setEnd(WorldBossGlobalData.getEnd());
        builder.setOpenPlaceId(WorldBossService.getOpenPlaceId());
        builder.setChallengeTimes(collectTimes);
        builder.setMaxDan(maxDan);
        builder.addAllDrawDanId(danIds);
        builder.setNextChallengeTime(nextChallengeTime);

        WorldBossGlobalData globalData = GlobalDataManager.getData(GlobalDataType.worldBoss);
        WorldBossDanRankInfo rankInfo = globalData.getRankInfoMap().get(getPlayerId());
        if(rankInfo != null){
            builder.setScore(rankInfo.getScore());
            builder.setDan(rankInfo.getDan());
        }
        builder.setNextSeason(globalData.getNextSeason());
        return builder.build();
    }

    @Override
    protected void loadData(PlayerBlobDb playerBlob) {
        PlayerWorldBossModelDb worldBossModelDb = playerBlob.getWorldBossModelDb();
        if(Objects.nonNull(worldBossModelDb)){
            this.collectTimes = worldBossModelDb.getCollectTimes();
            this.maxDan = worldBossModelDb.getMaxDan();
            this.danIds = new HashSet<>(worldBossModelDb.getDanIds());
            this.nextChallengeTime = worldBossModelDb.getNextChallengeTime();
        }
    }

    @Override
    protected void saveData(PlayerBlobDb playerBlob) {
        PlayerWorldBossModelDb worldBossModelDb = new PlayerWorldBossModelDb();
        worldBossModelDb.setCollectTimes(this.collectTimes);
        worldBossModelDb.setMaxDan(maxDan);
        worldBossModelDb.setDanIds(new ArrayList<>(danIds));
        worldBossModelDb.setNextChallengeTime(nextChallengeTime);
        playerBlob.setWorldBossModelDb(worldBossModelDb);
    }

    @Override
    public PlayerEventType[] getEventTypes() {
        return EVENT_TYPES;
    }

    @Override
    public void handle(PlayerEvent event) {
        switch (event.getEventType()){
            case day5Refresh:{
                collectTimes = 0;
                break;
            }
            case login:
            case logout:{
                ThreadPool.execute(() -> RoomHelper.commonLeaveRoomLogic(getPlayerId(), false, ServerConstants.getCurrentTimeMillis()));
                break;
            }
        }
    }

    /**
     * 检查最高段位
     * @param dan
     */
    public void checkMaxDan(int dan) {
        if(dan > this.maxDan){
            maxDan = dan;

            RedDot.wulinRankReward.sync(getPlayer());
        }
    }

    public int getCollectTimes() {
        return collectTimes;
    }

    public void setCollectTimes(int collectTimes) {
        this.collectTimes = collectTimes;
    }

    public int getMaxDan() {
        //初始段位为1
        return Math.max(maxDan, 1);
    }

    public void setMaxDan(int maxDan) {
        this.maxDan = maxDan;
    }

    public Set<Integer> getDanIds() {
        return danIds;
    }

    public void setDanIds(Set<Integer> danIds) {
        this.danIds = danIds;
    }

    public long getNextChallengeTime() {
        return nextChallengeTime;
    }

    public void setNextChallengeTime(long nextChallengeTime) {
        this.nextChallengeTime = nextChallengeTime;
    }
}

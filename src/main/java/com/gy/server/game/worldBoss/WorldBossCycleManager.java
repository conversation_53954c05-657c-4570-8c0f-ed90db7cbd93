package com.gy.server.game.worldBoss;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.core.thread.AbstractRunner;
import com.gy.server.game.combat.CombatManager;
import com.gy.server.game.combat.Stage;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.worldBoss.async.WorldBossTeamRankNotifyAsync;
import com.gy.server.game.worldBoss.async.WorldBossTeamRankUpdateAsync;
import com.gy.server.game.worldBoss.stage.WorldBossStage;
import com.gy.server.utils.time.DateTimeUtil;
import com.gy.server.world.room.base.RoomMemberInfo;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * 武林悬赏 周期
 * 战斗开始接收房间信息，轮询战斗即时伤害
 * 即时伤害排行榜？？？
 *
 *
 * <AUTHOR> 2024/4/17 17:45
 **/
public class WorldBossCycleManager extends AbstractRunner {

    private long expire = DateTimeUtil.MillisOfMinute * 10;

    @Override
    public String getRunnerName() {
        return "WorldBossCycleManager";
    }


    @Override
    protected void subRunnerExecute() throws Exception {
        checkWorldBossCycle();
    }

    /**
     * 检查武林悬赏周期
     *
     */
    private void checkWorldBossCycle() {
        //获取战斗内玩家id
        WorldBossGlobalData globalData = GlobalDataManager.getData(GlobalDataType.worldBoss);
        Set<Long> playerIds = new HashSet<>(globalData.getInPlayerIds());

        for (Long playerId : playerIds) {
            if(!PlayerManager.isOnline(playerId)){
                continue;
            }

            Stage stage = CombatManager.getStageByPid(playerId);
            if(!(stage instanceof WorldBossStage)){
                continue;
            }
            WorldBossStage worldBossStage = (WorldBossStage) stage;

            if (Objects.nonNull(stage) && !worldBossStage.isEnd()) {
                //更新伤害数据
                ThreadPool.execute(new WorldBossTeamRankUpdateAsync(playerId, worldBossStage));

                //同步房间排行榜信息
                ThreadPool.execute(new WorldBossTeamRankNotifyAsync(playerId, worldBossStage.getRoomId(), true, -1));
            } else {
                String hashKey = GsRedisKey.Room.room_member_info.getRedisKey(worldBossStage.getRoomId());
                RoomMemberInfo roomMemberInfo = TLBase.getInstance().getRedisAssistant().hashGetBeans(hashKey, playerId + "", RoomMemberInfo.class);
                if(Objects.isNull(roomMemberInfo)){
                    globalData.getInPlayerIds().remove(playerId);
                }
            }
        }
    }

    @Override
    public long getRunnerInterval() {
        return 5000L;
    }

    WorldBossCycleManager(){

    }

    private static final WorldBossCycleManager instance = new WorldBossCycleManager();

    public static WorldBossCycleManager getInstance(){
        return instance;
    }
}

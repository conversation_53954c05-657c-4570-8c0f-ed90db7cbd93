package com.gy.server.game.leagueTradingCompany.template;


import com.gy.server.game.util.StringExtUtil;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;
import java.util.Map;

public class TradingCompanyProduceTemplate {

    /**
     * 主键值
     */
    public int id;

    /**
     * 功能ID
     */
    public int function;

    /**
     * 条件类型
     * 1，排名类型，参数：最低排名，最高排名
     * 2，胜负类型，参数：1胜，2负
     */
    public int conditionType;

    /**
     * 条件参数
     */
    public String conditionDesc;

    /**
     * left:最低排名    right:最高排名
     */
    public Pair<Integer, Integer> condition1;

    /**
     * 是否胜利
     */
    public boolean condition2;

    /**
     * 拍品奖励组
     */
    public List<Integer> lotsList;

    public TradingCompanyProduceTemplate(Map<String, String> map) {
        id = Integer.parseInt(map.get("id"));
        function = Integer.parseInt(map.get("function"));
        conditionType = Integer.parseInt(map.get("conditionType"));
        conditionDesc = map.get("conditionDesc");

        switch (conditionType){
            case 1:{
                List<Integer> list = StringExtUtil.string2List(conditionDesc, ",", Integer.class);
                condition1 = Pair.of(list.get(0), list.size() > 1 ? list.get(1) : list.get(0));
                break;
            }
            case 2:{
                condition2 = conditionDesc.equals("1");
                break;
            }
        }

        lotsList = StringExtUtil.string2List(map.get("lotsList"), ",", Integer.class);
    }

    @Override
    public String toString() {
        return "TradingCompanyProduceTemplate{"
                + "id=" + id
                + "function=" + function
                + "lotsList=" + lotsList
                + '}';
    }

    public boolean canOpen(int functionId, int rank, boolean outcome) {
        if(this.function == functionId){
            switch (conditionType){
                case 1:{
                    if(condition1 != null){
                        return rank >= condition1.getLeft() && rank <= condition1.getRight();
                    }
                }
                case 2:{
                    return condition2 == outcome;
                }

            }
        }

        return false;
    }
}

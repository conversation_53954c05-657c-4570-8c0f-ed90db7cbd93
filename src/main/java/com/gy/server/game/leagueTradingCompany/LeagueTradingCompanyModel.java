package com.gy.server.game.leagueTradingCompany;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueDataInterface;
import com.gy.server.game.league.LeagueModel;
import com.gy.server.game.league.PlayerLeagueModel;
import com.gy.server.game.league.event.LeagueEvent;
import com.gy.server.game.league.event.LeagueEventHandler;
import com.gy.server.game.league.event.LeagueEventType;
import com.gy.server.game.league.log.LeagueLogListType;
import com.gy.server.game.leagueTradingCompany.bean.TradingCompanyGoodsInfo;
import com.gy.server.game.leagueTradingCompany.bean.TradingCompanyInfo;
import com.gy.server.game.leagueTradingCompany.template.TradingCompanyProduceTemplate;
import com.gy.server.game.leagueTradingCompany.template.TradingCompanyTemplate;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.packet.PbTradingCompany;
import com.ttlike.server.tl.baselib.serialize.base.RedisListStringBean;
import com.ttlike.server.tl.baselib.serialize.league.LeagueBlobDb;
import com.ttlike.server.tl.baselib.serialize.leagueTradingCompany.LeagueTradingCompanyModelDb;
import com.ttlike.server.tl.baselib.serialize.leagueTradingCompany.TradingCompanyInfoDb;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

import static com.gy.server.packet.PbTradingCompany.TradingCompanyGoodsState.*;

/**
 * 拍卖模块
 * 先考虑凤凰古城的排名，还有逐鹿的胜负就行
 * <AUTHOR> 2025/6/16 17:57
 **/
public class LeagueTradingCompanyModel extends LeagueModel implements LeagueEventHandler, LeagueDataInterface {

    /**
     * 拍卖行信息map
     */
    private Map<String, TradingCompanyInfo> infoMap = new ConcurrentHashMap<>();

    /**
     * 收藏map
     * key:成员id     value:拍品id集合
     */
    private Map<Long, Set<String>> collectMap = new ConcurrentHashMap<>();

    public LeagueTradingCompanyModel(League league) {
        super(league);
    }

    @Override
    public boolean canMerge(long leagueId) {
        //当有正在进行的拍卖时不能合并
        for (TradingCompanyInfo info : infoMap.values()) {
            for (TradingCompanyGoodsInfo goodsInfo : info.getGoodsInfoMap().values()) {
                if(goodsInfo.getState() == tcgsLeagueAuctionIn || goodsInfo.getState() == tcgsWorldAuctionIn){
                    return false;
                }
            }
        }
        return true;
    }

    @Override
    public void merge(League selfLeague, League targetLeague) {

    }

    @Override
    public void removeLeague(League league) {

    }

    @Override
    public void quitLeague(long pid) {
        collectMap.remove(pid);
        getLeague().removeLog(LeagueLogListType.tradingCompanyPersonalInformation, pid);
    }

    @Override
    protected void loadData(LeagueBlobDb leagueBlobDb) {
        LeagueTradingCompanyModelDb db = leagueBlobDb.getTradingCompanyModelDb();
        if(db != null){
            for (TradingCompanyInfoDb bean : db.getInfoList()) {
                TradingCompanyInfo info = new TradingCompanyInfo(bean);
                infoMap.put(info.getId(), info);
            }
            for (Map.Entry<Long, RedisListStringBean> entry : db.getCollectMap().entrySet()) {
                collectMap.computeIfAbsent(entry.getKey(), set -> new CopyOnWriteArraySet<>()).addAll(entry.getValue().getList());
            }
        }
    }

    @Override
    protected void saveData(LeagueBlobDb leagueBlobDb) {
        LeagueTradingCompanyModelDb db = new LeagueTradingCompanyModelDb();
        for (TradingCompanyInfo bean : infoMap.values()) {
            db.getInfoList().add(bean.genDb());
        }
        for (Map.Entry<Long, Set<String>> entry : collectMap.entrySet()) {
            db.getCollectMap().put(entry.getKey(), new RedisListStringBean(entry.getValue()));
        }
        leagueBlobDb.setTradingCompanyModelDb(db);
    }

    @Override
    public LeagueEventType[] getEventTypes() {
        return new LeagueEventType[]{LeagueEventType.gveEnd, LeagueEventType.day5Refresh};
    }

    @Override
    public void handle(LeagueEvent event) {
        switch (event.getEventType()){
            case gveEnd:{
                int functionId = event.getParam(0);
                int rank = event.getParam(1);
                boolean outcome = event.getParam(2);
                Set<Long> set = event.getParam(3);

                League league = getLeague();

                for (TradingCompanyProduceTemplate bean : TradingCompanyService.getProduceMap().values()) {
                    if(bean.canOpen(functionId, rank, outcome)){
                        activeTradingCompany(bean, functionId, set, league.getLeagueId());
                    }
                }

                break;
            }

            case day5Refresh:{
                check();
                break;
            }
        }
    }

    private void check(){
        Set<String> set = new HashSet<>();
        for (TradingCompanyInfo bean : infoMap.values()) {
            boolean finish = true;
            if(ServerConstants.getCurrentTimeMillis() > bean.getEndTime()){
                for (TradingCompanyGoodsInfo goodsInfo : bean.getGoodsInfoMap().values()) {
                    if(goodsInfo.getState() == PbTradingCompany.TradingCompanyGoodsState.tcgsLeagueAuctionIn ||
                            goodsInfo.getState() == PbTradingCompany.TradingCompanyGoodsState.tcgsWorldAuctionIn){
                        finish = false;
                    }
                }
            }

            if(finish){
                set.add(bean.getId());
            }
        }

        if(!set.isEmpty()){
            set.forEach(id -> infoMap.remove(id));
        }
    }

    /**
     * 激活功能拍卖
     * @param produceId
     */
    public void activeTradingCompany(int produceId){
        TradingCompanyProduceTemplate produceTemplate = TradingCompanyService.getProduceMap().get(produceId);
        if(produceTemplate != null){
            League league = getLeague();
            if(league != null){
                activeTradingCompany(produceTemplate, produceTemplate.function, league.getAllMember(), league.getLeagueId());

                TradingCompanyGlobalData globalData = getTradingCompanyGlobalData();
                globalData.getLeagueIdSet().add(getLeague().getLeagueId());
            }
        }
    }

    /**
     * 激活功能拍卖
     * @param bean
     * @param functionId
     * @param set
     * @param leagueId
     */
    private void activeTradingCompany(TradingCompanyProduceTemplate bean, int functionId, Collection<Long> set, long leagueId){
        //处理同功能上次拍卖
        for (String id : new ArrayList<>(infoMap.keySet())) {
            TradingCompanyInfo info = infoMap.get(id);

            if(info != null && bean.function == functionId){
                info.sendDividendMail();

                infoMap.remove(info.getId());
            }
        }

        TradingCompanyTemplate template = TradingCompanyService.getInfoMap().get(functionId);
        if(template != null && !bean.lotsList.isEmpty()){
            TradingCompanyInfo info = new TradingCompanyInfo(template, bean, set, leagueId);
            infoMap.put(info.getId(), info);

            TradingCompanyHelper.dealSync(getLeague());
        }
    }

    public PbTradingCompany.TradingCompanyModel genPb(Player player) {
        PbTradingCompany.TradingCompanyModel.Builder builder = PbTradingCompany.TradingCompanyModel.newBuilder();
        infoMap.values().forEach(bean -> builder.addLeagueInfos(bean.genPb()));

        TradingCompanyGlobalData globalData = getTradingCompanyGlobalData();
        globalData.getGoodsInfoMap().values().forEach(bean -> builder.addWorldGoodsInfos(bean.genPb()));

        builder.addAllCollectIds(collectMap.getOrDefault(player.getPlayerId(), new HashSet<>()));

        PlayerLeagueModel leagueModel = player.getModel(PlayerModelEnums.league);
        builder.putAllLotNumMap(leagueModel.getLotNumMap());
        return builder.build();
    }

    public TradingCompanyGlobalData getTradingCompanyGlobalData(){
        TradingCompanyGlobalData globalData = GlobalDataManager.getData(GlobalDataType.tradingCompany);
        return globalData;
    }

    public Map<String, TradingCompanyInfo> getInfoMap() {
        return infoMap;
    }

    public void setInfoMap(Map<String, TradingCompanyInfo> infoMap) {
        this.infoMap = infoMap;
    }

    public Map<Long, Set<String>> getCollectMap() {
        return collectMap;
    }

    public void setCollectMap(Map<Long, Set<String>> collectMap) {
        this.collectMap = collectMap;
    }
}

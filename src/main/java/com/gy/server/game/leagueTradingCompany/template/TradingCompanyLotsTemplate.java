package com.gy.server.game.leagueTradingCompany.template;


import com.gy.server.game.drop.RewardTemplate;

import java.util.Map;

public class TradingCompanyLotsTemplate {

    /**
     * 拍品主键值ID
     */
    public int id;

    /**
     * 奖励
     */
    public RewardTemplate content;

    /**
     * 最低价格
     */
    public int lowestPrice;

    /**
     * 最高价格
     */
    public int highestPrice;

    /**
     * 一口价
     */
    public int buyItNowPrice;

    /**
     * 竞价次数
     */
    public int biddingNum;

    /**
     * 每周购买限制
     */
    public int limitationTimes;


    /**
     * 每次加价数值
     */
    public int addPrice;


    public TradingCompanyLotsTemplate(Map<String, String> map) {
        id = Integer.parseInt(map.get("id"));
        content = RewardTemplate.readListFromText(map.get("content")).get(0);
        lowestPrice = Integer.parseInt(map.get("lowestPrice"));
        highestPrice = Integer.parseInt(map.get("highestPrice"));
        buyItNowPrice = Integer.parseInt(map.get("buyItNowPrice"));
        biddingNum = Integer.parseInt(map.get("BiddingNum"));
        limitationTimes = Integer.parseInt(map.get("limitationTimes"));
        addPrice = (highestPrice - lowestPrice) / biddingNum;
    }

    @Override
    public String toString() {
        return "TradingCompanyLotsTemplate{"
                + "id=" + id
                + "content=" + content
                + "lowestPrice=" + lowestPrice
                + "highestPrice=" + highestPrice
                + "buyItNowPrice=" + buyItNowPrice
                + "BiddingNum=" + biddingNum
                + "limitationTimes=" + limitationTimes
                + '}';
    }
}

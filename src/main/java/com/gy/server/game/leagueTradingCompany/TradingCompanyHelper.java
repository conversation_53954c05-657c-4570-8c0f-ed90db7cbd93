package com.gy.server.game.leagueTradingCompany;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.currency.Currency;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.league.LeagueModelEnums;
import com.gy.server.game.league.PlayerLeagueModel;
import com.gy.server.game.league.log.LeagueLogType;
import com.gy.server.game.leagueTradingCompany.bean.TradingCompanyGoodsInfo;
import com.gy.server.game.leagueTradingCompany.bean.TradingCompanyInfo;
import com.gy.server.game.leagueTradingCompany.template.TradingCompanyLotsTemplate;
import com.gy.server.game.mail.MailManager;
import com.gy.server.game.mail.MailType;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.game.text.Text;
import com.gy.server.game.text.TextParamText;
import com.gy.server.packet.PbCommons;
import com.gy.server.packet.PbTradingCompany;
import com.ttlike.server.tl.baselib.thread.ThreadPool;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;

/**
 * 拍卖行辅助类
 *
 * <AUTHOR> 2025/6/20 16:30
 **/
public class TradingCompanyHelper {


    /**
     * 处理拍卖日志
     * @param bean
     * @param endTime
     */
    public static void dealAuctionLog(TradingCompanyGoodsInfo bean, long endTime) {
        long time = bean.getWorldEndTime() != 0l ? bean.getWorldEndTime() : endTime;
        int lotsId = bean.getLotsId();
        int cost = bean.getPlayerId() != 0l ? bean.getBidMap().get(bean.getPlayerId()) : 0;
        int state = bean.getState().getNumber();

        League league = LeagueManager.getLeagueById(bean.getLeagueId());
        if(league != null){
            league.addLeagueLog(LeagueLogType.tradingCompanyGroupInformation, time + "", lotsId + "", cost + "", bean.getProduceId() + "", state + "");
        }

        for (Map.Entry<Long, Integer> entry : bean.getBidMap().entrySet()) {
            League temp = LeagueManager.getLeagueByPlayerId(entry.getKey());
            if(temp != null){
                temp.addLeagueLog(LeagueLogType.tradingCompanyPersonalInformation, entry.getKey() + "", time + "", lotsId + "", cost + "", (entry.getKey() == bean.getPlayerId() ? 1 : 0) + "");
            }
        }
    }

    /**
     * 处理同步
     * @param player
     */
    public static void dealSync(Player player, Collection<Long> playerIdSet) {
        Set<Long> set = new HashSet<>();
        set.addAll(playerIdSet);

        League league = LeagueManager.getLeagueByPlayer(player);
        if(league != null){
            set.addAll(league.getAllMember());
        }

        dealSync(set);
    }

    public static void dealSync(League league){
        dealSync(league.getAllMember());
    }

    public static void dealSync(Collection<Long> playerIdSet){
        ThreadPool.execute(() -> {
            for (long memberId : playerIdSet) {
                Player onlinePlayer = PlayerManager.getOnlinePlayer(memberId);
                if(onlinePlayer != null){
                    onlinePlayer.dataSyncModule.syncTradingCompany();
                }
            }
        });
    }

    /**
     * 拍卖结束竞价处理 时间结束/一口价处理
     * @param bean
     * @param returnMap
     * @return 增加分红值
     */
    public static int auctionEndsBidDeal(TradingCompanyGoodsInfo bean, Map<Long, Integer> returnMap, Map<Long, Pair<List<Reward>, Map<Integer, Integer>>> dividendRewardMap, boolean isWorld) {
//        if (bean.getState() == PbTradingCompany.TradingCompanyGoodsState.tcgsLeagueAuctionSuccess) {
//            //一口价在出价时处理
//            return 0;
//        }

        Map.Entry<Long, Integer> bidEntry = bean.getBidMap().entrySet().stream().max(Comparator.comparingInt(Map.Entry::getValue)).get();
        bean.setState(isWorld ? PbTradingCompany.TradingCompanyGoodsState.tcgsWorldAuctionSuccess : PbTradingCompany.TradingCompanyGoodsState.tcgsLeagueAuctionSuccess);
        bean.setPlayerId(bidEntry.getKey());

        //回退其他拍卖者出价
        for (Map.Entry<Long, Integer> entry : bean.getBidMap().entrySet()) {
            if (entry.getKey() == bidEntry.getKey()) {
                //发放拍品
                TradingCompanyLotsTemplate lotsTemplate = TradingCompanyService.getLotsMap().get(bean.getLotsId());
                Reward reward = lotsTemplate.content.createReward();
                Pair<List<Reward>, Map<Integer, Integer>> pair = dividendRewardMap.computeIfAbsent(entry.getKey(), list -> Pair.of(new ArrayList<Reward>(), new HashMap<>()));
                pair.getLeft().add(reward);
                pair.getRight().merge(bean.getLotsId(), 1, Integer::sum);
            } else {
                //回退出价
                returnMap.merge(entry.getKey(), entry.getValue(), Integer::sum);
            }
        }

        return bidEntry.getValue();
    }

    public static void addDividend(League league, String goodsId, int addDividend){
        if(league != null){
            LeagueTradingCompanyModel leagueTradingCompanyModelTemp = league.getModel(LeagueModelEnums.tradingCompany);
            if(leagueTradingCompanyModelTemp != null){
                for (TradingCompanyInfo info : leagueTradingCompanyModelTemp.getInfoMap().values()) {
                    if(info.getGoodsInfoMap().containsKey(goodsId)){
                        info.addDividend(addDividend);
                    }
                }
            }
        }
    }

    /**
     * 竞拍成功发送奖励
     * @param dividendRewardMap key:成员id  value:left: right:key:lots页签id value:数量
     */
    public static void bidSuccessSendReward(Map<Long, Pair<List<Reward>, Map<Integer, Integer>>> dividendRewardMap, boolean isWorld){
        bidSuccessSendReward(dividendRewardMap, false, isWorld, 0l);
    }

    /**
     * 竞拍成功发送奖励
     * @param dividendRewardMap key:成员id  value:left: right:key:lots页签id value:数量
     * @param isSync
     * @param isWorld true世界拍卖/false帮派拍卖
     * @param highestPlayerId 一口价玩家id
     */
    public static void bidSuccessSendReward(Map<Long, Pair<List<Reward>, Map<Integer, Integer>>> dividendRewardMap, boolean isSync, boolean isWorld, long highestPlayerId) {
//        //竞拍成功奖励map key:成员id  value:left: right:key:lots页签id value:数量
//        Map<Long, Pair<List<Reward>, Map<Integer, Integer>>> dividendRewardMap = new HashMap<>();

        //更新玩家购买
        for (Map.Entry<Long, Pair<List<Reward>, Map<Integer, Integer>>> entry : dividendRewardMap.entrySet()) {
            Player player = PlayerManager.getOnlinePlayer(entry.getKey());
            if (player != null) {
                PlayerLeagueModel leagueModel = player.getModel(PlayerModelEnums.league);
                leagueModel.addLotNum(entry.getValue().getRight(), isSync);
            } else {
                ThreadPool.execute(() -> {
                    Player temp = PlayerManager.getPlayer(entry.getKey());
                    if (temp != null) {
                        PlayerLeagueModel leagueModel = temp.getModel(PlayerModelEnums.league);
                        leagueModel.addLotNum(entry.getValue().getRight(), isSync);
                    }
                });
            }
        }

        for (Map.Entry<Long, Pair<List<Reward>, Map<Integer, Integer>>> entry : dividendRewardMap.entrySet()) {
            //发邮件
            MailType mailType = highestPlayerId == entry.getKey() ? MailType.tradingDirectPurchase : MailType.tradingSuccessful;
            PbCommons.PbText title = Text.genText(mailType.getTitleId()).build();
            PbCommons.PbText content = Text.genText(mailType.getContentId(), new TextParamText(String.valueOf(isWorld ? 1 : 0))).build();
            MailManager.sendMail(mailType, entry.getKey(), title, content, ServerConstants.getCurrentTimeMillis(), entry.getValue().getLeft());
        }
    }


    /**
     * 竞拍失败返回消耗
     * @param returnMap
     * @param isWorld true世界拍卖/false帮派拍卖
     * @param returnMap
     */
    public static void bidFailReturnCost(Map<Long, Integer> returnMap, boolean isWorld) {
        //发邮件
        MailType mailType = MailType.tradingFailed;
        PbCommons.PbText title = Text.genText(mailType.getTitleId()).build();
        PbCommons.PbText content = Text.genText(mailType.getContentId(), new TextParamText(String.valueOf(isWorld ? 1 : 0))).build();

        for (Map.Entry<Long, Integer> entry : returnMap.entrySet()) {
            Reward reward = new Reward(Currency.gold.getId(), -1, entry.getValue());
            List<Reward> rewardList = new ArrayList<>();
            rewardList.add(reward);

            MailManager.sendMail(mailType, entry.getKey(), title, content, ServerConstants.getCurrentTimeMillis(), rewardList);
        }
    }

}

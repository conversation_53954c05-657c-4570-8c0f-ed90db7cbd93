package com.gy.server.game.leagueTradingCompany.template;


import java.util.Map;

public class TradingCompanyTemplate {

    /**
     * 交易行主键值
     */
    public int id;

    /**
     * 功能ID
     */
    public int function;

    /**
     * 功能结束后具体时间开启交易行（s）
     */
    public int unlockTime;

    /**
     * 分红预览最小值
     */
    public int maxDividend;

    /**
     * 分红预览最大值
     */
    public int minDividend;

    /**
     * 开启持续时间（s）
     */
    public int duration;

    /**
     * 世界拍卖行开启持续时间（s）
     */
    public int worldDuration;

    public TradingCompanyTemplate(Map<String, String> map) {
        id = Integer.parseInt(map.get("id"));
        function = Integer.parseInt(map.get("function"));
        unlockTime = Integer.parseInt(map.get("unlockTime"));
        maxDividend = Integer.parseInt(map.get("maxDividend"));
        minDividend = Integer.parseInt(map.get("minDividend"));
        duration = Integer.parseInt(map.get("duration"));
        worldDuration = Integer.parseInt(map.get("worldDuration"));
    }

    @Override
    public String toString() {
        return "TradingCompanyTemplate{"
                + "id=" + id
                + "function=" + function
                + "unlockTime=" + unlockTime
                + "maxDividend=" + maxDividend
                + "minDividend=" + minDividend
                + "duration=" + duration
                + "worldDuration=" + worldDuration
                + '}';
    }
}

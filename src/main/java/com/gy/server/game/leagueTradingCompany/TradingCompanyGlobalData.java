package com.gy.server.game.leagueTradingCompany;

import com.gy.server.game.global.GlobalData;
import com.gy.server.game.leagueTradingCompany.bean.TradingCompanyGoodsInfo;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.ttlike.server.tl.baselib.serialize.leagueTradingCompany.TradingCompanyGlobalDataDb;
import com.ttlike.server.tl.baselib.serialize.leagueTradingCompany.TradingCompanyGoodsInfoDb;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * 拍卖全局数据
 *
 * <AUTHOR> 2025/6/18 20:01
 **/
public class TradingCompanyGlobalData extends GlobalData {

    /**
     * 正在进行拍卖的帮派id集合
     */
    private Set<Long> leagueIdSet = new CopyOnWriteArraySet<>();

    /**
     * 世界服拍卖商品信息
     */
    private Map<String, TradingCompanyGoodsInfo> goodsInfoMap = new ConcurrentHashMap<>();

    public void addWorldGoodsInfo(TradingCompanyGoodsInfo bean) {
        goodsInfoMap.put(bean.getId(), bean);
    }

    @Override
    public void readFromPb(byte[] bytes) throws Exception {
        TradingCompanyGlobalDataDb db = PbUtilCompress.decode(TradingCompanyGlobalDataDb.class, bytes);
        if(db != null){
            this.leagueIdSet.addAll(db.getLeagueIdSet());
            for (TradingCompanyGoodsInfoDb bean : db.getGoodsInfoList()) {
                TradingCompanyGoodsInfo goodsInfo = new TradingCompanyGoodsInfo(bean);
                goodsInfoMap.put(goodsInfo.getId(), goodsInfo);
            }
        }
    }

    @Override
    public byte[] writeToPb() {
        TradingCompanyGlobalDataDb db = new TradingCompanyGlobalDataDb();
        db.getLeagueIdSet().addAll(leagueIdSet);
        for (TradingCompanyGoodsInfo bean : goodsInfoMap.values()) {
            db.getGoodsInfoList().add(bean.genDb());
        }

        return PbUtilCompress.encode(db);
    }

    public Set<Long> getLeagueIdSet() {
        return leagueIdSet;
    }

    public void setLeagueIdSet(Set<Long> leagueIdSet) {
        this.leagueIdSet = leagueIdSet;
    }

    public Map<String, TradingCompanyGoodsInfo> getGoodsInfoMap() {
        return goodsInfoMap;
    }

    public void setGoodsInfoMap(Map<String, TradingCompanyGoodsInfo> goodsInfoMap) {
        this.goodsInfoMap = goodsInfoMap;
    }

}

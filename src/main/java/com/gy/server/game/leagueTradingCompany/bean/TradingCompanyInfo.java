package com.gy.server.game.leagueTradingCompany.bean;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.currency.Currency;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.leagueTradingCompany.TradingCompanyService;
import com.gy.server.game.leagueTradingCompany.template.TradingCompanyLotsTemplate;
import com.gy.server.game.leagueTradingCompany.template.TradingCompanyProduceTemplate;
import com.gy.server.game.leagueTradingCompany.template.TradingCompanyTemplate;
import com.gy.server.game.mail.MailManager;
import com.gy.server.game.mail.MailType;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbCommons;
import com.gy.server.packet.PbTradingCompany;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.serialize.leagueTradingCompany.TradingCompanyGoodsInfoDb;
import com.ttlike.server.tl.baselib.serialize.leagueTradingCompany.TradingCompanyInfoDb;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 拍卖行信息
 * 帮派拍卖  -> 成功 -> 增加分红
 *         -> 失败 -> 进入世界拍卖
 * 世界拍卖  -> 成功 -> 增加分红
 *         -> 失败 -> 流派，增加最小值分红
 *
 * 帮派拍卖持续到下个次功能的拍卖开启
 * <AUTHOR> 2025/6/16 18:01
 **/
public class TradingCompanyInfo {

    /**
     * 拍卖行唯一id
     * function - 时间戳
     */
    private String id;

    /**
     * Produce页签id
     */
    private int produceId;

    /**
     * 开始时间
     */
    private long startTime;

    /**
     * 结束时间
     */
    private long endTime;

    /**
     * 拍卖商品map
     */
    private Map<String, TradingCompanyGoodsInfo> goodsInfoMap = new ConcurrentHashMap<>();


    /***************************************分红****************************************/

    /**
     * 总分红值
     */
    private AtomicInteger allDividend = new AtomicInteger(0);

    /**
     * 可分红成员id集合
     */
    private Set<Long> canDividendMemberIdSet = new HashSet<>();

    /**
     * 已领取分红成员id集合
     */
    private Set<Long> receiveDividendMemberIdSet = new HashSet<>();


    public TradingCompanyInfo(TradingCompanyTemplate template, TradingCompanyProduceTemplate produceTemplate, Collection<Long> set, long leagueId) {
        long now = ServerConstants.getCurrentTimeMillis();

        this.id = produceTemplate.id + "-" + now;
        this.produceId = produceTemplate.id;
        this.startTime = now + template.unlockTime * DateTimeUtil.MillisOfSecond;
        this.endTime = this.startTime + template.duration * DateTimeUtil.MillisOfSecond;
        this.canDividendMemberIdSet.addAll(set);

        this.startTime = 0l;

        for (int lotsId : produceTemplate.lotsList) {
            TradingCompanyLotsTemplate lotsTemplate = TradingCompanyService.getLotsMap().get(lotsId);
            if(lotsTemplate != null){
                TradingCompanyGoodsInfo goodsInfo = new TradingCompanyGoodsInfo(lotsId, leagueId, produceTemplate);
                goodsInfoMap.put(goodsInfo.getId(), goodsInfo);
            }
        }
    }

    public TradingCompanyInfo(TradingCompanyInfoDb db) {
        this.id = db.getId();
        this.produceId = db.getProduceId();
        this.startTime = db.getStartTime();
        this.endTime = db.getEndTime();
        for (TradingCompanyGoodsInfoDb bean : db.getGoodsInfoList()) {
            TradingCompanyGoodsInfo info = new TradingCompanyGoodsInfo(bean);
            goodsInfoMap.put(info.getId(), info);
        }
        this.allDividend = new AtomicInteger(db.getAllDividend());
        this.canDividendMemberIdSet.addAll(db.getCanDividendMemberIdSet());
        this.receiveDividendMemberIdSet.addAll(db.getReceiveDividendMemberIdSet());
    }

    /**
     * 获取分红奖励
     * @return
     */
    public Reward getDividendReward(){
        TradingCompanyProduceTemplate produceTemplate = TradingCompanyService.getProduceMap().get(produceId);
        TradingCompanyTemplate template = TradingCompanyService.getInfoMap().get(produceTemplate.function);
        if(canDividendMemberIdSet.size() > 0){
            int dividend = allDividend.get() / canDividendMemberIdSet.size();
            dividend = dividend > template.maxDividend ? template.maxDividend : dividend < template.minDividend ? template.minDividend : dividend;
            if(dividend > 0){
                return new Reward(Currency.gold.getId(), -1, dividend);
            }
        }

        return null;
    }

    /**
     * 发送分红邮件
     */
    public void sendDividendMail() {
        Set<Long> set = new HashSet<>();
        set.addAll(canDividendMemberIdSet);
        set.removeAll(receiveDividendMemberIdSet);

        if(CollectionUtil.isNotEmpty(set)){
            Reward reward = getDividendReward();
            if(reward != null){
                ThreadPool.execute(() -> {
                    long sendTime = ServerConstants.getCurrentTimeMillis();
                    for (long playerId : set) {
                        //分红邮件
                        MailType mailType = MailType.tradingDividendReissue;
                        PbCommons.PbText titleText = Text.genText(mailType.getTitleId()).build();
                        PbCommons.PbText contentText = Text.genText(mailType.getContentId()).build();

                        List<Reward> rewardList = new ArrayList<>();
                        rewardList.add(reward);
                        MailManager.sendMail(mailType, playerId, titleText, contentText, sendTime, rewardList);
                    }
                });
            }
        }
    }

    public PbTradingCompany.TradingCompanyInfo genPb() {
        PbTradingCompany.TradingCompanyInfo.Builder builder = PbTradingCompany.TradingCompanyInfo.newBuilder();
        builder.setId(id);
        builder.setProduceId(produceId);
        builder.setStartTime(startTime);
        builder.setEndTime(endTime);
        goodsInfoMap.values().forEach(bean -> builder.addGoodsInfos(bean.genPb()));
        builder.setAllDividend(allDividend.get());
        builder.addAllCanDividendMemberIds(canDividendMemberIdSet);
        builder.addAllReceiveDividendMemberIds(receiveDividendMemberIdSet);
        return builder.build();
    }

    public TradingCompanyInfoDb genDb() {
        TradingCompanyInfoDb db = new TradingCompanyInfoDb();
        db.setId(id);
        db.setProduceId(produceId);
        db.setStartTime(startTime);
        db.setEndTime(endTime);
        for (TradingCompanyGoodsInfo bean : goodsInfoMap.values()) {
            db.getGoodsInfoList().add(bean.genDb());
        }
        db.setAllDividend(allDividend.get());
        db.getCanDividendMemberIdSet().addAll(canDividendMemberIdSet);
        db.getReceiveDividendMemberIdSet().addAll(receiveDividendMemberIdSet);
        return db;
    }

    public void addDividend(int addDividend) {
        this.allDividend.addAndGet(addDividend);
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

//    public PbTradingCompany.TradingCompanyType getType() {
//        return type;
//    }
//
//    public void setType(PbTradingCompany.TradingCompanyType type) {
//        this.type = type;
//    }

    public int getProduceId() {
        return produceId;
    }

    public void setProduceId(int produceId) {
        this.produceId = produceId;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public Map<String, TradingCompanyGoodsInfo> getGoodsInfoMap() {
        return goodsInfoMap;
    }

    public void setGoodsInfoMap(Map<String, TradingCompanyGoodsInfo> goodsInfoMap) {
        this.goodsInfoMap = goodsInfoMap;
    }

    public AtomicInteger getAllDividend() {
        return allDividend;
    }

    public void setAllDividend(AtomicInteger allDividend) {
        this.allDividend = allDividend;
    }

    public Set<Long> getCanDividendMemberIdSet() {
        return canDividendMemberIdSet;
    }

    public void setCanDividendMemberIdSet(Set<Long> canDividendMemberIdSet) {
        this.canDividendMemberIdSet = canDividendMemberIdSet;
    }

    public Set<Long> getReceiveDividendMemberIdSet() {
        return receiveDividendMemberIdSet;
    }

    public void setReceiveDividendMemberIdSet(Set<Long> receiveDividendMemberIdSet) {
        this.receiveDividendMemberIdSet = receiveDividendMemberIdSet;
    }

}

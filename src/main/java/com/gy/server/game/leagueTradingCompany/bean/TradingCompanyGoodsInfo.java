package com.gy.server.game.leagueTradingCompany.bean;


import com.gy.server.core.ServerConstants;
import com.gy.server.game.leagueTradingCompany.template.TradingCompanyProduceTemplate;
import com.gy.server.packet.PbTradingCompany;
import com.ttlike.server.tl.baselib.serialize.leagueTradingCompany.TradingCompanyGoodsInfoDb;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 拍卖行商品信息
 *
 * <AUTHOR> 2025/6/16 17:43
 **/
public class TradingCompanyGoodsInfo {

    /**
     * 唯一标识
     * 帮派id - functionId - 时间戳
     */
    private String id;

    /**
     * 拍品主键id
     */
    private int lotsId;

    /**
     * 玩家出价map
     */
    private Map<Long, Integer> bidMap = new ConcurrentHashMap<>();

    /**
     * 竞拍成功玩家id
     */
    private long playerId;

    /**
     * 拍卖状态
     */
    private PbTradingCompany.TradingCompanyGoodsState state;

    /**
     * 拍品在世界拍卖行的过期时间
     */
    private long worldEndTime;

    /**
     * 产出帮派id
     */
    private long leagueId;

    /**
     *
     */
    private int produceId;

    /**
     * 是否完成
     */
    private boolean finish = false;

    public TradingCompanyGoodsInfo(int lotsId, long leagueId, TradingCompanyProduceTemplate produceTemplate) {
        this.id = leagueId + "-" + lotsId + "-" + ServerConstants.getCurrentTimeMillis();
        this.lotsId = lotsId;
        this.state = PbTradingCompany.TradingCompanyGoodsState.tcgsLeagueAuctionIn;

        this.leagueId = leagueId;
        this.produceId = produceTemplate.id;
    }

    public TradingCompanyGoodsInfo(TradingCompanyGoodsInfoDb bean) {
        this.id = bean.getId();
        this.lotsId = bean.getLotsId();
        this.bidMap.putAll(bean.getBidMap());
        this.playerId = bean.getPlayerId();
        this.state = PbTradingCompany.TradingCompanyGoodsState.forNumber(bean.getState());
        this.worldEndTime = bean.getWorldEndTime();
        this.leagueId = bean.getLeagueId();
        this.produceId = bean.getProduceId();
        this.finish = bean.isFinish();
    }

    public PbTradingCompany.TradingCompanyGoodsInfo genPb() {
        PbTradingCompany.TradingCompanyGoodsInfo.Builder builder = PbTradingCompany.TradingCompanyGoodsInfo.newBuilder();
        builder.setId(id);
        builder.setLotsId(lotsId);
        builder.putAllBidMap(bidMap);
        builder.setPlayerId(playerId);
        builder.setState(state);
        builder.setWorldEndTime(worldEndTime);
        return builder.build();
    }

    public TradingCompanyGoodsInfoDb genDb() {
        TradingCompanyGoodsInfoDb db = new TradingCompanyGoodsInfoDb();
        db.setId(id);
        db.setLotsId(lotsId);
        db.getBidMap().putAll(bidMap);
        db.setPlayerId(playerId);
        db.setState(state.getNumber());
        db.setWorldEndTime(worldEndTime);
        db.setLeagueId(leagueId);
        db.setProduceId(produceId);
        db.setFinish(finish);
        return db;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getLotsId() {
        return lotsId;
    }

    public void setLotsId(int lotsId) {
        this.lotsId = lotsId;
    }

    public Map<Long, Integer> getBidMap() {
        return bidMap;
    }

    public void setBidMap(Map<Long, Integer> bidMap) {
        this.bidMap = bidMap;
    }

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public PbTradingCompany.TradingCompanyGoodsState getState() {
        return state;
    }

    public void setState(PbTradingCompany.TradingCompanyGoodsState state) {
        this.state = state;
    }

    public long getWorldEndTime() {
        return worldEndTime;
    }

    public void setWorldEndTime(long worldEndTime) {
        this.worldEndTime = worldEndTime;
    }

    public long getLeagueId() {
        return leagueId;
    }

    public void setLeagueId(long leagueId) {
        this.leagueId = leagueId;
    }

    public int getProduceId() {
        return produceId;
    }

    public void setProduceId(int produceId) {
        this.produceId = produceId;
    }

    public boolean isFinish() {
        return finish;
    }

    public void setFinish(boolean finish) {
        this.finish = finish;
    }
}

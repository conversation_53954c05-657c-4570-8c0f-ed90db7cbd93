package com.gy.server.game.leagueTradingCompany;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.thread.AbstractRunner;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.league.LeagueModelEnums;
import com.gy.server.game.leagueTradingCompany.bean.TradingCompanyGoodsInfo;
import com.gy.server.game.leagueTradingCompany.bean.TradingCompanyInfo;
import com.gy.server.game.leagueTradingCompany.template.TradingCompanyLotsTemplate;
import com.gy.server.game.leagueTradingCompany.template.TradingCompanyProduceTemplate;
import com.gy.server.game.leagueTradingCompany.template.TradingCompanyTemplate;
import com.gy.server.packet.PbTradingCompany;
import com.gy.server.utils.time.DateTimeUtil;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;

/**
 * 拍卖行管理类
 *
 * <AUTHOR> 2025/6/18 19:58
 **/
public class TradingCompanyManger extends AbstractRunner {

    @Override
    public String getRunnerName() {
        return "TradingCompanyManger";
    }

    @Override
    protected void subRunnerExecute() throws Exception {
        try {
            checkLeague();
        }catch (Exception e){
            e.printStackTrace();
        }

        try {
            checkWorld();
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
     * 检查帮派拍卖行
     */
    private void checkLeague() {
        TradingCompanyGlobalData globalData = getGlobalData();
        Set<Long> leagueIdSet = globalData.getLeagueIdSet();
        Set<Long> removeIdSet = new HashSet<>();

        for (long leagueId : leagueIdSet) {
            League league = LeagueManager.getLeagueById(leagueId);
            if (league == null) {
                removeIdSet.add(leagueId);
                continue;
            }

            //出价回退map key:成员id  value:回退数值
            Map<Long, Integer> returnMap = new HashMap<>();
//          //竞拍成功奖励map key:成员id    value:key:lots页签id
//          Map<Long, Map<Integer, List<Reward>>> dividendRewardMap = new HashMap<>();
            //竞拍成功奖励map key:成员id  value:left: right:key:lots页签id value:数量
            Map<Long, Pair<List<Reward>, Map<Integer, Integer>>> dividendRewardMap = new HashMap<>();

            LeagueTradingCompanyModel model = league.getModel(LeagueModelEnums.tradingCompany);
            if(model.getInfoMap().isEmpty()){
                removeIdSet.add(leagueId);
                continue;
            }

            for (String id : new ArrayList<>(model.getInfoMap().keySet())) {
                TradingCompanyInfo info = model.getInfoMap().get(id);
                if (info != null) {
                    TradingCompanyProduceTemplate produceTemplate = TradingCompanyService.getProduceMap().get(info.getProduceId());
                    TradingCompanyTemplate infoTemplate = TradingCompanyService.getInfoMap().get(produceTemplate.function);

                    long now = ServerConstants.getCurrentTimeMillis();
                    if (now >= info.getEndTime()) {

                        for (TradingCompanyGoodsInfo bean : info.getGoodsInfoMap().values()) {
                            if(bean.isFinish()){
                                continue;
                            }

                            if(bean.getState() != PbTradingCompany.TradingCompanyGoodsState.tcgsLeagueAuctionIn){
                                continue;
                            }

                            if (bean.getBidMap().isEmpty()) {
                                //进入世界拍卖行
                                bean.setWorldEndTime(info.getEndTime() + infoTemplate.worldDuration * DateTimeUtil.MillisOfSecond);
                                bean.setState(PbTradingCompany.TradingCompanyGoodsState.tcgsWorldAuctionIn);

                                globalData.addWorldGoodsInfo(bean);
                            } else {
                                int addDividend = TradingCompanyHelper.auctionEndsBidDeal(bean, returnMap, dividendRewardMap, false);
                                info.addDividend(addDividend);

                                TradingCompanyHelper.dealAuctionLog(bean, info.getEndTime());
                                bean.setFinish(true);
                            }
                        }
                    }
                }
            }

            TradingCompanyHelper.bidFailReturnCost(returnMap, false);
            TradingCompanyHelper.bidSuccessSendReward(dividendRewardMap, false);

        }

        if(!removeIdSet.isEmpty()){
            globalData.getLeagueIdSet().removeAll(removeIdSet);
        }
    }

    /**
     * 检查世界拍卖
     */
    private void checkWorld() {
        //key:帮派id  value:key:produceId value:分红值
        Map<Long, Map<Integer, Integer>> dividendMap = new HashMap<>();
        //出价回退map key:成员id  value:回退数值
        Map<Long, Integer> returnMap = new HashMap<>();
//        //竞拍成功奖励map key:成员id
//        Map<Long, List<Reward>> dividendRewardMap = new HashMap<>();
        //竞拍成功奖励map key:成员id  value:left: right:key:lots页签id value:数量
        Map<Long, Pair<List<Reward>, Map<Integer, Integer>>> dividendRewardMap = new HashMap<>();

        TradingCompanyGlobalData globalData = getGlobalData();
        for (String id : new ArrayList<>(globalData.getGoodsInfoMap().keySet())) {
            TradingCompanyGoodsInfo goodsInfo = globalData.getGoodsInfoMap().get(id);
            if(goodsInfo != null){
                if(ServerConstants.getCurrentTimeMillis() >= goodsInfo.getWorldEndTime()){
                    if(goodsInfo.isFinish()){
                        continue;
                    }

                    if(goodsInfo.getState() != PbTradingCompany.TradingCompanyGoodsState.tcgsWorldAuctionIn){
                        continue;
                    }

                    if(goodsInfo.getBidMap().isEmpty()){
                        //流拍
                        goodsInfo.setState(PbTradingCompany.TradingCompanyGoodsState.tcgsAuctionOut);

                        TradingCompanyLotsTemplate lotsTemplate = TradingCompanyService.getLotsMap().get(goodsInfo.getLotsId());
                        dividendMap.computeIfAbsent(goodsInfo.getLeagueId(), map -> new HashMap<>()).merge(goodsInfo.getProduceId(), lotsTemplate.lowestPrice, Integer::sum);
                    }else {
                        int addDividend = TradingCompanyHelper.auctionEndsBidDeal(goodsInfo, returnMap, dividendRewardMap, true);
                        dividendMap.computeIfAbsent(goodsInfo.getLeagueId(), map -> new HashMap<>()).merge(goodsInfo.getProduceId(), addDividend, Integer::sum);
                    }

                    TradingCompanyHelper.dealAuctionLog(goodsInfo, 0l);
                }
            }
        }

        TradingCompanyHelper.bidFailReturnCost(returnMap, true);
        TradingCompanyHelper.bidSuccessSendReward(dividendRewardMap, true);
    }


    @Override
    public long getRunnerInterval() {
        return 10000l;
    }

    private TradingCompanyGlobalData getGlobalData(){
        return GlobalDataManager.getData(GlobalDataType.tradingCompany);
    }
}

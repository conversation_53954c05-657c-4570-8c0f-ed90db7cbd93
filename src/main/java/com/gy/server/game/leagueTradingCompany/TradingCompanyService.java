package com.gy.server.game.leagueTradingCompany;

import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.currency.Currency;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.league.LeagueModelEnums;
import com.gy.server.game.league.PlayerLeagueModel;
import com.gy.server.game.leagueTradingCompany.bean.TradingCompanyGoodsInfo;
import com.gy.server.game.leagueTradingCompany.bean.TradingCompanyInfo;
import com.gy.server.game.leagueTradingCompany.template.TradingCompanyLotsTemplate;
import com.gy.server.game.leagueTradingCompany.template.TradingCompanyProduceTemplate;
import com.gy.server.game.leagueTradingCompany.template.TradingCompanyTemplate;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.game.service.Service;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.CollectionUtil;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;
import java.util.concurrent.CopyOnWriteArraySet;

import static com.gy.server.packet.PbTradingCompany.TradingCompanyGoodsState.*;

/**
 * 拍卖行服务类
 *
 * <AUTHOR> 2025/6/16 17:25
 **/
public class TradingCompanyService extends PlayerPacketHandler implements Service {

    private static Map<Integer, TradingCompanyLotsTemplate> lotsMap = new HashMap<>();

    /**
     * key:produce
     */
    private static Map<Integer, TradingCompanyProduceTemplate> produceMap = new HashMap<>();

    /**
     * key:functionId
     */
    private static Map<Integer, TradingCompanyTemplate> infoMap = new HashMap<>();

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.tradingCompany_Lots);
        for (Map<String, String> map : mapList) {
            TradingCompanyLotsTemplate template = new TradingCompanyLotsTemplate(map);
            lotsMap.put(template.id, template);
        }

        mapList = ConfigReader.read(ConfigFile.tradingCompany_Produce);
        for (Map<String, String> map : mapList) {
            TradingCompanyProduceTemplate template = new TradingCompanyProduceTemplate(map);
            produceMap.put(template.id, template);
        }

        mapList = ConfigReader.read(ConfigFile.tradingCompany_tradingCompany);
        for (Map<String, String> map : mapList) {
            TradingCompanyTemplate template = new TradingCompanyTemplate(map);
            infoMap.put(template.function, template);
        }
    }

    @Override
    public void clearConfigData() {
        lotsMap.clear();
        produceMap.clear();
        infoMap.clear();
    }

    /**
     * 拍卖行信息
     */
    @Handler(PtCode.TRADING_COMPANY_INFO_REQ)
    private void tradingCompanyInfoReq(Player player, PbProtocol.TradingCompanyInfoReq req, long time){
        PbProtocol.TradingCompanyInfoRst.Builder rst = PbProtocol.TradingCompanyInfoRst.newBuilder().setResult(Text.genOkServerRstInfo());

        logic:{
            int text = commonCheck(player);
            if(text != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(text));
                break logic;
            }

            League league = LeagueManager.getLeagueByPlayer(player);
            LeagueTradingCompanyModel model = league.getModel(LeagueModelEnums.tradingCompany);
            rst.setTradingCompany(model.genPb(player));
        }

        player.send(PtCode.TRADING_COMPANY_INFO_RST, rst.build(), time);
    }

    /**
     * 拍卖行出价
     */
    @Handler(PtCode.TRADING_COMPANY_BIDING_REQ)
    private void tradingCompanyBidingReq(Player player, PbProtocol.TradingCompanyBidingReq req, long time){
        PbProtocol.TradingCompanyBidingRst.Builder rst = PbProtocol.TradingCompanyBidingRst.newBuilder().setResult(Text.genOkServerRstInfo());
        boolean isWorld = req.getIsWorld();
        boolean isHighest = req.getIsHighest();
        String id = req.getId();

        logic:{
            int text = commonCheck(player);
            if(text != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(text));
                break logic;
            }

            TradingCompanyGoodsInfo goodsInfo = getTradingCompanyGoodsInfo(player, isWorld, id);
            if(goodsInfo == null){
                rst.setResult(Text.genServerRstInfo(Text.拍卖商品不存在));
                break logic;
            }

            if(goodsInfo.getPlayerId() != 0l){
                rst.setResult(Text.genServerRstInfo(Text.拍卖商品已成交));
                break logic;
            }

            int lotsId = goodsInfo.getLotsId();
            TradingCompanyLotsTemplate lotsTemplate = lotsMap.get(lotsId);
            if(lotsTemplate == null){
                rst.setResult(Text.genServerRstInfo(Text.对应的模板数据找不到));
                break logic;
            }

            if(goodsInfo.getBidMap().containsValue(lotsTemplate.highestPrice)){
                rst.setResult(Text.genServerRstInfo(Text.拍卖商品已到最高出价));
                break logic;
            }

            PlayerLeagueModel leagueModel = player.getModel(PlayerModelEnums.league);
            if(leagueModel.getLotNumMap().getOrDefault(lotsId, 0) >= lotsTemplate.limitationTimes){
                rst.setResult(Text.genServerRstInfo(Text.购买次数达到上限));
                break logic;
            }

            int next = 0;
            if(isHighest){
                next = lotsTemplate.buyItNowPrice;
            }else {
                Optional<Map.Entry<Long, Integer>> optional = goodsInfo.getBidMap().entrySet().stream().max(Comparator.comparingInt(Map.Entry::getValue));
                if(optional.isPresent()){
                    Map.Entry<Long, Integer> entry = optional.get();
                    if(entry.getKey() == player.getPlayerId()){
                        rst.setResult(Text.genServerRstInfo(Text.拍卖商品当前己方出价已是最高));
                        break logic;
                    }
                    next = entry.getValue() + lotsTemplate.addPrice;
                }else {
                    next = lotsTemplate.lowestPrice;
                }
            }

            int diff = next - goodsInfo.getBidMap().getOrDefault(player.getPlayerId(), 0);
            if(diff <= 0){
                rst.setResult(Text.genServerRstInfo(Text.数据异常));
                break logic;
            }

            Reward reward = new Reward(Currency.gold.getId(), -1, diff);
            if(reward.check(player) != -1){
                rst.setResult(Text.genServerRstInfo(Text.消耗不足));
                break logic;
            }
            reward.remove(player, BehaviorType.tradingCompanyBid);
            goodsInfo.getBidMap().put(player.getPlayerId(), next);

            if(isHighest){
                goodsInfo.setFinish(true);

                //出价回退map key:成员id  value:回退数值
                Map<Long, Integer> returnMap = new HashMap<>();
                //竞拍成功奖励map key:成员id  value:left: right:key:lots页签id value:数量
                Map<Long, Pair<List<Reward>, Map<Integer, Integer>>> dividendRewardMap = new HashMap<>();
                int addDividend = TradingCompanyHelper.auctionEndsBidDeal(goodsInfo, returnMap, dividendRewardMap, isWorld);

                League leagueTemp = isWorld ? LeagueManager.getLeagueById(goodsInfo.getLeagueId()) : LeagueManager.getLeagueByPlayer(player);
                TradingCompanyHelper.addDividend(leagueTemp, goodsInfo.getId(), addDividend);

                TradingCompanyHelper.bidFailReturnCost(returnMap, false);
                TradingCompanyHelper.bidSuccessSendReward(dividendRewardMap, false, false, player.getPlayerId());
            }

            TradingCompanyHelper.dealSync(player, goodsInfo.getBidMap().keySet());
        }

        player.send(PtCode.TRADING_COMPANY_BIDING_RST, rst.build(), time);
    }

    /**
     * 领取分红
     */
    @Handler(PtCode.TRADING_COMPANY_RECEIVE_DIVIDEND_REQ)
    private void tradingCompanyReceiveDividendReq(Player player, PbProtocol.TradingCompanyReceiveDividendReq req, long time){
        PbProtocol.TradingCompanyReceiveDividendRst.Builder rst = PbProtocol.TradingCompanyReceiveDividendRst.newBuilder().setResult(Text.genOkServerRstInfo());
        String id = req.getId();

        logic:{
            int text = commonCheck(player);
            if(text != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(text));
                break logic;
            }

            League league = LeagueManager.getLeagueByPlayer(player);
            LeagueTradingCompanyModel leagueTradingCompanyModel = league.getModel(LeagueModelEnums.tradingCompany);

            if(!leagueTradingCompanyModel.getInfoMap().containsKey(id)){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            TradingCompanyInfo info = leagueTradingCompanyModel.getInfoMap().get(id);

            if(!info.getCanDividendMemberIdSet().contains(player.getPlayerId())){
                rst.setResult(Text.genServerRstInfo(Text.未参与玩法不能分红));
                break logic;
            }

            for (TradingCompanyGoodsInfo bean : info.getGoodsInfoMap().values()) {
                if(bean.getState() != tcgsLeagueAuctionSuccess && bean.getState() != tcgsWorldAuctionSuccess && bean.getState() != tcgsAuctionOut){
                    rst.setResult(Text.genServerRstInfo(Text.拍卖未结束));
                    break logic;
                }
            }

            Reward reward = info.getDividendReward();
            reward.add(player, BehaviorType.tradingCompanyDividend);
            rst.setReward(reward.writeToPb());
            info.getReceiveDividendMemberIdSet().add(player.getPlayerId());
            player.dataSyncModule.syncTradingCompany();
        }

        player.send(PtCode.TRADING_COMPANY_RECEIVE_DIVIDEND_RST, rst.build(), time);
    }

    /**
     * 拍卖品收藏
     */
    @Handler(PtCode.TRADING_COMPANY_CHANGE_COLLECT_REQ)
    private void tradingCompanyAddCollectReq(Player player, PbProtocol.TradingCompanyChangeCollectReq req, long time) {
        PbProtocol.TradingCompanyChangeCollectRst.Builder rst = PbProtocol.TradingCompanyChangeCollectRst.newBuilder().setResult(Text.genOkServerRstInfo());
        boolean isWorld = req.getIsWorld();
        boolean isCancel = req.getIsCancel();
        String id = req.getId();

        logic:
        {
            int text = commonCheck(player);
            if (text != Text.没有异常) {
                rst.setResult(Text.genServerRstInfo(text));
                break logic;
            }

            League league = LeagueManager.getLeagueByPlayer(player);
            LeagueTradingCompanyModel leagueTradingCompanyModel = league.getModel(LeagueModelEnums.tradingCompany);

            TradingCompanyGoodsInfo goodsInfo = getTradingCompanyGoodsInfo(player, isWorld, id);
            if (goodsInfo == null) {
                rst.setResult(Text.genServerRstInfo(Text.拍卖商品不存在));
                break logic;
            }
            Set<String> idSet = leagueTradingCompanyModel.getCollectMap().computeIfAbsent(player.getPlayerId(), set -> new CopyOnWriteArraySet<>());
            if (isCancel) {
                idSet.remove(id);
                if (idSet.isEmpty()) {
                    leagueTradingCompanyModel.getCollectMap().remove(player.getPlayerId());
                }
            } else {
                idSet.add(id);
            }

            player.dataSyncModule.syncTradingCompany();
        }

        player.send(PtCode.TRADING_COMPANY_CHANGE_COLLECT_RST, rst.build(), time);
    }

    /**
     * 拍卖品收藏信息
     */
    @Handler(PtCode.TRADING_COMPANY_COLLECT_INFO_REQ)
    private void tradingCompanyCollectInfoReq(Player player, PbProtocol.TradingCompanyCollectInfoReq req, long time){
        PbProtocol.TradingCompanyCollectInfoRst.Builder rst = PbProtocol.TradingCompanyCollectInfoRst.newBuilder().setResult(Text.genOkServerRstInfo());

        logic:{
            int text = commonCheck(player);
            if (text != Text.没有异常) {
                rst.setResult(Text.genServerRstInfo(text));
                break logic;
            }

            League league = LeagueManager.getLeagueByPlayer(player);
            LeagueTradingCompanyModel leagueTradingCompanyModel = league.getModel(LeagueModelEnums.tradingCompany);
            Set<String> set = leagueTradingCompanyModel.getCollectMap().get(player.getPlayerId());
            if(CollectionUtil.isNotEmpty(set)){
                Map<String, TradingCompanyGoodsInfo> goodsInfoMap = new HashMap<>();

                for (TradingCompanyInfo info : leagueTradingCompanyModel.getInfoMap().values()) {
                    for (TradingCompanyGoodsInfo goodsInfo : info.getGoodsInfoMap().values()) {
                        if(set.contains(goodsInfo.getId())){
                            goodsInfoMap.put(goodsInfo.getId(), goodsInfo);
                        }
                    }
                }

                TradingCompanyGlobalData globalData = GlobalDataManager.getData(GlobalDataType.tradingCompany);
                for (TradingCompanyGoodsInfo goodsInfo : globalData.getGoodsInfoMap().values()) {
                    if(set.contains(goodsInfo.getId())){
                        goodsInfoMap.put(goodsInfo.getId(), goodsInfo);
                    }
                }

                Set<String> temp = new HashSet<>(set);
                temp.removeAll(goodsInfoMap.keySet());
                set.removeAll(temp);

                for (TradingCompanyGoodsInfo goodsInfo : goodsInfoMap.values()) {
                    rst.addGoodsInfos(goodsInfo.genPb());
                }
            }
        }

        player.send(PtCode.TRADING_COMPANY_COLLECT_INFO_RST, rst.build(), time);
    }

    public TradingCompanyGoodsInfo getTradingCompanyGoodsInfo(Player player, boolean isWorld, String id){
        if(isWorld){
            TradingCompanyGlobalData globalData = GlobalDataManager.getData(GlobalDataType.tradingCompany);
            return globalData.getGoodsInfoMap().get(id);
        }else {
            League league = LeagueManager.getLeagueByPlayer(player);
            LeagueTradingCompanyModel model = league.getModel(LeagueModelEnums.tradingCompany);

            for (TradingCompanyInfo bean : model.getInfoMap().values()) {
                if(bean.getGoodsInfoMap().containsKey(id)){
                    return bean.getGoodsInfoMap().get(id);
                }
            }
        }

        return null;
    }

    private int commonCheck(Player player){
        //function
//        if(Function..isNotOpen(player)){
//            return Text.功能未开启;
//        }

        League league = LeagueManager.getLeagueByPlayer(player);
        if(league == null){
            return Text.未加入帮派;
        }

        return Text.没有异常;
    }

    public static Map<Integer, TradingCompanyLotsTemplate> getLotsMap() {
        return lotsMap;
    }

    public static Map<Integer, TradingCompanyProduceTemplate> getProduceMap() {
        return produceMap;
    }

    public static Map<Integer, TradingCompanyTemplate> getInfoMap() {
        return infoMap;
    }

}

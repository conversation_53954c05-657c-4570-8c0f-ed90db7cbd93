package com.gy.server.game.redBearRevenge.template;

import java.util.Map;

/**
 * 红熊复仇宝箱模板
 *
 * <AUTHOR> 2024/10/17 16:50
 **/
public class RedBearRevengeChestTemplate {

    /**
     * npc id
     */
    public int npcId;

    /**
     * 随机掉落id
     */
    public int dropId;

    /**
     * 掉落权重
     */
    public int dropProbability;

    public RedBearRevengeChestTemplate(Map<String, String> map) {
        this.npcId = Integer.parseInt(map.get("npcId"));
        this.dropId = Integer.parseInt(map.get("dropId"));
        this.dropProbability = Integer.parseInt(map.get("dropProbability"));
    }
}

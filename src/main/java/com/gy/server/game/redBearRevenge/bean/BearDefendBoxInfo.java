package com.gy.server.game.redBearRevenge.bean;

import com.gy.server.core.ServerConstants;
import com.gy.server.packet.PbLeague;
import com.ttlike.server.tl.baselib.serialize.LeagueRedBearRevenge.BearDefendBoxInfoDb;

import java.util.HashSet;
import java.util.Set;

/**
 * 熊卫宝箱信息
 *
 * <AUTHOR> 2024/10/17 14:05
 **/
public class BearDefendBoxInfo {

    /**
     * 宝箱唯一标识
     */
    private long id;

    /**
     * 表格id
     */
    private int excelId;

    /**
     * 产出此宝箱的玩家id
     */
    private long producePlayerId;

    /**
     * 产出此宝箱的玩家名称
     */
    private String producePlayerName;

    /**
     * 已领取玩家id
     */
    private Set<Long> receivePlayerId = new HashSet<>();

    public BearDefendBoxInfo(int excelId, long producePlayerId, String producePlayerName) {
        this.id = ServerConstants.getCurrentTimeMillis();
        this.excelId = excelId;
        this.producePlayerId = producePlayerId;
        this.producePlayerName = producePlayerName;
    }

    public BearDefendBoxInfo(BearDefendBoxInfoDb db) {
        this.id = db.getId();
        this.excelId = db.getExcelId();
        this.producePlayerId = db.getProducePlayerId();
        this.producePlayerName = db.getProducePlayerName();
        this.receivePlayerId.addAll(db.getReceivePlayerId());
    }

    public PbLeague.BearDefendBoxInfo genPb() {
        PbLeague.BearDefendBoxInfo.Builder builder = PbLeague.BearDefendBoxInfo.newBuilder();
        builder.setId(id);
        builder.setExcelId(excelId);
        builder.setProducePlayerId(producePlayerId);
        builder.setProducePlayerName(producePlayerName);
        builder.addAllReceivePlayerId(receivePlayerId);
        return builder.build();
    }

    public BearDefendBoxInfoDb genDb() {
        BearDefendBoxInfoDb db = new BearDefendBoxInfoDb();
        db.setId(id);
        db.setExcelId(excelId);
        db.setProducePlayerId(producePlayerId);
        db.setProducePlayerName(producePlayerName);
        db.getReceivePlayerId().addAll(receivePlayerId);
        return db;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public int getExcelId() {
        return excelId;
    }

    public void setExcelId(int excelId) {
        this.excelId = excelId;
    }

    public long getProducePlayerId() {
        return producePlayerId;
    }

    public void setProducePlayerId(long producePlayerId) {
        this.producePlayerId = producePlayerId;
    }

    public Set<Long> getReceivePlayerId() {
        return receivePlayerId;
    }

    public void setReceivePlayerId(Set<Long> receivePlayerId) {
        this.receivePlayerId = receivePlayerId;
    }

    public String getProducePlayerName() {
        return producePlayerName;
    }

    public void setProducePlayerName(String producePlayerName) {
        this.producePlayerName = producePlayerName;
    }

}

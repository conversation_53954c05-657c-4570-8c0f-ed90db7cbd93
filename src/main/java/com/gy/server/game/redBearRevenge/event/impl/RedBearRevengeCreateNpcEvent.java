package com.gy.server.game.redBearRevenge.event.impl;

import com.gy.server.game.attribute.AttributeKey;
import com.gy.server.game.battleCollect.BattleCollectService;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.redBear.RedBearService;
import com.gy.server.game.redBearRevenge.LeagueRedBearRevengeModel;
import com.gy.server.game.redBearRevenge.RedBearRevengeService;
import com.gy.server.game.redBearRevenge.bean.RedBearRevengeNpcInfo;
import com.gy.server.game.redBearRevenge.event.AbstractRedBearRevengeEvent;
import com.gy.server.game.redBearRevenge.status.RedBearRevengeNodeEnum;
import com.gy.server.game.redBearRevenge.template.RedBearRevengeConst;
import com.gy.server.game.redBearRevenge.template.RedBearRevengeNpcParameterTemplate;

import java.util.*;

/**
 * npc创建
 * 1.生成npc  参数1【数量】参数2【npcID】 参数3 【坐标点】
 * <AUTHOR> 2024/10/21 16:34
 **/
public class RedBearRevengeCreateNpcEvent extends AbstractRedBearRevengeEvent {

    /**
     * npc创建数量
     */
    private int npcNum;

    /**
     * npc模板id
     */
    private int npcId;

    private List<String> points = new ArrayList<>();

    public RedBearRevengeCreateNpcEvent() {
    }

    public RedBearRevengeCreateNpcEvent(String param){
        String[] split = param.split("_");
        this.npcNum = Integer.parseInt(split[0]);
        this.npcId = Integer.parseInt(split[1]);
        this.points.addAll(Arrays.asList(split).subList(2, split.length));
        if(this.points.size() < this.npcNum){
            throw new IllegalArgumentException("RBCreateNpcEvent point is error .. ");
        }
    }

    @Override
    public void trigger(LeagueRedBearRevengeModel model) {
        //npc信息
        Map<Long, RedBearRevengeNpcInfo> npcInfos = model.getNpcInfoMap();
        //随机npc
        List<String> curPoints = new ArrayList<>(points);
        Collections.shuffle(curPoints);

        RedBearRevengeNodeEnum.log("红熊复仇-创建npc, count  " + npcNum);
        for (int i = 0; i < npcNum; i++) {
            RedBearRevengeNpcInfo npcInfo = RedBearRevengeNpcInfo.create(npcId, RedBearService.genPosition(curPoints.get(i)));
            npcInfos.put(npcInfo.getId(), npcInfo);
            RedBearRevengeNodeEnum.log("红熊复仇-创建npcId : " + npcInfo.getNpcTempId() + " npcId : " + npcId);

            if(npcInfo.getNpcTempId() == RedBearRevengeConst.Npc_Rage_Big_Bear || npcInfo.getNpcTempId() == RedBearRevengeConst.NPC_Big_Bear){
                long maxHp = getMaxHp(npcId);
                model.setSurplusHp(maxHp);
            }
        }

        //同步消息
        model.sync(true);
    }

    private static long getMaxHp(int npcId){
        RedBearRevengeNpcParameterTemplate npcTemplate = RedBearRevengeService.getNpcParameterTemplateMap().get(npcId);
        List<HeroUnit> heroUnits = BattleCollectService.createHeroUnits(npcTemplate.battleCollectId);
        return heroUnits.get(0).getAttributes().getValue(AttributeKey.最大生命);
    }
}

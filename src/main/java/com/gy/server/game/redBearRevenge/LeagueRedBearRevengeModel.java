package com.gy.server.game.redBearRevenge;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.attribute.AttributeKey;
import com.gy.server.game.battleCollect.BattleCollectService;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.drop.DropService;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.function.Function;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueDataInterface;
import com.gy.server.game.league.LeagueModel;
import com.gy.server.game.league.event.LeagueEvent;
import com.gy.server.game.league.event.LeagueEventHandler;
import com.gy.server.game.league.event.LeagueEventType;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.mail.MailManager;
import com.gy.server.game.mail.MailType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.redBearRevenge.bean.BearDefendBoxInfo;
import com.gy.server.game.redBearRevenge.bean.RedBearRevengeNpcInfo;
import com.gy.server.game.redBearRevenge.bean.RedBearRevengeRankInfo;
import com.gy.server.game.redBearRevenge.status.RedBearRevengeNodeEnum;
import com.gy.server.game.redBearRevenge.template.*;
import com.gy.server.game.reddot.RedDot;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbCommons;
import com.gy.server.packet.PbLeague;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.MathUtil;
import com.ttlike.server.tl.baselib.serialize.LeagueRedBearRevenge.BearDefendBoxInfoDb;
import com.ttlike.server.tl.baselib.serialize.LeagueRedBearRevenge.LeagueRedBearRevengeModelDb;
import com.ttlike.server.tl.baselib.serialize.LeagueRedBearRevenge.RedBearRevengeNpcInfoDb;
import com.ttlike.server.tl.baselib.serialize.LeagueRedBearRevenge.RedBearRevengeRankInfoDb;
import com.ttlike.server.tl.baselib.serialize.league.LeagueBlobDb;
import com.ttlike.server.tl.baselib.serialize.scene.ScenePosition3D;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.*;

/**
 * 红熊复仇模块
 *
 * <AUTHOR> 2024/10/16 19:39
 **/
public class LeagueRedBearRevengeModel extends LeagueModel implements LeagueEventHandler, LeagueDataInterface {

    //阶段
    private RedBearRevengeNodeEnum phase = RedBearRevengeNodeEnum.notOpen;

    /**
     * 开始时间，-1表示未开始
     */
    private long startTime = -1;

    /**
     * 阶段开始时间，有移动动作时，会在结束的时候开始倒计时
     */
    private long startPhaseTime = -1;

    /**
     * 驱逐阶段顺序
     */
    private List<RedBearRevengeNodeEnum> deportationIndexList = new ArrayList<>();

    /**
     * key:npc id   value:npc信息
     */
    private Map<Long, RedBearRevengeNpcInfo> npcInfoMap = new HashMap<>();

    /**
     * 开启次数
     */
    private int openTimes;

    /**
     * 当前buffId
     */
    private int curBuffId;

    /**
     * 当前buff生效时间
     */
    private long curBuffOverdueTime;

    /**
     * 大熊剩余血量
     */
    private long surplusHp;

    /**
     * 排行map
     * key:玩家id   value:红熊复仇排行信息
     */
    private Map<Long, RedBearRevengeRankInfo> rankMap = new HashMap<>();

    /**
     * 宝箱信息map
     * key:时间戳
     */
    private Map<Long, BearDefendBoxInfo> boxInfoMap = new HashMap<>();

    /**
     * 下一个buff
     */
    private int nextBuffId;

    /**
     * 当前buff生效时间
     */
    private long nextBuffOverdueTime;

    /**
     * 正在战斗中的玩家id
     */
    private Set<Long> inCombatPlayerIds = new HashSet<>();

    /**
     * 本轮伤害map
     * key:成员id     value:伤害值
     */
    private Map<Long, Long> damageMap = new HashMap<>();

    public LeagueRedBearRevengeModel(League league) {
        super(league);
    }

    /**
     * 填充奖励npc
     * @param canUsePoints
     */
    public void fillRewardPoint(List<ScenePosition3D> canUsePoints, int rewardId){
        if(CollectionUtil.isEmpty(canUsePoints)){
            return;
        }

        for (ScenePosition3D bean : canUsePoints) {
            RedBearRevengeNpcInfo npcInfo = new RedBearRevengeNpcInfo(RedBearRevengeConst.NPC_Reward, 0, bean, rewardId);
            npcInfoMap.put(npcInfo.getId(), npcInfo);
        }
    }

    /**
     * 清除熊卫
     */
    public void cleanDefendBear(){
        Set<Long> removeIdSet = new HashSet<>();
        for (RedBearRevengeNpcInfo bean : npcInfoMap.values()) {
            if(bean.getNpcTempId() == RedBearRevengeConst.NPC_Defend_Bear){
                removeIdSet.add(bean.getId());
            }
        }

        removeIdSet.forEach(id -> npcInfoMap.remove(id));
    }

    /**
     * 同步
     * @param sync 是否只同步场景内玩家
     */
    public void sync(boolean sync){
        League league = getLeague();
        PbProtocol.RedBearRevengeChangeNotify.Builder builder = PbProtocol.RedBearRevengeChangeNotify.newBuilder();
        builder.setInfo(genPb());
        league.notify2AllOnlineMember(PtCode.RED_BEAR_REVENGE_CHANGE_NOTIFY, builder.build(), sync);
    }

    public PbLeague.RedBearRevengeInfo genPb(){
        PbLeague.RedBearRevengeInfo.Builder builder = PbLeague.RedBearRevengeInfo.newBuilder();
        builder.setStartTime(startTime);
        builder.setStartPhaseTime(startPhaseTime);
        builder.setPhaseId(phase.getType());
        for (RedBearRevengeNpcInfo bean : npcInfoMap.values()) {
            builder.addNpcInfo(bean.genPb());
        }
        builder.setOpenTimes(openTimes);
        builder.setCurBuffId(curBuffId);
        builder.setCurBuffOverdueTime(curBuffOverdueTime);
        builder.setSurplusHp(surplusHp);
        for (BearDefendBoxInfo bean : boxInfoMap.values()) {
            builder.addBoxInfo(bean.genPb());
        }
        builder.setNextBuffId(nextBuffId);
        builder.setKillNum(getAllKillNum());
        return builder.build();
    }

    public void reset() {
        //补发
        Set<Long> playerIdSet = new HashSet<>(rankMap.keySet());
        Map<Long, RedBearRevengeNpcInfo> npcInfoMapTemp = new HashMap<>(npcInfoMap);
        Map<Long, BearDefendBoxInfo> boxInfoMapTemp = new HashMap<>(boxInfoMap);
        ThreadPool.execute(() -> finishHandler(playerIdSet, npcInfoMapTemp, boxInfoMapTemp));
        Set<Long> playPlayerIds = new HashSet<>();
        for (BearDefendBoxInfo boxInfo : boxInfoMap.values()) {
            playPlayerIds.addAll(boxInfo.getReceivePlayerId());
        }
        League.addPlayerPlayTimes(playPlayerIds, Function.redBearRevenge);
        //阶段
        this.phase = RedBearRevengeNodeEnum.notOpen;
        this.startTime = -1;
        this.startPhaseTime = -1;
        this.deportationIndexList.clear();
        this.npcInfoMap.clear();
        this.curBuffId = this.nextBuffId;
        this.curBuffOverdueTime = this.nextBuffOverdueTime;
        this.surplusHp = 0;
        this.rankMap.clear();
        this.boxInfoMap.clear();
        this.nextBuffId = 0;
        this.nextBuffOverdueTime = -1l;
        this.inCombatPlayerIds.clear();
        this.damageMap.clear();

        //红点处理
        updateRedDot(playerIdSet);
    }

    /**
     * 更新红点
     */
    public void updateRedDot(Set<Long> playerIdSet){
        ThreadPool.execute(() -> {
            for (long playerId : playerIdSet) {
                Player player = PlayerManager.getPlayer(playerId);
                if(player != null){
                    updateRedDot(player);
                }
            }
        });
    }

    /**
     * 更新红点
     * @param player
     */
    public void updateRedDot(Player player){
        RedDot.redBearRevengeRewardDot.sync(player);
    }

    private static void finishHandler(Set<Long> playerIdSet, Map<Long, RedBearRevengeNpcInfo> npcInfoMap, Map<Long, BearDefendBoxInfo> boxInfoMap){
        //查找宝箱npc
        //key:玩家id  value:未领取npc id
        Map<Long, Set<Long>> npcMap = new HashMap<>();
        for (RedBearRevengeNpcInfo bean : npcInfoMap.values()) {
            if(bean.getNpcTempId() == RedBearRevengeConst.NPC_Reward){
                Set<Long> setTemp = new HashSet<>(playerIdSet);
                setTemp.removeAll(bean.getReceivePlayerIdSet());

                setTemp.forEach(id -> {
                    npcMap.computeIfAbsent(id, set -> new HashSet<>()).add(bean.getId());
                });
            }
        }

        //key:玩家id  value:未领取宝箱 id
        Map<Long, Set<Long>> boxMap = new HashMap<>();
        for (BearDefendBoxInfo bean : boxInfoMap.values()) {
            Set<Long> setTemp = new HashSet<>(playerIdSet);
            setTemp.removeAll(bean.getReceivePlayerId());

            setTemp.forEach(id -> {
                boxMap.computeIfAbsent(id, set -> new HashSet<>()).add(bean.getId());
            });
        }

        for (Long playerId : playerIdSet) {
            if(!npcMap.containsKey(playerId) && !boxMap.containsKey(playerId)){
                continue;
            }

            List<Reward> rewardList = new ArrayList<>();
            Set<Long> setTemp = npcMap.get(playerId);
            if(setTemp != null){
                for (long npcId : setTemp) {
                    RedBearRevengeNpcInfo npcInfo = npcInfoMap.get(npcId);
                    RedBearRevengeRewardsTemplate rewardsTemplate = RedBearRevengeService.getRewardsTemplateMap().get(npcInfo.getRewardId());
                    rewardList.addAll(Reward.templateCollectionToReward(rewardsTemplate.rewards));
                }
            }

            setTemp = boxMap.get(playerId);
            if(setTemp != null){
                for (Long boxId : setTemp) {
                    BearDefendBoxInfo boxInfo = boxInfoMap.get(boxId);
                    RedBearRevengeChestTemplate chestTemplate = RedBearRevengeService.getChestTemplateMap().get(boxInfo.getExcelId());

                    Player player = PlayerManager.getPlayer(playerId);
                    rewardList.addAll(DropService.executeDrop(player, chestTemplate.dropId, BehaviorType.RedBearRevengeBearDefBoxReward, false, false));
                }
            }

            if(!rewardList.isEmpty()){
                MailType mailType = MailType.redBearRevengeReward;
                PbCommons.PbText title = Text.genText(mailType.getTitleId()).build();
                PbCommons.PbText content = Text.genText(mailType.getContentId()).build();
                MailManager.sendMail(mailType, playerId, title, content, ServerConstants.getCurrentTimeMillis(), rewardList);
            }
        }
    }

    @Override
    protected void loadData(LeagueBlobDb leagueBlobDb) {
        LeagueRedBearRevengeModelDb db = leagueBlobDb.getRedBearRevengeModelDb();
        if(db != null){
            this.phase = RedBearRevengeNodeEnum.of(db.getPhase());
            this.startTime = db.getStartTime();
            this.startPhaseTime = db.getStartPhaseTime();
            for (int type : db.getDeportationIndexList()) {
                this.deportationIndexList.add(RedBearRevengeNodeEnum.of(type));
            }
            for (RedBearRevengeNpcInfoDb bean : db.getNpcInfoList()) {
                RedBearRevengeNpcInfo npcInfo = new RedBearRevengeNpcInfo(bean);
                this.npcInfoMap.put(npcInfo.getId(), npcInfo);
            }
            this.openTimes = db.getOpenTimes();
            this.curBuffId = db.getCurBuffId();
            this.curBuffOverdueTime = db.getCurBuffOverdueTime();
            this.surplusHp = db.getSurplusHp();
            for (RedBearRevengeRankInfoDb bean : db.getRankList()) {
                RedBearRevengeRankInfo rankInfo = new RedBearRevengeRankInfo(bean);
                this.rankMap.put(rankInfo.getId(), rankInfo);
            }
            for (BearDefendBoxInfoDb bean : db.getBoxInfoList()) {
                BearDefendBoxInfo boxInfo = new BearDefendBoxInfo(bean);
                this.boxInfoMap.put(boxInfo.getId(), boxInfo);
            }
            this.nextBuffId = db.getNextBuffId();
            this.nextBuffOverdueTime = db.getNextBuffOverdueTime();
            this.inCombatPlayerIds.addAll(db.getInCombatPlayerIds());
            this.damageMap.putAll(db.getDamageMap());
        }
    }

    @Override
    protected void saveData(LeagueBlobDb leagueBlobDb) {
        LeagueRedBearRevengeModelDb db = new LeagueRedBearRevengeModelDb();
        db.setPhase(this.phase.getType());
        db.setStartTime(this.startTime);
        db.setStartPhaseTime(this.startPhaseTime);
        for (RedBearRevengeNodeEnum bean : this.deportationIndexList) {
            db.getDeportationIndexList().add(bean.getType());
        }
        for (RedBearRevengeNpcInfo bean : this.npcInfoMap.values()) {
            db.getNpcInfoList().add(bean.genDb());
        }
        db.setOpenTimes(this.openTimes);
        db.setCurBuffId(this.curBuffId);
        db.setCurBuffOverdueTime(this.curBuffOverdueTime);
        db.setSurplusHp(this.surplusHp);
        for (RedBearRevengeRankInfo bean : this.rankMap.values()) {
            db.getRankList().add(bean.genDb());
        }
        for (BearDefendBoxInfo bean : this.boxInfoMap.values()) {
            db.getBoxInfoList().add(bean.genDb());
        }
        db.setNextBuffId(this.nextBuffId);
        db.setNextBuffOverdueTime(this.nextBuffOverdueTime);
        db.getInCombatPlayerIds().addAll(this.inCombatPlayerIds);
        db.getDamageMap().putAll(damageMap);
        leagueBlobDb.setRedBearRevengeModelDb(db);
    }

    public void updateSurplusAndSync(long hp) {
        setSurplusHp(hp);
        sync(true);
    }

    public RedBearRevengeActiveTimeTemplate getNowPhaseTemplate() {
        return RedBearRevengeService.getActiveTimeTemplateMap().get(phase);
    }

    public int getEndRewardId(){
        return getBossId() == RedBearRevengeConst.Npc_Rage_Big_Bear ? surplusHp > 0l ? 2 : 1 : surplusHp > 0l ? 4 : 3;
    }

    public List<ScenePosition3D> getPairList(){
        return getPairList(getEndRewardId());
    }

    /**
     * 获取奖励宝箱坐标范围
     * @param rewardId
     * @return
     */
    public List<ScenePosition3D> getPairList(int rewardId){
        RedBearRevengeRewardsTemplate rewardsTemplate = RedBearRevengeService.getRewardsTemplateMap().get(rewardId);
        List<ScenePosition3D> positionList = RedBearRevengeService.getRedBearRevengeConst().positionList;
        return positionList.subList(0, Math.min(rewardsTemplate.rewardNum, positionList.size()));
    }

    /**
     * 获取boss最大血量
     * @return
     */
    public long getMaxHp(){
        RedBearRevengeNpcParameterTemplate npcTemplate = RedBearRevengeService.getNpcParameterTemplateMap().get(getBossId());
        List<HeroUnit> heroUnits = BattleCollectService.createHeroUnits(npcTemplate.battleCollectId);
        return heroUnits.get(0).getAttributes().getValue(AttributeKey.最大生命);
    }

    /**
     * 获取追逐阶段下一阶段
     * 追逐四个阶段顺序随机
     * 到达追逐最后一个阶段，检查是否满足进入{@link RedBearRevengeNodeEnum#battleA}的击杀数量，是进入{@link RedBearRevengeNodeEnum#battleA}否则进入{@link RedBearRevengeNodeEnum#battleB}
     * @param redBearRevengeNodeEnum
     * @return
     */
    public RedBearRevengeNodeEnum getDeportationNextStatus(RedBearRevengeNodeEnum redBearRevengeNodeEnum) {
        if(redBearRevengeNodeEnum == redBearRevengeNodeEnum.ready){
            return deportationIndexList.get(0);
        }
        int index = deportationIndexList.indexOf(redBearRevengeNodeEnum);
        return index == deportationIndexList.size() - 1 ? getAllKillNum() >= RedBearRevengeService.getRedBearRevengeConst().triggerCondition ?
                RedBearRevengeNodeEnum.battleA : RedBearRevengeNodeEnum.battleB : deportationIndexList.get(index + 1);
    }

    public void addDefBearBox(Player player) {
        //权重map
        Map<RedBearRevengeChestTemplate, Integer> weightMap = new HashMap<>();
        for (RedBearRevengeChestTemplate bean : RedBearRevengeService.getChestTemplateMap().values()) {
            weightMap.put(bean, bean.dropProbability);
        }

        RedBearRevengeChestTemplate chestTemplate = MathUtil.weightRandom(weightMap);
        BearDefendBoxInfo boxInfo = new BearDefendBoxInfo(chestTemplate.npcId, player.getPlayerId(), player.getName());
        boxInfoMap.put(boxInfo.getId(), boxInfo);

        //红点处理
        updateRedDot(player);
    }

    public boolean isOpen() {
        return getPhase() != RedBearRevengeNodeEnum.notOpen || getStartTime() > 0;
    }

    public void addOpenTimes() {
        openTimes++;
    }

    public int getAllKillNum(){
        return rankMap.values().stream().map(RedBearRevengeRankInfo::getKill).reduce(Integer::sum).orElse(0);
    }

    public int getBossId(){
        return getAllKillNum() >= RedBearRevengeService.getRedBearRevengeConst().triggerCondition ? RedBearRevengeConst.Npc_Rage_Big_Bear : RedBearRevengeConst.NPC_Big_Bear;
    }

    @Override
    public LeagueEventType[] getEventTypes() {
        return new LeagueEventType[0];
    }

    @Override
    public void handle(LeagueEvent event) {

    }

    public RedBearRevengeActiveTimeTemplate getActiveTimeTemplate(){
        return RedBearRevengeService.getActiveTimeTemplateMap().get(phase);
    }

    public void updateDamage(long playerId, long damage){
        damageMap.put(playerId, Math.max(damage, damageMap.getOrDefault(playerId, 0l)));
    }

    /**
     * 获取玩家伤害抄书
     * @param playerId
     * @return
     */
    public double getDamageOutPercent(long playerId){
        if(damageMap.isEmpty()){
            return 0d;
        }

        long damage = damageMap.getOrDefault(playerId, 0l);
        List<Long> damageList = new ArrayList<>(damageMap.values());
        damageList.sort(Long::compare);

        int index = damageList.indexOf(damage);
        double percent = (damageMap.size() - (index + 1)) / damageMap.size();
        return percent;
    }

    public RedBearRevengeNodeEnum getPhase() {
        return phase;
    }

    public void setPhase(RedBearRevengeNodeEnum phase) {
        this.phase = phase;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getStartPhaseTime() {
        return startPhaseTime;
    }

    public void setStartPhaseTime(long startPhaseTime) {
        this.startPhaseTime = startPhaseTime;
    }

    public Map<Long, RedBearRevengeNpcInfo> getNpcInfoMap() {
        return npcInfoMap;
    }

    public void setNpcInfoMap(Map<Long, RedBearRevengeNpcInfo> npcInfoMap) {
        this.npcInfoMap = npcInfoMap;
    }

    public int getOpenTimes() {
        return openTimes;
    }

    public void setOpenTimes(int openTimes) {
        this.openTimes = openTimes;
    }

    public int getCurBuffId() {
        return curBuffId;
    }

    public void setCurBuffId(int curBuffId) {
        this.curBuffId = curBuffId;
    }

    public long getCurBuffOverdueTime() {
        return curBuffOverdueTime;
    }

    public void setCurBuffOverdueTime(long curBuffOverdueTime) {
        this.curBuffOverdueTime = curBuffOverdueTime;
    }

    public long getSurplusHp() {
        return surplusHp;
    }

    public void setSurplusHp(long surplusHp) {
        this.surplusHp = surplusHp;
    }

    public Map<Long, RedBearRevengeRankInfo> getRankMap() {
        return rankMap;
    }

    public void setRankMap(Map<Long, RedBearRevengeRankInfo> rankMap) {
        this.rankMap = rankMap;
    }

    public Map<Long, BearDefendBoxInfo> getBoxInfoMap() {
        return boxInfoMap;
    }

    public void setBoxInfoMap(Map<Long, BearDefendBoxInfo> boxInfoMap) {
        this.boxInfoMap = boxInfoMap;
    }

    public int getNextBuffId() {
        return nextBuffId;
    }

    public void setNextBuffId(int nextBuffId) {
        this.nextBuffId = nextBuffId;
    }

    public Set<Long> getInCombatPlayerIds() {
        return inCombatPlayerIds;
    }

    public void setInCombatPlayerIds(Set<Long> inCombatPlayerIds) {
        this.inCombatPlayerIds = inCombatPlayerIds;
    }

    public List<RedBearRevengeNodeEnum> getDeportationIndexList() {
        return deportationIndexList;
    }

    public void setDeportationIndexList(List<RedBearRevengeNodeEnum> deportationIndexList) {
        this.deportationIndexList = deportationIndexList;
    }

    public long getNextBuffOverdueTime() {
        return nextBuffOverdueTime;
    }

    public void setNextBuffOverdueTime(long nextBuffOverdueTime) {
        this.nextBuffOverdueTime = nextBuffOverdueTime;
    }

    public Map<Long, Long> getDamageMap() {
        return damageMap;
    }

    public void setDamageMap(Map<Long, Long> damageMap) {
        this.damageMap = damageMap;
    }

    @Override
    public boolean canMerge(long leagueId) {
        return true;
    }

    @Override
    public void merge(League selfLeague, League targetLeague) {
//        合并后以进度保持以主体帮派进度和完成情况为准

    }

    @Override
    public void removeLeague(League league) {

    }

    @Override
    public void quitLeague(long pid) {

    }
}

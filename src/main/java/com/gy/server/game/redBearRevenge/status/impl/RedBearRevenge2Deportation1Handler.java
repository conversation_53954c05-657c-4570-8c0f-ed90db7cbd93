package com.gy.server.game.redBearRevenge.status.impl;

import com.gy.server.game.redBearRevenge.LeagueRedBearRevengeModel;
import com.gy.server.game.redBearRevenge.status.AbsRedBearRevengeStatusExecutor;
import com.gy.server.game.redBearRevenge.status.RedBearRevengeNodeEnum;

/**
 * 驱逐1
 *
 * <AUTHOR> 2024/10/17 15:04
 **/
public class RedBearRevenge2Deportation1Handler extends AbsRedBearRevengeStatusExecutor {

    @Override
    public RedBearRevengeNodeEnum curStatus() {
        return RedBearRevengeNodeEnum.deportation1;
    }

    @Override
    public RedBearRevengeNodeEnum nextStatus(LeagueRedBearRevengeModel model) {
        return model.getDeportationNextStatus(curStatus());
    }

    @Override
    public void doRightNow(LeagueRedBearRevengeModel model) {
        eventDoRightNow(model, "红熊复仇驱逐1-立刻执行 ", this.getClass().getSimpleName());
    }

    @Override
    public void phaseFinishDeal(LeagueRedBearRevengeModel model) {
        model.cleanDefendBear();
    }
}

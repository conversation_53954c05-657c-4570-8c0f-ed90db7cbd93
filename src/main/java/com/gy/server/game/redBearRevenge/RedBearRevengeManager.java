package com.gy.server.game.redBearRevenge;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.league.LeagueModelEnums;
import com.gy.server.game.redBearRevenge.bean.RedBearRevengeNpcInfo;
import com.gy.server.game.redBearRevenge.template.RedBearRevengeNpcParameterTemplate;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.function.Ticker;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.serialize.scene.ScenePosition3D;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 红熊复仇
 *
 * <AUTHOR> 2024/10/24 18:08
 **/
public class RedBearRevengeManager implements Ticker {

    @Override
    public void tick() {
        for (League league : LeagueManager.getLeagues()) {
            //开启红熊复仇活动
            LeagueRedBearRevengeModel redBearRevengeModel = league.getModel(LeagueModelEnums.redBearRevenge);

            if(redBearRevengeModel.getStartTime() <= 0){
                continue;
            }

            //未开始
            if(redBearRevengeModel.getStartTime() > ServerConstants.getCurrentTimeMillis()){
                continue;
            }

            //移动检测
            moveCheck(redBearRevengeModel);

            //切换状态
            canChangePhase(redBearRevengeModel);
            if(redBearRevengeModel.getStartPhaseTime() < 0){
                //开始计时
                redBearRevengeModel.setStartPhaseTime(ServerConstants.getCurrentTimeMillis());
            }
        }
    }

    /**
     * 能否切换阶段
     */
    private void canChangePhase(LeagueRedBearRevengeModel model){
        model.getPhase().tryNextPhase(model);
    }

    /**
     * 移动检测
     */
    private boolean moveCheck(LeagueRedBearRevengeModel redBearRevengeModel){
        Map<Long, RedBearRevengeNpcInfo> npcInfos = redBearRevengeModel.getNpcInfoMap();
        boolean newMove = false;
        if(CollectionUtil.isNotEmpty(npcInfos)){
            List<RedBearRevengeNpcInfo> redBearNpcInfos = new ArrayList<>(npcInfos.values());
            for (RedBearRevengeNpcInfo npcInfo : redBearNpcInfos) {
                //检查是否移动到目的地
                if(npcInfo.getTargetPoint() != null && npcInfo.getEndMoveTime() > 0 && ServerConstants.getCurrentTimeMillis() > npcInfo.getEndMoveTime()){
                    //到目的地
                    npcInfo.setCurPoint(npcInfo.getTargetPoint());
                    npcInfo.setStartMoveTime(npcInfo.getEndMoveTime());

                    //随机新目的地
                    npcInfo.randomMove(redBearRevengeModel);
                    newMove = true;
                }
            }
        }

        if(newMove){
            redBearRevengeModel.sync(true);
        }
        return newMove;
    }

    /**
     * 计算移动多长时间
     * @param npcId npc配表id
     * @param startPoint 起始位置
     * @param endPoint 重点位置
     * @return 移动时间
     */
    public static long calTime(int npcId, ScenePosition3D startPoint, ScenePosition3D endPoint){
        RedBearRevengeNpcParameterTemplate npcTemplate = RedBearRevengeService.getNpcParameterTemplateMap().get(npcId);
        float xDistance = startPoint.getX() - endPoint.getX();
        float yDistance = startPoint.getZ() - endPoint.getZ();
        long costSecond = (long)(Math.sqrt(yDistance * yDistance + xDistance * xDistance) / (double) npcTemplate.speed);
        return costSecond * DateTimeUtil.MillisOfSecond;
    }

    private static final RedBearRevengeManager instance = new RedBearRevengeManager();
    public static RedBearRevengeManager getInstance(){
        return instance;
    }
}

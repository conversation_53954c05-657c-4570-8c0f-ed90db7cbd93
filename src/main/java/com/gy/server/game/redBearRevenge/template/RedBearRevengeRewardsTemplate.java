package com.gy.server.game.redBearRevenge.template;

import com.gy.server.game.drop.RewardTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 红熊复仇奖励模板
 *
 * <AUTHOR> 2024/10/17 16:40
 **/
public class RedBearRevengeRewardsTemplate {

    public int id;

    /**
     * 可拾取数量
     */
    public int rewardNum;

    /**
     * 奖励
     */
    public List<RewardTemplate> rewards = new ArrayList<>();

    /**
     * buff
     */
    public int buff;

    /**
     * huff持续时间 秒
     */
    public int buffTime;


    public RedBearRevengeRewardsTemplate(Map<String, String> map){
        this.id = Integer.parseInt(map.get("RewardId"));
        this.rewardNum = Integer.parseInt(map.get("rewardNum"));
        this.rewards = RewardTemplate.readListFromText(map.get("rewardStr"));
        this.buff = Integer.parseInt(map.get("buff"));
        this.buffTime = Integer.parseInt(map.get("buffTime"));
    }
}

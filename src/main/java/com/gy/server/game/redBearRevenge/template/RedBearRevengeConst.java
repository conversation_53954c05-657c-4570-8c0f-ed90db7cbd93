package com.gy.server.game.redBearRevenge.template;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.util.StringExtUtil;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.serialize.scene.ScenePosition3D;
import org.apache.commons.lang3.tuple.Pair;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 常量模板
 *
 * <AUTHOR> 2024/10/17 15:34
 **/
public class RedBearRevengeConst {

    /**
     * 暴怒的熊王
     */
    public static final int Npc_Rage_Big_Bear = 1;
    /**
     * 红熊王
     */
    public static final int NPC_Big_Bear = 2;
    /**
     * 熊卫
     */
    public static final int NPC_Defend_Bear = 3;
    /**
     * 宝箱
     */
    public static final int NPC_Reward = 4;

    /**
     * 每周可开启次数
     */
    public int weekNum;

    /**
     * 开启所需帮派贡献
     */
    public int contributionNum;

    /**
     * 可选择开启的时间段
     */
    public Pair<String, String> openTime;

    /**
     * 奖励生成数量上限
     */
    public int rewardUpperLimit;

    /**
     * 进入战斗阶段A所需击杀熊卫数
     */
    public int triggerCondition;

    /**
     * 道具坐标生成间隔最小半径
     */
    public int propertyMinimumRange;

    /**
     * buff生效副本,stage表id
     */
    public Pair<Integer, Integer> EffectivePlace;

    /**
     * 熊卫血量
     */
    public int defeatedNum;

    /**
     * 出刀冷却（秒）
     */
    public int attackCd;

    /**
     * 熊卫宝箱掉落概率，万分比
     */
    public int chance;

    /**
     * 结算奖励宝箱坐标点位
     */
    public List<ScenePosition3D> positionList = new ArrayList<>();

    /**
     * 开启红熊王宝箱读条时间（秒）
     */
    public int openTheChestTime;

    /**
     * 宝箱拾取范围半径(米）
     */
    public int pickUpRange;

    /**
     * 每波出现小怪区域数量
     */
    public int regionNum;

    /**
     * 每个区域出现小怪数量上限
     */
    public int littleBearNum;

    public RedBearRevengeConst(Map<String, String> map) {
        this.weekNum = Integer.parseInt(map.get("WeekNum"));
        this.contributionNum = Integer.parseInt(map.get("contributionNum"));
        this.openTime = StringExtUtil.string2Pair(map.get("OpenTimePeriod"), "|", String.class, String.class);
        this.rewardUpperLimit = Integer.parseInt(map.get("RewardUpperLimit"));
        this.triggerCondition = Integer.parseInt(map.get("TriggerCondition"));
        this.propertyMinimumRange = Integer.parseInt(map.get("propertyMinimumRange"));
        this.EffectivePlace = StringExtUtil.string2Pair(map.get("EffectivePlace"), ",", Integer.class, Integer.class);
        this.defeatedNum = Integer.parseInt(map.get("DefeatedNum"));
        this.attackCd = Integer.parseInt(map.get("AttackCD"));
        this.chance = Integer.parseInt(map.get("Chance"));

        String[] strings = map.get("BigBearSucceed").split("_");
        for (String str : strings) {
            String[] positions = str.split(",");
            ScenePosition3D position3D = new ScenePosition3D();
            position3D.setX(Float.valueOf(positions[0]));
            position3D.setY(Float.valueOf(positions[1]));
            position3D.setZ(Float.valueOf(positions[2]));
            this.positionList.add(position3D);
        }

        this.openTheChestTime = Integer.parseInt(map.get("OpenTheChestTime"));
        this.pickUpRange = Integer.parseInt(map.get("PickUpRange"));
        this.regionNum = Integer.parseInt(map.get("regionNum"));
        this.littleBearNum = Integer.parseInt(map.get("littleBearNum"));
    }

    public boolean checkOpenTime(long time){
        String[] minTimeArray = openTime.getLeft().split(":");
        String[] maxTimeArray = openTime.getRight().split(":");
        long minTime = DateTimeUtil.toMillis(ServerConstants.getCurrentTimeLocalDateTime().withHour(Integer.parseInt(minTimeArray[0])).withMinute(Integer.parseInt(minTimeArray[1])).withSecond(0).withNano(0));
        long maxTime = DateTimeUtil.toMillis(ServerConstants.getCurrentTimeLocalDateTime().withHour(Integer.parseInt(maxTimeArray[0])).withMinute(Integer.parseInt(maxTimeArray[1])).withSecond(0).withNano(0));
        return minTime <= time && time <= maxTime;
    }

}


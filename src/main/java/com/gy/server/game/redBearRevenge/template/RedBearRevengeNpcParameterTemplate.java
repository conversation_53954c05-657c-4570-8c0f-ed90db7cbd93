package com.gy.server.game.redBearRevenge.template;

import com.gy.server.game.util.StringExtUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 红熊复仇npc信息模板
 *
 * <AUTHOR> 2024/10/17 16:27
 **/
public class RedBearRevengeNpcParameterTemplate {

    public int id;

    /**
     * npc id
     */
    public int npcId;

    /**
     * 动作
     * 大熊：1.移动2.攻击3.向天咆哮
     * 小熊：1.移动2.阵亡
     */
    public List<Integer> describeList = new ArrayList<>();

    /**
     * 速度
     */
    public int speed;


    /**
     * 战斗id
     */
    public int battleCollectId;

    public RedBearRevengeNpcParameterTemplate(Map<String, String> map){
        this.id = Integer.parseInt(map.get("id"));
        this.npcId = Integer.parseInt(map.get("content"));
        this.describeList = StringExtUtil.string2List(map.get("describe"), ",", Integer.class);
        this.speed = Integer.parseInt(map.get("speed"));
        this.battleCollectId = Integer.parseInt(map.get("BattleID"));
    }
}

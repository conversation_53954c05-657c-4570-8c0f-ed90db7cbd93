package com.gy.server.game.redBearRevenge.status.impl;

import com.gy.server.game.redBearRevenge.LeagueRedBearRevengeModel;
import com.gy.server.game.redBearRevenge.status.AbsRedBearRevengeStatusExecutor;
import com.gy.server.game.redBearRevenge.status.RedBearRevengeNodeEnum;

/**
 * 战斗B
 *
 * <AUTHOR> 2024/10/24 17:11
 **/
public class RedBearRevenge7BattleBHandler extends AbsRedBearRevengeStatusExecutor {

    @Override
    public RedBearRevengeNodeEnum curStatus() {
        return RedBearRevengeNodeEnum.battleB;
    }

    @Override
    public RedBearRevengeNodeEnum nextStatus(LeagueRedBearRevengeModel model) {
        return RedBearRevengeNodeEnum.reward;
    }

    @Override
    public void doRightNow(LeagueRedBearRevengeModel model) {
        eventDoRightNow(model, "红熊复仇战斗B-立刻执行 ", this.getClass().getSimpleName());
    }
}

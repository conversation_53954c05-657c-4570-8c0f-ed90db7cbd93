package com.gy.server.game.redBearRevenge.event.impl;

import com.gy.server.game.redBearRevenge.LeagueRedBearRevengeModel;
import com.gy.server.game.redBearRevenge.RedBearRevengeService;
import com.gy.server.game.redBearRevenge.event.AbstractRedBearRevengeEvent;
import com.gy.server.game.redBearRevenge.status.RedBearRevengeNodeEnum;
import com.gy.server.game.redBearRevenge.template.RedBearRevengeRewardsTemplate;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.serialize.scene.ScenePosition3D;

import java.util.List;

/**
 * 红熊复仇奖励事件
 * 3.道具生成行走面范围坐标 参数1【正方形行走面四个坐标】
 * <AUTHOR> 2024/10/22 9:47
 **/
public class RedBearRevengeRewardEvent extends AbstractRedBearRevengeEvent {

    public RedBearRevengeRewardEvent(String param){

    }


    @Override
    public void trigger(LeagueRedBearRevengeModel model) {
        int rewardId = model.getEndRewardId();
        List<ScenePosition3D> positionList = model.getPairList(rewardId);
        model.fillRewardPoint(positionList, rewardId);
        RedBearRevengeRewardsTemplate rewardsTemplate = RedBearRevengeService.getRewardsTemplateMap().get(rewardId);
        if(rewardsTemplate != null){
            model.setNextBuffId(rewardsTemplate.buff);
            model.setNextBuffOverdueTime(DateTimeUtil.MillisOfSecond * rewardsTemplate.buffTime);
        }

        //同步
        model.sync(true);
        RedBearRevengeNodeEnum.log("红熊王入侵-生成奖励 ");
    }


}

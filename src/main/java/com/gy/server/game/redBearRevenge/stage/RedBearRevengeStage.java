package com.gy.server.game.redBearRevenge.stage;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.attribute.AttributeKey;
import com.gy.server.game.battleCollect.BattleCollectService;
import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.combat.CombatManager;
import com.gy.server.game.combat.StageType;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.combat.unit.TeamUnit;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.league.LeagueModelEnums;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.player.Player;
import com.gy.server.game.redBearRevenge.LeagueRedBearRevengeModel;
import com.gy.server.game.redBearRevenge.RedBearRevengeService;
import com.gy.server.game.redBearRevenge.bean.RedBearRevengeRankInfo;
import com.gy.server.game.redBearRevenge.template.RedBearRevengeNpcParameterTemplate;
import com.gy.server.game.world.World;
import com.gy.server.packet.PbProtocol;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 红熊复仇boss战
 *
 * <AUTHOR> 2024/11/1 15:41
 **/
public class RedBearRevengeStage extends AbstractStage {

    private Player player;
    private int npcTempId;
    private int defIndex;

    StageType stageType = StageType.redBearRevenge;
    LineupType lineupType = LineupType.redBearRevenge;

    public RedBearRevengeStage(Player player, int npcTempId) {
        this.player = player;
        this.npcTempId = npcTempId;
    }

    @Override
    public void init() {
        League league = LeagueManager.getLeagueByPlayer(player);
        LeagueRedBearRevengeModel model = league.getModel(LeagueModelEnums.redBearRevenge);

        List<HeroUnit> atkHeroUnits = player.getLineupModel().createHeroUnits(stageType, lineupType);
        if(ServerConstants.getCurrentTimeMillis() < model.getStartTime() + model.getCurBuffOverdueTime() && model.getCurBuffId() != 0){
            for (HeroUnit bean : atkHeroUnits) {
                bean.addBuff(model.getCurBuffId(), 1);
            }
        }
        TeamUnit atkTeam = new TeamUnit(getNewId(), atkHeroUnits);

        RedBearRevengeNpcParameterTemplate npcTemplate = RedBearRevengeService.getNpcParameterTemplateMap().get(npcTempId);
        List<HeroUnit> defHeroUnits = BattleCollectService.createHeroUnits(npcTemplate.battleCollectId, World.getWorldLevel());
        defHeroUnits.get(0).getAttributes().setValue(AttributeKey.当前生命值, model.getSurplusHp());
        TeamUnit defTeam = new TeamUnit(getNewId(), defHeroUnits);
        defIndex = defHeroUnits.get(0).getPosIndex();
        init(npcTemplate.battleCollectId, stageType, atkTeam, defTeam, lineupType);
    }

    @Override
    public void afterFinish() {
        League league = LeagueManager.getLeagueByPlayer(player);
        LeagueRedBearRevengeModel model = league.getModel(LeagueModelEnums.redBearRevenge);
        model.getInCombatPlayerIds().remove(player.getPlayerId());
        long damage = CombatManager.getCombatDamage(getAtks(), this);
        model.updateDamage(player.getPlayerId(), damage);

        double percent = model.getDamageOutPercent(player.getPlayerId());
        PbProtocol.RedBearRevengeSettlement.Builder settlement = PbProtocol.RedBearRevengeSettlement.newBuilder();
        settlement.setPercent(percent);

        long addDamage = CombatManager.getCombatDamage(getAtks(), this);
        model.getRankMap().computeIfAbsent(player.getPlayerId(), bean -> new RedBearRevengeRankInfo(player.getPlayerId())).addDamage(addDamage);

        PbProtocol.CombatSettlementNotify.Builder rst = genCombatSettlement();
        rst.setRedBearRevenge(settlement.build());
        notifyCombatSettlement(player, rst.build());
        TeamUnit teamUnit = getDefs().get(0);
        for (HeroUnit heroUnit : teamUnit.getFormation()) {
            if(Objects.nonNull(heroUnit)){
                if(heroUnit.getPosIndex() == defIndex){
                    //防守方血量变化，同步帮派场景玩家
                    model.updateSurplusAndSync(heroUnit.getAttributes().getHp());
                }
            }
        }
    }

    @Override
    public List<Long> needSyncDefHpPlayerId() {
        League league = LeagueManager.getLeagueByPlayer(player);
        LeagueRedBearRevengeModel model = league.getModel(LeagueModelEnums.redBearRevenge);
        return new ArrayList<>(model.getInCombatPlayerIds());
    }

    @Override
    public void combatAbort(boolean isStart) {
        League league = LeagueManager.getLeagueByPlayer(player);
        LeagueRedBearRevengeModel model = league.getModel(LeagueModelEnums.redBearRevenge);
        model.getInCombatPlayerIds().remove(player.getPlayerId());
    }
}

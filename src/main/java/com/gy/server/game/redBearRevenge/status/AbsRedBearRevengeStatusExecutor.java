package com.gy.server.game.redBearRevenge.status;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.redBear.status.RedBearNodeType;
import com.gy.server.game.redBearRevenge.LeagueRedBearRevengeModel;
import com.gy.server.game.redBearRevenge.RedBearRevengeService;
import com.gy.server.game.redBearRevenge.event.AbstractRedBearRevengeEvent;
import com.gy.server.game.redBearRevenge.template.RedBearRevengeActiveTimeTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 2024/10/17 10:03
 **/
public abstract class AbsRedBearRevengeStatusExecutor implements RedBearRevengeNodeEnum.RBExecutor{

    @Override
    public void init(LeagueRedBearRevengeModel model) {

    }

    @Override
    public void changePhase(LeagueRedBearRevengeModel model) {
        RedBearNodeType.log("红熊复仇-状态切换, 当前状态 " + curStatus().name() + "  下个状态 : " + nextStatus(model).name());
        model.setPhase(nextStatus(model));
        model.getPhase().init(model);
        model.getPhase().doRightNow(model);
        model.sync(false);
    }

    public void doRightNow(LeagueRedBearRevengeModel model){

    }

    public void eventDoRightNow(LeagueRedBearRevengeModel model, String str, String simpleName){
        RedBearRevengeActiveTimeTemplate template = RedBearRevengeService.getLeagueRedBearRevengeActiveTimeTemplate(curStatus());

        List<Integer> list = new ArrayList<>(template.eventMap.keySet());
        list.sort(Integer::compare);

        for (int eventType : list) {
            AbstractRedBearRevengeEvent bean = template.eventMap.get(eventType);
            bean.trigger(model);
        }

        //开始计时
        model.setStartPhaseTime(ServerConstants.getCurrentTimeMillis());
        RedBearRevengeNodeEnum.log(str + " " + simpleName);
    }

    @Override
    public void phaseFinishDeal(LeagueRedBearRevengeModel model) {

    }

}

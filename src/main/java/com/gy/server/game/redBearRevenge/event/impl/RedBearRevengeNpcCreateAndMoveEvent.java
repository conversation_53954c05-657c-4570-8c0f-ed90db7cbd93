package com.gy.server.game.redBearRevenge.event.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.redBear.status.RedBearNodeType;
import com.gy.server.game.redBearRevenge.LeagueRedBearRevengeModel;
import com.gy.server.game.redBearRevenge.RedBearRevengeService;
import com.gy.server.game.redBearRevenge.bean.RedBearRevengeNpcInfo;
import com.gy.server.game.redBearRevenge.event.AbstractRedBearRevengeEvent;
import com.gy.server.game.redBearRevenge.template.RedBearRevengeConst;
import com.gy.server.game.util.StringExtUtil;
import com.gy.server.utils.MathUtil;
import com.ttlike.server.tl.baselib.serialize.scene.ScenePosition3D;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;

/**
 * 红熊复仇创建并移动事件
 * 2.生成+移动npc 参数1【数量】参数2【npcID】】参数生成和移动的区域【正方形行走面四个坐标】
 * 生成+移动npc：数量_角色ID_坐标区域1的四个点|坐标区域2的四个点|坐标区域3的四个点
 * <AUTHOR> 2024/10/22 9:44
 **/
public class RedBearRevengeNpcCreateAndMoveEvent extends AbstractRedBearRevengeEvent {

    /**
     * 数量
     */
    private int num;

    /**
     * npc模板id
     */
    private int npcId;

    /**
     * 目标区域map
     * key:顺序   value:区域信息
     */
    private Map<Integer, List<ScenePosition3D>> areaPointMap = new HashMap<>();

    public RedBearRevengeNpcCreateAndMoveEvent(String param){
        String[] split = param.split("_");
        this.num = Integer.parseInt(split[0]);
        this.npcId = Integer.parseInt(split[1]);

        int i = 1;
        List<String> temp = StringExtUtil.string2List(split[2], ";", String.class);
        for (String bean : temp) {
            List<ScenePosition3D> list = canUsePointList(bean);
            areaPointMap.put(i, list);
            i++;
        }
    }

    @Override
    public void trigger(LeagueRedBearRevengeModel model) {
        if(npcId != RedBearRevengeConst.NPC_Defend_Bear){
            throw new IllegalArgumentException("redBearRevenge npc is not move, npcId is " + npcId);
        }

        RedBearRevengeConst revengeConst = RedBearRevengeService.getRedBearRevengeConst();
        List<Integer> areaIdList = new ArrayList<>(areaPointMap.keySet());
        areaIdList = MathUtil.random(areaIdList, revengeConst.regionNum);

        List<Integer> temp = new ArrayList<>();
        for (int i = 1; i <= revengeConst.littleBearNum; i++){
            temp.addAll(areaIdList);
        }
        Collections.shuffle(temp);

        if(temp.size() < num){
            throw new IllegalArgumentException("redBearRevenge random num is not amply, pram：" + revengeConst.regionNum + "  " + revengeConst.littleBearNum);
        }

        long now = ServerConstants.getCurrentTimeMillis();
        for (int i = 1; i <= num; i++) {
            //创建npc
            int area = temp.get(i - 1);

            List<ScenePosition3D> list = new ArrayList<>(areaPointMap.get(area));
            if(list.size() < 2){
                throw new IllegalArgumentException("redBearRevenge random pool is not amply" + list.size());
            }

            ScenePosition3D startPosition = MathUtil.random(list);
            RedBearRevengeNpcInfo npcInfo = RedBearRevengeNpcInfo.create(npcId, area, startPosition);
            model.getNpcInfoMap().put(npcInfo.getId(), npcInfo);
            npcInfo.setStartMoveTime(now);
            npcInfo.randomMove(list);
            RedBearNodeType.log("RedBearRevengeNpcInfo :" + npcInfo);
        }

        //同步
        model.sync(true);
        RedBearNodeType.log("红熊复仇-npc创建并移动 : " + npcId);
    }

    private static List<ScenePosition3D> canUsePointList(String pointStr){
        List<ScenePosition3D> pointList = new ArrayList<>();
        for (String bean : StringExtUtil.string2List(pointStr, ":", String.class)) {
            List<Integer> point = StringExtUtil.string2List(bean, ",", Integer.class);
            if(point.size() < 3){
                throw new IllegalArgumentException("红熊复仇npc创建点有问题" + " !!! ");
            }

            ScenePosition3D scenePosition3D = new ScenePosition3D(point.get(0), 0, point.get(2));
            pointList.add(scenePosition3D);
        }

        //确定点位
        //x范围
        int xMin = (int) pointList.stream().min(((o1, o2) -> (int) (o1.getX() - o2.getX()))).get().getX();
        int xMax = (int) pointList.stream().max(((o1, o2) -> (int) (o1.getX() - o2.getX()))).get().getX();
        //z范围
        int zMin = (int) pointList.stream().min(((o1, o2) -> (int) (o1.getZ() - o2.getZ()))).get().getZ();
        int zMax = (int) pointList.stream().max(((o1, o2) -> (int) (o1.getZ() - o2.getZ()))).get().getZ();

        List<Pair<Integer, Integer>> list = pointDeal(xMin, xMax, zMin, zMax, 10);
        for (Pair<Integer, Integer> pair : list) {
            ScenePosition3D scenePosition3D = new ScenePosition3D(pair.getLeft(), 0f, pair.getRight());
            pointList.add(scenePosition3D);
        }

        return pointList;
    }

    private static List<Pair<Integer, Integer>> pointDeal(int xMin, int xMax, int zMin, int zMax, int diff){
        List<Pair<Integer, Integer>> temp = new ArrayList<>();
        for(int i = xMin / diff; i <= xMax / diff; i = i + diff){
            for(int j = zMin / diff; j <= zMax / diff; j = j + diff){
                temp.add(Pair.of(i * diff, j * diff));
            }
        }

        return temp;
    }

    public Map<Integer, List<ScenePosition3D>> getAreaPointMap() {
        return areaPointMap;
    }
}

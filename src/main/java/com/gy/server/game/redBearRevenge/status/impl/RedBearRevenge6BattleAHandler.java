package com.gy.server.game.redBearRevenge.status.impl;

import com.gy.server.game.redBearRevenge.LeagueRedBearRevengeModel;
import com.gy.server.game.redBearRevenge.status.AbsRedBearRevengeStatusExecutor;
import com.gy.server.game.redBearRevenge.status.RedBearRevengeNodeEnum;

/**
 * 战斗A
 *
 * <AUTHOR> 2024/10/24 17:11
 **/
public class RedBearRevenge6BattleAHandler extends AbsRedBearRevengeStatusExecutor {

    @Override
    public RedBearRevengeNodeEnum curStatus() {
        return RedBearRevengeNodeEnum.battleA;
    }

    @Override
    public RedBearRevengeNodeEnum nextStatus(LeagueRedBearRevengeModel model) {
        return RedBearRevengeNodeEnum.reward;
    }

    @Override
    public void doRightNow(LeagueRedBearRevengeModel model) {
        eventDoRightNow(model, "红熊复仇战斗A-立刻执行 ", this.getClass().getSimpleName());
    }

}

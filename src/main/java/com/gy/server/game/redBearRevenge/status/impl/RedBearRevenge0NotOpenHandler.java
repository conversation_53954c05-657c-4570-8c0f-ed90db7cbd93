package com.gy.server.game.redBearRevenge.status.impl;

import com.gy.server.game.redBearRevenge.LeagueRedBearRevengeModel;
import com.gy.server.game.redBearRevenge.status.AbsRedBearRevengeStatusExecutor;
import com.gy.server.game.redBearRevenge.status.RedBearRevengeNodeEnum;

/**
 * 未开启
 *
 * <AUTHOR> 2024/10/17 10:20
 **/
public class RedBearRevenge0NotOpenHandler extends AbsRedBearRevengeStatusExecutor {


    @Override
    public RedBearRevengeNodeEnum curStatus() {
        return RedBearRevengeNodeEnum.notOpen;
    }

    @Override
    public RedBearRevengeNodeEnum nextStatus(LeagueRedBearRevengeModel model) {
        return RedBearRevengeNodeEnum.ready;
    }
}

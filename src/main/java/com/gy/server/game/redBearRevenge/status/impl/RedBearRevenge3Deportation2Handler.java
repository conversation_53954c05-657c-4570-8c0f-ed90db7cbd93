package com.gy.server.game.redBearRevenge.status.impl;

import com.gy.server.game.redBearRevenge.LeagueRedBearRevengeModel;
import com.gy.server.game.redBearRevenge.status.AbsRedBearRevengeStatusExecutor;
import com.gy.server.game.redBearRevenge.status.RedBearRevengeNodeEnum;

/**
 * 驱逐2
 *
 * <AUTHOR> 2024/10/24 17:00
 **/
public class RedBearRevenge3Deportation2Handler extends AbsRedBearRevengeStatusExecutor {

    @Override
    public RedBearRevengeNodeEnum curStatus() {
        return RedBearRevengeNodeEnum.deportation2;
    }

    @Override
    public RedBearRevengeNodeEnum nextStatus(LeagueRedBearRevengeModel model) {
        return model.getDeportationNextStatus(curStatus());
    }

    @Override
    public void doRightNow(LeagueRedBearRevengeModel model) {
        eventDoRightNow(model, "红熊复仇驱逐2-立刻执行 ", this.getClass().getSimpleName());
    }

    @Override
    public void phaseFinishDeal(LeagueRedBearRevengeModel model) {
        model.cleanDefendBear();
    }
}

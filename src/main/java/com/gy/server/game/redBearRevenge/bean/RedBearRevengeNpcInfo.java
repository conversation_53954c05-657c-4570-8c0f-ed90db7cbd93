package com.gy.server.game.redBearRevenge.bean;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.redBearRevenge.LeagueRedBearRevengeModel;
import com.gy.server.game.redBearRevenge.RedBearRevengeManager;
import com.gy.server.game.redBearRevenge.RedBearRevengeService;
import com.gy.server.game.redBearRevenge.event.AbstractRedBearRevengeEvent;
import com.gy.server.game.redBearRevenge.event.impl.RedBearRevengeNpcCreateAndMoveEvent;
import com.gy.server.game.redBearRevenge.template.RedBearRevengeActiveTimeTemplate;
import com.gy.server.packet.PbLeague;
import com.gy.server.scene.map.SceneMapService;
import com.gy.server.utils.MathUtil;
import com.ttlike.server.tl.baselib.serialize.LeagueRedBearRevenge.RedBearRevengeNpcInfoDb;
import com.ttlike.server.tl.baselib.serialize.scene.ScenePosition3D;

import java.util.*;
import java.util.stream.Collectors;

import static com.gy.server.game.redBearRevenge.template.RedBearRevengeConst.NPC_Defend_Bear;
import static com.gy.server.game.redBearRevenge.template.RedBearRevengeConst.NPC_Reward;

/**
 * 红熊复仇npc信息
 *
 * <AUTHOR> 2024/10/21 18:15
 **/
public class RedBearRevengeNpcInfo {

    /**
     * 唯一id
     */
    private long id;

    /**
     * 模版id,页签id
     */
    private int npcTempId;

    /**
     * npc血量,只有为熊卫是才有
     * 三刀
     */
    private int hp = -1;

    /**
     * 选定区域
     * 仅npc可移动是存在
     */
    private int area;

    /**
     * 当前位置
     */
    private ScenePosition3D curPoint;

    /**
     * 目标位置
     */
    private ScenePosition3D targetPoint;

    /**
     * 开始移动时间
     */
    private long startMoveTime = -1;

    /**
     * 结束移动时间
     */
    private long endMoveTime = -1;

    /**
     * boss奖励模板id，模板id为宝箱时才有   rewards页签id
     */
    private int rewardId;

    /**
     * boss奖励领取玩家id集合，模板id为宝箱时才有
     */
    private Set<Long> receivePlayerIdSet = new HashSet<>();

    public RedBearRevengeNpcInfo() {

    }

    public RedBearRevengeNpcInfo(int npcId, int area, ScenePosition3D curPoint, int rewardId) {
        this.id = ServerConstants.allocateRuntimeIdentifier();
        this.npcTempId = npcId;
        this.area = area;
        if(curPoint != null){
            this.curPoint = curPoint;
            this.targetPoint = curPoint;
        }
        if(npcId == NPC_Defend_Bear){
            //为熊卫时，有三刀的血量
            this.hp = RedBearRevengeService.getRedBearRevengeConst().defeatedNum;
        }
        if(npcId == NPC_Reward){
            this.rewardId = rewardId;
        }
    }

    public RedBearRevengeNpcInfo(RedBearRevengeNpcInfoDb db) {
        this.id = db.getId();
        this.npcTempId = db.getNpcTempId();
        this.hp = db.getHp();
        this.area = db.getArea();
        if(db.getCurPoint() != null){
            this.curPoint = new ScenePosition3D();
            this.curPoint.setX(db.getCurPoint().getX());
            this.curPoint.setY(db.getCurPoint().getY());
            this.curPoint.setZ(db.getCurPoint().getZ());
        }
        if(db.getTargetPoint() != null){
            this.targetPoint = new ScenePosition3D();
            this.targetPoint.setX(db.getTargetPoint().getX());
            this.targetPoint.setY(db.getTargetPoint().getY());
            this.targetPoint.setZ(db.getTargetPoint().getZ());
        }
        this.startMoveTime = db.getStartMoveTime();
        this.endMoveTime = db.getEndMoveTime();
        this.rewardId = db.getRewardId();
        this.receivePlayerIdSet.addAll(db.getReceivePlayerIdSet());
    }

    public static RedBearRevengeNpcInfo create(int npcId, ScenePosition3D curPoint){
        return new RedBearRevengeNpcInfo(npcId, 0, curPoint, 0);
    }

    public static RedBearRevengeNpcInfo create(int npcId, int area, ScenePosition3D curPoint){
        return new RedBearRevengeNpcInfo(npcId, area, curPoint, 0);
    }

    public PbLeague.RedBearRevengeNpcInfo genPb() {
        PbLeague.RedBearRevengeNpcInfo.Builder builder = PbLeague.RedBearRevengeNpcInfo.newBuilder();
        builder.setId(id);
        builder.setTempId(npcTempId);
        builder.setHp(hp);
        if(curPoint != null){
            builder.setCurPoint(SceneMapService.convertPosition(curPoint));
        }
        if(targetPoint != null){
            builder.setTargetPoint(SceneMapService.convertPosition(targetPoint));
        }
        builder.setStartMoveTime(startMoveTime);
        builder.setRewardId(rewardId);
        builder.addAllReceivePlayerId(receivePlayerIdSet);
        return builder.build();
    }


    public RedBearRevengeNpcInfoDb genDb() {
        RedBearRevengeNpcInfoDb db = new RedBearRevengeNpcInfoDb();
        db.setId(id);
        db.setNpcTempId(npcTempId);
        db.setHp(hp);
        db.setArea(area);
        if(curPoint != null){
            db.setCurPoint(new ScenePosition3D(curPoint.getX(), curPoint.getY(), curPoint.getZ()));
        }
        if(targetPoint != null){
            db.setTargetPoint(new ScenePosition3D(targetPoint.getX(), targetPoint.getY(), targetPoint.getZ()));
        }
        db.setStartMoveTime(startMoveTime);
        db.setEndMoveTime(endMoveTime);
        db.setRewardId(rewardId);
        db.getReceivePlayerIdSet().addAll(receivePlayerIdSet);
        return db;
    }

    public void reduceHp(){
        hp--;
    }

    /**
     * 是否正在移动
     * @return
     */
    public boolean isMove(){
        return !Objects.equals(curPoint, targetPoint);
    }

    /**
     * 随机移动
     */
    public void randomMove(LeagueRedBearRevengeModel model){
        RedBearRevengeActiveTimeTemplate activeTimeTemplate = model.getActiveTimeTemplate();
        if(activeTimeTemplate == null){
            throw new IllegalArgumentException("RedBearRevengeActiveTimeTemplate is null, phase is " + model.getPhase());
        }

        AbstractRedBearRevengeEvent event = activeTimeTemplate.eventMap.get(2);
        if(event == null){
            throw new IllegalArgumentException("RedBearRevengeActiveTimeTemplate is null, phase is " + model.getPhase());
        }
        if(!(event instanceof RedBearRevengeNpcCreateAndMoveEvent)){
            throw new IllegalArgumentException("event data is error" + model.getPhase());
        }
        RedBearRevengeNpcCreateAndMoveEvent createAndMoveEvent = (RedBearRevengeNpcCreateAndMoveEvent) event;

        List<ScenePosition3D> list = createAndMoveEvent.getAreaPointMap().get(area);
        randomMove(list);
    }

    /**
     * 随机移动
     */
    public void randomMove(List<ScenePosition3D> list) {
        List<ScenePosition3D> temp = list.stream().filter(bean -> !bean.equals(curPoint)).collect(Collectors.toList());
        this.targetPoint = MathUtil.random(temp);
        long millis = RedBearRevengeManager.calTime(npcTempId, curPoint, targetPoint);
        this.endMoveTime = this.startMoveTime + millis;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public int getNpcTempId() {
        return npcTempId;
    }

    public void setNpcTempId(int npcTempId) {
        this.npcTempId = npcTempId;
    }

    public int getHp() {
        return hp;
    }

    public void setHp(int hp) {
        this.hp = hp;
    }

    public int getArea() {
        return area;
    }

    public void setArea(int area) {
        this.area = area;
    }

    public ScenePosition3D getCurPoint() {
        return curPoint;
    }

    public void setCurPoint(ScenePosition3D curPoint) {
        this.curPoint = curPoint;
    }

    public ScenePosition3D getTargetPoint() {
        return targetPoint;
    }

    public void setTargetPoint(ScenePosition3D targetPoint) {
        this.targetPoint = targetPoint;
    }

    public long getStartMoveTime() {
        return startMoveTime;
    }

    public void setStartMoveTime(long startMoveTime) {
        this.startMoveTime = startMoveTime;
    }

    public long getEndMoveTime() {
        return endMoveTime;
    }

    public void setEndMoveTime(long endMoveTime) {
        this.endMoveTime = endMoveTime;
    }

    public int getRewardId() {
        return rewardId;
    }

    public void setRewardId(int rewardId) {
        this.rewardId = rewardId;
    }

    public Set<Long> getReceivePlayerIdSet() {
        return receivePlayerIdSet;
    }

    public void setReceivePlayerIdSet(Set<Long> receivePlayerIdSet) {
        this.receivePlayerIdSet = receivePlayerIdSet;
    }

    @Override
    public String toString() {
        return "RedBearRevengeNpcInfo{" +
                "id=" + id +
                ", npcTempId=" + npcTempId +
                ", hp=" + hp +
                ", area=" + area +
                ", curPoint=" + curPoint +
                ", targetPoint=" + targetPoint +
                ", startMoveTime=" + startMoveTime +
                ", endMoveTime=" + endMoveTime +
                ", rewardId=" + rewardId +
                ", receivePlayerIdSet=" + receivePlayerIdSet +
                '}';
    }

}

package com.gy.server.game.redBearRevenge.template;

import com.gy.server.game.redBearRevenge.RedBearRevengeService;
import com.gy.server.game.redBearRevenge.event.AbstractRedBearRevengeEvent;
import com.gy.server.game.redBearRevenge.status.RedBearRevengeNodeEnum;

import java.util.HashMap;
import java.util.Map;

/**
 * 阶段模版
 *
 * <AUTHOR> 2024/10/17 10:34
 **/
public class RedBearRevengeActiveTimeTemplate {

    /**
     * 阶段id
     */
    public RedBearRevengeNodeEnum id;

    /**
     * 持续时间 秒
     */
    public int duration;

    /**
     * 事件map
     * key:事件类型     value:AbstractRedBearRevengeEvent
     *
     * AbstractRedBearRevengeEvent:
     * 1.生成npc  参数1【数量】参数2【npcID】 参数3 【坐标点】
     * 2.活动范围 参数1【正方形行走面四个坐标】
     * 3.道具生成行走面范围坐标 参数1【正方形行走面四个坐标】
     */
    public Map<Integer, AbstractRedBearRevengeEvent> eventMap = new HashMap<>();

    public RedBearRevengeActiveTimeTemplate(Map<String, String> map) {
        this.id = RedBearRevengeNodeEnum.of(Integer.parseInt(map.get("PhaseId")));
        this.duration = Integer.parseInt(map.get("duration"));
        String[] eventTypes = map.get("ImmediateAction").split("\\|");
        String[] params = map.get("EventArgs").split("\\|");
        if(params.length < eventTypes.length){
            throw new IllegalArgumentException("redBearRevengeActiveTimeTemplate event and param is error!");
        }

        for (int i = 0; i < eventTypes.length; i++) {
            int eventType = Integer.parseInt(eventTypes[i]);
            eventMap.put(eventType, RedBearRevengeService.genEvent(eventType, params[i]));
        }
    }
}

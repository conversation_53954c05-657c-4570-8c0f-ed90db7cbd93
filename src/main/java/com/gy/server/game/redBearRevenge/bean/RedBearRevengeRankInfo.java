package com.gy.server.game.redBearRevenge.bean;

import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.league.bean.LeagueMember;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.packet.PbLeague;
import com.ttlike.server.tl.baselib.serialize.LeagueRedBearRevenge.RedBearRevengeRankInfoDb;

/**
 * 红熊复仇排行信息
 *
 * <AUTHOR> 2024/11/5 18:18
 **/
public class RedBearRevengeRankInfo {

    /**
     * 玩家id
     */
    private long id;

    /**
     * 击杀熊卫数
     */
    private int kill;

    /**
     * 挑战次数
     */
    private int challengeNum;

    /**
     * 攻击boss伤害
     */
    private long damage;

    public RedBearRevengeRankInfo() {
    }

    public RedBearRevengeRankInfo(long id) {
        this.id = id;
    }

    public RedBearRevengeRankInfo(RedBearRevengeRankInfoDb db) {
        this.id = db.getId();
        this.kill = db.getKill();
        this.challengeNum = db.getChallengeNum();
        this.damage = db.getDamage();
    }

    public PbLeague.RedBearRevengeRankInfo genPb() {
        PbLeague.RedBearRevengeRankInfo.Builder builder = PbLeague.RedBearRevengeRankInfo.newBuilder();
        MiniGamePlayer miniGamePlayer = PlayerManager.getMiniPlayer(id);
        builder.setMiniPlayerInfo(miniGamePlayer.genMinMiniUser());
        League league = LeagueManager.getLeagueByPlayerId(id);
        LeagueMember leagueMember = league.getMemberMap().get(id);
        builder.setJob(leagueMember.getJobId());
        builder.setPoint(kill);
        builder.setChallengeNum(challengeNum);
        builder.setDamage(damage);
        return builder.build();
    }

    public RedBearRevengeRankInfoDb genDb() {
        RedBearRevengeRankInfoDb db = new RedBearRevengeRankInfoDb();
        db.setId(id);
        db.setKill(kill);
        db.setChallengeNum(challengeNum);
        db.setDamage(damage);
        return db;
    }

    public void addKill(){
        kill++;
    }

    public void addDamage(long addDamage){
        challengeNum++;
        damage = damage + addDamage;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public int getKill() {
        return kill;
    }

    public void setKill(int kill) {
        this.kill = kill;
    }

    public long getDamage() {
        return damage;
    }

    public void setDamage(long damage) {
        this.damage = damage;
    }

    public int getChallengeNum() {
        return challengeNum;
    }

    public void setChallengeNum(int challengeNum) {
        this.challengeNum = challengeNum;
    }

}

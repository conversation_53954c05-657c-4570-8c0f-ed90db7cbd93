package com.gy.server.game.redBearRevenge.status;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.log.Logger;
import com.gy.server.core.log.LoggerType;
import com.gy.server.game.redBearRevenge.LeagueRedBearRevengeModel;
import com.gy.server.game.redBearRevenge.status.impl.*;
import com.gy.server.game.redBearRevenge.template.RedBearRevengeActiveTimeTemplate;
import com.gy.server.utils.time.DateTimeUtil;

import java.util.*;

/**
 * 红熊复仇状态枚举
 *
 * <AUTHOR> 2024/10/16 19:40
 **/
public enum RedBearRevengeNodeEnum {

    notOpen(-1, new RedBearRevenge0NotOpenHandler()),
    ready(1, new RedBearRevenge1ReadyHandler()),

    /**
     * 驱逐阶段  四个打乱顺序
     */
    deportation1(2, new RedBearRevenge2Deportation1Handler()),
    deportation2(3, new RedBearRevenge3Deportation2Handler()),
    deportation3(4, new RedBearRevenge4Deportation3Handler()),
    deportation4(5, new RedBearRevenge5Deportation4Handler()),

    /**
     * 战斗阶段 A/B
     */
    battleA(6, new RedBearRevenge6BattleAHandler()),
    battleB(7,  new RedBearRevenge7BattleBHandler()),

    /**
     * 结算奖励
     */
    reward(8, new RedBearRevenge8RewardHandler()),
    ;

    private final int type;
    private final AbsRedBearRevengeStatusExecutor executor;


    RedBearRevengeNodeEnum(int type, AbsRedBearRevengeStatusExecutor executor) {
        this.type = type;
        this.executor = executor;
    }

    private static final Map<Integer, RedBearRevengeNodeEnum> map = new HashMap<>();
    public static RedBearRevengeNodeEnum of(int type) {
        return map.get(type);
    }
    static {
        for (RedBearRevengeNodeEnum value : values()) {
            map.put(value.type, value);
        }
    }

    public int getType() {
        return type;
    }

    /**
     * 尝试切换阶段
     */
    public void tryNextPhase(LeagueRedBearRevengeModel model){
        RedBearRevengeActiveTimeTemplate template = model.getNowPhaseTemplate();
        if(Objects.isNull(template)){
            //直接切下赛季
            changePhase(model);
        }else{
            //检查阶段开始时间
            if(model.getStartPhaseTime() > 0 &&
                    model.getStartPhaseTime() + template.duration * DateTimeUtil.MillisOfSecond
                            < ServerConstants.getCurrentTimeMillis() || specialCheck(model)){
                if(model.getPhase() == null || model.getPhase() == RedBearRevengeNodeEnum.reward){
                    //结束
                    model.reset();
                    //状态推送
                    model.sync(false);
                    RedBearRevengeNodeEnum.log("红熊复仇-重置。。。 ");
                }else{
                    changePhase(model);
                }
            }
        }
    }

    /**
     * 阶段切换特殊检查
     * @param model
     * @return
     */
    private boolean specialCheck(LeagueRedBearRevengeModel model) {
        return (model.getPhase() == battleA || model.getPhase() == battleB) && model.getSurplusHp() <= 0l;
    }

    public static void log(String log){
        LoggerType.redBear.logger.info(Logger.getCurrentTimeLogText() + " " + log);
    }

    public void doRightNow(LeagueRedBearRevengeModel model){
        executor.doRightNow(model);
    }

    /**
     * 切换阶段
     * @param model
     */
    public void changePhase(LeagueRedBearRevengeModel model){
        model.getPhase().executor.phaseFinishDeal(model);
        executor.changePhase(model);
    }

    private void phaseFinishDeal(LeagueRedBearRevengeModel model) {
    }

    public void init(LeagueRedBearRevengeModel model){
        executor.init(model);
    }


    public static interface RBExecutor{
        public void init(LeagueRedBearRevengeModel model);
        public RedBearRevengeNodeEnum curStatus();
        public RedBearRevengeNodeEnum nextStatus(LeagueRedBearRevengeModel model);

        public void changePhase(LeagueRedBearRevengeModel model);

        public void phaseFinishDeal(LeagueRedBearRevengeModel model);
    }
}

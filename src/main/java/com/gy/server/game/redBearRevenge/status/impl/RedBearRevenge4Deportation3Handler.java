package com.gy.server.game.redBearRevenge.status.impl;

import com.gy.server.game.redBearRevenge.LeagueRedBearRevengeModel;
import com.gy.server.game.redBearRevenge.status.AbsRedBearRevengeStatusExecutor;
import com.gy.server.game.redBearRevenge.status.RedBearRevengeNodeEnum;

/**
 * 驱逐3
 *
 * <AUTHOR> 2024/10/24 17:00
 **/
public class RedBearRevenge4Deportation3Handler extends AbsRedBearRevengeStatusExecutor {

    @Override
    public RedBearRevengeNodeEnum curStatus() {
        return RedBearRevengeNodeEnum.deportation3;
    }

    @Override
    public RedBearRevengeNodeEnum nextStatus(LeagueRedBearRevengeModel model) {
        return model.getDeportationNextStatus(curStatus());
    }

    @Override
    public void doRightNow(LeagueRedBearRevengeModel model) {
        eventDoRightNow(model, "红熊复仇驱逐3-立刻执行 ", this.getClass().getSimpleName());
    }

    @Override
    public void phaseFinishDeal(LeagueRedBearRevengeModel model) {
        model.cleanDefendBear();
    }
}

package com.gy.server.game.redBearRevenge.status.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.redBearRevenge.LeagueRedBearRevengeModel;
import com.gy.server.game.redBearRevenge.status.AbsRedBearRevengeStatusExecutor;
import com.gy.server.game.redBearRevenge.status.RedBearRevengeNodeEnum;

import java.util.Collections;

/**
 * 准备
 *
 * <AUTHOR> 2024/10/17 10:22
 **/
public class RedBearRevenge1ReadyHandler extends AbsRedBearRevengeStatusExecutor {

    @Override
    public void init(LeagueRedBearRevengeModel model) {
        model.getDeportationIndexList().clear();
        model.getDeportationIndexList().add(RedBearRevengeNodeEnum.deportation1);
        model.getDeportationIndexList().add(RedBearRevengeNodeEnum.deportation2);
        model.getDeportationIndexList().add(RedBearRevengeNodeEnum.deportation3);
        model.getDeportationIndexList().add(RedBearRevengeNodeEnum.deportation4);
        Collections.shuffle(model.getDeportationIndexList());
    }

    @Override
    public RedBearRevengeNodeEnum curStatus() {
        return RedBearRevengeNodeEnum.ready;
    }

    @Override
    public RedBearRevengeNodeEnum nextStatus(LeagueRedBearRevengeModel model) {
        if(model.getDeportationIndexList().isEmpty()){
            init(model);
        }
        return model.getDeportationNextStatus(curStatus());
    }

    @Override
    public void doRightNow(LeagueRedBearRevengeModel model) {
        model.setStartPhaseTime(ServerConstants.getCurrentTimeMillis());
        RedBearRevengeNodeEnum.log("红熊王复仇-立刻执行 " +this.getClass().getSimpleName());
    }

}

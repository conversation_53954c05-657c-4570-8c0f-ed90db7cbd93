package com.gy.server.game.redBearRevenge;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.attribute.AttributeKey;
import com.gy.server.game.battleCollect.BattleCollectService;
import com.gy.server.game.combat.CombatManager;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.common.ConstantConfigReader;
import com.gy.server.game.drop.DropService;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.function.Function;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.league.LeagueModelEnums;
import com.gy.server.game.league.enums.LeagueJobTypeEnums;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.redBear.template.LeagueRedBearConstant;
import com.gy.server.game.redBearRevenge.bean.BearDefendBoxInfo;
import com.gy.server.game.redBearRevenge.bean.RedBearRevengeNpcInfo;
import com.gy.server.game.redBearRevenge.bean.RedBearRevengeRankInfo;
import com.gy.server.game.redBearRevenge.event.AbstractRedBearRevengeEvent;
import com.gy.server.game.redBearRevenge.event.impl.RedBearRevengeCreateNpcEvent;
import com.gy.server.game.redBearRevenge.event.impl.RedBearRevengeNpcCreateAndMoveEvent;
import com.gy.server.game.redBearRevenge.event.impl.RedBearRevengeRewardEvent;
import com.gy.server.game.redBearRevenge.stage.RedBearRevengeStage;
import com.gy.server.game.redBearRevenge.status.RedBearRevengeNodeEnum;
import com.gy.server.game.redBearRevenge.template.*;
import com.gy.server.game.service.Service;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.MathUtil;
import org.jetbrains.annotations.Nullable;

import java.util.*;

/**
 * 红熊复仇服务类
 *
 * <AUTHOR> 2024/10/17 17:18
 **/
public class RedBearRevengeService extends PlayerPacketHandler implements Service {

    /**
     * 时间阶段map
     */
    private static Map<RedBearRevengeNodeEnum, RedBearRevengeActiveTimeTemplate> activeTimeTemplateMap = new HashMap<>();

    /**
     * npc模板map
     * key:npcId
     */
    private static Map<Integer, RedBearRevengeNpcParameterTemplate> npcParameterTemplateMap = new HashMap<>();

    /**
     * 结局奖励map
     */
    private static Map<Integer, RedBearRevengeRewardsTemplate> rewardsTemplateMap = new HashMap<>();

    /**
     * 宝箱map
     */
    private static Map<Integer, RedBearRevengeChestTemplate> chestTemplateMap = new HashMap<>();

    private static RedBearRevengeConst redBearRevengeConst;

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.redBearRevenge_ActiveTime);
        Map<RedBearRevengeNodeEnum, RedBearRevengeActiveTimeTemplate> activeTimeTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            RedBearRevengeActiveTimeTemplate template = new RedBearRevengeActiveTimeTemplate(map);
            activeTimeTemplateMapTemp.put(template.id, template);
        }
        activeTimeTemplateMap = activeTimeTemplateMapTemp;

        mapList = ConfigReader.read(ConfigFile.redBearRevenge_NpcParameter);
        Map<Integer, RedBearRevengeNpcParameterTemplate> npcParameterTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            RedBearRevengeNpcParameterTemplate template = new RedBearRevengeNpcParameterTemplate(map);
            npcParameterTemplateMapTemp.put(template.id, template);
        }
        npcParameterTemplateMap = npcParameterTemplateMapTemp;

        mapList = ConfigReader.read(ConfigFile.redBearRevenge_Rewards);
        Map<Integer, RedBearRevengeRewardsTemplate> rewardsTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            RedBearRevengeRewardsTemplate template = new RedBearRevengeRewardsTemplate(map);
            rewardsTemplateMapTemp.put(template.id, template);
        }
        rewardsTemplateMap = rewardsTemplateMapTemp;

        mapList = ConfigReader.read(ConfigFile.redBearRevenge_chest);
        Map<Integer, RedBearRevengeChestTemplate> chestTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            RedBearRevengeChestTemplate template = new RedBearRevengeChestTemplate(map);
            chestTemplateMapTemp.put(template.npcId, template);
        }
        chestTemplateMap = chestTemplateMapTemp;

        Map<String, String> map = ConstantConfigReader.read(ConfigFile.redBearRevenge_const);
        redBearRevengeConst = new RedBearRevengeConst(map);
    }

    @Override
    public void clearConfigData() {
        activeTimeTemplateMap.clear();
        npcParameterTemplateMap.clear();
        rewardsTemplateMap.clear();
        chestTemplateMap.clear();
    }

    public static AbstractRedBearRevengeEvent genEvent(int type, String param){
        switch (type){
            case 1:{
                return new RedBearRevengeCreateNpcEvent(param);
            }
            case 2:{
                return new RedBearRevengeNpcCreateAndMoveEvent(param);
            }
            case 3:{
                return new RedBearRevengeRewardEvent(param);
            }
        }
        return null;
    }

    /**
     * 红熊复仇信息
     */
    @Handler(PtCode.RED_BEAR_REVENGE_INFO_REQ)
    private void reqRearRevengeInfoReq(Player player, PbProtocol.RedBearRevengeInfoReq req, long time){
        PbProtocol.RedBearRevengeInfoRst.Builder rst = PbProtocol.RedBearRevengeInfoRst.newBuilder().setResult(Text.genOkServerRstInfo());

        logic:{
            int text = commonCheck(player);
            if(text != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(text));
                break logic;
            }

            League league = LeagueManager.getLeagueByPlayer(player);
            LeagueRedBearRevengeModel redBearRevengeModel = league.getModel(LeagueModelEnums.redBearRevenge);
            rst.setInfo(redBearRevengeModel.genPb());
        }
        player.send(PtCode.RED_BEAR_REVENGE_INFO_RST, rst.build(), time);
    }

    /**
     * 红熊复仇开启
     */
    @Handler(PtCode.RED_BEAR_REVENGE_OPEN_REQ)
    private void redBearRevengeOpenReq(Player player, PbProtocol.RedBearRevengeOpenReq req, long time){
        PbProtocol.RedBearRevengeOpenRst.Builder rst = PbProtocol.RedBearRevengeOpenRst.newBuilder().setResult(Text.genOkServerRstInfo());
        long openTime = req.getOpenTime();
        logic:{
            int text = commonCheck(player);
            if(text != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(text));
                break logic;
            }

            League league = LeagueManager.getLeagueByPlayer(player);
            LeagueRedBearRevengeModel redBearRevengeModel = league.getModel(LeagueModelEnums.redBearRevenge);
            if (!league.hasAuth(player.getPlayerId(), LeagueJobTypeEnums.设置活动开启时间)) {
                rst.setResult(Text.genServerRstInfo(Text.帮派权限不足));
                break logic;
            }

            //检查能否开启
            if(redBearRevengeModel.isOpen()){
                rst.setResult(Text.genServerRstInfo(Text.红熊复仇正在进行中));
                break logic;
            }

            if(ServerConstants.getCurrentTimeMillis() > req.getOpenTime()){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }

            //检查开启时间
            if(!redBearRevengeConst.checkOpenTime(openTime)){
                rst.setResult(Text.genServerRstInfo(Text.红熊复仇开启时间错误));
                break logic;
            }

            if(redBearRevengeModel.getOpenTimes() >= redBearRevengeConst.weekNum){
                rst.setResult(Text.genServerRstInfo(Text.红熊复仇开启次数不足));
                break logic;
            }

            redBearRevengeModel.setStartTime(openTime);
            redBearRevengeModel.addOpenTimes();
            redBearRevengeModel.sync(false);
        }
        player.send(PtCode.RED_BEAR_REVENGE_OPEN_RST, rst.build(), time);
    }

    /**
     * 红熊复仇-进攻熊卫
     */
    @Handler(PtCode.RED_BEAR_REVENGE_ATK_BEAR_DEF_REQ)
    private void redBearRevengeAtkBearDefReq(Player player, PbProtocol.RedBearRevengeAtkBearDefReq req, long time){
        PbProtocol.RedBearRevengeAtkBearDefRst.Builder rst = PbProtocol.RedBearRevengeAtkBearDefRst.newBuilder().setResult(Text.genOkServerRstInfo());
        long npcId = req.getNpcId();

        logic:{
            int text = commonCheck(player);
            if(text != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(text));
                break logic;
            }

            League league = LeagueManager.getLeagueByPlayer(player);
            LeagueRedBearRevengeModel redBearRevengeModel = league.getModel(LeagueModelEnums.redBearRevenge);
            if(redBearRevengeModel.getPhase() == RedBearRevengeNodeEnum.notOpen
                    || redBearRevengeModel.getPhase() == RedBearRevengeNodeEnum.ready
                    || redBearRevengeModel.getStartTime() <= 0){
                rst.setResult(Text.genServerRstInfo(Text.红熊复仇活动未开始));
                break logic;
            }

            RedBearRevengeNpcInfo npcInfo = redBearRevengeModel.getNpcInfoMap().get(npcId);
            if(Objects.isNull(npcInfo)){
                rst.setResult(Text.genServerRstInfo(Text.红熊复仇熊卫被其他人打死));
                break logic;
            }

            if(npcInfo.getNpcTempId() != RedBearRevengeConst.NPC_Defend_Bear){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            //TODO 检查距离

            npcInfo.reduceHp();
            if(npcInfo.getHp() <= 0){
                redBearRevengeModel.getNpcInfoMap().remove(npcId);
                //更新击杀数量
                redBearRevengeModel.getRankMap().computeIfAbsent(player.getPlayerId(), bean -> new RedBearRevengeRankInfo(player.getPlayerId())).addKill();
                //是否触发熊卫宝箱
                if(MathUtil.randomInt(10000) <= redBearRevengeConst.chance){
                    redBearRevengeModel.addDefBearBox(player);
                }
            }

            redBearRevengeModel.sync(true);
        }

        player.send(PtCode.RED_BEAR_REVENGE_ATK_BEAR_DEF_RST, rst.build(), time);
    }

    /**
     * 红熊复仇-进攻大熊
     */
    @Handler(PtCode.RED_BEAR_REVENGE_ATK_BIG_BEAR_REQ)
    private void redBearBattleReq(Player player, PbProtocol.RedBearRevengeAtkBigBearReq req, long time){
        PbProtocol.RedBearRevengeAtkBigBearRst.Builder rst = PbProtocol.RedBearRevengeAtkBigBearRst.newBuilder().setResult(Text.genOkServerRstInfo());
        long npcId = req.getNpcId();
        logic:{
            int text = commonCheck(player);
            if(text != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(text));
                break logic;
            }

            League league = LeagueManager.getLeagueByPlayer(player);

            LeagueRedBearRevengeModel redBearRevengeModel = league.getModel(LeagueModelEnums.redBearRevenge);
            if(redBearRevengeModel.getPhase() == RedBearRevengeNodeEnum.notOpen
                    || redBearRevengeModel.getPhase() == RedBearRevengeNodeEnum.ready
                    || redBearRevengeModel.getStartTime() <= 0){
                rst.setResult(Text.genServerRstInfo(Text.红熊复仇活动未开始));
                break logic;
            }
//            long npcId = req.getNpcId();
            RedBearRevengeNpcInfo npcInfo = redBearRevengeModel.getNpcInfoMap().get(npcId);
            if(Objects.isNull(npcInfo) || (npcInfo.getNpcTempId() != RedBearRevengeConst.NPC_Big_Bear && npcInfo.getNpcTempId() != RedBearRevengeConst.Npc_Rage_Big_Bear)){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }

            RedBearRevengeStage stage = new RedBearRevengeStage(player, npcInfo.getNpcTempId());
            stage.init();
            rst.setStage(stage.getStageRecord());
            CombatManager.combatPrepare(stage);
            redBearRevengeModel.getInCombatPlayerIds().add(player.getPlayerId());
        }
        player.send(PtCode.RED_BEAR_REVENGE_ATK_BIG_BEAR_RST, rst.build(), time);
    }

    /**
     * 红熊复仇-触发奖励
     */
    @Handler(PtCode.RED_BEAR_REVENGE_TRIGGER_REWARD_REQ)
    private void redBearRevengeTriggerRewardReq(Player player, PbProtocol.RedBearRevengeTriggerRewardReq req, long time){
        PbProtocol.RedBearRevengeTriggerRewardRst.Builder rst = PbProtocol.RedBearRevengeTriggerRewardRst.newBuilder().setResult(Text.genOkServerRstInfo());
        long npcId = req.getNpcId();

        logic:{
            int text = commonCheck(player);
            if(text != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(text));
                break logic;
            }

            League league = LeagueManager.getLeagueByPlayer(player);
            LeagueRedBearRevengeModel redBearRevengeModel = league.getModel(LeagueModelEnums.redBearRevenge);

            if(!redBearRevengeModel.getRankMap().containsKey(player.getPlayerId())){
                rst.setResult(Text.genServerRstInfo(Text.红熊复仇未参与不能领奖));
                break logic;
            }

            RedBearRevengeNpcInfo npcInfo = redBearRevengeModel.getNpcInfoMap().get(npcId);
            if(npcInfo == null){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }

            if(npcInfo.getNpcTempId() != RedBearRevengeConst.NPC_Reward){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }

            if(npcInfo.getReceivePlayerIdSet().contains(player.getPlayerId())){
                rst.setResult(Text.genServerRstInfo(Text.奖励已领取));
                break logic;
            }

            RedBearRevengeRewardsTemplate rewardsTemplate = rewardsTemplateMap.get(npcInfo.getRewardId());
            List<Reward> rewardList = Reward.templateCollectionToReward(rewardsTemplate.rewards);
            npcInfo.getReceivePlayerIdSet().add(player.getPlayerId());
            Reward.add(rewardList, player, BehaviorType.RedBearRevengeBigBearReward);
            rst.addAllRewards(Reward.writeCollectionToPb(rewardList));
        }

        player.send(PtCode.RED_BEAR_REVENGE_TRIGGER_REWARD_RST, rst.build(), time);
    }

    /**
     * 红熊复仇-领取熊卫宝箱
     */
    @Handler(PtCode.RED_BEAR_REVENGE_RECEIVE_DEF_BOX_REQ)
    private void redBearRevengeDefBoxReq(Player player, PbProtocol.RedBearRevengeReceiveDefBoxReq req, long time){
        PbProtocol.RedBearRevengeReceiveDefBoxRst.Builder rst = PbProtocol.RedBearRevengeReceiveDefBoxRst.newBuilder().setResult(Text.genOkServerRstInfo());
        long id = req.getId();
        boolean isOneClick = req.getIsOneClick();

        logic:{
            int text = commonCheck(player);
            if(text != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(text));
                break logic;
            }

            League league = LeagueManager.getLeagueByPlayer(player);
            LeagueRedBearRevengeModel redBearRevengeModel = league.getModel(LeagueModelEnums.redBearRevenge);

            if(!redBearRevengeModel.getRankMap().containsKey(player.getPlayerId())){
                rst.setResult(Text.genServerRstInfo(Text.红熊复仇未参与不能领奖));
                break logic;
            }

            List<Reward> rewardList = new ArrayList<>();
            List<Long> receiveIdList = new ArrayList<>();
            if(!isOneClick){
                BearDefendBoxInfo boxInfo = redBearRevengeModel.getBoxInfoMap().get(id);
                if(boxInfo == null){
                    rst.setResult(Text.genServerRstInfo(Text.参数异常));
                    break logic;
                }

                if(boxInfo.getReceivePlayerId().contains(player.getPlayerId())){
                    rst.setResult(Text.genServerRstInfo(Text.奖励已领取));
                    break logic;
                }

                int dropId = chestTemplateMap.get(boxInfo.getExcelId()).dropId;
                rewardList.addAll(DropService.executeDrop(player, dropId, BehaviorType.RedBearRevengeBearDefBoxReward, false, false));
                receiveIdList.add(boxInfo.getId());
            }else {
                for (BearDefendBoxInfo bean : redBearRevengeModel.getBoxInfoMap().values()) {
                    if(bean.getReceivePlayerId().contains(player.getPlayerId())){
                        continue;
                    }

                    int dropId = chestTemplateMap.get(bean.getExcelId()).dropId;
                    rewardList.addAll(DropService.executeDrop(player, dropId, BehaviorType.RedBearRevengeBearDefBoxReward, false, false));
                    receiveIdList.add(bean.getId());
                }
            }

            if(receiveIdList.isEmpty()){
                rst.setResult(Text.genServerRstInfo(Text.红熊复仇没有可领取奖励));
                break logic;
            }

            Reward.merge(rewardList);
            redBearRevengeModel.getBoxInfoMap().values().forEach(bean -> {
                if(receiveIdList.contains(bean.getId())){
                    bean.getReceivePlayerId().add(player.getPlayerId());
                }
            });

            Reward.add(rewardList, player, BehaviorType.RedBearRevengeBearDefBoxReward);
            rst.addAllRewards(Reward.writeCollectionToPb(rewardList));

            redBearRevengeModel.updateRedDot(player);
        }

        player.send(PtCode.RED_BEAR_REVENGE_RECEIVE_DEF_BOX_RST, rst.build(), time);
    }

    /**
     * 红熊复仇-获取排行信息
     */
    @Handler(PtCode.RED_BEAR_REVENGE_RANK_INFO_REQ)
    private void redBearRevengeRankInfoReq(Player player, PbProtocol.RedBearRevengeRankInfoReq req, long time){
        PbProtocol.RedBearRevengeRankInfoRst.Builder rst = PbProtocol.RedBearRevengeRankInfoRst.newBuilder().setResult(Text.genOkServerRstInfo());

        logic:{
            int text = commonCheck(player);
            if(text != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(text));
                break logic;
            }

            League league = LeagueManager.getLeagueByPlayer(player);
            LeagueRedBearRevengeModel redBearRevengeModel = league.getModel(LeagueModelEnums.redBearRevenge);
            redBearRevengeModel.getRankMap().values().forEach(bean -> rst.addRankInfo(bean.genPb()));
        }

        player.send(PtCode.RED_BEAR_REVENGE_RANK_INFO_RST, rst.build(), time);
    }

    @Nullable
    private static int commonCheck(Player player) {
        //检查帮派限制
        League league = LeagueManager.getLeagueByPlayer(player);
        if(Objects.isNull(league)){
            return Text.未加入帮派;
        }

        //检查帮派限制
        if(Function.redBearRevenge.isNotOpen(player)){
            return Text.功能未开启;
        }

        if(LeagueManager.canNotPlayLeague(player)){
            return Text.您退出帮派未满足24小时无法参与帮派活动;
        }
        if(!League.canPlay(player, Function.leagueEscort)){
            return Text.帮派玩法已经参加过;
        }
        return Text.没有异常;
    }

    public static long getMaxHp(){
        RedBearRevengeNpcParameterTemplate npcTemplate = npcParameterTemplateMap.get(LeagueRedBearConstant.NPC_Big_Bear);
        List<HeroUnit> heroUnits = BattleCollectService.createHeroUnits(npcTemplate.battleCollectId);
        return heroUnits.get(0).getAttributes().getValue(AttributeKey.最大生命);
    }

    public static Map<RedBearRevengeNodeEnum, RedBearRevengeActiveTimeTemplate> getActiveTimeTemplateMap() {
        return activeTimeTemplateMap;
    }

    public static RedBearRevengeActiveTimeTemplate getLeagueRedBearRevengeActiveTimeTemplate(RedBearRevengeNodeEnum nodeEnum){
        return activeTimeTemplateMap.get(nodeEnum);
    }

    public static RedBearRevengeActiveTimeTemplate getLeagueRedBearRevengeActiveTimeTemplate(int nodeEnum){
        return activeTimeTemplateMap.get(RedBearRevengeNodeEnum.of(nodeEnum));
    }

    public static Map<Integer, RedBearRevengeNpcParameterTemplate> getNpcParameterTemplateMap() {
        return npcParameterTemplateMap;
    }

    public static Map<Integer, RedBearRevengeRewardsTemplate> getRewardsTemplateMap() {
        return rewardsTemplateMap;
    }

    public static Map<Integer, RedBearRevengeChestTemplate> getChestTemplateMap() {
        return chestTemplateMap;
    }

    public static RedBearRevengeConst getRedBearRevengeConst() {
        return redBearRevengeConst;
    }
}

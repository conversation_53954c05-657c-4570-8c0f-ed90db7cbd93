package com.gy.server.game.redBearRevenge.status.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.redBearRevenge.LeagueRedBearRevengeModel;
import com.gy.server.game.redBearRevenge.event.impl.RedBearRevengeRewardEvent;
import com.gy.server.game.redBearRevenge.status.AbsRedBearRevengeStatusExecutor;
import com.gy.server.game.redBearRevenge.status.RedBearRevengeNodeEnum;

/**
 * 结算
 *
 * <AUTHOR> 2024/10/24 17:17
 **/
public class RedBearRevenge8RewardHandler extends AbsRedBearRevengeStatusExecutor {

    @Override
    public RedBearRevengeNodeEnum curStatus() {
        return RedBearRevengeNodeEnum.reward;
    }

    @Override
    public RedBearRevengeNodeEnum nextStatus(LeagueRedBearRevengeModel model) {
        return RedBearRevengeNodeEnum.notOpen;
    }

    @Override
    public void doRightNow(LeagueRedBearRevengeModel model) {
        RedBearRevengeRewardEvent event = new RedBearRevengeRewardEvent(null);
        event.trigger(model);

        //开始计时
        model.setStartPhaseTime(ServerConstants.getCurrentTimeMillis());
        RedBearRevengeNodeEnum.log("红熊复仇-立刻执行 " + this.getClass().getSimpleName());
    }
}

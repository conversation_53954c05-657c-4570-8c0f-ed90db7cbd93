package com.gy.server.game;

import com.gy.server.core.Configuration;
import com.gy.server.core.log.CommonLogger;
import com.gy.server.utils.CharsetEncoding;
import com.gy.server.utils.JsonUtil;
import com.gy.server.utils.net.HttpUtil;
import com.gy.server.utils.runner.Runner;
import com.ttlike.server.tl.baselib.CommonsConfiguration;
import com.ttlike.server.tl.baselib.serialize.server.GameServerState;

/**
 * @program: gs-trunk
 * @description: 区服状态管理器
 * @author: Huang.Xia
 * @create: 2025/5/22
 **/
public class GameServerStateUpdater implements Runner {

    private GameServerState state;

    public static GameServerStateUpdater instance = new GameServerStateUpdater();

    long lastUpdateTime = 0;


    @Override
    public void runnerExecute() throws Exception {
        if(state != null && state.getLimitIps().size() == 0){
            if(System.currentTimeMillis() - lastUpdateTime < 10000){
                return;
            }else if(lastUpdateTime > System.currentTimeMillis()){
                lastUpdateTime = System.currentTimeMillis();
            }

            lastUpdateTime = System.currentTimeMillis();
        }

        String url = CommonsConfiguration.getBillingUrl() + "/server_state";
        String postData = String.format("serverId=%s", Configuration.serverId);
        try {
            String result = HttpUtil.requestHttpWithPostReturnString(url, postData, CharsetEncoding.ENCODING_UTF_8);
            if (result != null) {
                state = JsonUtil.json2Obj(result, GameServerState.class);
            }
        }catch (Exception e){
            CommonLogger.error("更新区服状态失败 : " + url, e);
        }
    }

    @Override
    public long getRunnerInterval() {
        return 1000;
    }

    public GameServerState getState() {
        return state;
    }

}

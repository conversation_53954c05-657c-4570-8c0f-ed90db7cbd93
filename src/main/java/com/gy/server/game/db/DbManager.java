package com.gy.server.game.db;

import java.io.Serializable;
import java.util.*;

import com.gy.server.core.Configuration;
import com.gy.server.core.log.CommonLogger;
import com.gy.server.core.log.SystemLogger;
import com.gy.server.db.cache.guava.GuavaCacheUtil;
import com.gy.server.db.relation.hibernate.DataSource;
import com.gy.server.db.relation.hibernate.HibernateUtil;
import com.gy.server.game.event.ServerEvent;
import com.gy.server.game.event.ServerEventHandler;
import com.gy.server.game.event.ServerEventManager;
import com.gy.server.game.event.ServerEventType;
import com.gy.server.game.player.Player;
import com.gy.server.utils.EmbeddedLogger;
import com.ttlike.server.tl.baselib.CommonsConfiguration;

import javax.persistence.Table;

import static com.gy.server.game.event.ServerEventType.cacheCleanUp;

/**
 * <AUTHOR> - [Created on 2018/1/30 10:53]
 */
public class DbManager implements ServerEventHandler {

    private static final ServerEventType[] eventTypes = new ServerEventType[]{cacheCleanUp};

    private static volatile Map<Integer,DataSource> dataSources = new HashMap<>();

    private final static DbManager instance = new DbManager();

    /**
     * 使用缓存的className集合
     */
    private static Set<String> cacheClassName = new HashSet<>();

    private DbManager() {

    }


    public static void init() {

        //初始化使用缓存的class集合
        initCacheClazz();

        //根据db表对应class设置缓存
        initDbClassToCache();

        //DbManager注册为服务器事件处理器
        ServerEventManager.registerEventHandler(instance);
    }


    private static void initCacheClazz() {
        //nothing yet...
    }

    private static void initDbClassToCache() {
        GuavaCacheUtil.addCache(Player.class.getName());
    }

    public static boolean isCacheClass(String className) {
        return cacheClassName.contains(className);
    }

    public static boolean isCacheClass(Class clazz) {
        return isCacheClass(clazz.getName());
    }

    public static void mainThreadCheck(){
        try {
            if (!CommonsConfiguration.runMode.isLive() && CommonsConfiguration.isMainThread()) {
                EmbeddedLogger.warn("<<< Warning: DB Operation in Main Thread >>>");
                StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
                for (StackTraceElement element : stackTrace) {
                    EmbeddedLogger.warn(element.getClassName() + " -> " + element.getMethodName() + " : " + element.getLineNumber());
                }
            }
        }catch (Exception e){
            EmbeddedLogger.error("mainThreadCheck error");
        }
    }

    public static void deleteFromCache(Object entity, Object key) {
        GuavaCacheUtil.delete(entity.getClass().getName(), key);
    }

    /**
     * 清空数据库数据
     */
    public static void clearAll() {
        for(DataSource dataSource : dataSources.values()) {
            for (String className : dataSource.getAllClassNames()) {
                try {
                    Class clazz = Class.forName(className);
                    Table tableAnnotation = (Table) clazz.getAnnotation(Table.class);
                    if (tableAnnotation != null) {
                        deleteTable(dataSource, tableAnnotation.name());
                    }
                } catch (Exception e) {

                }
            }
        }
    }

    private static void deleteTable(DataSource dataSource,String tableName){
        try {
            String sql = "delete from " + tableName;
            dataSource.executeSql(sql);
        }catch (Exception e){
//            //不存在的表
            CommonLogger.info("sql table is null, tableName is " + tableName);
        }
    }

    public static void shutdown() {

    }

    @Override
    public ServerEventType[] getEventTypes() {
        return eventTypes;
    }

    @Override
    public void handle(ServerEvent event) {
        switch (event.getEventType()) {
            case cacheCleanUp: {
                GuavaCacheUtil.cleanUpAll();
                break;
            }
            default:
                break;
        }
    }

    public static void add(int serverNumber, Object entity) {
        DbManager.instance.getDataSource(serverNumber).save(entity);
    }

    public static void update(int serverNumber, Object entity) {
        DbManager.instance.getDataSource(serverNumber).update(entity);
    }

    public static void updateBatch(int serverNumber, List<?> list) {
        DbManager.instance.getDataSource(serverNumber).updateBatch(list);
    }

    public static <T> T get(int serverNumber, Class<T> clazz, Serializable key) {
        mainThreadCheck();

        return DbManager.instance.getDataSource(serverNumber).find(clazz, key);
    }

    public static void updatePlayerBatch(int serverNumber, List<?> list) {
        DbManager.instance.getDataSource(serverNumber).updateBatchThrowsException(list);
    }

    public static void updateThrowsException(int serverNumber,Object obj) {
        DbManager.instance.getDataSource(serverNumber).updateOnlyThrowsException(obj);
    }

    public static void saveThrowsException(int serverNumber, Object obj) throws Exception {
        Serializable key = DbManager.instance.getDataSource(serverNumber).saveThrowsException(obj);
        if (key != null && isCacheClass(obj.getClass())) {
            GuavaCacheUtil.put(obj.getClass().getName(), key, obj);
        }
    }

    /**
     * 根据游戏区号，获取对应MySQL数据源
     * @param serverNumber
     * @return
     */
    public DataSource getDataSource(int serverNumber){
        if(dataSources.get(serverNumber) != null){
            return dataSources.get(serverNumber);
        }

        synchronized (this){
            if(dataSources.get(serverNumber) != null){
                return dataSources.get(serverNumber);
            }
            return createDataSource(serverNumber);
        }
    }

    private static DataSource createDataSource(int id){
        try {
            DataSource dataSource = dataSources.get(id);
            if (dataSource != null) {
                return dataSource;
            }

            DataSourceInfo info = Configuration.dataSourceInfos.get(id);
            if (info != null) {
                dataSource = new DataSource();
                dataSource.init(Configuration.contextPath + "hibernate.cfg.xml", info.getUrl(), info.getUsr(), info.getPwd());
                dataSources.put(id, dataSource);
                return dataSource;
            } else {
                SystemLogger.warn("unknown datasource： " + id);
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }
}

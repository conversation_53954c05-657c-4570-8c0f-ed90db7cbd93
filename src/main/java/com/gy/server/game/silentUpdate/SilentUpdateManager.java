package com.gy.server.game.silentUpdate;

import com.google.protobuf.ByteString;
import com.gy.server.core.Configuration;
import com.gy.server.game.account.Account;
import com.gy.server.packet.PbSync;
import com.gy.server.utils.StringUtil;
import com.mchange.v2.lang.StringUtils;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.redis.key.BaseRedisKey;
import com.ttlike.server.tl.baselib.serialize.gm.SilentUpdate;

import java.util.HashSet;
import java.util.Set;

/**
 * @program: tl-game
 * @description: 静默更新管理
 * @author: <PERSON><PERSON>
 * @create: 2025/6/9
 **/
public class SilentUpdateManager {

    private static String version;

    private static byte[] updateData;

    private static PbSync.SyncData syncData;

    public static String getVersion() {
        return version;
    }

    public static void setVersion(String version) {
        SilentUpdateManager.version = version;
    }

    public static byte[] getUpdateData() {
        return updateData;
    }

    public static void setUpdateData(byte[] updateData) {
        SilentUpdateManager.updateData = updateData;
    }

    public static void init(){
        SilentUpdate silentUpdate = TLBase.getInstance().getRedisAssistant().getBean(SilentUpdate.class, BaseRedisKey.Commons.SILENT_UPDATE_INFO.getRedisKey());
        if (silentUpdate != null) {

            if(StringUtils.nonEmptyString(silentUpdate.getLiveVersion())){
                version = silentUpdate.getLiveVersion();
                updateData = TLBase.getInstance().getRedisAssistant().bytesGet(BaseRedisKey.Commons.SILENT_UPDATE_LIVE_DATA.getRedisKey());
            }

            //有测试版本，且本服在列表中
            if(StringUtils.nonEmptyString(silentUpdate.getTestVersion()) && silentUpdate.getTestServerIds().contains(Configuration.serverId)){

                if(!StringUtils.nonEmptyString(silentUpdate.getLiveVersion()) //无正式版本
                ||(Account.versionCompare(silentUpdate.getTestVersion(), silentUpdate.getLiveVersion()) > 0)){ //测试版本比正式版本高，测试版本生效

                    version = silentUpdate.getTestVersion();
                    updateData = TLBase.getInstance().getRedisAssistant().bytesGet(BaseRedisKey.Commons.SILENT_UPDATE_TEST_DATA.getRedisKey());
                }
            }
        }
    }

    public static PbSync.SyncData checkSilentUpdate(String clientVersion){
        if(StringUtils.nonEmptyString(version) && Account.versionCompare(clientVersion, version) > 0){
            return syncData;
        }
        return null;
    }

    public static void updateSyncData(){
        PbSync.SyncData.Builder builder = PbSync.SyncData.newBuilder();
        builder.setType(PbSync.SyncData.Type.silentUpdate);
        builder.setSilentUpdateSyncData(PbSync.SilentUpdateSyncData.newBuilder().setVersion(version).setData(ByteString.copyFrom(updateData)));
        syncData = builder.build();
    }
}

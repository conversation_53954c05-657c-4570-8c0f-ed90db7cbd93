package com.gy.server.game.heroShadows;

import com.gy.server.game.drop.Reward;
import com.gy.server.game.heroShadows.template.HeroShadowsAlbumLvTemplate;
import com.gy.server.game.heroShadows.template.HeroShadowsAlbumTemplate;
import com.gy.server.game.heroShadows.template.HeroShadowsSkillTemplate;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModel;
import com.gy.server.game.player.event.PlayerEvent;
import com.gy.server.game.player.event.PlayerEventHandler;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.packet.PbHeroShadows;
import com.gy.server.utils.CollectionUtil;
import com.ttlike.server.tl.baselib.serialize.heroShadows.PlayerHeroShadowsModelDb;
import com.ttlike.server.tl.baselib.serialize.player.PlayerBlobDb;

import java.util.*;

/**
 * 将魂模块
 *
 * <AUTHOR> 2025/4/18 16:52
 **/
public class PlayerHeroShadowsModel extends PlayerModel implements PlayerEventHandler {

    /**
     * key:章节id     value:侠影值
     */
    private Map<Integer, Integer> shadowNumMap = new HashMap<>();

    /**
     * 加成等级map
     * key:album页签id    value:等级
     */
    private Map<Integer, Integer> albumMap = new HashMap<>();

    /**
     * 技能等级map
     * key:章节id    value:技能等级
     */
    private Map<Integer, Integer> skillLevelMap = new HashMap<>();

    /**
     * 章节重置时间map
     * key:章节   value:可重置时间
     */
    private Map<Integer, Long> cdMap = new HashMap<>();

    public PlayerHeroShadowsModel(Player player) {
        super(player);
    }

    /**
     * 章节重置
     * @param id
     */
    public List<Reward> resetChapter(int id){
        List<Reward> list = new ArrayList<>();
        resetChapter(list, id);

        //检查其他章节是否还够解锁数值
        List<Integer> temp = new ArrayList<>(HeroShadowsService.getChapterMap().keySet());
        temp.sort(((o1, o2) -> o2 - o1));
        for (int chapterId : temp) {
            Set<Integer> setTemp = HeroShadowsService.getAlbumIdSetByChapterId(chapterId);
            setTemp.retainAll(albumMap.keySet());

            if(!skillLevelMap.containsKey(id) && setTemp.isEmpty()){
                continue;
            }
            resetChapter(list, id);
        }

        return list;
    }

    /**
     * 章节重置
     * @param id
     * @return
     */
    public void resetChapter(List<Reward> list, int id) {
        //羁绊
        Set<Integer> albumIdSet = HeroShadowsService.getAlbumIdSetByChapterId(id);
        for (int albumId : albumIdSet) {
            if(albumMap.containsKey(albumId)){
                int level = albumMap.get(albumId);

                Map<Integer, HeroShadowsAlbumLvTemplate> albumLvMap = HeroShadowsService.getAlbumLvMap().get(albumId);
                if(CollectionUtil.isNotEmpty(albumLvMap)){
                    for (HeroShadowsAlbumLvTemplate bean : albumLvMap.values()) {
                        if(bean.level <= level){
                            list.addAll(Reward.templateCollectionToReward(bean.cost));
                        }
                    }
                }

                albumMap.remove(albumId);
            }
        }

        //减少侠影值
        shadowNumMap.remove(id);

        //技能
        if(skillLevelMap.containsKey(id)){
            int level = skillLevelMap.get(id);
            Map<Integer, HeroShadowsSkillTemplate> map = HeroShadowsService.getSkillMap().get(id);
            for (HeroShadowsSkillTemplate bean : map.values()) {
                if(level >= bean.level){
                    List<Reward> temp = Reward.templateCollectionToReward(bean.cost);
                    list.addAll(temp);
                }
            }

            skillLevelMap.remove(id);
        }
    }



    /**
     * gm调整羁绊等级
     * @param newLevel
     * @param max 是否调整到最大
     */
    public void gmAlbumLevelUp(int newLevel, boolean max){
        if(newLevel <= 0 && !max){
            return;
        }

        for (HeroShadowsAlbumTemplate bean : HeroShadowsService.getAlbumMap().values()) {
            if(max || newLevel > bean.maxLv){
                newLevel = bean.maxLv;
            }

            int oldLevel = albumMap.getOrDefault(bean.id, 0);
            if(newLevel > oldLevel){
                albumMap.put(bean.id, newLevel);
            }
        }

        getPlayer().postEvent(PlayerEventType.heroShadowsAlbumUp);
    }

    /**
     * gm调整技能等级
     * @param newLevel
     * @param max 是否调整到最大
     */
    public void gmSkillLevelUp(int newLevel, boolean max){
        if(newLevel <= 0 && !max){
            return;
        }

        for (Map.Entry<Integer, Map<Integer, HeroShadowsSkillTemplate>> entry : HeroShadowsService.getSkillMap().entrySet()) {
            int maxLevel = entry.getValue().keySet().stream().max(Integer::compareTo).get();
            if(max || newLevel > maxLevel){
                newLevel = maxLevel;
            }

            int oldLevel = skillLevelMap.getOrDefault(entry.getKey(), 0);
            if(newLevel > oldLevel){
                skillLevelMap.put(entry.getKey(), newLevel);
            }
        }

        getPlayer().postEvent(PlayerEventType.heroShadowsSkillUp);
    }

    public PbHeroShadows.HeroShadowsModel genPb() {
        PbHeroShadows.HeroShadowsModel.Builder builder = PbHeroShadows.HeroShadowsModel.newBuilder();
        builder.setShadowNum(getAllShowShadows());
        builder.putAllAlbum(albumMap);
        builder.putAllSkillLevelMap(skillLevelMap);
        builder.putAllCdMap(cdMap);
        return builder.build();
    }

    @Override
    protected void loadData(PlayerBlobDb playerBlob) {
        PlayerHeroShadowsModelDb db = playerBlob.getHeroShadowsModelDb();
        if(Objects.nonNull(db)){
            this.shadowNumMap.putAll(db.getShadowNumMap());
            this.albumMap.putAll(db.getAlbumMap());
            this.skillLevelMap.putAll(db.getSkillLevelMap());
            this.cdMap.putAll(db.getCdMap());
        }
    }

    @Override
    protected void saveData(PlayerBlobDb playerBlob) {
        PlayerHeroShadowsModelDb db = new PlayerHeroShadowsModelDb(shadowNumMap, albumMap, skillLevelMap, cdMap);
        playerBlob.setHeroShadowsModelDb(db);
    }

    @Override
    public PlayerEventType[] getEventTypes() {
        return new PlayerEventType[]{};
    }

    @Override
    public void handle(PlayerEvent event) {

    }

    public int getAllShowShadows(){
        return shadowNumMap.values().stream().reduce(Integer::sum).orElse(0);
    }

    public Map<Integer, Integer> getShadowNumMap() {
        return shadowNumMap;
    }

    public void setShadowNumMap(Map<Integer, Integer> shadowNumMap) {
        this.shadowNumMap = shadowNumMap;
    }

    public Map<Integer, Integer> getAlbumMap() {
        return albumMap;
    }

    public void setAlbumMap(Map<Integer, Integer> albumMap) {
        this.albumMap = albumMap;
    }

    public Map<Integer, Integer> getSkillLevelMap() {
        return skillLevelMap;
    }

    public void setSkillLevelMap(Map<Integer, Integer> skillLevelMap) {
        this.skillLevelMap = skillLevelMap;
    }

    public Map<Integer, Long> getCdMap() {
        return cdMap;
    }

    public void setCdMap(Map<Integer, Long> cdMap) {
        this.cdMap = cdMap;
    }
}

package com.gy.server.game.heroShadows.template;

import com.gy.server.game.util.StringExtUtil;

import java.util.*;

public class HeroShadowsConstTemplate {

    /**
    * 章节与切换方案重置冷却时间 分钟
    */
    public int resetCD;

    public Map<Integer, Integer> autoItemMap = new HashMap<>();

    public HeroShadowsConstTemplate(Map<String, String> map) {
        resetCD = Integer.parseInt(map.get("resetCD"));
        autoItemMap = StringExtUtil.string2Map(map.get("autoItem"), ",", "|", Integer.class, Integer.class);
    }

    @Override
    public String toString() {
        return "HeroShadowsConstTemplate{"
                + "resetCD=" + resetCD
                + "autoItem=" + autoItemMap
                + '}';
    }
}

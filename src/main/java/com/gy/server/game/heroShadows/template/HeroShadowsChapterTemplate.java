package com.gy.server.game.heroShadows.template;

import java.util.Map;

public class HeroShadowsChapterTemplate {

    /**
    * ID
    */
    public int id; 

    /**
    * 章节顺序
    */
    public int order; 

    /**
    * 解锁需要侠影值
    */
    public int chapterShadowValue;

    /**
     * 解锁条件
     */
    public String unlockType;

    public HeroShadowsChapterTemplate(Map<String, String> map) {
        id = Integer.parseInt(map.get("id"));
        order = Integer.parseInt(map.get("order"));
        chapterShadowValue = Integer.parseInt(map.get("chapterShadowValue"));
        unlockType = map.get("unlockType");
    }

    @Override
    public String toString() {
        return "HeroShadowsChapterTemplate{"
                + "id=" + id
                + "order=" + order
                + "chapterShadowValue=" + chapterShadowValue
                + "unlockType=" + unlockType
                + '}';
    }
}

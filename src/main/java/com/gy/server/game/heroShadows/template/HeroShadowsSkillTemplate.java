package com.gy.server.game.heroShadows.template;

import com.gy.server.game.drop.RewardTemplate;
import com.ttlike.server.tl.baselib.serialize.combatAddition.CombatAdditions;

import java.util.List;
import java.util.Map;

public class HeroShadowsSkillTemplate {

    /**
    * ID
    */
    public int id;

    /**
    * 等级
    */
    public int level;

    /**
    * 解锁条件 侠影值
    */
    public int skillShadowValue;

    /**
    * 所需材料数量
    */
    public List<RewardTemplate> cost;

    /**
    * 技能效果
    */
    public CombatAdditions skillEffect;

    /**
    * 所属章节
    */
    public int chapter;

    public HeroShadowsSkillTemplate(Map<String, String> map) {
        id = Integer.parseInt(map.get("id"));
        level = Integer.parseInt(map.get("leve"));
        skillShadowValue = Integer.parseInt(map.get("skillShadowValue"));
        cost = RewardTemplate.readListFromText(map.get("cost"));
        skillEffect = CombatAdditions.readFromStr(map.get("skillEffect"));
        chapter = Integer.parseInt(map.get("chapter"));
    }

    @Override
    public String toString() {
        return "HeroShadowsSkillTemplate{"
                + "id=" + id
                + "leve=" + level
                + "skillShadowValue=" + skillShadowValue
                + "cost=" + cost
                + "skillEffect=" + skillEffect
                + "chapter=" + chapter
                + '}';
    }
}

package com.gy.server.game.heroShadows;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.common.ConstantConfigReader;
import com.gy.server.game.cond.CondManager;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.function.Function;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.heroShadows.template.*;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.service.Service;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.time.DateTimeUtil;

import java.util.*;

/**
 * 将魂服务类
 *
 * <AUTHOR> 2025/4/19 14:17
 **/
public class HeroShadowsService extends PlayerPacketHandler implements Service {

    /**
     * 章节map
     * key:章节顺序
     */
    private static Map<Integer, HeroShadowsChapterTemplate> chapterMap = new HashMap<>();

    /**
     * 加成map
     * key:album页签id
     */
    private static Map<Integer, HeroShadowsAlbumTemplate> albumMap = new HashMap<>();

    /**
     * album页签id升级map
     * key:album页签id    value: key:等级
     */
    private static Map<Integer, Map<Integer, HeroShadowsAlbumLvTemplate>> albumLvMap = new HashMap<>();

    /**
     * 影集最高等级map
     */
    private static Map<Integer, Integer> maxLevelMap = new HashMap<>();

    /**
     * 技能map
     * key:章节id     value:key:等级
     */
    private static Map<Integer, Map<Integer, HeroShadowsSkillTemplate>> skillMap = new HashMap<>();

    private static HeroShadowsConstTemplate constTemplate;

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.heroShadows_chapter);
        Map<Integer, HeroShadowsChapterTemplate> chapterMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            HeroShadowsChapterTemplate template = new HeroShadowsChapterTemplate(map);
            chapterMapTemp.put(template.order, template);
        }
        chapterMap = chapterMapTemp;

        mapList = ConfigReader.read(ConfigFile.heroShadows_album);
        Map<Integer, HeroShadowsAlbumTemplate> albumMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            HeroShadowsAlbumTemplate template = new HeroShadowsAlbumTemplate(map);
            albumMapTemp.put(template.id, template);
        }
        albumMap = albumMapTemp;

        mapList = ConfigReader.read(ConfigFile.heroShadows_albumLv);
        Map<Integer, Map<Integer, HeroShadowsAlbumLvTemplate>> albumLvMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            HeroShadowsAlbumLvTemplate template = new HeroShadowsAlbumLvTemplate(map);
            albumLvMapTemp.computeIfAbsent(template.albumGroup, temp1 -> new HashMap<>()).put(template.level, template);
        }
        albumLvMap = albumLvMapTemp;

        for (Map.Entry<Integer, Map<Integer, HeroShadowsAlbumLvTemplate>> entry : albumLvMap.entrySet()) {
            maxLevelMap.put(entry.getKey(), entry.getValue().keySet().stream().max(Integer::compare).get());
        }

        mapList = ConfigReader.read(ConfigFile.heroShadows_skill);
        Map<Integer, Map<Integer, HeroShadowsSkillTemplate>> skillMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            HeroShadowsSkillTemplate template = new HeroShadowsSkillTemplate(map);
            skillMapTemp.computeIfAbsent(template.chapter, bean -> new HashMap<>()).put(template.level, template);
        }
        skillMap = skillMapTemp;

        Map<String, String> map = ConstantConfigReader.read(ConfigFile.heroShadows_const);
        constTemplate = new HeroShadowsConstTemplate(map);
    }

    @Override
    public void clearConfigData() {
        chapterMap.clear();
        albumMap.clear();
        albumLvMap.clear();
        maxLevelMap.clear();
        skillMap.clear();
        constTemplate = null;
    }

    /**
     * 将魂信息
     */
    @Handler(PtCode.HERO_SHADOWS_INFO_REQ)
    private void heroShadowsInfoReq(Player player, PbProtocol.HeroShadowsInfoReq req, long time){
        PbProtocol.HeroShadowsInfoRst.Builder rst = PbProtocol.HeroShadowsInfoRst.newBuilder().setResult(Text.genOkServerRstInfo());

        logic:{
            if(Function.heroShadows.isNotOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
        }

        PlayerHeroShadowsModel model = player.getPlayerHeroShadowsModel();
        rst.setHeroShadows(model.genPb());
        player.send(PtCode.HERO_SHADOWS_INFO_RST, rst.build(), time);
    }

    /**
     * 将魂羁绊升级
     */
    @Handler(PtCode.HERO_SHADOWS_ALBUM_UP_REQ)
    private void heroShadowsAlbumUpReq(Player player, PbProtocol.HeroShadowsAlbumUpReq req, long time){
        PbProtocol.HeroShadowsAlbumUpRst.Builder rst = PbProtocol.HeroShadowsAlbumUpRst.newBuilder().setResult(Text.genOkServerRstInfo());
        List<Integer> idList = req.getIdList();

        logic:{
            if(Function.heroShadows.isNotOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }

            if(idList.isEmpty()){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }

            List<Integer> successIdList = new ArrayList<>();
            PlayerHeroShadowsModel model = player.getPlayerHeroShadowsModel();
            for (int id : idList) {
                HeroShadowsAlbumTemplate albumTemplate = albumMap.get(id);
                if(albumTemplate == null){
                    rst.setResult(Text.genServerRstInfo(Text.参数异常));
                    rst.addAllId(successIdList);
                    break logic;
                }

                HeroShadowsChapterTemplate chapterTemplate = chapterMap.get(albumTemplate.chapter);
                if(model.getAllShowShadows() < chapterTemplate.chapterShadowValue && CondManager.checkCond(player, chapterTemplate.unlockType)){
                    rst.setResult(Text.genServerRstInfo(Text.将魂章节未解锁));
                    rst.addAllId(successIdList);
                    break logic;
                }

                int albumLevel = model.getAlbumMap().getOrDefault(id, 0);
                if(albumLevel >= maxLevelMap.getOrDefault(id, 0)){
                    rst.setResult(Text.genServerRstInfo(Text.将魂羁绊已满级));
                    rst.addAllId(successIdList);
                    break logic;
                }

                int newAlbumLevel = albumLevel + 1;
                HeroShadowsAlbumLvTemplate albumLvTemplate = albumLvMap.getOrDefault(id, new HashMap<>()).get(newAlbumLevel);
                if(albumLvTemplate == null){
                    rst.setResult(Text.genServerRstInfo(Text.参数异常));
                    rst.addAllId(successIdList);
                    break logic;
                }

                List<Reward> cost = RewardTemplate.createRewards(albumLvTemplate.cost);
                if(Reward.check(player, cost) != -1){
                    rst.setResult(Text.genServerRstInfo(Text.消耗不足));
                    rst.addAllId(successIdList);
                    break logic;
                }
                Reward.remove(cost, player, BehaviorType.heroShadowsAlbumUp);

                int newShadowNum = model.getShadowNumMap().getOrDefault(chapterTemplate.id, 0) + albumLvTemplate.shadow;
                model.getShadowNumMap().put(chapterTemplate.id, newShadowNum);
                model.getAlbumMap().put(id, newAlbumLevel);
                successIdList.add(id);
            }

            if(!successIdList.isEmpty()){
                player.dataSyncModule.syncHeroShadows();
                player.postEvent(PlayerEventType.heroShadowsAlbumUp);
                rst.addAllId(successIdList);
            }
        }

        player.send(PtCode.HERO_SHADOWS_ALBUM_UP_RST, rst.build(), time);
    }

    /**
     * 将魂技能升级
     */
    @Handler(PtCode.HERO_SHADOWS_SKILL_UP_REQ)
    private void heroShadowsSkillUpReq(Player player, PbProtocol.HeroShadowsSkillUpReq req, long time){
        PbProtocol.HeroShadowsSkillUpRst.Builder rst = PbProtocol.HeroShadowsSkillUpRst.newBuilder().setResult(Text.genOkServerRstInfo());
        int id = req.getId();

        logic:{
            if(Function.heroShadows.isNotOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }

            HeroShadowsChapterTemplate chapterTemplate = chapterMap.get(id);
            if(chapterTemplate == null){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }

            PlayerHeroShadowsModel model = player.getPlayerHeroShadowsModel();
            if(model.getAllShowShadows() < chapterTemplate.chapterShadowValue){
                rst.setResult(Text.genServerRstInfo(Text.将魂章节未解锁));
                break logic;
            }

            Map<Integer, HeroShadowsSkillTemplate> skillLevelMap = skillMap.get(id);
            if(CollectionUtil.isEmpty(skillLevelMap)){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }

            int skillLevel = model.getSkillLevelMap().getOrDefault(id, 0);
            int maxSkillLevel = skillLevelMap.keySet().stream().max(Integer::compare).get();
            if(skillLevel >= maxSkillLevel){
                rst.setResult(Text.genServerRstInfo(Text.将魂章节技能已满级));
                break logic;
            }

            int nextSkillLevel = skillLevel + 1;
            HeroShadowsSkillTemplate skillTemplate = skillLevelMap.get(nextSkillLevel);
            if(model.getShadowNumMap().getOrDefault(id, 0) < skillTemplate.skillShadowValue){
                rst.setResult(Text.genServerRstInfo(Text.本章节侠影值不足));
                break logic;
            }

            List<Reward> cost = RewardTemplate.createRewards(skillTemplate.cost);
            if(Reward.check(player, cost) != -1){
                rst.setResult(Text.genServerRstInfo(Text.消耗不足));
                break logic;
            }
            Reward.remove(cost, player, BehaviorType.heroShadowsSkillUp);

            model.getSkillLevelMap().put(id, nextSkillLevel);
            player.dataSyncModule.syncHeroShadows();
            player.postEvent(PlayerEventType.heroShadowsSkillUp);
        }

        player.send(PtCode.HERO_SHADOWS_SKILL_UP_RST, rst.build(), time);
    }

    /**
     * 将魂章节重置
     */
    @Handler(PtCode.HERO_SHADOWS_CHAPTER_RESET_REQ)
    private void heroShadowsChapterResetReq(Player player, PbProtocol.HeroShadowsChapterResetReq req, long time){
        PbProtocol.HeroShadowsChapterResetRst.Builder rst = PbProtocol.HeroShadowsChapterResetRst.newBuilder().setResult(Text.genOkServerRstInfo());
        int id = req.getId();

        logic:{
            if(Function.heroShadows.isNotOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }

            HeroShadowsChapterTemplate chapterTemplate = chapterMap.get(id);
            if(chapterTemplate == null){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }

            PlayerHeroShadowsModel model = player.getPlayerHeroShadowsModel();
            if(model.getAllShowShadows() < chapterTemplate.chapterShadowValue){
                rst.setResult(Text.genServerRstInfo(Text.将魂章节未解锁));
                break logic;
            }

            long now = ServerConstants.getCurrentTimeMillis();
            long cdTime = model.getCdMap().getOrDefault(id, 0l);
            if(now < cdTime){
                rst.setResult(Text.genServerRstInfo(Text.将魂章节重置冷却中));
                break logic;
            }

            Set<Integer> albumIdSet = getAlbumIdSetByChapterId(id);
            albumIdSet.retainAll(model.getAlbumMap().keySet());

            if(!model.getSkillLevelMap().containsKey(id) && albumIdSet.isEmpty()){
                rst.setResult(Text.genServerRstInfo(Text.将魂此章节无养成));
                break logic;
            }

            long temp = now + constTemplate.resetCD * DateTimeUtil.MillisOfMinute;
            model.getCdMap().put(id, temp);

            //重置当前章节的所有节点和技能，并返还材料
            //如果重置后条件不满足解锁后续章节的条件，则会将后续章节全部一同重置
            List<Reward> list = model.resetChapter(id);
            Reward.add(list, player, BehaviorType.heroShadowsChapterReset);
            rst.addAllReward(Reward.writeCollectionToPb(list));

            player.dataSyncModule.syncHeroShadows();
            player.postEvent(PlayerEventType.heroShadowsChapterReset);
        }

        player.send(PtCode.HERO_SHADOWS_CHAPTER_RESET_RST, rst.build(), time);
    }

    public static Set<Integer> getAlbumIdSetByChapterId(int chapterId){
        Set<Integer> albumIdSet = new HashSet<>();
        for (HeroShadowsAlbumTemplate bean : albumMap.values()) {
            if(bean.chapter == chapterId){
                albumIdSet.add(bean.id);
            }
        }
        return albumIdSet;
    }

    public static Map<Integer, HeroShadowsChapterTemplate> getChapterMap() {
        return chapterMap;
    }

    public static Map<Integer, HeroShadowsAlbumTemplate> getAlbumMap() {
        return albumMap;
    }

    public static Map<Integer, Map<Integer, HeroShadowsAlbumLvTemplate>> getAlbumLvMap() {
        return albumLvMap;
    }

    public static Map<Integer, Map<Integer, HeroShadowsSkillTemplate>> getSkillMap() {
        return skillMap;
    }

    public static HeroShadowsConstTemplate getConstTemplate() {
        return constTemplate;
    }
}

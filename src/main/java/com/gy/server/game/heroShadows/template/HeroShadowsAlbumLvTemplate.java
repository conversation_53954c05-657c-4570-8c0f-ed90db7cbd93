package com.gy.server.game.heroShadows.template;

import java.util.*;

import com.gy.server.game.drop.RewardTemplate;
import com.ttlike.server.tl.baselib.serialize.combatAddition.CombatAdditions;

public class HeroShadowsAlbumLvTemplate {

    /**
     * ID
     */
    public int id;

    /**
     * 等级
     */
    public int level;

    /**
     * 所需侠影道具
     */
    public List<RewardTemplate> cost;

    /**
     * 属性加成
     */
    public CombatAdditions combatAddition;

    /**
     * 侠影值加成
     */
    public int shadow;

    /**
     * 影集id
     */
    public int albumGroup;

    public HeroShadowsAlbumLvTemplate(Map<String, String> map) {
        id = Integer.parseInt(map.get("id"));
        level = Integer.parseInt(map.get("level"));
        cost = RewardTemplate.readListFromText(map.get("cost"));
        combatAddition = CombatAdditions.readFromStr(map.get("combatAddition"));
        shadow = Integer.parseInt(map.get("shadow"));
        albumGroup = Integer.parseInt(map.get("albumGroup"));
    }

    @Override
    public String toString() {
        return "HeroShadowsAlbumLvTemplate{"
                + "id=" + id
                + "level=" + level
                + "cost=" + cost
                + "combatAddition=" + combatAddition
                + "shadow=" + shadow
                + "albumGroup=" + albumGroup
                + '}';
    }
}

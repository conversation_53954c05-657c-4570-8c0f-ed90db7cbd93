package com.gy.server.game.heroShadows.template;


import java.util.Map;

public class HeroShadowsAlbumTemplate {

    /**
    * ID
    */
    public int id;

    /**
    * 星级上限
    */
    public int maxLv;

    /**
    * 所属章节
    */
    public int chapter;

    public HeroShadowsAlbumTemplate(Map<String, String> map) {
        id = Integer.parseInt(map.get("id"));
        maxLv = Integer.parseInt(map.get("maxLv"));
        chapter = Integer.parseInt(map.get("chapter"));
    }

    @Override
    public String toString() {
        return "HeroShadowsAlbumTemplate{"
                + "id=" + id
                + "maxLv=" + maxLv
                + "chapter=" + chapter
                + '}';
    }
}

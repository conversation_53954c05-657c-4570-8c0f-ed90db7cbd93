package com.gy.server.game.mountainRiverTournament.reward.impl;

import com.gy.server.game.drop.Reward;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.mountainRiverTournament.PlayerMRTModel;
import com.gy.server.game.mountainRiverTournament.reward.IRewardCheck;
import com.gy.server.game.mountainRiverTournament.template.MRTTaskRewardTemplate;
import com.gy.server.game.player.Player;
import com.gy.server.packet.PbCommons;

import java.util.List;

/**
 * 参与次数
 * @author: gbk
 * @date: 2024-11-27 10:13
 */
public class BattleTimesRewardCheck  implements IRewardCheck<MRTTaskRewardTemplate, PlayerMRTModel> {

    @Override
    public boolean canReceive(MRTTaskRewardTemplate mrtTaskRewardTemplate, PlayerMRTModel model) {
        return Integer.parseInt(mrtTaskRewardTemplate.rewardCondition) <= model.getTodayBattleTimes();
    }

    @Override
    public List<PbCommons.PbReward> addToPlayer(Player player, MRTTaskRewardTemplate mrtTaskRewardTemplate, BehaviorType behaviorType) {
        return Reward.writeCollectionToPb(Reward.addFromTemplates(mrtTaskRewardTemplate.rewardGroup, player, behaviorType));
    }

    @Override
    public List<Reward> getReward(MRTTaskRewardTemplate mrtTaskRewardTemplate) {
        return Reward.templateCollectionToReward(mrtTaskRewardTemplate.rewardGroup);
    }
}
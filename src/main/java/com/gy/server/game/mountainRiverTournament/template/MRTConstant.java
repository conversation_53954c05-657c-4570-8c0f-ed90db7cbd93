package com.gy.server.game.mountainRiverTournament.template;

import com.gy.server.common.base.AbsTemplate;
import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.constant.ConstantService;
import com.gy.server.game.constant.ConstantType;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.mountainRiverTournament.bean.MRTPlayEnums;
import com.gy.server.utils.StringUtil;

import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.List;
import java.util.Map;

/**
 * 常量配置
 * @author: gbk
 * @date: 2024-11-11 17:17
 */
public class MRTConstant implements AbsTemplate {

    //开启时间
    public String openTime;
    //先开玩法类型
    public MRTPlayEnums openType;
    //结束玩法类型
    public MRTPlayEnums endType;
    //当段位到达最高后（段位ID），开始获得竞技点
    public int startToAcquire;
    //初始默认积分
    public int initialIntegral;
    //匹配范围每次扩大30分
    public int magnificationNumber;
    //匹配范围扩大上限：100分
    public int extendTheCeiling;

    //赛期持续多久
    public int duration;
    public int seasonDay;
    //初始权重
    public int weight;
    //每日段位奖励结算时间
    public int DailyBonusTime;
    //段位排行榜展示人数上限
    public int segmentLeaderboardNum;
    //积分排行榜展示人数上限
    public int leaderboardNum;
    //竞技风采录奖励
    public List<RewardTemplate> paperRewards;
    //x局内输y局，下局必定匹配人机
    public int  integralProtection1_1;
    public int  integralProtection1_2;
    //连输x局，下局必定匹配人机
    public int integralProtection2;
    //战斗场景
    public int scene6v6;
    public int scene3v3;




    @Override
    public void readTxt(Map<String, String> map) {
        if("openingTime".equals(map.get("key"))){
            this.openTime = map.get("value");
        }else if("openType".equals(map.get("key"))){
            int[] openTypes = StringUtil.splitToIntArray(map.get("value"), ",");
            this.openType = MRTPlayEnums.getPlayByType(openTypes[0]);
            this.endType = MRTPlayEnums.getPlayByType(openTypes[1]);
        }else if("startToAcquire".equals(map.get("key"))){
            this.startToAcquire = Integer.parseInt(map.get("value"));
        }else if("initialIntegral".equals(map.get("key"))){
            this.initialIntegral = Integer.parseInt(map.get("value"));
        }else if("magnificationNumber".equals(map.get("key"))){
            this.magnificationNumber = Integer.parseInt(map.get("value"));
        }else if("extendTheCeiling".equals(map.get("key"))){
            this.extendTheCeiling = Integer.parseInt(map.get("value"));
        }else if("DailyBonusTime".equals(map.get("key"))){
            this.DailyBonusTime = Integer.parseInt(map.get("value"));
        }else if("weight".equals(map.get("key"))){
            this.weight = Integer.parseInt(map.get("value"));
        }else if("duration".equals(map.get("key"))){
            this.duration = Integer.parseInt(map.get("value"));
        }else if("segmentLeaderboardNum".equals(map.get("key"))){
            this.segmentLeaderboardNum = Integer.parseInt(map.get("value"));
        }else if("leaderboardNum".equals(map.get("key"))){
            this.leaderboardNum = Integer.parseInt(map.get("value"));
        }else if("paperRewards".equals(map.get("key"))){
            this.paperRewards = RewardTemplate.readListFromText(map.get("value"));
        }else if("integralProtection1".equals(map.get("key"))){
            int[] ints = StringUtil.splitToIntArray(map.get("value"), "\\|");
            this.integralProtection1_1 = ints[0];
            this.integralProtection1_2 = ints[1];
        }else if("integralProtection2".equals(map.get("key"))){
            this.integralProtection2 = Integer.parseInt(map.get("value"));
        }else if("6v6scene".equals(map.get("key"))){
            this.scene6v6 = Integer.parseInt(map.get("value"));
        }else if("3v3scene".equals(map.get("key"))){
            this.scene3v3 = Integer.parseInt(map.get("value"));
        }else if("seasonDay".equals(map.get("key"))){
            this.seasonDay = Integer.parseInt(map.get("value"));
        }
    }

    public LocalDateTime getFirstOpenTime(){
        int[] openTimes = StringUtil.splitToIntArray(openTime,"\\|");
        return ServerConstants.getCurrentTimeLocalDateTime()
                .withYear(openTimes[0])
                .withMonth(openTimes[1])
                .withDayOfMonth(openTimes[2])
                .withHour(CommonUtils.getRefreshTimeHour())
                .withMinute(0).withSecond(0).withNano(0);
    }

    public long getNowWeekTestOpenTime(){
        Calendar calendar = Calendar.getInstance();
        // 将calendar的时间设置为本周的第一天（默认为星期日）
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        calendar.set(Calendar.HOUR_OF_DAY, 6);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        // 获取本周周一的日期
        return  calendar.getTime().getTime();
    }


}
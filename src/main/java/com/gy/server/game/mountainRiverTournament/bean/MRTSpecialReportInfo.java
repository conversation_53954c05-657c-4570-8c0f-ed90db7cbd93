package com.gy.server.game.mountainRiverTournament.bean;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.packet.PbLeague;

/**
 * 事件信息
 * @author: gbk
 * @date: 2024-11-22 18:03
 */
@ProtobufClass
public class MRTSpecialReportInfo {

    @Protobuf(order = 1)
    private int id;
    @Protobuf(order = 2)
    private MiniGamePlayer miniPlayer;
    @Protobuf(order = 3)
    private String showContent;
    @Protobuf(order = 4)
    private long recordId;
    @Protobuf(order = 5)
    private int recordType;
    @Protobuf(order = 6)
    private int serverId;
    @Protobuf(order = 7)
    private int combatTimes;

    public PbLeague.MRTSpecialReportInfo genPb(){
        PbLeague.MRTSpecialReportInfo.Builder builder = PbLeague.MRTSpecialReportInfo.newBuilder();
        builder.setId(id);
        builder.setMiniUser(miniPlayer.genMinMiniUser());
        builder.setShowContent(showContent);
        builder.setRecordId(recordId);
        builder.setRecordType(recordType);
        builder.setServerId(serverId);
        return builder.build();
    }

    public int getCombatTimes() {
        return combatTimes;
    }

    public void setCombatTimes(int combatTimes) {
        this.combatTimes = combatTimes;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public MiniGamePlayer getMiniPlayer() {
        return miniPlayer;
    }

    public void setMiniPlayer(MiniGamePlayer miniPlayer) {
        this.miniPlayer = miniPlayer;
    }

    public String getShowContent() {
        return showContent;
    }

    public void setShowContent(String showContent) {
        this.showContent = showContent;
    }

    public long getRecordId() {
        return recordId;
    }

    public void setRecordId(long recordId) {
        this.recordId = recordId;
    }

    public int getRecordType() {
        return recordType;
    }

    public void setRecordType(int recordType) {
        this.recordType = recordType;
    }

    public int getServerId() {
        return serverId;
    }

    public void setServerId(int serverId) {
        this.serverId = serverId;
    }
}
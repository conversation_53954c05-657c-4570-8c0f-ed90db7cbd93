package com.gy.server.game.mountainRiverTournament;

import com.gy.server.common.redis.bean.RedisIntegerBean;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.Configuration;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.mountainRiverTournament.async.MRTEnterSpecialReportAsync;
import com.gy.server.game.mountainRiverTournament.template.MRTConstant;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.serialize.RedisAssistant;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.Objects;

/**
 * 剑会群雄-逸侠对决 辅助类
 * @author: gbk
 * @date: 2024-11-21 17:52
 */
public class MRTHelper {


    /**
     * 减少玩家随机权重（world服处理）
     */
    public static void reducePlayerMatchWeight(long playerId){
        if(Configuration.isGameServer()){
            notifyMasterWorld("WorldMRTCommandService.reducePlayerMatchWeight", playerId);
        }else{
            String redisKey = GsRedisKey.MountainRiverTournament.match_weight.getRedisKey();
            RedisAssistant ra = TLBase.getInstance().getRedisAssistant();
            RedisIntegerBean matchWeightBean = ra.hashGetBeans(redisKey, playerId + "", RedisIntegerBean.class);
            MRTConstant constant = MountainRiverTournamentService.getConstant();
            int curMatchWeight = Objects.isNull(matchWeightBean) ? constant.weight : matchWeightBean.getParam();
            ra.hashPut(redisKey, playerId + "", new RedisIntegerBean(Math.max(1, curMatchWeight - 1)));

        }
    }

    /**
     * 减少玩家积分（world服处理）
     */
    public static void modifyPlayerPoint(int warZoneId, long playerId, int modifyPoint){
        if(Configuration.isGameServer()){
            notifyMasterWorld("WorldMRTCommandService.modifyPlayerPoint", warZoneId,playerId, modifyPoint);
        }else{
            RedisAssistant ra = TLBase.getInstance().getRedisAssistant();
            String redisKey = GsRedisKey.MountainRiverTournament.level_rank.getRedisKey(warZoneId);
            double point = ra.rankScore(redisKey, playerId + "");
            ra.rankPut(redisKey, playerId + "", point + modifyPoint);
        }
    }

    /**
     * 尝试进入
     * @param warZoneId
     * @param playerId
     * @param srId
     * @param param
     */
    public static void tryEnterSpecialReport(int warZoneId, long playerId, long recordId, int srId, int serverId, int recordType, Object... param){
        if(Configuration.isGameServer()){
            notifyMasterWorld("WorldMRTCommandService.tryEnterSpecialReport", warZoneId,playerId, recordId, srId, serverId, recordType, param);
        }else{
            ThreadPool.execute(new MRTEnterSpecialReportAsync(warZoneId, playerId, recordId, srId, serverId, recordType, param));
        }
    }

    /**
     * 广播给主世界服消息
     */
    public static void notifyMasterWorld(String method, Object... param){
        int masterWorldId = CommonUtils.getWorldMasterServerId();
        notifyWorld(masterWorldId, method, param);
    }

    public static void notifyWorld(int worldId, String method, Object... param){
        ServerCommandRequest request = CommandRequests.newServerCommandRequest(method);
        TLBase.getInstance().getRpcUtil().sendToNode(ServerType.WORLD, worldId, request, param);
    }



}
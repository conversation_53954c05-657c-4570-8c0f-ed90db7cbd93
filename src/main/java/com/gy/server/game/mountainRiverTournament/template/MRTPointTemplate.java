package com.gy.server.game.mountainRiverTournament.template;

import com.gy.server.common.base.AbsTemplate;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.utils.StringUtil;

import java.util.List;
import java.util.Map;

/**
 * 积分信息
 * @author: gbk
 * @date: 2024-11-22 11:30
 */
public class MRTPointTemplate implements AbsTemplate {

    //段位id
    public int id;
    //所需积分
    public int rankCount;
    //段位奖励
    public List<RewardTemplate> rewardGroup;
    public List<RewardTemplate> settlementGroup;
    //胜利积分
    public int winCount;
    //失败积分
    public int loseCount;
    //匹配范围
    public String range;
    public double minRange;
    public double maxRange;
    //下段位
    public int Nextrank;
    //匹配双方相差x分后，胜利方不获得积分
    public int victoryProtection;
    //是否启用积分保护
    public boolean IntegralProtection;


    @Override
    public void readTxt(Map<String, String> map) {
        this.id = Integer.parseInt(map.get("rankid"));
        this.rankCount = Integer.parseInt(map.get("rankCount"));
        this.rewardGroup = RewardTemplate.readListFromText(map.get("rewardGroup"));
        this.winCount = Integer.parseInt(map.get("winCount"));
        this.loseCount = Integer.parseInt(map.get("loseCount"));
        this.range = map.get("range");
        int[] ints = StringUtil.splitToIntArray(range, "\\|");
        this.minRange = ints[0];
        this.maxRange = ints[1];
        this.Nextrank= Integer.parseInt(map.get("Nextrank"));
        this.victoryProtection= Integer.parseInt(map.get("victoryProtection"));
        this.IntegralProtection= Integer.parseInt(map.get("IntegralProtection")) == 1;
    }
}
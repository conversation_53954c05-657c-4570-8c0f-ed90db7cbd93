package com.gy.server.game.mountainRiverTournament.async;

import com.gy.server.common.redis.bean.RedisIntegerBean;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.common.util.CommonUtils;
import com.gy.server.db.nosql.redis.GyRedisTop;
import com.gy.server.game.combat.CombatManager;
import com.gy.server.game.combat.StageType;
import com.gy.server.game.lineup.LineupHelper;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.lineup.bean.LineupInfoBean;
import com.gy.server.game.mountainRiverTournament.MRTHelper;
import com.gy.server.game.mountainRiverTournament.MRTStage;
import com.gy.server.game.mountainRiverTournament.MountainRiverTournamentService;
import com.gy.server.game.mountainRiverTournament.PlayerMRTModel;
import com.gy.server.game.mountainRiverTournament.bean.MRTInfo;
import com.gy.server.game.mountainRiverTournament.bean.MRTPlayEnums;
import com.gy.server.game.mountainRiverTournament.bean.MRTSpecialReportEnums;
import com.gy.server.game.mountainRiverTournament.bean.MRTSpecialReportInfo;
import com.gy.server.game.mountainRiverTournament.template.MRTConstant;
import com.gy.server.game.mountainRiverTournament.template.MRTPointTemplate;
import com.gy.server.game.mountainRiverTournament.template.MRTRobotTemplate;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.*;
import com.gy.server.game.record.RecordManager;
import com.gy.server.game.robot.RobotHelper;
import com.gy.server.game.robot.RobotType;
import com.gy.server.game.robot.bean.MultiTeamRobot;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbLeague;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.CollectionUtil;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.serialize.RedisAssistant;
import com.ttlike.server.tl.baselib.serialize.warZone.WarZoneInfo;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;

/**
 * 剑会群雄-逸侠对决 - 匹配
 * @author: gbk
 * @date: 2024-11-19 16:40
 */
public class MRTMatchAsync extends AbsMRTAsync {

    Player player;
    long time;
    int text = Text.没有异常;
    MRTStage stage;
    PbLeague.MRTMatchInfo.Builder matchInfo;
    LineupType lineupType;

    public MRTMatchAsync(Player player, long time){
        this.player = player;
        this.time = time;
    }

    @Override
    public void asyncExecute() {
        logic:{
            WarZoneInfo.WarZoneSingleInfo warZone = getCurWarZone();
            if(Objects.isNull(warZone)){
                text = Text.剑会群雄_未参与玩法;
                break logic;
            }
            MRTInfo info = getInfo();
            if(Objects.isNull(info)){
                text = Text.剑会群雄_未开始玩法;
                break logic;
            }
            //开始根据积分计算
            RedisAssistant ra = TLBase.getInstance().getRedisAssistant();
            String redisKey = GsRedisKey.MountainRiverTournament.level_rank.getRedisKey(warZone.getWarZoneId());
            double myPoint = ra.rankScore(redisKey, player.getPlayerId() + "");

            //获取信息

            matchOpponent(info, warZone.getWarZoneId(), redisKey, myPoint);


        }
    }


    /**
     * 根据我的积分匹配对手
     * @param warZoneId 战区id
     * @param redisKey key
     * @param myPoint 积分
     */
    public void matchOpponent(MRTInfo info, int warZoneId, String redisKey, double myPoint){
        MRTPlayEnums playTypeEnum = info.getPlayTypeEnum();
        MRTConstant constant = MountainRiverTournamentService.getConstant();
        StageType stageType;
        int battleCollectId;
        if(playTypeEnum == MRTPlayEnums.P6V6){
            lineupType = LineupType.mrt_6V6;
            battleCollectId = constant.scene6v6;
            stageType = StageType.mrt_6V6;
        }else{
            lineupType = LineupType.mrt_3V3;
            battleCollectId = constant.scene3v3;
            stageType = StageType.mrt_3V3;
        }
        List<LineupInfoBean> atkLineups = LineupHelper.getCrossLineup(player.getPlayerId(), lineupType);
        if(Objects.isNull(atkLineups)){
            text = Text.剑会群雄_没有布阵;
            return;
        }
        //检查是否必定是机器人
        PlayerMRTModel model = player.getModel(PlayerModelEnums.mrt);
        int loseInfo = model.getLoseInfo();
        MRTPointTemplate curPointTemp = MountainRiverTournamentService.getPointTemp((int) myPoint);
        matchInfo = PbLeague.MRTMatchInfo.newBuilder();
        //检查是否启用保护
        if(curPointTemp.IntegralProtection &&
                (CommonUtils.firstBinaryIsTrue(loseInfo,constant.integralProtection2) ||
                        CommonUtils.getBinaryOneCount(loseInfo, constant.integralProtection1_1) >= constant.integralProtection1_2)){
            matchRobot(info, warZoneId, atkLineups, myPoint, lineupType, stageType, battleCollectId);
        }else{
            Pair<Long, Double> targetInfo = match(redisKey, curPointTemp.minRange, curPointTemp.maxRange, 0);
            if(Objects.nonNull(targetInfo)){
                //找到合适的对手
                long targetPlayerId = targetInfo.getKey();
                double point = targetInfo.getValue();
                matchInfo.setIsRobot(false);
                MiniGamePlayer defMiniPlayer = PlayerHelper.getMiniPlayer(targetPlayerId);
                matchInfo.setDefUser(defMiniPlayer.genMiniUser());
                matchInfo.setPoint(point);
                //检查风采录是否有数据
                RedisAssistant ra = TLBase.getInstance().getRedisAssistant();
                String srRedisKey = GsRedisKey.MountainRiverTournament.special_report.getRedisKey(warZoneId);
                Map<String, MRTSpecialReportInfo> reportInfoMap = ra.hashGetAllBeans(srRedisKey, MRTSpecialReportInfo.class);
                Map<Integer, Boolean> hasReportInfos = new HashMap<>();
                for (MRTSpecialReportEnums reportEnums : MRTSpecialReportEnums.values()) {
                    hasReportInfos.put(reportEnums.getId(), reportInfoMap.containsKey(reportEnums.getId() + ""));
                }

                //通知world减少玩家匹配积分
                MRTHelper.reducePlayerMatchWeight(targetPlayerId);
                //获取布阵信息并且开打
                List<LineupInfoBean> defLineUps = LineupHelper.getCrossLineup(targetPlayerId, lineupType);
                if(Objects.isNull(defLineUps)){
                    text = Text.剑会群雄_没有布阵;
                    return;
                }
                stage = new MRTStage(info, warZoneId, player, atkLineups, myPoint, defMiniPlayer, defLineUps, point, hasReportInfos
                        ,lineupType, stageType, battleCollectId);
            }else{
                matchRobot(info, warZoneId, atkLineups, myPoint, lineupType, stageType, battleCollectId);
            }
        }


    }

    private void matchRobot(MRTInfo info, int warZoneId, List<LineupInfoBean> atkLineups, double myPoint, LineupType lineupType, StageType stageType, int battleCollectId){
        //机器人
        MRTRobotTemplate robotTemplate = MountainRiverTournamentService.getRobotTemplate();
        matchInfo.setIsRobot(true);
        matchInfo.setPoint(robotTemplate.point);
        MultiTeamRobot multiTeamRobot = RobotHelper.getRobotBean(RobotType.MRT,
                robotTemplate.npcId,
                robotTemplate.level,
                robotTemplate.battleCollectId1,
                robotTemplate.battleCollectId2,
                robotTemplate.battleCollectId3);
        matchInfo.setRobotInfo(multiTeamRobot.genMultiTeamPb());

        stage = new MRTStage(info, warZoneId, player, atkLineups, myPoint, robotTemplate.point, multiTeamRobot, lineupType, stageType, battleCollectId);
    }

    private Pair<Long, Double> match(String redisKey, double minPoint, double maxPoint, int pointGap){
        MRTConstant constant = MountainRiverTournamentService.getConstant();
        if(pointGap > constant.extendTheCeiling){
            return null;
        }
        RedisAssistant ra = TLBase.getInstance().getRedisAssistant();
        List<GyRedisTop> gyRedisTops = ra.rankRangeByScore(redisKey, Math.min(0, minPoint - pointGap), maxPoint + pointGap);
        List<GyRedisTop> canSelectPlayerInfos = new ArrayList<>();
        for (GyRedisTop gyRedisTop : gyRedisTops) {
            if(gyRedisTop.getValue().equals(player.getPlayerId() + "")){
                continue;
            }
            canSelectPlayerInfos.add(gyRedisTop);
        }
        if(CollectionUtil.isEmpty(canSelectPlayerInfos)){
            return match(redisKey, minPoint, maxPoint, pointGap + constant.magnificationNumber);
        }
        //需要比较对比
        long targetPlayerId = selectTargetPlayerId(canSelectPlayerInfos);
        Double point = ra.rankScore(redisKey, targetPlayerId + "");

        return Pair.of(targetPlayerId, point);
    }

    /**
     * 筛选匹配权重最大的玩家
     */
    private long selectTargetPlayerId(List<GyRedisTop> gyRedisTops){
        RedisAssistant ra = TLBase.getInstance().getRedisAssistant();
        String redisKey = GsRedisKey.MountainRiverTournament.match_weight.getRedisKey();
        //key:matchWeight value:玩家id
        Map<Integer, List<Long>> matchWeightInfos = new HashMap<>();
        MRTConstant constant = MountainRiverTournamentService.getConstant();
        for (GyRedisTop gyRedisTop : gyRedisTops) {
            RedisIntegerBean matchWeightBean = ra.hashGetBeans(redisKey, gyRedisTop.getValue(), RedisIntegerBean.class);
            int matchWeight = Objects.isNull(matchWeightBean) ? constant.weight : matchWeightBean.getParam();
            if(!matchWeightInfos.containsKey(matchWeight)){
                matchWeightInfos.put(matchWeight, new ArrayList<>());
            }
            matchWeightInfos.get(matchWeight).add(Long.parseLong(gyRedisTop.getValue()));
        }
        Integer max = Collections.max(matchWeightInfos.keySet());
        List<Long> targetPlayerIds = matchWeightInfos.get(max);
        Collections.shuffle(targetPlayerIds);
        return targetPlayerIds.get(0);
    }

    @Override
    public void execute() {
        PbProtocol.MountainRiverTournamentMatchRst.Builder rst = PbProtocol.MountainRiverTournamentMatchRst
                .newBuilder().setResult(Text.genServerRstInfo(text));
        if(text == Text.没有异常){
            stage.init();
            rst.setStage(stage.genAbstractPb());
            matchInfo.setAtkLineup(RecordManager.genLineup(lineupType, stage.getAtks()));
            matchInfo.setDefLineup(RecordManager.genLineup(lineupType, stage.getDefs()));
            rst.setMatchInfo(matchInfo);
            CombatManager.combatPrepare(stage);
        }
        player.send(PtCode.MOUNTAINRIVERTOURNAMENT_MATCH_RST, rst.build(), time);

    }
}
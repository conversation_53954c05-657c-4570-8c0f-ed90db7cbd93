package com.gy.server.game.mountainRiverTournament.async;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.db.nosql.redis.GyRedisTop;
import com.gy.server.game.mountainRiverTournament.MountainRiverTournamentService;
import com.gy.server.game.mountainRiverTournament.bean.MRTInfo;
import com.gy.server.game.mountainRiverTournament.bean.MRTPlayEnums;
import com.gy.server.game.mountainRiverTournament.template.MRTConstant;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbLeague;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.CollectionUtil;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.serialize.RedisAssistant;
import com.ttlike.server.tl.baselib.serialize.warZone.WarZoneInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 剑会群雄-逸侠对决 - 排行榜信息
 * @author: gbk
 * @date: 2024-11-25 20:10
 */
public class MRTRankInfoAsync extends AbsMRTAsync {

    Player player;
    int rankType; //1.段位 2.积分
    long time;
    int page;
    final int maxRankNum = 10;
    List<PbLeague.MRTRankInfo> rankInfos = new ArrayList<>();
    PbLeague.MRTRankInfo.Builder myRank;

    public MRTRankInfoAsync(Player player, int rankType, int page, long time){
        this.player = player;
        this.rankType = rankType;
        this.time = time;
        this.page = page;
    }

    @Override
    public void asyncExecute() {
        WarZoneInfo.WarZoneSingleInfo warZone = getCurWarZone();
        RedisAssistant ra = TLBase.getInstance().getRedisAssistant();
        PbLeague.MRTRankInfo.Builder rankInfo = PbLeague.MRTRankInfo.newBuilder();
        MRTConstant constant = MountainRiverTournamentService.getConstant();

        if(rankType == 1){
            //段位排行榜展示人数上限
            if(constant.segmentLeaderboardNum / maxRankNum < page){
                return ;
            }
            //段位
            String redisKey = GsRedisKey.MountainRiverTournament.level_rank.getRedisKey(warZone.getWarZoneId());
            List<GyRedisTop> gyRedisTops = ra.rankRange(redisKey, page * maxRankNum + 1, (page + 1) * maxRankNum, false);
            if(CollectionUtil.isNotEmpty(gyRedisTops)){
                Map<Long, MiniGamePlayer> miniPlayerInfos = getMiniPlayerInfos(gyRedisTops);
                for (GyRedisTop gyRedisTop : gyRedisTops) {
                    long playerId = Long.parseLong(gyRedisTop.getValue());
                    int rank = gyRedisTop.getRank();
                    Double point = gyRedisTop.getScore();
                    fillRankInfo(rankInfo, miniPlayerInfos.get(playerId), rank, point);
                    rankInfos.add(rankInfo.build());
                }
            }
            //填充自己的信息
            myRank = PbLeague.MRTRankInfo.newBuilder();
            fillRankInfo(myRank, PlayerHelper.getMiniPlayer(player.getPlayerId()),
                    ra.rankGet(redisKey, player.getPlayerId() + "",false),
                    Math.max(ra.rankScore(redisKey, player.getPlayerId() + ""), 0));
        }else{
            //积分排行榜展示人数上限
            if(constant.leaderboardNum / maxRankNum < page){
                return ;
            }
            MRTInfo info = getInfo();
            //积分
            String redisKey = GsRedisKey.MountainRiverTournament.point_rank.getRedisKey(warZone.getWarZoneId());
            String rankTempRedisKey = GsRedisKey.MountainRiverTournament.point_rank_temp.getRedisKey(warZone.getWarZoneId());
            List<GyRedisTop> gyRedisTops = ra.rankRange(redisKey, page * maxRankNum + 1, (page + 1) * maxRankNum, false);
            if(CollectionUtil.isNotEmpty(gyRedisTops)){

                Map<Long, MiniGamePlayer> miniPlayerInfos = getMiniPlayerInfos(gyRedisTops);
                for (GyRedisTop gyRedisTop : gyRedisTops) {
                    long playerId = Long.parseLong(gyRedisTop.getValue());
                    int rank = gyRedisTop.getRank();
                    Double point = gyRedisTop.getScore();
                    fillRankInfo(rankInfo, rankTempRedisKey, miniPlayerInfos.get(playerId), rank, point, info.getPlayType() == MRTPlayEnums.P6V6.getType());
                    rankInfos.add(rankInfo.build());
                }
            }
            //填充自己的信息
            myRank = PbLeague.MRTRankInfo.newBuilder();
            fillRankInfo(myRank, rankTempRedisKey, PlayerHelper.getMiniPlayer(player.getPlayerId()),
                    ra.rankGet(redisKey, player.getPlayerId() + "",false),
                    Math.max(ra.rankScore(redisKey, player.getPlayerId() + ""), 0),
                    info.getPlayType() == MRTPlayEnums.P6V6.getType());

        }


    }

    public Map<Long, MiniGamePlayer> getMiniPlayerInfos(List<GyRedisTop> gyRedisTops){
        List<Long> playerIds = new ArrayList<>();
        for (GyRedisTop gyRedisTop : gyRedisTops) {
            playerIds.add(Long.parseLong(gyRedisTop.getValue()));
        }
        return PlayerHelper.getMiniPlayersForMap(playerIds);
    }

    public void fillRankInfo(PbLeague.MRTRankInfo.Builder rankInfo, MiniGamePlayer miniGamePlayer, int rank, Double point){
        rankInfo.clear();

        rankInfo.setUser(miniGamePlayer.genMinMiniUser());
        rankInfo.setRank(rank);
        rankInfo.setPoint(point);
    }

    public void fillRankInfo(PbLeague.MRTRankInfo.Builder rankInfo, String redisKey, MiniGamePlayer miniGamePlayer, int rank, Double point, boolean is6V6){
        rankInfo.clear();
        RedisAssistant ra = TLBase.getInstance().getRedisAssistant();
        long playerId = miniGamePlayer.getPlayerId();
        rankInfo.setUser(miniGamePlayer.genMinMiniUser());
        rankInfo.setRank(rank);
        rankInfo.setPoint(point);
        if(is6V6){
            rankInfo.setLastPoint(point);
        }else{
            String v = ra.hashGetString(redisKey, playerId + "");
            rankInfo.setLastPoint((Objects.isNull(v) || v.isEmpty()) ? 0D : Double.parseDouble(v));
        }
    }

    @Override
    public void execute() {
        PbProtocol.MountainRiverTournamentGetRankRst.Builder rst = PbProtocol.MountainRiverTournamentGetRankRst.newBuilder().setResult(Text.genOkServerRstInfo());
        rst.addAllRankInfos(rankInfos);
        rst.setType(rankType);
        rst.setPage(page);
        rst.setMyRank(myRank);
        player.send(PtCode.MOUNTAINRIVERTOURNAMENT_GETRANK_RST, rst.build(), time);
    }
}
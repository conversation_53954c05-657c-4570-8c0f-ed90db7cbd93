package com.gy.server.game.mountainRiverTournament.async;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.mountainRiverTournament.MountainRiverTournamentService;
import com.gy.server.game.mountainRiverTournament.PlayerMRTModel;
import com.gy.server.game.mountainRiverTournament.bean.MRTInfo;
import com.gy.server.game.mountainRiverTournament.reward.MRTTaskRewardConditionEnums;
import com.gy.server.game.mountainRiverTournament.template.MRTPointTemplate;
import com.gy.server.game.mountainRiverTournament.template.MRTTaskRewardTemplate;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.serialize.RedisAssistant;
import com.ttlike.server.tl.baselib.serialize.warZone.WarZoneInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * 领取奖励(获取段位)
 * @author: gbk
 * @date: 2024-11-27 13:38
 */
public class MRTReceiveRewardAsync extends AbsMRTAsync {
    Player player;
    MRTTaskRewardTemplate taskRewardTemplate;
    long time;
    MRTInfo info;

    public MRTReceiveRewardAsync(Player player, MRTTaskRewardTemplate taskRewardTemplate, long time){
        this.player = player;
        this.taskRewardTemplate = taskRewardTemplate;
        this.time = time;
    }

    double point;
    @Override
    public void asyncExecute() {
        WarZoneInfo.WarZoneSingleInfo warZoneSingleInfo = getCurWarZone();
        info = getInfo();
        RedisAssistant ra = TLBase.getInstance().getRedisAssistant();
        String redisKey = GsRedisKey.MountainRiverTournament.level_rank.getRedisKey(warZoneSingleInfo.getWarZoneId());
        point = ra.rankScore(redisKey, player.getPlayerId() + "");
    }

    @Override
    public void execute() {
        PbProtocol.MountainRiverTournamentReceiveRst.Builder rst = PbProtocol.MountainRiverTournamentReceiveRst.newBuilder().setResult(Text.genOkServerRstInfo());
        MRTPointTemplate pointTemp = MountainRiverTournamentService.getPointTemp((int) point);
        MRTTaskRewardConditionEnums conditionType = taskRewardTemplate.conditionType;
        PlayerMRTModel model = player.getModel(PlayerModelEnums.mrt);
        if(!conditionType.canReceive(taskRewardTemplate, pointTemp.id)){
            rst.setResult(Text.genServerRstInfo(Text.剑会群雄_奖励不能领取));
        }else if(model.getHadReceiveSeasonRewardId()
                .getOrDefault(info.getSeasonId(), new ArrayList<>()).contains(taskRewardTemplate.id)){
            rst.setResult(Text.genServerRstInfo(Text.剑会群雄_奖励已经领取));
        }else{
            List<Integer> ids = model.getHadReceiveSeasonRewardId().getOrDefault(info.getSeasonId(), new ArrayList<>());
            ids.add(taskRewardTemplate.id);
            model.getHadReceiveSeasonRewardId().put(info.getSeasonId(),ids);
            rst.addAllRewards(conditionType.addToPlayer(player, taskRewardTemplate, BehaviorType.mrt_receive2));
            rst.setId(taskRewardTemplate.id);
            rst.setIsFengcailu(false);
        }
        player.send(PtCode.MOUNTAINRIVERTOURNAMENT_RECEIVE_RST, rst.build(), time);
    }
}
package com.gy.server.game.mountainRiverTournament;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.core.Configuration;
import com.gy.server.core.delay.MessageSystemHashInvoke;
import com.gy.server.core.delay.MessageSystemSyncInvoke;
import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.function.Function;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.mountainRiverTournament.async.*;
import com.gy.server.game.mountainRiverTournament.bean.MRTSpecialReportEnums;
import com.gy.server.game.mountainRiverTournament.bean.MRTSpecialReportInfo;
import com.gy.server.game.mountainRiverTournament.reward.MRTTaskRewardConditionEnums;
import com.gy.server.game.mountainRiverTournament.reward.MRTTaskRewardTypeEnums;
import com.gy.server.game.mountainRiverTournament.template.*;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.game.service.Service;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.StringUtil;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.serialize.RedisAssistant;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.*;

/**
 * 剑会群雄-逸侠对决
 * @author: gbk
 * @date: 2024-11-11 10:46
 */
public class MountainRiverTournamentService extends PlayerPacketHandler implements Service {

    private static MRTConstant constant;

    public static List<MRTRankTemplate> rankTemplates = new ArrayList<>();

    private static List<MRTRobotTemplate> robotTemplates = new ArrayList<>();

    private static List<MRTPointTemplate> pointTemplateList = new ArrayList<>();
    //段位id：段位信息
    private static Map<Integer, MRTPointTemplate> pointTemplateMap = new HashMap<>();
    //段位id：分差列表
    private static Map<Integer, List<MRTDIffCountTemplate>> diffCountTempMap = new HashMap<>();
    //赛季id：赛季信息
    private static Map<Integer, MRTSeasonTemplate> seasonTemplateMap = new HashMap<>();
    //类型：数据
    private static Map<Integer, MRTSpecialReportTemplate> specialReportTemplateMap = new HashMap<>();
    //奖励类型：id列表
    private static Map<MRTTaskRewardTypeEnums, Set<Integer>> taskRewardIdMap = new HashMap<>();
    //奖励id：奖励信息
    private static Map<Integer, MRTTaskRewardTemplate> taskRewardMap = new HashMap<>();
    //最小积分
    private static int minPoint = Integer.MAX_VALUE;

    public static MRTSeasonTemplate getSeasonTemp(int seasonId){
        return seasonTemplateMap.get(seasonId);
    }

    public static MRTSpecialReportTemplate getSpecialReportTemplate(int type){
        return specialReportTemplateMap.get(type);
    }

    public static Map<MRTTaskRewardTypeEnums, Set<Integer>> getTaskRewardIdMap() {
        return taskRewardIdMap;
    }
    
    public static MRTTaskRewardTemplate getTaskReward(int rewardId){
        return taskRewardMap.get(rewardId);
    }

    public static boolean canEnterRecord(int type, long atkPower, long defPower){
        return MRTSpecialReportEnums.getById(type).canEnterRecord(atkPower, defPower);
//        MRTSpecialReportTemplate specialReportTemplate = getSpecialReportTemplate(type);
//        return specialReportTemplate.canEnterRecord(atkPower, defPower);
    }
    
    public static int getDiffAddedCount(int rankId, double diffPoint){
        List<MRTDIffCountTemplate> diffCountTemplates = diffCountTempMap.get(rankId);
        if(CollectionUtil.isNotEmpty(diffCountTemplates)){
            for (int i = diffCountTemplates.size() - 1; i >= 0; i--) {
                MRTDIffCountTemplate diffCountTemplate = diffCountTemplates.get(i);
                if(diffPoint >= diffCountTemplate.diffCount){
                    return diffCountTemplate.addedCount;
                }
            }
        }
        return 0;
    }

    public static MRTPointTemplate getPointTemp(int point){
        int findPoint = Math.max(point, minPoint);
        for (int i = pointTemplateList.size() - 1; i >= 0; i--) {
            MRTPointTemplate mrtPointTemplate = pointTemplateList.get(i);
            if(mrtPointTemplate.rankCount <= findPoint){
                return mrtPointTemplate;
            }
        }
        return null;
    }

    public static MRTPointTemplate getPointTemplateById(int id){
        return pointTemplateMap.get(id);
    }

    public static int getPointByRank(int rank){
        for (MRTRankTemplate rankTemplate : rankTemplates) {
            if(rankTemplate.minRank <= rank && rankTemplate.maxRank >= rank){
                return rankTemplate.output;
            }
        }
        return 0;
    }

    public static List<MRTRobotTemplate> getRobotTemplates(){
        Collections.shuffle(robotTemplates);
        return robotTemplates;
    }

    public static MRTRobotTemplate getRobotTemplate(){
        return getRobotTemplates().get(0);
    }

    public static MRTConstant getConstant(){
        return constant;
    }

    @Handler(PtCode.MOUNTAINRIVERTOURNAMENT_MAINVIEW_REQ)
    public void mainView(Player player, PbProtocol.MountainRiverTournamentMainViewReq req, long time) {
        PbProtocol.MountainRiverTournamentMainViewRst.Builder rst = PbProtocol.MountainRiverTournamentMainViewRst.newBuilder().setResult(Text.genOkServerRstInfo());

        logic:{
            //检查能否开启
            int errorCode = commonCheck(player);
            if(errorCode != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(errorCode));
                break logic;
            }

            ThreadPool.execute(new MRTMainViewAsync(player, time));
            return;
        }

        player.send(PtCode.MOUNTAINRIVERTOURNAMENT_MAINVIEW_RST, rst.build(), time);
    }

    private int commonCheck(Player player){
        //检查function是否开启
        if(Function.mrt.isNotOpen(player)){
            return Text.功能未开启;
        }

        return Text.没有异常;
    }

    @Handler(PtCode.MOUNTAINRIVERTOURNAMENT_MATCH_REQ)
    public void match(Player player, PbProtocol.MountainRiverTournamentMatchReq req, long time) {
        PbProtocol.MountainRiverTournamentMatchRst.Builder rst = PbProtocol.MountainRiverTournamentMatchRst.newBuilder().setResult(Text.genOkServerRstInfo());

        //检查function是否开启
        logic:{
            int errorCode = commonCheck(player);
            if(errorCode != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(errorCode));
                break logic;
            }

            ThreadPool.execute(new MRTMatchAsync(player, time));
            return;

        }

        player.send(PtCode.MOUNTAINRIVERTOURNAMENT_MATCH_RST, rst.build(), time);
    }
    @Handler(PtCode.MOUNTAINRIVERTOURNAMENT_GETRANK_REQ)
    public void getRankInfo(Player player, PbProtocol.MountainRiverTournamentGetRankReq req, long time) {
        PbProtocol.MountainRiverTournamentGetRankRst.Builder rst = PbProtocol.MountainRiverTournamentGetRankRst.newBuilder().setResult(Text.genOkServerRstInfo());

        //检查function是否开启
        logic:{
            int type = req.getType();
            int page = req.getPage();
            int errorCode = commonCheck(player);
            if(errorCode != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(errorCode));
                break logic;
            }
            if(type != 1 && type != 2){
                rst.setResult(Text.genServerRstInfo(Text.数据异常));
                break logic;
            }
            ThreadPool.execute(new MRTRankInfoAsync(player, type, page,time));
            return;
        }

        player.send(PtCode.MOUNTAINRIVERTOURNAMENT_GETRANK_RST, rst.build(), time);
    }
    @Handler(PtCode.MOUNTAINRIVERTOURNAMENT_FENGCAILU_REQ)
    public void fengCaiLu(Player player, PbProtocol.MountainRiverTournamentFengcailuReq req, long time) {
        PbProtocol.MountainRiverTournamentFengcailuRst.Builder rst = PbProtocol.MountainRiverTournamentFengcailuRst.newBuilder().setResult(Text.genOkServerRstInfo());


        //检查function是否开启
        logic:{
            int errorCode = commonCheck(player);
            if(errorCode != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(errorCode));
                break logic;
            }

            ThreadPool.execute(new MRTFengcailuInfoAsync(player, time));
            return;
        }

        player.send(PtCode.MOUNTAINRIVERTOURNAMENT_FENGCAILU_RST, rst.build(), time);
    }

    @Handler(PtCode.MOUNTAINRIVERTOURNAMENT_SPECIALRECORD_REQ)
    public void specialRecord(Player player, PbProtocol.MountainRiverTournamentSpecialRecordReq req, long time) {
        PbProtocol.MountainRiverTournamentSpecialRecordRst.Builder rst = PbProtocol.MountainRiverTournamentSpecialRecordRst.newBuilder().setResult(Text.genOkServerRstInfo());

        //检查function是否开启
        logic:{
            int errorCode = commonCheck(player);
            if(errorCode != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(errorCode));
                break logic;
            }
            ThreadPool.execute(new MRTSpecialRecordAsync(player, time));
            return;
        }

        player.send(PtCode.MOUNTAINRIVERTOURNAMENT_SPECIALRECORD_RST, rst.build(), time);
    }

    @Handler(PtCode.MOUNTAINRIVERTOURNAMENT_PRAISE_REQ)
    public void praise(Player player, PbProtocol.MountainRiverTournamentPraiseReq req, long time) {
        PbProtocol.MountainRiverTournamentPraiseRst.Builder rst = PbProtocol.MountainRiverTournamentPraiseRst.newBuilder().setResult(Text.genOkServerRstInfo());

        //检查function是否开启
        logic:{
            long targetPlayerId = req.getPlayerId();
            int errorCode = commonCheck(player);
            if(errorCode != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(errorCode));
                break logic;
            }
            PlayerMRTModel mrtModel = player.getModel(PlayerModelEnums.mrt);
            if(mrtModel.getHadPraisePlayerIds().contains(targetPlayerId)){
                rst.setResult(Text.genServerRstInfo(Text.剑会群雄_已经点过赞));
                break logic;
            }
            ThreadPool.execute(new MRTPraiseAsync(player, req.getPlayerId(), time));
            return;
        }

        player.send(PtCode.MOUNTAINRIVERTOURNAMENT_PRAISE_RST, rst.build(), time);
    }
    @Handler(PtCode.MOUNTAINRIVERTOURNAMENT_RECEIVE_REQ)
    public void receiveReward(Player player, PbProtocol.MountainRiverTournamentReceiveReq req, long time) {
        PbProtocol.MountainRiverTournamentReceiveRst.Builder rst = PbProtocol.MountainRiverTournamentReceiveRst.newBuilder().setResult(Text.genOkServerRstInfo());

        //检查function是否开启
        logic:{
            boolean isFengcailu = req.getIsFengcailu();
            int id = req.getId();
            int errorCode = commonCheck(player);
            if(errorCode != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(errorCode));
                break logic;
            }
            PlayerMRTModel mrtModel = player.getModel(PlayerModelEnums.mrt);
            if(isFengcailu){
                //风采录
                if(mrtModel.isTodayReceiveFengcailu()){
                    rst.setResult(Text.genServerRstInfo(Text.剑会群雄_奖励已经领取));
                    break logic;
                }
                mrtModel.setTodayReceiveFengcailu(true);
                MRTConstant constant = getConstant();
                rst.addAllRewards(Reward.writeCollectionToPb(Reward.addFromTemplates(constant.paperRewards, player, BehaviorType.mrt_receive1)));
            }else{
                //奖励
                if(mrtModel.getHadReceiveDailyRewardId().contains(id)){
                    rst.setResult(Text.genServerRstInfo(Text.剑会群雄_奖励已经领取));
                    break logic;
                }
                MRTTaskRewardTemplate taskRewardTemplate = taskRewardMap.get(id);
                if(Objects.isNull(taskRewardTemplate)){
                    rst.setResult(Text.genServerRstInfo(Text.剑会群雄_奖励不存在));
                    break logic;
                }
                MRTTaskRewardTypeEnums rewardType = taskRewardTemplate.rewardType;
                if(rewardType == MRTTaskRewardTypeEnums.dailyLevel || rewardType == MRTTaskRewardTypeEnums.settlement){
                    rst.setResult(Text.genServerRstInfo(Text.剑会群雄_奖励不能领取));
                    break logic;
                }
                MRTTaskRewardConditionEnums conditionType = taskRewardTemplate.conditionType;
                if(conditionType == MRTTaskRewardConditionEnums.level){
                    //异步获取段位
                    ThreadPool.execute(new MRTReceiveRewardAsync(player, taskRewardTemplate, time));
                    return ;
                }else{
                    PlayerMRTModel model = player.getModel(PlayerModelEnums.mrt);
                    if(!conditionType.canReceive(taskRewardTemplate, model)){
                        rst.setResult(Text.genServerRstInfo(Text.剑会群雄_奖励不能领取));
                        break logic;
                    }
                    mrtModel.getHadReceiveDailyRewardId().add(id);
                    rst.addAllRewards(conditionType.addToPlayer(player, taskRewardTemplate, BehaviorType.mrt_receive2));
                }

            }

            rst.setId(id);
            rst.setIsFengcailu(isFengcailu);
        }

        player.send(PtCode.MOUNTAINRIVERTOURNAMENT_RECEIVE_RST, rst.build(), time);
    }


    @Override
    public void loadConfigData(boolean isStartup) throws Exception {

        List<MRTRankTemplate> rankTemplatesTemp = new ArrayList<>();
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.mountainRiverTournament_TournamentRank);
        for (Map<String, String> map : mapList) {
            MRTRankTemplate rankTemplate = new MRTRankTemplate();
            rankTemplate.readTxt(map);
            rankTemplatesTemp.add(rankTemplate);
        }
        rankTemplates = rankTemplatesTemp;
        List<MRTRobotTemplate> robotTemplatesTemp = new ArrayList<>();
        mapList = ConfigReader.read(ConfigFile.mountainRiverTournament_robot);
        for (Map<String, String> map : mapList) {
            MRTRobotTemplate robotTemplate = new MRTRobotTemplate();
            robotTemplate.readTxt(map);
            robotTemplatesTemp.add(robotTemplate);
        }
        robotTemplates = robotTemplatesTemp;

        mapList = ConfigReader.read(ConfigFile.mountainRiverTournament_TournamentDan);
        List<MRTPointTemplate> pointTemplateListTemp = new ArrayList<>();
        //段位id：段位信息
        Map<Integer, MRTPointTemplate> pointTemplateMapTemp = new HashMap<>();
        int minPointTemp = Integer.MAX_VALUE;
        for (Map<String, String> map : mapList) {
            MRTPointTemplate pointTemplate = new MRTPointTemplate();
            pointTemplate.readTxt(map);
            pointTemplateListTemp.add(pointTemplate);
            minPointTemp = Math.min(minPointTemp, pointTemplate.rankCount);
            pointTemplateMapTemp.put(pointTemplate.id, pointTemplate);
        }
        minPoint = minPointTemp;
        pointTemplateMap = pointTemplateMapTemp;
        pointTemplateList = pointTemplateListTemp;
        //段位id：分差列表
        Map<Integer, List<MRTDIffCountTemplate>> diffCountTempMapTemp = new HashMap<>();
        mapList = ConfigReader.read(ConfigFile.mountainRiverTournament_rankDifferenceCount);
        for (Map<String, String> map : mapList) {
            MRTDIffCountTemplate countTemplate = new MRTDIffCountTemplate();
            countTemplate.readTxt(map);
            int[] ints = StringUtil.splitToIntArray(map.get("rankid"), "\\|");
            for (int rankId : ints) {
                countTemplate.rankId = rankId;
                if(!diffCountTempMapTemp.containsKey(countTemplate.rankId)){
                    diffCountTempMapTemp.put(countTemplate.rankId, new ArrayList<>());
                }
                diffCountTempMapTemp.get(countTemplate.rankId).add(countTemplate);
            }
        }
        diffCountTempMap = diffCountTempMapTemp;
        //赛季id：赛季信息
        Map<Integer, MRTSeasonTemplate> seasonTemplateMapTemp = new HashMap<>();
        mapList = ConfigReader.read(ConfigFile.mountainRiverTournament_season);
        for (Map<String, String> map : mapList) {
            MRTSeasonTemplate seasonTemplate = new MRTSeasonTemplate();
            seasonTemplate.readTxt(map);
            seasonTemplateMapTemp.put(seasonTemplate.seasonId,seasonTemplate);
        }
        seasonTemplateMap = seasonTemplateMapTemp;
        //类型：数据
        Map<Integer, MRTSpecialReportTemplate> specialReportTemplateMapTemp = new HashMap<>();
        mapList = ConfigReader.read(ConfigFile.mountainRiverTournament_specialReport);
        for (Map<String, String> map : mapList) {
            MRTSpecialReportTemplate reportTemplate = new MRTSpecialReportTemplate();
            reportTemplate.readTxt(map);
            specialReportTemplateMapTemp.put(reportTemplate.id,reportTemplate);
        }
        specialReportTemplateMap = specialReportTemplateMapTemp;
        mapList = ConfigReader.read(ConfigFile.mountainRiverTournament_const);
        constant = new MRTConstant();
        for (Map<String, String> map : mapList) {
            constant.readTxt(map);
        }
        mapList = ConfigReader.read(ConfigFile.mountainRiverTournament_TaskReward);
        Map<MRTTaskRewardTypeEnums, Set<Integer>> taskRewardIdMapTemp = new HashMap<>();
        Map<Integer, MRTTaskRewardTemplate> taskRewardMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            MRTTaskRewardTemplate taskRewardTemplate = new MRTTaskRewardTemplate();
            taskRewardTemplate.readTxt(map);
            if(!taskRewardIdMapTemp.containsKey(taskRewardTemplate.rewardType)){
                taskRewardIdMapTemp.put(taskRewardTemplate.rewardType, new HashSet<>());
            }
            taskRewardIdMapTemp.get(taskRewardTemplate.rewardType).add(taskRewardTemplate.id);
            taskRewardMapTemp.put(taskRewardTemplate.id, taskRewardTemplate);
        }
        taskRewardIdMap = taskRewardIdMapTemp;
        taskRewardMap = taskRewardMapTemp;
    }

    @Override
    public void clearConfigData() {
        rankTemplates.clear();
        robotTemplates.clear();
        pointTemplateList.clear();
        pointTemplateMap.clear();
        diffCountTempMap.clear();
        seasonTemplateMap.clear();
        specialReportTemplateMap.clear();
    }


    @Override
    public boolean isGameServer() {
        return true;
    }

    @Override
    public boolean isWorldServer() {
        return true;
    }


    public static void main(String[] args) throws Exception {
        Configuration.serverId = 998;
        Configuration.startupMode = Configuration.StartupMode.xml;
        TLBase.getInstance().init(Configuration.serverId, ServerType.GAME, "game", Configuration.startupMode.name(), MessageSystemSyncInvoke.getInstance(), MessageSystemHashInvoke.getInstance());

        RedisAssistant redisAssistant = TLBase.getInstance().getRedisAssistant();
//        String redisKey = GsRedisKey.MountainRiverTournament.level_rank.getRedisKey(2);
//        System.out.println(redisAssistant.getRedisKeys(GsRedisKey.MountainRiverTournament.level_rank.getRedisKey("*"), 100));
////        for(int i = 0; i < 10; i++){
////            double value = i + 1;
////            String key = "player" + value;
////            redisAssistant.rankPut(rankKey, key, value);
////        }
//
//        redisAssistant.rankRemove(redisKey,"998000009");
//        redisAssistant.rankRemove(redisKey,"998000008");
//        List<GyRedisTop> gyRedisTops = redisAssistant.rankRange(redisKey,1,10, false);
//        System.out.println("----");
//        for (GyRedisTop gyRedisTop : gyRedisTops) {
//            System.out.println(gyRedisTop.getRank() + " : " +  gyRedisTop.getValue() + " : " + gyRedisTop.getScore());
//        }

        int warzoneId = 2;
        String redisKey = GsRedisKey.MountainRiverTournament.special_report_show.getRedisKey(warzoneId);
        Map<String, MRTSpecialReportInfo> reportInfoMap = redisAssistant.hashGetAllBeans(redisKey, MRTSpecialReportInfo.class);
        System.out.println(reportInfoMap);
//        MRTSpecialReportInfo reportInfo = reportInfoMap.get(2 + "");
//        reportInfo.setMiniPlayer(PlayerHelper.getMiniPlayer(9980000013L));
//        redisAssistant.hashPut(redisKey,2 + "", reportInfo);
////
//        reportInfo = reportInfoMap.get(3 + "");
//        reportInfo.setMiniPlayer(PlayerHelper.getMiniPlayer(9980000010L));
//        redisAssistant.hashPut(redisKey,3 + "", reportInfo);
//
//        reportInfo = reportInfoMap.get(4 + "");
//        reportInfo.setMiniPlayer(PlayerHelper.getMiniPlayer(9980000012L));
//        redisAssistant.hashPut(redisKey,4 + "", reportInfo);
    }

}
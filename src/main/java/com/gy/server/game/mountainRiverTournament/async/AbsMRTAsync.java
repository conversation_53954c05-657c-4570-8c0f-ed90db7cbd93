package com.gy.server.game.mountainRiverTournament.async;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.core.Configuration;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.mountainRiverTournament.bean.MRTInfo;
import com.gy.server.game.warZone.WarZoneHelper;
import com.gy.server.game.warZone.WarZoneTypeEnums;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.serialize.RedisAssistant;
import com.ttlike.server.tl.baselib.serialize.warZone.WarZoneInfo;

/**
 * 异步调用抽象类
 * @author: gbk
 * @date: 2024-11-19 19:29
 */
public abstract class AbsMRTAsync extends AsyncCall {


    public WarZoneInfo.WarZoneSingleInfo getCurWarZone(){
        WarZoneInfo warZoneInfo = WarZoneHelper.getWarZoneInfo(WarZoneTypeEnums.MountainRiverTournament);
        return warZoneInfo.getInfoByServerId(Configuration.serverId);
    }

    public WarZoneInfo getWarZone(){
        return WarZoneHelper.getWarZoneInfo(WarZoneTypeEnums.MountainRiverTournament);
    }

    public MRTInfo getInfo(){
        RedisAssistant ra = TLBase.getInstance().getRedisAssistant();
        return ra.getBean(MRTInfo.class, GsRedisKey.MountainRiverTournament.season.getRedisKey());
    }

}
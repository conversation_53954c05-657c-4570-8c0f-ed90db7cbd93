package com.gy.server.game.mountainRiverTournament.bean;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;

/**
 * 赛季信息
 * @author: gbk
 * @date: 2024-11-12 10:52
 */
@ProtobufClass
public class MRTInfo {

    //赛季id
    @Protobuf(order = 1)
    private int seasonId;
    //玩法类型
    @Protobuf(order = 2)
    private int playType;
    //下次刷新时间
    @Protobuf(order = 3)
    private long nextRefreshTime;

    public int getSeasonId() {
        return seasonId;
    }

    public void setSeasonId(int seasonId) {
        this.seasonId = seasonId;
    }

    public int getPlayType() {
        return playType;
    }

    public MRTPlayEnums getPlayTypeEnum(){
        return playType == MRTPlayEnums.P6V6.getType() ? MRTPlayEnums.P6V6 : MRTPlayEnums.P3V3;
    }

    public void setPlayType(int playType) {
        this.playType = playType;
    }

    public long getNextRefreshTime() {
        return nextRefreshTime;
    }

    public void setNextRefreshTime(long nextRefreshTime) {
        this.nextRefreshTime = nextRefreshTime;
    }
}
package com.gy.server.game.mountainRiverTournament.reward;

import com.gy.server.game.drop.Reward;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.player.Player;
import com.gy.server.packet.PbCommons;

import java.util.List;

/**
 * 奖励检查
 * @author: gbk
 * @date: 2024-11-27 09:50
 */
public interface IRewardCheck<R, I> {

    /**
     * 能否领取奖励
     * @param r reward
     * @param i Info
     * @return
     */
    public boolean canReceive(R r, I i);

    /**
     * 直接添加到玩家身上
     * @param player 玩家
     * @param r reward
     * @param behaviorType 操作类型
     * @return
     */
    public List<PbCommons.PbReward> addToPlayer(Player player, R r, BehaviorType behaviorType);

    public List<Reward> getReward(R r);

}
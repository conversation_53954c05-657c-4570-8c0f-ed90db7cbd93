package com.gy.server.game.mountainRiverTournament;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.mail.MailManager;
import com.gy.server.game.mail.MailType;
import com.gy.server.game.mountainRiverTournament.async.MRTReissueLevelAsync;
import com.gy.server.game.mountainRiverTournament.reward.MRTTaskRewardTypeEnums;
import com.gy.server.game.mountainRiverTournament.template.MRTPointTemplate;
import com.gy.server.game.mountainRiverTournament.template.MRTTaskRewardTemplate;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModel;
import com.gy.server.game.player.event.PlayerEvent;
import com.gy.server.game.player.event.PlayerEventHandler;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbCommons;
import com.gy.server.utils.CollectionUtil;
import com.ttlike.server.tl.baselib.serialize.base.RedisListIntegerBean;
import com.ttlike.server.tl.baselib.serialize.mrt.PlayerMRTModelDb;
import com.ttlike.server.tl.baselib.serialize.player.PlayerBlobDb;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.*;

/**
 * 剑会群雄-逸侠对决
 * @author: gbk
 * @date: 2024-11-18 13:37
 */
public class PlayerMRTModel extends PlayerModel implements PlayerEventHandler {

    public PlayerMRTModel(Player player) {
        super(player);
    }
    //总战斗次数
    private int battleTimes;
    //战斗胜利次数
    private int winTimes;
    //今天战斗次数
    private int todayBattleTimes;
    //今天胜利次数
    private int todayWinTimes;
    //玩家在每日的第一场失败时会不扣除积分标记
    private boolean isFirstFail;
    //今天战斗第一场分数
    private double todayPoint;
    //失败情况,失败是1
    private int loseInfo;
    //今天是否领取风采录奖励
    private boolean todayReceiveFengcailu;
    //已经领取奖励id
    private List<Integer> hadReceiveDailyRewardId = new ArrayList<>();
    //赛季奖励,赛季id：奖励id
    private Map<Integer, List<Integer>> hadReceiveSeasonRewardId = new HashMap<>();
    //已经点赞玩家id
    private List<Long> hadPraisePlayerIds = new ArrayList<>();

    public void addBattleTimes(boolean isWin){
        if(isWin){
            winTimes++;
            todayWinTimes++;
        }else{
            loseInfo = (loseInfo << 1) | 1;
        }
        todayBattleTimes++;
        battleTimes++;
    }

    public List<Boolean> getLoseInfoList(){
        List<Boolean> winInfos = new ArrayList<>();
        for(int i = 0; i < 5; i++){
            winInfos.add(loseInfo >> i  == 1);
        }
        return winInfos;
    }

    public void refresh(){
        Player player = getPlayer();
        //检查每日奖励补发
        Set<Integer> rewardIds = new HashSet<>(MountainRiverTournamentService.getTaskRewardIdMap().getOrDefault(MRTTaskRewardTypeEnums.dailyTask, new HashSet<>()));
        if(CollectionUtil.isNotEmpty(rewardIds)){
            if(rewardIds.size() != hadReceiveDailyRewardId.size()){
                if(CollectionUtil.isNotEmpty(rewardIds)){
                    List<RewardTemplate> allRewards = new ArrayList<>();
                    for (Integer rewardId : rewardIds) {
                        if(!hadReceiveDailyRewardId.contains(rewardId)){
                            hadReceiveDailyRewardId.add(rewardId);
                            MRTTaskRewardTemplate taskReward = MountainRiverTournamentService.getTaskReward(rewardId);
                            if(taskReward.conditionType.canReceive(taskReward, this)){
                                allRewards.addAll(taskReward.rewardGroup);
                            }
                        }
                    }
                    if(CollectionUtil.isNotEmpty(allRewards)){
                        MailType mailType = MailType.jhqxDailyReward;
                        PbCommons.PbText title = Text.genText(mailType.getTitleId()).build();
                        PbCommons.PbText content = Text.genText(mailType.getContentId()).build();
                        MailManager.sendMail(mailType, getPlayerId(), title, content, ServerConstants.getCurrentTimeMillis(), Reward.templateCollectionToReward(allRewards));
                    }
                }
            }
        }
        isFirstFail = false;
        todayPoint = -1;
        todayBattleTimes = 0;
        todayWinTimes = 0;
        todayReceiveFengcailu = false;
        hadReceiveDailyRewardId.clear();
        hadPraisePlayerIds.clear();
    }

    /**
     * 检查赛季奖励
     * @param seasonId 赛季id
     * @param endPoint 赛季结束时积分
     */
    public void checkSeasonReward(int seasonId, int endPoint){
        MRTPointTemplate pointTemp = MountainRiverTournamentService.getPointTemp(endPoint);
        Set<Integer> rewardIds = new HashSet<>(MountainRiverTournamentService.getTaskRewardIdMap().get(MRTTaskRewardTypeEnums.season));
        List<Integer> hadReceiveIds = hadReceiveSeasonRewardId.getOrDefault(seasonId, new ArrayList<>());
        if(hadReceiveIds.size() != rewardIds.size()){
            if(CollectionUtil.isNotEmpty(rewardIds)){
                List<RewardTemplate> allRewards = new ArrayList<>();
                for (Integer rewardId : rewardIds) {
                    if(!hadReceiveIds.contains(rewardId)){
                        MRTTaskRewardTemplate taskReward = MountainRiverTournamentService.getTaskReward(rewardId);
                        if(taskReward.conditionType.canReceive(taskReward, pointTemp.id)){
                            allRewards.addAll(taskReward.rewardGroup);
                        }
                    }
                }
                if(CollectionUtil.isNotEmpty(allRewards)){
                    MailType mailType = MailType.jhqxTierReward;
                    PbCommons.PbText title = Text.genText(mailType.getTitleId()).build();
                    PbCommons.PbText content = Text.genText(mailType.getContentId()).build();
                    MailManager.sendMail(mailType, getPlayerId(), title, content, ServerConstants.getCurrentTimeMillis(), Reward.templateCollectionToReward(allRewards));
                }
            }
        }

    }

    public int getTodayBattleTimes() {
        return todayBattleTimes;
    }

    public void setTodayBattleTimes(int todayBattleTimes) {
        this.todayBattleTimes = todayBattleTimes;
    }


    public int getLoseInfo() {
        return loseInfo;
    }

    public int getTodayWinTimes() {
        return todayWinTimes;
    }

    public void setTodayWinTimes(int todayWinTimes) {
        this.todayWinTimes = todayWinTimes;
    }

    public double getTodayPoint() {
        return todayPoint;
    }

    public void setTodayPoint(double todayPoint) {
        this.todayPoint = todayPoint;
    }

    public void setLoseInfo(int loseInfo) {
        this.loseInfo = loseInfo;
    }

    public boolean isFirstFail() {
        return isFirstFail;
    }

    public void setFirstFail(boolean firstFail) {
        isFirstFail = firstFail;
    }

    public int getBattleTimes() {
        return battleTimes;
    }

    public void setBattleTimes(int battleTimes) {
        this.battleTimes = battleTimes;
    }

    public int getWinTimes() {
        return winTimes;
    }

    public void setWinTimes(int winTimes) {
        this.winTimes = winTimes;
    }

    public boolean isTodayReceiveFengcailu() {
        return todayReceiveFengcailu;
    }

    public void setTodayReceiveFengcailu(boolean todayReceiveFengcailu) {
        this.todayReceiveFengcailu = todayReceiveFengcailu;
    }

    public List<Integer> getHadReceiveDailyRewardId() {
        return hadReceiveDailyRewardId;
    }

    public Map<Integer, List<Integer>> getHadReceiveSeasonRewardId() {
        return hadReceiveSeasonRewardId;
    }

    public void setHadReceiveSeasonRewardId(Map<Integer, List<Integer>> hadReceiveSeasonRewardId) {
        this.hadReceiveSeasonRewardId = hadReceiveSeasonRewardId;
    }

    public void setHadReceiveDailyRewardId(List<Integer> hadReceiveRewardId) {
        this.hadReceiveDailyRewardId = hadReceiveRewardId;
    }

    public List<Long> getHadPraisePlayerIds() {
        return hadPraisePlayerIds;
    }

    public void setHadPraisePlayerIds(List<Long> hadPraisePlayerIds) {
        this.hadPraisePlayerIds = hadPraisePlayerIds;
    }

    @Override
    protected void loadData(PlayerBlobDb playerBlob) {
        PlayerMRTModelDb mrtModelDb = playerBlob.getMrtModelDb();
        if(Objects.nonNull(mrtModelDb)){
            this.battleTimes = mrtModelDb.getBattleTimes();
            this.winTimes = mrtModelDb.getWinTimes();
            this.todayBattleTimes = mrtModelDb.getTodayBattleTimes();
            this.todayWinTimes = mrtModelDb.getTodayWinTimes();
            this.isFirstFail = mrtModelDb.isFirstFail();
            this.todayPoint = mrtModelDb.getTodayPoint();
            this.loseInfo = mrtModelDb.getLoseInfo();
            this.todayReceiveFengcailu = mrtModelDb.isTodayReceiveFengcailu();
            this.hadReceiveDailyRewardId = mrtModelDb.getHadReceiveDailyRewardId();
            Map<Integer, RedisListIntegerBean> hadReceiveSeasonRewardId = mrtModelDb.getHadReceiveSeasonRewardId();
            hadReceiveSeasonRewardId.forEach((k, v) -> this.hadReceiveSeasonRewardId.put(k, v.getList()));
            this.hadPraisePlayerIds = mrtModelDb.getHadPraisePlayerIds();
        }
    }

    @Override
    protected void saveData(PlayerBlobDb playerBlob) {
        PlayerMRTModelDb mrtModelDb = new PlayerMRTModelDb();
        mrtModelDb.setBattleTimes(battleTimes);
        mrtModelDb.setWinTimes(winTimes);
        mrtModelDb.setTodayBattleTimes(todayBattleTimes);
        mrtModelDb.setTodayWinTimes(todayWinTimes);
        mrtModelDb.setFirstFail(isFirstFail);
        mrtModelDb.setTodayPoint(todayPoint);
        mrtModelDb.setLoseInfo(loseInfo);
        mrtModelDb.setTodayReceiveFengcailu(todayReceiveFengcailu);
        mrtModelDb.getHadPraisePlayerIds().addAll(hadPraisePlayerIds);
        hadReceiveSeasonRewardId.forEach((k, v) -> {
            RedisListIntegerBean bean = new RedisListIntegerBean();
            mrtModelDb.getHadReceiveSeasonRewardId().put(k, bean);
        });
        mrtModelDb.getHadPraisePlayerIds().addAll(hadPraisePlayerIds);

        playerBlob.setMrtModelDb(mrtModelDb);
    }

    @Override
    public PlayerEventType[] getEventTypes() {
        return new PlayerEventType[]{PlayerEventType.day5Refresh, PlayerEventType.loginAfter};
    }

    @Override
    public void handle(PlayerEvent event) {
        switch (event.getEventType()){
            case day5Refresh:{
                refresh();
                break;
            }
            case loginAfter:{
                ThreadPool.execute(new MRTReissueLevelAsync(getPlayer()));
                break;
            }
        }
    }
}
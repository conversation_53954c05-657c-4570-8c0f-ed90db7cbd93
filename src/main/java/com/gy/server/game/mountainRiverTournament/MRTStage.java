package com.gy.server.game.mountainRiverTournament;

import com.gy.server.core.Configuration;
import com.gy.server.game.attribute.AttributeKey;
import com.gy.server.game.attribute.Attributes;
import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.combat.StageType;
import com.gy.server.game.combat.unit.Camp;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.combat.unit.HeroUnitCreator;
import com.gy.server.game.combat.unit.TeamUnit;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.lineup.bean.LineupInfoBean;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.mountainRiverTournament.bean.MRTCombatRecord;
import com.gy.server.game.mountainRiverTournament.bean.MRTInfo;
import com.gy.server.game.mountainRiverTournament.bean.MRTPlayEnums;
import com.gy.server.game.mountainRiverTournament.bean.MRTSpecialReportEnums;
import com.gy.server.game.mountainRiverTournament.template.MRTConstant;
import com.gy.server.game.mountainRiverTournament.template.MRTPointTemplate;
import com.gy.server.game.mountainRiverTournament.template.MRTSeasonTemplate;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.game.record.RecordManager;
import com.gy.server.game.record.RecordType;
import com.gy.server.game.record.async.RecordAddAsync;
import com.gy.server.game.record.combat.CombatRecord;
import com.gy.server.game.robot.bean.MultiTeamRobot;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbRecord;
import com.gy.server.packet.PbRobot;
import com.ttlike.server.tl.baselib.thread.ThreadPool;
import com.ttlike.server.tl.baselib.util.StringConcat;

import java.util.*;

/**
 * 剑会群雄-逸侠对决战斗
 * @author: gbk
 * @date: 2024-11-21 14:37
 */
public class MRTStage extends AbstractStage {

    Player player;
    MultiTeamRobot robot;
    List<LineupInfoBean> atkLineups;
    List<LineupInfoBean> defLineups;
    MiniGamePlayer defMiniPlayer;
    double defPoint;//防守方积分
    double myPoint;//当前自己的积分
    int warZoneId;
    MRTInfo info;
    private List<Boolean> atkWinScores = new ArrayList<>();
//    private List<Boolean> defWinScores = new ArrayList<>();
    Map<Integer, Boolean> hasReportInfos = new HashMap<>();
    int battleCollectId;
    LineupType lineupType;
    StageType stageType;

    //机器人
    public MRTStage(MRTInfo info, int warZoneId, Player player, List<LineupInfoBean> atkLineups
            , double myPoint, double defPoint, MultiTeamRobot robot
            , LineupType lineupType, StageType stageType, int battleCollectId){
        this.player = player;
        this.robot = robot;
        this.atkLineups = atkLineups;
        this.myPoint = myPoint;
        this.defPoint = defPoint;
        this.warZoneId = warZoneId;
        this.info = info;
        this.lineupType = lineupType;
        this.stageType = stageType;
        this.battleCollectId = battleCollectId;
    }
    //玩家
    public MRTStage(MRTInfo info, int warZoneId, Player player, List<LineupInfoBean> atkLineups
            , double myPoint, MiniGamePlayer defMiniPlayer, List<LineupInfoBean> defLineups
            , double defPoint, Map<Integer, Boolean> hasReportInfos
            , LineupType lineupType, StageType stageType, int battleCollectId){
        this.player = player;
        this.atkLineups = atkLineups;
        this.defMiniPlayer = defMiniPlayer;
        this.defLineups = defLineups;
        this.defPoint = defPoint;
        this.myPoint = myPoint;
        this.warZoneId = warZoneId;
        this.info = info;
        this.hasReportInfos = hasReportInfos;
        this.lineupType = lineupType;
        this.stageType = stageType;
        this.battleCollectId = battleCollectId;
    }

    @Override
    public void init() {


        List<TeamUnit> atkTeamUnits = new ArrayList<>();
        //获取赛季buff
        MRTSeasonTemplate seasonTemp = MountainRiverTournamentService.getSeasonTemp(info.getSeasonId());
        //进攻布阵
        for (LineupInfoBean atkLineup : atkLineups) {
            Map<Integer, HeroUnitCreator> mirrorStandsMap = atkLineup.getMirrorInfo().getMirrorStandsMap();
            List<HeroUnit> atkUnits = new ArrayList<>();
            mirrorStandsMap.forEach((k, v) ->{
                HeroUnit heroUnit = v.create();
                heroUnit.setPosIndex(k);
                //增加赛季buff
                heroUnit.addBuff(seasonTemp.buffId, 1);
                atkUnits.add(heroUnit);
            });
            TeamUnit atkTeam = new TeamUnit(getNewId(), atkUnits);
            atkTeam.setAutoMode(player.getPlayerId());
            atkTeamUnits.add(atkTeam);
        }
        //防守布阵
        List<TeamUnit> defTeamUnits = new ArrayList<>();
        if(Objects.nonNull(defMiniPlayer)){
            //真人布阵
            for (LineupInfoBean defLineup : defLineups) {
                Map<Integer, HeroUnitCreator> mirrorStandsMap = defLineup.getMirrorInfo().getMirrorStandsMap();
                List<HeroUnit> defUnits = new ArrayList<>();
                mirrorStandsMap.forEach((k, v) ->{
                    HeroUnit heroUnit = v.create();
                    heroUnit.setPosIndex(k);
                    defUnits.add(heroUnit);
                });
                TeamUnit defTeam = new TeamUnit(getNewId(), defUnits);
                defTeam.setAutoMode(defMiniPlayer.getPlayerId());
                defTeamUnits.add(defTeam);
            }
        }else{
            //机器人
            defTeamUnits.addAll(robot.createTeamUnits(this));
        }

        init(battleCollectId, stageType, atkTeamUnits, defTeamUnits, lineupType );
    }

    @Override
    public void afterFinish() {
        PbProtocol.CombatSettlementNotify.Builder rst = genCombatSettlement();
        MRTPointTemplate curPointTemp = MountainRiverTournamentService.getPointTemp((int) myPoint);
        MRTPointTemplate defPointTemp = MountainRiverTournamentService.getPointTemp((int) defPoint);
        PlayerMRTModel playerMRTModel = player.getModel(PlayerModelEnums.mrt);
        double diffPoint = myPoint - defPoint;
        int atkPointModify = 0;
        int defPointModify = 0;

        StringConcat sc = new StringConcat(",");
        sc.add(" iwWin : " + isWin());
        sc.add(", atkPoint : " + myPoint);
        sc.add(", defPoint : " + defPoint);
        if(isWin() && diffPoint >= curPointTemp.victoryProtection){
            //大于差值，不获得积分
            sc.add(", victoryProtection : " + diffPoint);
        }else{
            //计算胜负方积分
            //分差，进攻减防守
            int atkDiffAddedCount = MountainRiverTournamentService.getDiffAddedCount(curPointTemp.id, myPoint - defPoint);
            int defDiffAddedCount = MountainRiverTournamentService.getDiffAddedCount(defPointTemp.id, defPoint - myPoint);
            sc.add(", atkDiffAddedCount : " + atkDiffAddedCount);
            sc.add(", defDiffAddedCount : " + defDiffAddedCount);
            if(isWin()){
                //计算积分
                atkPointModify = curPointTemp.winCount + atkDiffAddedCount;
                defPointModify = defPointTemp.loseCount + defDiffAddedCount;
            }else{
                //减少积分
                atkPointModify = curPointTemp.loseCount + atkDiffAddedCount;
                defPointModify = defPointTemp.winCount + defDiffAddedCount;
            }
            if(atkPointModify < 0 && !playerMRTModel.isFirstFail()){
                //首次失败不会扣分
                playerMRTModel.setFirstFail(true);
                atkPointModify = 0;
                sc.add(", firstFail : 1");
            }
            //进攻方增加积分
            if(atkPointModify != 0){
                MRTHelper.modifyPlayerPoint(warZoneId, player.getPlayerId(), atkPointModify);
            }

            if(Objects.nonNull(defMiniPlayer) && defPointModify != 0){
                MRTHelper.modifyPlayerPoint(warZoneId, defMiniPlayer.getPlayerId(), defPointModify);
            }
        }
        GameLogger.mrtInfo(-1, "战斗结算: " + sc.toString());

        if(playerMRTModel.getTodayPoint() <= 0){
            playerMRTModel.setTodayPoint(myPoint);
        }
        playerMRTModel.addBattleTimes(isWin);

        //计算战报
        RecordType recordType = info.getPlayType() == MRTPlayEnums.P6V6.getType() ? RecordType.SixWayTournament : RecordType.ThreeWayTournament;
        long recordId = 0;
        CombatRecord combatRecord;
        PbRecord.RecordPlayer atkRecordPlayer = RecordManager.genRecordPlayer(player, lineupType, getAtks()).build();
        PbRecord.RecordPlayer defRecordPlayer = null;
        PbRobot.MultiTeamRobot robotInfo = null;
        if (Objects.nonNull(defLineups)) {
            //玩家
            defRecordPlayer = RecordManager.genRecordPlayer(defMiniPlayer, lineupType, getDefs()).build();
            combatRecord = CombatRecord.create(this, player.getMiniPlayer(), defMiniPlayer
                    , CombatRecord.OverdueTime.MRT, recordType.getId());
            //增加防守方战报
            MRTCombatRecord defRecord = new MRTCombatRecord(combatRecord.getCosKey(), !isWin, recordType, combatRecord.getId()
                    , robotInfo, atkRecordPlayer, defRecordPlayer, atkPointModify, defPointModify
                    , (int) myPoint, (int) defPoint, atkWinScores);
            ThreadPool.execute(new RecordAddAsync(defMiniPlayer.getPlayerId(), defRecord));
        } else {
            //机器人
            combatRecord = CombatRecord.create(this, player.getMiniPlayer()
                    , robot.genMiniUser(lineupType), false
                    , CombatRecord.OverdueTime.MRT, recordType.getId());
            robotInfo = robot.genMultiTeamPb();

        }
        //增加进攻方战报
        MRTCombatRecord atkRecord = new MRTCombatRecord(combatRecord.getCosKey(), isWin, recordType, combatRecord.getId()
                , robotInfo, atkRecordPlayer, defRecordPlayer, atkPointModify, defPointModify
                , (int) myPoint, (int) defPoint, atkWinScores);
        RecordManager.addRecord(atkRecord, player);
        RecordManager.save(combatRecord);
        rst.setMRTSettlement(PbProtocol.MRTSettlement.newBuilder().setRecord(atkRecord.buildPb()).build());

        notifyCombatSettlement(player, rst.build());
        //胜利并且是打真人，计算事件
        if(isWin() && Objects.nonNull(defLineups)){
            long atkFightPower = getFightPower(atkLineups);
            long defFightPower = getFightPower(defLineups);
            //1.一天上分
            MRTSpecialReportEnums specialReportEnums = MRTSpecialReportEnums.勤奋过人;
            if(!hasReportInfos.containsKey(specialReportEnums.getId()) || MountainRiverTournamentService.canEnterRecord(specialReportEnums.getId(), atkFightPower, defFightPower)){
                double newMyPoint = atkPointModify + myPoint;
                double todayAddedPoint = newMyPoint - playerMRTModel.getTodayPoint();
                MRTHelper.tryEnterSpecialReport(warZoneId, player.getPlayerId(), recordId, specialReportEnums.getId()
                        , Configuration.serverId, recordType.getId(), (int)todayAddedPoint);
            }
            //2.每天胜率
            specialReportEnums = MRTSpecialReportEnums.运筹帷幄;
            if(!hasReportInfos.containsKey(specialReportEnums.getId()) || MountainRiverTournamentService.canEnterRecord(specialReportEnums.getId(), atkFightPower, defFightPower)){
                MRTHelper.tryEnterSpecialReport(warZoneId, player.getPlayerId(), recordId, specialReportEnums.getId()
                        , Configuration.serverId, recordType.getId(), playerMRTModel.getTodayBattleTimes()
                        , playerMRTModel.getTodayWinTimes());
            }
            //3.血量最低
            specialReportEnums = MRTSpecialReportEnums.千钧一发;
            if(!hasReportInfos.containsKey(specialReportEnums.getId()) || MountainRiverTournamentService.canEnterRecord(specialReportEnums.getId(), atkFightPower, defFightPower)){
                long allMaxHp = 0L;
                long allCurHp = 0L;
                for (TeamUnit atk : getAtks()) {
                    for (HeroUnit heroUnit : atk.getFormation()) {
                        if(Objects.nonNull(heroUnit)){
                            Attributes attributes = heroUnit.getAttributes();
                            allCurHp += attributes.getHp();
                            allMaxHp += attributes.getValue(AttributeKey.最大生命);
                        }
                    }
                }
                MRTHelper.tryEnterSpecialReport(warZoneId, player.getPlayerId(), recordId, specialReportEnums.getId()
                        , Configuration.serverId, recordType.getId(),allCurHp, allMaxHp);
            }
            //4.最大血量
            specialReportEnums = MRTSpecialReportEnums.势如破竹;
            if(!hasReportInfos.containsKey(specialReportEnums.getId()) || MountainRiverTournamentService.canEnterRecord(specialReportEnums.getId(), atkFightPower, defFightPower)){
                long allMaxHp = 0L;
                long allCurHp = 0L;
                for (TeamUnit atk : getAtks()) {
                    for (HeroUnit heroUnit : atk.getFormation()) {
                        if(Objects.nonNull(heroUnit)){
                            Attributes attributes = heroUnit.getAttributes();
                            allCurHp += attributes.getHp();
                            allMaxHp += attributes.getValue(AttributeKey.最大生命);
                        }
                    }
                }
                MRTHelper.tryEnterSpecialReport(warZoneId, player.getPlayerId(), recordId, specialReportEnums.getId()
                        , Configuration.serverId, recordType.getId(),allCurHp, allMaxHp);
            }
            //5.战力差
            specialReportEnums = MRTSpecialReportEnums.天赋异禀;
            if(!hasReportInfos.containsKey(specialReportEnums.getId()) || MountainRiverTournamentService.canEnterRecord(specialReportEnums.getId(), atkFightPower, defFightPower)){
                if(defFightPower > atkFightPower){
                    MRTHelper.tryEnterSpecialReport(warZoneId, player.getPlayerId(), recordId, specialReportEnums.getId()
                            , Configuration.serverId, recordType.getId(), atkFightPower, defFightPower);
                }
            }
        }

    }

    public long getFightPower(List<LineupInfoBean> lineupInfoBeans){
        long fightPower = 0L;
        for (LineupInfoBean atkLineup : lineupInfoBeans) {
            Map<Integer, HeroUnitCreator> mirrorStandsMap = atkLineup.getMirrorInfo().getMirrorStandsMap();
            for (HeroUnitCreator creator : mirrorStandsMap.values()) {
                fightPower += creator.getFightingPower();
            }
        }
        return fightPower;
    }

    /**
     * 三队胜利条件 胜利场数大于失败场数
     */
    @Override
    protected void judgeWinner() {
        List<TeamUnit> defList = this.getDefs();
        if (defList == null) {
            this.isWin = true;
            return;
        }
        int winnerNum = 0;
        for (TeamUnit def : defList) {
            boolean isWin = def.isAllFail();
            atkWinScores.add(isWin);
//            defWinScores.add(!isWin);
            // 守方全挂
            if (isWin) {
                winnerNum++;
            }
        }
        this.isWin = winnerNum > defList.size() - winnerNum;
    }

    /**
     * 阵容切换 一队打一队  二队打二队 三队打三队
     */
    @Override
    public List<Camp> changeTeam() {
        List<Camp> list = new ArrayList<>();
        for (Camp camp : getCamps()) {
            list.add(camp);
            camp.setCurrentTeamIndex(camp.getCurrentTeamIndex() + 1);
        }
        return list;
    }

    /**
     * 小节是否可以初始化
     */
    @Override
    public boolean canInitSection() {
        Camp atkCamp = getAtkCamp();
        int currentTeamIndex = atkCamp.getCurrentTeamIndex();
        List<TeamUnit> teams = atkCamp.getTeams();
        if (currentTeamIndex >= teams.size() - 1) {
            return false;
        }
        return super.canInitSection();
    }

}
package com.gy.server.game.mountainRiverTournament.template;

import com.gy.server.common.base.AbsTemplate;

import java.util.Map;

/**
 * 机器人数据
 * @author: gbk
 * @date: 2024-11-21 15:16
 */
public class MRTRobotTemplate implements AbsTemplate {

    public int id;
    //积分
    public double point;
    //npcId
    public int npcId;
    //阵容三队
    public int battleCollectId1;
    public int battleCollectId2;
    public int battleCollectId3;
    //等级
    public int level;

    @Override
    public void readTxt(Map<String, String> map) {
        this.id = Integer.parseInt(map.get("robotId"));
        this.point = Double.parseDouble(map.get("points"));
        this.npcId = Integer.parseInt(map.get("npcId"));
        this.battleCollectId1 = Integer.parseInt(map.get("battleCollectId1"));
        this.battleCollectId2 = Integer.parseInt(map.get("battleCollectId2"));
        this.battleCollectId3 = Integer.parseInt(map.get("battleCollectId3"));
        this.level = Integer.parseInt(map.get("level"));
    }
}
package com.gy.server.game.mountainRiverTournament.async;

import com.gy.server.common.redis.bean.RedisStringBean;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.game.mountainRiverTournament.GsMRTCommandService;
import com.gy.server.game.mountainRiverTournament.PlayerMRTModel;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.utils.StringUtil;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.thread.ThreadPool;
import redis.clients.util.SafeEncoder;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 补发赛季结束段位检查
 * @author: gbk
 * @date: 2024-11-29 17:57
 */
public class MRTReissueLevelAsync extends AbsMRTAsync{

    Player player;
    String pointInfo;
    String redisKey = GsRedisKey.MountainRiverTournament.reissue_Level.getRedisKey();
    public MRTReissueLevelAsync(Player player){
        this.player = player;
    }

    @Override
    public void asyncExecute() {
        RedisStringBean redisStringBean = TLBase.getInstance().getRedisAssistant().hashGetBeans(redisKey, player.getPlayerId() + "", RedisStringBean.class);
        pointInfo = Objects.isNull(redisStringBean) ? null : redisStringBean.getParam();
    }

    @Override
    public void execute() {
        if(Objects.nonNull(pointInfo) && !pointInfo.isEmpty()){
            int[] ints = StringUtil.splitToIntArray(pointInfo, GsMRTCommandService.separator);
            int point = ints[0];
            int seasonId = ints[1];
            PlayerMRTModel mrtModel = player.getModel(PlayerModelEnums.mrt);
            mrtModel.checkSeasonReward(seasonId, point);

            //删除这个Key
            ThreadPool.execute(()-> TLBase.getInstance().getRedisAssistant().hashRemove(redisKey, player.getPlayerId() + ""));
        }
    }
}
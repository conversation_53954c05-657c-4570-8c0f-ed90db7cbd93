package com.gy.server.game.mountainRiverTournament.bean;

import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.PlayerHelper;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.serialize.RedisAssistant;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 特殊事件类型
 * @author: gbk
 * @date: 2024-11-22 17:18
 */
public enum MRTSpecialReportEnums {

    勤奋过人(1, new QinfenMRTEnterRecordDeal()),
    运筹帷幄(2, new YunchouMRTEnterRecordDeal()),
    千钧一发(3, new QianjunMRTEnterRecordDeal()),
    势如破竹(4, new ShiruMRTEnterRecordDeal()),
    天赋异禀(5, new TianfuMRTEnterRecordDeal()),

    ;
    int id;

    public int minPowerRadius;
    public int maxPowerRadius;
    MRTEnterRecordDeal deal;

    MRTSpecialReportEnums(int id, MRTEnterRecordDeal deal){
        this.id = id;
        this.deal = deal;
    }

    public static Map<Integer, MRTSpecialReportEnums> enumsMap = new HashMap<>();
    static{
        for (MRTSpecialReportEnums enums : MRTSpecialReportEnums.values()) {
            enumsMap.put(enums.id, enums);
        }
    }

    public int getId() {
        return id;
    }

    public static MRTSpecialReportEnums getById(int id){
        return enumsMap.get(id);
    }

    public int getMinPowerRadius() {
        return minPowerRadius;
    }

    public void setMinPowerRadius(int minPowerRadius) {
        this.minPowerRadius = minPowerRadius;
    }

    public int getMaxPowerRadius() {
        return maxPowerRadius;
    }

    public void setMaxPowerRadius(int maxPowerRadius) {
        this.maxPowerRadius = maxPowerRadius;
    }

    /**
     * 能否进入记录
     * @param atkPower 进攻方战力
     * @param defPower 防守方真理
     * @return 能够否进入记录
     */
    public boolean canEnterRecord(long atkPower, long defPower){
        int powerRadius =(int)(Math.abs(atkPower - defPower) * 10000L / atkPower);
        return minPowerRadius <= powerRadius && maxPowerRadius >= powerRadius;
    }

    /**
     * 尝试进入记录
     * @param redisKey redis的key
     * @param oldReportInfo 旧记录信息（需要对比）
     * @param playerId 玩家id
     * @param recordId 战报Id
     * @param param 相关参数
     */
    public void tryEnterRecord(String redisKey, MRTSpecialReportInfo oldReportInfo, long playerId,
                               int serverId, int recordType, long recordId, Object... param){
        if(deal.canEnter(oldReportInfo, param)){
            ThreadPool.execute(()->{
                MRTSpecialReportInfo recordInfo = build(playerId, this.id, recordId, serverId, recordType);
                deal.fillInfo(recordInfo, param);
                RedisAssistant ra = TLBase.getInstance().getRedisAssistant();
                ra.hashPut(redisKey, recordInfo.getId() + "", recordInfo);
            });
        }
    }

    private static MRTSpecialReportInfo build(long playerId, int srId, long recordId, int serverId, int recordType){
        MiniGamePlayer miniPlayer = PlayerHelper.getMiniPlayer(playerId);
        MRTSpecialReportInfo newInfo = new MRTSpecialReportInfo();
        newInfo.setId(srId);
        newInfo.setMiniPlayer(miniPlayer);
        newInfo.setRecordId(recordId);
        newInfo.setServerId(serverId);
        newInfo.setRecordType(recordType);
        return newInfo;
    }

    private static interface MRTEnterRecordDeal{
        boolean canEnter(MRTSpecialReportInfo oldReportInfo, Object... param);
        void fillInfo(MRTSpecialReportInfo recordInfo, Object... param);
    }

    private static class QinfenMRTEnterRecordDeal implements MRTEnterRecordDeal {

        @Override
        public boolean canEnter(MRTSpecialReportInfo oldReportInfo, Object... param) {
            if(Objects.isNull(oldReportInfo)){
                return true;
            }
            int oldPoint = Integer.parseInt(oldReportInfo.getShowContent());
            int myPoint = Integer.parseInt(param[0].toString());
            return myPoint > oldPoint;
        }

        @Override
        public void fillInfo(MRTSpecialReportInfo recordInfo, Object... param) {
            int myPoint = Integer.parseInt(param[0].toString());
            recordInfo.setShowContent(myPoint + "");
        }
    }

    private static class YunchouMRTEnterRecordDeal implements MRTEnterRecordDeal {
        @Override
        public boolean canEnter(MRTSpecialReportInfo oldReportInfo, Object... param) {
            if(Objects.isNull(oldReportInfo)){
                return true;
            }
            double todayBattleTime = Double.parseDouble(param[0].toString());
            double todayWinTimes = Double.parseDouble(param[1].toString());
            if(todayWinTimes / todayBattleTime == Double.parseDouble(oldReportInfo.getShowContent())){
                return (int)todayWinTimes > oldReportInfo.getCombatTimes();
            }
            return todayWinTimes / todayBattleTime > Double.parseDouble(oldReportInfo.getShowContent());
        }

        @Override
        public void fillInfo(MRTSpecialReportInfo recordInfo, Object... param) {
            double todayBattleTime = Double.parseDouble(param[0].toString());
            double todayWinTimes = Double.parseDouble(param[1].toString());
            recordInfo.setShowContent((todayWinTimes / todayBattleTime) + "");
            recordInfo.setCombatTimes((int)todayBattleTime);
        }
    }

    private static class QianjunMRTEnterRecordDeal implements MRTEnterRecordDeal {
        @Override
        public boolean canEnter(MRTSpecialReportInfo oldReportInfo, Object... param) {
            if(Objects.isNull(oldReportInfo)){
                return true;
            }
            double allMaxHp = Double.parseDouble(param[0].toString());
            double allCurHp = Double.parseDouble(param[1].toString());
            return allCurHp / allMaxHp < Double.parseDouble(oldReportInfo.getShowContent());
        }

        @Override
        public void fillInfo(MRTSpecialReportInfo recordInfo, Object... param) {
            double allMaxHp = Double.parseDouble(param[0].toString());
            double allCurHp = Double.parseDouble(param[1].toString());
            recordInfo.setShowContent((allCurHp / allMaxHp) + "");
        }
    }

    private static class ShiruMRTEnterRecordDeal implements MRTEnterRecordDeal {
        @Override
        public boolean canEnter(MRTSpecialReportInfo oldReportInfo, Object... param) {
            if(Objects.isNull(oldReportInfo)){
                return true;
            }
            double allMaxHp = Double.parseDouble(param[0].toString());
            double allCurHp = Double.parseDouble(param[1].toString());
            return allCurHp / allMaxHp > Double.parseDouble(oldReportInfo.getShowContent());
        }

        @Override
        public void fillInfo(MRTSpecialReportInfo recordInfo, Object... param) {
            double allMaxHp = Double.parseDouble(param[0].toString());
            double allCurHp = Double.parseDouble(param[1].toString());
            recordInfo.setShowContent((allCurHp / allMaxHp) + "");
        }
    }

    private static class TianfuMRTEnterRecordDeal implements MRTEnterRecordDeal {
        @Override
        public boolean canEnter(MRTSpecialReportInfo oldReportInfo, Object... param) {
            if(Objects.isNull(oldReportInfo)){
                return true;
            }
            long atkFightPower = Long.parseLong(param[0].toString());
            long defFightPower = Long.parseLong(param[1].toString());
            return defFightPower - atkFightPower > Long.parseLong(oldReportInfo.getShowContent());
        }

        @Override
        public void fillInfo(MRTSpecialReportInfo recordInfo, Object... param) {
            long atkFightPower = Long.parseLong(param[0].toString());
            long defFightPower = Long.parseLong(param[1].toString());
            recordInfo.setShowContent((defFightPower - atkFightPower) + "");
        }
    }
}
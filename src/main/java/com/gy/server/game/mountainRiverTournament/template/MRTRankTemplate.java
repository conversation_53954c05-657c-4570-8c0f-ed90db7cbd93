package com.gy.server.game.mountainRiverTournament.template;

import com.gy.server.common.base.AbsTemplate;
import com.gy.server.utils.StringUtil;

import java.util.Map;

/**
 * @author: gbk
 * @date: 2024-11-14 16:39
 */
public class MRTRankTemplate implements AbsTemplate {

    @Override
    public void readTxt(Map<String, String> map) {
        int[] ints = StringUtil.splitToIntArray(map.get("Ranking"), "\\|");
        this.minRank = ints[0];
        if(ints.length == 1){
            this.maxRank = ints[0];
        }else{
            this.maxRank = ints[1];
        }
        output = Integer.parseInt(map.get("output"));
    }

    public int minRank;
    public int maxRank;
    //产出
    public int output;



}
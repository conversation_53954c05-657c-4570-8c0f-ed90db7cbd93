package com.gy.server.game.mountainRiverTournament.reward;

import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.mountainRiverTournament.reward.impl.BattleTimesRewardCheck;
import com.gy.server.game.mountainRiverTournament.reward.impl.LevelRewardCheck;
import com.gy.server.game.mountainRiverTournament.reward.impl.WinTimesRewardCheck;
import com.gy.server.game.player.Player;
import com.gy.server.packet.PbCommons;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 任务奖励-条件类型
 * @author: gbk
 * @date: 2024-11-26 20:18
 */
public enum MRTTaskRewardConditionEnums {

    /**
     * 段位
     */
    level(1, new LevelRewardCheck()),

    /**
     * 战斗次数
     */
    battleTimes(2, new BattleTimesRewardCheck()),

    /**
     * 胜利次数
     */
    winTimes(3, new WinTimesRewardCheck()),

    ;

    int id;
    IRewardCheck check;
    MRTTaskRewardConditionEnums(int id, IRewardCheck check){
        this.id = id;
        this.check = check;
    }

    public <R, I> boolean canReceive(R r, I i){
        return check.canReceive(r, i);
    }

    public <R> List<PbCommons.PbReward> addToPlayer(Player player, R r, BehaviorType behaviorType){
        return check.addToPlayer(player, r, behaviorType);
    }

    private static Map<Integer, MRTTaskRewardConditionEnums> enums = new HashMap<>();
    static{
        for (MRTTaskRewardConditionEnums typeEnums : MRTTaskRewardConditionEnums.values()) {
            enums.put(typeEnums.id,typeEnums);
        }
    }

    public static MRTTaskRewardConditionEnums getById(int id){
        return enums.get(id);
    }

}
package com.gy.server.game.mountainRiverTournament.bean;

import com.gy.server.game.record.Record;
import com.gy.server.game.record.RecordHelper;
import com.gy.server.game.record.RecordType;
import com.gy.server.game.robot.bean.MultiTeamRobot;
import com.gy.server.packet.PbRecord;
import com.gy.server.packet.PbRobot;
import com.ttlike.server.tl.baselib.serialize.record.MRTCombatRecordDb;
import com.ttlike.server.tl.baselib.serialize.record.RecordDb;

import java.util.*;

/**
 * 战报信息
 * @author: gbk
 * @date: 2024-11-27 17:55
 */
public class MRTCombatRecord extends Record {

    private PbRobot.MultiTeamRobot robotInfo;
    private PbRecord.RecordPlayer atkUser;//进攻者信息
    private PbRecord.RecordPlayer defUser;//对手信息
    int atkModifyPoint;
    int defModifyPoint;

    int atkPoint;
    int defPoint;
    List<Boolean> winScores = new ArrayList<>();

    public MRTCombatRecord(){}

    public MRTCombatRecord(String cosKey, boolean isWin, RecordType type, long combatRecordId, PbRobot.MultiTeamRobot robotInfo,
                           PbRecord.RecordPlayer atkRecordPlayer,
                           PbRecord.RecordPlayer defRecordPlayer, int atkModifyPoint, int defModifyPoint,
                           int atkPoint, int defPoint, List<Boolean> winScores) {
        super(type, isWin, combatRecordId, cosKey);
        this.robotInfo = robotInfo;
        this.atkUser = atkRecordPlayer;
        this.defUser = defRecordPlayer;
        this.atkModifyPoint = atkModifyPoint;
        this.defModifyPoint = defModifyPoint;
        this.atkPoint = atkPoint;
        this.defPoint = defPoint;
        this.winScores = winScores;
    }

    @Override
    protected void subReadFromPb(PbRecord.Record record) {
        PbRecord.MRTCombatRecord mrtRecord = record.getMrtRecord();
        this.robotInfo = mrtRecord.getRobotInfo();
        this.atkUser = mrtRecord.getAtk();
        this.defUser = mrtRecord.getDef();
        this.atkModifyPoint = mrtRecord.getAtkModifyPoint();
        this.defModifyPoint = mrtRecord.getDefModifyPoint();
        this.atkPoint = mrtRecord.getAtkPoint();
        this.defPoint = mrtRecord.getDefPoint();
        this.winScores = mrtRecord.getWinScoresList();
    }

    @Override
    protected void subWriteToPb(PbRecord.Record.Builder builder) {
        builder.setMrtRecord(buildPb());
    }

    public PbRecord.MRTCombatRecord buildPb(){
        PbRecord.MRTCombatRecord .Builder builder = PbRecord.MRTCombatRecord .newBuilder();
        builder.setAtk(atkUser);
        if(Objects.nonNull(defUser)){
            builder.setDef(defUser);
        }
        if(Objects.nonNull(robotInfo)){
            builder.setRobotInfo(robotInfo);
        }
        builder.setAtkModifyPoint(atkModifyPoint);
        builder.setDefModifyPoint(defModifyPoint);
        builder.setAtkPoint(atkPoint);
        builder.setDefPoint(defPoint);
        builder.addAllWinScores(winScores);
        return builder.build();
    }

    @Override
    protected Set<PbRecord.RecordPlayer> getDetailRecordSet() {
        return Collections.emptySet();
    }

    @Override
    public void readFromDb2(RecordDb db) {
        MRTCombatRecordDb mrtCombatRecordDb = db.getMrtCombatRecordDb();
        this.atkUser = RecordHelper.getInstance().genRecordPlayerPb(mrtCombatRecordDb.getAtkUser());
        this.defUser = RecordHelper.getInstance().genRecordPlayerPb(mrtCombatRecordDb.getDefUser());
        this.robotInfo = new MultiTeamRobot(mrtCombatRecordDb.getRobotInfo()).genMultiTeamPb();
        this.atkModifyPoint = mrtCombatRecordDb.getAtkModifyPoint();
        this.defModifyPoint = mrtCombatRecordDb.getDefModifyPoint();
        this.atkPoint = mrtCombatRecordDb.getAtkPoint();
        this.defPoint = mrtCombatRecordDb.getDefPoint();
        this.winScores = new ArrayList<>(mrtCombatRecordDb.getWinScores());
    }

    @Override
    public void writeToDb(RecordDb db) {
        MRTCombatRecordDb mrtCombatRecordDb = new MRTCombatRecordDb();
        mrtCombatRecordDb.setAtkUser(RecordHelper.getInstance().genRecordPlayerDb(atkUser));
        mrtCombatRecordDb.setDefUser(RecordHelper.getInstance().genRecordPlayerDb(defUser));
        mrtCombatRecordDb.setRobotInfo(MultiTeamRobot.genMultiTeamDb(robotInfo));
        mrtCombatRecordDb.setAtkModifyPoint(atkModifyPoint);
        mrtCombatRecordDb.setDefModifyPoint(defModifyPoint);
        mrtCombatRecordDb.setAtkPoint(atkPoint);
        mrtCombatRecordDb.setDefPoint(defPoint);
        mrtCombatRecordDb.setWinScores(winScores);
        db.setMrtCombatRecordDb(mrtCombatRecordDb);
    }
}
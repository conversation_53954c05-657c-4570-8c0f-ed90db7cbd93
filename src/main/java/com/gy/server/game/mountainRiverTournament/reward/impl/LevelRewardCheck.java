package com.gy.server.game.mountainRiverTournament.reward.impl;

import com.gy.server.game.drop.Reward;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.mountainRiverTournament.reward.IRewardCheck;
import com.gy.server.game.mountainRiverTournament.template.MRTTaskRewardTemplate;
import com.gy.server.game.player.Player;
import com.gy.server.packet.PbCommons;

import java.util.List;

/**
 * 段位
 * @author: gbk
 * @date: 2024-11-27 09:56
 */
public class LevelRewardCheck implements IRewardCheck<MRTTaskRewardTemplate, Integer> {

    @Override
    public boolean canReceive(MRTTaskRewardTemplate mrtTaskRewardTemplate, Integer level) {
        return Integer.parseInt(mrtTaskRewardTemplate.rewardCondition) <= level;
    }

    @Override
    public List<PbCommons.PbReward> addToPlayer(Player player, MRTTaskRewardTemplate mrtTaskRewardTemplate, BehaviorType behaviorType) {
        return Reward.writeCollectionToPb(Reward.addFromTemplates(mrtTaskRewardTemplate.rewardGroup, player, behaviorType));
    }

    @Override
    public List<Reward> getReward(MRTTaskRewardTemplate mrtTaskRewardTemplate) {
        return Reward.templateCollectionToReward(mrtTaskRewardTemplate.rewardGroup);
    }
}
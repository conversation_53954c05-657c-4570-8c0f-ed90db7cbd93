package com.gy.server.game.mountainRiverTournament.template;

import com.gy.server.common.base.AbsTemplate;
import com.gy.server.game.mountainRiverTournament.bean.MRTSpecialReportEnums;
import com.gy.server.utils.StringUtil;

import java.util.Map;

/**
 * 事件类型
 * @author: gbk
 * @date: 2024-11-22 15:58
 */
public class MRTSpecialReportTemplate implements AbsTemplate {

    //类型
    public int id;
    //战力差
    public int minPowerRadius;
    public int maxPowerRadius;

    @Override
    public void readTxt(Map<String, String> map) {
        id = Integer.parseInt(map.get("eventType"));
        int[] ints = StringUtil.splitToIntArray(map.get("powerRadius"), "\\|");
        minPowerRadius = ints[0];
        maxPowerRadius = ints[1];
        MRTSpecialReportEnums specialReportEnums = MRTSpecialReportEnums.getById(id);
        specialReportEnums.setMinPowerRadius(minPowerRadius);
        specialReportEnums.setMaxPowerRadius(maxPowerRadius);
    }



}
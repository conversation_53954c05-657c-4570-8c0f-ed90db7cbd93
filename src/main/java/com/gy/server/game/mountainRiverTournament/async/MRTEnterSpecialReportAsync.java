package com.gy.server.game.mountainRiverTournament.async;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.game.mountainRiverTournament.bean.MRTSpecialReportEnums;
import com.gy.server.game.mountainRiverTournament.bean.MRTSpecialReportInfo;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.serialize.RedisAssistant;

/**
 * 尝试进入特殊战报
 * @author: gbk
 * @date: 2024-11-25 14:34
 */
public class MRTEnterSpecialReportAsync extends AbsMRTAsync{

    int warZoneId;
    long playerId;
    int srId;
    Object[] param;
    MRTSpecialReportInfo reportInfo;
    String redisKey;
    long recordId;
    int serverId;
    int recordType;

    public MRTEnterSpecialReportAsync(int warZoneId, long playerId, long recordId
            , int srId, int serverId, int recordType, Object... param){
        this.warZoneId = warZoneId;
        this.playerId = playerId;
        this.srId = srId;
        this.param = param;
        this.recordId = recordId;
        this.serverId = serverId;
        this.recordType = recordType;
    }

    @Override
    public void asyncExecute() {
        redisKey = GsRedisKey.MountainRiverTournament.special_report.getRedisKey(warZoneId);
        RedisAssistant ra = TLBase.getInstance().getRedisAssistant();
        reportInfo = ra.hashGetBeans(redisKey,srId + "", MRTSpecialReportInfo.class);
    }

    @Override
    public void execute() {
        MRTSpecialReportEnums specialReportEnums = MRTSpecialReportEnums.getById(srId);
        specialReportEnums.tryEnterRecord(redisKey, reportInfo, playerId, serverId, recordType, recordId, param);
    }

}
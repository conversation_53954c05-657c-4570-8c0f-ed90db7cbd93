package com.gy.server.game.mountainRiverTournament.async;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.game.mountainRiverTournament.bean.MRTSpecialReportInfo;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbLeague;
import com.gy.server.packet.PbProtocol;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.serialize.RedisAssistant;
import com.ttlike.server.tl.baselib.serialize.warZone.WarZoneInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 特殊战报信息
 * @author: gbk
 * @date: 2024-11-26 10:56
 */
public class MRTSpecialRecordAsync extends AbsMRTAsync {

    Player player;
    long time;
    List<PbLeague.MRTSpecialReportInfo> reportInfoList = new ArrayList<>();

    public MRTSpecialRecordAsync(Player player, long time){
        this.player = player;
        this.time = time;
    }

    @Override
    public void asyncExecute() {
        WarZoneInfo.WarZoneSingleInfo warZone = getCurWarZone();
        RedisAssistant ra = TLBase.getInstance().getRedisAssistant();
        String redisKey = GsRedisKey.MountainRiverTournament.special_report_show.getRedisKey(warZone.getWarZoneId());
        Map<String, MRTSpecialReportInfo> reportInfoMap = ra.hashGetAllBeans(redisKey, MRTSpecialReportInfo.class);
        for (MRTSpecialReportInfo reportInfo : reportInfoMap.values()) {
            reportInfoList.add(reportInfo.genPb());
        }
    }

    @Override
    public void execute() {
        PbProtocol.MountainRiverTournamentSpecialRecordRst.Builder rst = PbProtocol.MountainRiverTournamentSpecialRecordRst.newBuilder().setResult(Text.genOkServerRstInfo());
        rst.addAllInfos(reportInfoList);
        player.send(PtCode.MOUNTAINRIVERTOURNAMENT_SPECIALRECORD_RST, rst.build(), time);
    }
}
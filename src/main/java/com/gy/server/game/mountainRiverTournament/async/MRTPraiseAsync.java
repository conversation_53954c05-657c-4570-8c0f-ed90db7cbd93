package com.gy.server.game.mountainRiverTournament.async;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.game.mountainRiverTournament.PlayerMRTModel;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbLeague;
import com.gy.server.packet.PbProtocol;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.serialize.RedisAssistant;
import com.ttlike.server.tl.baselib.serialize.warZone.WarZoneInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * 点赞
 * @author: gbk
 * @date: 2024-11-26 11:17
 */
public class MRTPraiseAsync extends AbsMRTAsync {

    Player player;
    long targetPlayerId;
    long time;
    int text = Text.没有异常;
    List<PbLeague.MRTTournamentFengcailuInfo> fengcailuInfos = new ArrayList<>();

    public MRTPraiseAsync(Player player, long targetPlayerId, long time){
        this.player = player;
        this.targetPlayerId = targetPlayerId;
        this.time = time;
    }

    @Override
    public void asyncExecute() {
        WarZoneInfo.WarZoneSingleInfo warZone = getCurWarZone();
        String fengtailuRedisKey = GsRedisKey.MountainRiverTournament.fengtailu.getRedisKey(warZone.getWarZoneId());
        RedisAssistant ra = TLBase.getInstance().getRedisAssistant();
        List<String> playerIdStrs = ra.listGetAllValues(fengtailuRedisKey);
        if(!playerIdStrs.contains(targetPlayerId + "")){
            text = Text.参数异常;
            return;
        }
        //点赞
        String redisKey = GsRedisKey.MountainRiverTournament.support_count.getRedisKey(targetPlayerId + "");
        ra.increamentId(redisKey);
        ra.setAdd(GsRedisKey.MountainRiverTournament.support_count_key.getRedisKey(), targetPlayerId + "");

        PbLeague.MRTTournamentFengcailuInfo.Builder builder = PbLeague.MRTTournamentFengcailuInfo.newBuilder();
        for (String playerIdStr : playerIdStrs) {
            long playerId = Long.parseLong(playerIdStr);
            builder.clear();
            MiniGamePlayer miniPlayer = PlayerManager.getMiniPlayer(playerId);
            builder.setUser(miniPlayer.genMinMiniUser());
            String support_countKey = GsRedisKey.MountainRiverTournament.support_count.getRedisKey(playerId + "");
            long count = ra.getLong(support_countKey);
            builder.setSupportCount(Math.max(count, 0));
        }
        fengcailuInfos.add(builder.build());
    }

    @Override
    public void execute() {
        PbProtocol.MountainRiverTournamentPraiseRst.Builder rst = PbProtocol.MountainRiverTournamentPraiseRst.newBuilder();
        PlayerMRTModel mrtModel = player.getModel(PlayerModelEnums.mrt);
        mrtModel.getHadPraisePlayerIds().add(targetPlayerId);
        rst.setResult(Text.genServerRstInfo(text));
        rst.addAllInfos(fengcailuInfos);
        rst.setPlayerId(targetPlayerId);
        player.send(PtCode.MOUNTAINRIVERTOURNAMENT_PRAISE_RST, rst.build(), time);
    }
}
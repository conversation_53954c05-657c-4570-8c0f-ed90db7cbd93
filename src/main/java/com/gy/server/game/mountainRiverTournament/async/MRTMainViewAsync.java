package com.gy.server.game.mountainRiverTournament.async;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.game.mountainRiverTournament.MountainRiverTournamentService;
import com.gy.server.game.mountainRiverTournament.PlayerMRTModel;
import com.gy.server.game.mountainRiverTournament.bean.MRTInfo;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbLeague;
import com.gy.server.packet.PbProtocol;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.serialize.RedisAssistant;
import com.ttlike.server.tl.baselib.serialize.warZone.WarZoneInfo;

import java.util.ArrayList;
import java.util.Objects;

/**
 * 获取主界面信息
 * @author: gbk
 * @date: 2024-11-18 13:25
 */
public class MRTMainViewAsync extends AbsMRTAsync {

    Player player;
    MRTInfo info;
    double point;
    int myRank;
    long time;
    int text = Text.没有异常;

    public MRTMainViewAsync(Player player, long time){
        this.player = player;
        this.time = time;
    }

    @Override
    public void asyncExecute() {
        logic:{
            WarZoneInfo.WarZoneSingleInfo warZone = getCurWarZone();
            if(Objects.isNull(warZone)){
                text = Text.剑会群雄_未参与玩法;
                break logic;
            }
            this.info = getInfo();
            if(Objects.isNull(info)){
                text = Text.剑会群雄_未开始玩法;
                break logic;
            }
            WarZoneInfo.WarZoneSingleInfo warZoneSingleInfo = getCurWarZone();

            RedisAssistant ra = TLBase.getInstance().getRedisAssistant();
            String redisKey = GsRedisKey.MountainRiverTournament.level_rank.getRedisKey(warZoneSingleInfo.getWarZoneId());
            point = ra.rankScore(redisKey, player.getPlayerId() + "");
            myRank = ra.rankGet(redisKey, player.getPlayerId() + "", false);
            if(point <= 0){
                //初始化积分
                point = MountainRiverTournamentService.getConstant().initialIntegral;
                ra.rankPut(redisKey, player.getPlayerId() + "", point);
//                //初始化匹配权重
//                String matchWeightRedisKey = GsRedisKey.MountainRiverTournament.match_weight.getRedisKey();
//                ra.hashPut(matchWeightRedisKey, player.getPlayerId() + "", new RedisStringBean(MRTHelper.max_match_weight));
            }

        }
    }

    public PbLeague.MRTMainInfo genMainInfo(){
        PbLeague.MRTMainInfo.Builder builder = PbLeague.MRTMainInfo.newBuilder();
        if(Objects.nonNull(info)){
            builder.setSeasonId(info.getSeasonId());
            builder.setEndTime(info.getNextRefreshTime());
        }
        builder.setPoint(point);
        builder.setPlayType(info.getPlayType());
        builder.setMyRank(Math.max(myRank, 0));
        PlayerMRTModel mrtModel = player.getModel(PlayerModelEnums.mrt);
        builder.setBattleTimes(mrtModel.getBattleTimes());
        builder.setWinTimes(mrtModel.getWinTimes());
        builder.addAllHadReceiveRewardId(mrtModel.getHadReceiveDailyRewardId());
        builder.addAllHadReceiveRewardId(mrtModel.getHadReceiveSeasonRewardId().getOrDefault(info.getSeasonId(),new ArrayList<>()));
        builder.setFengcailuReward(mrtModel.isTodayReceiveFengcailu());
        builder.setTodayBattleTimes(mrtModel.getTodayBattleTimes());
        builder.setTodayWinTimes(mrtModel.getTodayWinTimes());
        builder.addAllHadPraisePlayerIds(mrtModel.getHadPraisePlayerIds());
        return builder.build();
    }

    @Override
    public void execute() {
        PbProtocol.MountainRiverTournamentMainViewRst.Builder rst = PbProtocol.MountainRiverTournamentMainViewRst
                .newBuilder().setResult(Text.genServerRstInfo(text));
        if(text == Text.没有异常){
            rst.setMainInfo(genMainInfo());
        }
        player.send(PtCode.MOUNTAINRIVERTOURNAMENT_MAINVIEW_RST, rst.build(), time);
    }
}
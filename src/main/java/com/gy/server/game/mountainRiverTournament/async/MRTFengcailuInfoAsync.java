package com.gy.server.game.mountainRiverTournament.async;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbLeague;
import com.gy.server.packet.PbProtocol;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.serialize.RedisAssistant;
import com.ttlike.server.tl.baselib.serialize.warZone.WarZoneInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 风采录信息
 * @author: gbk
 * @date: 2024-11-26 09:33
 */
public class MRTFengcailuInfoAsync extends AbsMRTAsync{

    Player player;
    long time;
    List<PbLeague.MRTTournamentFengcailuInfo> fengcailuInfos = new ArrayList<>();

    public MRTFengcailuInfoAsync(Player player, long time){
        this.player = player;
        this.time = time;
    }

    @Override
    public void asyncExecute() {
        WarZoneInfo.WarZoneSingleInfo warZone = getCurWarZone();
        String fengtailuRedisKey = GsRedisKey.MountainRiverTournament.fengtailu.getRedisKey(warZone.getWarZoneId());
        RedisAssistant ra = TLBase.getInstance().getRedisAssistant();
        List<Long> playerIds = getPlayerIds(fengtailuRedisKey);
        PbLeague.MRTTournamentFengcailuInfo.Builder builder = PbLeague.MRTTournamentFengcailuInfo.newBuilder();
        Map<Long, MiniGamePlayer> miniPlayersForMap = PlayerHelper.getMiniPlayersForMap(playerIds);
        for (long playerId : playerIds) {
            builder.clear();
            MiniGamePlayer miniPlayer = miniPlayersForMap.get(playerId);
            builder.setUser(miniPlayer.genMinMiniUser());
            String redisKey = GsRedisKey.MountainRiverTournament.support_count.getRedisKey(playerId + "");
            long count = ra.getLong(redisKey);
            builder.setSupportCount(Math.max(count, 0));
            fengcailuInfos.add(builder.build());
        }
    }

    private List<Long> getPlayerIds(String fengtailuRedisKey){
        RedisAssistant ra = TLBase.getInstance().getRedisAssistant();
        List<String> playerIdStrs = ra.listGetAllValues(fengtailuRedisKey);
        List<Long> playerIds = new ArrayList<>();
        for (String playerIdStr : playerIdStrs) {
            playerIds.add(Long.parseLong(playerIdStr));
        }
        return playerIds;
    }

    @Override
    public void execute() {
        PbProtocol.MountainRiverTournamentFengcailuRst.Builder rst = PbProtocol.MountainRiverTournamentFengcailuRst.newBuilder().setResult(Text.genOkServerRstInfo());
        rst.addAllInfos(fengcailuInfos);
        player.send(PtCode.MOUNTAINRIVERTOURNAMENT_FENGCAILU_RST, rst.build(), time);
    }

}
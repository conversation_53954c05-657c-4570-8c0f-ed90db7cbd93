package com.gy.server.game.mountainRiverTournament.template;

import com.gy.server.common.base.AbsTemplate;

import java.util.Map;

/**
 * 分差表
 * @author: gbk
 * @date: 2024-11-22 13:38
 */
public class MRTDIffCountTemplate implements AbsTemplate {

    //段位id
    public int rankId;
    //分差
    public int diffCount;
    //额外积分
    public int addedCount;

    @Override
    public void readTxt(Map<String, String> map) {
        diffCount = Integer.parseInt(map.get("DifferenceCount"));
        addedCount = Integer.parseInt(map.get("addCount"));
    }
}
package com.gy.server.game.mountainRiverTournament.template;

import com.gy.server.common.base.AbsTemplate;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.mountainRiverTournament.reward.MRTTaskRewardConditionEnums;
import com.gy.server.game.mountainRiverTournament.reward.MRTTaskRewardTypeEnums;

import java.util.List;
import java.util.Map;

/**
 * 任务奖励
 * @author: gbk
 * @date: 2024-11-27 09:58
 */
public class MRTTaskRewardTemplate implements AbsTemplate {

    //唯一id
    public int id;
    //奖励类型
    public MRTTaskRewardTypeEnums rewardType;
    //条件类型
    public MRTTaskRewardConditionEnums conditionType;
    //条件参数
    public String rewardCondition;
    //奖励
    public List<RewardTemplate> rewardGroup;


    @Override
    public void readTxt(Map<String, String> map) {
        id = Integer.parseInt(map.get("id"));
        rewardType = MRTTaskRewardTypeEnums.getById(Integer.parseInt(map.get("rewardType")));
        conditionType = MRTTaskRewardConditionEnums.getById(Integer.parseInt(map.get("conditionType")));
        rewardCondition = map.get("rewardCondition");
        rewardGroup = RewardTemplate.readListFromText(map.get("rewardGroup"));
    }
}
package com.gy.server.game.mountainRiverTournament;

import com.gy.server.annotation.MessageMethod;
import com.gy.server.annotation.MessageServiceBean;
import com.gy.server.common.redis.bean.RedisStringBean;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.core.MessageServerType;
import com.gy.server.core.MethodInvokeType;
import com.gy.server.core.command.CommandRequestParams;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.mail.MailHelper;
import com.gy.server.game.mail.globalMail.GlobalMailType;
import com.gy.server.game.mountainRiverTournament.bean.MRTPlayEnums;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.serialize.mrt.MRTPlayerLevelInfo;
import com.ttlike.server.tl.baselib.thread.ThreadPool;
import redis.clients.util.SafeEncoder;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @author: gbk
 * @date: 2024-11-28 14:15
 */
@MessageServiceBean(description = "剑会群雄-逸侠对决消息", messageServerType = MessageServerType.game)
public class GsMRTCommandService {


    @MessageMethod(description = "段位奖励", invokeType = MethodInvokeType.sync)
    private static void dailyLevelReward(ServerCommandRequest request, CommandRequestParams params) {
        int playTypeId = params.getParam(0);
        List<MRTPlayerLevelInfo> mrtPlayerLevelInfos = params.getParam(1);

        MRTPlayEnums curPlayType = MRTPlayEnums.getPlayByType(playTypeId);
        GlobalMailType curMailType;
        GlobalMailType lastMailType;
        if(curPlayType == MRTPlayEnums.P6V6){
            curMailType = GlobalMailType.mrtDaily6V6;
            lastMailType = GlobalMailType.mrtDaily3V3;
        }else{
            lastMailType = GlobalMailType.mrtDaily6V6;
            curMailType = GlobalMailType.mrtDaily3V3;
        }
        Map<Long, Double> curPointInfos = new HashMap<>();
        Map<Long, Double> lastPointInfos = new HashMap<>();
        for (MRTPlayerLevelInfo mrtPlayerLevelInfo : mrtPlayerLevelInfos) {
            double curPoint = mrtPlayerLevelInfo.getCurPoint();
            if(curPoint > 0){
                curPointInfos.put(mrtPlayerLevelInfo.getPlayerId(), curPoint);
            }
            double lastPoint = mrtPlayerLevelInfo.getLastPoint();
            if(lastPoint > 0){
                lastPointInfos.put(mrtPlayerLevelInfo.getPlayerId(), lastPoint);
            }
        }
        MailHelper.addMRTGlobalMail(curMailType, curPointInfos);
        MailHelper.addMRTGlobalMail(lastMailType, lastPointInfos);

    }

    public static final String separator = "_";

    @MessageMethod(description = "结算奖励", invokeType = MethodInvokeType.sync)
    private static void settlementReward(ServerCommandRequest request, CommandRequestParams params) {
        int playTypeId = params.getParam(0);
        List<MRTPlayerLevelInfo> mrtPlayerLevelInfos = params.getParam(1);
        int seasonId = params.getParam(2);

        MRTPlayEnums curPlayType = MRTPlayEnums.getPlayByType(playTypeId);
        GlobalMailType curMailType = curPlayType == MRTPlayEnums.P6V6 ? GlobalMailType.mrtSettlement6V6 : GlobalMailType.mrtSettlement3V3;
        Map<Long, Double> curPointInfos = new HashMap<>();
        for (MRTPlayerLevelInfo mrtPlayerLevelInfo : mrtPlayerLevelInfos) {
            double curPoint = mrtPlayerLevelInfo.getCurPoint();
            if(curPoint > 0){
                curPointInfos.put(mrtPlayerLevelInfo.getPlayerId(), curPoint);
            }
        }
        MailHelper.addMRTGlobalMail(curMailType, curPointInfos);


        //检查补发段位奖励
        Map<String, RedisStringBean> needSaveInfos = new HashMap<>();
        //补发在线
        curPointInfos.forEach((playerId, v)->{
            Player player = PlayerManager.getOnlinePlayer(playerId);
            int point = (int)(double)v;
            if(Objects.nonNull(player)){
                PlayerMRTModel mrtModel = player.getModel(PlayerModelEnums.mrt);
                mrtModel.checkSeasonReward(seasonId, point);
            }else{
                needSaveInfos.put(playerId + "", new RedisStringBean(point + separator + seasonId));
            }
        });
        //存入redis，等玩家在线再发剩余的
        ThreadPool.execute(()->{
            String redisKey = GsRedisKey.MountainRiverTournament.reissue_Level.getRedisKey();
            Map<byte[], byte[]> needSaveBytes = new HashMap<>();
            needSaveInfos.forEach((k, v) -> needSaveBytes.put(SafeEncoder.encode(k), PbUtilCompress.encode(v)));
            TLBase.getInstance().getRedisAssistant().hashPutAsync(redisKey, needSaveBytes);
        });

    }

}
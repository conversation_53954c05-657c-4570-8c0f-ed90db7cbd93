package com.gy.server.game.mountainRiverTournament.bean;

import java.util.HashMap;
import java.util.Map;

/**
 * 玩法类型
 * @author: gbk
 * @date: 2024-11-12 10:45
 */
public enum MRTPlayEnums {

    P6V6(1),
    P3V3(2),
    ;

    int typeId;
    MRTPlayEnums(int typeId){
        this.typeId = typeId;
    }

    public int getType(){
        return typeId;
    }

    private static Map<Integer, MRTPlayEnums> playTypes = new HashMap<>();
    static{
        for (MRTPlayEnums playEnums : MRTPlayEnums.values()) {
            playTypes.put(playEnums.typeId, playEnums);
        }
    }

    public static MRTPlayEnums getPlayByType(int typeId){
        return playTypes.get(typeId);
    }



}
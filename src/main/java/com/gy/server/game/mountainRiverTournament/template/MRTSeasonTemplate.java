package com.gy.server.game.mountainRiverTournament.template;

import com.gy.server.common.base.AbsTemplate;

import java.util.Map;

/**
 * @author: gbk
 * @date: 2024-11-22 14:27
 */
public class MRTSeasonTemplate implements AbsTemplate {

    public int seasonId;

    public int buffId;

    @Override
    public void readTxt(Map<String, String> map) {
        seasonId = Integer.parseInt(map.get("rankid"));
        buffId = Integer.parseInt(map.get("seasonBuff"));
    }
}
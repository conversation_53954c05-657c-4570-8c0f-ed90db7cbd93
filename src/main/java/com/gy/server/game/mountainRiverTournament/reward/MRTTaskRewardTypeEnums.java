package com.gy.server.game.mountainRiverTournament.reward;

import java.util.HashMap;
import java.util.Map;

/**
 * 任务奖励-奖励类型
 * @author: gbk
 * @date: 2024-11-26 20:09
 */
public enum MRTTaskRewardTypeEnums {

    //每日任务（手动领取）(2025.02.17废弃)
    dailyTask(1),
    //每日段位(每天自动发送)
    dailyLevel(2),
    //赛季奖励（手动领取）
    season(3),
    //结算奖励(2025.02.17废弃)
    settlement(4),

    ;

    int id;
    MRTTaskRewardTypeEnums(int id){
        this.id = id;
    }

    private static Map<Integer, MRTTaskRewardTypeEnums> enums = new HashMap<>();
    static{
        for (MRTTaskRewardTypeEnums typeEnums : MRTTaskRewardTypeEnums.values()) {
            enums.put(typeEnums.id,typeEnums);
        }
    }

    public static MRTTaskRewardTypeEnums getById(int id){
        return enums.get(id);
    }

}
package com.gy.server.game.league;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.core.Configuration;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.thread.AbstractRunner;
import com.gy.server.db.nosql.redis.GyRedisTop;
import com.gy.server.game.async.DeleteAsyncThread;
import com.gy.server.game.cond.CondManager;
import com.gy.server.game.db.DbAssistant;
import com.gy.server.game.db.DbManager;
import com.gy.server.game.keyword.KeyWordService;
import com.gy.server.game.league.async.LeagueAutoCreateAsyncCall;
import com.gy.server.game.league.async.LeagueMemberNotifyAsync;
import com.gy.server.game.league.bean.EsLeagueNameInfo;
import com.gy.server.game.league.bean.LeagueCustomJobInfo;
import com.gy.server.game.league.bean.LeagueJobInfo;
import com.gy.server.game.league.bean.LeagueMember;
import com.gy.server.game.league.log.LeagueLogType;
import com.gy.server.game.league.model.LeagueBaseModel;
import com.gy.server.game.league.template.LeagueConstant;
import com.gy.server.game.league.template.LeagueFrameTemplate;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.player.BaseInfoSyncType;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.player.name.NameHelper;
import com.gy.server.game.rank.RankManager;
import com.gy.server.game.rank.RankType;
import com.gy.server.game.reddot.RedDot;
import com.gy.server.game.secretRealm.SecretRealmManager;
import com.gy.server.game.speak.SpeakChannelType;
import com.gy.server.game.speak.SpeakHelper;
import com.gy.server.game.text.Text;
import com.gy.server.game.world.World;
import com.gy.server.packet.PbCommons;
import com.gy.server.packet.PbLeague;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.MathUtil;
import com.gy.server.utils.function.Ticker;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.thread.ThreadPool;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 帮派管理器
 *
 * <AUTHOR> - [Created on 2023/2/20 14:29]
 */
public class LeagueManager extends AbstractRunner implements Ticker {

    private static final LeagueManager instance = new LeagueManager();

    private static final Map<Integer, ConcurrentHashMap<Long, League>> leagues = new ConcurrentHashMap<>();
    /**
     * 玩家-帮派映射关系
     * key：玩家id，value:帮派id
     */
    private static final Map<Long, Long> playerLeagues = new ConcurrentHashMap<>();

    /**
     * 玩家申请帮派信息
     * key:玩家id，value：已申请帮派列表集合
     */
    private final static Map<Long, Set<Long>> playerApplies = new ConcurrentHashMap<>();

    public static LeagueManager getInstance() {
        return instance;
    }

    private int saveIndex = 0;
    // 下次保存时间
    private long nextSaveMillis;
    // 下次检查过期时间
    private long nextCheckOverdueTime;

    /**
     * 系统帮派创建中
     */
    private boolean systemLeagueCreating = false;

    /**
     * 根据玩家id获得其所在帮派id，如果帮派不存在则返回0
     */
    public static long getLeagueId(long playerId) {
        if (playerLeagues.containsKey(playerId)) {
            return playerLeagues.get(playerId);
        }
        return 0;
    }

    /**
     * 根据玩家 获取帮派ID
     *
     * @param player 玩家
     * @return 帮派ID
     */
    public static long getLeagueId(Player player) {
        return getLeagueId(player.getPlayerId());
    }

    public static PbCommons.MiniLeague genMiniLeaguePb(long leagueId) {
        PbCommons.MiniLeague.Builder builder = PbCommons.MiniLeague.newBuilder();
        if (leagueId != 0) {
            League league = getLeagueById(leagueId);
            if(Objects.nonNull(league)){
                builder.setId(league.getLeagueId())
                        .setName(league.getName());
            }
        }
        return builder.build();
    }

    /**
     * 是否加入帮派
     *
     * @param player 玩家信息
     * @return true 加入帮派
     */
    public static boolean isJoinLeague(Player player) {
        return isJoinLeague(player.getPlayerId());
    }

    /**
     * 是否加入帮派
     *
     * @param playerId 玩家信息
     * @return true 加入帮派
     */
    public static boolean isJoinLeague(long playerId) {
        return playerLeagues.containsKey(playerId);
    }

    /**
     * 是否加入帮派
     *
     * @param player 玩家信息
     * @return true 未加入帮派
     */
    public static boolean isNotJoinLeague(Player player) {
        return !isJoinLeague(player);
    }

    /**
     * 是否加入帮派
     *
     * @param playerId 玩家信息
     * @return true 未加入帮派
     */
    public static boolean isNotJoinLeague(long playerId) {
        return !isJoinLeague(playerId);
    }

    /**
     * 移除帮派
     */
    static void removeLeague(League league) {
        //移除
        leagues.remove(league.getLeagueId());

        //帮派解散
        dismiss(league);

        SecretRealmManager.removeLeague(league.getLeagueId());
        //删除排行榜
        RankManager.deleteItem(RankType.league, league);
        //删除es查询
        LeagueManager.removeLeagueName(league.getLeagueId());
    }

    /**
     * 帮派解散
     */
    private static void dismiss(League league) {
        //清除申请信息
        Set<Long> applies = league.getAllApplies();
        for (long playerId : applies) {
            removeApply(league.getLeagueId(), playerId);
        }
        ThreadPool.execute(new DeleteAsyncThread(league.buildLeagueData(), league.getLeagueId()));
    }

    /**
     * 检测帮派名合法性
     *
     * @param name 帮派名
     * @return 错误码
     */
    public static int checkValidName(String name) {
        LeagueConstant constant = LeagueService.getConstant();
//        if (!NameHelper.chinesePattern.matcher(name).matches()) {
//            return Text.帮派名不可用;
//        }
        int minLength = constant.leagueNameMinLength;
        int maxLength = constant.leagueNameMaxLength;
        if (name.length() < minLength || name.length() > maxLength) {
            return Text.帮派字不可用;
        }
        if (LeagueManager.getLeagueByName(name) != null) {
            return Text.帮派名已被占用;
        }
        if (KeyWordService.contains(name) && !Configuration.runMode.isPress()) {
            return Text.帮派名不可用;
        }
        return Text.没有异常;
    }

    /**
     * 检测帮派字边框
     *
     * @param iconFrameId 帮派字边框ID
     * @return 错误码
     */
    public static int checkValidIconFrameId(Player player, int iconFrameId) {
        LeagueFrameTemplate leagueFrameTemplate = LeagueService.getLeagueFrameTemplateMap().get(iconFrameId);
        if (Objects.isNull(leagueFrameTemplate)) {
            return Text.参数异常;
        }
        if (CondManager.checkNotCond(player, leagueFrameTemplate.unClock)) {
            return Text.帮派字边框未解锁;
        }
        return Text.没有异常;
    }

    /**
     * 检测帮派公告合法性
     *
     * @param notice 公告
     * @return 错误码
     */
    public static int checkValidNotice(String notice) {
        LeagueConstant constant = LeagueService.getConstant();
        if (KeyWordService.contains(notice)) {
            return Text.公告包含敏感字符;
        }
        int byteLength = NameHelper.getLength(notice);
        if (byteLength < constant.leagueNoticeMinLength || byteLength > constant.leagueNoticeMaxLength) {
            return Text.公告长度不满足要求;
        }
        return Text.没有异常;
    }

    /**
     * 检测帮派邮件合法性
     *
     * @param title   标题
     * @param content 内容
     * @return 错误码
     */
    public static int checkValidMail(String title, String content) {
        LeagueConstant constant = LeagueService.getConstant();
        if (KeyWordService.contains(title)) {
            return Text.帮派邮件标题包含敏感字符;
        }
        if (KeyWordService.contains(content)) {
            return Text.帮派邮件内容包含敏感字符;
        }
        int byteTitleLength = NameHelper.getLength(title);
        if (byteTitleLength <= 0 || byteTitleLength > constant.leagueMailTitleNum) {
            return Text.帮派邮件标题长度不符;
        }
        int byteContentLength = NameHelper.getLength(content);
        if (byteContentLength <= 0 || byteContentLength > constant.leagueMailWordNum) {
            return Text.帮派邮件内容长度不符;
        }
        return Text.没有异常;
    }

    /**
     * 检测帮派迎新语
     *
     * @param content 内容
     * @return 错误码
     */
    public static int checkValidWelcome(String content) {
        LeagueConstant constant = LeagueService.getConstant();
        if (KeyWordService.contains(content)) {
            return Text.帮派迎新语包含敏感字符;
        }
        int byteContentLength = NameHelper.getLength(content);
        if (byteContentLength < constant.leagueWelcomeMinLength || byteContentLength > constant.leagueWelcomeMaxLength) {
            return Text.帮派迎新语长度不符;
        }
        return Text.没有异常;
    }

    /**
     * 根据帮派名称获得帮派对象
     */
    public static League getLeagueByName(int serverNumber, String name) {
        if(leagues.containsKey(serverNumber)) {
            for (League l : leagues.get(serverNumber).values()) {
                if (l != null && StringUtils.equals(l.getName(), name)) {
                    return l;
                }
            }
        }
        return null;
    }

    /**
     * 根据帮派字名称获得帮派对象
     */
    public static League getLeagueByIconName(int serverNumber, String iconName) {
        if(leagues.containsKey(serverNumber)) {
            for (League l : leagues.get(serverNumber).values()) {
                if (l != null && StringUtils.equals(l.getIconName(), iconName)) {
                    return l;
                }
            }
        }
        return null;
    }


    /**
     * 初始化
     */
    public static void startup() {
        //读取所有帮派
        List<LeagueData> leagueDataList = DbAssistant.getAllLeague();
        if (CollectionUtil.isNotEmpty(leagueDataList)) {
            for (LeagueData leagueData : leagueDataList) {
                League league = new League(leagueData);
                //添加到帮派列表
                addLeague(league);
                LeagueBaseModel leagueBaseModel = league.getModel(LeagueModelEnums.base);
                //初始化玩家-帮会所属
                for (long memberId : leagueBaseModel.getMemberMap().keySet()) {
                    playerLeagues.put(memberId, league.getLeagueId());
                }
                //初始化玩家-帮会申请
                for (long playerId : league.getAllApplies()) {
                    playerApplies.computeIfAbsent(playerId, k -> new HashSet<>()).add(league.getLeagueId());
                }
                //初始化
                league.init();
            }
        }

        //初始化下次存储时间
        instance.nextSaveMillis = ServerConstants.getCurrentTimeMillis() + DateTimeUtil.MillisOfMinute;
        instance.nextCheckOverdueTime = ServerConstants.getCurrentTimeMillis();
    }

    /**
     * 添加到帮派列表
     */
    public static void addLeague(League league) {
        Map<Long, League> map = leagues.computeIfAbsent(League.getRealServerId(league.getLeagueId()), k -> new ConcurrentHashMap<>());
        map.put(league.getLeagueId(), league);
        //更新排行榜
        RankManager.postItem(RankType.league, league);
    }

    /**
     * 玩家加入帮派
     */
    public static void playerJoin(League league, long playerId) {
        //更新玩家-帮派所属关系
        updatePlayerLeague(league.getLeagueId(), playerId);

        //移除玩家申请
        removeApply(-1, playerId);

        //添加成员
        league.addMember(playerId);

        //移除邀请信息
        league.removeInvite(playerId);

        // 更新MiniPlayer
        Optional.ofNullable(PlayerManager.getMiniPlayer(playerId))
                .ifPresent(miniPlayer -> {
                    miniPlayer.change(BaseInfoSyncType.LeagueId, league.getLeagueId());
                    miniPlayer.change(BaseInfoSyncType.LeagueName, league.getName());
                });
    }


    public static void playerJoinAfter(League league, Player player, boolean isSend) {
        // 抛出加入事件
        player.postEvent(PlayerEventType.joinLeague);
        //添加日志
        league.addLeagueLog(LeagueLogType.unionjoin, player.getName());
        // 帮派聊天频道发送加入帮派通知 XXX加入帮派
        SpeakHelper.systemMsg(player, SpeakChannelType.LEAGUE, LeagueService.getConstant().joinMessageId, player.getName());
        if (isSend) {
            // 欢迎语发送
            ThreadPool.execute(() -> sendWelcome(league, player));
        }
    }
    public static void playerJoin(League league, Player player, boolean isSend) {
        playerJoin(league, player, isSend, false);
    }
    public static void playerJoin(League league, Player player, boolean isSend, boolean isLeader) {
        //更新玩家-帮派所属关系
        updatePlayerLeague(league.getLeagueId(), player.getPlayerId());

        //移除玩家申请
        removeApply(-1, player.getPlayerId());

        //添加成员
        league.addMember(player.getPlayerId());
        if(isLeader){
            league.setLeader(player.getPlayerId());
        }

        //移除邀请信息
        league.removeInvite(player.getPlayerId());

        // 更新MiniPlayer
        Optional.ofNullable(PlayerManager.getMiniPlayer(player.getPlayerId()))
                .ifPresent(miniPlayer -> {
                    miniPlayer.change(BaseInfoSyncType.LeagueId, league.getLeagueId());
                    miniPlayer.change(BaseInfoSyncType.LeagueName, league.getName());
                });
        // 抛出加入事件
        player.postEvent(PlayerEventType.joinLeague);
        // 帮派聊天频道发送加入帮派通知 XXX加入帮派
        SpeakHelper.systemMsg(player, SpeakChannelType.LEAGUE, LeagueService.getConstant().joinMessageId, player.getName());
        if (isSend) {
            // 欢迎语发送
            ThreadPool.execute(() -> sendWelcome(league, player));
        }

        //同步其他成员
        ThreadPool.execute(new LeagueMemberNotifyAsync(league, player.getPlayerId()));
    }

    /**
     * 发送祝福语(异步操作)
     *
     * @param league     帮派
     * @param joinPlayer 加入玩家信息
     */
    private static void sendWelcome(League league, Player joinPlayer) {
        int leagueWelcomeNum = LeagueService.getConstant().leagueWelcomeNum;
        Map<Long, LeagueMember> memberMap = league.getMemberMap();
        Set<LeagueMember> sendPlayerSet = new HashSet<>();
        List<LeagueMember> onlineMember = memberMap.values().stream()
                .filter(leagueMember -> leagueMember.isOnline() && leagueMember.getPlayerId() != joinPlayer.getPlayerId())
                .collect(Collectors.toList());
        if (onlineMember.size() < leagueWelcomeNum) {
            sendPlayerSet.addAll(onlineMember);
            // 剩下的人找活跃玩家
            int remainNum = leagueWelcomeNum - onlineMember.size();
            List<LeagueMember> collect = memberMap.values().stream()
                    .filter(leagueMember -> !sendPlayerSet.contains(leagueMember) && leagueMember.isActive() && leagueMember.getPlayerId() != joinPlayer.getPlayerId())
                    .collect(Collectors.toList());
            if (!collect.isEmpty()) {
                List<LeagueMember> random = MathUtil.random(collect, remainNum);
                sendPlayerSet.addAll(random);
            }
        } else {
            sendPlayerSet.addAll(MathUtil.random(onlineMember, leagueWelcomeNum));
        }
        for (LeagueMember member : sendPlayerSet) {
            Player player = PlayerManager.getPlayer(member.getPlayerId());
            SpeakHelper.commonMsg(player, SpeakChannelType.LEAGUE, league.getLeagueData().getWelcome());
        }
    }

    public static PbLeague.LeagueUserData genLeagueUserData(Player player) {
        PbLeague.LeagueUserData.Builder builder = PbLeague.LeagueUserData.newBuilder();

        long leagueId = LeagueManager.getLeagueId(player);
        builder.setId(leagueId);
        if (leagueId > 0) {
            League league = LeagueManager.getLeagueById(leagueId);
            if (Objects.nonNull(league)) {
                builder.setName(league.getName());
            }
            builder.setLevel(league.getLevel());
            builder.setIconName(league.getIconName());
            builder.setIconFrameId(league.getIconFrameId());
            builder.setBuildValue(league.getBuildValue());
            builder.setForceValue(league.getForceValue());
            builder.setNextCreateSRGangTime(league.getNextCreateSRGangTime());
            builder.setLeagueTribute(league.getLeagueTribute());
            builder.setActiveMemberCount(league.getActiveMemberCount(LeagueService.getConstant().activeTime));
            LeagueMember leagueMember = league.getMemberMap().get(player.getPlayerId());
            builder.setMyJobId(leagueMember.getJobId());
            builder.setJobIndex(leagueMember.getJobIndex());
            for (LeagueCustomJobInfo jobInfo : league.getCustomJobAuths().values()) {
                builder.addCustomJobAuths(jobInfo.genPb());
            }
            for (LeagueJobInfo jobInfo : league.getJobInfoMap().values()) {
                builder.addJobAuths(jobInfo.genPb());
            }
        }
        return builder.build();
    }

    /**
     * 申请加入帮派
     */
    static void playerApply(League league, long playerId) {
        if (league != null && !playerLeagues.containsKey(playerId)) {
            playerApplies.computeIfAbsent(playerId, k -> new HashSet<>()).add(league.getLeagueId());
            league.apply(playerId);
        }
    }


    /**
     * 更新玩家-帮派所属关系
     */
    static void updatePlayerLeague(long leagueId, long playerId) {
        if (leagueId <= 0) {
            playerLeagues.remove(playerId);
        } else {
            playerLeagues.put(playerId, leagueId);
        }
    }

    /**
     * 取消申请
     * leagueId = -1代表删除所有帮派的入会申请
     */
    public static void removeApply(long leagueId, long playerId) {
        Set<Long> applies = playerApplies.get(playerId);
        if (CollectionUtil.isNotEmpty(applies)) {
            if (leagueId <= 0) {
                for (Long apply : applies) {
                    League league = getLeagueById(apply);
                    if (league != null) {
                        league.removeApply(playerId);
                    }
                }
                applies.clear();
            } else {
                League league = getLeagueById(leagueId);
                if (league != null) {
                    league.removeApply(playerId);
                }
                applies.remove(leagueId);
            }
        }
    }

    /**
     * 根据帮派id获得帮派对象
     *
     * @return 当帮派被解散后，返回值为空
     */
    public static League getLeagueById(long leagueId) {
        return leagues.get(leagueId);
    }

    /**
     * 根据玩家信息获得帮派对象
     *
     * @return 当帮派被解散后，返回值为空
     */
    public static League getLeagueByPlayer(Player player) {
        return getLeagueByPlayerId(player.getPlayerId());
    }

    /**
     * 根据玩家ID获得帮派对象
     *
     * @return 当帮派被解散后，返回值为空
     */
    public static League getLeagueByPlayerId(long playerId) {
        return getLeagueById(playerLeagues.getOrDefault(playerId, 0L));
    }

    /**
     * 判断指定玩家申请帮派个数是否已达上限
     */
    static boolean isApplyFull(long playerId) {
        Set<Long> notExpiredApplySet = getNotExpiredApplySet(playerId);
        return notExpiredApplySet.size() >= LeagueService.getConstant().leagueApplyMaxNum;
    }

    /**
     * 获取未过期的申请帮派ID
     *
     * @param playerId 玩家ID
     * @return 申请帮派ID
     */
    public static Set<Long> getNotExpiredApplySet(long playerId) {
        Set<Long> result = new HashSet<>();
        Set<Long> set = playerApplies.get(playerId);
        if (CollectionUtil.isEmpty(set)) {
            return result;
        }
        for (Long leagueId : set) {
            League league = getLeagueById(leagueId);
            if (league.hasApply(playerId)) {
                result.add(leagueId);
            }
        }
        return result;
    }

    /**
     * 检查玩家是否已申请指定帮派
     */
    public static boolean isApplyAppointLeague(long leagueId, long playerId) {
        Set<Long> notExpiredApplySet = getNotExpiredApplySet(playerId);
        if (CollectionUtil.isNotEmpty(notExpiredApplySet)) {
            return notExpiredApplySet.contains(leagueId);
        }
        return false;
    }

    /**
     * 合服处理
     *
     * @param players 需要删除玩家列表
     */
    public static void merge(List<Player> players) {
        GameLogger.merge(String.format("deal league start, player size : %s", players.size()));
        players.forEach(player -> {

            GameLogger.merge(String.format("deal league start, playerId : %s", player.getPlayerId()));
            League league = LeagueManager.getLeagueByPlayerId(player.getPlayerId());
            if (Objects.nonNull(league)) {
                GameLogger.merge(String.format("league id is : %s", league.getLeagueId()));
                //是否是会长
                if (league.getLeader() == player.getPlayerId()) {
                    GameLogger.merge(String.format("player is leader, leaderId : %s", player.getPlayerId()));
                    List<Long> members = new ArrayList<>(league.getMemberMap().keySet());
                    //移除本人
                    members.remove(player.getPlayerId());
                    if (members.size() > 0) {
                        //联盟还有玩家，重新随机新会长
                        Collections.shuffle(members);
                        //获取新会长
                        long newLeaderId = members.get(0);

                        GameLogger.merge(String.format("league id : %s, new leaderId is : %s", league.getLeagueId(), newLeaderId));
                        league.setLeader(newLeaderId);
                        league.getViceLeaderSet().remove(newLeaderId);
                    }
                }
                //玩家退出
                league.quit(player);
                GameLogger.merge(String.format("player quit league, playerId : %s", player.getPlayerId()));
            } else {
                GameLogger.merge(String.format("player not enter league,  playerId : %s", player.getPlayerId()));
            }
        });
        GameLogger.merge(String.format("deal friend end, player size : %s", players.size()));
    }


    @Override
    public String getRunnerName() {
        return "LeagueManager";
    }

    @Override
    protected void subRunnerExecute() throws Exception {
        long now = ServerConstants.getCurrentTimeMillis();
        //周期存储
        if (now > nextSaveMillis) {
            nextSaveMillis = now + DateTimeUtil.MillisOfMinute;
            periodUpdateLeague();
        }
        // 检查申请过期时间 一小时检查一次就行了 不用太频繁
        if (now > nextCheckOverdueTime) {
            nextCheckOverdueTime = now + DateTimeUtil.MillisOfHour;
            for (League league : leagues.values()) {
                Set<Long> allApplies = league.getAllApplies();
                for (Long applyPlayerId : allApplies) {
                    if (league.applyIsOverdue(applyPlayerId)) {
                        removeApply(league.getLeagueId(), applyPlayerId);
                    }
                }
                Map<Long, Long> recruitInviteMap = league.getRecruitInviteMap();
                for (Long invitePlayerId : new HashSet<>(recruitInviteMap.keySet())) {
                    // 已加入其他帮派 删除
                    if (isJoinLeague(invitePlayerId)) {
                        league.removeInvite(invitePlayerId);
                    }
                    // 过期
                    if (league.inviteIsOverdue(invitePlayerId)) {
                        league.removeInvite(invitePlayerId);
                    }
                }
            }
        }
    }

    /**
     * 周期更新帮派
     */
    private void periodUpdateLeague() {
        for (League league : leagues.values()) {
            if (league != null) {
                if (league.getLeagueId() % 10 == saveIndex) {
                    DbManager.update(league.buildLeagueData());
                }
            }
        }
        saveIndex++;

        if (saveIndex >= 10) {
            saveIndex = 0;
        }
    }

    /**
     * 获得所有帮派对象
     */
    public static List<League> getLeagues() {
        return new ArrayList<>(leagues.values());
    }

    /**
     * 根据帮派名和字搜索帮派对象
     * content:帮派名或字 不需要传Null即可
     */
    public static List<League> searchLeagues(String content) {
        List<League> leagues = getLeagues();
        if (StringUtils.isNotBlank(content)) {
            leagues = leagues.stream()
                    .filter(league -> league.getName().contains(content) || league.getIconName().contains(content))
                    .collect(Collectors.toList());
        }
        return leagues;
    }

    /**
     * 联盟有加入申请红点
     */
    public static boolean leagueApplyJoinRedDot(Player player){
        League league = LeagueManager.getLeagueByPlayer(player);
        if(Objects.nonNull(league)){
            if(league.getViceLeaderSet().contains(player.getPlayerId()) || league.getLeader() == player.getPlayerId()){
                return league.getAllApplies().size() > 0;
            }
        }
        return false;
    }

    /**
     * 能否玩联盟玩法
     */
    public static boolean canNotPlayLeague(Player player){
        PlayerLeagueModel leagueModel = player.getModel(PlayerModelEnums.league);
        return ServerConstants.getCurrentTimeMillis() < leagueModel.getCanPlayerLeagueTime();
    }

    /**
     * 更新联盟信息到es
     */
    public static void updateLeagueToEs(League league, boolean isRegister){
//        EsLeagueNameInfo esPlayerNameInfo = new EsLeagueNameInfo(league.getLeagueId());
//        esPlayerNameInfo.setName(league.getName());
//        esPlayerNameInfo.setServerId(Configuration.serverId);
//        if(isRegister){
//            EsUtil.insert(esPlayerNameInfo);
//        }else{
//            EsUtil.update(esPlayerNameInfo);
//        }
    }

    /**
     * 根据名字模糊查询
     * @param context 查询内容
     * @param page 页
     * @param size 数量
     */
    public static List<EsLeagueNameInfo> searchByName(String context, int page, int size){
        List<EsLeagueNameInfo> result = new ArrayList<>();
//        try {
//            result.addAll(EsUtil.wildcardSearch(EsIndexType.league_name.genIndexName()
//                    , "name", context, EsLeagueNameInfo.class, page, size));
//        } catch (IOException ignored) {
//        }
        return result;
    }

    /**
     * 移除联盟名字
     */
    public static void removeLeagueName(long leagueId){
//        try {
//            EsUtil.accurateDelete(EsIndexType.league_name.genIndexName(), leagueId + "");
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
    }

    /**
     * 关闭
     */
    public static void shutdown() {
        List<LeagueData> allList = leagues.values().stream().map(League::buildLeagueData).collect(Collectors.toList());
        //存储所有帮派数据
        DbManager.updateBatch(allList);
    }

    @Override
    public void tick() {
        for (League league : leagues.values()) {
            league.tick();
        }

        systemLeagueCheck();
    }

    /**
     * 开服前两天内，若发现没有非满员系统帮派，自动创建一个
     */
    public void systemLeagueCheck(){
        if(World.getOpenServerDay() <= 2 && !systemLeagueCreating){
            boolean needCreate = true;
            for(League league : leagues.values()){
                LeagueBaseModel baseModel = league.getModel(LeagueModelEnums.base);
                if(baseModel.isSystemLeague() && !league.isFull()){
                    needCreate = false;
                    break;
                }
            }

            if(needCreate){
                systemLeagueCreating = true;
                ThreadPool.execute(new LeagueAutoCreateAsyncCall());
            }
        }
    }


    public boolean isSystemLeagueCreating() {
        return systemLeagueCreating;
    }

    public void setSystemLeagueCreating(boolean systemLeagueCreating) {
        this.systemLeagueCreating = systemLeagueCreating;
    }

    @Override
    public long getRunnerInterval() {
        return 500;
    }

    /**
     * 必须异步调用
     */
    public static Map<Long, MiniLeague> loadMiniLeagues(Collection<Long> lids){
        Set<String> miniLeagueKeys = new HashSet<>();
        for (Long lid : lids) {
            miniLeagueKeys.add(GsRedisKey.League.mini_league.getRedisKey(lid));
        }
        Map<String, MiniLeague> leagues = TLBase.getInstance().getRedisAssistant().mget(50, MiniLeague.class, miniLeagueKeys.toArray(new String[0]));

        Map<Long, MiniLeague> rst = new HashMap<>();
        if(CollectionUtil.isNotEmpty(leagues)){
            for(MiniLeague miniLeague : leagues.values()){
                rst.put(miniLeague.getId(), miniLeague);
            }
        }

        return rst;
    }
}

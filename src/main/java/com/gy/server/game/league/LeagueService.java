package com.gy.server.game.league;

import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.Configuration;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.log.SystemLogger;
import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.common.ConstantConfigReader;
import com.gy.server.game.currency.Currency;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.event.ServerEvent;
import com.gy.server.game.event.ServerEventHandler;
import com.gy.server.game.event.ServerEventType;
import com.gy.server.game.function.Function;
import com.gy.server.game.global.GlobalData;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.keyword.KeyWordService;
import com.gy.server.game.league.async.*;
import com.gy.server.game.league.bean.LeagueCustomJobInfo;
import com.gy.server.game.league.bean.LeagueJobInfo;
import com.gy.server.game.league.bean.LeagueMember;
import com.gy.server.game.league.enums.LeagueJobEnums;
import com.gy.server.game.league.enums.LeagueJobTypeEnums;
import com.gy.server.game.league.enums.LeaguePlayTypeEnums;
import com.gy.server.game.league.log.LeagueLog;
import com.gy.server.game.league.log.LeagueLogListType;
import com.gy.server.game.league.log.LeagueLogType;
import com.gy.server.game.league.model.LeagueBaseModel;
import com.gy.server.game.league.template.*;
import com.gy.server.game.liberty.LibertyHelper;
import com.gy.server.game.liberty.effect.LibertyType;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.mail.MailManager;
import com.gy.server.game.mail.MailType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.game.rank.RankItem;
import com.gy.server.game.rank.RankManager;
import com.gy.server.game.rank.RankType;
import com.gy.server.game.reddot.RedDot;
import com.gy.server.game.service.Service;
import com.gy.server.game.text.Text;
import com.gy.server.game.text.TextParamText;
import com.gy.server.game.warZone.WarZoneGlobalData;
import com.gy.server.game.world.World;
import com.gy.server.game.world.WorldBaseGlobalData;
import com.gy.server.packet.PbCommons;
import com.gy.server.packet.PbLeague;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.CollectionUtil;
import com.ttlike.server.tl.baselib.thread.ThreadPool;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.time.DayOfWeek;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.gy.server.game.event.ServerEventType.day5clock;

/**
 * 帮派服务类
 *
 * <AUTHOR> - [Created on 2023/2/20 15:36]
 */
public class LeagueService extends PlayerPacketHandler implements Service, ServerEventHandler {
    private static final ServerEventType[] eventTypes = new ServerEventType[]{day5clock};

    /**
     * 帮派常量
     */
    private static LeagueConstant constant;
    /**
     * 帮派等级表 Key:等级
     */
    private static Map<Integer, LeagueLevelTemplate> leagueLevelTemplateMap = new HashMap<>();
    /**
     * 表里最高等级
     */
    private static int leagueMaxLevel;
    /**
     * 帮派边框表 Key:ID
     */
    private static Map<Integer, LeagueFrameTemplate> leagueFrameTemplateMap = new HashMap<>();
    /**
     * 帮派建设 Key:次数
     */
    private static Map<Integer, LeagueBuildTemplate> leagueBuildTemplateMap = new HashMap<>();
    /**
     * 表里最大次数
     */
    private static int leagueBuildMaxNum;
    /**
     * 职位权限 Key:JOB
     */
    private static Map<Integer, LeagueJobTemplate> leagueJobTemplateMap = new HashMap<>();

    /**
     * 刷新间隔 毫秒
     */
    private static final int REFRESH_INTERVAL = 500;

    /**
     * 天工配表
     */
    private static volatile Map<Integer, LeagueTGTemplate> leagueTGTemplateMap = new HashMap<>();
    private static volatile Map<String, Integer> leagueType2Id = new HashMap<>();
    private static volatile Map<Integer, Boolean> limitsTypes = new HashMap<>();

    /**
     * 帮派玩法限制
     */
    private static volatile Map<Integer, LeagueEntranceTemplate> leagueEntranceTemplateMap = new HashMap<>();

    /**
     * 创建帮派
     */
    @Handler(PtCode.LEAGUE_CREATE_CLIENT)
    private void leagueCreate(Player player, PbProtocol.LeagueCreateReq req, long time) {
        PbProtocol.LeagueCreateRst.Builder rst = PbProtocol.LeagueCreateRst.newBuilder().setResult(Text.genOkServerRstInfo());
        String name = req.getName();
//        String iconName = req.getIconName();
        String notice = req.getNotice();
        int iconFrameId = req.getIconFrameId();
        int iconId = req.getIconId();
        logic:
        {
            if (Function.league.isNotOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            int result = LeagueManager.checkValidName(name);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }
            result = LeagueManager.checkValidNotice(notice);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }
            result = LeagueManager.checkValidIconFrameId(player, iconFrameId);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }
            if (LeagueManager.isJoinLeague(player)) {
                rst.setResult(Text.genServerRstInfo(Text.已加入帮派));
                break logic;
            }
            //创建帮派等级
            if (player.getLevel() < constant.leagueBuildLevel) {
                rst.setResult(Text.genServerRstInfo(Text.创建帮派等级不足));
                break logic;
            }
            // 检查扣除
            int check = Reward.check(player, Reward.templateCollectionToReward(constant.leagueBuildCost));
            if (check != -1) {
                rst.setResult(Text.genServerRstInfo(Text.创建帮派消耗不足));
                break logic;
            }
            if (rst.getResult().getResult()) {
                ThreadPool.execute(new LeagueCreateAsyncCall(player, name, iconId + "", notice, iconFrameId, time));
                return;
            }

        }

        player.send(PtCode.LEAGUE_CREATE_SERVER, rst.build(), time);

    }

    /**
     * 帮派修改
     */
    @Handler(PtCode.LEAGUE_UPDATE_CLIENT)
    private void leagueUpdate(Player player, PbProtocol.LeagueUpdateReq req, long time) {
        PbProtocol.LeagueUpdateRst.Builder rst = PbProtocol.LeagueUpdateRst.newBuilder().setResult(Text.genOkServerRstInfo());
        String name = req.getName();
//        String iconName = req.getIconName();
        String notice = req.getNotice();
        int iconFrameId = req.getIconFrameId();
        int iconId = req.getIconId();
        logic:
        {
            if (Function.league.isNotOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            if (LeagueManager.isNotJoinLeague(player)) {
                rst.setResult(Text.genServerRstInfo(Text.未加入帮派));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            // 判断权限
            if (!league.hasAuth(player.getPlayerId(), LeagueJobTypeEnums.帮派设置)) {
                rst.setResult(Text.genServerRstInfo(Text.帮派权限不足));
                break logic;
            }
            int result;
            boolean isUpdate = false;
            LeagueBaseModel baseModel = league.getModel(LeagueModelEnums.base);
            boolean changeName = false;
            if (StringUtils.isNotBlank(name) && !name.equals(league.getName())) {
                result = LeagueManager.checkValidName(name);
                if (result != -1) {
                    rst.setResult(Text.genServerRstInfo(result));
                    break logic;
                }
                isUpdate = true;
                changeName = true;
            }
            if (baseModel.getSetChangeNum() >= constant.LeagueSetChangeNum) {
                rst.setResult(Text.genServerRstInfo(Text.每月修改次数达到上限));
                break logic;
            }
            List<RewardTemplate> costList = new ArrayList<>(constant.LeagueSetChangeCost);
            if (StringUtils.isNotBlank(notice) && !notice.equals(league.getNotice())) {
                result = LeagueManager.checkValidNotice(notice);
                if (result != -1) {
                    rst.setResult(Text.genServerRstInfo(result));
                    break logic;
                }
                isUpdate = true;
            }
            if (iconFrameId != 0 && iconFrameId != league.getIconFrameId()) {
                result = LeagueManager.checkValidIconFrameId(player, iconFrameId);
                if (result != -1) {
                    rst.setResult(Text.genServerRstInfo(result));
                    break logic;
                }
                isUpdate = true;
            }
            if (!isUpdate) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            if (!costList.isEmpty()) {
                // 检查扣除
                List<Reward> cost = Reward.templateCollectionToReward(costList);
                int check = Reward.check(player, cost);
                if (check != -1) {
                    rst.setResult(Text.genServerRstInfo(Text.帮派修改消耗不足));
                    break logic;
                }

                if(!changeName) {
                    Reward.remove(cost, player, BehaviorType.LeagueUpdate);
                }
            }

            if(changeName){
                //涉及改名，需要走异步流程
                ThreadPool.execute(new LeagueUpdateAsyncCall(league, player, req, time));
                return;
            }

            // 修改次数
            baseModel.setSetChangeNum(baseModel.getSetChangeNum() + 1);
            // 修改信息
            league.updateBaseInfo(name, iconId, iconFrameId, notice);

            if(!name.isEmpty()){
                LeagueManager.updateLeagueToEs(league, true);
            }

            rst.setLeague(league.genLeague());
            rst.setMyLeague(league.genMyLeague(player));
        }

        player.send(PtCode.LEAGUE_UPDATE_SERVER, rst.build(), time);

    }

    /**
     * 申请加入帮派
     */
    @Handler(PtCode.LEAGUE_APPLY_CLIENT)
    private void leagueApply(Player player, PbProtocol.LeagueApplyReq req, long time) {
        PbProtocol.LeagueApplyRst.Builder rst = PbProtocol.LeagueApplyRst.newBuilder().setResult(Text.genOkServerRstInfo());
        long leagueId = req.getLeagueId();
        logic:
        {
            if (Function.league.isNotOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            if (LeagueManager.isJoinLeague(player)) {
                rst.setResult(Text.genServerRstInfo(Text.已加入帮派));
                break logic;
            }
            League league = LeagueManager.getLeagueById(leagueId);
            if (Objects.isNull(league)) {
                rst.setResult(Text.genServerRstInfo(Text.已加入帮派));
                break logic;
            }

            PlayerLeagueModel model = player.getModel(PlayerModelEnums.league);
            if(ServerConstants.getCurrentTimeMillis() < model.getCanPlayerLeagueTime() && model.getCanPlayerLeagueTime() != 0){
                rst.setResult(Text.genServerRstInfo(Text.您退出帮派未满足24小时加入帮派));
                break logic;
            }

            if (LeagueManager.isApplyFull(player.getPlayerId())) {
                rst.setResult(Text.genServerRstInfo(Text.申请帮派数量达到上限));
                break logic;
            }
            if (LeagueManager.isApplyAppointLeague(leagueId, player.getPlayerId())) {
                rst.setResult(Text.genServerRstInfo(Text.已经申请过该帮派));
                break logic;
            }
            if (league.isFull()) {
                rst.setResult(Text.genServerRstInfo(Text.帮派人员已满));
                break logic;
            }
            // 检查帮派的加入条件
            if (!league.isReachJoinCondition(player)) {
                rst.setResult(Text.genServerRstInfo(Text.不满足帮派加入条件));
                break logic;
            }
            rst.setLeagueId(leagueId);
            rst.setIsAutoAgree(league.isAutoAgree());
            if (league.isAutoAgree()) {
                //直接加入
                LeagueManager.playerJoin(league, player, false);
                rst.setLeague(league.genMyLeague(player));
            } else {
                //加入申请列表
                LeagueManager.playerApply(league, player.getPlayerId());
                //帮主/副帮主同步红点
                for (Long viceLeader : league.getViceLeaderSet()) {
                    if(PlayerManager.isOnline(viceLeader)){
                        RedDot.leagueApplyJoin.sync(PlayerManager.getOnlinePlayer(viceLeader));
                    }
                }
                if(PlayerManager.isOnline(league.getLeader())){
                    RedDot.leagueApplyJoin.sync(PlayerManager.getOnlinePlayer(league.getLeader()));
                }

            }
        }

        player.send(PtCode.LEAGUE_APPLY_SERVER, rst.build(), time);

    }

    /**
     * 快速一键加入帮派
     */
    @Handler(PtCode.LEAGUE_QUICK_JOIN_CLIENT)
    private void leagueQuickJoin(Player player, long time) {
        PbProtocol.LeagueQuickJoinRst.Builder rst = PbProtocol.LeagueQuickJoinRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:
        {
            if (Function.league.isNotOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            if (LeagueManager.isJoinLeague(player)) {
                rst.setResult(Text.genServerRstInfo(Text.已加入帮派));
                break logic;
            }

            PlayerLeagueModel model = player.getModel(PlayerModelEnums.league);
            if(ServerConstants.getCurrentTimeMillis() < model.getCanPlayerLeagueTime() && model.getCanPlayerLeagueTime() != 0){
                rst.setResult(Text.genServerRstInfo(Text.您退出帮派未满足24小时加入帮派));
                break logic;
            }

            //遍历加入可直接加入的帮派
            List<League> leagues = LeagueManager.getLeagues();
            List<League> canJoinLeagueList = leagues.stream().filter(league -> !league.isFull() && league.isReachJoinCondition(player)).collect(Collectors.toList());
            if (canJoinLeagueList.isEmpty()) {
                rst.setResult(Text.genServerRstInfo(Text.未找到可以加入的帮派));
                break logic;
            }
            // 建设值从高到底排序
            // 先优先判断能否自动加入
            List<League> canAutoJoinList = canJoinLeagueList.stream().filter(League::isAutoAgree).collect(Collectors.toList());
            if (canAutoJoinList.isEmpty()) {
                if (LeagueManager.isApplyFull(player.getPlayerId())) {
                    rst.setResult(Text.genServerRstInfo(Text.申请帮派数量达到上限));
                    break logic;
                }
                int remainCount = constant.leagueApplyMaxNum - LeagueManager.getNotExpiredApplySet(player.getPlayerId()).size();
                int limitCount = Math.min(remainCount, constant.leagueQuickApplyMaxNum);
                //加入前10审批
                List<League> applyList = canJoinLeagueList.stream()
                        .sorted(Comparator.comparing(League::getWeekContribution).reversed())
                        .limit(limitCount)
                        .collect(Collectors.toList());
                for (League league : applyList) {
                    rst.addLeagueId(league.getLeagueId());
                    LeagueManager.playerApply(league, player.getPlayerId());
                }
                rst.setIsAutoAgree(false);
            } else {
                List<League> autoLeagueList = canJoinLeagueList.stream().filter(League::isAutoAgree).filter(l-> !l.isSystemLeague())
                        .sorted(Comparator.comparing(League::getWeekContribution).reversed())
                        .collect(Collectors.toList());
                if(autoLeagueList.isEmpty()){
                    //优先加入玩家帮派
                    autoLeagueList = canJoinLeagueList.stream().filter(League::isAutoAgree)
                            .sorted(Comparator.comparing(League::getWeekContribution).reversed())
                            .collect(Collectors.toList());
                }
                League league = autoLeagueList.get(0);
                LeagueManager.playerJoin(league, player, true);
                rst.setLeague(league.genMyLeague(player));
                rst.setIsAutoAgree(true);
            }
        }

        player.send(PtCode.LEAGUE_QUICK_JOIN_SERVER, rst.build(), time);

    }

    /**
     * 帮派列表
     */
    @Handler(PtCode.LEAGUE_LIST_CLIENT)
    private void list(Player player, PbProtocol.LeagueListReq req, long time) {
        PbProtocol.LeagueListRst.Builder rst = PbProtocol.LeagueListRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        logic:
        {
            // 第几页从1开始
            int page = req.getPage();
            if (page < 1 || page > 999) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            if (Function.league.isNotOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
//            if (LeagueManager.isJoinLeague(player)) {
//                rst.setResult(Text.genServerRstInfo(Text.已加入帮派));
//                break logic;
//            }
            int pageSize = constant.leaguePageNum;
            String content = req.getContent();
            List<League> leagues = LeagueManager.searchLeagues(content);
//            List<League> sortLeagues = leagues.stream()
//                    .sorted(Comparator.comparing(League::getBuildValue).reversed())
//                    .collect(Collectors.toList());

            League systemLeague = leagues.stream().filter(League::isSystemLeague).filter(l->!l.isFull()).findFirst().orElse(null);

            leagues = leagues.stream().filter(l->!l.isSystemLeague()).collect(Collectors.toList());

            List<League> sortLeagues = leagues.stream()
                    .sorted((o1, o2) -> {
                        //帮派等级>帮主战力>帮派周贡献>帮派编号
                        if (o1.isSystemLeague() == o2.isSystemLeague()) {
                            if (o1.isSystemLeague() || o2.isSystemLeague()) {
                                return Long.compare(o1.getLeagueId(), o2.getLeagueId());
                            }
                            if (o1.getLevel() == o2.getLevel()) {
                                MiniGamePlayer leagueLeader1 = PlayerManager.getMiniPlayer(o1.getLeader());
                                MiniGamePlayer leagueLeader2 = PlayerManager.getMiniPlayer(o2.getLeader());
                                if (leagueLeader2.getFightingPower() == leagueLeader1.getFightingPower()) {
                                    if (o2.getWeekContribution() == o1.getWeekContribution()) {
                                        return (int) (o2.getLeagueId() - o1.getLeagueId());
                                    }
                                    return (int) (o2.getWeekContribution() - o1.getWeekContribution());
                                }
                                return (int) (leagueLeader2.getFightingPower() - leagueLeader1.getFightingPower());
                            }
                            return o2.getLevel() - o1.getLevel();
                        } else {
                            return Boolean.compare(o1.isSystemLeague(), o2.isSystemLeague());
                        }
                    })
                    .collect(Collectors.toList());


            if(systemLeague != null){
                if(sortLeagues.size() > 4){
                    sortLeagues.add(4, systemLeague);
                }else{
                    sortLeagues.add(systemLeague);
                }
            }

            int totalRecords = sortLeagues.size();
            int totalPages = totalRecords % pageSize == 0 ? totalRecords / pageSize
                    : totalRecords / pageSize + 1;

            rst.setTotalPage(totalPages);
            int skipNum = (page - 1) * pageSize;
            rst.setPage(page);
            sortLeagues.stream().skip(skipNum).limit(pageSize).forEach(league -> {
                PbLeague.ListLeague.Builder listLeaguePb = PbLeague.ListLeague.newBuilder()
                        .setApply(LeagueManager.isApplyAppointLeague(league.getLeagueId(), player.getPlayerId()))
                        .setLeague(league.genLeague());
                rst.addLeagues(listLeaguePb);
            });
        }
        player.send(PtCode.LEAGUE_LIST_SERVER, rst.build(), time);
    }

    private void leagueSort(List<League> sortLeagues){
        sortLeagues.stream()
                .sorted((o1, o2) -> {
                    //帮派等级>帮主战力>帮派周贡献>帮派编号
                    if (o1.isSystemLeague() == o2.isSystemLeague()) {
                        if (o1.isSystemLeague()) {
                            return Long.compare(o1.getLeagueId(), o2.getLeagueId());
                        }
                        if (o1.getLevel() == o2.getLevel()) {
                            MiniGamePlayer leagueLeader1 = PlayerManager.getMiniPlayer(o1.getLeader());
                            MiniGamePlayer leagueLeader2 = PlayerManager.getMiniPlayer(o2.getLeader());
                            long fightPower1 = leagueLeader1 == null ? 0l : leagueLeader1.getFightingPower();
                            long fightPower2 = leagueLeader2 == null ? 0l : leagueLeader2.getFightingPower();
                            if (fightPower2 == fightPower1) {
                                if (o2.getWeekContribution() == o1.getWeekContribution()) {
                                    return (int) (o2.getLeagueId() - o1.getLeagueId());
                                }
                                return (int) (o2.getWeekContribution() - o1.getWeekContribution());
                            }
                            return (int) (leagueLeader2.getFightingPower() - leagueLeader1.getFightingPower());
                        }
                        return o2.getLevel() - o1.getLevel();
                    } else {
                        return Boolean.compare(o1.isSystemLeague(), o2.isSystemLeague());
                    }
                })
                .collect(Collectors.toList());
    }

    /**
     * 我的帮派信息
     */
    @Handler(PtCode.LEAGUE_INFO_CLIENT)
    private void leagueInfo(Player player, long time) {
        PbProtocol.LeagueInfoRst.Builder rst = PbProtocol.LeagueInfoRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:
        {
            if (Function.league.isNotOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            if (LeagueManager.isNotJoinLeague(player)) {
                rst.setResult(Text.genServerRstInfo(Text.未加入帮派));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            rst.setLeague(league.genMyLeague(player));
        }

        player.send(PtCode.LEAGUE_INFO_SERVER, rst.build(), time);

    }

    /**
     * 群发邮件
     */
    @Handler(PtCode.LEAGUE_MAIL_CLIENT)
    private void batchMail(Player player, PbProtocol.LeagueMailReq req, long time) {
        PbProtocol.LeagueMailRst.Builder rst = PbProtocol.LeagueMailRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:
        {
            if (Function.league.isNotOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            String content = req.getContent().trim();
            String title = req.getTitle().trim();
            int result = LeagueManager.checkValidMail(title, content);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }
            if (LeagueManager.isNotJoinLeague(player)) {
                rst.setResult(Text.genServerRstInfo(Text.未加入帮派));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            if (!league.hasAuth(player.getPlayerId(), LeagueJobTypeEnums.群发消息)) {
                rst.setResult(Text.genServerRstInfo(Text.帮派权限不足));
                break logic;
            }
            // 发送次数
            int remainSendMailCount = league.getRemainSendMailCount();
            if (remainSendMailCount <= 0) {
                //次数不足
                rst.setResult(Text.genServerRstInfo(Text.帮派发送邮件次数不足));
                break logic;
            }
            // 增加次数
            league.addSendMailCount();
            //发邮件
            MailType mailType = MailType.league_batch_send;
            PbCommons.PbText titleText = Text.genText(mailType.getTitleId(), new TextParamText(KeyWordService.mark(title))).build();
            PbCommons.PbText contentText = Text.genText(mailType.getContentId(), new TextParamText(KeyWordService.mark(content))).build();
            String sender = KeyWordService.mark(player.getName());
            for (Long targetId : league.getMemberMap().keySet()) {
                MailManager.sendMail(mailType, targetId, titleText, contentText, ServerConstants.getCurrentTimeMillis(), sender);
            }
            //构造返回
            rst.setRemainMailCount(league.getRemainSendMailCount());
        }

        player.send(PtCode.LEAGUE_MAIL_SERVER, rst.build(), time);

    }

    /**
     * 弹劾帮主
     */
    @Handler(PtCode.LEAGUE_APPLY_LEADER_CLIENT)
    private void applyLeader(Player player, long time) {
        PbProtocol.LeagueApplyLeaderRst.Builder rst = PbProtocol.LeagueApplyLeaderRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:
        {
            if (Function.league.isNotOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            if (LeagueManager.isNotJoinLeague(player)) {
                rst.setResult(Text.genServerRstInfo(Text.未加入帮派));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            if (league.getLeader() == player.getPlayerId()) {
                rst.setResult(Text.genServerRstInfo(Text.自己不能弹劾自己));
                break logic;
            }
            if (league.isApplyLeader(player)) {
                rst.setResult(Text.genServerRstInfo(Text.已经申请弹劾帮主请勿重复申请));
                break logic;
            }
            if (!league.leaderCanDown()) {
                rst.setResult(Text.genServerRstInfo(Text.不满足弹劾条件));
                break logic;
            }
            league.leaderDown(player);
            rst.setLeaderDownTime(league.getLeaderDownTime());
        }

        player.send(PtCode.LEAGUE_APPLY_LEADER_SERVER, rst.build(), time);

    }

    /**
     * 让位帮主
     */
    @Handler(PtCode.LEAGUE_ABDICATE_CLIENT)
    private void abdicate(Player player, PbProtocol.LeagueAbdicateReq req, long time) {
        PbProtocol.LeagueAbdicateRst.Builder rst = PbProtocol.LeagueAbdicateRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:
        {
            long memberId = req.getMemberId();
            int result = appointCheck(player, memberId);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            league.changeLeader(memberId, player, 1);

            rst.setMemberId(memberId);
        }

        player.send(PtCode.LEAGUE_ABDICATE_SERVER, rst.build(), time);

    }

    /**
     * 修改成员权限
     */
    @Handler(PtCode.LEAGUE_APPOINT_VICE_CLIENT)
    private void appointVice(Player player, PbProtocol.LeagueAppointViceReq req, long time) {
        PbProtocol.LeagueAppointViceRst.Builder rst = PbProtocol.LeagueAppointViceRst.newBuilder().setResult(Text.genOkServerRstInfo());
        long memberId = req.getMemberId();
        logic:
        {
            boolean isAppoint = req.getIsAppoint();
            int jobId = req.getJobId();
            int jobIndex = req.getJobIndex();
            int result = appointCheck(player, memberId);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }
            MiniGamePlayer memberMiniPlayer = PlayerManager.getMiniPlayer(memberId);
            League league = LeagueManager.getLeagueByPlayer(player);
            //检查是否有任命权限
            if(!league.hasAuth(player.getPlayerId(), LeagueJobTypeEnums.职位任免)){
                rst.setResult(Text.genServerRstInfo(Text.帮派权限不足));
                break logic;
            }
            if(Objects.isNull(LeagueJobEnums.of(jobId))){
                rst.setResult(Text.genServerRstInfo(Text.帮派职位错误));
                break logic;
            }
            LeagueMember playerMember = league.getMember(player.getPlayerId());
            LeagueMember targetMember = league.getMember(memberId);
            LeagueJobTemplate memberJobTemplate = leagueJobTemplateMap.get(targetMember.getJobId());
            LeagueJobTemplate playerJobTemplate = leagueJobTemplateMap.get(playerMember.getJobId());
            if(playerJobTemplate.Rank > memberJobTemplate.Rank){
                rst.setResult(Text.genServerRstInfo(Text.帮派权限不足));
                break logic;
            }
            int oldJob = league.getJob(player.getPlayerId());
            if (isAppoint) {
                //检查人数
                Set<Long> memberIds = league.getMemberByJob(jobId);
                LeagueJobTemplate leagueJobTemplate = leagueJobTemplateMap.get(jobId);
                if(memberIds.size() >= leagueJobTemplate.PositionNum){
                    rst.setResult(Text.genServerRstInfo(Text.任命职位人数已满));
                    break logic;
                }

                if(targetMember.getJobId() == jobId){
                    rst.setResult(Text.genServerRstInfo(Text.不能重复任命));
                    break logic;
                }
                if(jobId == LeagueJobEnums.xinzeng.getID()){
                    //检查自定义职位是否存在
                    if(!league.getCustomJobAuths().containsKey(jobIndex)){
                        rst.setResult(Text.genServerRstInfo(Text.帮派自定义职位不存在));
                        break logic;
                    }
                    //检查职位上是否有人
                    for (Long jobMemberId : memberIds) {
                        LeagueMember member1 = league.getMember(jobMemberId);
                        if(Objects.nonNull(member1) && member1.getJobIndex() == jobIndex){
                            rst.setResult(Text.genServerRstInfo(Text.帮派自定义职位已经有人));
                            break logic;
                        }
                    }
                    targetMember.setJobIndex(jobIndex);
                }
                targetMember.setJobId(jobId);
                //任命日志
                league.addLeagueLog(LeagueLogType.unionAppoint, oldJob + ""
                        , player.getName()
                        , memberMiniPlayer.getName()
                        , jobId + "");
            } else {
                // 取消任命
                targetMember.setJobId(LeagueJobEnums.bangzhong.getID());
                //任命日志
                league.addLeagueLog(LeagueLogType.unionRecall, oldJob + ""
                        , player.getName()
                        , memberMiniPlayer.getName()
                        , jobId + "");
            }
            rst.setIsAppoint(isAppoint);
            rst.setMemberId(memberId);
            rst.setJobId(jobId);
            rst.setJobIndex(jobIndex);
            if(PlayerManager.isOnline(memberId)){
                PlayerManager.getPlayer(memberId).send(PtCode.LEAGUE_APPOINT_VICE_SERVER, rst.build(), time);
            }
        }
        player.send(PtCode.LEAGUE_APPOINT_VICE_SERVER, rst.build(), time);

    }

    private int appointCheck(Player leaderPlayer, long memberId) {
        if (Function.league.isNotOpen(leaderPlayer)) {
            return Text.功能未开启;
        }
        if (LeagueManager.isNotJoinLeague(leaderPlayer)) {
            return Text.未加入帮派;
        }
        if (memberId == leaderPlayer.getPlayerId()) {
            return Text.参数异常;
        }
        League league = LeagueManager.getLeagueByPlayer(leaderPlayer);
        if (!league.getMemberMap().containsKey(memberId)) {
            return Text.帮派不能操作自己;
        }

        return Text.没有异常;
    }

    /**
     * 帮派捐献
     */
    @Handler(PtCode.LEAGUE_DONATION_CLIENT)
    private void donation(Player player, long time) {
        PbProtocol.LeagueDonationRst.Builder rst = PbProtocol.LeagueDonationRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:
        {
            if (Function.league.isNotOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            if (LeagueManager.isNotJoinLeague(player)) {
                rst.setResult(Text.genServerRstInfo(Text.未加入帮派));
                break logic;
            }
            PlayerLeagueModel leagueModel = player.getModel(PlayerModelEnums.league);
            int todayDonationCount = leagueModel.getDonationCount();
            if (todayDonationCount >= constant.leagueBuildMaxNum) {
                rst.setResult(Text.genServerRstInfo(Text.今日捐献次数已达上限));
                break logic;
            }
            Integer num = Math.min(todayDonationCount + 1, leagueBuildMaxNum);
            LeagueBuildTemplate leagueBuildTemplate = leagueBuildTemplateMap.get(num);
            League league = LeagueManager.getLeagueByPlayer(player);
            if (!leagueBuildTemplate.cost.isEmpty()) {
                List<Reward> costList = Reward.templateCollectionToReward(leagueBuildTemplate.cost);
                if (Reward.check(player, costList) != -1) {
                    rst.setResult(Text.genServerRstInfo(Text.消耗不足));
                    break logic;
                }

                boolean isGold = costList.get(0).getType() == Currency.gold.getId() || costList.get(0).getType() == Currency.boundGold.getId() ? true : false;
                Reward.remove(costList, player, BehaviorType.LeagueDonationCost);
                //元宝捐献日志
                league.addLeagueLog(isGold ? LeagueLogType.buildGold : LeagueLogType.build, player.getName());
            }else{
                // 增加LOG
                league.addLeagueLog(LeagueLogType.build, player.getName());
            }
            List<Reward> rewardList = leagueModel.donation(leagueBuildTemplate);

            //特权
            LibertyHelper.checkCurrencyRewardLiberty(LibertyType.leagueRate, player, rewardList);

            rst.addAllReward(Reward.writeCollectionToPb(rewardList));
            //增加联盟帮贡
            league.addLeagueTribute(leagueBuildTemplate.tribute);
            rst.setLeague(LeagueManager.getLeagueByPlayer(player).genMyLeague(player));
            rst.setLeagueLog(league.getLeagueLogList(LeagueLogListType.build).get(0).writeToPb());
        }

        player.send(PtCode.LEAGUE_DONATION_SERVER, rst.build(), time);

    }

    /**
     * 查看帮派日志
     */
    @Handler(PtCode.LEAGUE_LOG_CLIENT)
    private void leagueLog(Player player, PbProtocol.LeagueLogReq req, long time) {
        PbProtocol.LeagueLogRst.Builder rst = PbProtocol.LeagueLogRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:
        {
            if (Function.league.isNotOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            if (LeagueManager.isNotJoinLeague(player)) {
                rst.setResult(Text.genServerRstInfo(Text.未加入帮派));
                break logic;
            }
            LeagueLogListType logListType = LeagueLogListType.of(req.getLogListType());
            if (Objects.isNull(logListType)) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);

            List<LeagueLog> leagueLogList = league.getLeagueLogList(logListType, player.getPlayerId());
            for (LeagueLog leagueLog : leagueLogList) {
                rst.addLeagueLog(leagueLog.writeToPb());
            }
        }

        player.send(PtCode.LEAGUE_LOG_SERVER, rst.build(), time);

    }

    /**
     * 帮派踢人
     */
    @Handler(PtCode.LEAGUE_KICK_CLIENT)
    private void leagueKick(Player player, PbProtocol.LeagueKickReq req, long time) {
        ThreadPool.execute(new LeagueKickAsyncCall(player, req.getMemberId(), time));

    }

    /**
     * 退出帮派
     */
    @Handler(PtCode.LEAGUE_QUIT_CLIENT)
    private void leagueQuit(Player player, long time) {
        PbProtocol.LeagueKickRst.Builder rst = PbProtocol.LeagueKickRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:
        {
            if (Function.league.isNotOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            if (LeagueManager.isNotJoinLeague(player)) {
                rst.setResult(Text.genServerRstInfo(Text.未加入帮派));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            int myJob = league.getJob(player.getPlayerId());
            if (myJob == LeagueJobEnums.leader.getID() && league.getMemberCount() > 1) {
                rst.setResult(Text.genServerRstInfo(Text.帮派还有其他人帮主不能退出帮派));
                break logic;
            }
            league.quit(player);

            //添加日志
            league.addLeagueLog(LeagueLogType.unionQuit, myJob + "", player.getName());
        }
        player.send(PtCode.LEAGUE_QUIT_SERVER, rst.build(), time);

    }

    /**
     * 查看申请列表
     */
    @Handler(PtCode.LEAGUE_APPLY_LIST_CLIENT)
    private void leagueApplyList(Player player, long time) {
        PbProtocol.LeagueApplyListRst.Builder rst = PbProtocol.LeagueApplyListRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:
        {
            if (Function.league.isNotOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            if (LeagueManager.isNotJoinLeague(player)) {
                rst.setResult(Text.genServerRstInfo(Text.未加入帮派));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            if (!league.hasAuth(player.getPlayerId(), LeagueJobTypeEnums.申请列表)) {
                rst.setResult(Text.genServerRstInfo(Text.帮派权限不足));
                break logic;
            }
            Set<Long> allApplies = league.getNotOverdueApplies();
            for (Long applyPlayerId : allApplies) {
                MiniGamePlayer miniPlayer = PlayerManager.getMiniPlayer(applyPlayerId);
                if (Objects.nonNull(miniPlayer)) {
                    PbLeague.LeagueApplyUser.Builder applyBuilder = PbLeague.LeagueApplyUser.newBuilder()
                            .setApplyTime(league.getAppliesMap().get(applyPlayerId))
                            .setMinUser(miniPlayer.genBaseMiniUser());
                    if (PlayerManager.isOnline(miniPlayer.getPlayerId())) {
                        applyBuilder.setLastLogoutTime(-1);
                    } else {
                        applyBuilder.setLastLogoutTime(miniPlayer.getLastLogoutTime());
                    }
                    rst.addApplyUser(applyBuilder);
                }
            }
        }
        player.send(PtCode.LEAGUE_APPLY_LIST_SERVER, rst.build(), time);
    }

    /**
     * 审批帮派申请
     */
    @Handler(PtCode.LEAGUE_APPLY_AGREE_CLIENT)
    private void leagueApplyAgree(Player player, PbProtocol.LeagueApplyAgreeReq req, long time) {
        long applyPlayerId = req.getPlayerId();
        boolean isAgree = req.getIsAgree();
        ThreadPool.execute(new LeagueApplyAgreeAsyncCall(player, applyPlayerId, isAgree, time));
    }

    /**
     * 迎新设置
     */
    @Handler(PtCode.LEAGUE_WELCOME_SET_CLIENT)
    private void welcomeSet(Player player, PbProtocol.LeagueWelcomeSetReq req, long time) {
        PbProtocol.LeagueWelcomeSetRst.Builder rst = PbProtocol.LeagueWelcomeSetRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:
        {
            if (Function.league.isNotOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            if (LeagueManager.isNotJoinLeague(player)) {
                rst.setResult(Text.genServerRstInfo(Text.未加入帮派));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            if (!league.hasAuth(player.getPlayerId(), LeagueJobTypeEnums.迎新设置)) {
                rst.setResult(Text.genServerRstInfo(Text.帮派权限不足));
                break logic;
            }
            int joinLevel = req.getJoinLevel();
            if (joinLevel < 1 || joinLevel > player.getMaxLevel()) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            long joinFightPower = req.getJoinFightPower();
            if (joinFightPower < 1) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            //迎新设置
            league.welcomeSet(joinLevel, joinFightPower, req.getIsAutoAgree());
            //自动同意处理申请列表
            if(req.getIsAutoAgree()){
                autoAgreeEnterLeague(league);
            }
            rst.setJoinLevel(joinLevel)
                    .setJoinFightPower(joinFightPower)
                    .setIsAutoAgree(req.getIsAutoAgree());
        }

        player.send(PtCode.LEAGUE_WELCOME_SET_SERVER, rst.build(), time);

    }

    @Handler(PtCode.LEAGUE_WELCOME_CONTENT_SET_REQ)
    private void updateWelcomeSet(Player player, PbProtocol.LeagueContentSetReq req, long time) {
        PbProtocol.LeagueContentSetRst.Builder rst = PbProtocol.LeagueContentSetRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            if (Function.league.isNotOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            if (LeagueManager.isNotJoinLeague(player)) {
                rst.setResult(Text.genServerRstInfo(Text.未加入帮派));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            if (!league.hasAuth(player.getPlayerId(), LeagueJobTypeEnums.帮派宣言)) {
                rst.setResult(Text.genServerRstInfo(Text.帮派权限不足));
                break logic;
            }
            String content = req.getContent();
            int result = LeagueManager.checkValidWelcome(content);
            if (result != Text.没有异常) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }
            //迎新设置
            league.noticeSet(content);

            rst.setContent(content);
        }
        player.send(PtCode.LEAGUE_WELCOME_CONTENT_SET_RST, rst.build(), time);
    }

    /**
     * 自动进入联盟
     * @param league 联盟
     */
    private void autoAgreeEnterLeague(League league){
        //检查申请列表是否还存在玩家
        ConcurrentHashMap<Long, Long> appliesMap = league.getAppliesMap();
        List<Pair<Long, Long>> applyInfos = new ArrayList<>();
        appliesMap.forEach((k, v)->applyInfos.add(Pair.of(k, v)));
        //按申请时间排序
        applyInfos.sort((o1, o2) -> (int)(o1.getValue() - o2.getValue()));
        List<Long> waitEnterLeaguePlayerIds = new ArrayList<>();
        //开始自动进入联盟
        for(int i = 0; i < applyInfos.size(); i++){
            //检查联盟是否满了
            if (league.isFull()) {
                break;
            }
            //检查玩家是否存在联盟
            Pair<Long, Long> applyInfo = applyInfos.get(i);
            Long playerId = applyInfo.getKey();
            //检查是否进入了联盟
            if(LeagueManager.isJoinLeague(playerId)){
                continue;
            }
            //进入联盟
            if(PlayerManager.isOnline(playerId)){
                //进入联盟
                Player player = PlayerManager.getPlayer(playerId);
                LeagueManager.playerJoin(league, player, true);
            }else {
                //异步进入联盟，先暂时加入，后续异步调用playerJoinAfter接口
                LeagueManager.playerJoin(league, playerId);
                waitEnterLeaguePlayerIds.add(playerId);
            }
        }
        if(CollectionUtil.isNotEmpty(waitEnterLeaguePlayerIds)){
            ThreadPool.execute(new LeagueMemberAutoEnterAsync(league, waitEnterLeaguePlayerIds));
        }
        //清除申请信息
        appliesMap.clear();
    }

    @Handler(PtCode.LEAGUE_TG_GET_INFO_REQ)
    private void LeagueTGGetInfo(Player player, PbProtocol.LeagueTGGetInfoReq req, long time) {
        PbProtocol.LeagueTGGetInfoRst.Builder rst = PbProtocol.LeagueTGGetInfoRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            League league = LeagueManager.getLeagueByPlayer(player);
            if(Objects.isNull(league)){
                rst.setResult(Text.genServerRstInfo(Text.帮派不存在));
                break logic;
            }
            LeagueBaseModel baseModel = league.getModel(LeagueModelEnums.base);
            rst.putAllInfos(baseModel.getTgInfos());
        }
        player.send(PtCode.LEAGUE_TG_GET_INFO_RST, rst.build(), time);
    }

    @Handler(PtCode.LEAGUE_TG_UP_REQ)
    private void LeagueTGUp(Player player, PbProtocol.LeagueTGUpReq req, long time) {
        PbProtocol.LeagueTGUpRst.Builder rst = PbProtocol.LeagueTGUpRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            League league = LeagueManager.getLeagueByPlayer(player);
            if(Objects.isNull(league)){
                rst.setResult(Text.genServerRstInfo(Text.帮派不存在));
                break logic;
            }
            if (!league.hasAuth(player.getPlayerId(), LeagueJobTypeEnums.帮派天工)) {
                rst.setResult(Text.genServerRstInfo(Text.帮派权限不足));
                break logic;
            }
            int type = req.getType();
            LeagueBaseModel baseModel = league.getModel(LeagueModelEnums.base);
            Map<Integer, Integer> tgInfos = baseModel.getTgInfos();
            int nextLevel = 1;
            if(tgInfos.containsKey(type)){
                nextLevel = tgInfos.get(type) + 1;
                int nextId = leagueType2Id.get(genTgTempKey(type, nextLevel));
                if(!leagueTGTemplateMap.containsKey(nextId)){
                    rst.setResult(Text.genServerRstInfo(Text.联盟天工等级达到最大));
                    break logic;
                }
            }

            LeagueTGTemplate nextTemplate = leagueTGTemplateMap.get(leagueType2Id.get(genTgTempKey(type, nextLevel)));
            if(nextTemplate == null){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            if(nextTemplate.unlockLevel > league.getLevel()){
                rst.setResult(Text.genServerRstInfo(Text.联盟天工类型未解锁));
                break logic;
            }
            if(nextTemplate.leagueLevel > league.getLevel()){
                rst.setResult(Text.genServerRstInfo(Text.联盟天工不能升级));
                break logic;
            }
            if(nextTemplate.costs > baseModel.getLeagueTribute()){
                rst.setResult(Text.genServerRstInfo(Text.消耗不足));
                break logic;
            }
            baseModel.setLeagueTribute(baseModel.getLeagueTribute() - nextTemplate.costs);

            tgInfos.put(type, nextLevel);
            rst.setLeagueTribute(baseModel.getLeagueTribute());
            rst.putAllInfos(baseModel.getTgInfos());
        }
        player.send(PtCode.LEAGUE_TG_UP_RST, rst.build(), time);
    }

    @Handler(PtCode.LEAGUE_SEARCH_REQ)
    private void leagueSearch(Player player, PbProtocol.LeagueSearchReq req, long time) {
        PbProtocol.LeagueSearchRst.Builder rst = PbProtocol.LeagueSearchRst.newBuilder().setResult(Text.genOkServerRstInfo());
        String content = req.getContent();
        int page = req.getPage();
        boolean numeric = StringUtils.isNumeric(content);

        if(page < 0){
            rst.setResult(Text.genServerRstInfo(Text.参数异常));
            player.send(PtCode.LEAGUE_SEARCH_RST, rst.build(), time);
            return;
        }

        if(numeric){
            long leagueId = Long.parseLong(content);
            League leagueById = LeagueManager.getLeagueById(leagueId);
            if(Objects.nonNull(leagueById)){
                PbLeague.ListLeague.Builder listLeaguePb = PbLeague.ListLeague.newBuilder()
                        .setApply(LeagueManager.isApplyAppointLeague(leagueId, player.getPlayerId()))
                        .setLeague(leagueById.genLeague());
                rst.addLeagues(listLeaguePb);
            }
            rst.setPage(page);
            player.send(PtCode.LEAGUE_SEARCH_RST, rst.build(), time);
        }else{
            ThreadPool.execute(new LeagueSearchAsync(player, req, time));
        }
    }

    @Handler(PtCode.LEAGUE_DIPLOMACY_REQ)
    private void diplomacy(Player player, PbProtocol.LeagueDiplomacyReq req, long time) {
        PbProtocol.LeagueDiplomacyRst.Builder rst = PbProtocol.LeagueDiplomacyRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:
        {
            League league = LeagueManager.getLeagueByPlayer(player);
            if (Objects.isNull(league)) {
                rst.setResult(Text.genServerRstInfo(Text.帮派不存在));
                break logic;
            }
            if (Objects.isNull(LeagueManager.getLeagueById(req.getLeagueId()))) {
                rst.setResult(Text.genServerRstInfo(Text.帮派不存在));
                break logic;
            }
            if (!league.hasAuth(player.getPlayerId(), LeagueJobTypeEnums.帮派外交)) {
                rst.setResult(Text.genServerRstInfo(Text.帮派权限不足));
                break logic;
            }
            if(req.getLeagueId() == league.getLeagueId()) {
                rst.setResult(Text.genServerRstInfo(Text.帮派不能操作自己));
                break logic;
            }
            LeagueBaseModel baseModel = league.getModel(LeagueModelEnums.base);
            if(req.getIsAdd()){
                baseModel.getDiplomacyLeagueIds().add(req.getLeagueId());
            }else{
                baseModel.getDiplomacyLeagueIds().remove(req.getLeagueId());
            }
            rst.addAllDiplomacyLeagueIds(baseModel.getDiplomacyLeagueIds());
        }
        player.send(PtCode.LEAGUE_DIPLOMACY_RST, rst.build(), time);
    }

    @Handler(PtCode.LEAGUE_ADD_CUSTOM_JOB_AUTH_REQ)
    private void addCustomJobAuth(Player player, PbProtocol.LeagueModifyJobAuthReq req, long time){
        PbProtocol.LeagueModifyJobAuthRst.Builder rst = PbProtocol.LeagueModifyJobAuthRst.newBuilder().setResult(Text.genOkServerRstInfo());
        int jobIndex = req.getIndex();
        List<Integer> jobAuths = req.getJobAuthsList();
        String name = req.getName();
        boolean isAdd = req.getIsAdd();
        int jobId = req.getJobId();
        logic:{
            League league = LeagueManager.getLeagueByPlayer(player);
            if (Objects.isNull(league)) {
                rst.setResult(Text.genServerRstInfo(Text.帮派不存在));
                break logic;
            }
            if(!league.hasAuth(player.getPlayerId(), LeagueJobTypeEnums.职位任免)){
                rst.setResult(Text.genServerRstInfo(Text.帮派权限不足));
                break logic;
            }
            Map<Integer, LeagueCustomJobInfo> customJobAuths = league.getCustomJobAuths();
            if(jobId > 0){
                if(Objects.isNull(LeagueJobEnums.of(jobId))){
                    rst.setResult(Text.genServerRstInfo(Text.帮派职位错误));
                    break logic;
                }
                //修改职位权限
                LeagueJobTemplate leagueJobTemplate = leagueJobTemplateMap.get(jobId);
                for (Integer jobAuth : jobAuths) {
                    if(Objects.isNull(LeagueJobTypeEnums.of(jobAuth))){
                        rst.setResult(Text.genServerRstInfo(Text.帮派自定义职位错误));
                        break logic;
                    }
                    //检查职位能否被用
                    if(!leagueJobTemplate.limitslist.contains(jobAuth)){
                        rst.setResult(Text.genServerRstInfo(Text.帮派职位不可修改));
                        break logic;
                    }
                }
                //检查职位信息是否可以被修改
                for (Integer jobAuthId : leagueJobTemplate.limitslist) {
                    if(!jobAuths.contains(jobAuthId)
                        && !limitsTypes.getOrDefault(jobAuthId, false)){
                        rst.setResult(Text.genServerRstInfo(Text.帮派职位不可修改));
                        break logic;
                    }
                }
                LeagueJobInfo jobInfo = league.getJobInfoMap().get(jobId);
                jobInfo.setJobAuths(new ArrayList<>(jobAuths));
            }else{
                //修改自定义职位
                if(isAdd){
                    //检查职位是否满了
                    LeagueJobTemplate xinzengJobTemplate = leagueJobTemplateMap.get(LeagueJobEnums.xinzeng.getID());
                    if(customJobAuths.size() >= xinzengJobTemplate.PositionNum){
                        rst.setResult(Text.genServerRstInfo(Text.帮派自定义职位已满));
                        break logic;
                    }
                    //检查名字是否合法
                    if(KeyWordService.contains(name)){
                        rst.setResult(Text.genServerRstInfo(Text.帮派自定义职位名字包含敏感字符));
                        break logic;
                    }
                    LeagueCustomJobInfo jobInfo = new LeagueCustomJobInfo(jobIndex, name, jobAuths);
                    customJobAuths.put(jobInfo.getIndex(), jobInfo);
                }else{
                    //删除
                    customJobAuths.remove(jobIndex);
                    Set<Long> memberByJob = league.getMemberByJob(LeagueJobEnums.xinzeng);
                    //职位有人需要删除
                    for (Long memberId : memberByJob) {
                        LeagueMember member = league.getMember(memberId);
                        if(Objects.nonNull(member) && member.getJobIndex() == jobIndex){
                            member.setJobId(LeagueJobEnums.bangzhong.getID());
                        }
                    }
                }
            }
            rst.setIsAdd(isAdd);
            rst.setIndex(jobIndex);
            rst.setName(name);
            rst.addAllJobAuths(jobAuths);
            rst.setJobId(jobId);
        }
        player.send(PtCode.LEAGUE_ADD_CUSTOM_JOB_AUTH_RST, rst.build(), time);
    }

    @Handler(PtCode.LEAGUE_GET_PLAY_INFO_REQ)
    private void getPlayInfo(Player player, PbProtocol.LeagueGetPlayInfoReq req, long time){
        PbProtocol.LeagueGetPlayInfoRst.Builder rst = PbProtocol.LeagueGetPlayInfoRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:
        {
            if(Function.league.isNotOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            if (Objects.isNull(league)) {
                rst.setResult(Text.genServerRstInfo(Text.帮派不存在));
                break logic;
            }
            rst.addAllPlayInfos(LeaguePlayTypeEnums.buildAllPlayInfo(player,league));
        }
        player.send(PtCode.LEAGUE_GET_PLAY_INFO_RST, rst.build(), time);
    }

    /**
     * 区分两个帮派合并时候的主从关系
     * 无法区分主从时，返回null
     */
    public static Pair<League, League> choseMaster(League l1, League l2){
        if(l1 == l2){
            return null;
        }

        if(l1.getLevel() == l2.getLevel()){
            if(l1.getMemberCount() == l2.getMemberCount()){
                return null;
            }
            if(l1.getMemberCount() > l2.getMemberCount()){
                return Pair.of(l1, l2);
            }else{
                return Pair.of(l2, l1);
            }
        }

        if(l1.getLevel() > l2.getLevel()){
            return Pair.of(l1, l2);
        }else{
            return Pair.of(l2, l1);
        }
    }

    @Handler(PtCode.LEAGUE_MERGE_LIST_REQ)
    private void mergeList(Player player, PbProtocol.LeagueMergeListReq req, long time) {
        PbProtocol.LeagueMergeListRst.Builder rst = PbProtocol.LeagueMergeListRst.newBuilder().setResult(Text.genOkServerRstInfo());
        int page = req.getPage();
        logic:
        {
            if (page < 1 || page > 999) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }

            if (Function.league.isNotOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            if (LeagueManager.isNotJoinLeague(player)) {
                rst.setResult(Text.genServerRstInfo(Text.未加入帮派));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            // 判断权限
            if (!league.hasAuth(player.getPlayerId(), LeagueJobTypeEnums.帮派合并)) {
                rst.setResult(Text.genServerRstInfo(Text.帮派权限不足));
                break logic;
            }

            int pageSize = constant.leaguePageNum;
            List<League> leagues = LeagueManager.getLeagues();
            List<League> sortLeagues = leagues.stream()
                    .filter(l->{
                         //排除自己
                        if(l == league){
                            return false;
                        }

                        //确定主从
                        Pair<League, League> pair = choseMaster(league, l);
                        if(Objects.isNull(pair)){
                            return false;
                        }

                        League master = pair.getKey();
                        League slave = pair.getValue();

                        //满足条件才放入列表
                        return master.getMemberCount() + slave.getActiveMemberCount(constant.mergeActiveRuleHour) <= master.getMemberLimit();
                    })
                    .sorted((o1, o2) -> {
                        //系统帮派放最后 帮派等级>帮主战力>帮派周贡献>帮派编号
                        if(o1.isSystemLeague() == o2.isSystemLeague()) {
                            if(o1.isSystemLeague()){
                                return Long.compare(o1.getLeagueId(), o2.getLeagueId());
                            }
                            if (o1.getLevel() == o2.getLevel()) {
                                    if (o2.getWeekContribution() == o1.getWeekContribution()) {
                                        if(o1.getMemberCount() == o2.getMemberCount()){
                                            return (int) (o2.getLeagueId() - o1.getLeagueId());
                                        }else{
                                            return Integer.compare(o2.getMemberCount(), o2.getMemberCount());
                                        }
                                    }
                                    return (int) (o2.getWeekContribution() - o1.getWeekContribution());
                            }
                            return o2.getLevel() - o1.getLevel();
                        }else{
                            return Boolean.compare(o1.isSystemLeague(), o2.isSystemLeague());
                        }
                    })
                    .collect(Collectors.toList());

            int totalRecords = sortLeagues.size();
            int totalPages = totalRecords % pageSize == 0 ? totalRecords / pageSize
                    : totalRecords / pageSize + 1;

            rst.setTotalPage(totalPages);
            int skipNum = (page - 1) * pageSize;
            rst.setPage(page);
            sortLeagues.stream().skip(skipNum).limit(pageSize).forEach(l -> {
                rst.addMergeList(l.generateMergeLeague(league));
            });
        }

        player.send(PtCode.LEAGUE_MERGE_LIST_RST, rst.build(), time);
    }

    @Handler(PtCode.LEAGUE_MERGE_REQ)
    private void mergeRequest(Player player, PbProtocol.LeagueMergeReq req, long time) {
        PbProtocol.LeagueMergeRst.Builder rst = PbProtocol.LeagueMergeRst.newBuilder().setResult(Text.genOkServerRstInfo());
        long targetLeagueId = req.getLeagueId();
        int mergeType = req.getMergeType();
        logic:
        {
            if (Function.league.isNotOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            if (LeagueManager.isNotJoinLeague(player)) {
                rst.setResult(Text.genServerRstInfo(Text.未加入帮派));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            // 判断权限
            if (!league.hasAuth(player.getPlayerId(), LeagueJobTypeEnums.帮派合并)) {
                rst.setResult(Text.genServerRstInfo(Text.帮派权限不足));
                break logic;
            }

            League targetLeague = LeagueManager.getLeagueById(targetLeagueId);
            if (Objects.isNull(targetLeague)) {
                rst.setResult(Text.genServerRstInfo(Text.帮派不存在));
                break logic;
            }

            if(targetLeague == league){
                rst.setResult(Text.genServerRstInfo(Text.数据异常));
                break logic;
            }

            Pair<League, League> pair = choseMaster(league, targetLeague);
            if(Objects.isNull(pair)){
                rst.setResult(Text.genServerRstInfo(Text.帮派不符合合并条件));
                break logic;
            }

            if(pair.getLeft() == league && mergeType != 2){
                rst.setResult(Text.genServerRstInfo(Text.合并方式不正确));
                break logic;
            }

            if(pair.getLeft().getMemberCount() + pair.getRight().getActiveMemberCount(constant.mergeActiveRuleHour) > pair.getLeft().getMemberLimit()){
                rst.setResult(Text.genServerRstInfo(Text.帮派不符合合并条件));
                break logic;
            }

            league.getMergeApply().put(targetLeagueId, mergeType);
            targetLeague.getMergeApplied().put(league.getLeagueId(), mergeType);
        }

        player.send(PtCode.LEAGUE_MERGE_RST, rst.build(), time);
    }

    @Handler(PtCode.LEAGUE_MERGE_APPLY_LIST_REQ)
    private void applyList(Player player, PbProtocol.LeagueMergeApplyListReq req, long time) {
        PbProtocol.LeagueMergeApplyListRst.Builder rst = PbProtocol.LeagueMergeApplyListRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:
        {
            if (Function.league.isNotOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            if (LeagueManager.isNotJoinLeague(player)) {
                rst.setResult(Text.genServerRstInfo(Text.未加入帮派));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            // 判断权限
            if (!league.hasAuth(player.getPlayerId(), LeagueJobTypeEnums.帮派合并)) {
                rst.setResult(Text.genServerRstInfo(Text.帮派权限不足));
                break logic;
            }

            for(Map.Entry<Long, Integer> entry : league.getMergeApplied().entrySet()){
                int applyType = entry.getValue();
                long applyLeagueId = entry.getKey();
                League applyLeague = LeagueManager.getLeagueById(applyLeagueId);
                if(applyLeague != null){
                    PbLeague.MergeApplyLeague.Builder bd = PbLeague.MergeApplyLeague.newBuilder();
                    bd.setId(applyLeague.getLeagueId())
                            .setName(applyLeague.getName())
                            .setIconName(applyLeague.getIconName())
                            .setIconFrameId(applyLeague.getIconFrameId())
                            .setMergeType(applyType);
                    rst.addMergeList(bd);
                }
            }

        }

        player.send(PtCode.LEAGUE_MERGE_APPLY_LIST_RST, rst.build(), time);
    }

    @Handler(PtCode.LEAGUE_MERGE_APPLY_DETAIL_REQ)
    private void applyDetail(Player player, PbProtocol.LeagueMergeApplyDetailReq req, long time) {
        PbProtocol.LeagueMergeApplyDetailRst.Builder rst = PbProtocol.LeagueMergeApplyDetailRst.newBuilder().setResult(Text.genOkServerRstInfo());
        long targetLeagueId = req.getLeagueId();
        logic:
        {
            if (Function.league.isNotOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            if (LeagueManager.isNotJoinLeague(player)) {
                rst.setResult(Text.genServerRstInfo(Text.未加入帮派));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            // 判断权限
            if (!league.hasAuth(player.getPlayerId(), LeagueJobTypeEnums.帮派合并)) {
                rst.setResult(Text.genServerRstInfo(Text.帮派权限不足));
                break logic;
            }

            if(!league.getMergeApplied().containsKey(targetLeagueId)){
                rst.setResult(Text.genServerRstInfo(Text.目标帮派不在申请列表中));
                break logic;
            }

            League applyLeague = LeagueManager.getLeagueById(targetLeagueId);
            if (applyLeague == null) {
                rst.setResult(Text.genServerRstInfo(Text.帮派不存在));
                break logic;
            }

            PbLeague.MergeApplyDetail.Builder bd = PbLeague.MergeApplyDetail.newBuilder();
            bd.setLeagueId(applyLeague.getLeagueId())
                    .setLeader(applyLeague.getLeaderMinMiniUser())
                    .setMergeType(league.getMergeApplied().get(targetLeagueId));

            RankItem rankItem = RankManager.getRankData(RankType.league).getRankItemByItemId(targetLeagueId);
            if(rankItem != null){
                bd.setLeagueRank(rankItem.rank);
            }else{
                bd.setLeagueRank(-1);
            }
            bd.setLevel(applyLeague.getLevel())
                    .setBuildValue(applyLeague.getBuildValue())
                    .setActiveMemberCount(applyLeague.getActiveMemberCount(constant.mergeActiveRuleHour))
                    .setMaxMemberCount(applyLeague.getMemberLimit())
                    .setLeagueName(applyLeague.getName());
            rst.setDetail(bd);
        }

        player.send(PtCode.LEAGUE_MERGE_APPLY_DETAIL_RST, rst.build(), time);
    }

    @Handler(PtCode.LEAGUE_MERGE_AGREE_REQ)
    private void mergeAgree(Player player, PbProtocol.LeagueMergeAgreeReq req, long time) {
        PbProtocol.LeagueMergeAgreeRst.Builder rst = PbProtocol.LeagueMergeAgreeRst.newBuilder().setResult(Text.genOkServerRstInfo());
        long targetLeagueId = req.getLeagueId();
        boolean deny = req.getDeny();
        logic:
        {
            if (Function.league.isNotOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            if (LeagueManager.isNotJoinLeague(player)) {
                rst.setResult(Text.genServerRstInfo(Text.未加入帮派));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            // 判断权限
            if (!league.hasAuth(player.getPlayerId(), LeagueJobTypeEnums.帮派合并)) {
                rst.setResult(Text.genServerRstInfo(Text.帮派权限不足));
                break logic;
            }

            if(!league.getMergeApplied().containsKey(targetLeagueId)){
                rst.setResult(Text.genServerRstInfo(Text.目标帮派不在申请列表中));
                break logic;
            }

            League applyLeague = LeagueManager.getLeagueById(targetLeagueId);
            if (applyLeague == null) {
                rst.setResult(Text.genServerRstInfo(Text.帮派不存在));
                break logic;
            }

            if(deny){
                league.getMergeApplied().remove(targetLeagueId);
                applyLeague.getMergeApply().remove(league.getLeagueId());
                rst.setResult(Text.genOkServerRstInfo());
                break logic;
            }

            int mergeType = league.getMergeApplied().get(targetLeagueId);


            Pair<League, League> pair = choseMaster(league, applyLeague);
            if(Objects.isNull(pair)){
                rst.setResult(Text.genServerRstInfo(Text.帮派不符合合并条件));
                break logic;
            }

            League master = pair.getKey();
            League slave = pair.getValue();

            if(master == league && mergeType != 1){
                rst.setResult(Text.genServerRstInfo(Text.合并方式不正确));
                break logic;
            }

            if(slave == league && mergeType != 2){
                rst.setResult(Text.genServerRstInfo(Text.合并方式不正确));
                break logic;
            }

            if(master.getMemberCount() + slave.getActiveMemberCount(constant.mergeActiveRuleHour) > master.getMemberLimit()){
                rst.setResult(Text.genServerRstInfo(Text.帮派不符合合并条件));
                break logic;
            }

            //检查是否有功能阻止合并
            if(!canMerge(master) || !canMerge(slave)){
                rst.setResult(Text.genServerRstInfo(Text.帮派活动进行中无法合并));
                break logic;
            }

            //正式合并
            ThreadPool.execute(new LeagueMergeAsyncCall(player, master, slave, time, rst));
            return;

        }

        player.send(PtCode.LEAGUE_MERGE_AGREE_RST, rst.build(), time);
    }

    private boolean canMerge(League league){

        //league model
        for(LeagueModel model : league.getLeagueModels().values()){
            if(model instanceof LeagueDataInterface){
                if(!((LeagueDataInterface) model).canMerge(league.getLeagueId())){
                    return false;
                }
            }else{
                SystemLogger.warn("league model is not LeagueDataInterface : " + model.getClass().getName());
            }
        }

        //global data
        for(GlobalData data : GlobalDataManager.getDataMap(League.getRealServerId(league.getLeagueId())).values()){
            if(data instanceof LeagueDataInterface){
                if(!((LeagueDataInterface) data).canMerge(league.getLeagueId())){
                    return false;
                }
            }
        }

        return true;
    }

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        Map<String, String> constantMap = ConstantConfigReader.read(ConfigFile.league_constant);
        constant = new LeagueConstant(constantMap);
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.league_leagueLevel);
        Map<Integer, LeagueLevelTemplate> leagueLevelTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            LeagueLevelTemplate leagueLevelTemplate = new LeagueLevelTemplate(map);
            leagueLevelTemplateMapTemp.put(leagueLevelTemplate.level, leagueLevelTemplate);
        }
        leagueLevelTemplateMap = leagueLevelTemplateMapTemp;
        leagueMaxLevel = Collections.max(leagueLevelTemplateMap.keySet());

        mapList = ConfigReader.read(ConfigFile.league_leagueFrame);
        Map<Integer, LeagueFrameTemplate> leagueFrameTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            LeagueFrameTemplate leagueFrameTemplate = new LeagueFrameTemplate(map);
            leagueFrameTemplateMapTemp.put(leagueFrameTemplate.id, leagueFrameTemplate);
        }
        leagueFrameTemplateMap = leagueFrameTemplateMapTemp;

        mapList = ConfigReader.read(ConfigFile.league_facilityBuild);
        Map<Integer, LeagueBuildTemplate> leagueBuildTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            LeagueBuildTemplate leagueBuildTemplate = new LeagueBuildTemplate(map);
            leagueBuildTemplateMapTemp.put(leagueBuildTemplate.order, leagueBuildTemplate);
        }
        leagueBuildTemplateMap = leagueBuildTemplateMapTemp;
        leagueBuildMaxNum = Collections.max(leagueBuildTemplateMap.keySet());

        mapList = ConfigReader.read(ConfigFile.league_leagueStatus);
        Map<Integer, LeagueJobTemplate> leagueJobTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            LeagueJobTemplate leagueJobTemplate = new LeagueJobTemplate(map);
            leagueJobTemplateMapTemp.put(leagueJobTemplate.job, leagueJobTemplate);
        }
        leagueJobTemplateMap = leagueJobTemplateMapTemp;

        mapList = ConfigReader.read(ConfigFile.league_leagueLog);
        for (Map<String, String> map : mapList) {
            String type = map.get("type");
            try {
                LeagueLogType leagueLogType = LeagueLogType.valueOf(type);
                leagueLogType.init(map);
            } catch (IllegalArgumentException e) {
                String errorMsg = String.format("leagueLogType is not found!type:%s", type);
                if (Configuration.runMode.isTest()) {
                    System.err.println(errorMsg);
                } else {
                    throw new RuntimeException(errorMsg);
                }
            }
        }

        mapList = ConfigReader.read(ConfigFile.league_leagueLogList);
        for (Map<String, String> map : mapList) {
            String type = map.get("type");
            try {
                LeagueLogListType leagueLogListType = LeagueLogListType.valueOf(type);
                leagueLogListType.init(map);
            } catch (IllegalArgumentException e) {
                String errorMsg = String.format("leagueLogListType is not found!type:%s", type);
                if (Configuration.runMode.isTest()) {
                    System.err.println(errorMsg);
                } else {
                    throw new RuntimeException(errorMsg);
                }
            }
        }
//        mapList = ConfigReader.read(ConfigFile.league_leagueEffect);
//        for (Map<String, String> map : mapList) {
//            String type = map.get("type");
//            try {
//                LeagueTGTypeEnums leagueTGTypeEnums = LeagueTGTypeEnums.valueOf(type);
//                leagueTGTypeEnums.setId(Integer.parseInt(map.get("id")));
//            }catch (Exception e){
//                throw new IllegalArgumentException("leagueEffect type is null, type : " + type);
//            }
//        }
        LeagueTGTypeEnums.init();

        mapList = ConfigReader.read(ConfigFile.league_leagueTechnology);
        Map<Integer, LeagueTGTemplate> leagueTGTemplates = new HashMap<>();
        Map<String, Integer> leagueType2Ids = new HashMap<>();
        for (Map<String, String> map : mapList) {
            LeagueTGTemplate template = new LeagueTGTemplate();
            template.id = Integer.parseInt(map.get("id"));
            template.type = Integer.parseInt(map.get("type"));
            template.unlockLevel = Integer.parseInt(map.get("class"));
            String[] effects = map.get("effectser").split("\\|");
            template.addedType = LeagueTGTypeEnums.getById(Integer.parseInt(effects[0]));
            template.level = Integer.parseInt(map.get("level"));
            template.addedNum = effects[1];
            template.leagueLevel = Integer.parseInt(map.get("grade"));
            template.costs = Long.parseLong(map.get("cost"));
            leagueTGTemplates.put(template.id, template);
            leagueType2Ids.put(genTgTempKey(template.type, template.level), template.id );
        }
        leagueType2Id = leagueType2Ids;
        leagueTGTemplateMap = leagueTGTemplates;

        mapList = ConfigReader.read(ConfigFile.league_limitsType);
        Map<Integer, Boolean> limitsTypeMap = new HashMap<>();
        for (Map<String, String> map : mapList) {
            limitsTypeMap.put(Integer.parseInt(map.get("id")), Integer.parseInt(map.get("Change")) == 1);
        }
        limitsTypes = limitsTypeMap;
        mapList = ConfigReader.read(ConfigFile.LeagueEntrance_LeagueEntrance);
        Map<Integer, LeagueEntranceTemplate> leagueEntranceTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            LeagueEntranceTemplate template = new LeagueEntranceTemplate();
            template.id = Integer.parseInt(map.get("Id"));
            template.functionId = Integer.parseInt(map.get("FunctionId"));
            template.type = Integer.parseInt(map.get("type"));
            template.group = Integer.parseInt(map.get("group"));
            template.order = Integer.parseInt(map.get("order"));
            leagueEntranceTemplateMapTemp.put(template.functionId, template);
        }
        leagueEntranceTemplateMap = leagueEntranceTemplateMapTemp;
    }

    public static String genTgTempKey(int type, int level){
        return type + "_" + level;
    }

    @Override
    public void clearConfigData() {
        leagueLevelTemplateMap.clear();
        leagueFrameTemplateMap.clear();
        leagueBuildTemplateMap.clear();
        leagueJobTemplateMap.clear();
        leagueType2Id.clear();
        leagueTGTemplateMap.clear();
        limitsTypes.clear();
    }

    public static LeagueConstant getConstant() {
        return constant;
    }

    public static Map<Integer, LeagueLevelTemplate> getLeagueLevelTemplateMap() {
        return leagueLevelTemplateMap;
    }

    public static Map<Integer, LeagueFrameTemplate> getLeagueFrameTemplateMap() {
        return leagueFrameTemplateMap;
    }

    public static Map<Integer, LeagueBuildTemplate> getLeagueBuildTemplateMap() {
        return leagueBuildTemplateMap;
    }

    public static Map<Integer, LeagueJobTemplate> getLeagueJobTemplateMap() {
        return leagueJobTemplateMap;
    }

    public static int getLeagueMaxLevel() {
        return leagueMaxLevel;
    }

    public static int getLeagueBuildMaxNum() {
        return leagueBuildMaxNum;
    }

    public static Map<Integer, LeagueTGTemplate> getLeagueTGTemplateMap() {
        return leagueTGTemplateMap;
    }

    public static Map<String, Integer> getLeagueType2Id() {
        return leagueType2Id;
    }

    /**
     * 能否玩帮派玩法
     * @param function 功能
     * @return 是否能玩
     */
    public static boolean canPlayLeagueActivity(Function function){
        int functionId = function.getId();
        //无限制
        if(!leagueEntranceTemplateMap.containsKey(functionId)){
            return true;
        }
        LeagueEntranceTemplate curTemplate = leagueEntranceTemplateMap.get(functionId);
        switch (curTemplate.type){
            case 1:{
                //常驻
                return true;
            }
            case 2:
            case 3:{
                //组内互斥(仅限gs)
                if(curTemplate.type == 2 && !Configuration.serverType.isGame()){
                    return false;
                }
                long openTime;
                if(curTemplate.type == 2){
                    //本服开启时间
                    WorldBaseGlobalData globalData = GlobalDataManager.getData(GlobalDataType.worldBase);
                    openTime = globalData.getOpeningTime().getTime();
                }else{
                    //首个gs开服时间为准
                    WarZoneGlobalData globalData = GlobalDataManager.getData(GlobalDataType.warOrder);
                    openTime = globalData.getFirstServerOpenTime();
                }
                if(openTime <= 0){
                    //等待开启时间写入
                    return false;
                }

                long betweenWeek = CommonUtils.getBetweenWeek(openTime);
                int count = 0;
                for (LeagueEntranceTemplate template : leagueEntranceTemplateMap.values()) {
                    if(template.type == curTemplate.type && curTemplate.group == template.group){
                        count++;
                    }
                }
                return betweenWeek % count == curTemplate.order;
            }
            default:{
                return false;
            }
        }

    }



    @Override
    public ServerEventType[] getEventTypes() {
        return eventTypes;
    }

    @Override
    public void handle(ServerEvent event) {
        switch (event.getEventType()) {
            case day5clock: {
                for (League league : LeagueManager.getLeagues()) {
                    if (league != null) {
                        league.day5Refresh();
                    }
                }

                //自动合并检查
                if(World.getOpenServerDay() >= constant.autoMergeStartDay
                        && ServerConstants.getCurrentTimeLocalDateTime().getDayOfWeek() == DayOfWeek.SUNDAY){
                    mergeSuggest();
                }
                break;
            }
        }
    }

    public static void mergeSuggest() {
        List<League> allLeagues = LeagueManager.getLeagues();

        List<League> list = LeagueManager.getLeagues().stream().filter(l -> l.getMemberCount() < constant.autoMergeMemberCount).collect(Collectors.toList());
        for (League league : list) {
            Collections.shuffle(allLeagues);
            for (League target : allLeagues) {
                Pair<League, League> pair = choseMaster(league, target);
                if (!Objects.isNull(pair)) {
                    if (pair.getLeft().getMemberCount() + pair.getRight().getActiveMemberCount(constant.mergeActiveRuleHour) <= pair.getLeft().getMemberLimit()) {
                        LeagueBaseModel baseModel = league.getModel(LeagueModelEnums.base);
                        if (baseModel != null) {
                            baseModel.updateSuggestMergeLid(target.getLeagueId());
                            SystemLogger.info("league merge suggest : " + league.getLeagueId() + " -> " + target.getLeagueId());
                            break;
                        }
                    }
                }
            }
        }
    }
}

package com.gy.server.game.league.async;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.global.GlobalData;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.league.*;
import com.gy.server.game.league.bean.LeagueMember;
import com.gy.server.game.mail.MailManager;
import com.gy.server.game.mail.MailType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.text.Text;
import com.gy.server.game.text.TextParamText;
import com.gy.server.packet.PbCommons;
import com.gy.server.packet.PbProtocol;

import java.util.HashMap;
import java.util.Map;

/**
 * @program: tl_game_4_live
 * @description: 帮派合并
 * @author: <PERSON>.<PERSON>
 * @create: 2025/3/21
 **/
public class LeagueMergeAsyncCall extends AsyncCall {

    private League master;
    private League slave;
    private long time;
    private PbProtocol.LeagueMergeAgreeRst.Builder rst;

    private Map<Long, Player> members = new HashMap<>();

    private Player operator;


    public LeagueMergeAsyncCall(Player  operator, League master, League slave, long time, PbProtocol.LeagueMergeAgreeRst.Builder rst) {
        this.master = master;
        this.slave = slave;
        this.time = time;
        this.rst = rst;
        this.operator = operator;
    }

    @Override
    public void execute() {
        merge(master, slave);
        for(LeagueMember member : slave.getMemberMap().values()){
            Player player = members.get(member.getPlayerId());
            if(player != null) {
                if (member.isActive(LeagueService.getConstant().mergeActiveRuleHour)) {
                    //加入新帮派
                    LeagueManager.playerJoin(master, player, true);
                    if(player.isOnLine()){
                        player.dataSyncModule.syncLeague();
                    }
                } else {
                    //踢掉
                    slave.kick(operator, player);

                    //增加踢人邮件
                    MailType mailType = MailType.leagueQuit;
                    PbCommons.PbText titleText = Text.genText(mailType.getTitleId()).build();
                    PbCommons.PbText contentText = Text.genText(mailType.getContentId(), new TextParamText(slave.getName())).build();
                    MailManager.sendMail(mailType, member.getPlayerId(), titleText, contentText, ServerConstants.getCurrentTimeMillis());

                    player.saveOnce();
                    if(player.isOnLine()){
                        player.dataSyncModule.syncLeague();
                    }
                }
            }
        }

        operator.send(PtCode.LEAGUE_MERGE_AGREE_RST, rst.build(), time);
    }

    @Override
    public void asyncExecute() {
        for (LeagueMember member : slave.getMemberMap().values()) {
            Player player = PlayerManager.getPlayer(member.getPlayerId());
            if (player!= null) {
                members.put(player.getPlayerId(), player);
            }
        }
    }


    private void merge(League master, League slave){
        //league model
        for(LeagueModel model : slave.getLeagueModels().values()){
            if(model instanceof LeagueDataInterface){
                ((LeagueDataInterface) model).merge(slave, master);
            }
        }

        //global data
        for(GlobalData data : GlobalDataManager.getDataMap(League.getRealServerId(master.getLeagueId())).values()){
            if(data instanceof LeagueDataInterface){
                ((LeagueDataInterface) data).merge(slave, master);
            }
        }
    }
}

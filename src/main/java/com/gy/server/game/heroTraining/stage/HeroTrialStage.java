package com.gy.server.game.heroTraining.stage;

import com.gy.server.game.battleCollect.BattleCollectService;
import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.combat.StageType;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.combat.unit.TeamUnit;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.heroTraining.HeroTrialGlobalData;
import com.gy.server.game.heroTraining.HeroTrialType;
import com.gy.server.game.heroTraining.bean.HeroTrialRecord;
import com.gy.server.game.heroTraining.template.HeroTrialTemplate;
import com.gy.server.game.log.GameLogAssistant;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.rank.RankManager;
import com.gy.server.game.rank.RankType;
import com.gy.server.game.record.RecordManager;
import com.gy.server.game.record.RecordType;
import com.gy.server.game.record.combat.CombatRecord;
import com.gy.server.packet.PbCommons;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbRecord;
import com.gy.server.utils.CollectionUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * 英雄试炼
 *
 * <AUTHOR> - [Created on 2022/5/5 16:08]
 */
public class HeroTrialStage extends AbstractStage {

    private final Player player;
    private final HeroTrialTemplate heroTrialTemplate;
    /**
     * 试炼类型
     */
    private final int type;
    /**
     * 试炼主题
     */
    private final int subject;
    /**
     * 试炼难度
     */
    private final int diff;

    private HeroTrialType heroTrialType;

    public HeroTrialStage(Player player, HeroTrialTemplate heroTrialTemplate) {
        this.player = player;
        this.heroTrialTemplate = heroTrialTemplate;
        this.type = heroTrialTemplate.type;
        this.subject = heroTrialTemplate.subject;
        this.diff = heroTrialTemplate.difficulty;
        this.heroTrialType = HeroTrialType.getTypeById(heroTrialTemplate.type);
    }

    @Override
    public void init() {
        List<HeroUnit> attackers = player.getLineupModel().createHeroUnits(StageType.heroTrial, heroTrialType.lineupType);
        TeamUnit attackerTeam = new TeamUnit(getNewId(), attackers);
        List<HeroUnit> defUnits = BattleCollectService.createHeroUnits(heroTrialTemplate.battleCollect, heroTrialTemplate.level);
        TeamUnit defenderTeam = new TeamUnit(getNewId(), defUnits);
        if (heroTrialTemplate.buffId != -1) {
            defenderTeam.addUnitBuff(heroTrialTemplate.buffId, 1);
        }
        init(heroTrialTemplate.battleCollect, StageType.heroTrial, attackerTeam, defenderTeam, heroTrialType.lineupType);
    }

    @Override
    public void afterFinish() {
        long fightingPower = player.getFightingPower();
        PbProtocol.CombatSettlementNotify.Builder rst = genCombatSettlement();

        // 赢了，发奖和记录
        if (isWin()) {
            // 增加今日挑战次数
            player.getHeroTrialModel().addChallengeTimes(type);
            player.getHeroTrialModel().passDiff(type, subject, diff);
            //掉落奖励
            List<Reward> rewards = RewardTemplate.createRewards(heroTrialTemplate.rewardList);
            Reward.add(rewards, player, BehaviorType.HeroTrialFight);
            rst.addAllRewards(Reward.writeCollectionToPb(rewards));

            // 尝试进入历史榜
            HeroTrialGlobalData heroTrialGlobalData = GlobalDataManager.getData(GlobalDataType.HeroTrial);
            // 更新排行榜数据
            heroTrialGlobalData.updateRank(player, type);
            //战报记录
            combatRecord();
            // 更新排行榜
            HeroTrialType trialType = HeroTrialType.getTypeById(type);
            RankType rankType = trialType.rankType;
            if (heroTrialTemplate.difficulty != 0) {
                RankManager.postItem(rankType, player);
            }
            //同步自己的成绩
            player.dataSyncModule.syncHeroTrial(type, subject);

        }
        player.postEvent(PlayerEventType.heroTrial, type, isWin, subject, diff, heroTrialTemplate.id, player);
        rst.setHeroTrial(PbProtocol.HeroTrialSettlement.newBuilder().setId(heroTrialTemplate.id).build());
        HeroTrialType heroTrialType = HeroTrialType.getTypeById(type);
        //日志
        List<HeroUnit> atkList = CollectionUtil.isEmpty(getAtks()) ? new ArrayList<>() : new ArrayList<>(getAtks().get(0).getUnits());
        GameLogger.heroTrial(player, fightingPower, isWin(), heroTrialTemplate.id, heroTrialType.lineupType,
                getTimer().roundIndex, GameLogAssistant.getRemainHp(atkList));

        notifyCombatSettlement(player, rst.build());

    }

    /**
     * 战报记录
     */
    private void combatRecord() {
        RecordType recordType = heroTrialType.recordType;
        //添加战报
        CombatRecord combatRecord = CombatRecord.create(this, player.getMiniPlayer(), PbCommons
                .MiniUser.newBuilder().build(), false, CombatRecord.OverdueTime.HeroTrial, recordType.getId());
        // 保存战报战斗信息
        RecordManager.save(combatRecord);
        PbRecord.RecordPlayer atkRecordPlayer = RecordManager.genRecordPlayer(player, true, heroTrialType.lineupType, getHeroUnits(true)).build();
        HeroTrialRecord holder = new HeroTrialRecord(combatRecord.getCosKey(), recordType, isWin, combatRecord.getId(), atkRecordPlayer);
        String subKey = subject + "_" + diff;
        RecordManager.addRecord(holder, subKey);
    }
}

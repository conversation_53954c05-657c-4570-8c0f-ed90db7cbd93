package com.gy.server.game.heroTraining;

import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.combat.CombatManager;
import com.gy.server.game.cond.CondManager;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.function.Function;
import com.gy.server.game.function.FunctionService;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.heroTraining.bean.HeroTrialSubject;
import com.gy.server.game.heroTraining.stage.HeroTrialStage;
import com.gy.server.game.heroTraining.template.HeroTrialTemplate;
import com.gy.server.game.lineup.LineupService;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.rank.RankService;
import com.gy.server.game.rank.RankType;
import com.gy.server.game.record.RecordService;
import com.gy.server.game.record.RecordType;
import com.gy.server.game.service.Service;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbCommons;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.CollectionUtil;

import java.util.*;

/**
 * <AUTHOR> - [Created on 2022/5/5 15:53]
 */
public class HeroTrialService extends PlayerPacketHandler implements Service {

    /**
     * Map<试炼类型,Map<英雄主题,Map<难度,VO>
     */
    private static Map<Integer, Map<Integer, Map<Integer, HeroTrialTemplate>>> heroTrialTemplateMap = new HashMap<>();

    /**
     * Map<试炼类型,Map<英雄主题,最大难度
     */
    private static Map<Integer, Map<Integer, Integer>> heroTrialMaxDiffMap = new HashMap<>();

    /**
     * key:HeroTrialTemplate id    value:挑战类型
     */
    private static Map<Integer, Integer> idTypeMap = new HashMap<>();

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {

        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.heroTrial_heroTrial);
        Map<Integer, Map<Integer, Map<Integer, HeroTrialTemplate>>> heroTrialTemplateMapTemp = new HashMap<>();
        Map<Integer, Map<Integer, Integer>> heroTrialMaxDiffMapTemp = new HashMap<>();
        Map<Integer, Integer> idTypeMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            int id = Integer.parseInt(map.get("id"));
            int type = Integer.parseInt(map.get("type"));
            int subject = Integer.parseInt(map.get("subject"));
            int difficulty = Integer.parseInt(map.get("difficulty"));
            int battleCollect = Integer.parseInt(map.get("battleCollect"));
            int level = Integer.parseInt(map.get("level"));
            String unlock = map.get("unlock");
            int power = Integer.parseInt(map.get("power"));
            int buffId = Integer.parseInt(map.get("buffId"));
            List<RewardTemplate> rewards = RewardTemplate.readListFromText(map.get("rewards"));
            HeroTrialTemplate heroTrialTemplate = new HeroTrialTemplate();
            heroTrialTemplate.id = id;
            heroTrialTemplate.type = type;
            heroTrialTemplate.subject = subject;
            heroTrialTemplate.difficulty = difficulty;
            heroTrialTemplate.unlock = unlock;
            heroTrialTemplate.power = power;
            heroTrialTemplate.battleCollect = battleCollect;
			heroTrialTemplate.level = level;            heroTrialTemplate.rewardList = rewards;
            heroTrialTemplate.buffId = buffId;
            Map<Integer, Map<Integer, HeroTrialTemplate>> trialMiddleMap = heroTrialTemplateMapTemp.computeIfAbsent(type, map1 -> new HashMap<>());
            Map<Integer, HeroTrialTemplate> trialTemplateMap = trialMiddleMap.computeIfAbsent(subject, map1 -> new HashMap<>());
            trialTemplateMap.put(difficulty, heroTrialTemplate);
            Map<Integer, Integer> maxDiffMap = heroTrialMaxDiffMapTemp.computeIfAbsent(type, map1 -> new HashMap<>());
            maxDiffMap.compute(subject, (k, v) -> v == null ? difficulty : Math.max(difficulty, v));

            idTypeMapTemp.put(heroTrialTemplate.id, heroTrialTemplate.type);
        }
        heroTrialTemplateMap = heroTrialTemplateMapTemp;
        heroTrialMaxDiffMap = heroTrialMaxDiffMapTemp;
        idTypeMap = idTypeMapTemp;

        mapList = ConfigReader.read(ConfigFile.heroTrial_heroTrialType);
        for (Map<String, String> map : mapList) {
            int type = Integer.parseInt(map.get("type"));
            int soulType = Integer.parseInt(map.get("soulType"));
            String[] temp = map.get("weekday").split(",");
            int[] openTime = Arrays.stream(temp).mapToInt(Integer::parseInt).toArray();
            HashSet<Integer> openTimeSet = CollectionUtil.toCollection(openTime, HashSet::new);
            int dailyChallengeLimit = Integer.parseInt(map.get("dailyChallengeLimit"));
            int functionId = Integer.parseInt(map.get("functionId"));
            int lineUseId = Integer.parseInt(map.get("lineUseId"));
            String serverName = map.get("serverName");
            int rankId = Integer.parseInt(map.get("rankId"));
            int recordId = Integer.parseInt(map.get("recordId"));
            HeroTrialType heroTrialType = HeroTrialType.valueOf(serverName);
            heroTrialType.id = type;
            heroTrialType.soulType = soulType;
            heroTrialType.openDay = openTimeSet;
            heroTrialType.dailyChallengeLimit = dailyChallengeLimit;
            heroTrialType.function = Function.getFunctionById(functionId);
            heroTrialType.lineupType = LineupType.getType(lineUseId);
            heroTrialType.rankType = RankType.getRankTypeByRankId(rankId);
            heroTrialType.recordType = RecordType.of(recordId);
        }
        HeroTrialType.init();
    }

    @Override
    public void clearConfigData() {
        heroTrialTemplateMap.clear();
        heroTrialMaxDiffMap.clear();
    }

    @Override
    public Set<Class<? extends Service>> preServices() {
        Set<Class<? extends Service>> set = new HashSet<>();
        set.add(FunctionService.class);
        set.add(LineupService.class);
        set.add(RankService.class);
        set.add(RecordService.class);
        return set;
    }

    /**
     * 英雄试炼-开始战斗
     */
    @Handler(PtCode.HERO_TRIAL_FIGHT_START_CLIENT)
    private void fightStart(Player player, PbProtocol.HeroTrialFightStartReq req, long time) {
        PbProtocol.HeroTrialFightStartRst.Builder rst = PbProtocol.HeroTrialFightStartRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        int type = req.getType();
        int subject = req.getSubject();
        AbstractStage stage = null;
        logic:
        {
            rst.setResult(checkIsOpen(player, type, subject));
            if (!rst.getResult().getResult()) {
                break logic;
            }
            PlayerHeroTrialModel heroTrialModel = player.getHeroTrialModel();
            if (heroTrialModel.isRealPass(type, subject)) {
                rst.setResult(Text.genServerRstInfo(Text.关卡已通关));
                break logic;
            }
            HeroTrialType heroTrialType = HeroTrialType.getTypeById(type);
            // 判断今日挑战次数
            int challengeTimes = heroTrialModel.getChallengeTimes(type);
            if (heroTrialType.dailyChallengeLimit <= challengeTimes) {
                rst.setResult(Text.genServerRstInfo(Text.英雄试炼今日挑战次数已满));
                break logic;
            }
            Map<Integer, HeroTrialSubject> subjectMap = heroTrialModel.getSubjectMap(type);
            int currentDiff;
            if (subjectMap == null || !subjectMap.containsKey(subject)) {
                currentDiff = 1;
            } else {
                HeroTrialSubject heroTrialSubject = subjectMap.get(subject);
                currentDiff = heroTrialSubject.getCurrentDiff();
            }
            HeroTrialTemplate heroTrialTemplate = heroTrialTemplateMap.get(type).get(subject).get(currentDiff);
            if (CondManager.checkNotCond(player, heroTrialTemplate.unlock)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            stage = new HeroTrialStage(player, heroTrialTemplate);
            stage.init();
            rst.setStage(stage.getStageRecord());
        }
        player.send(PtCode.HERO_TRIAL_FIGHT_START_SERVER, rst.build(), time);
        if (Objects.nonNull(stage)) {
            CombatManager.combatPrepare(stage);
        }

    }


    /**
     * 检查英雄试炼是否开启
     *
     * @param player 玩家
     * @param type   试炼类型
     */
    public PbCommons.ServerRstInfo.Builder checkIsOpen(Player player, int type, int subject) {
        HeroTrialType heroTrialType = HeroTrialType.getTypeById(type);
        if (heroTrialType == null) {
            return Text.genServerRstInfo(Text.参数异常);
        }

        if (heroTrialType.function.isNotOpen(player)) {
            return Text.genServerRstInfo(Text.功能未开启);
        }
        Map<Integer, Map<Integer, HeroTrialTemplate>> subjectMap = heroTrialTemplateMap.get(type);
        if (!subjectMap.containsKey(subject)) {
            return Text.genServerRstInfo(Text.参数异常);
        }
        if (!player.getHeroTrialModel().isOpenDay(type)) {
            return Text.genServerRstInfo(Text.今日不能挑战请在开启时间内进行挑战);
        }

        return Text.genOkServerRstInfo();
    }


    /**
     * 获取主题下最高关卡(难度)
     *
     * @param type    试炼类型
     * @param subject 主题
     * @return 最高关卡
     */
    public static int getMaxDiff(int type, int subject) {
        Map<Integer, Integer> maxMap = heroTrialMaxDiffMap.get(type);
        if (maxMap == null) {
            return 0;
        }
        return maxMap.getOrDefault(subject, 0);

    }

    /**
     * 获取英雄试炼模板
     *
     * @param type    试炼类型
     * @param subject 试炼主题
     * @param diff    难度
     * @return 试炼模板
     */
    public static HeroTrialTemplate getHeroTrialTemplate(int type, int subject, int diff) {
        Map<Integer, Map<Integer, HeroTrialTemplate>> mapMap = heroTrialTemplateMap.get(type);
        if (mapMap == null || !mapMap.containsKey(subject)) {
            return null;
        }
        Map<Integer, HeroTrialTemplate> diffMap = mapMap.get(subject);
        return diffMap.get(diff);
    }

    public static Map<Integer, Map<Integer, Map<Integer, HeroTrialTemplate>>> getHeroTrialTemplateMap() {
        return heroTrialTemplateMap;
    }

    public static Map<Integer, Integer> getIdTypeMap() {
        return idTypeMap;
    }
}

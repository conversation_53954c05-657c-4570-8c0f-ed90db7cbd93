package com.gy.server.game.heroTraining.bean;

import com.google.common.collect.Sets;
import com.gy.server.game.record.Record;
import com.gy.server.game.record.RecordHelper;
import com.gy.server.game.record.RecordType;
import com.gy.server.packet.PbRecord;
import com.ttlike.server.tl.baselib.serialize.record.HeroTrialRecordDb;
import com.ttlike.server.tl.baselib.serialize.record.RecordDb;
import org.jetbrains.annotations.NotNull;

import java.util.Set;

/**
 * 侠客试炼战报
 *
 * <AUTHOR> - [Created on 2022/5/9 14:04]
 */
public class HeroTrialRecord extends Record {


    private PbRecord.RecordPlayer atkUser;//攻方信息


    public HeroTrialRecord(String cosKey, RecordType recordType, boolean win, long combatRecordId,
                           PbRecord.RecordPlayer atkUser) {
        super(recordType, win, combatRecordId, cosKey);
        this.atkUser = atkUser;
    }

    public HeroTrialRecord() {

    }

//    public HeroTrialRecord(HeroTrialRecordDb beanRecordDb) {
//        super(beanRecordDb.getType(), beanRecordDb.getTime(), beanRecordDb.isWin(), beanRecordDb.getCombatRecordId(), beanRecordDb.getServerId());
//        this.atkUser = RecordHelper.getInstance().genRecordPlayerPb(beanRecordDb.getAtkUser());
//    }

    public static HeroTrialRecord newRecord() {
        return new HeroTrialRecord();
    }

    @Override
    public int compareTo(@NotNull Record o) {
        HeroTrialRecord o1 = (HeroTrialRecord) o;
        if (this.atkUser.getFightingPower() == o1.atkUser.getFightingPower()) {
            return Long.compare(this.atkUser.getId(), o1.atkUser.getId());
        }
        return Long.compare(this.atkUser.getFightingPower(), o1.atkUser.getFightingPower());
    }


    @Override
    protected void subReadFromPb(PbRecord.Record record) {
        this.atkUser = record.getHeroTrialRecord().getAtkPlayer();
    }

    @Override
    protected void subWriteToPb(PbRecord.Record.Builder builder) {
        builder.setHeroTrialRecord(PbRecord.HeroTrialRecord.newBuilder()
                .setAtkPlayer(atkUser));
    }

    @Override
    protected Set<PbRecord.RecordPlayer> getDetailRecordSet() {
        return Sets.newHashSet(atkUser);
    }

    public HeroTrialRecordDb genHeroTrialRecordDb() {
        return new HeroTrialRecordDb(RecordHelper.getInstance().genRecordPlayerDb(atkUser));
    }

    @Override
    public void readFromDb2(RecordDb db) {
        HeroTrialRecordDb bean = db.getHeroTrialRecordDb();
        if(bean != null){
            this.atkUser = RecordHelper.getInstance().genRecordPlayerPb(bean.getAtkUser());
        }
    }

    @Override
    public void writeToDb(RecordDb db) {
        db.setHeroTrialRecordDb(genHeroTrialRecordDb());
    }
}

package com.gy.server.game.heroTraining;

import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.constant.ConstantService;
import com.gy.server.game.constant.ConstantType;
import com.gy.server.game.heroTraining.bean.HeroTrialSubject;
import com.gy.server.game.heroTraining.template.HeroTrialTemplate;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModel;
import com.gy.server.game.player.event.PlayerEvent;
import com.gy.server.game.player.event.PlayerEventHandler;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.packet.PbCommons;
import com.gy.server.packet.PbHeroTrial;
import com.gy.server.packet.PbSync;
import com.ttlike.server.tl.baselib.serialize.player.HeroTrialModelDb;
import com.ttlike.server.tl.baselib.serialize.player.PlayerBlobDb;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 英雄试炼
 * <AUTHOR> - [Created on 2022/5/5 16:34]
 */
public class PlayerHeroTrialModel extends PlayerModel implements PlayerEventHandler {
    /**
     * 通关关卡信息
     * Key 试炼类型  Value:Key:主题ID,Value:VO
     */
    private final Map<Integer, Map<Integer, HeroTrialSubject>> trialSubjectMap = new HashMap<>();
    /**
     * 每日挑战次数
     * Key 试炼类型  Value:每日挑战次数
     */
    private final Map<Integer, Integer> challengeTimes = new HashMap<>();


    public Map<Integer, HeroTrialSubject> getSubjectMap(int type) {
        return trialSubjectMap.get(type);
    }

    public PlayerHeroTrialModel(Player player) {
        super(player);
    }

    @Override
    protected void loadData(PlayerBlobDb playerBlob) {
        HeroTrialModelDb heroTrialModelDb = playerBlob.getHeroTrialModelDb();
        if (heroTrialModelDb != null) {
            Map<Integer, HeroTrialModelDb.HeroTrialTypeDb> trialSubjectDbMap = heroTrialModelDb.getTrialSubjectMap();
            for (Map.Entry<Integer, HeroTrialModelDb.HeroTrialTypeDb> mapEntry : trialSubjectDbMap.entrySet()) {
                Integer type = mapEntry.getKey();
                Map<Integer, HeroTrialSubject> trialSubjectMap = this.trialSubjectMap.computeIfAbsent(type, map1 -> new HashMap<>());
                Map<Integer, HeroTrialModelDb.HeroTrialSubjectDb> dbEntryMap = mapEntry.getValue().getTrialSubjectMap();
                for (Map.Entry<Integer, HeroTrialModelDb.HeroTrialSubjectDb> dbEntry : dbEntryMap.entrySet()) {
                    Integer subject = dbEntry.getKey();
                    HeroTrialModelDb.HeroTrialSubjectDb trialSubjectDb = dbEntry.getValue();

                    HeroTrialSubject heroTrialSubject = new HeroTrialSubject();
                    heroTrialSubject.setCurrentDiff(trialSubjectDb.getCurrentDiff());
                    heroTrialSubject.setAllPass(trialSubjectDb.isAllPass());
                    trialSubjectMap.put(subject, heroTrialSubject);
                }
            }
            this.challengeTimes.putAll(heroTrialModelDb.getChallengeTimes());
        }
    }

    @Override
    protected void saveData(PlayerBlobDb playerBlob) {
        HeroTrialModelDb heroTrialModelDb = new HeroTrialModelDb();
        Map<Integer, HeroTrialModelDb.HeroTrialTypeDb> trialSubjectDbMap = heroTrialModelDb.getTrialSubjectMap();
        for (Map.Entry<Integer, Map<Integer, HeroTrialSubject>> mapEntry : this.trialSubjectMap.entrySet()) {
            Integer type = mapEntry.getKey();
            HeroTrialModelDb.HeroTrialTypeDb heroTrialTypeDb = trialSubjectDbMap.computeIfAbsent(type, map1 -> new HeroTrialModelDb.HeroTrialTypeDb());
            Map<Integer, HeroTrialModelDb.HeroTrialSubjectDb> subjectDbMap = heroTrialTypeDb.getTrialSubjectMap();
            for (Map.Entry<Integer, HeroTrialSubject> subjectEntry : mapEntry.getValue().entrySet()) {
                Integer subject = subjectEntry.getKey();
                HeroTrialSubject heroTrialSubject = subjectEntry.getValue();
                HeroTrialModelDb.HeroTrialSubjectDb heroTrialSubjectDb = new HeroTrialModelDb.HeroTrialSubjectDb();
                heroTrialSubjectDb.setCurrentDiff(heroTrialSubject.getCurrentDiff());
                heroTrialSubjectDb.setAllPass(heroTrialSubject.isAllPass());
                subjectDbMap.put(subject, heroTrialSubjectDb);
            }
        }
        heroTrialModelDb.getChallengeTimes().putAll(this.challengeTimes);
        playerBlob.setHeroTrialModelDb(heroTrialModelDb);
    }

    /**
     * GM 通关到指定难度
     *
     * @param diff -1 代表通关该主题
     */
    public void gmPassHeroTrial(int type, int subject, int diff) {
        Map<Integer, HeroTrialSubject> trialSubjectMap = this.trialSubjectMap.computeIfAbsent(type, map1 -> new HashMap<>());
        int maxDiff = HeroTrialService.getMaxDiff(type, subject);
        if (diff == -1) {
            HeroTrialSubject heroTrialSubject = new HeroTrialSubject(maxDiff, true);
            trialSubjectMap.put(subject, heroTrialSubject);
        } else {
            int curDiff = Math.min(maxDiff, diff);
            boolean isAllPass = maxDiff == curDiff;
            HeroTrialSubject heroTrialSubject = new HeroTrialSubject(curDiff, isAllPass);
            trialSubjectMap.put(subject, heroTrialSubject);
        }
        getPlayer().dataSyncModule.syncHeroTrial(type, subject);
        HeroTrialTemplate heroTrialTemplate = HeroTrialService.getHeroTrialTemplate(type, subject, diff == -1 ? maxDiff : diff);
        getPlayer().postEvent(PlayerEventType.heroTrial, type, true, subject, diff, heroTrialTemplate.id, getPlayer());
    }

    /**
     * 主题是否真的通关  这个判断是兼容以后扩展难度
     *
     * @param type    试炼类型
     * @param subject 英雄主题
     * @return true 通关  false 未通关
     */
    public boolean isRealPass(int type, int subject) {
        Map<Integer, HeroTrialSubject> subjectMap = trialSubjectMap.get(type);
        if (subjectMap == null || !subjectMap.containsKey(subject)) {
            return false;
        }
        HeroTrialSubject heroTrialSubject = subjectMap.get(subject);
        int maxDiff = HeroTrialService.getMaxDiff(type, subject);
        return heroTrialSubject.isAllPass() && heroTrialSubject.getCurrentDiff() == maxDiff;
    }

    /**
     * 生成HeroTrialData Builder
     *
     * @param type    类型
     * @param subject 主题
     * @param data    HeroTrialInfo
     */
    public void genHeroTrialDataBuilder(int type, int subject, PbSync.HeroTrialData.Builder data) {
        PbHeroTrial.HeroTrialInfo.Builder heroInfoBuilder = PbHeroTrial.HeroTrialInfo.newBuilder();
        heroInfoBuilder.setType(type);
        Map<Integer, HeroTrialSubject> subjectMap = this.trialSubjectMap.get(type);
        if (subjectMap == null || !subjectMap.containsKey(subject)) {
            return;
        }
        HeroTrialSubject heroTrialSubject = subjectMap.get(subject);
        PbHeroTrial.HeroTrialSubjectInfo.Builder trialBuilder = PbHeroTrial.HeroTrialSubjectInfo.newBuilder();
        trialBuilder.setSubject(subject);
        trialBuilder.setIsAllPass(isRealPass(type, subject));
        trialBuilder.setDifficulty(heroTrialSubject.getCurrentDiff());
        heroInfoBuilder.addHeroSubjectInfo(trialBuilder);
        heroInfoBuilder.setTimes(challengeTimes.getOrDefault(type, 0));
        heroInfoBuilder.setType(type);
        data.addHeroTrialInfo(heroInfoBuilder);
        PbCommons.KeyValueDb.Builder kv = PbCommons.KeyValueDb.newBuilder();
        kv.setIntKey(type);
        kv.setBoolValue(isOpenDay(type));
        data.addOpenState(kv);
    }

    public PbSync.HeroTrialData.Builder genHeroTrialDataBuilder() {
        PbSync.HeroTrialData.Builder data = PbSync.HeroTrialData.newBuilder();
        Map<Integer, Map<Integer, Map<Integer, HeroTrialTemplate>>> heroTrialTemplateMap = HeroTrialService.getHeroTrialTemplateMap();
        for (HeroTrialType heroTrialType : HeroTrialType.values()) {
            int type = heroTrialType.id;
            PbHeroTrial.HeroTrialInfo.Builder heroInfoBuilder = PbHeroTrial.HeroTrialInfo.newBuilder();
            heroInfoBuilder.setType(type);
            Map<Integer, Map<Integer, HeroTrialTemplate>> subjectTemplateMap = heroTrialTemplateMap.get(type);
            for (Integer subject : subjectTemplateMap.keySet()) {
                HeroTrialSubject heroTrialSubject = getHeroTrialSubject(type, subject);
                PbHeroTrial.HeroTrialSubjectInfo.Builder trialBuilder = PbHeroTrial.HeroTrialSubjectInfo.newBuilder();
                if (Objects.nonNull(heroTrialSubject)) {
                    trialBuilder.setSubject(subject);
                    trialBuilder.setIsAllPass(isRealPass(type, subject));
                    trialBuilder.setDifficulty(heroTrialSubject.getCurrentDiff());
                } else {
                    // 每个主题补全第一关
                    trialBuilder.setSubject(subject)
                            .setIsAllPass(false)
                            .setDifficulty(1);

                }
                heroInfoBuilder.addHeroSubjectInfo(trialBuilder);
            }
            heroInfoBuilder.setTimes(challengeTimes.getOrDefault(type, 0));
            data.addHeroTrialInfo(heroInfoBuilder);
            PbCommons.KeyValueDb.Builder kv = PbCommons.KeyValueDb.newBuilder();
            kv.setIntKey(type);
            kv.setBoolValue(isOpenDay(type));
            data.addOpenState(kv);
        }
        return data;
    }

    public HeroTrialSubject getHeroTrialSubject(int type, int subject) {
        if (trialSubjectMap.containsKey(type)) {
            Map<Integer, HeroTrialSubject> subjectMap = trialSubjectMap.get(type);
            return subjectMap.get(subject);
        }
        return null;
    }

    public boolean isOpenDay(int type) {
        LocalDateTime now = ServerConstants.getCurrentTimeLocalDateTime();
        LocalDateTime refreshClock = now.withHour(CommonUtils.getRefreshTimeHour())
                .withMinute(0).withSecond(0).withNano(0);
        boolean targetClockLater = false;

        if (refreshClock.isBefore(now)) { //现在是在刷新点之后
            targetClockLater = true;
        }

        //如果是0点到6点  依然算前一天的  否则正常算
        HeroTrialType heroTrialType = HeroTrialType.getTypeById(type);
        if (targetClockLater) {
            return heroTrialType.openDay.contains(now.getDayOfWeek().getValue());
        } else {
            return heroTrialType.openDay.contains(now.minusDays(1).getDayOfWeek().getValue());
        }
    }

    public Map<Integer, Integer> getChallengeTimes() {
        return challengeTimes;
    }

    public int getChallengeTimes(int type) {
        return challengeTimes.getOrDefault(type, 0);
    }

    public void addChallengeTimes(int type) {
        challengeTimes.put(type, challengeTimes.getOrDefault(type, 0) + 1);
    }

    /**
     * 通关难度
     */
    public void passDiff(int type, int subject, int diff) {
        Map<Integer, HeroTrialSubject> subjectMap = trialSubjectMap.computeIfAbsent(type, map1 -> new HashMap<>());
        int maxDiff = HeroTrialService.getMaxDiff(type, subject);
        HeroTrialSubject heroTrialSubject = subjectMap.computeIfAbsent(subject, v1 -> new HeroTrialSubject(diff, false));
        int nextDiff = Math.min(maxDiff, diff + 1);
        // 通关了
        if (maxDiff == diff) {
            heroTrialSubject.setAllPass(true);
        }
        heroTrialSubject.setCurrentDiff(nextDiff);

    }

    /**
     * 获取通关总数
     */
    public int getTotalPassNum() {
        int total = 0;
        for (Map<Integer, HeroTrialSubject> entryMap : trialSubjectMap.values()) {
            for (HeroTrialSubject subject : entryMap.values()) {
                if (subject.isAllPass()) {
                    total += subject.getCurrentDiff();
                } else {
                    total += subject.getCurrentDiff() - 1;
                }
            }
        }
        return total;
    }


    @Override
    public PlayerEventType[] getEventTypes() {
        return new PlayerEventType[]{
                PlayerEventType.day5Refresh
        };
    }

    @Override
    public void handle(PlayerEvent event) {
        switch (event.getEventType()) {
            case day5Refresh: {
                getPlayer().dataSyncModule.syncAllHeroTrial();
                break;
            }
        }
    }
}

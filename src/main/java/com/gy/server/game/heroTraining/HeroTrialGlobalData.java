package com.gy.server.game.heroTraining;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.global.GlobalData;
import com.gy.server.game.heroTraining.bean.HeroTrialRankBean;
import com.gy.server.game.heroTraining.bean.HeroTrialRecord;
import com.gy.server.game.heroTraining.template.HeroTrialTemplate;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.ttlike.server.tl.baselib.serialize.world.HeroTrialWorldModelDb;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * <AUTHOR> - [Created on 2022/5/9 14:00]
 */
public class HeroTrialGlobalData extends GlobalData {

    /**
     * 排行榜信息
     * <试炼类型,<playerId, Bean>>
     */
    private Map<Integer, Map<Long, HeroTrialRankBean>> heroTrialWorldRankMap = new ConcurrentHashMap<>();


    @Override
    public void readFromPb(byte[] bytes) throws Exception {
        HeroTrialWorldModelDb heroTrialWorldModelDb = PbUtilCompress.decode(HeroTrialWorldModelDb.class, bytes);
        Map<Integer, HeroTrialWorldModelDb.HeroTrialRankMapDb> heroTrialWorldRankDbMap = heroTrialWorldModelDb.getHeroTrialWorldRankMap();
        for (Map.Entry<Integer, HeroTrialWorldModelDb.HeroTrialRankMapDb> mapEntry : heroTrialWorldRankDbMap.entrySet()) {
            Integer type = mapEntry.getKey();
            Map<Long, HeroTrialRankBean> rankBeanMap = this.heroTrialWorldRankMap.computeIfAbsent(type, map1 -> new HashMap<>());
            HeroTrialWorldModelDb.HeroTrialRankMapDb heroTrialRankMapDb = mapEntry.getValue();
            Map<Long, HeroTrialWorldModelDb.HeroTrialRankBeanDb> trialRankBeanMap = heroTrialRankMapDb.getHeroTrialWorldRankMap();
            for (Map.Entry<Long, HeroTrialWorldModelDb.HeroTrialRankBeanDb> rankBeanEntry : trialRankBeanMap.entrySet()) {
                Long playerId = rankBeanEntry.getKey();
                HeroTrialWorldModelDb.HeroTrialRankBeanDb heroTrialRankBeanDb = rankBeanEntry.getValue();
                HeroTrialRankBean heroTrialRankBean = new HeroTrialRankBean();
                heroTrialRankBean.setPlayerId(heroTrialRankBeanDb.getPlayerId());
                heroTrialRankBean.setPassCount(heroTrialRankBeanDb.getPassCount());
                heroTrialRankBean.setLastPastTime(heroTrialRankBeanDb.getLastPastTime());
                rankBeanMap.put(playerId, heroTrialRankBean);
            }
        }

    }

    @Override
    public byte[] writeToPb() {
        HeroTrialWorldModelDb heroTrialWorldModelDb = new HeroTrialWorldModelDb();
        Map<Integer, HeroTrialWorldModelDb.HeroTrialRankMapDb> heroTrialWorldRankMap = heroTrialWorldModelDb.getHeroTrialWorldRankMap();
        for (Map.Entry<Integer, Map<Long, HeroTrialRankBean>> mapEntry : this.heroTrialWorldRankMap.entrySet()) {
            Integer type = mapEntry.getKey();
            HeroTrialWorldModelDb.HeroTrialRankMapDb heroTrialRankMapDb = heroTrialWorldRankMap.computeIfAbsent(type, value1 -> new HeroTrialWorldModelDb.HeroTrialRankMapDb());
            Map<Long, HeroTrialWorldModelDb.HeroTrialRankBeanDb> rankBeanDbMap = heroTrialRankMapDb.getHeroTrialWorldRankMap();
            for (Map.Entry<Long, HeroTrialRankBean> rankBeanEntry : mapEntry.getValue().entrySet()) {
                Long playerId = rankBeanEntry.getKey();
                HeroTrialRankBean heroTrialRankBean = rankBeanEntry.getValue();
                HeroTrialWorldModelDb.HeroTrialRankBeanDb heroTrialRankBeanDb = new HeroTrialWorldModelDb.HeroTrialRankBeanDb();
                heroTrialRankBeanDb.setPlayerId(heroTrialRankBean.getPlayerId());
                heroTrialRankBeanDb.setPassCount(heroTrialRankBean.getPassCount());
                heroTrialRankBeanDb.setLastPastTime(heroTrialRankBean.getLastPastTime());
                rankBeanDbMap.put(playerId, heroTrialRankBeanDb);
            }
        }
        return PbUtilCompress.encode(heroTrialWorldModelDb);
    }

    /**
     * 查询玩家进度，没打过进度为0
     */
    public int getProgress(long playerId, int type) {
        HeroTrialRankBean heroTrialRankBean = getHeroTrialRankBean(playerId, type);
        if (Objects.isNull(heroTrialRankBean)) {
            return 0;
        }
        return heroTrialRankBean.getPassCount();
    }

    /**
     * 获取排行榜Bean
     *
     * @param playerId 玩家ID
     * @param type     试炼类型
     * @return 排行榜Bean
     */
    public HeroTrialRankBean getHeroTrialRankBean(long playerId, int type) {
        Map<Long, HeroTrialRankBean> heroTrialRankBeanMap = heroTrialWorldRankMap.get(type);
        if (Objects.isNull(heroTrialRankBeanMap) || !heroTrialRankBeanMap.containsKey(playerId)) {
            return null;
        }
        return heroTrialRankBeanMap.get(playerId);
    }

    /**
     * 根据playerId得到最高纪录的达成时间
     */
    public long getPassTime(long playerId, int type) {
        HeroTrialRankBean heroTrialRankBean = getHeroTrialRankBean(playerId, type);
        if (Objects.isNull(heroTrialRankBean)) {
            return 0;
        }
        return heroTrialRankBean.getLastPastTime();
    }

    /**
     * 更新排行榜数据
     */
    public void updateRank(Player player, int type) {
        Map<Long, HeroTrialRankBean> rankBeanMap = heroTrialWorldRankMap.computeIfAbsent(type, map1 -> new HashMap<>());
        HeroTrialRankBean heroTrialRankBean = rankBeanMap.computeIfAbsent(player.getPlayerId(), bean -> new HeroTrialRankBean());
        heroTrialRankBean.setPlayerId(player.getPlayerId());
        PlayerHeroTrialModel trialModel = player.getModel(PlayerModelEnums.heroTrail);
        heroTrialRankBean.setPassCount(trialModel.getTotalPassNum());
        heroTrialRankBean.setLastPastTime(ServerConstants.getCurrentTimeMillis());
    }
}

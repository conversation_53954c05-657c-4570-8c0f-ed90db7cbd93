package com.gy.server.game.heroTraining.template;

import com.gy.server.game.drop.RewardTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * 英雄试炼模板
 *
 * <AUTHOR> - [Created on 2022/5/5 15:39]
 */
public class HeroTrialTemplate {

    public int id;

    public int type;

    /**
     * 英雄主题 对应英雄ID
     */
    public int subject;

    /**
     * 难度
     */
    public int difficulty;

    /**
     * 解锁条件
     */
    public String unlock;

    /**
     * 推荐战力
     */
    public int power;

    /**
     * 战斗阵容表
     */
    public int battleCollect;

    /**
     * 属性阵容
     */
    public int level;
    /**
     * 战斗中生效的BUFF
     */
    public int buffId;

    /**
     * 通关奖励
     */
    public List<RewardTemplate> rewardList = new ArrayList<>();

}

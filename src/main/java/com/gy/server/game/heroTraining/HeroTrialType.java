package com.gy.server.game.heroTraining;

import com.gy.server.game.function.Function;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.rank.RankType;
import com.gy.server.game.record.RecordType;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public enum HeroTrialType {

    /**
     * 正常试炼
     */
    NORMAL,
    /**
     * 风
     */
    DRAGON,
    /**
     * 林
     */
    TIGER,
    /**
     * 火
     */
    BIRD,
    /**
     * 山
     */
    TORTOISE,
    ;


    public int id;
    /**
     * 角色阵营类型限制
     */
    public int soulType;
    /**
     * 开启时间
     */
    public Set<Integer> openDay = new HashSet<>();
    /**
     * 每日最大挑战层数
     */
    public int dailyChallengeLimit;
    public Function function;
    public LineupType lineupType;
    public RankType rankType;
    public RecordType recordType;

    public static void init(){
        for (HeroTrialType type : HeroTrialType.values()) {
            idToType.put(type.id, type);
            lineupToType.put(type.lineupType, type);
            rankToType.put(type.rankType, type);
            recordToType.put(type.recordType, type);
        }
    }


    public static final Map<Integer, HeroTrialType> idToType = new HashMap<>();
    public static final Map<LineupType, HeroTrialType> lineupToType = new HashMap<>();
    public static final Map<RankType, HeroTrialType> rankToType = new HashMap<>();
    public static final Map<RecordType, HeroTrialType> recordToType = new HashMap<>();

    public static HeroTrialType getTypeById(int id) {
        return idToType.get(id);
    }

    public static HeroTrialType getTypeByLineup(LineupType lineupType) {
        return lineupToType.get(lineupType);
    }

    public static HeroTrialType getTypeByRank(RankType rankType) {
        return rankToType.get(rankType);
    }
}

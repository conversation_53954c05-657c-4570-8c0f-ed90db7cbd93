package com.gy.server.game.heroTraining.bean;

/**
 * 英雄试炼主题Bean
 * 玩家英雄试炼主题信息
 *
 * <AUTHOR> - [Created on 2022/5/9 20:09]
 */
public class HeroTrialSubject {

    // 当前所在难度
    private int currentDiff;

    // 是否全部通关
    private boolean isAllPass;

    public int getCurrentDiff() {
        return currentDiff;
    }

    public void setCurrentDiff(int currentDiff) {
        this.currentDiff = currentDiff;
    }

    public boolean isAllPass() {
        return isAllPass;
    }

    public void setAllPass(boolean allPass) {
        isAllPass = allPass;
    }

    public HeroTrialSubject() {
    }

    public HeroTrialSubject(int currentDiff, boolean isAllPass) {
        this.currentDiff = currentDiff;
        this.isAllPass = isAllPass;
    }
}

package com.gy.server.game.global;

import java.time.LocalDateTime;
import java.util.*;

import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.Configuration;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.db.DbAssistant;
import com.gy.server.game.db.DbManager;
import com.gy.server.game.event.ServerEventHandler;
import com.gy.server.game.event.ServerEventManager;
import com.gy.server.game.fix.designer.world.FixDesignerGlobalData;
import com.gy.server.game.treasure.TreasureGlobalData;
import com.gy.server.utils.function.Ticker;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

/**
 * <AUTHOR> - [Created on 2019/1/18 16:36]
 */
public class GlobalDataManager implements Ticker {

    private static final long GAME_SAVE_PERIOD_SECOND = 300;
    private static final long CENTER_SAVE_PERIOD_SECOND = 300;
    public static final long SERVER_PREFIX = 10000000L;

    private static final GlobalDataManager INSTANCE = new GlobalDataManager();

    private static Map<Integer, Map<GlobalDataType, GlobalData>> dataMap = new HashMap<>();

    /**
     * 下次存储数据时间
     */
    private LocalDateTime nextSaveTime;

    private GlobalDataManager() {

    }

    public static GlobalDataManager getInstance() {
        return INSTANCE;
    }

    public static void startup() throws Exception {
        init();
    }

    private static void init() throws Exception {
        for(int serverNumber : Configuration.dataSourceInfos.keySet()) {
            for (GlobalDataType type : GlobalDataType.values()) {
                // 只加载包含当前服务器类型的
                if (!type.getServerTypes().contains(Configuration.serverType)) {
                    continue;
                }

                //读取数据
                GlobalData data = load(serverNumber, type);
                if (data == null) {
                    data = type.create();

                    if (type == GlobalDataType.worldBase) {
                        //首次开服
                        DbAssistant.updatePlayerAutoIncrement(Configuration.serverId * SERVER_PREFIX + 1);
                        DbAssistant.updateLeagueAutoIncrement(Configuration.serverId * SERVER_PREFIX + 1);
                    } else if (type == GlobalDataType.FixDesigner) {
                        FixDesignerGlobalData model = (FixDesignerGlobalData) data;
                        model.openServerFix();
                    }

                    save(serverNumber,data);
                }

                //数据初始化
                data.init();

                if (data instanceof ServerEventHandler) {
                    //global注册为服务器事件处理器
                    ServerEventManager.registerEventHandler((ServerEventHandler) data);
                }

                //放入集合
                Map<GlobalDataType, GlobalData> map = dataMap.get(serverNumber);
                if(map == null){
                    map = new HashMap<>();
                    dataMap.put(serverNumber, map);
                }
                map.put(type, data);
            }
        }

        //初始化下次数据存储时间
        INSTANCE.nextSaveTime = ServerConstants.getCurrentTimeLocalDateTime().plusSeconds(GAME_SAVE_PERIOD_SECOND);
    }

    public static void shutdown() {
        //马贼事件强制结束
        if(GlobalDataType.treasure.getServerTypes().contains(Configuration.serverType)){
            TreasureGlobalData treasureGlobalData = getData(GlobalDataType.treasure);
            treasureGlobalData.shutdown();
        }
        //存储所有数据
        saveAll();
    }

    private static GlobalData load(int serverNumber, GlobalDataType type) throws Exception {
        Global global = DbManager.get(serverNumber, Global.class, type);
        if (global != null) {
            GlobalData data = type.create();
            if(!type.isJpbSave()) {
                data.readFromPb(global.getInfo().getBytes());
            }else{
                data = PbUtilCompress.decode(data.getClass(), global.getInfo().getBytes());
                if(data != null) {
                    data.setType(type);
                }
            }
            return data;
        }
        return null;
    }

    private static void save(int serverNumber, GlobalData data) {
        //主节点数据只运行主节点保存
        if(Configuration.serverType.isWorld()){
            if(!CommonUtils.isMainWorldServer()) {
                return;
            }
        }

        Global global = new Global();
        global.setType(data.getType());
        global.getInfo().setBytes(data.writeToPb());
        DbManager.update(serverNumber, global);
    }

    private static void saveAll() {
        if(Configuration.serverType.isWorld()){
            if(!CommonUtils.isMainWorldServer()) {
                return;
            }
        }

        for(Map.Entry<Integer, Map<GlobalDataType, GlobalData>> entry : dataMap.entrySet()){
            int serverNumber = entry.getKey();
            Map<GlobalDataType, GlobalData> map = entry.getValue();
            List<Global> list = new ArrayList<>();
            for(GlobalData data : map.values()){
                Global global = new Global();
                global.setType(data.getType());
                global.getInfo().setBytes(data.writeToPb());

                list.add(global);
            }
            if(!list.isEmpty()){
                DbManager.updateBatch(serverNumber,list);
            }
        }
    }

    @SuppressWarnings("unchecked")
    public static <T extends GlobalData> T getData(GlobalDataType type) {
        return (T) dataMap.get(type);
    }

    public static Map<GlobalDataType, GlobalData> getDataMap(int serverNumber) {
        return dataMap.get(serverNumber);
    }

    public static Map<Integer, Map<GlobalDataType, GlobalData>> getDataMap() {
        return dataMap;
    }

    @Override
    public void tick() {
        LocalDateTime now = ServerConstants.getCurrentTimeLocalDateTime();

        //保存数据
        if (now.isAfter(nextSaveTime)) {
            if (Configuration.serverType.isGame()) {
                nextSaveTime = now.plusSeconds(GAME_SAVE_PERIOD_SECOND);
            } else {
                nextSaveTime = now.plusSeconds(CENTER_SAVE_PERIOD_SECOND);
            }

            ThreadPool.execute(GlobalDataManager::saveAll);
        }

        //模块tick
        for (GlobalDataType dataType : GlobalDataType.values()) {
            if(dataType.getServerTypes().contains(Configuration.serverType)){
                GlobalData data = getData(dataType);
                if(Objects.nonNull(data)){
                    data.tick();
                }
            }
        }
    }
}

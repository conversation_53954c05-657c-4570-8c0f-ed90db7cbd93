package com.gy.server.game.giftPackage.template;

import com.gy.server.common.base.AbsTemplate;
import com.gy.server.common.util.CommonUtils;
import com.gy.server.game.giftPackage.PlayerGiftPackageModel;
import com.gy.server.game.util.StringExtUtil;
import com.gy.server.utils.CollectionUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class GPPopUpTemplate implements AbsTemplate {

    public int id;

    public int giftId;

    public int goal;

    public int cleanGoal;

    public List<Integer> previousIds = new ArrayList<>();

    @Override
    public void readTxt(Map<String, String> map) {
        id = Integer.parseInt(map.get("id"));
        giftId = Integer.parseInt(map.get("giftId"));
        goal = Integer.parseInt(map.get("goal"));
        cleanGoal = Integer.parseInt(map.get("cleanGoal"));
        String previousStr = map.get("previous");
        if(!"-1".equals(previousStr)){
            previousIds.addAll(StringExtUtil.string2List(map.get("previous"), ",", Integer.class));
        }
    }


    public boolean canClearGoal(long lastRefreshTime){
        /**
         * -1=不清理
         * 1=日清理
         * 2=自然周清理
         * 3=自然月清理
         */
        switch (cleanGoal){
            case 1:{
                return CommonUtils.isNewDay(lastRefreshTime);
            }
            case 2:{
                return CommonUtils.isNewWeek(lastRefreshTime);
            }
            case 3:{
                return CommonUtils.isNewMonth(lastRefreshTime);
            }
            default:{
                return false;
            }
        }
    }

    /**
     * 检查是否有前置限制
     */
    public boolean checkPrevious(PlayerGiftPackageModel model){
        if(CollectionUtil.isNotEmpty(previousIds)){
            for (Integer previousId : previousIds) {
                if(!model.hasBuyGiftPackage(previousId)){
                    return true;
                }
            }
        }
        return false;
    }

}

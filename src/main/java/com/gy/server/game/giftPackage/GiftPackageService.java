package com.gy.server.game.giftPackage;

import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.cond.CondManager;
import com.gy.server.game.currency.Currency;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.giftPackage.template.GPPopUpTemplate;
import com.gy.server.game.giftPackage.template.GiftPackageTemplate;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.service.Service;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: gs-trunk
 * @description: 通用礼包模块
 * @author: Huang.Xia
 * @create: 2025/4/27
 **/
public class GiftPackageService extends PlayerPacketHandler implements Service {

    public static Map<Integer, GiftPackageTemplate> giftPackageTemplates = new HashMap<>();
    public static Map<Integer, GPPopUpTemplate> popUpTemplateMap = new HashMap<>();

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        Map<Integer, GiftPackageTemplate> tmpMap = new HashMap<>();
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.giftPackage_giftPackage);
        for (Map<String, String> map : mapList) {
            GiftPackageTemplate template = new GiftPackageTemplate();
            template.readTxt(map);
            tmpMap.put(template.id, template);
        }
        giftPackageTemplates = tmpMap;

        mapList = ConfigReader.read(ConfigFile.giftPackage_giftGoal);
        Map<Integer, GPPopUpTemplate> popUpTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            GPPopUpTemplate template = new GPPopUpTemplate();
            template.readTxt(map);
            popUpTemplateMapTemp.put(template.id, template);
        }
        popUpTemplateMap = popUpTemplateMapTemp;
    }

    @Handler(PtCode.GIFT_PACKAGE_BUY_REQ)
    private void buyGiftPackage(Player player, PbProtocol.GiftPackageBuyReq req, long time) {
        PbProtocol.GiftPackageBuyRst.Builder b = PbProtocol.GiftPackageBuyRst.newBuilder();
        b.setResult(Text.genOkServerRstInfo());
        int giftPackageId = req.getPackageId();
        int count = req.getCount();
        PlayerGiftPackageModel model = player.getPlayerGiftPackageModel();
        GiftPackage giftPackage = model.getPackages().get(giftPackageId);
        logic:
        {
            if (giftPackage == null) {
                b.setResult(Text.genServerRstInfo(Text.该礼包不存在));
                break logic;
            }

            //参数合法性检测
            if(count <= 0 || count > 100){
                b.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }

            GiftPackageTemplate template = giftPackageTemplates.get(giftPackage.getTid());
            if (template == null) {
                b.setResult(Text.genServerRstInfo(Text.该礼包不存在));
                break logic;
            }

            if(giftPackage.isExpired()){
                b.setResult(Text.genServerRstInfo(Text.该礼包已过期));
                break logic;
            }

            if(template.canNotBuy(giftPackage.getBuyTimes() + count - 1)){
                b.setResult(Text.genServerRstInfo(Text.该礼包购买次数不足));
                break logic;
            }

            //校验条件
            if (!CondManager.checkCond(player, template.unlockType)) {
                b.setResult(Text.genServerRstInfo(Text.该礼包未解锁));
                break logic;
            }

            /**
             * 0:免费
             * 1：玉
             * 2：索引pay表
             * 3：元宝
             */
            Reward cost = null;
            switch (template.payWay){
                case 0:
                    break;
                case 1:{
                    cost = Reward.create(Currency.jade, template.payPara * count);
                    break;
                }
                case 2:{
                    throw new IllegalArgumentException("直购商品请走充值流程");
                }
                case 3:{
                    cost = template.payRewardCost.createReward();
                    cost.setValue(cost.getValue() * count);
                    break;
                }
            }

            if(cost!= null){

                if(cost.getValue() < 0){
                    //溢出检测
                    b.setResult(Text.genServerRstInfo(Text.参数异常));
                    break logic;
                }

                //资源检查
                if(cost.check(player) != -1){
                    b.setResult(Text.genServerRstInfo(Text.消耗不足));
                    break logic;
                }

                //扣除资源
                cost.remove(player, BehaviorType.giftPackageBuy);
            }

            //发货
            giftPackage.setBuyTimes(giftPackage.getBuyTimes() + count);
            List<Reward> rewards =  new ArrayList<>();
            for(int i = 0; i < count; i++) {
               rewards.addAll(RewardTemplate.createRewards(template.contents));
            }
            model.dealGiveReward(rewards, template);

            Reward.merge(rewards);
            Reward.add( rewards, player, BehaviorType.giftPackageBuy);
            b.addAllRewards(Reward.writeCollectionToPb(rewards));

            model.checkPopUpGP();

            giftPackage.sync(player,false);

            player.postEvent(PlayerEventType.giftPackageBuy, giftPackage.getTid());
        }

        player.send(PtCode.GIFT_PACKAGE_BUY_RST, b.build(), time);
    }

    @Handler(PtCode.GIFT_PACKAGE_POP_REQ)
    private void popNotice(Player player, PbProtocol.GiftPackagePopReq req, long time) {
        PbProtocol.GiftPackagePopRst.Builder b = PbProtocol.GiftPackagePopRst.newBuilder();
        b.setResult(Text.genOkServerRstInfo());
        List<Integer> ids = req.getPackageIdsList();
        PlayerGiftPackageModel model = player.getPlayerGiftPackageModel();
        for(int id : ids){
            GiftPackage giftPackage = model.getPackages().get(id);
            if(giftPackage != null){
                giftPackage.setNew(false);
            }
        }
        player.send(PtCode.GIFT_PACKAGE_POP_RST, b.build(), time);
    }
}

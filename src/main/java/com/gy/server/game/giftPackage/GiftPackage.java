package com.gy.server.game.giftPackage;

import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.log.SystemLogger;
import com.gy.server.game.activity.ActivityTemplate;
import com.gy.server.game.giftPackage.template.GiftPackageTemplate;
import com.gy.server.game.player.Player;
import com.gy.server.packet.PbCommons;
import com.gy.server.packet.PbSync;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.serialize.giftPackage.GiftPackageDb;

/**
 * @program: gs-trunk
 * @description: 礼包
 * @author: <PERSON>.<PERSON>
 * @create: 2025/4/27
 **/
public class GiftPackage {

    /**
     * 实例id
     */
    private int id;

    private int tid;

    /**
     * 已购次数
     */
    private int buyTimes;

    /**
     * 过期时间
     */
    private long expireTime;

    /**
     * 最后刷新时间
     */
    private long lastRefreshTime;

    private int activityId;

    /**
     * 是否新礼包
     */
    private boolean isNew = true;

    public GiftPackage(){}

    public GiftPackage(int id, int tid) {
        this.tid = tid;
        this.id = id;
        initExpireTime(null);
    }

    public GiftPackage(int id, int tid, ActivityTemplate activityTemplate) {
        this.id = id;
        this.tid = tid;
        this.activityId = activityTemplate.id;
        initExpireTime(activityTemplate);
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getTid() {
        return tid;
    }

    public void setTid(int tid) {
        this.tid = tid;
    }

    public boolean isNew() {
        return isNew;
    }

    public void setNew(boolean aNew) {
        isNew = aNew;
    }

    public int getBuyTimes() {
        return buyTimes;
    }

    public int getActivityId() {
        return activityId;
    }

    public void setActivityId(int activityId) {
        this.activityId = activityId;
    }

    public void setBuyTimes(int buyTimes) {
        this.buyTimes = buyTimes;
    }

    public long getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(long expireTime) {
        this.expireTime = expireTime;
    }

    public long getLastRefreshTime() {
        return lastRefreshTime;
    }

    public void setLastRefreshTime(long lastRefreshTime) {
        this.lastRefreshTime = lastRefreshTime;
    }

    public void initExpireTime(ActivityTemplate activityTemplate) {
        GiftPackageTemplate template = GiftPackageService.giftPackageTemplates.get(tid);
        if(template == null){
            SystemLogger.error("GiftPackageTemplate not found, tid: " + tid);
            throw new IllegalArgumentException("GiftPackageTemplate not found, tid: " + tid);
        }

        switch (template.timeType){
            case 1:{
                //固定时长
                expireTime = ServerConstants.getCurrentTimeMillis() + template.timePara * 1000;
                break;
            }
            case 2:{
                //活动时长
                if(activityTemplate == null){
                    throw new IllegalArgumentException("ActivityTemplate not found, packageTid : " + tid);
                }
                if(activityTemplate.endTime != null) {
                    expireTime = DateTimeUtil.toMillis(activityTemplate.endTime);
                }else {
                    expireTime = -1;//长期有效
                }
                break;
            }
            case 3:{
                //下一次刷新点
                expireTime = DateTimeUtil.toMillis(CommonUtils.getNextClock());
                break;
            } default:{
                throw new IllegalArgumentException("Invalid timeType: " + template.timeType);
            }
        }
    }

    public boolean canRefresh(){
        GiftPackageTemplate template = GiftPackageService.giftPackageTemplates.get(tid);
        if(template == null){
            SystemLogger.error("GiftPackageTemplate not found, tid: " + tid);
            throw new IllegalArgumentException("GiftPackageTemplate not found, tid: " + tid);
        }

        /**
         * 1、不刷新
         * 2、日刷新
         * 3、周刷新
         * 4、月刷新
         */
        switch (template.refreshType){
            case 1:{
                return false;
            }
            case 2:{
                return CommonUtils.isNewDay(lastRefreshTime);
            }
            case 3:{
                return CommonUtils.isNewWeek(lastRefreshTime);
            }
            case 4:{
                return CommonUtils.isNewMonth(lastRefreshTime);
            }
            default:{
                throw new IllegalArgumentException("Invalid refreshType: " + template.refreshType);
            }
        }
    }

    public void refresh(){
        this.lastRefreshTime = ServerConstants.getCurrentTimeMillis();
        this.buyTimes = 0;
        this.isNew = true;
    }

    public boolean isExpired() {
        return expireTime > 0 && ServerConstants.getCurrentTimeMillis() > expireTime;
    }

    public PbCommons.GiftPackage.Builder toPb(){
        PbCommons.GiftPackage.Builder builder = PbCommons.GiftPackage.newBuilder();
        builder.setId(id)
                .setTid(tid)
                .setActivityId(activityId)
                .setBuyTimes(buyTimes)
                .setExpireTime(expireTime);
        return builder;
    }

    public void sync(Player player, boolean withTemplate){
        PbSync.GiftPackageSyncData.Builder builder = PbSync.GiftPackageSyncData.newBuilder();
        builder.addGiftPackage(toPb());
        player.dataSyncModule.syncGiftPackage(builder);
    }

    public void syncRemove(Player player){
        PbSync.GiftPackageSyncData.Builder builder = PbSync.GiftPackageSyncData.newBuilder();
        builder.addRemoveIds(id);
        builder.putRemovePackages(id, activityId);
        player.dataSyncModule.syncGiftPackage(builder);
    }

    public GiftPackageDb toDb() {
        GiftPackageDb db = new GiftPackageDb();
        db.setId(id);
        db.setTid(tid);
        db.setBuyTimes(buyTimes);
        db.setExpireTime(expireTime);
        db.setLastRefreshTime(lastRefreshTime);
        db.setActivityId(activityId);
        db.setNew(isNew);
        return db;
    }

    public static GiftPackage fromDb(GiftPackageDb db){
        GiftPackage giftPackage = new GiftPackage();
        giftPackage.id = db.getId();
        giftPackage.tid = db.getTid();
        giftPackage.buyTimes = db.getBuyTimes();
        giftPackage.expireTime = db.getExpireTime();
        giftPackage.lastRefreshTime = db.getLastRefreshTime();
        giftPackage.activityId = db.getActivityId();
        giftPackage.isNew = db.isNew();
        return giftPackage;
    }
}

package com.gy.server.game.giftPackage;

import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.activity.ActivityTemplate;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.function.Function;
import com.gy.server.game.giftPackage.template.GPPopUpTemplate;
import com.gy.server.game.giftPackage.template.GiftPackageTemplate;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.pay.ReceiptCheckResult;
import com.gy.server.game.pay.ReceiptFinishHandler;
import com.gy.server.game.pay.ReceiptFinishResult;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModel;
import com.gy.server.game.player.event.PlayerEvent;
import com.gy.server.game.player.event.PlayerEventHandler;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbSync;
import com.gy.server.utils.CollectionUtil;
import com.ttlike.server.tl.baselib.serialize.giftPackage.GiftPackageDb;
import com.ttlike.server.tl.baselib.serialize.giftPackage.PlayerGiftPackageModelDb;
import com.ttlike.server.tl.baselib.serialize.player.PlayerBlobDb;

import java.util.*;

/**
 * @program: gs-trunk
 * @description: 礼包模块
 * @author: Huang.Xia
 * @create: 2025/4/27
 **/
public class PlayerGiftPackageModel extends PlayerModel implements PlayerEventHandler, ReceiptFinishHandler {

    private Map<Integer, GiftPackage> packages = new HashMap<>();


    /**
     * 拍脸礼包清除goal时间信息
     * 拍脸礼包id_上次清除goal时间
     */
    private Map<Integer, Long> popUpLastClearGoalTimes = new HashMap<>();

    /**
     * 等待拍脸礼包
     */
    private List<Integer> waitPopUpIds = new ArrayList<>();

    /**
     * 礼包购买map
     * key:礼包模板     value:购买次数
     */
    private Map<Integer, Integer> buyNumMap = new HashMap<>();

    public PlayerGiftPackageModel(Player player) {
        super(player);
    }

    /**
     * 常规功能礼包添加入口
     */
    public void addPackage(int packageTid){
        Player player = getPlayer();
        GiftPackage gift = new GiftPackage(player.allocateIdentifier(), packageTid);
        packages.put(gift.getId(), gift);

        gift.sync(player, true);
    }

    /**
     * 活动礼包添加入口
     */
    public void addPackage(int packageTid, ActivityTemplate activityTemplate){
        Player player = getPlayer();
        GiftPackage gift = new GiftPackage(player.allocateIdentifier(), packageTid, activityTemplate);
        packages.put(gift.getId(), gift);
        gift.sync(player, true);
    }

    public void removePackage(int packageTid, int activityId){
        Player player = getPlayer();

        Iterator iterator = packages.values().iterator();
        while (iterator.hasNext()){
            GiftPackage gift = (GiftPackage) iterator.next();
            if(gift.getTid() == packageTid){
                if(activityId <= 0 || activityId == gift.getActivityId()){
                    iterator.remove();
                    gift.syncRemove(player);
                }
            }
        }
    }

    public PbSync.GiftPackageSyncData genUserData(){
        PbSync.GiftPackageSyncData.Builder builder = PbSync.GiftPackageSyncData.newBuilder();
        for(GiftPackage gift : packages.values()){
            if(!gift.isExpired()) {
                GiftPackageTemplate template = GiftPackageService.giftPackageTemplates.get(gift.getTid());
                if (template != null) {
                    builder.addGiftPackage(gift.toPb());
                }
            }
        }
        return builder.build();
    }

    @Override
    public ReceiptCheckResult orderCheck(Player player, int goodsId, List<String> extParams) {
        int giftPackageId = Integer.parseInt(extParams.get(0));
        GiftPackage giftPackage = packages.get(giftPackageId);
        if (giftPackage == null) {
            return ReceiptCheckResult.failed(Text.该礼包不存在);
        }

        GiftPackageTemplate template = GiftPackageService.giftPackageTemplates.get(giftPackage.getTid());
        if (template == null) {
            return ReceiptCheckResult.failed(Text.该礼包不存在);
        }

        if(giftPackage.isExpired()){
            return ReceiptCheckResult.failed(Text.该礼包已过期);
        }

        if(template.canNotBuy(giftPackage.getBuyTimes())){
            return ReceiptCheckResult.failed(Text.该礼包购买次数不足);
        }

        if(template.payWay != 2){
            return ReceiptCheckResult.failed(Text.参数异常);
        }

        return ReceiptCheckResult.success(template.payPara);
    }

    @Override
    public ReceiptFinishResult finishReceipt(Player player, int goodsId, List<String> extParams) {
        int giftPackageId = Integer.parseInt(extParams.get(0));
        GiftPackage giftPackage = packages.get(giftPackageId);
        if (giftPackage == null) {
            return ReceiptFinishResult.fail();
        }

        GiftPackageTemplate template = GiftPackageService.giftPackageTemplates.get(giftPackage.getTid());
        if (template == null) {
            return ReceiptFinishResult.fail();
        }

        if(giftPackage.isExpired()){
            return ReceiptFinishResult.fail();
        }

        if(template.canNotBuy(giftPackage.getBuyTimes())){
            return ReceiptFinishResult.fail();
        }

        if(template.payWay != 2){
            return ReceiptFinishResult.fail();
        }

        //发货
        giftPackage.setBuyTimes(giftPackage.getBuyTimes() + 1);
        List<Reward> rewards = RewardTemplate.createRewards(template.contents);
        dealGiveReward(rewards, template);

        Reward.add( rewards, player, BehaviorType.giftPackageBuy);

        checkPopUpGP();

        //同步
        giftPackage.sync(player, false);
        player.postEvent(PlayerEventType.giftPackageBuy, giftPackage.getTid());

        return new ReceiptFinishResult(true,  rewards);
    }

    public void dealGiveReward(List<Reward> rewards, GiftPackageTemplate template){
        //处理额外奖励
        if(!hadBuy(template.id)){
            if(!template.firstGive.isEmpty()){
                rewards.addAll(Reward.templateCollectionToReward(template.firstGive));
            }else {
                if(!template.normalGive.isEmpty()){
                    rewards.addAll(Reward.templateCollectionToReward(template.normalGive));
                }
            }
        }else {
            if(!template.normalGive.isEmpty()){
                rewards.addAll(Reward.templateCollectionToReward(template.normalGive));
            }
        }

        addBuy(template.id);
    }

    @Override
    public PbProtocol.PayCreateReceiptReq.PayFunction getPayFunction() {
        return PbProtocol.PayCreateReceiptReq.PayFunction.GiftPackage;
    }

    @Override
    protected void loadData(PlayerBlobDb playerBlob) {
        PlayerGiftPackageModelDb giftPackageModelDb = playerBlob.getGiftPackageModelDb();
        if(giftPackageModelDb != null){
            for(GiftPackageDb giftDb : giftPackageModelDb.getGiftPackages().values()){
                GiftPackage gift = GiftPackage.fromDb(giftDb);
                GiftPackageTemplate template = GiftPackageService.giftPackageTemplates.get(gift.getTid());
                if (Objects.isNull(template)) {
                    CommonUtils.handleException(new RuntimeException(String.format("gift read from db error, template not found, id: %s.", gift.getTid())));
                    continue;
                }
                packages.put(gift.getId(), gift);
            }
            popUpLastClearGoalTimes = new HashMap<>(giftPackageModelDb.getPopUpLastClearGoalTimes());
            waitPopUpIds = new ArrayList<>(giftPackageModelDb.getWaitPopUpIds());
            buyNumMap.putAll(giftPackageModelDb.getBuyNumMap());
        }
    }

    @Override
    protected void saveData(PlayerBlobDb playerBlob) {
        PlayerGiftPackageModelDb db = new PlayerGiftPackageModelDb();
        for(GiftPackage gift : packages.values()){
            GiftPackageDb giftDb = gift.toDb();
            db.getGiftPackages().put(giftDb.getId(), giftDb);
        }
        db.setPopUpLastClearGoalTimes(popUpLastClearGoalTimes);
        db.setWaitPopUpIds(waitPopUpIds);
        db.getBuyNumMap().putAll(buyNumMap);
        playerBlob.setGiftPackageModelDb(db);
    }

    @Override
    public PlayerEventType[] getEventTypes() {
        return new PlayerEventType[]{PlayerEventType.day5Refresh
                , PlayerEventType.goalFinish
                , PlayerEventType.loginAfter
                , PlayerEventType.functionCheck};
    }

    @Override
    public void handle(PlayerEvent event) {
        switch (event.getEventType()) {
            case day5Refresh:
            {
                for(GiftPackage gift : packages.values()){
                    if(gift.canRefresh()){
                        gift.refresh();
                        gift.sync(event.getSource(),false);
                    }
                }
                popUpRefresh();
                popUpGoalCheck();
                break;
            }
            case loginAfter:
            case functionCheck: {
                popUpGoalCheck();
                break;
            }
            case goalFinish: {
                int id = event.getParam(0);
                for (GPPopUpTemplate popUpTemplate : GiftPackageService.popUpTemplateMap.values()) {
                    //检测
                    if(popUpTemplate.goal == id){
                        if(!popUpTemplate.checkPrevious(this)){
                            //触发礼包
                            addPopUpGP(popUpTemplate);
                        }else{
                            waitPopUpIds.add(popUpTemplate.id);
                        }
                    }
                }
                break;
            }
        }

    }

    public void addPopUpGP(GPPopUpTemplate popUpTemplate){
        addPackage(popUpTemplate.giftId);
//        checkPopUpGP();
    }

    /**
     * 检查后置礼包能否可以触发
     */
    public void checkPopUpGP(){
        if(CollectionUtil.isNotEmpty(waitPopUpIds)){
            List<Integer> waitPopUpIdsTemp = new ArrayList<>(waitPopUpIds);
            for (Integer waitPopUpId : waitPopUpIdsTemp) {
                GPPopUpTemplate waitPopUpTemp = GiftPackageService.popUpTemplateMap.get(waitPopUpId);
                if(waitPopUpTemp.checkPrevious(this)){
                    waitPopUpIds.remove(waitPopUpId);
                    addPopUpGP(waitPopUpTemp);
                }
            }
        }
    }

    public boolean hasBuyGiftPackage(int tid){
        for (GiftPackage aPackage : packages.values()) {
            if(aPackage.getTid() == tid && aPackage.getBuyTimes() > 0){
                return true;
            }
        }
        return false;
    }

    /**
     * 拍脸礼包每日刷新
     */
    public void popUpRefresh(){
        Player player = getPlayer();
        for (Map.Entry<Integer, Long> infos : popUpLastClearGoalTimes.entrySet()) {
            GPPopUpTemplate gpPopUpTemplate = GiftPackageService.popUpTemplateMap.get(infos.getKey());
            if(Objects.nonNull(gpPopUpTemplate)){
                if(gpPopUpTemplate.canClearGoal(infos.getValue())){
                    popUpLastClearGoalTimes.put(infos.getKey(), ServerConstants.getCurrentTimeMillis());
                    player.getGoalModel().remove(gpPopUpTemplate.goal);
                }
            }
        }
    }

    public void popUpGoalCheck(){
        Player player = getPlayer();
        if(Function.giftPopUp.isOpen(player)){
            //检查是否还有没注册的goal
            for (GPPopUpTemplate popUpTemplate : GiftPackageService.popUpTemplateMap.values()) {
                player.getGoalModel().tryRegister(popUpTemplate.goal);
            }
        }
    }

    /**
     * 增加购买次数
     * @param templateId 礼包模板id
     */
    public void addBuy(int templateId){
        buyNumMap.merge(templateId, 1, Integer::sum);
    }

    /**
     * 礼包是否购买
     * @param templateId
     * @return
     */
    public boolean hadBuy(int templateId){
        return buyNumMap.containsKey(templateId);
    }

    public Map<Integer, GiftPackage> getPackages() {
        return packages;
    }

    public void setPackages(Map<Integer, GiftPackage> packages) {
        this.packages = packages;
    }

    public Map<Integer, Integer> getBuyNumMap() {
        return buyNumMap;
    }

    public void setBuyNumMap(Map<Integer, Integer> buyNumMap) {
        this.buyNumMap = buyNumMap;
    }
}

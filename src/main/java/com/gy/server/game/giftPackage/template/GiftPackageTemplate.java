package com.gy.server.game.giftPackage.template;


import java.util.*;
import com.gy.server.common.base.AbsTemplate;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.packet.PbCommons;

public class GiftPackageTemplate implements AbsTemplate {

    public int id; // 礼包id 
    public int name; // 礼包名称 
    public int rebate; // 返利比 
    public List<RewardTemplate> contents; // 礼包内容

    /**
     * 首次赠送
     * 首次赠送与每次赠送互斥
     */
    public List<RewardTemplate> firstGive;
    /**
     * 每次赠送
     */
    public List<RewardTemplate> normalGive;

    /**
     * 1、不刷新
     * 2、日刷新
     * 3、周刷新
     * 4、月刷新
     */
    public int refreshType; // 刷新类型

    /**
     * 限购次数
     * -1 不限购
     */
    public int limitTimes;

    /**
     * 1：固定时长，倒计时秒
     * 2：活动结束时间
     * 3：下一次刷新时间点（游戏玩法统一刷新时间点）
     */
    public int timeType; // 有效期 
    public int timePara; // 时间参数（秒） 
    public int group; // 组id  
    public int sort; // 排序
    /**
     * 0:免费
     * 1：玉
     * 2：索引pay表
     * 3：reward支付
     */
    public int payWay; // 支付方式 
    public int payPara; // 支付参数（价格）

    public RewardTemplate payRewardCost;//支付类型4时专用
    public int showType; // 展示方式

    /**
     * 解锁方式
     */
    public String unlockType;


    @Override
    public void readTxt(Map<String, String> map) {
        id = Integer.parseInt(map.get("id"));
        name = Integer.parseInt(map.get("name"));
        rebate = Integer.parseInt(map.get("rebate"));
        contents = RewardTemplate.readListFromText(map.get("rewards"));
        firstGive = RewardTemplate.readListFromText(map.get("firstGive"));
        normalGive = RewardTemplate.readListFromText(map.get("normalGive"));
        refreshType = Integer.parseInt(map.get("refreshType"));
        limitTimes = Integer.parseInt(map.get("limitTimes"));
        timeType = Integer.parseInt(map.get("timeType"));
        timePara = Integer.parseInt(map.get("timePara"));
        group = Integer.parseInt(map.get("group"));
        sort = Integer.parseInt(map.get("sort"));
        payWay = Integer.parseInt(map.get("payWay"));
        if(payWay == 3){
            payRewardCost = RewardTemplate.readListFromText(map.get("payPara")).get(0);
        }else {
            payPara = Integer.parseInt(map.get("payPara"));
        }
        showType = Integer.parseInt(map.get("showType"));
        unlockType = map.get("unlockType");

    }

    @Override
    public String toString() {
        return "GiftPackageTemplate{"
                + "id=" + id
                + "name=" + name
                + "rebate=" + rebate
                + "contents=" + contents
                + "firstGive=" + firstGive
                + "normalGive=" + normalGive
                + "refreshType=" + refreshType
                + "limitTimes=" + limitTimes
                + "timeType=" + timeType
                + "timePara=" + timePara
                + "group=" + group
                + "sort=" + sort
                + "payWay=" + payWay
                + "payPara=" + payPara
                + "showType=" + showType
                + "unlockType=" + unlockType
                + '}';
    }

    public PbCommons.GiftPackageTemplate.Builder toPb() {
        PbCommons.GiftPackageTemplate.Builder builder = PbCommons.GiftPackageTemplate.newBuilder();
        builder.setTid(id)
                .setName(name)
                .setRebate(rebate)
                .setRefreshType(refreshType)
                .setLimitTimes(limitTimes)
                .setGroup(group)
                .setSort(sort)
                .setPayPara(payPara)
                .setPayWay(payWay)
                .setShowType(showType)
                .setUnlockType(unlockType);
        if(payRewardCost != null){
            builder.setPayRewardCost(payRewardCost.writeToPb());
        }
        if (builder != null) {
            builder.addAllRewards(RewardTemplate.writeCollectionToPb(contents));
            builder.addAllFirstGives(RewardTemplate.writeCollectionToPb(firstGive));
            builder.addAllNormalGives(RewardTemplate.writeCollectionToPb(normalGive));
        }
        return builder;
    }


    public boolean canNotBuy(int num){
        return num >= limitTimes && limitTimes != -1;
    }

    public boolean canBuy(int num){
        return !canNotBuy(num);
    }
}

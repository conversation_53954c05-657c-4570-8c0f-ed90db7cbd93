package com.gy.server.game.battleCollect;

import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.attribute.AttributeKey;
import com.gy.server.game.attribute.Attributes;
import com.gy.server.game.battleCollect.template.BattleCollectTemplate;
import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.combat.unit.TeamUnit;
import com.gy.server.game.monster.MonsterService;
import com.gy.server.game.monster.MonsterTemplate;
import com.gy.server.game.service.Service;
import com.gy.server.game.util.StringExtUtil;
import com.gy.server.utils.StringUtil;

import java.util.*;

/**
 * <AUTHOR> - [Created on 2022/11/17 16:45]
 */
public class BattleCollectService implements Service {

    private static Map<Integer, BattleCollectTemplate> battleCollectTemplateMap = new HashMap<>();
    private static Map<Integer, List<Integer>> stage2BattleCollectIds = new HashMap<>();

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.battleCollect_battleCollect);
        Map<Integer, BattleCollectTemplate> battleCollectTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            BattleCollectTemplate battleCollectTemplate = new BattleCollectTemplate();
            battleCollectTemplate.id = Integer.parseInt(map.get("id"));
            battleCollectTemplate.monsterList = StringExtUtil.string2List(map.get("monsterList"), ",", Integer.class);
            battleCollectTemplate.backupList = StringExtUtil.string2List(map.get("backupList"), ",", Integer.class);
            if(battleCollectTemplate.backupList.size() > TeamUnit.MAX_SUPPORT_UNIT_COUNT){
                throw new IllegalArgumentException("battleCollect_battleCollect backupList size is error");
            }
            battleCollectTemplate.battleScene = Integer.parseInt(map.get("battlescene"));
            battleCollectTemplate.type = Integer.parseInt(map.get("type"));
            battleCollectTemplate.sceneSkill = map.get("sceneSkill");
            battleCollectTemplate.MaxRound = Integer.parseInt(map.get("MaxRound"));
            battleCollectTemplate.MaxTime = Long.parseLong(map.get("MaxTime"));
            String teamSoulStr = map.get("teamSoul");
            if(!"-1".equals(teamSoulStr)){
                int[] ints = StringUtil.splitToIntArray(teamSoulStr, ",");
                if(ints.length == 2){
                    battleCollectTemplate.atkTeamSoul = ints[0];
                    battleCollectTemplate.defTeamSoul = ints[1];
                }else{
                    battleCollectTemplate.atkTeamSoul = ints[0];
                }
            }
            battleCollectTemplateMapTemp.put(battleCollectTemplate.id, battleCollectTemplate);
            if(!stage2BattleCollectIds.containsKey(battleCollectTemplate.type)){
                stage2BattleCollectIds.put(battleCollectTemplate.type, new ArrayList<>());
            }
            stage2BattleCollectIds.get(battleCollectTemplate.type).add(battleCollectTemplate.id);
        }
        battleCollectTemplateMap = battleCollectTemplateMapTemp;
    }

    public static Map<Integer, BattleCollectTemplate> getBattleCollectTemplateMap() {
        return battleCollectTemplateMap;
    }

    public static Map<Integer, List<Integer>> getStage2BattleCollectIds() {
        return stage2BattleCollectIds;
    }

    public static List<Attributes> getAttributes(int battleCollectId, int lv) {
        List<Attributes> attributesList = new ArrayList<>();
        BattleCollectTemplate battleCollectTemplate = battleCollectTemplateMap.get(battleCollectId);
        List<Integer> monsterList = battleCollectTemplate.monsterList;
        for (int i = 0; i < monsterList.size(); i++) {
            Integer monsterTemplateId = monsterList.get(i);
            if (monsterTemplateId == -1) {
                continue;
            }
            MonsterTemplate monsterTemplate = MonsterService.monsterTemplates.get(monsterTemplateId);
            if (monsterTemplate != null) {
                attributesList.add(monsterTemplate.getAttributes(lv));
            }
        }
        return attributesList;
    }

    /**
     * 生成怪物阵容
     *
     * @param battleCollectId 阵容ID
     */
    public static List<HeroUnit> createHeroUnits(int battleCollectId) {
        return createHeroUnits(battleCollectId, -1);
    }

    /**
     * 生成怪物阵容
     *
     * @param battleCollectIds 阵容ID
     */
    public static List<TeamUnit> createTeamUnits(AbstractStage stage, int lv, int... battleCollectIds) {
        List<TeamUnit> teamUnits = new ArrayList<>();
        for (int battleCollectId : battleCollectIds) {
            List<HeroUnit> heroUnits = createHeroUnits(battleCollectId, lv);
            TeamUnit teamUnit = new TeamUnit(stage.getNewId(), heroUnits);
            teamUnits.add(teamUnit);
        }
        return teamUnits;
    }

    /**
     * 生成怪物阵容带血量继承
     *
     * @param battleCollectIds 阵容ID
     */
    public static List<TeamUnit> createTeamUnitsWithHp(AbstractStage stage, int lv, Map<Integer, Long> hpMap, int... battleCollectIds) {
        List<TeamUnit> teamUnits = new ArrayList<>();
        for (int battleCollectId : battleCollectIds) {
            List<HeroUnit> heroUnits = createHeroUnits(battleCollectId, lv);
            List<HeroUnit> temp = new ArrayList<>();
            for (HeroUnit unit : heroUnits) {
                if(hpMap.containsKey(unit.getTid())){
                    long nowHp = hpMap.get(unit.getTid());
                    if(nowHp == 0){
                        continue;
                    }
                    unit.getAttributes().setValue(AttributeKey.当前生命值, nowHp);
                }
                temp.add(unit);
            }
            TeamUnit teamUnit = new TeamUnit(stage.getNewId(), temp);
            teamUnits.add(teamUnit);
        }
        return teamUnits;
    }

    /**
     * 找到怪物组里的Boss
     *
     * @param teamUnitList 阵容信息
     * @return Boss的HeroUnit 如果有多个Boss 返回第一个，如果无返回Null
     */
    public static HeroUnit findBossUnit(List<TeamUnit> teamUnitList) {
        for (TeamUnit defTeam : teamUnitList) {
            for (HeroUnit defUnit : defTeam.getUnits()) {
                int tid = defUnit.getTid();
                MonsterTemplate monsterTemplate = MonsterService.monsterTemplates.get(tid);
                if (Objects.nonNull(monsterTemplate) && monsterTemplate.isBoss()) {
                    return defUnit;
                }
            }
        }
        return null;
    }

    /**
     * 找到怪物组里的Boss
     *
     * @param battleCollectId 阵容组ID
     * @return monsterId 如果有多个Boss 返回第一个，如果无返回-1
     */
    public static int findBossId(int battleCollectId) {
        BattleCollectTemplate battleCollectTemplate = battleCollectTemplateMap.get(battleCollectId);
        for (Integer monsterId : battleCollectTemplate.monsterList) {
            MonsterTemplate monsterTemplate = MonsterService.monsterTemplates.get(monsterId);
            if (Objects.nonNull(monsterTemplate) && monsterTemplate.isBoss()) {
                return monsterTemplate.id;
            }
        }
        return -1;
    }

    /**
     * 生成怪物阵容 带指定等级
     *
     * @param battleCollectId 阵容ID
     * @param lv              通过指定等级获取属性
     */
    public static List<HeroUnit> createHeroUnits(int battleCollectId, int lv) {
        List<HeroUnit> heroUnitList = new ArrayList<>();
        BattleCollectTemplate battleCollectTemplate = battleCollectTemplateMap.get(battleCollectId);
        List<Integer> monsterList = battleCollectTemplate.monsterList;
        for (int i = 0; i < monsterList.size(); i++) {
            Integer monsterTemplateId = monsterList.get(i);
            if (monsterTemplateId == -1) {
                continue;
            }
            MonsterTemplate monsterTemplate = MonsterService.monsterTemplates.get(monsterTemplateId);
            if (monsterTemplate != null) {
                HeroUnit heroUnit = monsterTemplate.genUnit(lv);
                heroUnit.setPosIndex(i);
                heroUnitList.add(heroUnit);
            }
        }
        return heroUnitList;
    }

    public static List<HeroUnit> createHeroBackUnits(int battleCollectId, int lv) {
        List<HeroUnit> heroUnitList = new ArrayList<>();
        BattleCollectTemplate battleCollectTemplate = battleCollectTemplateMap.get(battleCollectId);
        List<Integer> monsterList = battleCollectTemplate.backupList;
        for (int i = 0; i < monsterList.size(); i++) {
            Integer monsterTemplateId = monsterList.get(i);
            if (monsterTemplateId == -1) {
                continue;
            }
            MonsterTemplate monsterTemplate = MonsterService.monsterTemplates.get(monsterTemplateId);
            if (monsterTemplate != null) {
                HeroUnit heroUnit = monsterTemplate.genUnit(lv);
                heroUnit.setPosIndex(i);
                heroUnitList.add(heroUnit);
            }
        }
        return heroUnitList;
    }

    @Override
    public boolean isWorldServer() {
        return true;
    }
}

package com.gy.server.game.battleCollect.template;

import java.util.ArrayList;
import java.util.List;

import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.monster.MonsterService;
import com.gy.server.game.monster.MonsterTemplate;

/**
 * <AUTHOR> - [Created on 2022/11/17 16:46]
 */
public class BattleCollectTemplate {

    public int id;
    /**
     * 怪物列表
     */
    public List<Integer> monsterList = new ArrayList<>();
    public List<Integer> backupList = new ArrayList<>();

    /**
     * 战斗场景ID
     */
    public int battleScene;

    /**
     * 战斗类型
     */
    public int type;

    public String sceneSkill;

    public int MaxRound;
    //最大战斗时长秒
    public long MaxTime;

    public int atkTeamSoul;
    public int defTeamSoul;

    public List<HeroUnit> genHeroUnits(){
        List<HeroUnit> result = new ArrayList<>();
        for (int i = 0; i < monsterList.size(); i++) {
            Integer monsterTemplateId = monsterList.get(i);
            if (monsterTemplateId == -1) {
                continue;
            }
            MonsterTemplate monsterTemplate = MonsterService.monsterTemplates.get(monsterTemplateId);
            if(monsterTemplate != null){
                HeroUnit heroUnit = monsterTemplate.genUnit(Math.max(monsterTemplate.level, 0));
                heroUnit.setPosIndex(i);
                result.add(heroUnit);
            }
        }
        return result;

    }


    public List<HeroUnit> genHeroBackUnits(){
        List<HeroUnit> result = new ArrayList<>();
        for (int i = 0; i < backupList.size(); i++) {
            Integer monsterTemplateId = backupList.get(i);
            if (monsterTemplateId == -1) {
                continue;
            }
            MonsterTemplate monsterTemplate = MonsterService.monsterTemplates.get(monsterTemplateId);
            if(monsterTemplate != null){
                HeroUnit heroUnit = monsterTemplate.genUnit(Math.max(monsterTemplate.level, 0));
                heroUnit.setPosIndex(i);
                result.add(heroUnit);
            }
        }
        return result;

    }

    public List<HeroUnit> genHeroBackUnits(int level){
        List<HeroUnit> result = new ArrayList<>();
        for (int i = 0; i < backupList.size(); i++) {
            Integer monsterTemplateId = backupList.get(i);
            if (monsterTemplateId == -1) {
                continue;
            }
            MonsterTemplate monsterTemplate = MonsterService.monsterTemplates.get(monsterTemplateId);
            if(monsterTemplate != null){
                HeroUnit heroUnit = monsterTemplate.genUnit(level > 0 ? level : Math.max(monsterTemplate.level, 0));
                heroUnit.setPosIndex(i);
                result.add(heroUnit);
            }
        }
        return result;

    }

    public List<HeroUnit> genHeroUnits(int level){
        List<HeroUnit> result = new ArrayList<>();
        for (int i = 0; i < monsterList.size(); i++) {
            Integer monsterTemplateId = monsterList.get(i);
            if (monsterTemplateId == -1) {
                continue;
            }
            MonsterTemplate monsterTemplate = MonsterService.monsterTemplates.get(monsterTemplateId);
            if(monsterTemplate != null){
                HeroUnit heroUnit = monsterTemplate.genUnit(level > 0 ? level : Math.max(monsterTemplate.level, 0));
                heroUnit.setPosIndex(i);
                result.add(heroUnit);
            }
        }
        return result;

    }

}

package com.gy.server.game.account.register;

import com.google.protobuf.ByteString;
import com.gy.server.common.gateway.GateNode;
import com.gy.server.common.gateway.net.GateNodeConnection;
import com.gy.server.core.thread.ForkJoinSyncTask;
import com.gy.server.game.account.Account;
import com.gy.server.game.account.login.LoginHelper;
import com.gy.server.game.db.DbAssistant;
import com.gy.server.game.db.DbManager;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.log.constant.ClientEventType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.BaseInfoSyncManager;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerData;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.LockUtil;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.redis.key.BaseRedisKey;
import com.ttlike.server.tl.baselib.serialize.RedisAssistant;
import com.ttlike.server.tl.baselib.serialize.player.FaceDataDb;

/**
 * 创角任务
 *
 * <AUTHOR> - [Created on 2022/5/30 14:26]
 */
public class RegisterTask implements ForkJoinSyncTask {

    private boolean registerResult = true;

    private final static String registerLockKeyPrefix = "register#";

    private final PbProtocol.RegisterCheckReq req;
    private final PbProtocol.LoginCheckReq loginCheckReq;

    private final GateNode gateNode;
    private final Account account;

    private final long clientId;

    private String roleInfo;
    private boolean playerIdIsError = false;

    public RegisterTask(PbProtocol.RegisterCheckReq registerReq, GateNode gateNode, long clientId) {
        this.req = registerReq;
        this.loginCheckReq = registerReq.getCheckParam();
        this.account = new Account(loginCheckReq, loginCheckReq.getIp());
        this.gateNode = gateNode;
        this.clientId = clientId;
    }

    @Override
    public void execute() {
        if (!registerResult) {
            return;
        }
        //检测玩家id是否异常
        if(playerIdIsError){
            gateNode.send(clientId, PtCode.REGISTER_SERVER, PbProtocol.RegisterRst.newBuilder().setResult(Text.genServerRstInfo(Text.服务器数据库异常)).build());
            return;
        }else{
            gateNode.send(clientId, PtCode.REGISTER_SERVER, PbProtocol.RegisterRst.newBuilder().setResult(Text.genOkServerRstInfo()).build());
        }
        //创角完登录
        if (req.getAfterLogin()) {
            account.setLoginCheckResult(true);
            LoginHelper.login(clientId, gateNode, account);
        }
    }

    @Override
    public void asyncExecute() {
        String name = req.getName();
        // 捏脸数据
        ByteString faceData = req.getFaceData();
        String accountId = loginCheckReq.getAccountId();
        int accountType = loginCheckReq.getAccountType();
        int serverNum = loginCheckReq.getServerNum();
        synchronized (LockUtil.getLockKey(registerLockKeyPrefix + accountId + accountType + serverNum)) {
            // 判断已经创建角色
            Long playerIdByAccount = DbAssistant.getPlayerIdByAccount(accountId, accountType, serverNum);
            if (playerIdByAccount != null) {
                sendRegisterResultFail(Text.已经创角过角色不能重复创建);
                registerResult = false;
                return;
            }
            Long namePlayerId = DbAssistant.getPlayerIdByName(name);
            if (namePlayerId != null) {
                sendRegisterResultFail(Text.角色名已被注册);
                registerResult = false;
                return;
            }
            // 创建PlayerData并保存
            PlayerData playerData = new PlayerData();
            playerData.setLevel(1);
            playerData.setName(name);
            playerData.setSex(req.getSex());
            playerData.setProfession(req.getProfession());
            playerData.updateAccountInfo(account);
            // 捏脸数据更新
            FaceDataDb faceDataDb = new FaceDataDb();
            faceDataDb.setFaceData(faceData.toByteArray());
            playerData.getPlayerBlob().setFaceDataDb(faceDataDb);

            DbManager.add(playerData.getServerNum(),playerData);

            afterRegister(playerData);
            GameLogger.clientEvent(req, ClientEventType.注册结果注册成功);
            TLBase.getInstance().getRedisAssistant().setAdd(BaseRedisKey.Commons.AREA_PIDS.getRedisKey(serverNum), String.valueOf(playerData.getPlayerId()));

        }


    }

    /**
     * 注册之后的操作
     */
    private void afterRegister(PlayerData playerData) {

        //检查玩家id是否正确
        if(playerData.getPlayerId() < GlobalDataManager.SERVER_PREFIX){
            playerIdIsError = true;
        }

        long playerId = playerData.getPlayerId();
        long serverId = playerData.getServerNum();
        // 注册到redis
        RedisAssistant redisAssistant = TLBase.getInstance().getRedisAssistant();
        redisAssistant.hashPut(BaseRedisKey.PlayerKey.Account_2_PID.getRedisKey(loginCheckReq.getAccountId(), loginCheckReq.getAccountType()), String.valueOf(serverId), String.valueOf(playerId));
        // 添加minPlayer
        Player player = new Player(playerData);
        player.init(true);
        PlayerManager.updateMiniPlayer(player);
        BaseInfoSyncManager.sync(player.getPlayerId());

    }

    @Override
    public long getDelayMillis() {
        return -10 * DateTimeUtil.MillisOfSecond;
    }

    /**
     * 发送登录结果失败
     */
    private void sendRegisterResultFail(int errMsgId) {
        GateNodeConnection connection = gateNode.getConnection();
        if (connection != null) {

            PbProtocol.RegisterRst.Builder rst = PbProtocol.RegisterRst.newBuilder().setResult(Text.genServerRstInfo(errMsgId));

            connection.send(clientId, PtCode.REGISTER_SERVER, rst.build());
        }

    }

    public String getRoleInfo() {
        return roleInfo;
    }

    public void setRoleInfo(String roleInfo) {
        this.roleInfo = roleInfo;
    }
}

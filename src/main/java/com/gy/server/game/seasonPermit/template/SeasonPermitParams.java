package com.gy.server.game.seasonPermit.template;

import com.gy.server.game.drop.RewardTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> -[Create on 2020/5/25]
 */
public class SeasonPermitParams {

    /**
     * 经验表id
     */
    public int expId;


    /**
     * 前N次消耗
     */
    public RewardTemplate buyLevelCost;
    /**
     * 普通赛季通行证
     */
    public int payid;
    public int payidEpic;
    public int payidEpic2;
    //每周经验上限
    public long weeklyMaxExp;

    //奖励
    public List<RewardTemplate> epicExps = new ArrayList<>();


}

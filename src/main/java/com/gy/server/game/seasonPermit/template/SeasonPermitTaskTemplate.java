package com.gy.server.game.seasonPermit.template;

import com.gy.server.game.drop.RewardTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * 赛季通行证任务
 * @author: gbk
 * @date: 2024-08-05 15:51
 */
public class SeasonPermitTaskTemplate {

    public int id;

    public int seasonId;

    public int goalId;

    public List<RewardTemplate> exp = new ArrayList<>();

    //重置频率
    public int reset;

}
package com.gy.server.game.seasonPermit;

import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.constant.ConstantService;
import com.gy.server.game.constant.ConstantType;
import com.gy.server.game.currency.Currency;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.exp.ExpService;
import com.gy.server.game.function.Function;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.mail.MailManager;
import com.gy.server.game.mail.MailType;
import com.gy.server.game.pay.*;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModel;
import com.gy.server.game.player.event.PlayerEvent;
import com.gy.server.game.player.event.PlayerEventHandler;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.player.goal.PlayerGoalModel;
import com.gy.server.game.seasonPermit.template.SeasonPermitParams;
import com.gy.server.game.seasonPermit.template.SeasonPermitRewardTemplate;
import com.gy.server.game.seasonPermit.template.SeasonPermitTaskTemplate;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbSync;
import com.gy.server.utils.MathUtil;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.serialize.player.PlayerBlobDb;
import com.ttlike.server.tl.baselib.serialize.seasonPermit.SeasonPermitModelDb;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.util.*;

import static com.gy.server.game.player.event.PlayerEventType.*;

/**
 * <AUTHOR> -[Create on 2020/5/25]
 */
public class PlayerSeasonPermitModel extends PlayerModel implements PlayerEventHandler, ReceiptFinishHandler {

    private static final PlayerEventType[] eventTypes = new PlayerEventType[]{login, finishReceipt
            , day0Refresh, day5Refresh, levelUp, passMission, goalFinish, goalUpdate};

    /**
     * 赛季通行证购买情况
     */
    private Map<Integer, Set<Integer>> seasonBuyInfos = new HashMap<>();
    /**
     * 已领取普通奖励的等级集合
     */
    private Map<Integer, Set<Integer>> receiveCommonRewardLevels = new HashMap<>();
    /**
     * 已领取精英奖励的等级集合
     */
    private Map<Integer, Set<Integer>> receiveEliteRewardLevels = new HashMap<>();
    /**
     * Key：赛季Id Value：等级
     */
    private Map<Integer, Integer> seasonRecord = new HashMap<>();

    /**
     * 当前赛季Id
     */
    private int nowSeason = -1;

    /**
     * 当前的经验
     */
    private long exp = 0;

    /**
     * 当前等级
     */
    private int level = 0;

    /**
     * 本赛季开始时间
     */
    private long startTime;

    /**
     * 本赛季结束时间
     */
    private long endTime;


    /**
     * 赛季通行证起始天数，大纪元日
     * （原先是不会变的，现在需求变更，如果出现delay，新赛季数据延期，或者玩家进入最后一赛季，会调整这个时间，所以已经不是单纯的第一赛季开启天数了）
     */
    private long openDays;

    /**
     * 发送了奖励补发奖励
     */
    private boolean isSendFinishMail = false;

    /**
     * 任务领取情况
     * key ： goalId  value：是否领取
     */
    private Map<Integer, Boolean> taskReceiveInfos = new HashMap<>();
    //本周获得经验
    private long curWeekAddExp;

    public PlayerSeasonPermitModel(Player player) {
        super(player);
    }

    @Override
    protected void loadData(PlayerBlobDb playerBlob) {
        SeasonPermitModelDb seasonPermitModelDb = playerBlob.getSeasonPermitModelDb();
        if(Objects.nonNull(seasonPermitModelDb)){
            seasonPermitModelDb.getSeasonBuyInfos().forEach(info -> seasonBuyInfos.put(info.getIntKey(), info.getSetValue()));
            seasonPermitModelDb.getReceiveCommonRewardLevels().forEach(info -> receiveCommonRewardLevels.put(info.getIntKey(), info.getSetValue()));
            seasonPermitModelDb.getReceiveEliteRewardLevels().forEach(info -> receiveEliteRewardLevels.put(info.getIntKey(), info.getSetValue()));
            seasonRecord.putAll(seasonPermitModelDb.getSeasonRecord());
            nowSeason = seasonPermitModelDb.getNowSeason();
            exp = seasonPermitModelDb.getExp();
            level = seasonPermitModelDb.getLevel();
            startTime = seasonPermitModelDb.getStartTime();
            endTime = seasonPermitModelDb.getEndTime();
            openDays = seasonPermitModelDb.getOpenDays();
            isSendFinishMail = seasonPermitModelDb.isSendFinishMail();
            curWeekAddExp = seasonPermitModelDb.getCurWeekAddExp();
            taskReceiveInfos = seasonPermitModelDb.getTaskReceiveInfos();
        }
    }

    @Override
    protected void saveData(PlayerBlobDb playerBlob) {
        SeasonPermitModelDb seasonPermitModelDb = new SeasonPermitModelDb();
        seasonBuyInfos.forEach((k, v) -> seasonPermitModelDb.getSeasonBuyInfos().add(new SeasonPermitModelDb.IntSetEntry(k, v)));
        receiveCommonRewardLevels.forEach((k, v) -> seasonPermitModelDb.getReceiveCommonRewardLevels().add(new SeasonPermitModelDb.IntSetEntry(k, v)));
        receiveEliteRewardLevels.forEach((k, v) -> seasonPermitModelDb.getReceiveEliteRewardLevels().add(new SeasonPermitModelDb.IntSetEntry(k, v)));
        seasonPermitModelDb.getSeasonRecord().putAll(seasonRecord);
        seasonPermitModelDb.setNowSeason(nowSeason);
        seasonPermitModelDb.setExp(exp);
        seasonPermitModelDb.setLevel(level);
        seasonPermitModelDb.setStartTime(startTime);
        seasonPermitModelDb.setEndTime(endTime);
        seasonPermitModelDb.setOpenDays(openDays);
        seasonPermitModelDb.setSendFinishMail(isSendFinishMail);
        seasonPermitModelDb.setCurWeekAddExp(curWeekAddExp);
        seasonPermitModelDb.setTaskReceiveInfos(taskReceiveInfos);

        playerBlob.setSeasonPermitModelDb(seasonPermitModelDb);
    }


    public void addBuy(int goodsId){
        if(!seasonBuyInfos.containsKey(nowSeason)){
            seasonBuyInfos.put(nowSeason, new HashSet<>());
        }
        seasonBuyInfos.get(nowSeason).add(goodsId);
    }

    public Set<Integer> getBuyGoodsIds(){
        return seasonBuyInfos.getOrDefault(nowSeason, new HashSet<>());
    }

    public Set<Integer> getNowSeasonReceiveNormalRewardLevels() {
        if (nowSeason != -1 && receiveCommonRewardLevels.get(nowSeason) == null) {
            receiveCommonRewardLevels.put(nowSeason, new HashSet<>());
        }
        return receiveCommonRewardLevels.get(nowSeason);
    }

    public Set<Integer> getNowSeasonReceiveEliteRewardLevels() {
        if (nowSeason != -1 && receiveEliteRewardLevels.get(nowSeason) == null) {
            receiveEliteRewardLevels.put(nowSeason, new HashSet<>());
        }
        return receiveEliteRewardLevels.get(nowSeason);
    }

    public void setNowSeason(int nowSeason) {
        this.nowSeason = nowSeason;
    }

    public Map<Integer, Set<Integer>> getReceiveCommonRewardLevels() {
        return receiveCommonRewardLevels;
    }

    public Map<Integer, Set<Integer>> getReceiveEliteRewardLevels() {
        return receiveEliteRewardLevels;
    }

    public Map<Integer, Integer> getSeasonRecord() {
        return seasonRecord;
    }

    public int getNowSeason() {
        return nowSeason;
    }

    public long getExp() {
        return exp;
    }

    public int getLevel() {
        return level;
    }

    public long getStartTime() {
        return startTime;
    }

    public long getEndTime() {
        return endTime;
    }


    public long getOpenDays() {
        return openDays;
    }

    public void setOpenDays(long openDays) {
        this.openDays = openDays;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public void initSeasonDate(int newSeason) {
        Player player = getPlayer();
        //清空赛季所有经验
        player.decreaseNumeric(Currency.seasonExp, player.getCurrencyModel().getCurrency(Currency.seasonExp), BehaviorType.SeasonPermitReset);

        //重置等级经验
        level = 1;
        exp = 0;

        //更改赛季
        nowSeason = newSeason;

        //设置新赛季的开始结束时间
        Pair<Integer, Integer> time = SeasonPermitService.getSeasonTime().get(newSeason);
        int refreshHour = CommonUtils.getRefreshTimeHour();
        LocalDateTime openDay = DateTimeUtil.toLocalDateTime(DateUtils.MILLIS_PER_DAY * openDays).withHour(refreshHour).withMinute(0).withSecond(0).withNano(0);
        startTime = DateTimeUtil.toMillis(openDay.plusDays(time.getLeft()));
        endTime = DateTimeUtil.toMillis(openDay.plusDays(time.getRight()));
        //重新注册任务
        List<SeasonPermitTaskTemplate> taskTemplates = new ArrayList<>(SeasonPermitService.getTaskTemplates(nowSeason).values());
        PlayerGoalModel goalModel = player.getGoalModel();
        //清除上赛季goal
        hadRegisterGoalIds.forEach(id -> goalModel.remove(id));
        hadRegisterGoalIds.clear();
        finishGoalIds.clear();
        //重新注册goal
        taskTemplates.forEach(t -> {
            hadRegisterGoalIds.add(t.goalId);
            goalModel.register(t.goalId);
        });
    }
    //已经注册goalid
    private Set<Integer> hadRegisterGoalIds = new HashSet<>();
    //完成goalId
    private Set<Integer> finishGoalIds = new HashSet<>();


    public boolean addLevel(int addLevel) {
        int maxLevel = SeasonPermitService.getSeasonRewardMap().get(nowSeason).size();
        if (level + addLevel > maxLevel) {
            return false;
        }
        level += addLevel;
        return true;
    }

    public long addExp(long addExp) {
        SeasonPermitParams params = SeasonPermitService.getParamsMap().get(nowSeason);
        if(params == null){
            return exp;
        }

        long canAddExp = params.weeklyMaxExp - curWeekAddExp;
        addExp = Math.min(canAddExp, addExp);
        if(addExp > 0){
            curWeekAddExp += addExp;
            long oldExp = exp;
            exp = addExp + exp;
            int i = 100;
            while (i-- > 0) {
                long levelUpExp = ExpService.getExp(params.expId, level);
                if (this.exp >= levelUpExp) {
                    //升级
                    if (!addLevel(1)) {
                        //升级失败回退经验
                        exp -= addExp;
                        break;
                    }
                    if (oldExp > levelUpExp) {
                        exp = oldExp - levelUpExp;
                    } else if (oldExp == levelUpExp) {
                        this.exp = 0;
                    } else {
                        this.exp = exp - levelUpExp;
                    }
                } else {
                    break;
                }

            }
        }

        return exp;
    }


    public PbSync.SeasonPermitSyncData.Builder genSeasonPermitSyncData() {
        PbSync.SeasonPermitSyncData.Builder data = PbSync.SeasonPermitSyncData.newBuilder();
        Player player = getPlayer();

        data.setNowSeason(nowSeason);
        if(nowSeason > 0){
            data.setExp(exp);
            data.setLevel(level);
            data.setCurWeekAddExp(curWeekAddExp);
            if (receiveEliteRewardLevels.get(nowSeason) != null) {
                data.addAllReceiveEliteRewardLevels(receiveEliteRewardLevels.get(nowSeason));
            }
            if (receiveCommonRewardLevels.get(nowSeason) != null) {
                data.addAllReceiveCommonRewardLevels(receiveCommonRewardLevels.get(nowSeason));
            }
            data.addAllHadBuyGoodsIds(getBuyGoodsIds());
            data.setStartTime(startTime);
            data.setEndTime(endTime);

            SeasonPermitParams template = SeasonPermitService.getParamsMap().get(nowSeason);
            if (template != null && player.getAccount() != null) {
                PayItem payItem = PayService.getPayItem(getPlayer().getAccount().payChannel, template.payid);
                if (payItem != null) {
                    data.setPayItem(payItem.genPayItem(player));
                }
                PayItem epicPayItem = PayService.getPayItem(getPlayer().getAccount().payChannel, template.payidEpic);
                if (epicPayItem != null) {
                    data.setPayItemEpic(epicPayItem.genPayItem(player));
                }
                PayItem epicPayItem2 = PayService.getPayItem(getPlayer().getAccount().payChannel, template.payidEpic2);
                if (epicPayItem2 != null) {
                    data.setPayItemEpic2(epicPayItem2.genPayItem(player));
                }
            }

            PbSync.SeasonPermitSyncData.SeasonPermitTaskInfo.Builder taskInfo = PbSync.SeasonPermitSyncData.SeasonPermitTaskInfo.newBuilder();
            PlayerGoalModel goalModel = getPlayer().getGoalModel();
            for (SeasonPermitTaskTemplate taskTemplate : SeasonPermitService.getTaskTemplates(nowSeason).values()) {
                taskInfo.clear();
                int goalId = taskTemplate.goalId;
                taskInfo.setGoalId(goalId);
//                long[] progress = goalModel.getProcess(goalId);
//                taskInfo.setCurProgress(progress[0]);
//                taskInfo.setTotalProgress(progress[1]);
                taskInfo.setStatus(taskReceiveInfos.containsKey(goalId) ? (taskReceiveInfos.get(goalId) ? 2 : 1) : 0);
                data.addTaskInfos(taskInfo.build());
            }
        }

        return data;
    }

    /**
     * 以防万一
     */
    public void supplement() {

    }

    public void seasonOver() {
        //换赛季了
        if (nowSeason == -1 || endTime < ServerConstants.getCurrentTimeMillis()) {
            long nowDays = ServerConstants.getCurrentTimeLocalDateTime().toLocalDate().toEpochDay();
            //根据时间计算出的处于哪个赛季
            int newSeason = SeasonPermitService.getNowSeason(getPlayer(), nowDays);
            if (newSeason != 0) {
                GameLogger.seasonPassLog(getPlayer(), nowSeason, level);
                //要开新赛季
                if (nowSeason < newSeason) {
                    int[] seasonIds = SeasonPermitService.getSeasonTime().keySet().stream().mapToInt(Integer::valueOf).toArray();
                    //最后一个赛季
                    int lastSeasonId = MathUtil.max(seasonIds);
                    //如果是最后一个赛季，并且玩家按照现在的进度，开启后不是第一天了，修正一下，改为第一天
                    if (newSeason == lastSeasonId) {
                        Pair<Integer, Integer> time = SeasonPermitService.getSeasonTime().get(newSeason);
                        long newStartDay = openDays + time.getLeft();
                        //现在已经超过了赛季开启的时间，那调整到赛季开启时间去
                        if (nowDays > newStartDay) {
                            openDays = openDays + (nowDays - newStartDay);
                        }
                    }

                    initNextSeason(newSeason);
                    GameLogger.seasonPassStartLog(getPlayer(), nowSeason);
                } else if (nowSeason == newSeason && !isSendFinishMail) {//最后一赛季
                    sendMail();
                    isSendFinishMail = true;
                }
            }
        }
    }


    public void refreshTask(){
        LocalDateTime currentTimeLocalDateTime = ServerConstants.getCurrentTimeLocalDateTime();
        if(currentTimeLocalDateTime.getDayOfWeek() == DayOfWeek.MONDAY){
            //每周刷新任务
            PlayerGoalModel goalModel = getPlayer().getGoalModel();
            for (SeasonPermitTaskTemplate taskTemplate : SeasonPermitService.getTaskTemplates(nowSeason).values()) {
                if(taskTemplate.reset == 1){
                    goalModel.remove(taskTemplate.goalId);
                    goalModel.register(taskTemplate.goalId);
                    hadRegisterGoalIds.add(taskTemplate.goalId);
                    taskReceiveInfos.remove(taskTemplate.goalId);
                }
            }
            curWeekAddExp = 0;
        }

    }

    public void initNextSeason(int newSeason) {
        //记录当前赛季等级
        if (nowSeason != -1) {
            seasonRecord.put(nowSeason, level);
            if (isSendFinishMail) {
                isSendFinishMail = false;
            } else {
                sendMail();
            }
        }

        initSeasonDate(newSeason);
    }

    public boolean isBuyElite(){
        return !getBuyGoodsIds().isEmpty();
    }

    private void sendMail() {
        List<Reward> rewards = new ArrayList<>();
        Map<Integer, SeasonPermitRewardTemplate> rewardTemplateMap = SeasonPermitService.getSeasonRewardMap().get(nowSeason);
        boolean isBuyElite = isBuyElite();
        for (int i = 1; i <= level; i++) {
            if (receiveCommonRewardLevels.get(nowSeason) == null || !receiveCommonRewardLevels.get(nowSeason).contains(i)) {
                rewards.addAll(Reward.templateCollectionToReward(rewardTemplateMap.get(i).normalRewards));
                receiveCommonRewardLevels.computeIfAbsent(nowSeason, obj -> new HashSet<>()).add(i);
            }
            if ((isBuyElite && receiveEliteRewardLevels.get(nowSeason) == null) || (isBuyElite && !receiveEliteRewardLevels.get(nowSeason).contains(i))) {
                rewards.addAll(Reward.templateCollectionToReward(rewardTemplateMap.get(i).eliteRewards));
                receiveEliteRewardLevels.computeIfAbsent(nowSeason, obj -> new HashSet<>()).add(i);
            }
        }

        if (rewards.size() > 0) {
            Reward.merge(rewards);
            MailType mailType = MailType.season_permit_end;
            MailManager.sendMail(mailType,
                    getPlayerId(),
                    Text.genText(mailType.getTitleId()).build(),
                    Text.genText(mailType.getContentId()).build(),
                    ServerConstants.getCurrentTimeMillis(),
                    rewards
            );
        }
    }

    @Override
    public PlayerEventType[] getEventTypes() {
        return eventTypes;
    }

    @Override
    public void handle(PlayerEvent event) {
        if (Function.seasonPermit.isOpen(getPlayer())) {
            switch (event.getEventType()) {
                case levelUp:
                case passMission:
                case login: {
                    if (nowSeason == -1) {
                        openDays = ServerConstants.getCurrentTimeLocalDateTime().toLocalDate().toEpochDay();
                        seasonOver();
                        getPlayer().dataSyncModule.syncSeasonPermit();
                    }
                    break;
                }
                case day5Refresh: {
                    if (nowSeason > 0) {
                        refreshTask();
                        seasonOver();
                        getPlayer().dataSyncModule.syncSeasonPermit();
                    }

                    break;
                }
                case finishReceipt: {
                    Receipt receipt = event.getParam(0);
                    SeasonPermitParams params = SeasonPermitService.getParamsMap().get(nowSeason);
                    if (params != null) {
                        if (receipt.getGoodsId() == params.payid) {
                            addBuy(receipt.getGoodsId());
                            getPlayer().dataSyncModule.syncSeasonPermit();
                        } else if (receipt.getGoodsId() == params.payidEpic
                            || receipt.getGoodsId() == params.payidEpic2) {
                            addBuy(receipt.getGoodsId());
                            //发送道具
                            SeasonPermitParams seasonPermitParams = SeasonPermitService.getParamsMap().get(nowSeason);
                            List<Reward> rewards = Reward.templateCollectionToReward(seasonPermitParams.epicExps);
                            Reward.add(rewards, getPlayer(), BehaviorType.SeasonPermitBuy);
//                            MailType mailType = MailType.season_permit;
//                            MailManager.sendMail(mailType,
//                                    getPlayerId(),
//                                    Text.genText(mailType.getTitleId()).build(),
//                                    Text.genText(mailType.getContentId()).build(),
//                                    Reward.templateCollectionToReward(seasonPermitParams.epicExps)
//                            );
                            getPlayer().dataSyncModule.syncSeasonPermit();
                        }
                    }
                    break;
                }
                case goalFinish: {
                    if(nowSeason > 0){
                        int tid = event.getParam(0);
                        SeasonPermitTaskTemplate taskTemplate = SeasonPermitService.getTaskTemplates(nowSeason).get(tid);
                        if(Objects.nonNull(taskTemplate) && !taskReceiveInfos.containsKey(tid)){
                            taskReceiveInfos.put(tid, false);
                            getPlayer().dataSyncModule.syncSeasonPermit();
                        }
                    }
                    break;
                }

                case goalUpdate:{
                    if(nowSeason > 0){
                        int goalId = event.getParam(0);
                        Map<Integer, SeasonPermitTaskTemplate> taskTemplates = SeasonPermitService.getTaskTemplates(nowSeason);
                        for (SeasonPermitTaskTemplate template : taskTemplates.values()) {
                            if(template.goalId == goalId){
                                getPlayer().dataSyncModule.syncSeasonPermit();
                                return;
                            }
                        }
                    }
                    break;
                }
            }
        }

    }

    public Map<Integer, Boolean> getTaskReceiveInfos() {
        return taskReceiveInfos;
    }

    public void setTaskReceiveInfos(Map<Integer, Boolean> taskReceiveInfos) {
        this.taskReceiveInfos = taskReceiveInfos;
    }

    @Override
    public ReceiptCheckResult orderCheck(Player player, int goodsId, List<String> extParams) {
        /** 实际做了再来修改
         * PlayerSeasonPermitModel seasonPermitModel = player.getSeasonPermitModel();
         *             int nowSeason = seasonPermitModel.getNowSeason();
         *
         *             long leftEnd = seasonPermitModel.getEndTime() - ServerConstants.getCurrentTimeMillis();
         *             if (leftEnd > 0) {
         *                 if (leftEnd < 5 * DateTimeUtil.MillisOfMinute) {
         *                     rst.setResult(Text.genServerRstInfo(Text.当前赛季将要结束));
         *                     break logic;
         *                 }
         *             } else {
         *                 rst.setResult(Text.genServerRstInfo(Text.本季度赛季通行证已结束));
         *                 break logic;
         *             }
         *
         *             //检查赛季通行证是否存在
         *             SeasonPermitParams seasonPermitParams = SeasonPermitService.getParamsMap().get(nowSeason);
         *             if(seasonPermitParams.payid != goodsId
         *                     && seasonPermitParams.payidEpic != goodsId
         *                     && seasonPermitParams.payidEpic2 != goodsId){
         *                 rst.setResult(Text.genServerRstInfo(Text.参数异常));
         *                 break logic;
         *             }
         *             //检查购买情况
         *             Set<Integer> buyGoodsIds = seasonPermitModel.getBuyGoodsIds();
         *             if(buyGoodsIds.contains(goodsId) ||
         *                     buyGoodsIds.contains(seasonPermitParams.payidEpic) ||
         *                     buyGoodsIds.contains(seasonPermitParams.payidEpic2)){
         *                 rst.setResult(Text.genServerRstInfo(Text.本赛季已经购买请勿重复购买));
         *                 break logic;
         *             }
         *
         *             if(buyGoodsIds.contains(seasonPermitParams.payid) && goodsId == seasonPermitParams.payidEpic){
         *                 goodsId = seasonPermitParams.payidEpic2;
         *             }
         *
         *             PayItem payItem = PayService.getPayItem(player.getAccount().payChannel, goodsId);
         *             if (payItem == null) {
         *                 //商品不存在
         *                 rst.setResult(Text.genServerRstInfo(Text.充值商品不存在));
         *                 break logic;
         *             }
         */
        return ReceiptCheckResult.failed(Text.参数异常);
    }

    @Override
    public ReceiptFinishResult finishReceipt(Player player, int goodsId, List<String> extParams) {
        return null;//TODO 待需求确认
    }

    @Override
    public PbProtocol.PayCreateReceiptReq.PayFunction getPayFunction() {
        return null;//TODO 待需求确认
    }
}

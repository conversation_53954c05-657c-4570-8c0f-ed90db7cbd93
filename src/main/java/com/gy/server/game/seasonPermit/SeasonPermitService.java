package com.gy.server.game.seasonPermit;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.seasonPermit.template.SeasonPermitParams;
import com.gy.server.game.seasonPermit.template.SeasonPermitRewardTemplate;
import com.gy.server.game.seasonPermit.template.SeasonPermitTaskTemplate;
import com.gy.server.game.service.Service;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.MathUtil;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> -[Create on 2020/5/25]
 */
public class SeasonPermitService extends PlayerPacketHandler implements Service {

    /**
     * Key：赛季id Key：level Value：奖励模板
     */
    private static Map<Integer, Map<Integer, SeasonPermitRewardTemplate>> seasonRewardMap = new HashMap<>();

    /**
     * Key：赛季id Value：参数配置
     */
    private static Map<Integer, SeasonPermitParams> paramsMap = new HashMap<>();

    /**
     * Key：赛季id left：开始天数 right：结束天数
     */
    private static Map<Integer, Pair<Integer, Integer>> seasonTime = new HashMap<>();
    private static Map<Integer, Map<Integer, SeasonPermitTaskTemplate>> taskTemplates = new HashMap<>();

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.seasonPermit_seasonPermit);
        Map<Integer, Map<Integer, SeasonPermitRewardTemplate>> temp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            SeasonPermitRewardTemplate template = new SeasonPermitRewardTemplate();

            temp.computeIfAbsent(Integer.parseInt(map.get("seasonId")), value -> new HashMap<>())
                    .put(Integer.parseInt(map.get("level")), template);

            template.level = Integer.parseInt(map.get("level"));
            template.normalRewards = RewardTemplate.readListFromText(map.get("commonreward"));
            template.eliteRewards = RewardTemplate.readListFromText(map.get("elitereward"));
        }
        seasonRewardMap = temp;

        mapList = ConfigReader.read(ConfigFile.seasonPermit_seasonPermitTime);
        int lastEndTime = 0;
        Map<Integer, Pair<Integer, Integer>> temp2 = new HashMap<>();
        for (Map<String, String> map : mapList) {
            int seasonId = Integer.parseInt(map.get("seasonId"));
            int openTime = Integer.parseInt(map.get("openTime"));
            int endTime = Integer.parseInt(map.get("endTime"));
            if(lastEndTime != 0 && lastEndTime != openTime){
                throw new IllegalArgumentException("seasonPermit_seasonPermitTime end time is error, seasonId : " + seasonId);
            }else{
                lastEndTime = endTime;
            }
            temp2.computeIfAbsent(seasonId,
                    value -> Pair.of(openTime, endTime));
        }
        seasonTime = temp2;

        mapList = ConfigReader.read(ConfigFile.seasonPermit_seasonPermitConfig);
        Map<Integer, SeasonPermitParams> temp3 = new HashMap<>();
        for (Map<String, String> map : mapList) {

            int seasonId = Integer.parseInt(map.get("seasonId"));
            SeasonPermitParams params = temp3.get(seasonId);
            if (params == null) {
                params = new SeasonPermitParams();
                temp3.put(seasonId, params);
            }

            String key = map.get("key");
            String value = map.get("value");
            switch (key) {
                case "expid":
                    params.expId = Integer.parseInt(value);
                    break;
                case "buylevelcost":
                    params.buyLevelCost = RewardTemplate.readListFromText(value).get(0);
                    break;
                case "payid":
                    params.payid = Integer.parseInt(value);
                    break;
                case "payidEpic":
                    params.payidEpic = Integer.parseInt(value);
                    break;
                case "payidEpic2":
                    params.payidEpic2 = Integer.parseInt(value);
                    break;
                case "epicExp":
                    params.epicExps.addAll(RewardTemplate.readListFromText(value));
                    break;
                    case "weeklyMaxExp":
                        params.weeklyMaxExp = Long.parseLong(value);
                    break;
            }
        }
        paramsMap = temp3;

        mapList = ConfigReader.read(ConfigFile.seasonPermit_seasonPermitTask);
        Map<Integer, Map<Integer, SeasonPermitTaskTemplate>> taskTemplateMap = new HashMap<>();
        for (Map<String, String> map : mapList) {
            SeasonPermitTaskTemplate taskTemplate = new SeasonPermitTaskTemplate();
            taskTemplate.id = Integer.parseInt(map.get("id"));
            taskTemplate.seasonId = Integer.parseInt(map.get("seasonId"));
            taskTemplate.goalId = Integer.parseInt(map.get("goalId"));
            taskTemplate.reset = Integer.parseInt(map.get("reset"));
            taskTemplate.exp = RewardTemplate.readListFromText(map.get("rewardExp"));
            if(!taskTemplateMap.containsKey(taskTemplate.seasonId)){
                taskTemplateMap.put(taskTemplate.seasonId, new HashMap<>());
            }
            taskTemplateMap.get(taskTemplate.seasonId).put(taskTemplate.goalId, taskTemplate);
        }
        taskTemplates = taskTemplateMap;

    }

    @Override
    public void clearConfigData() {
        seasonRewardMap.clear();
        paramsMap.clear();
        seasonTime.clear();
        taskTemplates.clear();
    }

    public static Map<Integer, Map<Integer, SeasonPermitRewardTemplate>> getSeasonRewardMap() {
        return seasonRewardMap;
    }

    public static Map<Integer, SeasonPermitParams> getParamsMap() {
        return paramsMap;
    }

    static Map<Integer, Pair<Integer, Integer>> getSeasonTime() {
        return seasonTime;
    }

    public static Map<Integer, SeasonPermitTaskTemplate> getTaskTemplates(int seasonId) {
        return taskTemplates.get(seasonId);
    }

    /**
     * 根据当前时间计算玩家处于哪个赛季
     */
    static int getNowSeason(Player player, long nowDays) {

        //开启天数
        long difference = nowDays - player.getSeasonPermitModel().getOpenDays();
        int maxSeason = player.getSeasonPermitModel().getNowSeason();
        for (Map.Entry<Integer, Pair<Integer, Integer>> entry : seasonTime.entrySet()) {
            maxSeason = MathUtil.max(entry.getKey(), maxSeason);
            if (difference >= entry.getValue().getLeft() && difference < entry.getValue().getRight()) {
                return entry.getKey();
            }
        }

        return Math.max(player.getSeasonPermitModel().getNowSeason(), maxSeason);
    }

    /**
     * 赛季通行证-购买精英通行证
     */
    @Handler(PtCode.SEASON_BUY_ELITE_PERMIT_REQ)
    private void buyElitePermit(Player player, PbProtocol.SeasonBuyElitePermitReq req, long timeStamp) {
        PbProtocol.SeasonBuyElitePermitRst.Builder rst = PbProtocol.SeasonBuyElitePermitRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        player.send(PtCode.SEASON_BUY_ELITE_PERMIT_RST, rst.build(), timeStamp);
    }

    /**
     * 赛季通行证-购买等级
     */
    @Handler(PtCode.SEASON_BUY_PERMIT_LEVEL_REQ)
    private void buyLevel(Player player, PbProtocol.SeasonBuyPermitLevelReq req, long timeStamp) {
        PbProtocol.SeasonBuyPermitLevelRst.Builder rst = PbProtocol.SeasonBuyPermitLevelRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());


        int buyLevel = req.getBuyLevel();

        logic:
        {
            PlayerSeasonPermitModel module = player.getSeasonPermitModel();
            //最大等级
            int maxLevel = seasonRewardMap.get(module.getNowSeason()).size();
            if (buyLevel + module.getLevel() > maxLevel) {
                rst.setResult(Text.genServerRstInfo(Text.已达到最大等级));
                break logic;
            }

            if (buyLevel < 0) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }

            SeasonPermitParams params = paramsMap.get(module.getNowSeason());
            long costValue = params.buyLevelCost.value * buyLevel;

            Reward cost = Reward.create(params.buyLevelCost.type, params.buyLevelCost.itemTemplateId, costValue);
            if (cost.check(player) != -1) {
                rst.setResult(Text.genServerRstInfo(Text.货币不足无法购买));
                break logic;
            }

            //扣钱
            cost.remove(player, BehaviorType.SeasonPermitBuyLevel);

            //加等级
            module.addLevel(buyLevel);

            //同步信息
            player.dataSyncModule.syncSeasonPermit();
        }
        player.send(PtCode.SEASON_BUY_PERMIT_LEVEL_RST, rst.build(), timeStamp);

    }

    /**
     * 赛季通行证-领取等级奖励
     */
    @Handler(PtCode.SEASON_RECEIVE_REWARD_REQ)
    private void receiveLevelReward(Player player, PbProtocol.SeasonReceiveRewardReq req, long timeStamp) {
        PbProtocol.SeasonReceiveRewardRst.Builder rst = PbProtocol.SeasonReceiveRewardRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());


        int level = req.getLevel();

        logic:
        {

            PlayerSeasonPermitModel module = player.getSeasonPermitModel();

            if (module.getNowSeason() == -1) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }

            long leftEnd = module.getEndTime() - ServerConstants.getCurrentTimeMillis();
            if (leftEnd <= 0) {
                rst.setResult(Text.genServerRstInfo(Text.本季度赛季通行证已结束));
                break logic;
            }

            if (level > seasonRewardMap.get(module.getNowSeason()).size()) {
                rst.setResult(Text.genServerRstInfo(Text.数据异常));
                break logic;
            }

            Map<Integer, SeasonPermitRewardTemplate> rewardTemplateMap = seasonRewardMap.get(module.getNowSeason());

            List<Reward> rewards = new ArrayList<>();

            //领指定等级的所有奖励
            if (level >= 1) {
                if (level > module.getLevel()) {
                    rst.setResult(Text.genServerRstInfo(Text.超过当前等级无法领取));
                    break logic;
                }
                boolean flag = true;
                if (!module.getNowSeasonReceiveNormalRewardLevels().contains(level)) {
                    rewards.addAll(Reward.templateCollectionToReward(rewardTemplateMap.get(level).normalRewards));
                    module.getNowSeasonReceiveNormalRewardLevels().add(level);
                    flag = false;
                }

                if (module.isBuyElite()) {
                    if (!module.getNowSeasonReceiveEliteRewardLevels().contains(level)) {
                        rewards.addAll(Reward.templateCollectionToReward(rewardTemplateMap.get(level).eliteRewards));
                        module.getNowSeasonReceiveEliteRewardLevels().add(level);
                        flag = false;
                    }
                }
                if (flag) {
                    rst.setResult(Text.genServerRstInfo(Text.当前等级奖励已经领取请勿重复领取));
                    break logic;
                }

            } else {
                List<Integer> levelList = Stream.iterate(1, item -> item + 1).limit(module.getLevel()).collect(Collectors.toList());
                List<Integer> eliteLevelList = new ArrayList<>(levelList);
                //普通奖励
                levelList.removeAll(module.getNowSeasonReceiveNormalRewardLevels());
                for (int value : levelList) {
                    rewards.addAll(Reward.templateCollectionToReward(rewardTemplateMap.get(value).normalRewards));
                    module.getNowSeasonReceiveNormalRewardLevels().add(value);
                }

                //精英奖励
                if (module.isBuyElite()) {
                    eliteLevelList.removeAll(module.getNowSeasonReceiveEliteRewardLevels());
                    for (int value : eliteLevelList) {
                        rewards.addAll(Reward.templateCollectionToReward(rewardTemplateMap.get(value).eliteRewards));
                        module.getNowSeasonReceiveEliteRewardLevels().add(value);
                    }
                }
            }

            Reward.merge(rewards);
            //发奖
            for (Reward reward : rewards) {
                reward.add(player, BehaviorType.SeasonPermitReceiveReward);
            }

            rst.addAllRewards(Reward.writeCollectionToPb(rewards));

            //同步
            player.dataSyncModule.syncSeasonPermit();

        }

        player.send(PtCode.SEASON_RECEIVE_REWARD_RST, rst.build(), timeStamp);
    }

    /**
     * 领取奖励信息
     */
    @Handler(PtCode.SEASON_RECEIVE_TASK_REWARD_REQ)
    public void receiveTaskReward(Player player, PbProtocol.SeasonReceiveTaskRewardReq req, long timeStamp) {

        PbProtocol.SeasonReceiveTaskRewardRst.Builder rst = PbProtocol.SeasonReceiveTaskRewardRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());


        logic:
        {
            int receiveGoalId = req.getGoalId();
            PlayerSeasonPermitModel module = player.getSeasonPermitModel();

            if (module.getNowSeason() == -1) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }

            long leftEnd = module.getEndTime() - ServerConstants.getCurrentTimeMillis();
            if (leftEnd <= 0) {
                rst.setResult(Text.genServerRstInfo(Text.本季度赛季通行证已结束));
                break logic;
            }

            Map<Integer, Boolean> taskReceiveInfos = module.getTaskReceiveInfos();
            List<RewardTemplate> rewardTemplates = new ArrayList<>();

            SeasonPermitTaskTemplate receiveTaskTemplate = SeasonPermitService.getTaskTemplates(module.getNowSeason()).get(receiveGoalId);
            for (Integer goalId : taskReceiveInfos.keySet()) {
                if(!taskReceiveInfos.get(goalId)){
                    SeasonPermitTaskTemplate taskTemplate = SeasonPermitService.getTaskTemplates(module.getNowSeason()).get(goalId);
                    if(Objects.nonNull(taskTemplate)){
                        if(Objects.isNull(receiveTaskTemplate) || taskTemplate.reset == receiveTaskTemplate.reset){
                            taskReceiveInfos.put(goalId, true);
                            rewardTemplates.addAll(taskTemplate.exp);
                            rst.addReceiveGoalIds(goalId);
                        }
                    }
                }
            }
            Reward.addFromTemplates(rewardTemplates, player, BehaviorType.SeasonPermitReceiveExp);

            player.dataSyncModule.syncSeasonPermit();
        }

        player.send(PtCode.SEASON_RECEIVE_TASK_REWARD_RST, rst.build(), timeStamp);
    }


}

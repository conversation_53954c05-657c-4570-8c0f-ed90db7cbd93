package com.gy.server.game.rank;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.thread.AbstractRunner;
import com.gy.server.game.db.DbAssistant;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.lineup.bean.LineupInfoBean;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.record.RecordManager;
import com.gy.server.game.tournament.TournamentService;
import com.gy.server.packet.PbRecord;
import com.gy.server.utils.MathUtil;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.thread.ThreadPool;
import org.apache.commons.lang3.tuple.Pair;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 排行榜管理器
 *
 * <AUTHOR> - [Created on 2018/4/16 16:51]
 */
public class RankManager extends AbstractRunner {

    private static final long SORT_INTERVAL = DateTimeUtil.MillisOfSecond * 5;

    private static final long UPDATE_INTERVAL_MINUTES = 10;
    private static final long ADD_LOG_MINUTES = 2;

    private static final RankManager instance = new RankManager();

    private static Map<Integer, Map<RankType, RankData>> dataMap = new HashMap<>();

    private LocalDateTime nextUpdateTime = ServerConstants.getCurrentTimeLocalDateTime().plusMinutes(UPDATE_INTERVAL_MINUTES);
    private LocalDateTime nextAddLogTime = ServerConstants.getCurrentTimeLocalDateTime().plusMinutes(ADD_LOG_MINUTES);


    private RankManager() {

    }

    public static RankManager getInstance() {
        return instance;
    }

    public static void loadRank() {
        for (RankType rankType : RankType.values()) {
            rankType.getRankDeal().init(rankType);
            RankData rankData = new RankData(rankType);
            dataMap.put(rankType, rankData);

            rankData.init();
        }
    }

    public static void update() {
        for (RankData rankData : dataMap.values()) {
            rankData.update();
        }
    }

    /**
     * 清空内存排行榜数据
     * 仅在清档使用
     */
    public void clear(){
        dataMap.clear();
    }

    /**
     * 推送排行榜条目
     *
     * @param rankType 排行榜类型
     * @param object   排行榜条目
     */
    public static void postItem(RankType rankType, Object object) {
        if (rankType.matchObject(object) && rankType.canRank(object)) {
            RankData data = dataMap.get(rankType);
            RankItem rankItem = RankItem.create(rankType, object);
            if (rankType.isMs()) {
                rankItem.setMsSave(true);
            }
            data.postItem(rankItem);
        }
    }

    /**
     * 删除排行榜条目
     *
     * @param rankType 排行榜类型
     * @param object   排行榜条目
     */
    public static void deleteItem(RankType rankType, Object object) {
        if (rankType.matchObject(object)) {
            RankData data = dataMap.get(rankType);
            data.deleteItem(RankItem.create(rankType, object));
        }
    }

    /**
     * 获取排行榜数据
     * @param rankType 排行榜类型
     */
    public static RankData getRankData(RankType rankType) {
        if(!dataMap.containsKey(rankType)){
            dataMap.put(rankType, new RankData(rankType));
        }
        return dataMap.get(rankType);
    }

    /**
     * 根据机器人要求获取排行榜上玩家对应阵容
     * 异步使用
     * @param player
     * @param isRobotA true A / false B
     * @return
     */
    public static long getLineupByFightPowerRankRank(Player player, boolean isRobotA){
        List<RankItem> randomItems = getRandomPool(player, RankType.playerFightPower, isRobotA);
        if(randomItems.isEmpty()){
            return 0l;
        }

        RankItem randomItem = MathUtil.random(randomItems);
        return randomItem.getId();
    }

//    /**
//     * 根据机器人要求获取排行榜上玩家对应阵容
//     * 异步使用
//     * @param player
//     * @param rankType
//     * @param isRobotA true A / false B
//     * @return
//     */
//    public static Pair<PbRecord.RecordPlayer, List<LineupInfoBean>> getLineupByFightPowerRankRank(Player player, RankType rankType, LineupType lineupType, boolean isRobotA){
//        List<RankItem> randomItemList = getRandomPool(player, rankType, isRobotA);
//        if(randomItemList.isEmpty()){
//            return null;
//        }
//
////        //过滤机器人
////        for (RankItem rankItem : randomItemList) {
////            rankItem.
////        }
//
//        //随机
//        Player randomPlayer = null;
//        while (randomItemList.size() > 0 && randomPlayer == null){
//            RankItem randomItem = MathUtil.random(randomItemList);
//            MiniGamePlayer miniRandomPlayer = PlayerManager.getMiniPlayer(randomItem.getId());
//
//            //排行榜有些有机器人
//            if(miniRandomPlayer == null){
//                randomItemList.remove(randomItem);
//            }
//            randomPlayer = PlayerManager.getPlayer(randomItem.getId());
//        }
//
//        //随不出真人
//        if(randomPlayer == null){
//            return null;
//        }
//
//
//        PbRecord.RecordPlayer recordPlayer = RecordManager.genRecordPlayer(randomPlayer, false, LineupType.Common, null).build();
//
//        List<LineupInfoBean> lineInfoBean = lineupType.getLineup(randomPlayer);
//        if(lineInfoBean == null){
//            //尝试拿主线布阵
//            return Pair.of(recordPlayer, LineupType.Common.getLineup(randomPlayer));
//        }
//        return Pair.of(recordPlayer, lineInfoBean);
//    }

    private static List<RankItem> getRandomPool(Player player, RankType rankType, boolean isRobotA){
        List<RankItem> randomItems = new ArrayList<>();

        RankData data = RankManager.getRankData(rankType);
        RankItem myItem = data.getRankItemByPlayer(player);
        if(myItem == null){
            return null;
        }

        List<RankItem> rankItems = data.getItems();
        if(isRobotA){
            int minRank = Math.max(0, myItem.rank - TournamentService.getTournamentConst().robotA);
            int maxRank = Math.min(myItem.rank + TournamentService.getTournamentConst().robotA, rankItems.size());
            randomItems.addAll(rankItems.subList(minRank, maxRank));
            randomItems.remove(myItem);
        }else {
            long value = myItem.getValues().length > 1 ? myItem.getValues()[0] : 0;
            Pair<Integer, Integer> robotB = TournamentService.getTournamentConst().robotB;
            value = value * (10000 - robotB.getLeft()) / 10000;

//            List<RankItem> randomItemsTemp = new ArrayList<>();

            int rankTemp = myItem.rank;
            long range = value * robotB.getLeft() / 10000;
            for (int i = myItem.rank; i < rankItems.size(); i++) {
                RankItem rankItem = rankItems.get(i);
                long rangTemp = Math.abs(myItem.getValues().length > 1 ? myItem.getValues()[0] : 0 - value);
                if(range <= rangTemp){
                    rankTemp = rankItem.rank;
                }else {
                    break;
                }
            }

            int minRank = Math.max(0, Math.min(rankTemp - robotB.getRight(), rankItems.size() - 1));
            int maxRank = Math.min(rankTemp + robotB.getRight(), rankItems.size() - 1);
            randomItems.addAll(rankItems.subList(minRank, maxRank));
            randomItems.remove(myItem);
        }

        return randomItems;
    }
    
    /**
     * 检查更新
     */
    private void checkUpdate() {
        LocalDateTime now = ServerConstants.getCurrentTimeLocalDateTime();
        if (this.nextUpdateTime.isBefore(now)) {
            this.nextUpdateTime = now.plusMinutes(UPDATE_INTERVAL_MINUTES);

            ThreadPool.execute(RankManager::update);
        }
    }

    /**
     * 增加日志
     */
    private void logInfo() {
        LocalDateTime now = ServerConstants.getCurrentTimeLocalDateTime();
        if (this.nextAddLogTime.isBefore(now)) {
            this.nextAddLogTime = now.plusMinutes(ADD_LOG_MINUTES);

            //增加排行榜日志
            ThreadPool.execute(() -> {
                for (RankType rankType : dataMap.keySet()) {
                    int dbRankNum = DbAssistant.getRankItemCountByRankId(rankType.getRankId());
                    int cacheRankNum = dataMap.get(rankType).getItems().size();
                    //打印排行榜数量
                    GameLogger.rankNumInfo(rankType, cacheRankNum, dbRankNum);
                    if (dbRankNum != cacheRankNum) {
                        for (RankItem item : dataMap.get(rankType).getItems()) {
                            GameLogger.rankInfo(false, item);
                        }
                    }
                }
            });
        }
    }

    @Override
    public String getRunnerName() {
        return "RankManager";
    }

    @Override
    protected void subRunnerExecute() throws Exception {
        for (RankData rankData : dataMap.values()) {
            rankData.checkSort();
        }

        this.checkUpdate();
        this.logInfo();
    }

    @Override
    public long getRunnerInterval() {
        return SORT_INTERVAL;
    }

}

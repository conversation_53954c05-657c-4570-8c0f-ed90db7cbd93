package com.gy.server.game.weaponSoul.template;

import com.gy.server.game.drop.RewardTemplate;
import com.ttlike.server.tl.baselib.serialize.combatAddition.CombatAdditions;

import java.util.List;
import java.util.Map;

/**
 * 器魂模板
 *
 * <AUTHOR> 2023/12/19 11:22
 **/
public class WeaponSoulTemplate {

    public int id;
    public int index;
    public int spot;
    public int level;
    public List<RewardTemplate> cost;

    /**
     * 解锁的下一个点
     */
    public int unlock;

    /**
     * 装备位
     */
    public int equipmentPosition;

    /**
     * 增加的强化等级上限
     */
    public int addStrengthLv;

    /**
     * 加成效果
     */
    public CombatAdditions combatAdditions;

    public WeaponSoulTemplate(Map<String, String> map) {
        this.id = Integer.parseInt(map.get("id"));
        this.index = Integer.parseInt(map.get("position"));
        this.spot = Integer.parseInt(map.get("spot"));
        this.level = Integer.parseInt(map.get("level"));
        this.cost = RewardTemplate.readListFromText(map.get("consume"));
        this.unlock = Integer.parseInt(map.get("limit"));

        String upper = map.get("upper");
        if(!upper.equals("-1")){
            String[] array = upper.split("\\|");
            this.equipmentPosition = Integer.parseInt(array[0]);
            this.addStrengthLv = Integer.parseInt(array[1]);
        }

        this.combatAdditions = CombatAdditions.readFromStr(map.get("effect"));
    }
}

package com.gy.server.game.weaponSoul;

import com.gy.server.game.equipment.EquipmentPositionType;
import com.gy.server.game.equipment.template.EquipmentTemplate;
import com.gy.server.game.item.ItemService;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModel;
import com.gy.server.game.player.event.PlayerEvent;
import com.gy.server.game.player.event.PlayerEventHandler;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.weaponSoul.template.WeaponSoulTemplate;
import com.gy.server.packet.PbWeaponSoul;
import com.ttlike.server.tl.baselib.serialize.player.PlayerBlobDb;
import com.ttlike.server.tl.baselib.serialize.weaponSoul.PlayerWeaponSoulModelDb;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 器魂 属性加成和装备强化等级上限加成
 * 计算器魂各装备位最大强化等级加成,单个器魂找最大，不同器魂累加
 * 属性影响，单个器魂找最新，不同器魂累加
 * <AUTHOR> 2023/12/18 16:27
 **/
public class PlayerWeaponSoulModel extends PlayerModel implements PlayerEventHandler {

    private static final PlayerEventType[] eventTypes = new PlayerEventType[]{};

    /**
     * 器魂现在id
     */
    private int id;

    /**
     * key 装备位   value 强化等级上限    内存不进库
     */
    private Map<Integer, Integer> addMaxStrengthLvMap = new HashMap<>();

    public PlayerWeaponSoulModel(Player player) {
        super(player);
    }

    @Override
    public void init() {
        //计算强化等级上限加成
        countWeaponSoulAddMaxStrengthLv();
    }

    @Override
    protected void loadData(PlayerBlobDb playerBlob) {
        PlayerWeaponSoulModelDb weaponSoulModelDb = playerBlob.getWeaponSoulModelDb();
        if(Objects.nonNull(weaponSoulModelDb)){
            this.id = weaponSoulModelDb.getId();
        }
    }

    @Override
    protected void saveData(PlayerBlobDb playerBlob) {
        PlayerWeaponSoulModelDb db = new PlayerWeaponSoulModelDb();
        db.setId(id);
        playerBlob.setWeaponSoulModelDb(db);
    }

    @Override
    public PlayerEventType[] getEventTypes() {
        return eventTypes;
    }

    @Override
    public void handle(PlayerEvent event) {

    }

    public Map<Integer, Integer> getAddMaxStrengthLvMap() {
        return addMaxStrengthLvMap;
    }

    public void setAddMaxStrengthLvMap(Map<Integer, Integer> addMaxStrengthLvMap) {
        this.addMaxStrengthLvMap = addMaxStrengthLvMap;
    }

    public PbWeaponSoul.WeaponSoulModel genPb() {
        PbWeaponSoul.WeaponSoulModel.Builder builder = PbWeaponSoul.WeaponSoulModel.newBuilder();
        builder.setId(id);
        builder.putAllAddMaxStrengthLv(addMaxStrengthLvMap);
        return builder.build();
    }

    /**
     * 计算器魂各装备位最大强化等级加成
     * 单个器魂找最大，不同器魂累加
     */
    public void countWeaponSoulAddMaxStrengthLv(){
        if(id == 0){
            return;
        }

        //key:器魂序号  value: key:装备位 value:增加的强化等级
        Map<Integer, Map<Integer, Integer>> tempMap = new HashMap<>();

        Map<Integer, WeaponSoulTemplate> weaponSoulTemplateMap = WeaponSoulService.getWeaponSoulTemplateMap();
        weaponSoulTemplateMap.values().stream().filter(template -> id >= template.id).forEach(template -> {
            if(template.equipmentPosition != 0){
                Map<Integer, Integer> map = tempMap.getOrDefault(template.index, new HashMap<>());
                int maxAddMaxStrengthLv = map.getOrDefault(template.equipmentPosition, 0);
                if(template.addStrengthLv > maxAddMaxStrengthLv){
                    map.put(template.equipmentPosition, template.addStrengthLv);
                    tempMap.put(template.index, map);
                }
            }
        });

        this.addMaxStrengthLvMap.clear();
        tempMap.values().forEach(map -> map.forEach((key, value) -> {
            this.addMaxStrengthLvMap.put(key, this.addMaxStrengthLvMap.getOrDefault(key, 0) + value);
        }));
    }

    /**
     * 获取最大强化等级加成
     * @param type
     * @return
     */
    public int getAddMaxStrengthLvByEquipmentPositionType(EquipmentPositionType type){
        return getAddMaxStrengthLvByEquipmentPositionType(type.id);
    }

    public int getAddMaxStrengthLvByEquipmentPositionType(int type){
        return addMaxStrengthLvMap.getOrDefault(type, 0);
    }

    public int getAddMaxStrengthLvByTemplateId(int templateId){
        EquipmentTemplate equipmentTemplate = (EquipmentTemplate) ItemService.getItemTemplate(templateId);
        return getAddMaxStrengthLvByEquipmentPositionType(equipmentTemplate.equipmentPositionType);
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }
}

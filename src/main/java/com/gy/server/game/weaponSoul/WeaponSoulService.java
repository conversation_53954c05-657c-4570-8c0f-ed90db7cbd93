package com.gy.server.game.weaponSoul;

import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.function.Function;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.service.Service;
import com.gy.server.game.text.Text;
import com.gy.server.game.weaponSoul.template.WeaponSoulTemplate;
import com.gy.server.packet.PbProtocol;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 器魂服务类
 *
 * <AUTHOR> 2023/12/19 13:47
 **/
public class WeaponSoulService extends PlayerPacketHandler implements Service {

    /**
     * key 器魂序号|融魂点|融魂点等级
     * value  key 融魂点等级
     */
    private static Map<Integer, WeaponSoulTemplate> weaponSoulTemplateMap = new HashMap<>();

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.WeaponSoul_WeaponSoul);
        Map<Integer, WeaponSoulTemplate> weaponSoulTemplateMapTemp = new HashMap<>();
        for(Map<String, String> map : mapList){
            WeaponSoulTemplate template = new WeaponSoulTemplate(map);
            weaponSoulTemplateMapTemp.put(template.id, template);
        }
        weaponSoulTemplateMap = weaponSoulTemplateMapTemp;
    }

    @Override
    public void clearConfigData() {
        weaponSoulTemplateMap.clear();
    }

    /**
     * 器魂信息
     * @param player
     * @param req
     */
    @Handler(PtCode.WEAPON_SOUL_INFO_REQ)
    private void weaponSoulInfoReq(Player player, PbProtocol.WeaponSoulInfoReq req, long time){
        PbProtocol.WeaponSoulInfoRst.Builder rst = PbProtocol.WeaponSoulInfoRst.newBuilder().setResult(Text.genOkServerRstInfo());

        logic:{
            if(Function.WeaponSoul.isNotOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }

            PlayerWeaponSoulModel model = player.getWeaponSoulModel();
            rst.setWeaponSoul(model.genPb());
        }

        player.send(PtCode.WEAPON_SOUL_INFO_RST, rst.build(), time);
    }

    /**
     * 器魂升级
     * @param player
     * @param req
     */
    @Handler(PtCode.WEAPON_SOUL_UP_REQ)
    private void weaponSoulUpReq(Player player, PbProtocol.WeaponSoulUpReq req, long time){
        PbProtocol.WeaponSoulUpRst.Builder rst = PbProtocol.WeaponSoulUpRst.newBuilder().setResult(Text.genOkServerRstInfo());

        logic:{
            if(Function.WeaponSoul.isNotOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }

            PlayerWeaponSoulModel model = player.getWeaponSoulModel();
            int nextId = model.getId() == 0 ? 1 : weaponSoulTemplateMap.get(model.getId()).unlock;
            if(nextId == -1){
                rst.setResult(Text.genServerRstInfo(Text.器魂已达到最大等级));
                break logic;
            }

            WeaponSoulTemplate template = weaponSoulTemplateMap.get(nextId);
            List<RewardTemplate> costTemplates = template.cost;
            List<Reward> costs = Reward.templateCollectionToReward(costTemplates);
            if(Reward.check(player, costs) != -1){
                rst.setResult(Text.genServerRstInfo(Text.消耗不足));
                break logic;
            }

            int oldId = model.getId();
            Reward.remove(costs, player, BehaviorType.weaponSoulUpConsume);
            model.setId(nextId);
            model.countWeaponSoulAddMaxStrengthLv();

            player.postEvent(PlayerEventType.weaponSoulUp, nextId);

            player.dataSyncModule.syncWeaponSoul();
            GameLogger.weaponSoulUp(player, oldId, nextId, costs);
        }

        player.send(PtCode.WEAPON_SOUL_UPO_RST, rst.build(), time);
    }

    public static Map<Integer, WeaponSoulTemplate> getWeaponSoulTemplateMap() {
        return weaponSoulTemplateMap;
    }

    public static void setWeaponSoulTemplateMap(Map<Integer, WeaponSoulTemplate> weaponSoulTemplateMap) {
        WeaponSoulService.weaponSoulTemplateMap = weaponSoulTemplateMap;
    }

}

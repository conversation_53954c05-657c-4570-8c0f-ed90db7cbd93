package com.gy.server.game.leagueDuel.status.impl;

import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.leagueDuel.LeagueDuelGlobalData;
import com.gy.server.game.leagueDuel.LeagueDuelService;
import com.gy.server.game.leagueDuel.status.DuelNodeType;
import com.gy.server.game.leagueDuel.status.ILeagueDuelStatusHandler;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.DuelStatusType;

import java.time.LocalDateTime;

/**
 * 布阵阶段 下个阶段拉取公会内玩家数据
 *
 * <AUTHOR> - [Created on 2023/5/26 14:25]
 */
public class LeagueDuelLineupHandler implements ILeagueDuelStatusHandler {
    @Override
    public DuelStatusType handleStatus() {
        return DuelStatusType.lineup;
    }

    @Override
    public void handler(LeagueDuelGlobalData globalData) {
        //无需处理 等待时间结束就行
//        LocalDateTime now = ServerConstants.getCurrentTimeLocalDateTime();
//        LocalDateTime lineupEndTime = getEndTime();
//        if (!now.isBefore(lineupEndTime)) {
//            changeStatus(DuelStatusType.pullMirror);
//        }
        changeStatus(DuelStatusType.pullMirror);
    }

    @Override
    public LocalDateTime getEndTime() {
        Integer duration = LeagueDuelService.getConstant().getOnTimeMap().get(DuelNodeType.lineup).getRight();
        LeagueDuelGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueDuel);
        return globalData.getOpenTime().plusMinutes(duration);
    }
}

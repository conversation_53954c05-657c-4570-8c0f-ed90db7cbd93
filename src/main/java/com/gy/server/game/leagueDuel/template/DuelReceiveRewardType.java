package com.gy.server.game.leagueDuel.template;

import java.util.HashMap;
import java.util.Map;

/**
 * 对决奖励类型
 *
 * <AUTHOR> - [Created on 2023/5/25 10:14]
 */
public enum DuelReceiveRewardType {
    /**
     * 团队胜利
     */
    teamWin(1),
    /**
     * 团队失败
     */
    teamLose(2),
    /**
     * 个人挑战一场
     */
    personFight(3),
    /**
     * 挑战同战力的
     */
    personSame(4),
    /**
     * 挑战高战力的
     */
    personHigh(5),
    ;

    private final int type;

    private static final Map<Integer, DuelReceiveRewardType> map = new HashMap<>();

    DuelReceiveRewardType(int type) {
        this.type = type;
    }

    public static DuelReceiveRewardType of(int type) {
        return map.get(type);
    }

    static {
        for (DuelReceiveRewardType value : values()) {
            map.put(value.type, value);
        }
    }
}

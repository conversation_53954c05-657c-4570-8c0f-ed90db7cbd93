package com.gy.server.game.leagueDuel.stage;

import java.util.*;

import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.battleCollect.BattleCollectService;
import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.combat.StageType;
import com.gy.server.game.combat.unit.TeamUnit;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.league.LeagueModelEnums;
import com.gy.server.game.leagueDuel.LeagueDuelGlobalData;
import com.gy.server.game.leagueDuel.LeagueDuelModel;
import com.gy.server.game.leagueDuel.LeagueDuelService;
import com.gy.server.game.leagueDuel.record.LeagueDuelRecord;
import com.gy.server.game.leagueDuel.template.*;
import com.gy.server.game.lineup.LineupHelper;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.lineup.bean.LineupInfoBean;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.record.RecordManager;
import com.gy.server.game.record.RecordType;
import com.gy.server.game.record.combat.CombatRecord;
import com.gy.server.game.robot.bean.BaseRobot;
import com.gy.server.packet.PbCommons;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbRecord;
import com.gy.server.packet.PbRobot;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.DuelCityPointInfo;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.PersonRewardBean;

/**
 * 帮派对决战场类
 *
 * <AUTHOR> - [Created on 2023/5/31 14:58]
 */
public class LeagueDuelStage extends AbstractStage {
    /**
     * 玩家信息
     */
    private final Player player;
    private final League league;
    private final int diff;
    /**
     * 目标帮派ID
     */
    private final long targetLeagueId;
    /**
     * 城防军buff等级
     */
    private final int cityDefenceBuffLv;
    /**
     * 阵眼是否被击破
     */
    private final boolean isEyeBroken;
    /**
     * 目标城市据点信息
     */
    private final DuelCityPointInfo duelCityPointInfo;

    public LeagueDuelStage(Player player, long targetLeagueId, DuelCityPointInfo duelCityPointInfo,
                           int diff, int cityDefenceBuffLv, boolean isEyeBroken) {
        this.player = player;
        this.league = LeagueManager.getLeagueByPlayer(player);
        this.targetLeagueId = targetLeagueId;
        this.duelCityPointInfo = duelCityPointInfo;
        this.diff = diff;
        this.cityDefenceBuffLv = cityDefenceBuffLv;
        this.isEyeBroken = isEyeBroken;
    }

    @Override
    public void init() {
        List<TeamUnit> atkUnits = player.getLineupModel().createTeamUnits(this, StageType.leagueDuel, LineupType.duelAttack);
        List<TeamUnit> defTeamUnits;
        if (duelCityPointInfo.isRobot()) {
            int battleCollectId = duelCityPointInfo.getRobotInfo().getRobotBattleCollect().getBattleCollectId();
            defTeamUnits = BattleCollectService.createTeamUnits(this, -1, battleCollectId);
        } else {
            List<LineupInfoBean> lineupInfoBeanList = LineupInfoBean.readFromDb(duelCityPointInfo.getLineupDbList());
            defTeamUnits = LineupType.duelDefence.createTeamUnitMirrorList(this, lineupInfoBeanList);
        }
        LeagueDuelPointTemplate pointTemplate = LeagueDuelService.getLeagueDuelPointTemplate(duelCityPointInfo.getCityId(), duelCityPointInfo.getPointId());
        //城防军不受buff限制
        if (Objects.nonNull(pointTemplate) && pointTemplate.pointType != DuelPointType.CityDefense) {
            //守军作用Buff
            Map<Integer, Integer> defBuffMap = new HashMap<>();
            //城防军加成Buff
            if (cityDefenceBuffLv > 0) {
                defBuffMap.put(LeagueDuelService.getConstant().getCityDefenceBuffId(), cityDefenceBuffLv);
            }
            //deBuff
            if (duelCityPointInfo.getDeBuffLv() > 0) {
                defBuffMap.put(LeagueDuelService.getConstant().getDeBuffId(), duelCityPointInfo.getDeBuffLv());
            }
            //困难加成的buff
            if (diff == 2) {
                defBuffMap.put(LeagueDuelService.getConstant().getDifficultyModeBuff(), 1);
            }
            //阵眼加成的Buff
            if (!isEyeBroken) {
                LeagueDuelCityTemplate duelCityTemplate = LeagueDuelService.getDuelCityTemplateMap().get(duelCityPointInfo.getCityId());
                defBuffMap.put(duelCityTemplate.cityBuff, 1);
            }
            //给守军添加buff
            for (TeamUnit defTeamUnit : defTeamUnits) {
                if(!duelCityPointInfo.isRobot()){
                    defTeamUnit.setAutoMode(duelCityPointInfo.getMiniPlayer().getPlayerId());
                }
                for (Map.Entry<Integer, Integer> entry : defBuffMap.entrySet()) {
                    defTeamUnit.addUnitBuff(entry.getKey(), entry.getValue());
                }
            }
        }
        init(LeagueDuelService.getConstant().getBattleCollectId(), StageType.leagueDuel, atkUnits, defTeamUnits, LineupType.duelAttack);
    }

    @Override
    public void afterFinish() {
        LeagueDuelConst constant = LeagueDuelService.getConstant();
        long playerId = player.getPlayerId();
        LeagueDuelModel duelModel = league.getModel(LeagueModelEnums.duel);
        //逻辑之前判断城池是否攻破
        boolean beforePassCity = duelModel.isPassCity(duelCityPointInfo.getCityId());
        int addStar = 0;
        //城市是否被攻破
        boolean isCityBreak = false;
        if (isWin) {
            LeagueDuelPointTemplate pointTemplate = LeagueDuelService.getLeagueDuelPointTemplate(duelCityPointInfo.getCityId(), duelCityPointInfo.getPointId());
            if (Objects.nonNull(pointTemplate)) {
                //城防军扣血量
                if (pointTemplate.pointType == DuelPointType.CityDefense) {
                    int downHp = constant.getCityDefenceAtkDownHp();
                    int remainHp = Math.max(duelCityPointInfo.getCityDefenceHp() - downHp, 0);
                    duelCityPointInfo.setCityDefenceHp(remainHp);
                    // 城防军buff等级
                    duelCityPointInfo.setCityDefenceBuffLv(Math.max(cityDefenceBuffLv - 1, 0));
                } else {//其余据点获得星级
                    int diffStar = pointTemplate.getDiffStar(diff);
                    addStar = diffStar - duelCityPointInfo.getStar();
                    duelCityPointInfo.setStar(diffStar);
                    duelModel.addTotalStar(addStar);
                    //据点被破
                    if (duelModel.isBreak(duelCityPointInfo)) {
                        //再次判断城市是否被破,第一次破城才算破城星数
                        if (!beforePassCity && duelModel.isPassCity(duelCityPointInfo.getCityId())) {
                            LeagueDuelCityTemplate leagueDuelCityTemplate = LeagueDuelService.getLeagueDuelCityTemplate(duelCityPointInfo.getCityId());
                            duelModel.addTotalStar(leagueDuelCityTemplate.cityStar);
                            isCityBreak = true;
                        }
                    }
                }
            }
            //添加胜利次数
            duelModel.addHasWinNum(playerId);
        } else {
            int fightNum = duelModel.getFightNum(playerId);
            //首次失败赠送额外的次数
            if (fightNum <= 0) {
                duelModel.addExtraFightNum(playerId, constant.getFailAttacksNum());
            }
        }
        duelModel.addFightNum(playerId);
        //所有次数使用完毕 判断debuff次数
        if (duelModel.isNotEnoughAttack(playerId)) {
            int winNum = duelModel.getHasWinNum(playerId);
            if (winNum < constant.getDeBuffWinAttackNum()) {
                duelModel.addDeBuffNum(playerId);
            }
        }
        //移除正在战斗的城池
        duelModel.removeFighting(duelCityPointInfo.getCityId(), duelCityPointInfo.getPointId());
        //战报记录
        combatRecord();
        //检查个人奖励
//        checkPersonReward();
        if (isWin) {
            //同步我方场景内玩家城池信息
            duelModel.fightFinishSync(true, duelCityPointInfo, addStar);
            //同步敌方场景内玩家城池信息
            LeagueDuelGlobalData duelGlobalData = GlobalDataManager.getData(GlobalDataType.leagueDuel);
            if (duelGlobalData.isCross()) {
                Object[] params = new Object[4];
                params[0] = targetLeagueId;
                params[1] = addStar;
                params[2] = isCityBreak;
                params[3] = PbUtilCompress.encode(duelCityPointInfo);
                ServerCommandRequest request = CommandRequests.newServerCommandRequest("LeagueDuelGameCommandService.fightResultSync");
                TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, Player.getRealServerId(targetLeagueId), request, params);
            } else {
                League targetLeague = LeagueManager.getLeagueById(targetLeagueId);
                LeagueDuelModel targetDuelModel = targetLeague.getModel(LeagueModelEnums.duel);
                targetDuelModel.updatePointInfo(duelCityPointInfo);
                if (isCityBreak) {
                    targetDuelModel.costRemainCityNum();
                }
                targetDuelModel.fightFinishSync(false, duelCityPointInfo, addStar);
            }
            //增加破敌数
            duelModel.addBreakCount(playerId);
        }else{
            League targetLeague = LeagueManager.getLeagueById(targetLeagueId);
            LeagueDuelModel targetDuelModel = targetLeague.getModel(LeagueModelEnums.duel);
            if(!duelCityPointInfo.isRobot()){
                //增加御敌数
                targetDuelModel.addDefenseCount(duelCityPointInfo.getMiniPlayer().getPlayerId());
            }
        }
        PbProtocol.CombatSettlementNotify.Builder rst = genCombatSettlement();
        PbProtocol.LeagueDuelSettlement.Builder leagueDuelSettlement = PbProtocol.LeagueDuelSettlement.newBuilder();
        //发送战斗结果
        leagueDuelSettlement.setRemainDeBuffNum(duelModel.getDeBuffNum(playerId))
                .setRemainFightNum(duelModel.getRemainFightNum(playerId))
                .setCityPointInfo(duelCityPointInfo.genPb(false));
        rst.setLeagueDuel(leagueDuelSettlement.build());

        notifyCombatSettlement(player, rst.build());

        player.postEvent(PlayerEventType.leagueDuelAtk);
    }

    /**
     * 检查个人奖励
     */
    private void checkPersonReward() {
        LeagueDuelConst constant = LeagueDuelService.getConstant();
        LeagueDuelModel duelModel = league.getModel(LeagueModelEnums.duel);
        Map<DuelReceiveRewardType, LeagueDuelRewardTemplate> duelRewardTemplateMap = LeagueDuelService.getDuelRewardTemplateTypeMap();
        PersonRewardBean personRewardBean = duelModel.getPersonRewardBeanMap().computeIfAbsent(player.getPlayerId(), v -> new PersonRewardBean());
        Map<Integer, Integer> rewardStatusMap = personRewardBean.getRewardStatusMap();
        // 战斗一次奖励领取
        LeagueDuelRewardTemplate rewardTemplate = duelRewardTemplateMap.get(DuelReceiveRewardType.personFight);
        Integer rewardStatus = rewardStatusMap.getOrDefault(rewardTemplate.id, PersonRewardBean.STATUS_UNFINISHED);
        if (rewardStatus == PersonRewardBean.STATUS_UNFINISHED) {
            rewardStatusMap.put(rewardTemplate.id, PersonRewardBean.STATUS_COMPLETED);
        }
        //机器人默认同战力
        if (duelCityPointInfo.isRobot()) {
            rewardTemplate = duelRewardTemplateMap.get(DuelReceiveRewardType.personSame);
            rewardStatusMap.put(rewardTemplate.id, PersonRewardBean.STATUS_COMPLETED);
        } else {
            // 我的阵容战力
//            long myLineupPower = LineupHelper.calculateFightingPower(LineupType.duelAttack, player);
            long myLineupPower = LineupHelper.getFightPowerByLineUpType(LineupType.duelAttack, player);
            double sameMinPower = myLineupPower * constant.getMinChallengeSamePower();
            double sameMaxPower = myLineupPower * constant.getMaxChallengeSamePower();
            long defPower = duelCityPointInfo.getLineupPower();
            // 判断是不是同战力
            if (defPower >= sameMinPower && defPower <= sameMaxPower) {
                rewardTemplate = duelRewardTemplateMap.get(DuelReceiveRewardType.personSame);
                rewardStatus = rewardStatusMap.getOrDefault(rewardTemplate.id, PersonRewardBean.STATUS_UNFINISHED);
                if (rewardStatus == PersonRewardBean.STATUS_UNFINISHED) {
                    rewardStatusMap.put(rewardTemplate.id, PersonRewardBean.STATUS_COMPLETED);
                }
            }
            // 判断是不是跨战力
            double topLineupPower = myLineupPower * constant.getChallengeTallPower();
            if (defPower >= topLineupPower) {
                rewardTemplate = duelRewardTemplateMap.get(DuelReceiveRewardType.personHigh);
                rewardStatus = rewardStatusMap.getOrDefault(rewardTemplate.id, PersonRewardBean.STATUS_UNFINISHED);
                if (rewardStatus == PersonRewardBean.STATUS_UNFINISHED) {
                    rewardStatusMap.put(rewardTemplate.id, PersonRewardBean.STATUS_COMPLETED);
                }
            }
        }

    }


    /**
     * 战报记录
     */
    private void combatRecord() {
        //添加战报
        CombatRecord combatRecord;
        PbRecord.RecordPlayer atkRecordPlayer = RecordManager.genRecordPlayer(player, false, LineupType.duelAttack, getHeroUnits(true)).build();
        PbCommons.MiniUser defUser;
        PbRecord.RecordPlayer defRecordPlayer = null;
        PbRobot.BaseRobot baseRobotPb = null;
        RecordType recordType = RecordType.leagueDuel;
        if (duelCityPointInfo.isRobot()) {
            BaseRobot baseRobot = new BaseRobot(duelCityPointInfo.getRobotInfo());
            baseRobotPb = baseRobot.genPb();
            defUser = baseRobot.genMiniUser(LineupType.duelDefence);
        } else {
            defUser = PlayerHelper.genBaseMiniUser(duelCityPointInfo.getMiniPlayer()).build();
            defRecordPlayer = RecordManager.genRecordPlayer(((MiniGamePlayer) duelCityPointInfo.getMiniPlayer()), LineupType.duelDefence).build();
        }
        combatRecord = CombatRecord.create(this, player.getMiniPlayer(), defUser, false, CombatRecord.OverdueTime.LeagueDuel, recordType.getId());
        //帮派对决战报

        LeagueDuelRecord leagueDuelRecord = new LeagueDuelRecord(combatRecord.getCosKey(), recordType, duelCityPointInfo.isRobot(), isWin, combatRecord.getId(),
                atkRecordPlayer, defRecordPlayer, baseRobotPb, duelCityPointInfo.getCityId(), duelCityPointInfo.getPointId());
        RecordManager.addRecord(leagueDuelRecord, player);
        //阵眼战报
        int eyesId = LeagueDuelService.getCityEyesId(duelCityPointInfo.getCityId());
        if (duelCityPointInfo.getPointId() == eyesId) {
            LeagueDuelRecord eyesRecord = new LeagueDuelRecord(combatRecord.getCosKey(), RecordType.leagueDuelEye, duelCityPointInfo.isRobot(), isWin, combatRecord.getId(),
                    atkRecordPlayer, defRecordPlayer, baseRobotPb, duelCityPointInfo.getCityId(), duelCityPointInfo.getPointId());
            RecordManager.addRecord(eyesRecord, player);
        }
        // 保存战报战斗信息
        RecordManager.save(combatRecord);
    }


    @Override
    public void combatAbort(boolean isStart) {
        LeagueDuelModel duelModel = league.getModel(LeagueModelEnums.duel);
        //移除正在战斗的城池
        duelModel.removeFighting(duelCityPointInfo.getCityId(), duelCityPointInfo.getPointId());
    }
}

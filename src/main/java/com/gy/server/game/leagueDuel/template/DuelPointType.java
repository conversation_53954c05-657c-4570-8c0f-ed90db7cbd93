package com.gy.server.game.leagueDuel.template;

import java.util.HashMap;
import java.util.Map;

/**
 * 帮派对决建筑类型
 *
 * <AUTHOR> - [Created on 2023/5/25 10:14]
 */
public enum DuelPointType {
    /**
     * 据点
     */
    Stronghold(1),
    /**
     * 阵眼
     */
    ArrayEye(2),
    /**
     * 城防军
     */
    CityDefense(3),
    ;

    private final int type;

    private static final Map<Integer, DuelPointType> map = new HashMap<>();

    DuelPointType(int type) {
        this.type = type;
    }

    public static DuelPointType of(int type) {
        return map.get(type);
    }

    static {
        for (DuelPointType value : values()) {
            map.put(value.type, value);
        }
    }
}

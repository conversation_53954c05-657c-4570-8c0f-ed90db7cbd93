package com.gy.server.game.leagueDuel.record;

import com.google.common.collect.Sets;
import com.gy.server.game.record.Record;
import com.gy.server.game.record.RecordHelper;
import com.gy.server.game.record.RecordType;
import com.gy.server.game.robot.bean.BaseRobot;
import com.gy.server.packet.PbRecord;
import com.gy.server.packet.PbRobot;
import com.ttlike.server.tl.baselib.serialize.record.LeagueDuelRecordDb;
import com.ttlike.server.tl.baselib.serialize.record.RecordDb;

import java.util.Set;

/**
 * 帮派对决战报
 *
 * <AUTHOR> - [Created on 2023/5/30 10:11]
 */
public class LeagueDuelRecord extends Record {
    private int cityId;//对战城市ID
    private int pointId;//对战据点ID
    private boolean isRobot;
    private PbRecord.RecordPlayer atkUser;//攻方信息
    private PbRecord.RecordPlayer defUser;//对手信息
    private PbRobot.BaseRobot robotInfo;//对手机器人信息

//    public LeagueDuelRecord(LeagueDuelRecordDb beanRecordDb) {
//        super(beanRecordDb.getType(), beanRecordDb.getTime(), beanRecordDb.isWin(), beanRecordDb.getCombatRecordId(), beanRecordDb.getServerId());
//        this.cityId = beanRecordDb.getCityId();
//        this.pointId = beanRecordDb.getPointId();
//        this.isRobot = beanRecordDb.isRobot();
//        this.atkUser = RecordHelper.getInstance().genRecordPlayerPb(beanRecordDb.getAtkUser());
//        this.defUser = RecordHelper.getInstance().genRecordPlayerPb(beanRecordDb.getDefUser());
//        this.robotInfo = BaseRobot.genPb(beanRecordDb.getRobotInfo());
//    }

    @Override
    protected void subReadFromPb(PbRecord.Record record) {
        PbRecord.LeagueDuelRecord duelRecord = record.getDuelRecord();
        this.isRobot = duelRecord.getRobot();
        if (isRobot) {
            this.robotInfo = duelRecord.getRobotInfo();
        } else {
            this.defUser = duelRecord.getDefPlayer();
        }
        this.atkUser = duelRecord.getAtkPlayer();
        this.cityId = duelRecord.getCityId();
        this.pointId = duelRecord.getPointId();

    }

    @Override
    protected void subWriteToPb(PbRecord.Record.Builder builder) {
        PbRecord.LeagueDuelRecord.Builder record = PbRecord.LeagueDuelRecord.newBuilder()
                .setRobot(isRobot)
                .setAtkPlayer(atkUser);
        if (isRobot) {
            record.setRobotInfo(robotInfo);
        } else {
            record.setDefPlayer(defUser);
        }
        record.setCityId(cityId);
        record.setPointId(pointId);
        builder.setDuelRecord(record);
    }

    public LeagueDuelRecord(String cosKey, RecordType recordType, boolean isRobot, boolean win, long combatRecordId,
                            PbRecord.RecordPlayer atkRecordPlayer, PbRecord.RecordPlayer defRecordPlayer,
                            PbRobot.BaseRobot robotInfo, int cityId, int pointId) {
        super(recordType, win, combatRecordId, cosKey);
        this.atkUser = atkRecordPlayer;
        this.isRobot = isRobot;
        if (isRobot) {
            this.robotInfo = robotInfo;
        } else {
            this.defUser = defRecordPlayer;
        }
        this.cityId = cityId;
        this.pointId = pointId;
    }

    public LeagueDuelRecord() {
    }

    public boolean isRobot() {
        return isRobot;
    }

    public void setRobot(boolean robot) {
        isRobot = robot;
    }

    public PbRobot.BaseRobot getRobotInfo() {
        return robotInfo;
    }

    public void setRobotInfo(PbRobot.BaseRobot robotInfo) {
        this.robotInfo = robotInfo;
    }

    @Override
    protected Set<PbRecord.RecordPlayer> getDetailRecordSet() {
        return Sets.newHashSet(atkUser, defUser);
    }

    public LeagueDuelRecordDb genLeagueDuelRecordDb() {
        return new LeagueDuelRecordDb(cityId, pointId, isRobot, RecordHelper.getInstance().genRecordPlayerDb(atkUser), RecordHelper.getInstance().genRecordPlayerDb(defUser), BaseRobot.genDb(robotInfo));
    }

    @Override
    public void readFromDb2(RecordDb db) {
        LeagueDuelRecordDb bean = db.getLeagueDuelRecordDb();
        if(bean != null) {
            this.cityId = bean.getCityId();
            this.pointId = bean.getPointId();
            this.isRobot = bean.isRobot();
            this.atkUser = RecordHelper.getInstance().genRecordPlayerPb(bean.getAtkUser());
            this.defUser = RecordHelper.getInstance().genRecordPlayerPb(bean.getDefUser());
            this.robotInfo = BaseRobot.genPb(bean.getRobotInfo());
        }
    }

    @Override
    public void writeToDb(RecordDb db) {
        db.setLeagueDuelRecordDb(genLeagueDuelRecordDb());
    }


}

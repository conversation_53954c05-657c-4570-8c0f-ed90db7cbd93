package com.gy.server.game.leagueDuel.status.impl;

import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.leagueDuel.LeagueDuelGlobalData;
import com.gy.server.game.leagueDuel.status.ILeagueDuelStatusHandler;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.DuelStatusType;

import java.time.LocalDateTime;

/**
 * 帮派对决检查是否开启跨服玩法
 *
 * <AUTHOR> - [Created on 2023/5/25 14:50]
 */
public class LeagueDuelCheckHandler implements ILeagueDuelStatusHandler {
    @Override
    public DuelStatusType handleStatus() {
        return DuelStatusType.checkCross;
    }


    @Override
    public void handler(LeagueDuelGlobalData globalData) {

    }

    @Override
    public LocalDateTime getEndTime() {
        LeagueDuelGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueDuel);
        return globalData.getOpenTime();
    }
}

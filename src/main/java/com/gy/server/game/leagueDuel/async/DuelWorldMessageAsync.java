package com.gy.server.game.leagueDuel.async;

import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.leagueDuel.status.LeagueDuelStatusManager;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;

/**
 * 帮派对决向主world发送主消息
 *
 * <AUTHOR> - [Created on 2023/6/7 10:34]
 */
public class DuelWorldMessageAsync {
    public static void deal(String messageName, Object[] params) {
        int masterWorldId = CommonUtils.getWorldMasterServerId();
        if (masterWorldId <= 0) {
            //主节点被竞争 等待重新分配
            //加入消息队列 等待下次执行
            LeagueDuelStatusManager.getInstance().addMessage(messageName, params);
            return;
        }
        ServerCommandRequest request = CommandRequests.newServerCommandRequest(messageName);
        TLBase.getInstance().getRpcUtil().sendToNode(ServerType.WORLD, masterWorldId, request, params);
    }

    public static void deal(String messageName) {
        deal(messageName, null);
    }

}

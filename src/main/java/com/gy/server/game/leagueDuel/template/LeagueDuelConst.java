package com.gy.server.game.leagueDuel.template;

import com.gy.server.game.leagueDuel.status.DuelNodeType;
import com.gy.server.game.util.StringExtUtil;
import org.apache.commons.lang3.tuple.Pair;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 帮派对决常量表
 *
 * <AUTHOR> - [Created on 2023/5/25 10:51]
 */
public class LeagueDuelConst {

    /**
     * 每个玩家可以攻打几次
     */
    private final int attacksNum;
    /**
     * 第一次攻打失败可以获得几次攻击机会
     */
    private final int failAttacksNum;
    /**
     * 胜利小于x次会获得debuff攻击机会
     */
    private final int deBuffWinAttackNum;
    /**
     * debuff攻击上限等级
     */
    private final int debuffSuperpositionNum;
    /**
     * 破城所有建筑被攻击星数
     */
    private final int cityStarNum;
    /**
     * 每周几开启 1-7
     */
    private final List<Integer> onTimeWeekList;
    /**
     * 节点开启时间
     */
    private final Map<DuelNodeType, Pair<LocalTime, Integer>> onTimeMap = new HashMap<>();

    /**
     * 大于这个天数 开启跨服
     */
    private final int crossServer;
    /**
     * 帮会报名计算的人数的活跃天数
     */
    private final int joinActiveDay;
    /**
     * 帮会总战力计算的人数的活跃天数
     */
    private final int totalFightPowerDay;
    /**
     * 报名所需活跃人数
     */
    private final int joinActiveMember;
    /**
     * 城防军血量
     */
    private final int cityDefenceMaxHp;
    /**
     * 攻击胜利掉多少血量
     */
    private final int cityDefenceAtkDownHp;
    /**
     * 战役表ID
     */
    private final int battleCollectId;
    /**
     * 城防军buffId
     */
    private final int cityDefenceBuffId;
    /**
     * debuff攻击的debuffId
     */
    private final int deBuffId;
    /**
     * 困难模式对应的buffId
     */
    private final int difficultyModeBuff;
    /**
     * 同战力系数 最小值和最大值
     */
    private final double minChallengeSamePower;
    private final double maxChallengeSamePower;
    /**
     * 跨战力系数
     */
    private final double challengeTallPower;
    /**
     * 成功战力系数
     */
    private final double winCoefficient;
    /**
     * 失败战力系数
     */
    private final double failCoefficient;
    /**
     * 是否开启跨服开关
     */
    private final boolean isOpenCross;


    public LeagueDuelConst(Map<String, String> constantMap) {
        this.attacksNum = Integer.parseInt(constantMap.get("attacksNum"));
        this.failAttacksNum = Integer.parseInt(constantMap.get("failAttacksNum"));
        this.cityStarNum = Integer.parseInt(constantMap.get("cityStarNum"));
        this.debuffSuperpositionNum = Integer.parseInt(constantMap.get("debuffSuperpositionNum"));
        this.deBuffWinAttackNum = Integer.parseInt(constantMap.get("debuffWinAttackNum"));
        this.onTimeWeekList = StringExtUtil.string2List(constantMap.get("onTimeWeek"), ",", Integer.class);
        this.onTimeWeekList.sort(Comparator.comparing(Integer::intValue));
        String onTimeStr = constantMap.get("onTime");
        String[] onTimeStrArr = onTimeStr.split(",");
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("HH:mm");
        for (String onTimeTmp : onTimeStrArr) {
            String[] split = onTimeTmp.split("[|]");
            DuelNodeType duelNodeType = DuelNodeType.of(Integer.parseInt(split[0]));
            LocalTime localTime = LocalTime.parse(split[1], dateTimeFormatter);
            int duration = Integer.parseInt(split[2]);
            this.onTimeMap.put(duelNodeType, Pair.of(localTime, duration));
        }
        this.cityDefenceMaxHp = Integer.parseInt(constantMap.get("urbanDefenseBlood"));
        this.cityDefenceAtkDownHp = Integer.parseInt(constantMap.get("urbanDefenseAttackBlood"));
        this.crossServer = Integer.parseInt(constantMap.get("crossServer"));
        this.joinActiveDay = Integer.parseInt(constantMap.get("participateInTime"));
        this.joinActiveMember = Integer.parseInt(constantMap.get("participateInNum"));
        this.totalFightPowerDay = Integer.parseInt(constantMap.get("combatPowerTime"));
        this.battleCollectId = Integer.parseInt(constantMap.get("battleCollectId"));
        this.cityDefenceBuffId = Integer.parseInt(constantMap.get("urbanDefenseBuffId"));
        this.deBuffId = Integer.parseInt(constantMap.get("debuffId"));
        this.difficultyModeBuff = Integer.parseInt(constantMap.get("difficultyModeBuff"));
        String[] challengeSamePowerArr = constantMap.get("challengeSamePower").split("[|]");
        this.minChallengeSamePower = Double.parseDouble(challengeSamePowerArr[0]);
        this.maxChallengeSamePower = Double.parseDouble(challengeSamePowerArr[1]);
        this.challengeTallPower = Double.parseDouble(constantMap.get("challengeTallPower"));
        this.winCoefficient = Double.parseDouble(constantMap.get("winCoefficient"));
        this.failCoefficient = Double.parseDouble(constantMap.get("failCoefficientParameter"));
        this.isOpenCross = Integer.parseInt(constantMap.get("isOpenCross")) == 1;
    }

    public List<Integer> getOnTimeWeekList() {
        return onTimeWeekList;
    }

    public int getAttacksNum() {
        return attacksNum;
    }

    public Map<DuelNodeType, Pair<LocalTime, Integer>> getOnTimeMap() {
        return onTimeMap;
    }

    public int getCrossServer() {
        return crossServer;
    }

    public int getJoinActiveDay() {
        return joinActiveDay;
    }

    public int getJoinActiveMember() {
        return joinActiveMember;
    }

    public int getTotalFightPowerDay() {
        return totalFightPowerDay;
    }

    public int getCityDefenceMaxHp() {
        return cityDefenceMaxHp;
    }

    public int getCityDefenceAtkDownHp() {
        return cityDefenceAtkDownHp;
    }

    public int getBattleCollectId() {
        return battleCollectId;
    }

    public int getCityDefenceBuffId() {
        return cityDefenceBuffId;
    }

    public int getDeBuffId() {
        return deBuffId;
    }

    public int getDifficultyModeBuff() {
        return difficultyModeBuff;
    }

    public int getFailAttacksNum() {
        return failAttacksNum;
    }

    public int getDeBuffWinAttackNum() {
        return deBuffWinAttackNum;
    }

    public int getCityStarNum() {
        return cityStarNum;
    }

    public int getDebuffSuperpositionNum() {
        return debuffSuperpositionNum;
    }

    public double getMinChallengeSamePower() {
        return minChallengeSamePower;
    }

    public double getMaxChallengeSamePower() {
        return maxChallengeSamePower;
    }

    public double getChallengeTallPower() {
        return challengeTallPower;
    }

    public double getWinCoefficient() {
        return winCoefficient;
    }

    public double getFailCoefficient() {
        return failCoefficient;
    }

    public boolean isOpenCross() {
        return isOpenCross;
    }
}

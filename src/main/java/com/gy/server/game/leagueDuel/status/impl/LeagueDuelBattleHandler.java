package com.gy.server.game.leagueDuel.status.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.leagueDuel.LeagueDuelGlobalData;
import com.gy.server.game.leagueDuel.LeagueDuelService;
import com.gy.server.game.leagueDuel.status.DuelNodeType;
import com.gy.server.game.leagueDuel.status.ILeagueDuelStatusHandler;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.DuelStatusType;

import java.time.LocalDateTime;

/**
 * 战斗状态
 *
 * <AUTHOR> - [Created on 2023/5/29 13:19]
 */
public class LeagueDuelBattleHandler implements ILeagueDuelStatusHandler {
    @Override
    public DuelStatusType handleStatus() {
        return DuelStatusType.battle;
    }

    @Override
    public void handler(LeagueDuelGlobalData globalData) {
        LocalDateTime lineupEndTime = getEndTime();
        //等待时间结束
        if (ServerConstants.getCurrentTimeLocalDateTime().isAfter(lineupEndTime)) {
            //设置下个阶段开启时间并切换状态
            changeStatus(DuelStatusType.over, lineupEndTime);
        }
    }

    @Override
    public LocalDateTime getEndTime() {
        Integer duration = LeagueDuelService.getConstant().getOnTimeMap().get(DuelNodeType.battle).getRight();
        LeagueDuelGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueDuel);
        return globalData.getOpenTime().plusMinutes(duration);
    }
}

package com.gy.server.game.leagueDuel.status.impl;

import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.league.LeagueModelEnums;
import com.gy.server.game.leagueDuel.LeagueDuelGlobalData;
import com.gy.server.game.leagueDuel.LeagueDuelHelper;
import com.gy.server.game.leagueDuel.LeagueDuelModel;
import com.gy.server.game.leagueDuel.LeagueDuelService;
import com.gy.server.game.leagueDuel.async.DuelWorldMessageAsync;
import com.gy.server.game.leagueDuel.status.DuelNodeType;
import com.gy.server.game.leagueDuel.status.ILeagueDuelStatusHandler;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.*;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 结算状态
 *
 * <AUTHOR> - [Created on 2023/5/29 13:19]
 */
public class LeagueDuelOverHandler implements ILeagueDuelStatusHandler {
    @Override
    public DuelStatusType handleStatus() {
        return DuelStatusType.over;
    }

    @Override
    public void handler(LeagueDuelGlobalData globalData) {
        Map<Long, Long> matchMap = globalData.getMatchMap();
        DuelOverResultMapInfo duelOverResultMapInfo = new DuelOverResultMapInfo();
        for (Long leagueId : matchMap.keySet()) {
            //结算结果数据
            DuelOverResultInfo duelOverResultInfo = new DuelOverResultInfo();
            duelOverResultInfo.setLeagueId(leagueId);
            League league = LeagueManager.getLeagueById(leagueId);
            LeagueDuelModel duelModel = league.getModel(LeagueModelEnums.duel);
            //生成本届的数据
            LeagueDuelRankNodeInfo leagueDuelRankNodeInfo = LeagueDuelHelper.newLeagueDuelRankNodeInfo(league);
            duelOverResultInfo.setSimpleDuelInfo(leagueDuelRankNodeInfo);
            //上次匹配的公会ID
            duelModel.setLastMatchLeagueId(duelModel.getLastMatchLeagueId());
            //星级和星级的更新时间
            duelOverResultInfo.setStar(duelModel.getTotalStar());
            duelOverResultInfo.setLastStarUpdateTime(duelModel.getLastAddStarTime());
            duelOverResultMapInfo.getResultInfoMap().put(leagueId, duelOverResultInfo);
        }
        //统计完毕 跨服玩法上传数据
        if (globalData.isCross()) {
            Object[] params = new Object[1];
            params[0] = PbUtilCompress.encode(duelOverResultMapInfo);
            DuelWorldMessageAsync.deal("LeagueDuelWorldCommandService.uploadDuelResult", params);
        } else {
            Map<Long, DuelOverResultInfo> resultInfoMap = duelOverResultMapInfo.getResultInfoMap();
            Set<Long> finishLeagueSet = new HashSet<>();
            for (DuelOverResultInfo duelOverResultInfo : resultInfoMap.values()) {
                long leagueId = duelOverResultInfo.getLeagueId();
                if (finishLeagueSet.contains(leagueId)) {
                    continue;
                }
                Long targetLeagueId = globalData.getMatchMap().get(leagueId);
                DuelOverResultInfo targetResultInfo = resultInfoMap.get(targetLeagueId);
                //左方是否胜利
                boolean leftWin = isLeftWin(duelOverResultInfo, targetResultInfo);
                League league = LeagueManager.getLeagueById(leagueId);
                LeagueDuelModel duelModel = league.getModel(LeagueModelEnums.duel);
                duelModel.overFinish(leftWin);
                //上一场数据
                DuelLastMainInfo duelLastMainInfo = LeagueDuelHelper.newDuelLastMainInfo(league, targetResultInfo.getSimpleDuelInfo());
                duelModel.setLastMainInfo(duelLastMainInfo);
                League targetLeague = LeagueManager.getLeagueById(targetLeagueId);
                LeagueDuelModel targetDuelModel = targetLeague.getModel(LeagueModelEnums.duel);
                targetDuelModel.overFinish(!leftWin);
                //上一场数据
                DuelLastMainInfo tarLastMainInfo = LeagueDuelHelper.newDuelLastMainInfo(targetLeague, duelOverResultInfo.getSimpleDuelInfo());
                targetDuelModel.setLastMainInfo(tarLastMainInfo);
                finishLeagueSet.add(leagueId);
                finishLeagueSet.add(targetLeagueId);
                //添加历史战报
                LeagueDuelHelper.combatHistoryRecord(league, targetResultInfo, leftWin);
                LeagueDuelHelper.combatHistoryRecord(targetLeague, duelOverResultInfo, !leftWin);
            }
        }
        //切换待发奖状态
        changeStatus(DuelStatusType.beforeSendReward);
    }

    /**
     * 是否左方胜利
     * 先比较星级，星级相同比较星级更新时间，星级更新时间相同比较公会ID
     *
     * @param left  左方
     * @param right 右方
     * @return true 左方胜利
     */
    private boolean isLeftWin(DuelOverResultInfo left, DuelOverResultInfo right) {
        int leftStar = left.getStar();
        int rightStar = right.getStar();
        if (leftStar == rightStar) {
            long leftLastStarUpdateTime = left.getLastStarUpdateTime();
            long rightLastStarUpdateTime = right.getLastStarUpdateTime();
            if (leftLastStarUpdateTime == rightLastStarUpdateTime) {
                long leftLeagueId = left.getLeagueId();
                long rightLeagueId = right.getLeagueId();
                return leftLeagueId > rightLeagueId;
            } else {
                return leftLastStarUpdateTime < rightLastStarUpdateTime;
            }
        } else {
            return leftStar > rightStar;
        }
    }

    @Override
    public LocalDateTime getEndTime() {
        Integer duration = LeagueDuelService.getConstant().getOnTimeMap().get(DuelNodeType.over).getRight();
        LeagueDuelGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueDuel);
        return globalData.getOpenTime().plusMinutes(duration);
    }
}

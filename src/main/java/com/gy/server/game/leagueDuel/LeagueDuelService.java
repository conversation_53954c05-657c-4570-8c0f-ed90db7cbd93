package com.gy.server.game.leagueDuel;

import com.gy.server.core.callback.response.CallbackResponse;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.combat.CombatManager;
import com.gy.server.game.combat.Stage;
import com.gy.server.game.common.ConstantConfigReader;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.function.Function;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.league.LeagueModelEnums;
import com.gy.server.game.league.bean.LeagueMember;
import com.gy.server.game.league.enums.LeagueJobTypeEnums;
import com.gy.server.game.leagueDuel.stage.LeagueDuelStage;
import com.gy.server.game.leagueDuel.status.LeagueDuelStatusManager;
import com.gy.server.game.leagueDuel.template.*;
import com.gy.server.game.liberty.LibertyHelper;
import com.gy.server.game.liberty.effect.LibertyType;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.service.Service;
import com.gy.server.game.text.Text;
import com.gy.server.game.util.StringExtUtil;
import com.gy.server.packet.PbLeague;
import com.gy.server.packet.PbLeagueDuel;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.rpc.TLMessageCallbackTask;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.DuelCityPointInfo;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.DuelLastMainInfo;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.DuelStatusType;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.PersonRewardBean;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 帮派对决服务类（逐鹿战场）
 *
 * <AUTHOR> - [Created on 2023/5/24 17:52]
 */
public class LeagueDuelService extends PlayerPacketHandler implements Service {

    /**
     * 点位信息 Key1=cityId  Key2=pointId
     */
    private static Map<Integer, Map<Integer, LeagueDuelPointTemplate>> duelPointTemplateMap = new HashMap<>();
    private static Map<Integer, LeagueDuelPointTemplate> duelPointIdTemplateMap = new HashMap<>();
    /**
     * 城市内据点类型的数量
     */
    private static Map<Integer, Integer> duelPointStrongholdNumMap = new HashMap<>();
    /**
     * 城池对应的阵眼
     * Key1=cityId  Key2=阵眼ID
     */
    private static Map<Integer, Integer> duelCityEyesMap = new HashMap<>();
    /**
     * 城池对应的城防军
     * Key1=cityId  Key2=城防军Id
     */
    private static Map<Integer, Integer> duelCityDefenceMap = new HashMap<>();
    /**
     * 城市信息
     */
    private static Map<Integer, LeagueDuelCityTemplate> duelCityTemplateMap = new HashMap<>();
    /**
     * 奖励信息
     */
    private static Map<DuelReceiveRewardType, LeagueDuelRewardTemplate> duelRewardTemplateTypeMap = new HashMap<>();
    private static Map<Integer, LeagueDuelRewardTemplate> duelRewardTemplateMap = new HashMap<>();
    /**
     * 宝箱奖励
     */
    private static Map<Integer, LeagueDuelChestTemplate> duelChestTemplateMap = new HashMap<>();
    /**
     * 城市关联的宝箱ID
     */
    private static Map<Integer, List<Integer>> cityChestRewardMap = new HashMap<>();
    /**
     * 常量类
     */
    private static LeagueDuelConst constant;
    /**
     * 次数奖励
     */
    private static Map<Integer, List<RewardTemplate>> timesRewards = new HashMap<>();;


    /**
     * 帮派对决主界面信息
     */
    @Handler(PtCode.DUEL_MAIN_INFO_CLIENT)
    private void mainInfo(Player player, long time) {
        PbProtocol.DuelMainInfoRst.Builder rst = PbProtocol.DuelMainInfoRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        logic:
        {
            if(!Function.leagueDuel.isOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }

            if (LeagueManager.isNotJoinLeague(player)) {
                rst.setResult(Text.genServerRstInfo(Text.未加入帮派));
                break logic;
            }
            if(LeagueManager.canNotPlayLeague(player)){
                rst.setResult(Text.genServerRstInfo(Text.您退出帮派未满足24小时无法参与帮派活动));
                break logic;
            }
            LeagueDuelGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueDuel);
            LocalDateTime endTime = LeagueDuelStatusManager.getInstance().getHandler(globalData.getStatus()).getEndTime();
            League league = LeagueManager.getLeagueByPlayer(player);
            boolean isJoin = isJoin(league);
            LeagueDuelModel duelModel = league.getModel(LeagueModelEnums.duel);
            //未达到布阵阶段或者未加入本次帮派对决 去查找上一场的数据
            if (globalData.getStatus().getType() < DuelStatusType.beforeLineup.getType() || !isJoin) {
                DuelLastMainInfo lastMainInfo = duelModel.getLastMainInfo();
                PbLeagueDuel.LeagueDuelMainInfo.Builder mainBuilder = PbLeagueDuel.LeagueDuelMainInfo.newBuilder()
                        .setStatus(globalData.getStatus().getType())
                        .setStatusEndTime(DateTimeUtil.toMillis(endTime));
                if (Objects.nonNull(lastMainInfo)) {

                    mainBuilder.setMyLeagueDuel(lastMainInfo.getMyLeagueInfo().genMiniLeagueDuelPb())
                            .setTargetLeagueDuel(lastMainInfo.getTargetLeagueInfo().genMiniLeagueDuelPb())
                            .setIsWin(lastMainInfo.isWin());
                }
                rst.setDuelMainInfo(mainBuilder)
                        .setRemainDeBuffNum(0)
                        .setRemainFightNum(0);
            } else {
                PbLeagueDuel.MiniLeagueDuel myDuelPb = duelModel.genMiniLeagueDuelPb();
                PbLeagueDuel.LeagueDuelMainInfo.Builder mainBuilder = PbLeagueDuel.LeagueDuelMainInfo.newBuilder()
                        .setMyLeagueDuel(myDuelPb)
                        .setStatus(globalData.getStatus().getType())
                        .setStatusEndTime(DateTimeUtil.toMillis(endTime));
                //超过战斗阶段才会显示胜利和失败
                if (globalData.getStatus().getType() > DuelStatusType.battle.getType()) {
                    mainBuilder.setIsWin(duelModel.isWin());
                }
                Long targetLeagueId = globalData.getMatchMap().get(league.getLeagueId());
                if (globalData.isCross()) {
                    int targetServerId = Player.getRealServerId(targetLeagueId);
                    ServerCommandRequest request = CommandRequests.newServerCommandRequest("LeagueDuelGameCommandService.mainInfo");
                    TLBase.getInstance().getRpcUtil().sendToNodeWithCallBack(new MainInfoCallbackTask(mainBuilder, rst, player, time), ServerType.GAME, targetServerId, request, targetLeagueId);
                    return;
                } else {
                    League targetLeague = LeagueManager.getLeagueById(targetLeagueId);
                    LeagueDuelModel targetDuelModel = targetLeague.getModel(LeagueModelEnums.duel);
                    PbLeagueDuel.MiniLeagueDuel targetDuelPb = targetDuelModel.genMiniLeagueDuelPb();
                    mainBuilder.setTargetLeagueDuel(targetDuelPb);
                    rst.setDuelMainInfo(mainBuilder);
                }
                rst.setRemainFightNum(duelModel.getRemainFightNum(player.getPlayerId()))
                        .setRemainDeBuffNum(duelModel.getDeBuffNum(player.getPlayerId()));
            }
            rst.addAllHadReceiveChessRewards(duelModel.getHadReceiveChessRewards().getOrDefault(player.getPlayerId(), new HashSet<>()));
            rst.addAllHadReceiveTimesRewards(duelModel.getHadReceiveTimesRewards().getOrDefault(player.getPlayerId(), new HashSet<>()));

        }
        player.send(PtCode.DUEL_MAIN_INFO_SERVER, rst.build(), time);
    }

    /**
     * 是否报名
     */
    private boolean isJoin(League league) {
        LeagueDuelGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueDuel);
        return globalData.getJoinLeagueSet().contains(league.getLeagueId());
    }

    /**
     * 帮派对决进入战场
     */
    @Handler(PtCode.DUEL_JOIN_BATTLEFIELD_CLIENT)
    private void join(Player player, long time) throws Exception {
        PbProtocol.DuelJoinBattlefieldRst.Builder rst = PbProtocol.DuelJoinBattlefieldRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        logic:
        {
            if(!Function.leagueDuel.isOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }

            if (LeagueManager.isNotJoinLeague(player)) {
                rst.setResult(Text.genServerRstInfo(Text.未加入帮派));
                break logic;
            }
            LeagueDuelGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueDuel);
            //判断当前阶段是战斗阶段才可以进入战场
            if (globalData.getStatus().getType() < DuelStatusType.beforeBattle.getType()) {
                rst.setResult(Text.genServerRstInfo(Text.帮派对决未达到战斗阶段));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            if (!isJoin(league)) {
                rst.setResult(Text.genServerRstInfo(Text.帮派对决未报名));
                break logic;
            }
            //加入战场标志
            LeagueDuelModel duelModel = league.getModel(LeagueModelEnums.duel);
            duelModel.getBattlefieldPlayerSet().add(player.getPlayerId());
            //获取敌方数据
            Long targetLeagueId = globalData.getMatchMap().get(league.getLeagueId());
            //战场Pb
            PbLeagueDuel.LeagueDuelBattlefieldInfo.Builder battleInfoBuilder = PbLeagueDuel.LeagueDuelBattlefieldInfo.newBuilder()
                    .addAllMyCityInfos(duelModel.genLeagueDuelCityInfoListPb())
                    .setFireCityId(duelModel.getFireCityId())
                    .setFirePointId(duelModel.getFirePointId())
                    .setMyStar(duelModel.getTotalStar());
            if (globalData.isCross()) {
                int targetServerId = Player.getRealServerId(targetLeagueId);
                ServerCommandRequest request = CommandRequests.newServerCommandRequest("LeagueDuelGameCommandService.getCityInfo");
                TLBase.getInstance().getRpcUtil().sendToNodeWithCallBack(new JoinCallbackTask(battleInfoBuilder, rst, player, time), ServerType.GAME, targetServerId, request, targetLeagueId);
                return;
            } else {
                League targetLeague = LeagueManager.getLeagueById(targetLeagueId);
                LeagueDuelModel tarDuelModel = targetLeague.getModel(LeagueModelEnums.duel);
                List<PbLeagueDuel.LeagueDuelCityInfo> leagueDuelCityInfoList = tarDuelModel.genLeagueDuelCityInfoListPb();
                battleInfoBuilder.addAllTargetCityInfos(leagueDuelCityInfoList)
                        .setTarStar(tarDuelModel.getTotalStar());
                rst.setBattlefieldInfo(battleInfoBuilder);
            }
        }
        player.send(PtCode.DUEL_JOIN_BATTLEFIELD_SERVER, rst.build(), time);
    }

    /**
     * 帮派对决退出战场
     */
    @Handler(PtCode.DUEL_QUIT_BATTLEFIELD_CLIENT)
    private void quit(Player player, long time) throws Exception {
        PbProtocol.DuelQuitBattlefieldRst.Builder rst = PbProtocol.DuelQuitBattlefieldRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        logic:
        {
            if(!Function.leagueDuel.isOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }

            if (LeagueManager.isNotJoinLeague(player)) {
                rst.setResult(Text.genServerRstInfo(Text.未加入帮派));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            //退出战场标志
            LeagueDuelModel duelModel = league.getModel(LeagueModelEnums.duel);
            duelModel.getBattlefieldPlayerSet().remove(player.getPlayerId());
        }
        player.send(PtCode.DUEL_QUIT_BATTLEFIELD_SERVER, rst.build(), time);
    }

    /**
     * 帮派对决城市据点信息
     */
    @Handler(PtCode.DUEL_CITY_POINT_INFO_CLIENT)
    private void cityPointInfo(Player player, PbProtocol.DuelCityPointInfoReq req, long time) throws Exception {
        PbProtocol.DuelCityPointInfoRst.Builder rst = PbProtocol.DuelCityPointInfoRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        int cityId = req.getCityId();
        boolean isMyCity = req.getIsMyCity();
        logic:
        {
            if(!Function.leagueDuel.isOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }

            if (!duelCityTemplateMap.containsKey(cityId)) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            if (LeagueManager.isNotJoinLeague(player)) {
                rst.setResult(Text.genServerRstInfo(Text.未加入帮派));
                break logic;
            }
            LeagueDuelGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueDuel);
            //判断当前阶段是战斗阶段才可以查看据点信息
            if (globalData.getStatus().getType() < DuelStatusType.beforeBattle.getType()) {
                rst.setResult(Text.genServerRstInfo(Text.帮派对决未达到战斗阶段));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            if (!isJoin(league)) {
                rst.setResult(Text.genServerRstInfo(Text.帮派对决未报名));
                break logic;
            }
            LeagueDuelModel duelModel = league.getModel(LeagueModelEnums.duel);
            rst.setMyStar(duelModel.getTotalStar());
            if (isMyCity) {
                rst.addAllCityPointInfo(duelModel.genLeagueDuelCityPointInfoListPb(cityId));
            }
            //获取敌方数据
            Long targetLeagueId = globalData.getMatchMap().get(league.getLeagueId());
            if (globalData.isCross()) {
                int targetServerId = Player.getRealServerId(targetLeagueId);
                ServerCommandRequest request = CommandRequests.newServerCommandRequest("LeagueDuelGameCommandService.getCityPointInfo");
                TLBase.getInstance().getRpcUtil().sendToNodeWithCallBack(new CityPointInfoCallbackTask(isMyCity, rst, player, time), ServerType.GAME, targetServerId, request, targetLeagueId, cityId, isMyCity);
                return;
            } else {
                League targetLeague = LeagueManager.getLeagueById(targetLeagueId);
                LeagueDuelModel tarDuelModel = targetLeague.getModel(LeagueModelEnums.duel);
                if (!isMyCity) {
                    List<PbLeagueDuel.LeagueDuelCityPointInfo> leagueDuelCityInfoList = tarDuelModel.genLeagueDuelCityPointInfoListPb(cityId);
                    rst.addAllCityPointInfo(leagueDuelCityInfoList);
                }
                rst.setTarStar(tarDuelModel.getTotalStar());
            }
        }
        player.send(PtCode.DUEL_CITY_POINT_INFO_SERVER, rst.build(), time);
    }

    @Handler(PtCode.DUEL_CITY_POINT_INFO_MUTI_REQ)
    private void cityPointInfoMuti(Player player, PbProtocol.DuelCityPointInfoMutiReq req, long time) throws Exception {
        PbProtocol.DuelCityPointInfoMutiRst.Builder rst = PbProtocol.DuelCityPointInfoMutiRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        Set<Integer> cityIds = new HashSet<>(req.getCityIdsList());
        logic:
        {
            if(!Function.leagueDuel.isOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            if(CollectionUtil.isEmpty(cityIds)){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            for (Integer cityId : cityIds) {
                if (!duelCityTemplateMap.containsKey(cityId)) {
                    rst.setResult(Text.genServerRstInfo(Text.参数异常));
                    break logic;
                }
            }
            if (LeagueManager.isNotJoinLeague(player)) {
                rst.setResult(Text.genServerRstInfo(Text.未加入帮派));
                break logic;
            }
            LeagueDuelGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueDuel);
            //判断当前阶段是战斗阶段才可以查看据点信息
            if (globalData.getStatus().getType() < DuelStatusType.beforeBattle.getType()) {
                rst.setResult(Text.genServerRstInfo(Text.帮派对决未达到战斗阶段));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            if (!isJoin(league)) {
                rst.setResult(Text.genServerRstInfo(Text.帮派对决未报名));
                break logic;
            }
            //获取敌方数据
            Long targetLeagueId = globalData.getMatchMap().get(league.getLeagueId());
            if (globalData.isCross()) {
                int targetServerId = Player.getRealServerId(targetLeagueId);
                ServerCommandRequest request = CommandRequests.newServerCommandRequest("LeagueDuelGameCommandService.cityPointInfoMuti");
                TLBase.getInstance().getRpcUtil().sendToNodeWithCallBack(new CityPointInfoMutiCallbackTask(rst, player, time), ServerType.GAME, targetServerId, request, targetLeagueId, cityIds);
                return;
            } else {
                League targetLeague = LeagueManager.getLeagueById(targetLeagueId);
                LeagueDuelModel tarDuelModel = targetLeague.getModel(LeagueModelEnums.duel);
                for (Integer cityId : cityIds) {
                    List<PbLeagueDuel.LeagueDuelCityPointInfo> leagueDuelCityInfoList = tarDuelModel.genLeagueDuelCityPointInfoListPb(cityId);
                    rst.addAllCityPointInfo(leagueDuelCityInfoList);
                }
                rst.setTarStar(tarDuelModel.getTotalStar());
            }
        }
        player.send(PtCode.DUEL_CITY_POINT_INFO_MUTI_RST, rst.build(), time);
    }


    /**
     * 帮派对决据点布阵信息
     */
    @Handler(PtCode.DUEL_POINT_LINEUP_CLIENT)
    private void pointLineup(Player player, PbProtocol.DuelPointLineupReq req, long time) throws Exception {
        PbProtocol.DuelPointLineupRst.Builder rst = PbProtocol.DuelPointLineupRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        int cityId = req.getCityId();
        int pointId = req.getPointId();
        boolean isMyCity = req.getIsMyCity();
        logic:
        {
            if(!Function.leagueDuel.isOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }

            if (!duelCityTemplateMap.containsKey(cityId)) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            Map<Integer, LeagueDuelPointTemplate> pointTemplateMap = duelPointTemplateMap.get(cityId);
            if (!pointTemplateMap.containsKey(pointId)) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            if (LeagueManager.isNotJoinLeague(player)) {
                rst.setResult(Text.genServerRstInfo(Text.未加入帮派));
                break logic;
            }
            LeagueDuelGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueDuel);
            //判断当前阶段是战斗阶段才可以查看据点信息
            if (globalData.getStatus().getType() < DuelStatusType.beforeBattle.getType()) {
                rst.setResult(Text.genServerRstInfo(Text.帮派对决未达到战斗阶段));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            if (!isJoin(league)) {
                rst.setResult(Text.genServerRstInfo(Text.帮派对决未报名));
                break logic;
            }
            LeagueDuelModel duelModel = league.getModel(LeagueModelEnums.duel);
            if (isMyCity) {
                rst.setLineupData(duelModel.genLeagueDuelPointLineupInfoPb(cityId, pointId));
                break logic;
            }
            //获取敌方数据
            Long targetLeagueId = globalData.getMatchMap().get(league.getLeagueId());
            if (globalData.isCross()) {
                int targetServerId = Player.getRealServerId(targetLeagueId);
                ServerCommandRequest request = CommandRequests.newServerCommandRequest("LeagueDuelGameCommandService.getPointLineupInfo");
                TLBase.getInstance().getRpcUtil().sendToNodeWithCallBack(new PointLineupCallbackTask(rst, player, time), ServerType.GAME, targetServerId, request, targetLeagueId, cityId, pointId);
                return;
            } else {
                League targetLeague = LeagueManager.getLeagueById(targetLeagueId);
                LeagueDuelModel tarDuelModel = targetLeague.getModel(LeagueModelEnums.duel);
                PbLeagueDuel.DuelPointLineup duelPointLineup = tarDuelModel.genLeagueDuelPointLineupInfoPb(cityId, pointId);
                rst.setLineupData(duelPointLineup);
            }
        }
        player.send(PtCode.DUEL_POINT_LINEUP_SERVER, rst.build(), time);
    }

    /**
     * 集火标志
     */
    @Handler(PtCode.DUEL_FIRE_SET_CLIENT)
    private void fireSet(Player player, PbProtocol.DuelCityFireReq req, long time) throws Exception {
        PbProtocol.DuelCityFireRst.Builder rst = PbProtocol.DuelCityFireRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        int cityId = req.getCityId();
        int pointId = req.getPointId();
        logic:
        {
            if(!Function.leagueDuel.isOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            if (LeagueManager.isNotJoinLeague(player)) {
                rst.setResult(Text.genServerRstInfo(Text.未加入帮派));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            if (!isJoin(league)) {
                rst.setResult(Text.genServerRstInfo(Text.帮派对决未报名));
                break logic;
            }
            //判断是不是会长和副会长
            if (!league.hasAuth(player.getPlayerId(), LeagueJobTypeEnums.玩法指挥)) {
                rst.setResult(Text.genServerRstInfo(Text.集火权限不足));
                break logic;
            }

            LeagueDuelGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueDuel);
            //判断当前阶段是战斗阶段才可以查看据点信息
            if (globalData.getStatus().getType() < DuelStatusType.beforeBattle.getType()) {
                rst.setResult(Text.genServerRstInfo(Text.帮派对决未达到战斗阶段));
                break logic;
            }
            LeagueDuelModel duelModel = league.getModel(LeagueModelEnums.duel);
            if(cityId > 0){
                if (!duelCityTemplateMap.containsKey(cityId)) {
                    rst.setResult(Text.genServerRstInfo(Text.参数异常));
                    break logic;
                }
                //设置集火标志
                duelModel.setFireCityId(cityId);
                duelModel.syncFireSync(cityId, duelModel.getFirePointId());
            }else{
                if (!duelPointIdTemplateMap.containsKey(pointId)) {
                    rst.setResult(Text.genServerRstInfo(Text.参数异常));
                    break logic;
                }
                duelModel.setFirePointId(pointId);
                duelModel.syncFireSync(duelModel.getFireCityId(), pointId);
            }

        }
        player.send(PtCode.DUEL_FIRE_SET_SERVER, rst.build(), time);
    }

    /**
     * 战斗
     */
    @Handler(PtCode.DUEL_FIGHT_CLIENT)
    private void fight(Player player, PbProtocol.DuelFightReq req, long time) throws Exception {
        PbProtocol.DuelFightRst.Builder rst = PbProtocol.DuelFightRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        int cityId = req.getCityId();
        int pointId = req.getPointId();
        int diff = req.getDiff();
        LeagueDuelStage leagueDuelStage = null;
        logic:
        {
            if(!Function.leagueDuel.isOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }

            if (!duelCityTemplateMap.containsKey(cityId)) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            Map<Integer, LeagueDuelPointTemplate> pointTemplateMap = duelPointTemplateMap.get(cityId);
            if (!pointTemplateMap.containsKey(pointId)) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            if (diff != 1 && diff != 2) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            if (LeagueManager.isNotJoinLeague(player)) {
                rst.setResult(Text.genServerRstInfo(Text.未加入帮派));
                break logic;
            }
            LeagueDuelGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueDuel);
            //判断当前阶段是战斗阶段才可以查看据点信息
            if (globalData.getStatus().getType() < DuelStatusType.beforeBattle.getType()) {
                rst.setResult(Text.genServerRstInfo(Text.帮派对决未达到战斗阶段));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            if (!isJoin(league)) {
                rst.setResult(Text.genServerRstInfo(Text.帮派对决未报名));
                break logic;
            }
            LeagueDuelModel duelModel = league.getModel(LeagueModelEnums.duel);
            //判断攻击次数是否足够
            if (duelModel.isNotEnoughAttack(player.getPlayerId())) {
                rst.setResult(Text.genServerRstInfo(Text.城池攻击次数不足));
                break logic;
            }
            if (LineupType.duelAttack.nullCheck(player)) {
                rst.setResult(Text.genServerRstInfo(Text.帮派对决未布阵进攻阵容));
                break logic;
            }
            LeagueDuelPointTemplate pointTemplate = pointTemplateMap.get(pointId);
            if (duelModel.isFighting(cityId, pointId)) {
                rst.setResult(Text.genServerRstInfo(Text.据点正在被攻打));
                break logic;
            }
            Stage stage = CombatManager.getStageByPlayer(player);
            if (Objects.nonNull(stage)) {
                rst.setResult(Text.genServerRstInfo(Text.据点正在被攻打));
                break logic;
            }
            Long targetLeagueId = globalData.getMatchMap().get(league.getLeagueId());
            if (globalData.isCross()) {
                ServerCommandRequest request = CommandRequests.newServerCommandRequest("LeagueDuelGameCommandService.fightReq");
                TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, Player.getRealServerId(targetLeagueId), request, targetLeagueId, cityId, pointId, player.getPlayerId(), time);
                return;
            } else {
                League targetLeague = LeagueManager.getLeagueById(targetLeagueId);
                LeagueDuelModel targetDuelModel = targetLeague.getModel(LeagueModelEnums.duel);
                //判断前置的城市是否攻破
                if (targetDuelModel.isNotPassPreCity(cityId)) {
                    rst.setResult(Text.genServerRstInfo(Text.前置城池未攻破));
                    break logic;
                }
                DuelCityPointInfo duelCityPointInfo = targetDuelModel.getDuelCityPointInfo(cityId, pointId);
                if (pointTemplate.pointType == DuelPointType.CityDefense) {
                    if (duelCityPointInfo.getCityDefenceHp() <= 0) {
                        rst.setResult(Text.genServerRstInfo(Text.城防军已被攻破));
                        break logic;
                    }
                } else {
                    int diffStar = pointTemplate.getDiffStar(diff);
                    if (duelCityPointInfo.getStar() >= diffStar) {
                        rst.setResult(Text.genServerRstInfo(Text.据点该难度已攻破));
                        break logic;
                    }
                }
                duelModel.addFighting(cityId, pointId);
                //进入战斗
                try {
                    leagueDuelStage = new LeagueDuelStage(player, targetLeagueId, duelCityPointInfo, diff,
                            targetDuelModel.getCityDefenceBuffLv(cityId), targetDuelModel.isEyesBreak(cityId));
                    leagueDuelStage.init();
                    rst.setStage(leagueDuelStage.getStageRecord());
                } catch (Exception e) {
                    e.printStackTrace();
                    duelModel.removeFighting(cityId, pointId);
                }
            }
        }
        player.send(PtCode.DUEL_FIGHT_SERVER, rst.build(), time);
        if (Objects.nonNull(leagueDuelStage)) {
            CombatManager.combatPrepare(leagueDuelStage);
        }
    }

    /**
     * Debuff攻击
     */
    @Handler(PtCode.DUEL_DeBuff_ATK_CLIENT)
    private void deBuffAtk(Player player, PbProtocol.DuelDeBuffAtkReq req, long time) throws Exception {
        PbProtocol.DuelDeBuffAtkRst.Builder rst = PbProtocol.DuelDeBuffAtkRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        int cityId = req.getCityId();
        int pointId = req.getPointId();
        logic:
        {
            if(!Function.leagueDuel.isOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }

            if (!duelCityTemplateMap.containsKey(cityId)) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            Map<Integer, LeagueDuelPointTemplate> pointTemplateMap = duelPointTemplateMap.get(cityId);
            if (!pointTemplateMap.containsKey(pointId)) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            if (LeagueManager.isNotJoinLeague(player)) {
                rst.setResult(Text.genServerRstInfo(Text.未加入帮派));
                break logic;
            }
            LeagueDuelGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueDuel);
            //判断当前阶段是战斗阶段才可以查看据点信息
            if (globalData.getStatus().getType() < DuelStatusType.beforeBattle.getType()) {
                rst.setResult(Text.genServerRstInfo(Text.帮派对决未达到战斗阶段));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            if (!isJoin(league)) {
                rst.setResult(Text.genServerRstInfo(Text.帮派对决未报名));
                break logic;
            }
            LeagueDuelModel duelModel = league.getModel(LeagueModelEnums.duel);
            //判断前置的城市是否攻破
            if (duelModel.isNotPassPreCity(cityId)) {
                rst.setResult(Text.genServerRstInfo(Text.前置城池未攻破));
                break logic;
            }
            //判断攻击次数是否足够
            if (duelModel.getDeBuffNum(player.getPlayerId()) <= 0) {
                rst.setResult(Text.genServerRstInfo(Text.deBuff攻击次数不足));
                break logic;
            }
            LeagueDuelPointTemplate pointTemplate = pointTemplateMap.get(pointId);
            if (pointTemplate.pointType == DuelPointType.CityDefense) {
                rst.setResult(Text.genServerRstInfo(Text.城防军不能deBuff攻击));
                break logic;
            }
            Long targetLeagueId = globalData.getMatchMap().get(league.getLeagueId());
            if (globalData.isCross()) {
                ServerCommandRequest request = CommandRequests.newServerCommandRequest("LeagueDuelGameCommandService.deBuffAtk");
                TLBase.getInstance().getRpcUtil().sendToNodeWithCallBack(new DeBuffAtkCallbackTask(rst, duelModel, player, cityId, pointId, time), ServerType.GAME, Player.getRealServerId(targetLeagueId), request, targetLeagueId, cityId, pointId);
                return;
            } else {
                League targetLeague = LeagueManager.getLeagueById(targetLeagueId);
                LeagueDuelModel targetDuelModel = targetLeague.getModel(LeagueModelEnums.duel);
                DuelCityPointInfo duelCityPointInfo = targetDuelModel.getDuelCityPointInfo(cityId, pointId);
                if (duelCityPointInfo.getStar() >= pointTemplate.difficultyStar) {
                    rst.setResult(Text.genServerRstInfo(Text.据点所有难度已攻破));
                    break logic;
                }
                int deBuffLv = duelCityPointInfo.getDeBuffLv();
                if (deBuffLv >= constant.getDebuffSuperpositionNum()) {
                    rst.setResult(Text.genServerRstInfo(Text.该据点deBuff等级已达上限));
                    break logic;
                }
                duelModel.costDeBuffNum(player.getPlayerId());
                duelCityPointInfo.setDeBuffLv(deBuffLv + 1);
                targetDuelModel.syncPointSync(true, duelCityPointInfo);
                // 给我方同步数据
                duelModel.syncPointSync(false, duelCityPointInfo);
                rst.setCityPointInfo(duelCityPointInfo.genPb(duelModel.isFighting(cityId, pointId)));
                rst.setRemainDeBuffNum(duelModel.getDeBuffNum(player.getPlayerId()));
            }
        }
        player.send(PtCode.DUEL_DeBuff_ATK_SERVER, rst.build(), time);
    }

    /**
     * 奖励领取
     */
    @Handler(PtCode.DUEL_REWARD_CLIENT)
    private void rewardDraw(Player player, PbProtocol.DuelRewardReq req, long time) throws Exception {
        PbProtocol.DuelRewardRst.Builder rst = PbProtocol.DuelRewardRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        int rewardId = req.getReward();
        logic:
        {
            if(!Function.leagueDuel.isOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }

            LeagueDuelRewardTemplate duelRewardTemplate = duelRewardTemplateMap.get(rewardId);
            if (Objects.isNull(duelRewardTemplate)) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            if (duelRewardTemplate.receiveRewardType == DuelReceiveRewardType.teamWin
                    || duelRewardTemplate.receiveRewardType == DuelReceiveRewardType.teamLose) {
                rst.setResult(Text.genServerRstInfo(Text.团队奖励不可以手动领取));
                break logic;
            }
            if (LeagueManager.isNotJoinLeague(player)) {
                rst.setResult(Text.genServerRstInfo(Text.未加入帮派));
                break logic;
            }
            LeagueDuelGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueDuel);
            if (globalData.getStatus() != DuelStatusType.battle) {
                rst.setResult(Text.genServerRstInfo(Text.不在领奖的阶段));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            LeagueDuelModel duelModel = league.getModel(LeagueModelEnums.duel);
            PersonRewardBean personRewardBean = duelModel.getPersonRewardBeanMap().get(player.getPlayerId());
            if (Objects.isNull(personRewardBean)) {
                rst.setResult(Text.genServerRstInfo(Text.未达到领取奖励的条件));
                break logic;
            }
            Map<Integer, Integer> rewardStatusMap = personRewardBean.getRewardStatusMap();
            Integer rewardStatus = rewardStatusMap.getOrDefault(rewardId, PersonRewardBean.STATUS_UNFINISHED);
            if (rewardStatus == PersonRewardBean.STATUS_RECEIVED) {
                rst.setResult(Text.genServerRstInfo(Text.个人奖励已经领取));
                break logic;
            }
            if (rewardStatus == PersonRewardBean.STATUS_UNFINISHED) {
                rst.setResult(Text.genServerRstInfo(Text.未达到领取奖励的条件));
                break logic;
            }
            rewardStatusMap.put(rewardId, PersonRewardBean.STATUS_RECEIVED);
            //发奖
            List<Reward> rewardList = Reward.templateCollectionToReward(duelRewardTemplate.rewardList);
//            Reward.addFromTemplates(duelRewardTemplate.rewardList, player, BehaviorType.leagueDuelPersonReward, false);

            //特权
            LibertyHelper.checkCurrencyRewardLiberty(LibertyType.leagueRate, player, rewardList);

            Reward.add(rewardList, player, BehaviorType.leagueDuelPersonReward);
            rst.addAllRewards(Reward.writeCollectionToPb(rewardList));
        }
        player.send(PtCode.DUEL_REWARD_SERVER, rst.build(), time);
    }

    /**
     * 个人奖励列表
     */
    @Handler(PtCode.DUEL_REWARD_LIST_CLIENT)
    private void rewardList(Player player, long time) throws Exception {
        PbProtocol.DuelRewardListRst.Builder rst = PbProtocol.DuelRewardListRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        logic:
        {
            if(!Function.leagueDuel.isOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }

            if (LeagueManager.isNotJoinLeague(player)) {
                rst.setResult(Text.genServerRstInfo(Text.未加入帮派));
                break logic;
            }
            LeagueDuelGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueDuel);
            if (globalData.getStatus() != DuelStatusType.battle) {
                rst.setResult(Text.genServerRstInfo(Text.不在领奖的阶段));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            LeagueDuelModel duelModel = league.getModel(LeagueModelEnums.duel);
            PersonRewardBean personRewardBean = duelModel.getPersonRewardBeanMap().computeIfAbsent(player.getPlayerId(), v -> new PersonRewardBean());
            Map<Integer, Integer> rewardStatusMap = personRewardBean.getRewardStatusMap();
            for (LeagueDuelRewardTemplate duelRewardTemplate : duelRewardTemplateTypeMap.values()) {
                if (duelRewardTemplate.receiveRewardType == DuelReceiveRewardType.teamWin
                        || duelRewardTemplate.receiveRewardType == DuelReceiveRewardType.teamLose) {
                    continue;
                }
                Integer status = rewardStatusMap.getOrDefault(duelRewardTemplate.id, PersonRewardBean.STATUS_UNFINISHED);
                rst.putRewardStatus(duelRewardTemplate.id, status);
            }
        }
        player.send(PtCode.DUEL_REWARD_LIST_SERVER, rst.build(), time);
    }

    /**
     * 帮派玩家剩余攻击次数
     */
    @Handler(PtCode.DUEL_REMAIN_ATK_CLIENT)
    private void remainAtkNum(Player player, long time) throws Exception {
        PbProtocol.DuelRemainAtkRst.Builder rst = PbProtocol.DuelRemainAtkRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        logic:
        {
            if(!Function.leagueDuel.isOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }

            if (LeagueManager.isNotJoinLeague(player)) {
                rst.setResult(Text.genServerRstInfo(Text.未加入帮派));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            league.getLeagueFightPower();
            LeagueDuelModel duelModel = league.getModel(LeagueModelEnums.duel);
            for (LeagueMember member : league.getMemberMap().values()) {
                PbLeague.LeagueMember leagueMember = member.genPb(league);
                PbLeagueDuel.DuelRemainAtkNum.Builder builder = PbLeagueDuel.DuelRemainAtkNum.newBuilder()
                        .setMemberId(member.getPlayerId())
                        .setLastOnlineTime(leagueMember.getLastLogoutTime())
                        .setRemainAtkNum(duelModel.getRemainFightNum(member.getPlayerId()))
                        .setFightPower(leagueMember.getUser().getFightingPower())
                        .setName(leagueMember.getUser().getName())
                        .setBreakCount(duelModel.getBreakCounts().getOrDefault(member.getPlayerId(), 0))
                        .setDefenseCount(duelModel.getDefenseCounts().getOrDefault(member.getPlayerId(), 0));
                rst.addDuelRemainAtkNum(builder);
            }

        }
        player.send(PtCode.DUEL_REMAIN_ATK_SERVER, rst.build(), time);
    }

    /**
     * 帮派城池宝箱奖励领取
     */
    @Handler(PtCode.DUEL_CHESS_REWARD_CLIENT)
    private void chessReward(Player player, PbProtocol.DuelChessRewardReq req, long time) throws Exception {
        PbProtocol.DuelChessRewardRst.Builder rst = PbProtocol.DuelChessRewardRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        logic:
        {
            if(!Function.leagueDuel.isOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }

            if (LeagueManager.isNotJoinLeague(player)) {
                rst.setResult(Text.genServerRstInfo(Text.未加入帮派));
                break logic;
            }
            int chessRewardId = req.getChessRewardId();
            if (!duelChestTemplateMap.containsKey(chessRewardId)) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }

            League league = LeagueManager.getLeagueByPlayer(player);
            LeagueDuelModel duelModel = league.getModel(LeagueModelEnums.duel);
            Set<Integer> hasChessRewardIdSet = duelModel.getHadReceiveChessRewards().getOrDefault(player.getPlayerId(), new HashSet<>());
            if (hasChessRewardIdSet.contains(chessRewardId)) {
                rst.setResult(Text.genServerRstInfo(Text.宝箱奖励已经领取));
                break logic;
            }
            LeagueDuelGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueDuel);
            Long targetLeagueId = globalData.getMatchMap().get(league.getLeagueId());
            League targetLeague = LeagueManager.getLeagueById(targetLeagueId);
            if(Objects.isNull(targetLeague)){
                rst.setResult(Text.genServerRstInfo(Text.数据异常));
                break logic;
            }
            LeagueDuelModel targetDuelModel = targetLeague.getModel(LeagueModelEnums.duel);
            LeagueDuelChestTemplate leagueDuelChestTemplate = duelChestTemplateMap.get(chessRewardId);
            int cityId = leagueDuelChestTemplate.cityId;
            Map<Integer, DuelCityPointInfo> pointInfoMap = targetDuelModel.getPointInfoMap().get(cityId);
//            Set<Integer> hasPointList = new HashSet<>();
            for (Integer pointId : leagueDuelChestTemplate.guardList) {
                DuelCityPointInfo duelCityPointInfo = pointInfoMap.get(pointId);
                if(!duelModel.isBreak(duelCityPointInfo)){
                    rst.setResult(Text.genServerRstInfo(Text.宝箱守护城池未击破));
                    break logic;
                }
            }
//            for (DuelCityPointInfo pointInfo : pointInfoMap.values()) {
//                if(duelModel.isBreak(pointInfo)){
//                    hasPointList.add(pointInfo.getPointId());
//                }
//            }
//            if (CollectionUtil.isEmpty(hasPointList)) {
//                rst.setResult(Text.genServerRstInfo(Text.宝箱守护城池未击破));
//                break logic;
//            }
            //前置城池一个也未击破
//            if (leagueDuelChestTemplate.guardList.stream()
//                    .noneMatch(hasPointList::contains)) {
//                rst.setResult(Text.genServerRstInfo(Text.宝箱守护城池未击破));
//                break logic;
//            }
            hasChessRewardIdSet.add(chessRewardId);
            List<Reward> rewards = Reward.addFromTemplates(leagueDuelChestTemplate.rewardList, player, BehaviorType.leagueDuelBoxReward);
            duelModel.getHadReceiveChessRewards().put(player.getPlayerId(), hasChessRewardIdSet);
            rst.addAllRewards(Reward.writeCollectionToPb(rewards));
            //同步帮派成员状态信息
//            duelModel.syncChessRewardSync(chessRewardId);

        }
        player.send(PtCode.DUEL_CHESS_REWARD_SERVER, rst.build(), time);
    }

    @Handler(PtCode.DUEL_TIMESREWARD_REQ)
    private void timesReward(Player player, PbProtocol.DuelTimesRewardReq req, long time) throws Exception {
        PbProtocol.DuelTimesRewardRst.Builder rst = PbProtocol.DuelTimesRewardRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        logic:
        {
            int times = req.getTimes();
            if (!Function.leagueDuel.isOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }

            if (LeagueManager.isNotJoinLeague(player)) {
                rst.setResult(Text.genServerRstInfo(Text.未加入帮派));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            if (!isJoin(league)) {
                rst.setResult(Text.genServerRstInfo(Text.帮派对决未报名));
                break logic;
            }
            List<RewardTemplate> rewardTemplates = timesRewards.get(times);
            if(Objects.isNull(rewardTemplates)){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            LeagueDuelModel duelModel = league.getModel(LeagueModelEnums.duel);
            Set<Integer> timesRewards = duelModel.getHadReceiveTimesRewards().getOrDefault(player.getPlayerId(), new HashSet<>());
            if (timesRewards.contains(times)) {
                rst.setResult(Text.genServerRstInfo(Text.次数奖励已经领取));
                break logic;
            }
            if(duelModel.getFightNum(player.getPlayerId()) < times){
                rst.setResult(Text.genServerRstInfo(Text.次数奖励不满足));
                break logic;
            }
            timesRewards.add(times);
            List<Reward> rewards = Reward.addFromTemplates(rewardTemplates, player, BehaviorType.leagueDuelTimesReward);
            duelModel.getHadReceiveTimesRewards().put(player.getPlayerId(), timesRewards);
            rst.addAllRewards(Reward.writeCollectionToPb(rewards));
        }
        player.send(PtCode.DUEL_TIMESREWARD_RST, rst.build(), time);
    }


    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.leagueDuel_leagueDuelPoint);
        Map<Integer, Map<Integer, LeagueDuelPointTemplate>> duelPointTemplateMapTemp = new HashMap<>();
        Map<Integer, LeagueDuelPointTemplate> duelPointIdTemplateMapTemp = new HashMap<>();
        Map<Integer, Integer> duelPointStrongholdNumMapTemp = new HashMap<>();
        Map<Integer, Integer> duelCityDefenceMapTemp = new HashMap<>();
        Map<Integer, Integer> duelCityEyesMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            LeagueDuelPointTemplate duelPointTemplate = new LeagueDuelPointTemplate();
            duelPointTemplate.id = Integer.parseInt(map.get("id"));
            duelPointTemplate.cityId = Integer.parseInt(map.get("cityId"));
            duelPointTemplate.pointId = Integer.parseInt(map.get("pointId"));
            duelPointTemplate.pointType = DuelPointType.of(Integer.parseInt(map.get("pointType")));
            duelPointTemplate.rank = Integer.parseInt(map.get("rank"));
            duelPointTemplate.star = Integer.parseInt(map.get("star"));
            duelPointTemplate.difficultyStar = Integer.parseInt(map.get("difficultyStar"));
            duelPointTemplate.robotRule = map.get("robotType");
            Map<Integer, LeagueDuelPointTemplate> subMap = duelPointTemplateMapTemp.computeIfAbsent(duelPointTemplate.cityId, map1 -> new HashMap<>());
            subMap.put(duelPointTemplate.pointId, duelPointTemplate);
            duelPointIdTemplateMapTemp.put(duelPointTemplate.id, duelPointTemplate);
            if (duelPointTemplate.pointType == DuelPointType.Stronghold) {
                duelPointStrongholdNumMapTemp.merge(duelPointTemplate.cityId, 1, Integer::sum);
            }
            if (duelPointTemplate.pointType == DuelPointType.CityDefense) {
                duelCityDefenceMapTemp.put(duelPointTemplate.cityId, duelPointTemplate.pointId);
            }
            if (duelPointTemplate.pointType == DuelPointType.ArrayEye) {
                duelCityEyesMapTemp.put(duelPointTemplate.cityId, duelPointTemplate.pointId);
            }
        }
        duelPointTemplateMap = duelPointTemplateMapTemp;
        duelPointIdTemplateMap = duelPointIdTemplateMapTemp;
        duelPointStrongholdNumMap = duelPointStrongholdNumMapTemp;
        duelCityDefenceMap = duelCityDefenceMapTemp;
        duelCityEyesMap = duelCityEyesMapTemp;

        mapList = ConfigReader.read(ConfigFile.leagueDuel_leagueDuelCity);
        Map<Integer, LeagueDuelCityTemplate> duelCityTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            LeagueDuelCityTemplate duelCityTemplate = new LeagueDuelCityTemplate();
            duelCityTemplate.cityId = Integer.parseInt(map.get("id"));
            duelCityTemplate.cityType = DuelCityType.of(Integer.parseInt(map.get("type")));
            duelCityTemplate.cityBuff = Integer.parseInt(map.get("cityBuff"));
            duelCityTemplate.cityStar = Integer.parseInt(map.get("cityStar"));
            String preCityStr = map.get("preCity");
            if (!preCityStr.equals("-1")) {
                duelCityTemplate.preCityList = StringExtUtil.string2List(preCityStr, "|", Integer.class);
            }
            duelCityTemplateMapTemp.put(duelCityTemplate.cityId, duelCityTemplate);
        }
        duelCityTemplateMap = duelCityTemplateMapTemp;

        mapList = ConfigReader.read(ConfigFile.leagueDuel_leagueDuelReward);
        Map<DuelReceiveRewardType, LeagueDuelRewardTemplate> duelRewardTemplateTypeMapTemp = new HashMap<>();
        Map<Integer, LeagueDuelRewardTemplate> duelRewardTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            LeagueDuelRewardTemplate duelRewardTemplate = new LeagueDuelRewardTemplate();
            duelRewardTemplate.id = Integer.parseInt(map.get("id"));
            duelRewardTemplate.receiveRewardType = DuelReceiveRewardType.of(Integer.parseInt(map.get("rewardCondition")));
            duelRewardTemplate.rewardList = RewardTemplate.readListFromText(map.get("reward"));
            duelRewardTemplateTypeMapTemp.put(duelRewardTemplate.receiveRewardType, duelRewardTemplate);
            duelRewardTemplateMapTemp.put(duelRewardTemplate.id, duelRewardTemplate);
        }
        duelRewardTemplateTypeMap = duelRewardTemplateTypeMapTemp;
        duelRewardTemplateMap = duelRewardTemplateMapTemp;

        mapList = ConfigReader.read(ConfigFile.leagueDuel_leagueDuelCityTreasureChest);
        Map<Integer, LeagueDuelChestTemplate> duelChestTemplateMapTemp = new HashMap<>();
        Map<Integer, List<Integer>> cityChestRewardMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            LeagueDuelChestTemplate duelChestTemplate = new LeagueDuelChestTemplate();
            duelChestTemplate.id = Integer.parseInt(map.get("id"));
            duelChestTemplate.cityId = Integer.parseInt(map.get("treasureChestCity"));
            duelChestTemplate.guardList = StringExtUtil.string2List(map.get("guardId"), "|", Integer.class);
            duelChestTemplate.rewardList = RewardTemplate.readListFromText(map.get("reward"));
            duelChestTemplateMapTemp.put(duelChestTemplate.id, duelChestTemplate);
            List<Integer> chessList = cityChestRewardMapTemp.computeIfAbsent(duelChestTemplate.cityId, list1 -> new ArrayList<>());
            chessList.add(duelChestTemplate.id);
        }
        duelChestTemplateMap = duelChestTemplateMapTemp;
        cityChestRewardMap = cityChestRewardMapTemp;

        Map<String, String> constantMap = ConstantConfigReader.read(ConfigFile.leagueDuel_const);
        constant = new LeagueDuelConst(constantMap);
        mapList = ConfigReader.read(ConfigFile.leagueDuel_duelTimesReward);
        Map<Integer, List<RewardTemplate>> timesRewardMap = new HashMap<>();
        for (Map<String, String> map : mapList) {
            int times = Integer.parseInt(map.get("Times"));
            List<RewardTemplate> rewardTemplates = RewardTemplate.readListFromText(map.get("Reward"));
            timesRewardMap.put(times, rewardTemplates);
        }
        timesRewards = timesRewardMap;
    }

    @Override
    public void clearConfigData() {
        duelPointTemplateMap.clear();
        duelPointIdTemplateMap.clear();
        duelCityEyesMap.clear();
        duelCityDefenceMap.clear();
        duelCityTemplateMap.clear();
        duelRewardTemplateTypeMap.clear();
        duelRewardTemplateMap.clear();
        duelChestTemplateMap.clear();
        cityChestRewardMap.clear();
        duelPointStrongholdNumMap.clear();
        timesRewards.clear();

    }

    public static LeagueDuelConst getConstant() {
        return constant;
    }

    public static Map<Integer, Map<Integer, LeagueDuelPointTemplate>> getDuelPointTemplateMap() {
        return duelPointTemplateMap;
    }

    public static int getCityEyesId(int cityId) {
        return duelCityEyesMap.get(cityId);
    }

    public static int getCityDefenceId(int cityId) {
        return duelCityDefenceMap.get(cityId);
    }

    public static LeagueDuelPointTemplate getLeagueDuelPointTemplate(int cityId, int pointId) {
        Map<Integer, LeagueDuelPointTemplate> pointTemplateMap = LeagueDuelService.duelPointTemplateMap.get(cityId);
        if (CollectionUtil.isEmpty(pointTemplateMap)) {
            return null;
        }
        return pointTemplateMap.get(pointId);
    }

    public static LeagueDuelPointTemplate getLeagueDuelPointTemplate(int pointUid) {
        return duelPointIdTemplateMap.get(pointUid);
    }

    public static Map<Integer, LeagueDuelCityTemplate> getDuelCityTemplateMap() {
        return duelCityTemplateMap;
    }

    public static LeagueDuelCityTemplate getLeagueDuelCityTemplate(int cityId) {
        return duelCityTemplateMap.get(cityId);
    }

    public static Map<DuelReceiveRewardType, LeagueDuelRewardTemplate> getDuelRewardTemplateTypeMap() {
        return duelRewardTemplateTypeMap;
    }

    public static Map<Integer, LeagueDuelRewardTemplate> getDuelRewardTemplateMap() {
        return duelRewardTemplateMap;
    }

    public static Map<Integer, LeagueDuelChestTemplate> getDuelChestTemplateMap() {
        return duelChestTemplateMap;
    }

    public static Map<Integer, List<Integer>> getCityChestRewardMap() {
        return cityChestRewardMap;
    }

    public static int getStrongholdNum(int cityId) {
        return duelPointStrongholdNumMap.getOrDefault(cityId, 0);
    }


    @Override
    public boolean isWorldServer() {
        return true;
    }

    private static class MainInfoCallbackTask extends TLMessageCallbackTask {
        private final PbLeagueDuel.LeagueDuelMainInfo.Builder mainBuilder;
        private final PbProtocol.DuelMainInfoRst.Builder rst;
        private final Player player;
        private final long time;

        public MainInfoCallbackTask(PbLeagueDuel.LeagueDuelMainInfo.Builder mainBuilder, PbProtocol.DuelMainInfoRst.Builder rst, Player player, long time) {
            this.mainBuilder = mainBuilder;
            this.rst = rst;
            this.player = player;
            this.time = time;
        }

        @Override
        public void complete(CallbackResponse response) {
            PbLeagueDuel.MiniLeagueDuel targetDuelPb = response.getParam(0);
            mainBuilder.setTargetLeagueDuel(targetDuelPb);
            rst.setDuelMainInfo(mainBuilder);
            player.send(PtCode.DUEL_MAIN_INFO_SERVER, rst.build(), time);
        }

        @Override
        public void timeout() {
            rst.setResult(Text.genServerRstInfo(Text.服务器异常));
            player.send(PtCode.DUEL_MAIN_INFO_SERVER, rst.build(), time);
        }
    }

    private static class JoinCallbackTask extends TLMessageCallbackTask {
        private final PbLeagueDuel.LeagueDuelBattlefieldInfo.Builder battleInfoBuilder;
        private final PbProtocol.DuelJoinBattlefieldRst.Builder rst;
        private final Player player;
        private final long time;

        public JoinCallbackTask(PbLeagueDuel.LeagueDuelBattlefieldInfo.Builder battleInfoBuilder, PbProtocol.DuelJoinBattlefieldRst.Builder rst, Player player, long time) {
            this.battleInfoBuilder = battleInfoBuilder;
            this.rst = rst;
            this.player = player;
            this.time = time;
        }

        @Override
        public void complete(CallbackResponse response) {
            List<PbLeagueDuel.LeagueDuelCityInfo> leagueDuelCityInfoList = response.getParam(0);
            int targetStar = response.getParam(1);
            battleInfoBuilder
                    .setTarStar(targetStar)
                    .addAllTargetCityInfos(leagueDuelCityInfoList);
            rst.setBattlefieldInfo(battleInfoBuilder);
            player.send(PtCode.DUEL_JOIN_BATTLEFIELD_SERVER, rst.build(), time);
        }

        @Override
        public void timeout() {
            rst.setResult(Text.genServerRstInfo(Text.服务器异常));
            player.send(PtCode.DUEL_JOIN_BATTLEFIELD_SERVER, rst.build(), time);
        }
    }

    private static class CityPointInfoCallbackTask extends TLMessageCallbackTask {
        private final boolean isMyCity;
        private final PbProtocol.DuelCityPointInfoRst.Builder rst;
        private final Player player;
        private final long time;

        public CityPointInfoCallbackTask(boolean isMyCity, PbProtocol.DuelCityPointInfoRst.Builder rst, Player player, long time) {
            this.isMyCity = isMyCity;
            this.rst = rst;
            this.player = player;
            this.time = time;
        }

        @Override
        public void complete(CallbackResponse response) {
            int targetStar = response.getParam(0);
            if (!isMyCity) {
                List<PbLeagueDuel.LeagueDuelCityPointInfo> leagueDuelCityInfoList = response.getParam(1);
                rst.addAllCityPointInfo(leagueDuelCityInfoList);
            }
            rst.setTarStar(targetStar);
            player.send(PtCode.DUEL_CITY_POINT_INFO_SERVER, rst.build(), time);
        }

        @Override
        public void timeout() {
            rst.setResult(Text.genServerRstInfo(Text.服务器异常));
            player.send(PtCode.DUEL_CITY_POINT_INFO_SERVER, rst.build(), time);
        }
    }

    private static class CityPointInfoMutiCallbackTask extends TLMessageCallbackTask {
        private final PbProtocol.DuelCityPointInfoMutiRst.Builder rst;
        private final Player player;
        private final long time;

        public CityPointInfoMutiCallbackTask(PbProtocol.DuelCityPointInfoMutiRst.Builder rst, Player player, long time) {
            this.rst = rst;
            this.player = player;
            this.time = time;
        }

        @Override
        public void complete(CallbackResponse response) {
            int targetStar = response.getParam(0);
            List<PbLeagueDuel.LeagueDuelCityPointInfo> leagueDuelCityInfoList = response.getParam(1);
            rst.addAllCityPointInfo(leagueDuelCityInfoList);
            rst.setTarStar(targetStar);
            player.send(PtCode.DUEL_CITY_POINT_INFO_MUTI_RST, rst.build(), time);
        }

        @Override
        public void timeout() {
            rst.setResult(Text.genServerRstInfo(Text.服务器异常));
            player.send(PtCode.DUEL_CITY_POINT_INFO_MUTI_RST, rst.build(), time);
        }
    }

    private static class PointLineupCallbackTask extends TLMessageCallbackTask {
        private final PbProtocol.DuelPointLineupRst.Builder rst;
        private final Player player;
        private final long time;

        public PointLineupCallbackTask(PbProtocol.DuelPointLineupRst.Builder rst, Player player, long time) {
            this.rst = rst;
            this.player = player;
            this.time = time;
        }

        @Override
        public void complete(CallbackResponse response) {
            PbLeagueDuel.DuelPointLineup duelPointLineup = response.getParam(0);
            rst.setLineupData(duelPointLineup);
            player.send(PtCode.DUEL_POINT_LINEUP_SERVER, rst.build(), time);
        }

        @Override
        public void timeout() {
            rst.setResult(Text.genServerRstInfo(Text.服务器异常));
            player.send(PtCode.DUEL_POINT_LINEUP_SERVER, rst.build(), time);
        }
    }

    private static class DeBuffAtkCallbackTask extends TLMessageCallbackTask {
        private final PbProtocol.DuelDeBuffAtkRst.Builder rst;
        private final LeagueDuelModel duelModel;
        private final Player player;
        private final int cityId;
        private final int pointId;
        private final long time;

        public DeBuffAtkCallbackTask(PbProtocol.DuelDeBuffAtkRst.Builder rst, LeagueDuelModel duelModel, Player player, int cityId, int pointId, long time) {
            this.rst = rst;
            this.duelModel = duelModel;
            this.player = player;
            this.cityId = cityId;
            this.pointId = pointId;
            this.time = time;
        }

        @Override
        public void complete(CallbackResponse response) {
            int result = response.getParam(0);
            if (result != Text.没有异常) {
                rst.setResult(Text.genServerRstInfo(result));
            } else {
                duelModel.costDeBuffNum(player.getPlayerId());
                DuelCityPointInfo duelCityPointInfo = PbUtilCompress.decode(DuelCityPointInfo.class, response.getParam(1));
                rst.setCityPointInfo(duelCityPointInfo.genPb(duelModel.isFighting(cityId, pointId)));
                rst.setRemainDeBuffNum(duelModel.getDeBuffNum(player.getPlayerId()));
                // 给我方同步数据
                duelModel.syncPointSync(false, duelCityPointInfo);
            }
            player.send(PtCode.DUEL_DeBuff_ATK_SERVER, rst.build(), time);
        }

        @Override
        public void timeout() {
            rst.setResult(Text.genServerRstInfo(Text.服务器异常));
            player.send(PtCode.DUEL_DeBuff_ATK_SERVER, rst.build(), time);
        }
    }
}

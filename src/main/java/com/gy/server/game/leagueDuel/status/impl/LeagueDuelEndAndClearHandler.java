package com.gy.server.game.leagueDuel.status.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.league.LeagueModelEnums;
import com.gy.server.game.leagueDuel.LeagueDuelGlobalData;
import com.gy.server.game.leagueDuel.LeagueDuelModel;
import com.gy.server.game.leagueDuel.LeagueDuelService;
import com.gy.server.game.leagueDuel.status.DuelNodeType;
import com.gy.server.game.leagueDuel.status.ILeagueDuelStatusHandler;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.DuelStatusType;

import java.time.LocalDateTime;

/**
 * 结束数据清理阶段
 *
 * <AUTHOR> - [Created on 2023/6/6 13:33]
 */
public class LeagueDuelEndAndClearHandler implements ILeagueDuelStatusHandler {
    @Override
    public DuelStatusType handleStatus() {
        return DuelStatusType.endAndClear;
    }


    @Override
    public void handler(LeagueDuelGlobalData globalData) {
        LocalDateTime now = ServerConstants.getCurrentTimeLocalDateTime();
        if (now.isBefore(globalData.getOpenTime())) {
            return;
        }
        for (Long leagueId : globalData.getJoinLeagueSet()) {
            League league = LeagueManager.getLeagueById(leagueId);
            LeagueDuelModel duelModel = league.getModel(LeagueModelEnums.duel);
            duelModel.endAndClear();
        }
        globalData.getJoinLeagueSet().clear();
        globalData.getMatchMap().clear();
        changeStatus(DuelStatusType.noStart, calcOpenTime());
    }

    @Override
    public LocalDateTime getEndTime() {
        Integer duration = LeagueDuelService.getConstant().getOnTimeMap().get(DuelNodeType.endAndClear).getRight();
        LeagueDuelGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueDuel);
        return globalData.getOpenTime().plusMinutes(duration);
    }
}

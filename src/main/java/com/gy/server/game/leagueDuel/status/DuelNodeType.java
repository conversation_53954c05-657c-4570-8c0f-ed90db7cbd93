package com.gy.server.game.leagueDuel.status;

import java.util.HashMap;
import java.util.Map;

/**
 * 帮派对决各个节点
 *
 * <AUTHOR> - [Created on 2023/5/25 16:29]
 */
public enum DuelNodeType {
    /**
     * 准备阶段
     */
    ready(1),
    /**
     * 布阵阶段
     */
    lineup(2),
    /**
     * 战斗阶段
     */
    battle(3),
    /**
     * 玩法结束阶段
     */
    over(4),
    /**
     * 发奖阶段
     */
    sendReward(5),
    /**
     * 结束并清空数据
     */
    endAndClear(6),

    ;

    private final int type;

    private static final Map<Integer, DuelNodeType> map = new HashMap<>();

    DuelNodeType(int type) {
        this.type = type;
    }

    public static DuelNodeType of(int type) {
        return map.get(type);
    }

    static {
        for (DuelNodeType value : values()) {
            map.put(value.type, value);
        }
    }

}

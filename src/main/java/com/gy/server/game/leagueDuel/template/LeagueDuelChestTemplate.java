package com.gy.server.game.leagueDuel.template;

import com.gy.server.game.drop.RewardTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * 城池宝箱奖励
 *
 * <AUTHOR> - [Created on 2023/5/25 10:38]
 */
public class LeagueDuelChestTemplate {
    /**
     * 宝箱ID
     */
    public int id;
    /**
     * 宝箱所在城池
     */
    public int cityId;
    /**
     * 宝箱守护点位ID
     */
    public List<Integer> guardList = new ArrayList<>();
    /**
     * 宝箱奖励
     */
    public List<RewardTemplate> rewardList = new ArrayList<>();


}

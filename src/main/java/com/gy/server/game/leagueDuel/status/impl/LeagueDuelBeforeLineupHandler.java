package com.gy.server.game.leagueDuel.status.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.leagueDuel.LeagueDuelGlobalData;
import com.gy.server.game.leagueDuel.LeagueDuelService;
import com.gy.server.game.leagueDuel.status.DuelNodeType;
import com.gy.server.game.leagueDuel.status.ILeagueDuelStatusHandler;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.DuelStatusType;

import java.time.LocalDateTime;

/**
 * 布阵前阶段(准备完成等待布阵)
 *
 * <AUTHOR> - [Created on 2023/5/26 14:25]
 */
public class LeagueDuelBeforeLineupHandler implements ILeagueDuelStatusHandler {
    @Override
    public DuelStatusType handleStatus() {
        return DuelStatusType.beforeLineup;
    }

    @Override
    public void handler(LeagueDuelGlobalData globalData) {
        if (readyIsEnd()) {
            changeStatus(DuelStatusType.lineup, getEndTime());
        }
    }

    @Override
    public LocalDateTime getEndTime() {
        Integer duration = LeagueDuelService.getConstant().getOnTimeMap().get(DuelNodeType.ready).getRight();
        LeagueDuelGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueDuel);
        return globalData.getOpenTime().plusMinutes(duration);
    }


    /**
     * 准备阶段是否结束
     *
     * @return true 结束
     */
    private boolean readyIsEnd() {
        LocalDateTime now = ServerConstants.getCurrentTimeLocalDateTime();
        LocalDateTime readyEndTime = getEndTime();
        return !now.isBefore(readyEndTime);
    }
}

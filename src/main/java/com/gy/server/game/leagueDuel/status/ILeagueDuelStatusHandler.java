package com.gy.server.game.leagueDuel.status;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.log.CommonLogger;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.league.LeagueModelEnums;
import com.gy.server.game.leagueDuel.LeagueDuelGlobalData;
import com.gy.server.game.leagueDuel.LeagueDuelModel;
import com.gy.server.game.leagueDuel.LeagueDuelService;
import com.gy.server.packet.PbLeagueDuel;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.DuelStatusType;
import org.apache.commons.lang3.tuple.Pair;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 帮派对决-状态处理器
 *
 * <AUTHOR> - [Created on 2023/5/25 13:38]
 */
public interface ILeagueDuelStatusHandler {

    /**
     * 可以处理哪个状态
     */
    DuelStatusType handleStatus();

    /**
     * 游戏服状态处理
     */
    void handler(LeagueDuelGlobalData globalData);

    /**
     * 获取阶段结束时间
     */
    LocalDateTime getEndTime();

    /**
     * 状态切换
     *
     * @param nextStatus 下个状态
     * @param openTime   下个状态开启时间
     */
    default void changeStatus(DuelStatusType nextStatus, LocalDateTime openTime, boolean isChangeOpenTime) {
        LeagueDuelGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueDuel);
        globalData.setStatus(nextStatus);
        if (isChangeOpenTime) {
            globalData.setOpenTime(openTime);
        }
        LocalDateTime startTime = globalData.getOpenTime();
        //同步客户端状态信息变化
        LocalDateTime endTime = LeagueDuelStatusManager.getInstance().getHandler(nextStatus).getEndTime();
        PbProtocol.DuelSyncRst.Builder rstBuilder = PbProtocol.DuelSyncRst.newBuilder();
        PbLeagueDuel.DuelSyncData.Builder duelSyncDataBuilder = PbLeagueDuel.DuelSyncData.newBuilder()
                .setType(PbLeagueDuel.DuelSyncData.Type.STATUS_CHANGE)
                .setStatusChange(PbLeagueDuel.StatusChangeSyncData.newBuilder()
                        .setStatus(nextStatus.getType())
                        .setStatusStartTime(DateTimeUtil.toMillis(startTime))
                        .setStatusEndTime(DateTimeUtil.toMillis(endTime)));
        rstBuilder.addSyncData(duelSyncDataBuilder);
        PbProtocol.DuelSyncRst rst = rstBuilder.build();
        //通知在玩法场景内的玩家
        Set<Long> joinLeagueSet = globalData.getJoinLeagueSet();
        for (Long leagueId : joinLeagueSet) {
            League league = LeagueManager.getLeagueById(leagueId);
            if(Objects.nonNull(league)){
                LeagueDuelModel duelModel = league.getModel(LeagueModelEnums.duel);
                duelModel.syncAllBattlefieldPlayer(rst);
            }
        }
        CommonLogger.info(String.format("帮派对决状态切换：%s -> %s, 开始时间:%s", handleStatus(), nextStatus, startTime));
    }

    /**
     * 状态切换
     *
     * @param nextStatus 下个状态
     * @param openTime   下个状态开启时间
     */
    default void changeStatus(DuelStatusType nextStatus, LocalDateTime openTime) {
        changeStatus(nextStatus, openTime, true);
    }

    /**
     * 状态切换
     *
     * @param nextStatus 下个状态
     */
    default void changeStatus(DuelStatusType nextStatus) {
        changeStatus(nextStatus, null, false);
    }

    default LocalDateTime calcOpenTime() {
        LocalDateTime now = ServerConstants.getCurrentTimeLocalDateTime();
        List<Integer> onTimeWeekList = LeagueDuelService.getConstant().getOnTimeWeekList();
        LocalDateTime newOpenTime = ServerConstants.getCurrentTimeLocalDateTime();
        Pair<LocalTime, Integer> pairTime = LeagueDuelService.getConstant().getOnTimeMap().get(DuelNodeType.ready);
        LocalTime configOpenTime = pairTime.getLeft().withSecond(0).withNano(0);
        boolean isOpen = false;
        int nowDayOfWeek = now.getDayOfWeek().getValue();
        for (Integer timeWeek : onTimeWeekList) {
            if (nowDayOfWeek < timeWeek) {
                newOpenTime = now.plusDays(timeWeek - nowDayOfWeek).with(configOpenTime);
                isOpen = true;
                break;
            } else if (nowDayOfWeek == timeWeek) {
                // 当前时间小于当天开启时间 则开启时间就是今天
                if (now.toLocalTime().isBefore(configOpenTime)) {
                    newOpenTime = now.with(configOpenTime);
                    isOpen = true;
                    break;
                }
            }
        }
        // 本周未开启 下周开启
        if (!isOpen) {
            Integer timeWeek = onTimeWeekList.get(0);
            //下周配置开启
            newOpenTime = now.plusWeeks(1).plusDays(timeWeek - nowDayOfWeek).with(configOpenTime);
        }
        return newOpenTime;
    }

}

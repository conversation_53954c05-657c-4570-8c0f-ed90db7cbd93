package com.gy.server.game.leagueDuel.message;

import com.gy.server.annotation.MessageMethod;
import com.gy.server.annotation.MessageServiceBean;
import com.gy.server.core.MessageServerType;
import com.gy.server.core.MethodInvokeType;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.command.CommandRequestParams;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.combat.CombatManager;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.league.LeagueModelEnums;
import com.gy.server.game.leagueDuel.LeagueDuelGlobalData;
import com.gy.server.game.leagueDuel.LeagueDuelHelper;
import com.gy.server.game.leagueDuel.LeagueDuelModel;
import com.gy.server.game.leagueDuel.LeagueDuelService;
import com.gy.server.game.leagueDuel.stage.LeagueDuelStage;
import com.gy.server.game.leagueDuel.status.ILeagueDuelStatusHandler;
import com.gy.server.game.leagueDuel.status.LeagueDuelStatusManager;
import com.gy.server.game.leagueDuel.template.DuelPointType;
import com.gy.server.game.leagueDuel.template.LeagueDuelPointTemplate;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbLeagueDuel;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.*;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 帮派对决消息
 */
@MessageServiceBean(description = "帮派对决消息", messageServerType = MessageServerType.game)
public class LeagueDuelGameCommandService {

    /**
     * 通知服务器是否开启跨服
     */
    @MessageMethod(description = "检查是否跨服", invokeType = MethodInvokeType.sync)
    private static void checkCross(ServerCommandRequest request, CommandRequestParams params) {
        if (LeagueDuelService.getConstant().isOpenCross()) {
            boolean openCross = params.getParam(0);
            LeagueDuelGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueDuel);
            globalData.setCross(openCross);
            //切换到下个状态
            ILeagueDuelStatusHandler handler = LeagueDuelStatusManager.getInstance().getHandler(globalData.getStatus());
            handler.changeStatus(DuelStatusType.ready);
        }
    }

    /**
     * 活动终止
     */
    @MessageMethod(description = "活动终止", invokeType = MethodInvokeType.sync)
    private static void notifyAbort(ServerCommandRequest request, CommandRequestParams params) {
        LeagueDuelGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueDuel);
        //切换到下个状态
        ILeagueDuelStatusHandler handler = LeagueDuelStatusManager.getInstance().getHandler(globalData.getStatus());
        handler.changeStatus(DuelStatusType.abort);
    }

    /**
     * 数据准备完成
     */
    @MessageMethod(description = "数据准备完成", invokeType = MethodInvokeType.sync)
    private static void notifyReadyFinish(ServerCommandRequest request, CommandRequestParams params) {
        long openTime = params.getParam(0);
        LeagueDuelMatchInfo matchInfo = PbUtilCompress.decode(LeagueDuelMatchInfo.class, params.getParam(1));
        LeagueDuelGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueDuel);
        //切换到下个状态
        ILeagueDuelStatusHandler handler = LeagueDuelStatusManager.getInstance().getHandler(globalData.getStatus());
        handler.changeStatus(DuelStatusType.beforeLineup, DateTimeUtil.toLocalDateTime(openTime));
        globalData.getMatchMap().putAll(matchInfo.getMatchMap());
        globalData.getJoinLeagueSet().addAll(matchInfo.getMatchMap().keySet());
    }

    /**
     * 获取主界面信息
     */
    @MessageMethod(description = "获取主界面信息", invokeType = MethodInvokeType.sync)
    private static void mainInfo(ServerCommandRequest request, CommandRequestParams params) {
        long leagueId = params.getParam(0);
        League league = LeagueManager.getLeagueById(leagueId);
        LeagueDuelModel targetDuelModel = league.getModel(LeagueModelEnums.duel);
        PbLeagueDuel.MiniLeagueDuel targetDuelPb = targetDuelModel.genMiniLeagueDuelPb();
        request.addCallbackParam(targetDuelPb);
    }

    /**
     * 获取城市列表信息
     */
    @MessageMethod(description = "获取城市列表信息", invokeType = MethodInvokeType.sync)
    private static void getCityInfo(ServerCommandRequest request, CommandRequestParams params) {
        long leagueId = params.getParam(0);
        League league = LeagueManager.getLeagueById(leagueId);
        LeagueDuelModel targetDuelModel = league.getModel(LeagueModelEnums.duel);
        List<PbLeagueDuel.LeagueDuelCityInfo> leagueDuelCityInfoList = targetDuelModel.genLeagueDuelCityInfoListPb();
        request.addCallbackParam(leagueDuelCityInfoList);
        request.addCallbackParam(targetDuelModel.getTotalStar());
    }

    /**
     * 获取城市据点信息
     */
    @MessageMethod(description = "获取城市据点信息", invokeType = MethodInvokeType.sync)
    private static void getCityPointInfo(ServerCommandRequest request, CommandRequestParams params) {
        long leagueId = params.getParam(0);
        int cityId = params.getParam(1);
        //是否是敌方的城市
        boolean isTargetCity = params.getParam(2);
        League league = LeagueManager.getLeagueById(leagueId);
        LeagueDuelModel targetDuelModel = league.getModel(LeagueModelEnums.duel);
        request.addCallbackParam(targetDuelModel.getTotalStar());
        if (!isTargetCity) {
            List<PbLeagueDuel.LeagueDuelCityPointInfo> leagueDuelCityPointInfos = targetDuelModel.genLeagueDuelCityPointInfoListPb(cityId);
            request.addCallbackParam(leagueDuelCityPointInfos);
        }
    }

    @MessageMethod(description = "批量获取城市据点信息", invokeType = MethodInvokeType.sync)
    private static void cityPointInfoMuti(ServerCommandRequest request, CommandRequestParams params) {
        long leagueId = params.getParam(0);
        Set<Integer> cityIds = params.getParam(1);
        League league = LeagueManager.getLeagueById(leagueId);
        LeagueDuelModel targetDuelModel = league.getModel(LeagueModelEnums.duel);
        request.addCallbackParam(targetDuelModel.getTotalStar());
        for (Integer cityId : cityIds) {
            List<PbLeagueDuel.LeagueDuelCityPointInfo> leagueDuelCityPointInfos = targetDuelModel.genLeagueDuelCityPointInfoListPb(cityId);
            request.addCallbackParam(leagueDuelCityPointInfos);
        }
    }

    /**
     * 获取据点布阵信息
     */
    @MessageMethod(description = "获取据点布阵信息", invokeType = MethodInvokeType.sync)
    private static void getPointLineupInfo(ServerCommandRequest request, CommandRequestParams params) {
        long leagueId = params.getParam(0);
        int cityId = params.getParam(1);
        int pointId = params.getParam(2);
        League league = LeagueManager.getLeagueById(leagueId);
        LeagueDuelModel targetDuelModel = league.getModel(LeagueModelEnums.duel);
        PbLeagueDuel.DuelPointLineup duelPointLineup = targetDuelModel.genLeagueDuelPointLineupInfoPb(cityId, pointId);
        request.addCallbackParam(duelPointLineup);
    }

    /**
     * 战斗请求
     */
    @MessageMethod(description = "战斗请求", invokeType = MethodInvokeType.sync)
    private static void fightReq(ServerCommandRequest request, CommandRequestParams params) {
        long leagueId = params.getParam(0);
        int cityId = params.getParam(1);
        int pointId = params.getParam(2);
        long playerId = params.getParam(3);
        int diff = params.getParam(3);
        long time = params.getParam(4);
        League league = LeagueManager.getLeagueById(leagueId);
        LeagueDuelModel targetDuelModel = league.getModel(LeagueModelEnums.duel);
        DuelCityPointInfo duelCityPointInfo = targetDuelModel.getDuelCityPointInfo(cityId, pointId);
        Object[] paramsArr = new Object[5];
        paramsArr[0] = PbUtilCompress.encode(duelCityPointInfo);
        paramsArr[1] = playerId;
        paramsArr[2] = diff;
        paramsArr[3] = leagueId;
        paramsArr[4] = time;
        TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, request.getServerId(),
                CommandRequests.newServerCommandRequest("LeagueDuelGameCommandService.fightRst"), paramsArr);
    }

    /**
     * 战斗响应
     */
    @MessageMethod(description = "战斗响应", invokeType = MethodInvokeType.sync)
    private static void fightRst(ServerCommandRequest request, CommandRequestParams params) {
        DuelCityPointInfo duelCityPointInfo = PbUtilCompress.decode(DuelCityPointInfo.class, params.getParam(0));
        int cityId = duelCityPointInfo.getCityId();
        int pointId = duelCityPointInfo.getPointId();
        LeagueDuelPointTemplate pointTemplate = LeagueDuelService.getDuelPointTemplateMap()
                .get(cityId).get(pointId);
        long playerId = params.getParam(1);
        long time = params.getParam(4);
        if (!PlayerManager.isOnline(playerId)) {
            return;
        }
        Player player = PlayerManager.getPlayer(playerId);
        PbProtocol.DuelFightRst.Builder rst = PbProtocol.DuelFightRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        LeagueDuelStage leagueDuelStage = null;
        logic:
        {
            League league = LeagueManager.getLeagueByPlayer(player);
            LeagueDuelModel duelModel = league.getModel(LeagueModelEnums.duel);
            int diff = params.getParam(2);
            int targetLeagueId = params.getParam(3);
            if (pointTemplate.pointType == DuelPointType.CityDefense) {
                if (duelCityPointInfo.getCityDefenceHp() <= 0) {
                    rst.setResult(Text.genServerRstInfo(Text.城防军已被攻破));
                    break logic;
                }
            } else {
                int diffStar = pointTemplate.getDiffStar(diff);
                if (duelCityPointInfo.getStar() >= diffStar) {
                    rst.setResult(Text.genServerRstInfo(Text.据点该难度已攻破));
                    break logic;
                }
            }
            //这边需要二次判断一下是否正在被攻打
            if (duelModel.isFighting(cityId, pointId)) {
                rst.setResult(Text.genServerRstInfo(Text.据点正在被攻打));
                break logic;
            }
            duelModel.addFighting(cityId, pointId);
            //进入战斗
            leagueDuelStage = new LeagueDuelStage(player, targetLeagueId, duelCityPointInfo, diff,
                    duelModel.getCityDefenceBuffLv(cityId), duelModel.isEyesBreak(cityId));
            leagueDuelStage.init();
            rst.setStage(leagueDuelStage.getStageRecord());
        }
        player.send(PtCode.DUEL_FIGHT_SERVER, rst.build(), time);
        if (Objects.nonNull(leagueDuelStage)) {
            CombatManager.combatPrepare(leagueDuelStage);
        }

    }

    /**
     * 战斗结果同步
     */
    @MessageMethod(description = "战斗结果同步", invokeType = MethodInvokeType.sync)
    private static void fightResultSync(ServerCommandRequest request, CommandRequestParams params) {
        long leagueId = params.getParam(0);
        int addStar = params.getParam(1);
        boolean isCityBreak = params.getParam(2);
        DuelCityPointInfo duelCityPointInfo = PbUtilCompress.decode(DuelCityPointInfo.class, params.getParam(3));
        League league = LeagueManager.getLeagueById(leagueId);
        LeagueDuelModel targetDuelModel = league.getModel(LeagueModelEnums.duel);
        targetDuelModel.updatePointInfo(duelCityPointInfo);
        targetDuelModel.fightFinishSync(false, duelCityPointInfo, addStar);
        if (isCityBreak) {
            targetDuelModel.costRemainCityNum();
        }

    }

    /**
     * deBuff攻击
     */
    @MessageMethod(description = "deBuff攻击", invokeType = MethodInvokeType.sync)
    private static void deBuffAtk(ServerCommandRequest request, CommandRequestParams params) {
        long leagueId = params.getParam(0);
        int cityId = params.getParam(1);
        int pointId = params.getParam(2);
        League league = LeagueManager.getLeagueById(leagueId);
        LeagueDuelModel targetDuelModel = league.getModel(LeagueModelEnums.duel);
        DuelCityPointInfo duelCityPointInfo = targetDuelModel.getDuelCityPointInfo(cityId, pointId);
        int result = Text.没有异常;
        logic:
        {
            LeagueDuelPointTemplate pointTemplate = LeagueDuelService.getLeagueDuelPointTemplate(cityId, pointId);
            if (Objects.isNull(pointTemplate)) {
                result = Text.对应的模板数据找不到;
                break logic;
            }
            if (duelCityPointInfo.getStar() >= pointTemplate.difficultyStar) {
                result = Text.据点所有难度已攻破;
                break logic;
            }
            int deBuffLv = duelCityPointInfo.getDeBuffLv();
            if (deBuffLv >= LeagueDuelService.getConstant().getDebuffSuperpositionNum()) {
                result = Text.该据点deBuff等级已达上限;
                break logic;
            }
            duelCityPointInfo.setDeBuffLv(duelCityPointInfo.getDeBuffLv() + 1);
            targetDuelModel.syncPointSync(true, duelCityPointInfo);
        }
        request.addCallbackParam(result);
        if (result != Text.没有异常) {
            request.addCallbackParam(PbUtilCompress.encode(duelCityPointInfo));
        }
    }

    /**
     * 结算结果通知
     */
    @MessageMethod(description = "结算结果通知", invokeType = MethodInvokeType.sync)
    private static void notifyResult(ServerCommandRequest request, CommandRequestParams params) {
        DuelOverResultMapInfo resultMapInfo = PbUtilCompress.decode(DuelOverResultMapInfo.class, params.getParam(0));
        for (Map.Entry<Long, DuelOverResultInfo> entry : resultMapInfo.getResultInfoMap().entrySet()) {
            Long leagueId = entry.getKey();
            DuelOverResultInfo targetResultInfo = entry.getValue();
            League league = LeagueManager.getLeagueById(leagueId);
            LeagueDuelModel leagueModel = league.getModel(LeagueModelEnums.duel);
            leagueModel.overFinish(!targetResultInfo.isWin());
            //上一场数据
            DuelLastMainInfo duelLastMainInfo = LeagueDuelHelper.newDuelLastMainInfo(league, targetResultInfo.getSimpleDuelInfo());
            leagueModel.setLastMainInfo(duelLastMainInfo);
            LeagueDuelHelper.combatHistoryRecord(league, targetResultInfo, leagueModel.isWin());
        }
    }
}

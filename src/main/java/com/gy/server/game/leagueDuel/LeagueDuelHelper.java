package com.gy.server.game.leagueDuel;

import com.google.common.collect.Lists;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.log.SystemLogger;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueModelEnums;
import com.gy.server.game.league.LeagueService;
import com.gy.server.game.league.bean.LeagueMember;
import com.gy.server.game.leagueDuel.async.LeagueDuelSaveHistoryRecordAsync;
import com.gy.server.game.leagueDuel.record.LeagueDuelHistoryRecord;
import com.gy.server.game.leagueDuel.template.DuelReceiveRewardType;
import com.gy.server.game.leagueDuel.template.LeagueDuelChestTemplate;
import com.gy.server.game.leagueDuel.template.LeagueDuelRewardTemplate;
import com.gy.server.game.mail.MailManager;
import com.gy.server.game.mail.MailType;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.record.RecordType;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbCommons;
import com.gy.server.packet.PbRecord;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.*;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.*;

/**
 * 帮派对决帮助类
 *
 * <AUTHOR> - [Created on 2023/5/25 17:07]
 */
public class LeagueDuelHelper {


    /**
     * 历史战报记录
     */
    public static void combatHistoryRecord(League myLeague, DuelOverResultInfo rivalDuel, boolean isWin) {
        LeagueDuelModel duelModel = myLeague.getModel(LeagueModelEnums.duel);
        PbRecord.LeagueDuelHistoryRecordInfo myRecord = duelModel.genPbLeagueDuelHistoryRecordInfo();
        PbRecord.LeagueDuelHistoryRecordInfo rivalRecord = PbRecord.LeagueDuelHistoryRecordInfo.newBuilder()
                .setId(rivalDuel.getLeagueId())
                .setName(rivalDuel.getSimpleDuelInfo().getLeagueName())
                .setIconName(rivalDuel.getSimpleDuelInfo().getIconName())
                .setIconFrameId(rivalDuel.getSimpleDuelInfo().getIconFrameId())
                .setTotalPower(rivalDuel.getSimpleDuelInfo().getLeagueFightPower())
                .setTotalStar(rivalDuel.getStar()).build();
        LeagueDuelHistoryRecord historyRecord = new LeagueDuelHistoryRecord(RecordType.leagueDuelHistory, isWin, myRecord, rivalRecord);
        if(myLeague.getLeader() > 0) {
            ThreadPool.execute(new LeagueDuelSaveHistoryRecordAsync(historyRecord, myLeague.getLeader()));
        }else{
            SystemLogger.warn("system league leader is 0, can not save history record");
        }

    }

    public static LeagueDuelRankNodeInfo newLeagueDuelRankNodeInfo(League league) {
        LeagueDuelModel leagueDuelModel = league.getModel(LeagueModelEnums.duel);
        LeagueDuelRankNodeInfo leagueDuelRankNodeInfo = new LeagueDuelRankNodeInfo();
        leagueDuelRankNodeInfo.setLeagueId(league.getLeagueId());
        leagueDuelRankNodeInfo.setLeagueName(league.getName());
        leagueDuelRankNodeInfo.setLeaderName(league.getLeaderName());
        leagueDuelRankNodeInfo.setIconName(league.getIconName());
        leagueDuelRankNodeInfo.setIconFrameId(league.getIconFrameId());
        leagueDuelRankNodeInfo.setLeaderId(league.getLeader());
        leagueDuelRankNodeInfo.setLeaderName(league.getLeaderName());
        leagueDuelRankNodeInfo.setWinNum(leagueDuelModel.getWinningStreakNum());
        leagueDuelRankNodeInfo.setLastMatchLeagueId(leagueDuelModel.getLastMatchLeagueId());
        leagueDuelRankNodeInfo.setLeagueFightPower(LeagueDuelHelper.getLeagueDuelFightPower(league));
        leagueDuelRankNodeInfo.setTotalFightPower(league.getLeagueFightPower());
        leagueDuelRankNodeInfo.setMemberCount(league.getMemberCount());
        leagueDuelRankNodeInfo.setRemainCityNum(leagueDuelModel.getRemainCityNum());
        leagueDuelRankNodeInfo.setTotalStar(leagueDuelModel.getTotalStar());
        leagueDuelRankNodeInfo.setServerId(Player.getServerId(league.getLeagueId()));
        return leagueDuelRankNodeInfo;
    }

    public static DuelLastMainInfo newDuelLastMainInfo(League league, LeagueDuelRankNodeInfo targetResultInfo) {
        LeagueDuelModel leagueDuelModel = league.getModel(LeagueModelEnums.duel);
        DuelLastMainInfo duelLastMainInfo = new DuelLastMainInfo();
        duelLastMainInfo.setMyLeagueInfo(LeagueDuelHelper.newLeagueDuelRankNodeInfo(league));
        duelLastMainInfo.setTargetLeagueInfo(targetResultInfo);
        duelLastMainInfo.setWin(leagueDuelModel.isWin());
        return duelLastMainInfo;
    }

    /**
     * 获取活跃玩家总战力
     * 活跃玩家总战力乘以战力系数
     */
    public static long getLeagueDuelFightPower(League league) {
        Map<Long, LeagueMember> memberMap = league.getMemberMap();
        double totalFightPower = 0;
        for (LeagueMember member : memberMap.values()) {
            if (member.isActive(LeagueDuelService.getConstant().getTotalFightPowerDay() * 24)) {
                MiniGamePlayer miniPlayer = PlayerManager.getMiniPlayer(member.getPlayerId());
                if (Objects.nonNull(miniPlayer)) {
                    totalFightPower += miniPlayer.getFightingPower();
                }
            }
        }
        LeagueDuelModel leagueDuelModel = league.getModel(LeagueModelEnums.duel);
        int winNum = leagueDuelModel.getWinNum();
        if (winNum > 0) {
            totalFightPower = totalFightPower * winNum * LeagueDuelService.getConstant().getWinCoefficient();
        }
        int loseNum = leagueDuelModel.getLoseNum();
        if (loseNum > 0) {
            totalFightPower = totalFightPower * loseNum * LeagueDuelService.getConstant().getFailCoefficient();
        }
        return (long) totalFightPower;
    }

    /**
     * TODO 假数据 测试使用
     * 获取战区内服务器
     */
    public static List<Integer> getZoneSever(long zoneId) {
        List<Integer> serverList = Lists.newArrayList();
        serverList.add(990);
        serverList.add(991);
        return serverList;
    }

    /**
     * 发送团队奖励
     *
     * @param league 帮派
     */
    public static void sendTeamReward(League league) {
        Map<DuelReceiveRewardType, LeagueDuelRewardTemplate> duelRewardTemplateTypeMap = LeagueDuelService.getDuelRewardTemplateTypeMap();
        List<Reward> rewardList = new ArrayList<>();
        MailType mailType;
        LeagueDuelModel duelModel = league.getModel(LeagueModelEnums.duel);
        if (duelModel.isWin()) {
            mailType = MailType.leagueDuelTeamWinReward;
            LeagueDuelRewardTemplate duelRewardTemplate = duelRewardTemplateTypeMap.get(DuelReceiveRewardType.teamWin);
            rewardList.addAll(Reward.templateCollectionToReward(duelRewardTemplate.rewardList));
        } else {
            mailType = MailType.leagueDuelTeamLoseReward;
            LeagueDuelRewardTemplate duelRewardTemplate = duelRewardTemplateTypeMap.get(DuelReceiveRewardType.teamLose);
            rewardList.addAll(Reward.templateCollectionToReward(duelRewardTemplate.rewardList));
        }
        PbCommons.PbText titleText = Text.genText(mailType.getTitleId()).build();
        PbCommons.PbText contentText = Text.genText(mailType.getContentId()).build();
        for (Long playerId : league.getMemberMap().keySet()) {
            MailManager.sendMail(
                    mailType,
                    playerId,
                    titleText,
                    contentText,
                    ServerConstants.getCurrentTimeMillis(),
                    rewardList);
        }
    }

    /**
     * 补发个人奖励
     *
     * @param league 帮派
     */
    public static void reissuePersonReward(League league) {
        LeagueDuelModel duelModel = league.getModel(LeagueModelEnums.duel);
        Map<Long, PersonRewardBean> personRewardBeanMap = duelModel.getPersonRewardBeanMap();
        Map<Integer, LeagueDuelRewardTemplate> duelRewardTemplateMap = LeagueDuelService.getDuelRewardTemplateMap();
        for (Map.Entry<Long, PersonRewardBean> entry : personRewardBeanMap.entrySet()) {
            Long playerId = entry.getKey();
            List<Reward> reissueRewardList = new ArrayList<>();
            PersonRewardBean personRewardBean = entry.getValue();
            for (Map.Entry<Integer, Integer> rewardEntry : personRewardBean.getRewardStatusMap().entrySet()) {
                Integer rewardId = rewardEntry.getKey();
                //未领取的奖励补发
                if (rewardEntry.getValue() == PersonRewardBean.STATUS_COMPLETED) {
                    personRewardBean.getRewardStatusMap().put(rewardId, PersonRewardBean.STATUS_RECEIVED);
                    reissueRewardList.addAll(Reward.templateCollectionToReward(duelRewardTemplateMap.get(rewardId).rewardList));
                }
            }
            if (!reissueRewardList.isEmpty()) {
                Reward.merge(reissueRewardList);
                MailType mailType = MailType.leagueDuelReissuePersonReward;
                PbCommons.PbText titleText = Text.genText(mailType.getTitleId()).build();
                PbCommons.PbText contentText = Text.genText(mailType.getContentId()).build();
                MailManager.sendMail(
                        mailType,
                        playerId,
                        titleText,
                        contentText,
                        ServerConstants.getCurrentTimeMillis(),
                        reissueRewardList);
            }
        }
    }

    /**
     * 宝箱奖励发奖
     *
     * @param league 帮派
     */
    public static void sendBoxReward(League league) {
        Map<Integer, LeagueDuelChestTemplate> duelChestTemplateMap = LeagueDuelService.getDuelChestTemplateMap();
        LeagueDuelModel duelModel = league.getModel(LeagueModelEnums.duel);
        Map<Integer, Map<Integer, DuelCityPointInfo>> pointInfoMap = duelModel.getPointInfoMap();
        //获取宝箱奖励id
        List<Integer> canReceiveRewardsIds = new ArrayList<>();
        for (LeagueDuelChestTemplate duelChestTemplate : duelChestTemplateMap.values()) {
            int cityId = duelChestTemplate.cityId;
            Map<Integer, DuelCityPointInfo> pointInfos = pointInfoMap.get(cityId);
            if(CollectionUtil.isNotEmpty(pointInfos)){
                Set<Integer> hasPassPointList = new HashSet<>();
                for (DuelCityPointInfo pointInfo : pointInfos.values()) {
                    if(duelModel.isBreak(pointInfo)){
                        hasPassPointList.add(pointInfo.getPointId());
                    }
                }
                if (hasPassPointList.containsAll(duelChestTemplate.guardList)) {
                    canReceiveRewardsIds.add(duelChestTemplate.id);
                }
            }
        }
        if (CollectionUtil.isNotEmpty(canReceiveRewardsIds)) {
            for (long memberId : league.getAllMember()) {
                //检查哪些帮派成员没有全部领取
                Set<Integer> canReceiveRewardIdsTemp = new HashSet<>(canReceiveRewardsIds);
                Set<Integer> hasChessRewardIdSet = duelModel.getHadReceiveChessRewards().getOrDefault(memberId, new HashSet<>());
                canReceiveRewardIdsTemp.removeAll(hasChessRewardIdSet);
                if (CollectionUtil.isNotEmpty(canReceiveRewardIdsTemp)) {
                    //统计奖励
                    List<Reward> rewardList = new ArrayList<>();
                    for (Integer rewardId : canReceiveRewardIdsTemp) {
                        LeagueDuelChestTemplate duelChestTemplate = duelChestTemplateMap.get(rewardId);
                        rewardList.addAll(Reward.templateCollectionToReward(duelChestTemplate.rewardList));
                    }
                    //合并奖励并且发奖
                    Reward.merge(rewardList);
                    sendMail(memberId, rewardList);
                }
            }



        }
    }

    private static void sendMail(long playerId, List<Reward> rewardList){
        MailType mailType = MailType.leagueDuelBoxReward;
        PbCommons.PbText titleText = Text.genText(mailType.getTitleId()).build();
        PbCommons.PbText contentText = Text.genText(mailType.getContentId()).build();
        MailManager.sendMail(
                mailType,
                playerId,
                titleText,
                contentText,
                ServerConstants.getCurrentTimeMillis(),
                rewardList);
    }

}

package com.gy.server.game.leagueDuel.status.impl;

import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.leagueDuel.LeagueDuelGlobalData;
import com.gy.server.game.leagueDuel.LeagueDuelService;
import com.gy.server.game.leagueDuel.async.LeagueDuelPullMirrorAsync;
import com.gy.server.game.leagueDuel.status.DuelNodeType;
import com.gy.server.game.leagueDuel.status.ILeagueDuelStatusHandler;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.DuelStatusType;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.time.LocalDateTime;

/**
 * 拉取镜像
 *
 * <AUTHOR> - [Created on 2023/5/26 14:52]
 */
public class LeagueDuelPullMirrorHandler implements ILeagueDuelStatusHandler {
    @Override
    public DuelStatusType handleStatus() {
        return DuelStatusType.pullMirror;
    }


    @Override
    public void handler(LeagueDuelGlobalData globalData) {
        //游戏服去拉数据 跨服上报 本服自己处理
        LeagueDuelPullMirrorAsync leagueDuelPullMirrorAsync = new LeagueDuelPullMirrorAsync();
        ThreadPool.execute(leagueDuelPullMirrorAsync);
        //切换状态
        changeStatus(DuelStatusType.beforeBattle);
    }

    @Override
    public LocalDateTime getEndTime() {
        Integer duration = LeagueDuelService.getConstant().getOnTimeMap().get(DuelNodeType.lineup).getRight();
        LeagueDuelGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueDuel);
        return globalData.getOpenTime().plusMinutes(duration);
    }
}

package com.gy.server.game.leagueDuel.template;

import com.gy.server.game.drop.RewardTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * 帮派对决奖励表
 *
 * <AUTHOR> - [Created on 2023/5/25 10:49]
 */
public class LeagueDuelRewardTemplate {
    /**
     * 奖励ID
     */
    public int id;
    /**
     * 领取奖励类型
     */
    public DuelReceiveRewardType receiveRewardType;
    /**
     * 奖励列表
     */
    public List<RewardTemplate> rewardList = new ArrayList<>();

}



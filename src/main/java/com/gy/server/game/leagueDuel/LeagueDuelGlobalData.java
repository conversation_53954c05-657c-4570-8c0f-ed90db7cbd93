package com.gy.server.game.leagueDuel;

import com.gy.server.game.global.GlobalData;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueData;
import com.gy.server.game.league.LeagueDataInterface;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.DuelStatusType;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.LeagueDuelGlobalDataDb;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * 帮派对决全局数据
 *
 * <AUTHOR> - [Created on 2023/5/25 13:26]
 */
public class LeagueDuelGlobalData extends GlobalData implements LeagueDataInterface {

    /**
     * 整届比赛的状态
     */
    private volatile DuelStatusType status = DuelStatusType.noStart;
    /**
     * 阶段开启时间
     */
    private LocalDateTime openTime;
    /**
     * 是否是跨服
     */
    private boolean isCross;

    /**
     * 对决匹配信息
     * 1帮派和2帮派匹配 会有两条数据 1-2 和 2-1 方便查找
     */
    private Map<Long, Long> matchMap = new ConcurrentHashMap<>();
    /**
     * 报名的帮派ID
     */
    private Set<Long> joinLeagueSet = new CopyOnWriteArraySet<>();


    public DuelStatusType getStatus() {
        return status;
    }

    public void setStatus(DuelStatusType status) {
        this.status = status;
    }

    public LocalDateTime getOpenTime() {
        return openTime;
    }

    public void setOpenTime(LocalDateTime openTime) {
        this.openTime = openTime;
    }

    public boolean isCross() {
        return isCross;
    }

    public void setCross(boolean cross) {
        isCross = cross;
    }

    public Map<Long, Long> getMatchMap() {
        return matchMap;
    }

    public Set<Long> getJoinLeagueSet() {
        return joinLeagueSet;
    }

    public void setJoinLeagueSet(Set<Long> joinLeagueSet) {
        this.joinLeagueSet = joinLeagueSet;
    }

    @Override
    public void readFromPb(byte[] bytes) throws Exception {
        LeagueDuelGlobalDataDb duelGlobalDataDb = PbUtilCompress.decode(LeagueDuelGlobalDataDb.class, bytes);
        this.status = duelGlobalDataDb.getStatus();
        if (duelGlobalDataDb.getOpenTime() > 0) {
            this.openTime = DateTimeUtil.toLocalDateTime(duelGlobalDataDb.getOpenTime());
        }
        this.isCross = duelGlobalDataDb.isCross();
        this.matchMap = duelGlobalDataDb.getMatchMap();
        this.joinLeagueSet = duelGlobalDataDb.getJoinLeagueSet();
    }

    @Override
    public byte[] writeToPb() {
        LeagueDuelGlobalDataDb duelGlobalDataDb = new LeagueDuelGlobalDataDb();
        duelGlobalDataDb.setStatus(this.status);
        if (Objects.nonNull(this.openTime)) {
            duelGlobalDataDb.setOpenTime(DateTimeUtil.toMillis(this.openTime));
        }
        duelGlobalDataDb.setCross(this.isCross);
        duelGlobalDataDb.setMatchMap(this.matchMap);
        duelGlobalDataDb.setJoinLeagueSet(this.joinLeagueSet);
        return PbUtilCompress.encode(duelGlobalDataDb);
    }

    @Override
    public boolean canMerge(long leagueId) {
        return matchMap.get(leagueId) == null;
    }

    @Override
    public void merge(League selfLeague, League targetLeague) {

    }

    @Override
    public void removeLeague(League league) {

    }

    @Override
    public void quitLeague(long pid) {

    }
}

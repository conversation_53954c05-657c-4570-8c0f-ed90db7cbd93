package com.gy.server.game.leagueDuel.template;

/**
 * 帮派对决城池点位表
 *
 * <AUTHOR> - [Created on 2023/5/25 10:02]
 */
public class LeagueDuelPointTemplate {

    /**
     * 唯一ID
     */
    public int id;
    /**
     * 城池ID
     */
    public int cityId;
    /**
     * 位置信息ID
     */
    public int pointId;
    /**
     * 建筑类型
     */
    public DuelPointType pointType;
    /**
     * 点位排名
     */
    public int rank;
    /**
     * 普通产出星数
     */
    public int star;
    /**
     * 困难产出星数
     */
    public int difficultyStar;
    /**
     * 机器人生成规则
     */
    public String robotRule;

    /**
     * 根据难度获得的星级
     *
     * @param diff 难度
     * @return 该难度获得的星级
     */
    public int getDiffStar(int diff) {
        if (diff == 1) {
            return star;
        } else {
            return difficultyStar;
        }
    }


}

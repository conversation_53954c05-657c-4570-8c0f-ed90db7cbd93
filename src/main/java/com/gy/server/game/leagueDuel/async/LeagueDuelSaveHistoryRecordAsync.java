package com.gy.server.game.leagueDuel.async;

import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.leagueDuel.record.LeagueDuelHistoryRecord;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.record.RecordManager;

/**
 * 帮派对决保存战报
 *
 * <AUTHOR> - [Created on 2023/12/7 18:30]
 */
public class LeagueDuelSaveHistoryRecordAsync extends AsyncCall {

    LeagueDuelHistoryRecord historyRecord;

    long playerId;

    Player player;

    public LeagueDuelSaveHistoryRecordAsync(LeagueDuelHistoryRecord historyRecord, long playerId) {
        this.historyRecord = historyRecord;
        this.playerId = playerId;
    }

    @Override
    public void asyncExecute() {
        this.player = PlayerManager.getPlayer(playerId);
    }

    @Override
    public void execute() {
        RecordManager.addRecord(historyRecord, player);
    }


}

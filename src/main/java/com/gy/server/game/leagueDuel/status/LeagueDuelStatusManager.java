package com.gy.server.game.leagueDuel.status;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.leagueDuel.LeagueDuelGlobalData;
import com.gy.server.game.leagueDuel.async.DuelWorldMessageAsync;
import com.gy.server.utils.ReflectionUtil;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.DuelStatusType;
import org.apache.commons.lang3.tuple.Pair;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 帮派对决状态管理类
 *
 * <AUTHOR> - [Created on 2023/5/25 13:37]
 */
public class LeagueDuelStatusManager {
    private static volatile boolean isInit = false;


    //待发送的消息
    private CopyOnWriteArrayList<Pair<String, Object[]>> messageQueue = new CopyOnWriteArrayList<>();
    //下次发送消息的时间
    private long nextSendMillions = ServerConstants.getCurrentTimeMillis();

    private static final Map<DuelStatusType, ILeagueDuelStatusHandler> HANDLERS = new HashMap<>();

    public static void init(){
        if(!isInit){
            isInit = true;
            Set<Class<? extends ILeagueDuelStatusHandler>> classSet = ReflectionUtil.getSubClassSetNoInterfaceAndAbstract(ILeagueDuelStatusHandler.class, "com.gy.server.game.leagueDuel.status.impl");
            for (Class<? extends ILeagueDuelStatusHandler> handler : classSet) {
                try {
                    ILeagueDuelStatusHandler instance = handler.newInstance();
                    HANDLERS.put(instance.handleStatus(), instance);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }


    public void tick() {
        init();
        LeagueDuelGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueDuel);
        HANDLERS.get(globalData.getStatus()).handler(globalData);
        //伺机去发送失败的消息 一次tick执行一个
        long currentTimeMillis = ServerConstants.getCurrentTimeMillis();
        if (!messageQueue.isEmpty() && currentTimeMillis >= nextSendMillions) {
            Pair<String, Object[]> removeMessage = messageQueue.remove(0);
            DuelWorldMessageAsync.deal(removeMessage.getKey(), removeMessage.getValue());
            this.nextSendMillions = currentTimeMillis + DateTimeUtil.MillisOfSecond * 2;
        }
    }

    public ILeagueDuelStatusHandler getHandler(DuelStatusType duelStatusType) {
        return HANDLERS.get(duelStatusType);
    }

    private static final LeagueDuelStatusManager instance = new LeagueDuelStatusManager();

    private LeagueDuelStatusManager() {
    }

    public static LeagueDuelStatusManager getInstance() {
        return instance;
    }

    public void addMessage(String message, Object[] params) {
        messageQueue.add(Pair.of(message, params));
    }
}

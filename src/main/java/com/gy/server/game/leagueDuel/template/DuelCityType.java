package com.gy.server.game.leagueDuel.template;

import java.util.HashMap;
import java.util.Map;

/**
 * 对决城池类型
 *
 * <AUTHOR> - [Created on 2023/5/25 10:28]
 */
public enum DuelCityType {
    /**
     * 小城
     */
    smallCity(1),
    /**
     * 中城
     */
    midCity(2),
    /**
     * 大城
     */
    bigCity(3),

    ;
    private final int type;

    private static final Map<Integer, DuelCityType> map = new HashMap<>();

    DuelCityType(int type) {
        this.type = type;
    }

    public static DuelCityType of(int type) {
        return map.get(type);
    }

    static {
        for (DuelCityType value : values()) {
            map.put(value.type, value);
        }
    }

}

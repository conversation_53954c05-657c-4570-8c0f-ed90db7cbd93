package com.gy.server.game.leagueDuel.status.impl;

import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.leagueDuel.LeagueDuelGlobalData;
import com.gy.server.game.leagueDuel.LeagueDuelHelper;
import com.gy.server.game.leagueDuel.LeagueDuelService;
import com.gy.server.game.leagueDuel.async.DuelWorldMessageAsync;
import com.gy.server.game.leagueDuel.status.ILeagueDuelStatusHandler;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.DuelStatusType;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.LeagueDuelRankInfo;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.LeagueDuelRankNodeInfo;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 数据准备阶段
 *
 * <AUTHOR> - [Created on 2023/5/25 14:50]
 */
public class LeagueDuelReadyHandler implements ILeagueDuelStatusHandler {
    @Override
    public DuelStatusType handleStatus() {
        return DuelStatusType.ready;
    }


    @Override
    public void handler(LeagueDuelGlobalData globalData) {
        //根据公会战力去排序
        List<LeagueDuelRankNodeInfo> rankLeagueList = new ArrayList<>();
        //拉取满足条件的帮派
        List<League> leagues = LeagueManager.getLeagues();
        for (League league : leagues) {
            if (league.isActiveLeague(LeagueDuelService.getConstant().getJoinActiveMember(), LeagueDuelService.getConstant().getJoinActiveDay() * DateTimeUtil.MillisOfDay)) {
                LeagueDuelRankNodeInfo leagueDuelRankInfo = LeagueDuelHelper.newLeagueDuelRankNodeInfo(league);
                rankLeagueList.add(leagueDuelRankInfo);
            }
        }
        if (globalData.isCross()) {
            //上报到world服
            LeagueDuelRankInfo leagueDuelRankInfo = new LeagueDuelRankInfo();
            leagueDuelRankInfo.setRankNodeInfoList(rankLeagueList);
            Object[] params = new Object[1];
            params[0] = PbUtilCompress.encode(leagueDuelRankInfo);
            DuelWorldMessageAsync.deal("LeagueDuelWorldCommandService.uploadLeague", params);
        } else {
            //小于两个就不开了
            if (rankLeagueList.size() < 2) {
                changeStatus(DuelStatusType.abort);
                return;
            }
            Collections.sort(rankLeagueList);
            //两两匹配
            int group = rankLeagueList.size() / 2;
            for (int i = 1; i <= group; i++) {
                LeagueDuelRankNodeInfo leftLeague = rankLeagueList.get(0);
                //找对手
                for (int j = 1; j < rankLeagueList.size(); j++) {
                    LeagueDuelRankNodeInfo rightLeague = rankLeagueList.get(j);
                    if (leftLeague.getLastMatchLeagueId() != rightLeague.getLeagueId()) {
                        // 添加两条数据 方便查找
                        globalData.getMatchMap().put(leftLeague.getLeagueId(), rightLeague.getLeagueId());
                        globalData.getMatchMap().put(rightLeague.getLeagueId(), leftLeague.getLeagueId());
                        //移除掉
                        rankLeagueList.remove(leftLeague);
                        rankLeagueList.remove(rightLeague);
                        globalData.getJoinLeagueSet().add(leftLeague.getLeagueId());
                        globalData.getJoinLeagueSet().add(rightLeague.getLeagueId());
                        break;
                    }
                }
            }
        }
        changeStatus(DuelStatusType.beforeLineup);
    }

    @Override
    public LocalDateTime getEndTime() {
        LeagueDuelGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueDuel);
        return globalData.getOpenTime();
    }


}

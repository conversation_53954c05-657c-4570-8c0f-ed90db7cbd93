package com.gy.server.game.leagueDuel.record;

import com.google.common.collect.Sets;
import com.gy.server.game.record.Record;
import com.gy.server.game.record.RecordType;
import com.gy.server.packet.PbRecord;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.LeagueDuelHistoryRecordInfoDb;
import com.ttlike.server.tl.baselib.serialize.record.LeagueDuelHistoryRecordDb;
import com.ttlike.server.tl.baselib.serialize.record.RecordDb;

import java.util.Set;

/**
 * 帮派对决战报-历史战绩
 *
 * <AUTHOR> - [Created on 2023/5/30 10:11]
 */
public class LeagueDuelHistoryRecord extends Record {
    private PbRecord.LeagueDuelHistoryRecordInfo myInfo;
    private PbRecord.LeagueDuelHistoryRecordInfo rivalInfo;

//    public LeagueDuelHistoryRecord(LeagueDuelHistoryRecordDb beanRecordDb) {
//        super(beanRecordDb.getType(), beanRecordDb.getTime(), beanRecordDb.isWin(), beanRecordDb.getCombatRecordId(), beanRecordDb.getServerId());
//        myInfo = parseLeagueDuelHistoryRecordInfo(beanRecordDb.getMyInfo());
//        rivalInfo = parseLeagueDuelHistoryRecordInfo(beanRecordDb.getRivalInfo());
//    }

    private PbRecord.LeagueDuelHistoryRecordInfo parseLeagueDuelHistoryRecordInfo(LeagueDuelHistoryRecordInfoDb recordInfoDb){
        return PbRecord.LeagueDuelHistoryRecordInfo.newBuilder()
                .setId(recordInfoDb.getId())
                .setName(recordInfoDb.getName())
                .setIconName(recordInfoDb.getIconName())
                .setIconFrameId(recordInfoDb.getIconFrameId())
                .setTotalPower(recordInfoDb.getTotalPower())
                .setTotalStar(recordInfoDb.getTotalStar()).build();
    }

    @Override
    protected void subReadFromPb(PbRecord.Record record) {
        PbRecord.LeagueDuelHistoryRecord historyRecord = record.getDuelHistoryRecord();
        this.myInfo = historyRecord.getMyInfo();
        this.rivalInfo = historyRecord.getRivalInfo();
    }

    @Override
    protected void subWriteToPb(PbRecord.Record.Builder builder) {
        PbRecord.LeagueDuelHistoryRecord.Builder record = PbRecord.LeagueDuelHistoryRecord.newBuilder()
                .setMyInfo(myInfo)
                .setRivalInfo(rivalInfo);
        builder.setDuelHistoryRecord(record);
    }

    public LeagueDuelHistoryRecord(RecordType recordType, boolean win, PbRecord.LeagueDuelHistoryRecordInfo myInfo,
                                   PbRecord.LeagueDuelHistoryRecordInfo rivalInfo) {
        super(recordType, win, -1, "-1");
        this.myInfo = myInfo;
        this.rivalInfo = rivalInfo;
    }

    public LeagueDuelHistoryRecord() {
    }

    @Override
    protected Set<PbRecord.RecordPlayer> getDetailRecordSet() {
        return Sets.newHashSet();
    }

    public LeagueDuelHistoryRecordDb genLeagueDuelHistoryRecordDb() {
        return new LeagueDuelHistoryRecordDb(genLeagueDuelHistoryRecordInfoDb(myInfo), genLeagueDuelHistoryRecordInfoDb(rivalInfo));
    }

    @Override
    public void readFromDb2(RecordDb db) {
        LeagueDuelHistoryRecordDb bean = db.getLeagueDuelHistoryRecordDb();
        if(bean != null){
            myInfo = parseLeagueDuelHistoryRecordInfo(bean.getMyInfo());
            rivalInfo = parseLeagueDuelHistoryRecordInfo(bean.getRivalInfo());
        }
    }

    @Override
    public void writeToDb(RecordDb db) {
        db.setLeagueDuelHistoryRecordDb(genLeagueDuelHistoryRecordDb());
    }

    public LeagueDuelHistoryRecordInfoDb genLeagueDuelHistoryRecordInfoDb(PbRecord.LeagueDuelHistoryRecordInfo leagueDuelHistoryRecordInfo){
        return new LeagueDuelHistoryRecordInfoDb(leagueDuelHistoryRecordInfo.getId(), leagueDuelHistoryRecordInfo.getName(), leagueDuelHistoryRecordInfo.getIconName(), leagueDuelHistoryRecordInfo.getIconFrameId(),
                leagueDuelHistoryRecordInfo.getTotalPower(), leagueDuelHistoryRecordInfo.getTotalStar());
    }
}

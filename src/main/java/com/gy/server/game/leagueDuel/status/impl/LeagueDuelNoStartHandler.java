package com.gy.server.game.leagueDuel.status.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.leagueDuel.LeagueDuelGlobalData;
import com.gy.server.game.leagueDuel.LeagueDuelService;
import com.gy.server.game.leagueDuel.status.ILeagueDuelStatusHandler;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.DuelStatusType;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 帮派对决未开始
 *
 * <AUTHOR> - [Created on 2023/5/25 14:50]
 */
public class LeagueDuelNoStartHandler implements ILeagueDuelStatusHandler {
    @Override
    public DuelStatusType handleStatus() {
        return DuelStatusType.noStart;
    }


    @Override
    public void handler(LeagueDuelGlobalData globalData) {
        if (Objects.isNull(globalData.getOpenTime())) {
            globalData.setOpenTime(calcOpenTime());
        }
        LocalDateTime now = ServerConstants.getCurrentTimeLocalDateTime();
        if (!now.isBefore(globalData.getOpenTime())) {
            if (LeagueDuelService.getConstant().isOpenCross()) {
                changeStatus(DuelStatusType.checkCross);
            } else {
                changeStatus(DuelStatusType.ready);
            }

        }
    }

    @Override
    public LocalDateTime getEndTime() {
        LeagueDuelGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueDuel);
        return globalData.getOpenTime();
    }
}

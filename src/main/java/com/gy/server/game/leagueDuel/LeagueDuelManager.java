package com.gy.server.game.leagueDuel;

import com.gy.server.game.leagueDuel.status.LeagueDuelStatusManager;
import com.gy.server.utils.function.Ticker;

/**
 * 帮派对决管理类
 *
 * <AUTHOR> - [Created on 2023/5/25 13:24]
 */
public class LeagueDuelManager implements Ticker {

    private static final LeagueDuelManager instance = new LeagueDuelManager();

    public static LeagueDuelManager getInstance() {
        return instance;
    }

    @Override
    public void tick() {
        LeagueDuelStatusManager.getInstance().tick();
    }


}

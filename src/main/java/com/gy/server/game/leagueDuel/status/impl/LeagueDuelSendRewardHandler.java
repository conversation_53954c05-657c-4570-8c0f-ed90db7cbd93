package com.gy.server.game.leagueDuel.status.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.leagueDuel.LeagueDuelGlobalData;
import com.gy.server.game.leagueDuel.LeagueDuelHelper;
import com.gy.server.game.leagueDuel.LeagueDuelService;
import com.gy.server.game.leagueDuel.status.DuelNodeType;
import com.gy.server.game.leagueDuel.status.ILeagueDuelStatusHandler;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.DuelStatusType;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * 发奖状态
 *
 * <AUTHOR> - [Created on 2023/6/6 13:33]
 */
public class LeagueDuelSendRewardHandler implements ILeagueDuelStatusHandler {
    @Override
    public DuelStatusType handleStatus() {
        return DuelStatusType.sendReward;
    }


    @Override
    public void handler(LeagueDuelGlobalData globalData) {
        LocalDateTime now = ServerConstants.getCurrentTimeLocalDateTime();
        if (now.isBefore(globalData.getOpenTime())) {
            return;
        }
        changeStatus(DuelStatusType.beforeEnd);
        //发奖
        Set<Long> joinLeagueSet = globalData.getJoinLeagueSet();
        for (Long leagueId : joinLeagueSet) {
            League league = LeagueManager.getLeagueById(leagueId);
            //1.团队奖励
            LeagueDuelHelper.sendTeamReward(league);
            //2.个人未领取奖励
            LeagueDuelHelper.reissuePersonReward(league);
            //3.攻破城池的宝箱奖励补发
            LeagueDuelHelper.sendBoxReward(league);
        }
    }

    @Override
    public LocalDateTime getEndTime() {
        Integer duration = LeagueDuelService.getConstant().getOnTimeMap().get(DuelNodeType.sendReward).getRight();
        LeagueDuelGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueDuel);
        return globalData.getOpenTime().plusMinutes(duration);
    }
}

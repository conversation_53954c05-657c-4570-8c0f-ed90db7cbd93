package com.gy.server.game.leagueDuel.async;

import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.league.LeagueModelEnums;
import com.gy.server.game.league.bean.LeagueMember;
import com.gy.server.game.leagueDuel.LeagueDuelGlobalData;
import com.gy.server.game.leagueDuel.LeagueDuelModel;
import com.gy.server.game.leagueDuel.LeagueDuelService;
import com.gy.server.game.leagueDuel.template.DuelPointType;
import com.gy.server.game.leagueDuel.template.LeagueDuelCityTemplate;
import com.gy.server.game.leagueDuel.template.LeagueDuelPointTemplate;
import com.gy.server.game.lineup.LineupHelper;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.lineup.bean.LineupInfoBean;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.robot.RobotHelper;
import com.gy.server.game.robot.RobotType;
import com.gy.server.game.robot.bean.BaseRobot;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.DuelCityPointInfo;
import com.ttlike.server.tl.baselib.serialize.lineup.LineupDb;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 拉取公会镜像数据
 *
 * <AUTHOR> - [Created on 2023/5/26 17:07]
 */
public class LeagueDuelPullMirrorAsync extends AsyncCall {
    //帮派成员阵容
    Map<Long, List<LineupInfoBean>> defLineupMap = new HashMap<>();
    Map<Long, MiniGamePlayer> defGamePlayerMap = new HashMap<>();
    Map<Long, Map<Long, Long>> leagueFightLineupPower = new HashMap<>();

    @Override
    public void execute() {
        LeagueDuelGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueDuel);
        for (Long leagueId : globalData.getJoinLeagueSet()) {
            League league = LeagueManager.getLeagueById(leagueId);
            Map<Long, Long> memberFightLineupPower = leagueFightLineupPower.computeIfAbsent(leagueId, map1 -> new HashMap<>());
            List<Long> sortMemberList = new ArrayList<>();
            //排序 高战力
            memberFightLineupPower.entrySet().stream()
                    .sorted(Map.Entry.<Long, Long>comparingByValue()
                            .reversed()).forEachOrdered(e -> sortMemberList.add(e.getKey()));
            //遍历城池去布防
            Map<Integer, LeagueDuelCityTemplate> duelCityTemplateMap = LeagueDuelService.getDuelCityTemplateMap();
            Map<Integer, Map<Integer, LeagueDuelPointTemplate>> duelPointTemplateMap = LeagueDuelService.getDuelPointTemplateMap();

            LeagueDuelModel duelModel = league.getModel(LeagueModelEnums.duel);
            //初始化城池数量
            duelModel.setRemainCityNum(duelCityTemplateMap.size());
            //城池ID
            for (Integer cityId : duelCityTemplateMap.keySet()) {
                Map<Integer, LeagueDuelPointTemplate> pointTemplateMap = duelPointTemplateMap.get(cityId);
                for (LeagueDuelPointTemplate pointTemplate : pointTemplateMap.values()) {
                    int pointId = pointTemplate.pointId;
                    int rank = pointTemplate.rank;
                    DuelCityPointInfo duelCityPointInfo = new DuelCityPointInfo(cityId, pointId);
                    //城防军初始化血量和Buff等级
                    if (pointTemplate.pointType == DuelPointType.CityDefense) {
                        int cityDefenceMaxHp = LeagueDuelService.getConstant().getCityDefenceMaxHp();
                        duelCityPointInfo.setCityDefenceHp(cityDefenceMaxHp);
                        int initLv = cityDefenceMaxHp / LeagueDuelService.getConstant().getCityDefenceAtkDownHp();
                        duelCityPointInfo.setCityDefenceBuffLv(initLv);
                    }
                    // 该排名没有阵容
                    if (rank < 0 || rank > sortMemberList.size()) {
                        duelCityPointInfo.setRobot(true);
                        String[] params = pointTemplate.robotRule.split("_");
                        BaseRobot robotBean = RobotHelper.getRobotBean(RobotType.LEAGUE_DUEL, params[1], params[2], -1);
                        duelCityPointInfo.setRobotInfo(robotBean.genDb());
                    } else {
                        Long memberId = sortMemberList.get(rank - 1);
                        List<LineupInfoBean> lineupInfoBeanList = defLineupMap.get(memberId);
                        List<LineupDb.LineupInfoDb> dbList = LineupInfoBean.toDbList(lineupInfoBeanList);
                        duelCityPointInfo.setLineupDbList(dbList);
                        duelCityPointInfo.setLineupPower(memberFightLineupPower.get(memberId));
                        duelCityPointInfo.setRobot(false);
                        duelCityPointInfo.setMiniPlayer(defGamePlayerMap.get(memberId));
                    }
                    duelModel.addDuelCityPointInfo(duelCityPointInfo);
                }
            }
        }
        //跨服玩法通知world服布防完成
        if (globalData.isCross()) {
            DuelWorldMessageAsync.deal("LeagueDuelWorldCommandService.lineupFinish");
        }
    }

    @Override
    public void asyncExecute() {
        //拉取公会镜像数据去布防
        LeagueDuelGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueDuel);
        for (Long leagueId : globalData.getJoinLeagueSet()) {
            League league = LeagueManager.getLeagueById(leagueId);
            Map<Long, LeagueMember> memberMap = league.getMemberMap();
            Map<Long, Long> memberFightLineupPower = leagueFightLineupPower.computeIfAbsent(leagueId, map1 -> new HashMap<>());
            for (Long memberId : memberMap.keySet()) {
                Player player = PlayerManager.getPlayer(memberId);
                // 拉取镜像如果未空 则给默认上阵
                LineupHelper.nullCheckAndAdd(player, LineupType.duelDefence);
                List<LineupInfoBean> duelDefenceLineup = LineupType.duelDefence.getLineup(player);
                //生成最新的镜像数据
                LineupType.duelDefence.getSaveType().getLineupSave().genMirrorDataBean(player, duelDefenceLineup, LineupType.duelDefence);
                //计算阵容战力用于排序
                long fightingPower = LineupHelper.getFightPowerByLineUpType(LineupType.duelDefence, duelDefenceLineup, player, true);
                memberFightLineupPower.put(memberId, fightingPower);
                defLineupMap.put(memberId, duelDefenceLineup);
                defGamePlayerMap.put(memberId, player.getMiniPlayer());
            }
        }
    }
}

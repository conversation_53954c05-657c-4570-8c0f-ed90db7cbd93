package com.gy.server.game.leagueDuel.template;

import java.util.ArrayList;
import java.util.List;

/**
 * 帮派对决城池表
 *
 * <AUTHOR> - [Created on 2023/5/25 10:24]
 */
public class LeagueDuelCityTemplate {

    /**
     * 城池ID
     */
    public int cityId;

    /**
     * 城池类型
     */
    public DuelCityType cityType;
    /**
     * 城池阵眼Buff
     */
    public int cityBuff;
    /**
     * 城池击破获得星数
     */
    public int cityStar;
    /**
     * 破前置城池ID
     */
    public List<Integer> preCityList = new ArrayList<>();


}

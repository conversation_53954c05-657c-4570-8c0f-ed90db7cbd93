package com.gy.server.game.leagueDuel;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueDataInterface;
import com.gy.server.game.league.LeagueModel;
import com.gy.server.game.leagueDuel.template.DuelPointType;
import com.gy.server.game.leagueDuel.template.LeagueDuelCityTemplate;
import com.gy.server.game.leagueDuel.template.LeagueDuelConst;
import com.gy.server.game.leagueDuel.template.LeagueDuelPointTemplate;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.lineup.bean.LineupBean;
import com.gy.server.game.lineup.bean.LineupInfoBean;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.robot.bean.BaseRobot;
import com.gy.server.packet.PbLeagueDuel;
import com.gy.server.packet.PbLineup;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbRecord;
import com.ttlike.server.tl.baselib.serialize.league.LeagueBlobDb;
import com.ttlike.server.tl.baselib.serialize.leagueDuel.*;
import com.ttlike.server.tl.baselib.serialize.lineup.LineupDb;

import java.util.*;

/**
 * 帮派对决model
 *
 * <AUTHOR> - [Created on 2023/5/24 17:54]
 */
public class LeagueDuelModel extends LeagueModel implements LeagueDataInterface {
    /**
     * 各个城市的点位信息
     */
    private Map<Integer, Map<Integer, DuelCityPointInfo>> pointInfoMap = new HashMap<>();
    /**
     * 连续胜利次数
     */
    private int winningStreakNum;
    /**
     * 战斗结果 只有当战斗完阶段才会有值
     */
    private boolean isWin;
    /**
     * 剩余城池据点数
     */
    private int remainCityNum;
    /**
     * 获得总星数
     */
    private int totalStar;
    /**
     * 上次星级增加时间
     * 相同星级的话 时间小的获胜
     */
    private long lastAddStarTime;
    /**
     * 上次匹配的公会ID
     */
    private long lastMatchLeagueId;
    /**
     * 成员已经战斗的次数
     * Key 成员ID
     */
    private Map<Long, Integer> hasFightNumMap = new HashMap<>();
    /**
     * 额外获得的战斗次数
     * 总次数就是两次+加上这个次数
     */
    private Map<Long, Integer> extraFightNumMap = new HashMap<>();
    /**
     * 成员已经胜利的次数
     * Key 成员ID
     */
    private Map<Long, Integer> hasWinNumMap = new HashMap<>();
    /**
     * 成员剩余的debuff次数
     * Key 成员ID
     */
    private Map<Long, Integer> deBuffNumMap = new HashMap<>();
    /**
     * 上次主界面信息
     */
    private DuelLastMainInfo lastMainInfo;
    /**
     * 集火城市ID
     */
    private int fireCityId;
    /**
     * 集火据点id
     */
    private int firePointId;
    /**
     * 已经攻破的据点ID
     * Key 城市ID Value 据点ID列表
     */
//    private Map<Integer, Set<Integer>> hasPassPointId = new HashMap<>();
    /**
     * 个人奖励类型
     */
    private Map<Long, PersonRewardBean> personRewardBeanMap = new HashMap<>();
    /**
     * 胜利场次
     */
    private int winNum;
    /**
     * 失败场次
     */
    private int loseNum;

    //破敌数
    private Map<Long, Integer> breakCounts = new HashMap<>();
    //御敌数
    private Map<Long, Integer> defenseCounts = new HashMap<>();
    //已经领取次数奖励
    private Map<Long, Set<Integer>> hadReceiveTimesRewards = new HashMap<>();
    private Map<Long, Set<Integer>> hadReceiveChessRewards = new HashMap<>();

    //=========================内存数据 start================
    /**
     * 当前在战场内的玩家
     */
    private Set<Long> battlefieldPlayerSet = new HashSet<>();
    /**
     * 正在被挑战的据点主键ID
     */
    private Set<Integer> fightingPointUidSet = new HashSet<>();
    //=========================内存数据 end  ================


    public LeagueDuelModel(League league) {
        super(league);
    }

    /**
     * 战斗完同步信息
     *
     * @param isAtk 是否是攻击方
     */
    public void fightFinishSync(boolean isAtk, DuelCityPointInfo duelCityPointInfo, int addStar) {
        PbProtocol.DuelSyncRst.Builder rst = PbProtocol.DuelSyncRst.newBuilder();
        //所在城市信息同步 攻击方需要同步对方的城池改变信息
        rst.addSyncData(genCitySyncPb(!isAtk, duelCityPointInfo.getCityId()));
        //据点信息同步 攻击方需要同步对方的城池改变信息
        rst.addSyncData(genPointSyncPb(!isAtk, duelCityPointInfo));
        //星数变化同步 攻击方自己的星数会变化
        rst.addSyncData(genStarSyncPb(isAtk, addStar));
        //同步所有在战场内的玩家
        syncAllBattlefieldPlayer(rst.build());
    }

    public boolean isBreak(DuelCityPointInfo pointInfo){
        LeagueDuelConst constant = LeagueDuelService.getConstant();
        return pointInfo.getStar() >= constant.getCityStarNum();
    }

    /**
     * 同步所有在战场内的玩家
     */
    public void syncAllBattlefieldPlayer(PbProtocol.DuelSyncRst rst) {
        for (Long memberId : getBattlefieldPlayerSet()) {
            if (!PlayerManager.isOnline(memberId)) {
                continue;
            }
            Player player = PlayerManager.getOnlinePlayer(memberId);
            player.send(PtCode.DUEL_SYNC_SERVER, rst);
        }
    }

    /**
     * 同步宝箱领取信息
     */
    public void syncChessRewardSync(int chessRewardId) {
        PbProtocol.DuelSyncRst.Builder rst = PbProtocol.DuelSyncRst.newBuilder();
        PbLeagueDuel.DuelSyncData duelSyncData = genChessSyncData(chessRewardId);
        rst.addSyncData(duelSyncData);
        syncAllBattlefieldPlayer(rst.build());
    }

    public PbLeagueDuel.DuelSyncData genChessSyncData(int chessRewardId) {
        PbLeagueDuel.DuelSyncData.Builder syncBuilder = PbLeagueDuel.DuelSyncData.newBuilder();
        PbLeagueDuel.ChessRewardSyncData duelCityPointInfoPb = PbLeagueDuel.ChessRewardSyncData.newBuilder()
                .setChessRewardId(chessRewardId).build();
        syncBuilder.setType(PbLeagueDuel.DuelSyncData.Type.CHESS_CHANGE);
        syncBuilder.setChessSyncInfo(duelCityPointInfoPb);
        return syncBuilder.build();
    }

    /**
     * 集火状态同步
     */
    public void syncFireSync(int fireCityId, int firePointId) {
        PbProtocol.DuelSyncRst.Builder rst = PbProtocol.DuelSyncRst.newBuilder();
        PbLeagueDuel.DuelSyncData duelSyncData = genFireSyncPb(fireCityId, firePointId);
        rst.addSyncData(duelSyncData);
        syncAllBattlefieldPlayer(rst.build());
    }

    public PbLeagueDuel.DuelSyncData genFireSyncPb(int fireCityId, int firePointId) {
        PbLeagueDuel.DuelSyncData.Builder syncBuilder = PbLeagueDuel.DuelSyncData.newBuilder();
        syncBuilder.setType(PbLeagueDuel.DuelSyncData.Type.FIRE_SYNC)
                .setFireCityId(fireCityId)
                .setFirePointId(firePointId);
        return syncBuilder.build();
    }

    /**
     * 同步据点信息
     */
    public void syncPointSync(boolean isMyChange, DuelCityPointInfo duelCityPointInfo) {
        PbProtocol.DuelSyncRst.Builder rst = PbProtocol.DuelSyncRst.newBuilder();
        PbLeagueDuel.DuelSyncData duelSyncData = genPointSyncPb(isMyChange, duelCityPointInfo);
        rst.addSyncData(duelSyncData);
        syncAllBattlefieldPlayer(rst.build());
    }

    public PbLeagueDuel.DuelSyncData genPointSyncPb(boolean isMyChange, DuelCityPointInfo duelCityPointInfo) {
        int cityId = duelCityPointInfo.getCityId();
        int pointId = duelCityPointInfo.getPointId();
        PbLeagueDuel.DuelSyncData.Builder syncBuilder = PbLeagueDuel.DuelSyncData.newBuilder();
        PbLeagueDuel.LeagueDuelCityPointInfo duelCityPointInfoPb = duelCityPointInfo.
                genPb(isFighting(cityId, pointId));
        syncBuilder.setType(PbLeagueDuel.DuelSyncData.Type.POINT_INFO);
        PbLeagueDuel.PointInfoSyncData.Builder pointInfoSync = PbLeagueDuel.PointInfoSyncData.newBuilder()
                .setPointSyncInfo(duelCityPointInfoPb)
                .setIsMyChange(isMyChange);
        syncBuilder.setPointSyncInfo(pointInfoSync);
        return syncBuilder.build();
    }

    public PbLeagueDuel.DuelSyncData genCitySyncPb(boolean isMyChange, int cityId) {
        PbLeagueDuel.DuelSyncData.Builder syncBuilder = PbLeagueDuel.DuelSyncData.newBuilder();
        syncBuilder.setType(PbLeagueDuel.DuelSyncData.Type.CITY_INFO);
        PbLeagueDuel.CityInfoSyncData.Builder cityInfoSyncDataBuilder = PbLeagueDuel.CityInfoSyncData.newBuilder();
        if (isMyChange) {
            cityInfoSyncDataBuilder.setCityInfo(genLeagueDuelCityInfoPb(cityId));
        } else {
            cityInfoSyncDataBuilder.setCityInfo(genTargetLeagueDuelCityInfoPb(cityId));
        }
        syncBuilder.setCitySyncInfo(cityInfoSyncDataBuilder);
        return syncBuilder.build();
    }

    public PbLeagueDuel.DuelSyncData genStarSyncPb(boolean isMyChange, int addStar) {
        PbLeagueDuel.DuelSyncData.Builder syncBuilder = PbLeagueDuel.DuelSyncData.newBuilder();
        syncBuilder.setType(PbLeagueDuel.DuelSyncData.Type.STAR_CHANGE);
        PbLeagueDuel.StarChangeSyncData.Builder starChangeInfo =
                PbLeagueDuel.StarChangeSyncData.newBuilder()
                        .setAddStar(addStar)
                        .setIsMyChange(isMyChange);
        syncBuilder.setStarSyncInfo(starChangeInfo);
        return syncBuilder.build();
    }


    /**
     * 攻击次数是否足够
     *
     * @param playerId 玩家ID
     * @return true足够
     */
    public boolean isNotEnoughAttack(long playerId) {
        Integer hasAtkNum = hasFightNumMap.getOrDefault(playerId, 0);
        int totalAtkNum = LeagueDuelService.getConstant().getAttacksNum() + extraFightNumMap.getOrDefault(playerId, 0);
        return hasAtkNum >= totalAtkNum;
    }

    public int getRemainFightNum(long playerId) {
        Integer hasAtkNum = hasFightNumMap.getOrDefault(playerId, 0);
        int totalAtkNum = LeagueDuelService.getConstant().getAttacksNum() + extraFightNumMap.getOrDefault(playerId, 0);
        return totalAtkNum - hasAtkNum;
    }

    /**
     * 增加总星数
     *
     * @param addStar 增加的星级
     */
    public void addTotalStar(int addStar) {
        totalStar += addStar;
        this.lastAddStarTime = ServerConstants.getCurrentTimeMillis();
    }

    /**
     * 阵眼是否被打破
     *
     * @return true 攻破
     */
    public boolean isEyesBreak(int cityId) {
        int eyesId = LeagueDuelService.getCityEyesId(cityId);
        LeagueDuelPointTemplate leagueDuelPointTemplate = LeagueDuelService.getLeagueDuelPointTemplate(cityId, eyesId);
        if (Objects.isNull(leagueDuelPointTemplate)) {
            return false;
        }
        DuelCityPointInfo duelCityPointInfo = pointInfoMap.get(cityId).get(eyesId);
        return duelCityPointInfo.getStar() == leagueDuelPointTemplate.difficultyStar;
    }

    /**
     * 是否攻破目标城市
     *
     * @param cityId 城市ID
     * @return true 攻破
     */
    public boolean isPassCity(int cityId) {
        Map<Integer, LeagueDuelPointTemplate> pointTemplateMap = LeagueDuelService.getDuelPointTemplateMap().get(cityId);
        Map<Integer, DuelCityPointInfo> pointInfos = pointInfoMap.get(cityId);
        for (LeagueDuelPointTemplate pointTemplate : pointTemplateMap.values()) {
            if (pointTemplate.pointType != DuelPointType.Stronghold) {
                continue;
            }
            if (!isBreak(pointInfos.get(pointTemplate.pointId))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 前置城市是否未攻破
     *
     * @param cityId 城市ID
     * @return true 未攻破
     */
    public boolean isNotPassPreCity(int cityId) {
        LeagueDuelCityTemplate leagueDuelCityTemplate = LeagueDuelService.getDuelCityTemplateMap().get(cityId);
        if (Objects.isNull(leagueDuelCityTemplate)) {
            return false;
        }
        List<Integer> preCityIdList = leagueDuelCityTemplate.preCityList;
        if (preCityIdList.isEmpty()) {
            return false;
        }
        for (Integer preCityId : preCityIdList) {
            if (isPassCity(preCityId)) {
                return false;
            }
        }
        return true;
    }


    /**
     * 生成pb数据
     */
    public PbLeagueDuel.MiniLeagueDuel genMiniLeagueDuelPb() {
        League league = getLeague();
        return PbLeagueDuel.MiniLeagueDuel.newBuilder()
                .setLeagueId(league.getLeagueId())
                .setName(league.getName())
                .setIconName(league.getIconName())
                .setIconFrameId(league.getIconFrameId())
                .setMemberNum(league.getMemberCount())
                .setWinCount(winningStreakNum)
                .setRemainCityNum(remainCityNum)
                .setLeaderName(league.getLeaderName())
                .setTotalPower(getLeague().getLeagueFightPower())
                .setStar(getTotalStar())
                .setServerId(Player.getServerId(league.getLeagueId()))
                .build();
    }

    /**
     * 生成pb数据
     */
    public List<PbLeagueDuel.LeagueDuelCityInfo> genLeagueDuelCityInfoListPb() {
        List<PbLeagueDuel.LeagueDuelCityInfo> list = new ArrayList<>();
        for (Integer cityId : pointInfoMap.keySet()) {
            list.add(genLeagueDuelCityInfoPb(cityId));
        }
        return list;
    }

    /**
     * 生成pb数据
     */
    public PbLeagueDuel.LeagueDuelCityInfo genLeagueDuelCityInfoPb(int cityId) {
        Map<Integer, Map<Integer, LeagueDuelPointTemplate>> duelPointTemplateMap = LeagueDuelService.getDuelPointTemplateMap();
        Map<Integer, LeagueDuelPointTemplate> pointTemplateMap = duelPointTemplateMap.get(cityId);
        PbLeagueDuel.LeagueDuelCityInfo.Builder builder = PbLeagueDuel.LeagueDuelCityInfo.newBuilder()
                .setCityId(cityId);
        Map<Integer, DuelCityPointInfo> cityPointInfoMap = pointInfoMap.get(cityId);
        int remainPointNum = 0;
        for (Integer pointId : cityPointInfoMap.keySet()) {
            LeagueDuelPointTemplate leagueDuelPointTemplate = pointTemplateMap.get(pointId);
            //只过滤据点
            if (leagueDuelPointTemplate.pointType != DuelPointType.Stronghold) {
                continue;
            }
            DuelCityPointInfo cityPointInfo = cityPointInfoMap.get(pointId);
            if (!isBreak(cityPointInfo)) {
                remainPointNum++;
            }
        }
        builder.setRemainPointNum(remainPointNum);
        return builder.build();
    }

    /**
     * 生成敌方城市Pb数据
     */
    public PbLeagueDuel.LeagueDuelCityInfo genTargetLeagueDuelCityInfoPb(int cityId) {
        Map<Integer, Map<Integer, LeagueDuelPointTemplate>> duelPointTemplateMap = LeagueDuelService.getDuelPointTemplateMap();
        Map<Integer, LeagueDuelPointTemplate> pointTemplateMap = duelPointTemplateMap.get(cityId);
        PbLeagueDuel.LeagueDuelCityInfo.Builder builder = PbLeagueDuel.LeagueDuelCityInfo.newBuilder()
                .setCityId(cityId);
        int remainPointNum = LeagueDuelService.getStrongholdNum(cityId);
        Map<Integer, DuelCityPointInfo> pointInfos = pointInfoMap.get(cityId);
        for (DuelCityPointInfo pointInfo : pointInfos.values()) {
            LeagueDuelPointTemplate leagueDuelPointTemplate = pointTemplateMap.get(pointInfo.getPointId());
            //只过滤据点
            if (leagueDuelPointTemplate.pointType != DuelPointType.Stronghold || !isBreak(pointInfo)) {
                continue;
            }
            remainPointNum--;
        }
        builder.setRemainPointNum(remainPointNum);
        return builder.build();
    }

    /**
     * 生成据点pb数据
     */
    public List<PbLeagueDuel.LeagueDuelCityPointInfo> genLeagueDuelCityPointInfoListPb(int cityId) {
        List<PbLeagueDuel.LeagueDuelCityPointInfo> list = new ArrayList<>();
        Map<Integer, DuelCityPointInfo> cityPointInfoMap = pointInfoMap.get(cityId);
        for (Integer pointId : cityPointInfoMap.keySet()) {
            DuelCityPointInfo cityPointInfo = cityPointInfoMap.get(pointId);
            PbLeagueDuel.LeagueDuelCityPointInfo duelCityPointInfo = cityPointInfo.genPb(isFighting(cityId, pointId));
            list.add(duelCityPointInfo);
        }
        return list;
    }

    /**
     * 生成据点布阵信息pb数据
     */
    public PbLeagueDuel.DuelPointLineup genLeagueDuelPointLineupInfoPb(int cityId, int pointId) {
        Map<Integer, DuelCityPointInfo> cityPointInfoMap = pointInfoMap.get(cityId);
        DuelCityPointInfo cityPointInfo = cityPointInfoMap.get(pointId);
        return genLineupPb(cityPointInfo);
    }

    public PbLeagueDuel.DuelPointLineup genLineupPb(DuelCityPointInfo duelCityPointInfo) {
        PbLeagueDuel.DuelPointLineup.Builder builder = PbLeagueDuel.DuelPointLineup.newBuilder()
                .setCityId(duelCityPointInfo.getCityId())
                .setPointId(duelCityPointInfo.getPointId())
                .setIsRobot(duelCityPointInfo.isRobot());
        if (duelCityPointInfo.isRobot()) {
            builder.setRobotInfo(new BaseRobot(duelCityPointInfo.getRobotInfo()).genPb());
        } else {
            List<LineupDb.LineupInfoDb> lineupDbList = duelCityPointInfo.getLineupDbList();
            List<LineupInfoBean> lineupInfoBeanList = LineupInfoBean.readFromDb(lineupDbList);
            PbLineup.LineupStand lineupStand = LineupBean.genLineupStand(LineupType.duelDefence, lineupInfoBeanList);
            builder.setLineupInfo(lineupStand);
            builder.setLineupPower(duelCityPointInfo.getLineupPower());
        }
        return builder.build();

    }

    /**
     * 城池是否在战斗中
     *
     * @param cityId  城池ID
     * @param pointId 据点ID
     * @return true 城池在战斗中
     */
    public boolean isFighting(int cityId, int pointId) {
        Map<Integer, Map<Integer, LeagueDuelPointTemplate>> duelPointTemplateMap = LeagueDuelService.getDuelPointTemplateMap();
        Map<Integer, LeagueDuelPointTemplate> pointTemplateMap = duelPointTemplateMap.get(cityId);
        LeagueDuelPointTemplate leagueDuelPointTemplate = pointTemplateMap.get(pointId);
        return fightingPointUidSet.contains(leagueDuelPointTemplate.id);
    }

    /**
     * 将正在战斗的城池加入战斗中
     *
     * @param cityId  城池ID
     * @param pointId 据点ID
     */
    public void addFighting(int cityId, int pointId) {
        Map<Integer, Map<Integer, LeagueDuelPointTemplate>> duelPointTemplateMap = LeagueDuelService.getDuelPointTemplateMap();
        Map<Integer, LeagueDuelPointTemplate> pointTemplateMap = duelPointTemplateMap.get(cityId);
        LeagueDuelPointTemplate leagueDuelPointTemplate = pointTemplateMap.get(pointId);
        fightingPointUidSet.add(leagueDuelPointTemplate.id);
    }

    /**
     * 移除正在战斗的城池
     *
     * @param cityId  城池ID
     * @param pointId 据点ID
     */
    public void removeFighting(int cityId, int pointId) {
        Map<Integer, Map<Integer, LeagueDuelPointTemplate>> duelPointTemplateMap = LeagueDuelService.getDuelPointTemplateMap();
        Map<Integer, LeagueDuelPointTemplate> pointTemplateMap = duelPointTemplateMap.get(cityId);
        LeagueDuelPointTemplate leagueDuelPointTemplate = pointTemplateMap.get(pointId);
        fightingPointUidSet.remove(leagueDuelPointTemplate.id);
    }

    /**
     * 结算完成
     *
     * @param isWin 是否胜利
     */
    public void overFinish(boolean isWin) {
        this.isWin = isWin;
        if (isWin) {
            this.winningStreakNum++;
            this.winNum++;
        } else {
            this.winningStreakNum = 0;
            this.loseNum++;
        }
    }

    @Override
    protected void loadData(LeagueBlobDb leagueBlobDb) {
        LeagueDuelModelDb duelModelDb = leagueBlobDb.getDuelModelDb();
        if (Objects.nonNull(duelModelDb)) {
            List<DuelCityPointInfo> pointInfoList = duelModelDb.getPointInfoList();
            for (DuelCityPointInfo duelCityPointInfo : pointInfoList) {
                Map<Integer, DuelCityPointInfo> subMap = this.pointInfoMap.computeIfAbsent(duelCityPointInfo.getCityId(), map1 -> new HashMap<>());
                subMap.put(duelCityPointInfo.getPointId(), duelCityPointInfo);
            }
            this.winningStreakNum = duelModelDb.getWinningStreakNum();
            this.isWin = duelModelDb.isWin();
            this.remainCityNum = duelModelDb.getRemainCityNum();
            this.totalStar = duelModelDb.getTotalStar();
            this.lastAddStarTime = duelModelDb.getLastAddStarTime();
            this.lastMatchLeagueId = duelModelDb.getLastMatchLeagueId();
            this.hasFightNumMap = duelModelDb.getHasFightNumMap();
            this.extraFightNumMap = duelModelDb.getExtraFightNumMap();
            this.hasWinNumMap = duelModelDb.getHasWinNumMap();
            this.deBuffNumMap = duelModelDb.getDeBuffNumMap();
            if (Objects.nonNull(duelModelDb.getLastMainInfo())) {
                this.lastMainInfo = duelModelDb.getLastMainInfo();
            }
            this.fireCityId = duelModelDb.getFireCityId();
//            for (Integer pointUid : duelModelDb.getHasPassPointUIdList()) {
//                LeagueDuelPointTemplate leagueDuelPointTemplate = LeagueDuelService.getLeagueDuelPointTemplate(pointUid);
//                this.hasPassPointId.computeIfAbsent(leagueDuelPointTemplate.cityId, map -> new HashSet<>()).add(leagueDuelPointTemplate.id);
//            }
            this.personRewardBeanMap = duelModelDb.getPersonRewardBeanMap();
            this.winNum = duelModelDb.getWinNum();
            this.loseNum = duelModelDb.getLoseNum();
//            this.hasChessRewardIdSet = duelModelDb.getHasChessRewardIdSet();
            Map<Long, PersonRewardIdBean> hadReceiveChessRewards = duelModelDb.getHadReceiveChessRewards();
            hadReceiveChessRewards.forEach((k,v)->this.hadReceiveChessRewards.put(k, v.getRewardIds()));
            Map<Long, PersonRewardIdBean> hadReceiveTimesRewards = duelModelDb.getHadReceiveTimesRewards();
            hadReceiveTimesRewards.forEach((k,v)->this.hadReceiveTimesRewards.put(k, v.getRewardIds()));
            this.firePointId = duelModelDb.getFirePointId();
            this.breakCounts = duelModelDb.getBreakCounts();
            this.defenseCounts = duelModelDb.getDefenseCounts();
        }
    }

    @Override
    protected void saveData(LeagueBlobDb leagueBlobDb) {
        LeagueDuelModelDb duelModelDb = new LeagueDuelModelDb();
        for (Map<Integer, DuelCityPointInfo> cityPointInfoMap : this.pointInfoMap.values()) {
            for (DuelCityPointInfo cityPointInfo : cityPointInfoMap.values()) {
                duelModelDb.getPointInfoList().add(cityPointInfo);
            }
        }
        duelModelDb.setWinningStreakNum(this.winningStreakNum);
        duelModelDb.setWin(this.isWin);
        duelModelDb.setRemainCityNum(this.remainCityNum);
        duelModelDb.setTotalStar(this.totalStar);
        duelModelDb.setLastAddStarTime(this.lastAddStarTime);
        duelModelDb.setLastMatchLeagueId(this.lastMatchLeagueId);
        duelModelDb.setHasFightNumMap(this.hasFightNumMap);
        duelModelDb.setExtraFightNumMap(this.extraFightNumMap);
        duelModelDb.setHasWinNumMap(this.hasWinNumMap);
        duelModelDb.setDeBuffNumMap(this.deBuffNumMap);
        if (Objects.nonNull(this.lastMainInfo)) {
            duelModelDb.setLastMainInfo(this.lastMainInfo);
        }
        duelModelDb.setFireCityId(this.fireCityId);
//        for (Map.Entry<Integer, Set<Integer>> entry : hasPassPointId.entrySet()) {
//            Integer cityId = entry.getKey();
//            for (Integer pointId : entry.getValue()) {
//                LeagueDuelPointTemplate leagueDuelPointTemplate = LeagueDuelService.getLeagueDuelPointTemplate(cityId, pointId);
//                if (Objects.nonNull(leagueDuelPointTemplate)) {
//                    duelModelDb.getHasPassPointUIdList().add(leagueDuelPointTemplate.id);
//                }
//            }
//        }
        duelModelDb.setPersonRewardBeanMap(this.personRewardBeanMap);
        duelModelDb.setWinNum(winNum);
        duelModelDb.setLoseNum(loseNum);
//        duelModelDb.setHasChessRewardIdSet(hasChessRewardIdSet);

        duelModelDb.setFirePointId(firePointId);
        hadReceiveChessRewards.forEach((k, v)->{
            PersonRewardIdBean rewardIdBean = new PersonRewardIdBean();
            rewardIdBean.setRewardIds(v);
            duelModelDb.getHadReceiveChessRewards().put(k, rewardIdBean);
        });
        hadReceiveTimesRewards.forEach((k, v)->{
            PersonRewardIdBean rewardIdBean = new PersonRewardIdBean();
            rewardIdBean.setRewardIds(v);
            duelModelDb.getHadReceiveTimesRewards().put(k, rewardIdBean);
        });

        duelModelDb.setBreakCounts(breakCounts);
        duelModelDb.setDefenseCounts(defenseCounts);
        leagueBlobDb.setDuelModelDb(duelModelDb);
    }

    /**
     * 添加一个据点信息
     */
    public void addDuelCityPointInfo(DuelCityPointInfo cityPointInfo) {
        Map<Integer, DuelCityPointInfo> cityPointInfoMap = pointInfoMap.computeIfAbsent(cityPointInfo.getCityId(), map1 -> new HashMap<>());
        cityPointInfoMap.put(cityPointInfo.getPointId(), cityPointInfo);
    }

    public Map<Integer, Map<Integer, DuelCityPointInfo>> getPointInfoMap() {
        return pointInfoMap;
    }

    public DuelCityPointInfo getDuelCityPointInfo(int cityId, int pointId) {
        return pointInfoMap.get(cityId).get(pointId);
    }

    public void setPointInfoMap(Map<Integer, Map<Integer, DuelCityPointInfo>> pointInfoMap) {
        this.pointInfoMap = pointInfoMap;
    }

    public int getWinningStreakNum() {
        return winningStreakNum;
    }

    public void setWinningStreakNum(int winningStreakNum) {
        this.winningStreakNum = winningStreakNum;
    }

    public int getRemainCityNum() {
        return remainCityNum;
    }

    public void setRemainCityNum(int remainCityNum) {
        this.remainCityNum = remainCityNum;
    }

    public void costRemainCityNum() {
        this.remainCityNum--;
    }

    public long getLastMatchLeagueId() {
        return lastMatchLeagueId;
    }

    public void setLastMatchLeagueId(long lastMatchLeagueId) {
        this.lastMatchLeagueId = lastMatchLeagueId;
    }

    public int getFightNum(long playerId) {
        return hasFightNumMap.getOrDefault(playerId, 0);
    }

    public void addFightNum(long playerId) {
        hasFightNumMap.merge(playerId, 1, Integer::sum);
    }

    public int getHasWinNum(long playerId) {
        return hasWinNumMap.getOrDefault(playerId, 0);
    }

    public void addHasWinNum(long playerId) {
        hasWinNumMap.merge(playerId, 1, Integer::sum);
    }

    /**
     * 获取玩家的剩余的套debuff次数
     */
    public int getDeBuffNum(long playerId) {
        return deBuffNumMap.getOrDefault(playerId, 0);
    }

    public void addDeBuffNum(long playerId) {
        deBuffNumMap.merge(playerId, 1, Integer::sum);
    }

    public void costDeBuffNum(long playerId) {
        deBuffNumMap.merge(playerId, -1, Integer::sum);
    }

    public Map<Long, Integer> getHasFightNumMap() {
        return hasFightNumMap;
    }

    public void setHasFightNumMap(Map<Long, Integer> hasFightNumMap) {
        this.hasFightNumMap = hasFightNumMap;
    }

    public Map<Long, Integer> getHasWinNumMap() {
        return hasWinNumMap;
    }

    public void setHasWinNumMap(Map<Long, Integer> hasWinNumMap) {
        this.hasWinNumMap = hasWinNumMap;
    }

    public Map<Long, Integer> getDeBuffNumMap() {
        return deBuffNumMap;
    }

    public void setDeBuffNumMap(Map<Long, Integer> deBuffNumMap) {
        this.deBuffNumMap = deBuffNumMap;
    }

    public DuelLastMainInfo getLastMainInfo() {
        return lastMainInfo;
    }

    public void setLastMainInfo(DuelLastMainInfo lastMainInfo) {
        this.lastMainInfo = lastMainInfo;
    }

    public boolean isWin() {
        return isWin;
    }

    public Set<Long> getBattlefieldPlayerSet() {
        return battlefieldPlayerSet;
    }

    public void setWin(boolean win) {
        isWin = win;
    }

    public int getTotalStar() {
        return totalStar;
    }

    public void setTotalStar(int totalStar) {
        this.totalStar = totalStar;
    }

    public void setBattlefieldPlayerSet(Set<Long> battlefieldPlayerSet) {
        this.battlefieldPlayerSet = battlefieldPlayerSet;
    }

    public int getFireCityId() {
        return fireCityId;
    }

    public void setFireCityId(int fireCityId) {
        this.fireCityId = fireCityId;
    }

    public Set<Integer> getFightingPointUidSet() {
        return fightingPointUidSet;
    }

    public void setFightingPointUidSet(Set<Integer> fightingPointUidSet) {
        this.fightingPointUidSet = fightingPointUidSet;
    }

    public int getCityDefenceBuffLv(int cityId) {
        int cityDefenceId = LeagueDuelService.getCityDefenceId(cityId);
        DuelCityPointInfo duelCityPointInfo = getDuelCityPointInfo(cityId, cityDefenceId);
        return duelCityPointInfo.getCityDefenceBuffLv();
    }

    public Map<Long, Integer> getExtraFightNumMap() {
        return extraFightNumMap;
    }

    public void setExtraFightNumMap(Map<Long, Integer> extraFightNumMap) {
        this.extraFightNumMap = extraFightNumMap;
    }

    public int getExtraFightNum(long playerId) {
        return extraFightNumMap.getOrDefault(playerId, 0);
    }

    public void addExtraFightNum(long playerId, int addNum) {
        extraFightNumMap.merge(playerId, addNum, Integer::sum);
    }

    public Map<Long, PersonRewardBean> getPersonRewardBeanMap() {
        return personRewardBeanMap;
    }

    public void setPersonRewardBeanMap(Map<Long, PersonRewardBean> personRewardBeanMap) {
        this.personRewardBeanMap = personRewardBeanMap;
    }

    public long getLastAddStarTime() {
        return lastAddStarTime;
    }

    public void setLastAddStarTime(long lastAddStarTime) {
        this.lastAddStarTime = lastAddStarTime;
    }

    public int getWinNum() {
        return winNum;
    }

    public void setWinNum(int winNum) {
        this.winNum = winNum;
    }

    public int getLoseNum() {
        return loseNum;
    }

    public void setLoseNum(int loseNum) {
        this.loseNum = loseNum;
    }

    public Map<Long, Set<Integer>> getHadReceiveTimesRewards() {
        return hadReceiveTimesRewards;
    }

    public void setHadReceiveTimesRewards(Map<Long, Set<Integer>> hadReceiveTimesRewards) {
        this.hadReceiveTimesRewards = hadReceiveTimesRewards;
    }

    public Map<Long, Set<Integer>> getHadReceiveChessRewards() {
        return hadReceiveChessRewards;
    }

    public void setHadReceiveChessRewards(Map<Long, Set<Integer>> hadReceiveChessRewards) {
        this.hadReceiveChessRewards = hadReceiveChessRewards;
    }

    public int getFirePointId() {
        return firePointId;
    }

    public void setFirePointId(int firePointId) {
        this.firePointId = firePointId;
    }

    /**
     * 更新据点信息
     */
    public void updatePointInfo(DuelCityPointInfo duelCityPointInfo) {
        Map<Integer, DuelCityPointInfo> subMap = this.pointInfoMap.computeIfAbsent(duelCityPointInfo.getCityId(), map1 -> new HashMap<>());
        subMap.put(duelCityPointInfo.getPointId(), duelCityPointInfo);
    }

    /**
     * 活动结束清理数据
     */
    public void endAndClear() {
        this.pointInfoMap.clear();
        this.remainCityNum = 0;
        this.hasFightNumMap.clear();
        this.hasWinNumMap.clear();
        this.deBuffNumMap.clear();
        this.isWin = false;
        this.totalStar = 0;
        this.battlefieldPlayerSet.clear();
        this.fightingPointUidSet.clear();
        this.extraFightNumMap.clear();
        this.personRewardBeanMap.clear();
        this.lastAddStarTime = 0;
        this.breakCounts.clear();
        this.defenseCounts.clear();
        this.fireCityId = 0;
        this.firePointId = 0;
        this.hadReceiveTimesRewards.clear();
        this.hadReceiveChessRewards.clear();
    }

    public PbRecord.LeagueDuelHistoryRecordInfo genPbLeagueDuelHistoryRecordInfo() {
        League league = getLeague();
        return PbRecord.LeagueDuelHistoryRecordInfo.newBuilder()
                .setId(league.getLeagueId())
                .setName(league.getName())
                .setIconName(league.getIconName())
                .setIconFrameId(league.getIconFrameId())
                .setTotalPower(league.getLeagueFightPower())
                .setTotalStar(totalStar).build();
    }

    public Map<Long, Integer> getBreakCounts() {
        return breakCounts;
    }

    public void addBreakCount(long playerId){
        breakCounts.put(playerId, breakCounts.getOrDefault(playerId, 0) + 1);
    }

    public void setBreakCounts(Map<Long, Integer> breakCounts) {
        this.breakCounts = breakCounts;
    }

    public Map<Long, Integer> getDefenseCounts() {
        return defenseCounts;
    }

    public void addDefenseCount(long playerId){
        defenseCounts.put(playerId, defenseCounts.getOrDefault(playerId, 0) + 1);
    }

    public void setDefenseCounts(Map<Long, Integer> defenseCounts) {
        this.defenseCounts = defenseCounts;
    }

    @Override
    public boolean canMerge(long leagueId) {
        return true;//globalData 会拦截
    }

    @Override
    public void merge(League selfLeague, League targetLeague) {

    }

    @Override
    public void removeLeague(League league) {

    }

    @Override
    public void quitLeague(long pid) {

    }
}

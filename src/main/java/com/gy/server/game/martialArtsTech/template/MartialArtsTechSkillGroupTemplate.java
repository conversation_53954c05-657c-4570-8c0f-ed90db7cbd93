package com.gy.server.game.martialArtsTech.template;

import com.gy.server.game.liberty.effect.LibertyTemplate;
import com.ttlike.server.tl.baselib.serialize.combatAddition.CombatAdditions;

import java.util.ArrayList;
import java.util.List;

/**
 * 心法技能模板类
 *
 * <AUTHOR> - [Created on 2023/4/14 18:22]
 */
public class MartialArtsTechSkillGroupTemplate {

    public int id;
    /**
     * 心法效果组ID
     */
    public int skillGroupId;
    /**
     * 等级
     */
    public int lv;
    /**
     * 心法类型
     * 1 战斗类
     * 2 功能类
     * 3 权益类
     */
    public int type;
    /**
     * 参数值
     * 说明：如果type类型是1，则后面属性条读取的是combatAddition。
     * 如果type类型是2或3，则读取的是权益表的id，逗号后面的值是权益表id生效具体加成。如201,1表示201权益加成“1”。多条属性用“|”拼接。
     */
    public String param;
    /**
     * type=1时读取这个
     */
    public CombatAdditions combatAdditions;
    /**
     * type=2/3时读取这个 权益值
     */
    public List<LibertyTemplate> libertyListIdList = new ArrayList<>();

}

package com.gy.server.game.martialArtsTech;

import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.function.Function;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.liberty.effect.LibertyTemplate;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.martialArtsTech.template.MartialArtsTechSkillGroupTemplate;
import com.gy.server.game.martialArtsTech.template.MartialArtsTechStarUpTemplate;
import com.gy.server.game.martialArtsTech.template.MartialArtsTechTemplate;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.service.Service;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.ttlike.server.tl.baselib.serialize.combatAddition.CombatAdditions;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 心法服务类
 *
 * <AUTHOR> - [Created on 2023/4/14 18:19]
 */
public class MartialArtsTechService extends PlayerPacketHandler implements Service {

    private static Map<Integer, MartialArtsTechTemplate> martialArtsTechTemplateMap = new HashMap<>();
    /**
     * 心法效果组
     * Key1=组ID key2=等级
     */
    private static Map<Integer, Map<Integer, MartialArtsTechSkillGroupTemplate>> skillGroupTemplateMap = new HashMap<>();
    /**
     * 升星组表
     * Key1=组ID key2=等级
     */
    private static Map<Integer, Map<Integer, MartialArtsTechStarUpTemplate>> starUpTemplateMap = new HashMap<>();


    /**
     * 心法升星
     */
    @Handler(PtCode.MARTIAL_ARTS_TECH_STAR_CLIENT)
    private void techStar(Player player, PbProtocol.MartialArtsTechStarReq req, long time) {
        PbProtocol.MartialArtsTechStarRst.Builder rst = PbProtocol.MartialArtsTechStarRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:
        {
            int id = req.getId();
            if (!martialArtsTechTemplateMap.containsKey(id)) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            if (Function.martialArtsTech.isNotOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            PlayerMartialArtsTechModel artsTechModel = player.getModel(PlayerModelEnums.martialArtsTech);
            Map<Integer, Integer> martialArtsTechMap = artsTechModel.getMartialArtsTechMap();
            Integer nowStar = martialArtsTechMap.getOrDefault(id, 0);
            MartialArtsTechTemplate martialArtsTechTemplate = martialArtsTechTemplateMap.get(id);
            Map<Integer, MartialArtsTechStarUpTemplate> techStarUpTemplateMap = starUpTemplateMap.get(martialArtsTechTemplate.starGroupId);
            Integer maxStar = Collections.max(techStarUpTemplateMap.keySet());
            if (nowStar >= maxStar) {
                rst.setResult(Text.genServerRstInfo(Text.心法已达最高级));
                break logic;
            }
            int nextStar = nowStar + 1;
            MartialArtsTechStarUpTemplate starUpTemplate = techStarUpTemplateMap.get(nextStar);
            int itemCount = player.getBagModel().getItemCount(martialArtsTechTemplate.normalItemId);
            if (itemCount < starUpTemplate.cost) {
                rst.setResult(Text.genServerRstInfo(Text.心法升星消耗不足));
                break logic;
            }
            player.getBagModel().removeItem(martialArtsTechTemplate.normalItemId, starUpTemplate.cost, BehaviorType.martialArtsTechStar);
            martialArtsTechMap.put(id, nextStar);
            player.postEvent(PlayerEventType.martialArtsTechStar, id, nextStar);
            rst.setId(id);
            rst.setStar(nextStar);
        }
        player.send(PtCode.MARTIAL_ARTS_TECH_STAR_SERVER, rst.build(), time);
    }


    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.martialArtsTech_martialArtsTech);
        Map<Integer, MartialArtsTechTemplate> martialArtsTechTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            MartialArtsTechTemplate martialArtsTechTemplate = new MartialArtsTechTemplate();
            martialArtsTechTemplate.id = Integer.parseInt(map.get("id"));
            martialArtsTechTemplate.quality = Integer.parseInt(map.get("quality"));
            martialArtsTechTemplate.normalItemId = Integer.parseInt(map.get("normalItemId"));
            martialArtsTechTemplate.skillGroupId = Integer.parseInt(map.get("skillGroupId"));
            martialArtsTechTemplate.starGroupId = Integer.parseInt(map.get("starGroupId"));
            martialArtsTechTemplateMapTemp.put(martialArtsTechTemplate.id, martialArtsTechTemplate);
        }
        martialArtsTechTemplateMap = martialArtsTechTemplateMapTemp;

        mapList = ConfigReader.read(ConfigFile.martialArtsTech_techSkillGroup);
        Map<Integer, Map<Integer, MartialArtsTechSkillGroupTemplate>> skillGroupTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            MartialArtsTechSkillGroupTemplate skillGroupTemplate = new MartialArtsTechSkillGroupTemplate();
            skillGroupTemplate.id = Integer.parseInt(map.get("id"));
            skillGroupTemplate.skillGroupId = Integer.parseInt(map.get("skillGroupId"));
            skillGroupTemplate.lv = Integer.parseInt(map.get("lv"));
            skillGroupTemplate.type = Integer.parseInt(map.get("type"));
            skillGroupTemplate.param = map.get("param");
            if (skillGroupTemplate.type == 1) {
                skillGroupTemplate.combatAdditions = CombatAdditions.readFromStr(skillGroupTemplate.param);
            } else {
                skillGroupTemplate.libertyListIdList = LibertyTemplate.readLibertyListFromText(skillGroupTemplate.param);
            }
            Map<Integer, MartialArtsTechSkillGroupTemplate> templateMap = skillGroupTemplateMapTemp.computeIfAbsent(skillGroupTemplate.skillGroupId, map1 -> new HashMap<>());
            templateMap.put(skillGroupTemplate.lv, skillGroupTemplate);
        }
        skillGroupTemplateMap = skillGroupTemplateMapTemp;

        mapList = ConfigReader.read(ConfigFile.martialArtsTech_techStarUp);
        Map<Integer, Map<Integer, MartialArtsTechStarUpTemplate>> starUpTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            MartialArtsTechStarUpTemplate starUpTemplate = new MartialArtsTechStarUpTemplate();
            starUpTemplate.id = Integer.parseInt(map.get("id"));
            starUpTemplate.starGroupId = Integer.parseInt(map.get("starGroupId"));
            starUpTemplate.starLv = Integer.parseInt(map.get("starLv"));
            starUpTemplate.cost = Integer.parseInt(map.get("cost"));
            starUpTemplate.skillLv = Integer.parseInt(map.get("skillLv"));
            Map<Integer, MartialArtsTechStarUpTemplate> templateMap = starUpTemplateMapTemp.computeIfAbsent(starUpTemplate.starGroupId, map1 -> new HashMap<>());
            templateMap.put(starUpTemplate.starLv, starUpTemplate);
        }
        starUpTemplateMap = starUpTemplateMapTemp;
    }

    @Override
    public void clearConfigData() {
        martialArtsTechTemplateMap.clear();
        skillGroupTemplateMap.clear();
        starUpTemplateMap.clear();
    }

    public static Map<Integer, MartialArtsTechTemplate> getMartialArtsTechTemplateMap() {
        return martialArtsTechTemplateMap;
    }

    public static Map<Integer, Map<Integer, MartialArtsTechSkillGroupTemplate>> getSkillGroupTemplateMap() {
        return skillGroupTemplateMap;
    }

    /**
     * 获取心法效果表
     *
     * @param groupId 组ID
     * @param star    星级
     */
    public static MartialArtsTechSkillGroupTemplate getTechSkillGroupTemplate(int groupId, int star) {
        return skillGroupTemplateMap.get(groupId).get(star);
    }

    /**
     * 通过心法ID获取心法效果表
     *
     * @param id   心法ID
     * @param star 星级
     */
    public static MartialArtsTechSkillGroupTemplate getTechSkillGroupTemplateById(int id, int star) {
        MartialArtsTechTemplate martialArtsTechTemplate = martialArtsTechTemplateMap.get(id);
        return skillGroupTemplateMap.get(martialArtsTechTemplate.skillGroupId).get(star);
    }

    public static Map<Integer, Map<Integer, MartialArtsTechStarUpTemplate>> getStarUpTemplateMap() {
        return starUpTemplateMap;
    }
}

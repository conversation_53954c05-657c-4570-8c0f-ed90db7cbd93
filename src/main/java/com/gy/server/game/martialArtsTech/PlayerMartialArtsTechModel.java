package com.gy.server.game.martialArtsTech;

import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModel;
import com.ttlike.server.tl.baselib.serialize.player.MartialArtsTechModelDb;
import com.ttlike.server.tl.baselib.serialize.player.PlayerBlobDb;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 玩家心法模块
 *
 * <AUTHOR> - [Created on 2023/4/17 10:49]
 */
public class PlayerMartialArtsTechModel extends PlayerModel {

    /**
     * Key=心法ID  Value=心法星级
     */
    private Map<Integer, Integer> martialArtsTechMap = new HashMap<>();

    public PlayerMartialArtsTechModel(Player player) {
        super(player);
    }

    @Override
    protected void loadData(PlayerBlobDb playerBlob) {
        MartialArtsTechModelDb modelDb = playerBlob.getMartialArtsTechModelDb();
        if (Objects.nonNull(modelDb)) {
            martialArtsTechMap = modelDb.getMartialArtsTechMap();
        }
    }

    @Override
    protected void saveData(PlayerBlobDb playerBlob) {
        MartialArtsTechModelDb modelDb = new MartialArtsTechModelDb();
        modelDb.setMartialArtsTechMap(martialArtsTechMap);
        playerBlob.setMartialArtsTechModelDb(modelDb);
    }

    public Map<Integer, Integer> getMartialArtsTechMap() {
        return martialArtsTechMap;
    }
}

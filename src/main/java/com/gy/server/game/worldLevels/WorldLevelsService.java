package com.gy.server.game.worldLevels;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.service.Service;

/**
 * <AUTHOR> - [Created on 2022/11/3 16:38]
 */
public class WorldLevelsService implements Service {

    /**
     * Key:开服天数 Value:世界等级
     */
    private static Map<Integer, Integer> worldLevelsMap = new HashMap<>();

    public static Map<Integer, Integer> getWorldLevelsMap() {
        return worldLevelsMap;
    }

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        // 世界等级表
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.worldLevels_worldLevels);
        Map<Integer, Integer> worldLevelsMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            worldLevelsMapTemp.put(Integer.parseInt(map.get("openServerDay")), Integer.parseInt(map.get("worldLevels")));
        }
        worldLevelsMap = worldLevelsMapTemp;
    }

    @Override
    public void clearConfigData() {
        worldLevelsMap.clear();
    }

    @Override
    public boolean isPreSystemStartup() {
        return true;
    }
}

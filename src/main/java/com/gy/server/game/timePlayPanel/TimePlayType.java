package com.gy.server.game.timePlayPanel;

import com.gy.server.game.player.Player;
import com.gy.server.packet.PbProtocol;

/**
 * 限时玩法
 *
 * <AUTHOR> - [Created on 2023/5/17 17:13]
 */
public enum TimePlayType {
    /**
     * 珍珑棋局
     */
    chess,
    /**
     * 帮派宴会
     */
    leagueBanquet,
    ;


    /**
     * 构建返回值
     */
    public void buildRst(PbProtocol.TimePlayInfoRst.Builder rst, Player player) {

    }

    /**
     * 活动现在是否开启
     *
     * @return true 开启
     */
    public boolean isOpenNow(Player player) {
        TimePlayTemplate timePlayTemplate = TimePlayService.getTimePlayTemplateByTypeMap().get(this);
        return timePlayTemplate.isOpenNow(player);
    }

    /**
     * 活动今天是否开启 不校验具体时间
     *
     * @return true 开启
     */
    public boolean isOpenDay(Player player) {
        TimePlayTemplate timePlayTemplate = TimePlayService.getTimePlayTemplateByTypeMap().get(this);
        return timePlayTemplate.isOpenDay(player);
    }

    /**
     * 活动现在是否开启
     *
     * @return true 开启
     */
    public boolean isOpenNow() {
        return isOpenNow(null);
    }

}

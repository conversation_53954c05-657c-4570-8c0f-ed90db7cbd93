package com.gy.server.game.timePlayPanel;

import java.util.HashMap;
import java.util.Map;

/**
 * 限时玩法开启时间类型
 *
 * <AUTHOR> - [Created on 2023/5/17 17:20]
 */
public enum TimePlayTimeType {
    /**
     * 每天开启
     */
    permanent(0),
    /**
     * 每天固定时间开启
     */
    everyDay(1),
    /**
     * 开服第N天开启
     */
    openServerDay(2),
    /**
     * 固定时间开启
     */
    fixTime(3),
    /**
     * 每周周几固定开启
     */
    week(4),
    /**
     * 每月几号开启
     */
    month(5),

    ;

    private final int type;

    TimePlayTimeType(int type) {
        this.type = type;
    }

    private static final Map<Integer, TimePlayTimeType> map = new HashMap<>();

    static {
        for (TimePlayTimeType playTimeType : values()) {
            map.put(playTimeType.type, playTimeType);
        }
    }

    public static TimePlayTimeType of(int type) {
        return map.get(type);
    }
}

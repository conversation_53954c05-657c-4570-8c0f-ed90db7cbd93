package com.gy.server.game.timePlayPanel;

import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.game.service.Service;
import com.gy.server.game.text.Text;
import com.gy.server.game.util.StringExtUtil;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.time.DateTimeFormatterType;
import com.gy.server.utils.time.DateTimeUtil;
import org.apache.commons.lang3.tuple.Pair;

import java.time.DayOfWeek;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 限时玩法服务类
 *
 * <AUTHOR> - [Created on 2023/3/22 14:35]
 */
public class TimePlayService extends PlayerPacketHandler implements Service {

    /**
     * 限时玩法
     */
    private static Map<Integer, TimePlayTemplate> timePlayTemplateMap = new HashMap<>();
    /**
     * 限时玩法通过type
     */
    private static Map<TimePlayType, TimePlayTemplate> timePlayTemplateByTypeMap = new HashMap<>();
    /**
     * 限时玩法通过stage
     */
    private static Map<Integer, TimePlayTemplate> timePlayTemplateByStageMap = new HashMap<>();


    @Handler(PtCode.TIME_PLAY_INFO_CLIENT)
    private void timePlayInfo(Player player, PbProtocol.TimePlayInfoReq req, long time) {
        PbProtocol.TimePlayInfoRst.Builder rst = PbProtocol.TimePlayInfoRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:
        {
            int id = req.getId();
            if (!timePlayTemplateMap.containsKey(id)) {
                rst.setResult(Text.genServerRstInfo(Text.限时玩法ID不存在));
                break logic;
            }
            TimePlayTemplate timePlayTemplate = timePlayTemplateMap.get(id);
            PlayerTimePlayModel playerTimePlayModel = player.getModel(PlayerModelEnums.timePlay);
            rst.setTodayPlayNum(playerTimePlayModel.getTodayNum(id));
            timePlayTemplate.type.buildRst(rst, player);
            rst.setId(id);
        }
        player.send(PtCode.TIME_PLAY_INFO_SERVER, rst.build(), time);
    }

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.timePlayPanel_timePlayPanel);
        Map<Integer, TimePlayTemplate> timePlayTemplateMapTemp = new HashMap<>();
        Map<TimePlayType, TimePlayTemplate> timePlayTemplateByTypeMapTemp = new HashMap<>();
        Map<Integer, TimePlayTemplate> timePlayTemplateByStageMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            TimePlayTemplate timePlayTemplate = new TimePlayTemplate();
            timePlayTemplate.id = Integer.parseInt(map.get("id"));
            timePlayTemplate.type = TimePlayType.valueOf(map.get("type"));
            timePlayTemplate.condition = map.get("condition");
            timePlayTemplate.timeType = TimePlayTimeType.of(Integer.parseInt(map.get("timeType")));
            if (timePlayTemplate.timeType == TimePlayTimeType.openServerDay) {
                timePlayTemplate.openDay = Integer.parseInt(map.get("timePara1"));
            } else if (timePlayTemplate.timeType == TimePlayTimeType.everyDay) {
                String[] timeTypes = map.get("timePara1").split("-");
                DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("HH:mm");
                timePlayTemplate.startDayTime = LocalTime.parse(timeTypes[0], dateTimeFormatter);
                timePlayTemplate.endDayTime = LocalTime.parse(timeTypes[1], dateTimeFormatter);
            } else if (timePlayTemplate.timeType == TimePlayTimeType.fixTime) {
                timePlayTemplate.startTime = DateTimeUtil.parseString(map.get("startTime"), DateTimeFormatterType.date_time);
                timePlayTemplate.endTime = DateTimeUtil.parseString(map.get("endTime"), DateTimeFormatterType.date_time);
            } else if (timePlayTemplate.timeType == TimePlayTimeType.week) {
                String[] timePara1s = map.get("timePara1").split(";");
                for (String timePara1 : timePara1s) {
                    timePlayTemplate.dayOfWeekList.add(DayOfWeek.of(Integer.parseInt(timePara1)));
                }
                DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("HH:mm");
                timePlayTemplate.startDayTime = LocalTime.parse(map.get("startTime"), dateTimeFormatter);
                timePlayTemplate.endDayTime = LocalTime.parse(map.get("endTime"), dateTimeFormatter);
            } else if (timePlayTemplate.timeType == TimePlayTimeType.month) {
                timePlayTemplate.dayOfMonthList = StringExtUtil.string2List(map.get("timePara1"), ";", Integer.class);
                DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("HH:mm");
                timePlayTemplate.startDayTime = LocalTime.parse(map.get("startTime"), dateTimeFormatter);
                timePlayTemplate.endDayTime = LocalTime.parse(map.get("endTime"), dateTimeFormatter);
            }
            String closeSeverStr = map.get("closeSever");
            if (!closeSeverStr.equals("-1")) {
                for (String closeSever : closeSeverStr.split(";")) {
                    if (closeSever.contains("-")) {
                        String[] serverRangeArr = closeSever.split("-");
                        timePlayTemplate.closeSeverList.add(Pair.of(Integer.parseInt(serverRangeArr[0]), Integer.parseInt(serverRangeArr[1])));
                    } else {
                        timePlayTemplate.closeSeverList.add(Pair.of(Integer.parseInt(closeSever), Integer.parseInt(closeSever)));
                    }
                }
            }

            timePlayTemplate.isOpen = Integer.parseInt(map.get("onoff")) == 1;
            timePlayTemplate.stageTypeId = Integer.parseInt(map.get("stageType"));
            timePlayTemplate.resetType = Integer.parseInt(map.get("resetType"));
            timePlayTemplate.resetPara1 = Integer.parseInt(map.get("resetPara1"));
            timePlayTemplate.numberMax = Integer.parseInt(map.get("numberMax"));
            timePlayTemplateMapTemp.put(timePlayTemplate.id, timePlayTemplate);
            timePlayTemplateByTypeMapTemp.put(timePlayTemplate.type, timePlayTemplate);
            if (timePlayTemplate.stageTypeId != -1) {
                timePlayTemplateByStageMapTemp.put(timePlayTemplate.stageTypeId, timePlayTemplate);
            }
        }
        timePlayTemplateMap = timePlayTemplateMapTemp;
        timePlayTemplateByTypeMap = timePlayTemplateByTypeMapTemp;
        timePlayTemplateByStageMap = timePlayTemplateByStageMapTemp;
    }

    @Override
    public void clearConfigData() {
        timePlayTemplateMap.clear();
        timePlayTemplateByTypeMap.clear();
        timePlayTemplateByStageMap.clear();
    }

    public static Map<Integer, TimePlayTemplate> getTimePlayTemplateMap() {
        return timePlayTemplateMap;
    }

    public static Map<Integer, TimePlayTemplate> getTimePlayTemplateByStageMap() {
        return timePlayTemplateByStageMap;
    }

    public static Map<TimePlayType, TimePlayTemplate> getTimePlayTemplateByTypeMap() {
        return timePlayTemplateByTypeMap;
    }
}

package com.gy.server.game.timePlayPanel;

import com.gy.server.core.Configuration;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.cond.CondManager;
import com.gy.server.game.player.Player;
import com.gy.server.game.world.World;
import com.gy.server.utils.time.DateTimeDurationType;
import com.gy.server.utils.time.DateTimeUtil;
import org.apache.commons.lang3.tuple.Pair;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 限时玩法模板类
 *
 * <AUTHOR> - [Created on 2023/3/22 14:39]
 */
public class TimePlayTemplate {

    public int id;

    /**
     * 绑定玩法类型
     */
    public TimePlayType type;
    /**
     * 解锁条件
     */
    public String condition;
    /**
     * 开启时间类型
     */
    public TimePlayTimeType timeType;
    /**
     * 开服第几天开启
     */
    public int openDay;
    /**
     * 每天固定开启时间
     */
    public LocalTime startDayTime;
    /**
     * 每天固定结束时间
     */
    public LocalTime endDayTime;
    /**
     * 固定开启开始时间
     */
    public LocalDateTime startTime;
    /**
     * 固定开启结束时间
     */
    public LocalDateTime endTime;
    /**
     * 每周周几开启
     */
    public List<DayOfWeek> dayOfWeekList = new ArrayList<>();
    /**
     * 每月第几天开启
     */
    public List<Integer> dayOfMonthList = new ArrayList<>();
    /**
     * 不开启的服务器Id
     * Pair 服务器ID 区间
     */
    public List<Pair<Integer, Integer>> closeSeverList = new ArrayList<>();
    /**
     * 功能开关
     */
    public boolean isOpen;

    /**
     * 关联战斗类型
     */
    public int stageTypeId;
    /**
     * 次数重置类型
     */
    public int resetType;
    /**
     * 重置参数1
     */
    public int resetPara1;
    /**
     * 次数上限
     */
    public int numberMax;


    /**
     * 判断该活动现在是否开启
     */
    public boolean isOpenNow(Player player) {
        // 活动关闭
        if (!isOpen) {
            return false;
        }
        // 判断服务器是否开启该活动
        for (Pair<Integer, Integer> pair : closeSeverList) {
            if (pair.getLeft() <= Configuration.serverId && Configuration.serverId <= pair.getRight()) {
                return false;
            }
        }
        if (Objects.nonNull(player) && CondManager.checkNotCond(player, condition)) {
            return false;
        }
        // 永久开启
        if (timeType == TimePlayTimeType.permanent) {
            return true;
        }
        // 开服多少天开启
        if (timeType == TimePlayTimeType.openServerDay) {
            return World.getOpenServerDay() >= openDay;
        }
        LocalDateTime now = ServerConstants.getCurrentTimeLocalDateTime();

        // 每天/周/月固定时间开启
        if (timeType == TimePlayTimeType.week || timeType == TimePlayTimeType.everyDay || timeType == TimePlayTimeType.month) {
            if (timeType == TimePlayTimeType.week) {
                DayOfWeek dayOfWeek = now.getDayOfWeek();
                if (!dayOfWeekList.contains(dayOfWeek)) {
                    return false;
                }
            }
            if (timeType == TimePlayTimeType.month) {
                int dayOfMonth = now.getDayOfMonth();
                if (!dayOfMonthList.contains(dayOfMonth)) {
                    return false;
                }
            }
            LocalTime localTime = now.toLocalTime();
            return (localTime.isAfter(startDayTime) && localTime.isBefore(endDayTime)) ||
                    (localTime.equals(startDayTime) && localTime.equals(endDayTime));
        }

        // 固定时间开启
        return (now.isAfter(startTime) && now.isBefore(endTime)) || now.equals(startTime) || now.equals(endTime);
    }

    /**
     * 判断该活动今天是否开启 不校验具体时间
     */
    public boolean isOpenDay(Player player) {
        // 活动关闭
        if (!isOpen) {
            return false;
        }
        // 判断服务器是否开启该活动
        for (Pair<Integer, Integer> pair : closeSeverList) {
            if (pair.getLeft() <= Configuration.serverId && Configuration.serverId <= pair.getRight()) {
                return false;
            }
        }
        if (Objects.nonNull(player) && CondManager.checkNotCond(player, condition)) {
            return false;
        }
        // 永久开启
        if (timeType == TimePlayTimeType.permanent) {
            return true;
        }
        // 开服多少天开启
        if (timeType == TimePlayTimeType.openServerDay) {
            return World.getOpenServerDay() >= openDay;
        }
        LocalDateTime now = ServerConstants.getCurrentTimeLocalDateTime();

        // 每天/周/月固定时间开启
        if (timeType == TimePlayTimeType.week || timeType == TimePlayTimeType.everyDay || timeType == TimePlayTimeType.month) {
            if (timeType == TimePlayTimeType.week) {
                DayOfWeek dayOfWeek = now.getDayOfWeek();
                if (dayOfWeekList.contains(dayOfWeek)) {
                    return true;
                }
            }
            if (timeType == TimePlayTimeType.month) {
                int dayOfMonth = now.getDayOfMonth();
                if (dayOfMonthList.contains(dayOfMonth)) {
                    return true;
                }
            }
            return timeType == TimePlayTimeType.everyDay;
        }
        // 固定时间开启
        boolean isSameStartDay = DateTimeUtil.inSameType(DateTimeDurationType.day, now, startTime);
        boolean isSameEndDay = DateTimeUtil.inSameType(DateTimeDurationType.day, now, endTime);
        return isSameStartDay || isSameEndDay || (now.isAfter(startTime) && now.isBefore(endTime));
    }

}

package com.gy.server.game.timePlayPanel;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.log.CommonLogger;
import com.gy.server.game.chess.ChessConstant;
import com.gy.server.game.chess.ChessService;
import com.gy.server.game.combat.StageType;
import com.gy.server.game.constant.ConstantService;
import com.gy.server.game.constant.ConstantType;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.mail.MailManager;
import com.gy.server.game.mail.MailType;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModel;
import com.gy.server.game.player.event.PlayerEvent;
import com.gy.server.game.player.event.PlayerEventHandler;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.text.Text;
import com.gy.server.game.text.TextParamText;
import com.gy.server.packet.PbCommons;
import com.gy.server.packet.PbUser;
import com.gy.server.utils.CollectionUtil;
import com.ttlike.server.tl.baselib.serialize.player.PlayerBlobDb;
import com.ttlike.server.tl.baselib.serialize.player.TimePlayModelDb;

import java.time.DayOfWeek;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 玩家限时玩法模块
 *
 * <AUTHOR> - [Created on 2023/3/22 18:00]
 */
public class PlayerTimePlayModel extends PlayerModel implements PlayerEventHandler {

    /**
     * 今日已参加次数
     * Key:玩法ID value:今日消耗次数
     */
    private Map<Integer, Integer> todayNumMap = new ConcurrentHashMap<>();

    /**
     * 棋局助战次数
     */
    private int chessAssistBattleTimes = 0;

    /**
     * 棋局参加次数
     */
    private int chessJoinTimes;
    /**
     * 棋局参与奖励
     */
    private Set<Integer> hadReceiveChessRewards = new HashSet<>();

    public int getChessAssistBattleTimes() {
        return chessAssistBattleTimes;
    }

    public void setChessAssistBattleTimes(int chessAssistBattleTimes) {
        this.chessAssistBattleTimes = chessAssistBattleTimes;
    }

    public Map<Integer, Integer> getTodayNumMap() {
        return todayNumMap;
    }

    public PbUser.ChessInfo genPb(){
        PbUser.ChessInfo.Builder pb = PbUser.ChessInfo.newBuilder();
        pb.setChessJoinTimes(chessJoinTimes);
        pb.setChessAssistBattleTimes(chessAssistBattleTimes);
        pb.addAllHadReceiveChessRewards(hadReceiveChessRewards);
        return pb.build();
    }

    public void setTodayNumMap(Map<Integer, Integer> todayNumMap) {
        this.todayNumMap = todayNumMap;
    }

    public int getChessJoinTimes() {
        return chessJoinTimes;
    }

    public void setChessJoinTimes(int chessJoinTimes) {
        this.chessJoinTimes = chessJoinTimes;
    }

    public Set<Integer> getHadReceiveChessRewards() {
        return hadReceiveChessRewards;
    }

    public void setHadReceiveChessRewards(Set<Integer> hadReceiveChessRewards) {
        this.hadReceiveChessRewards = hadReceiveChessRewards;
    }

    public PlayerTimePlayModel(Player player) {
        super(player);
    }

    @Override
    protected void loadData(PlayerBlobDb playerBlob) {
        TimePlayModelDb timePlayModelDb = playerBlob.getTimePlayModelDb();
        if (Objects.nonNull(timePlayModelDb)) {
            this.todayNumMap = timePlayModelDb.getTodayNumMap();
            this.chessAssistBattleTimes = timePlayModelDb.getChessAssistBattleTimes();
            this.chessJoinTimes = timePlayModelDb.getChessJoinTimes();
            this.hadReceiveChessRewards = timePlayModelDb.getHadReceiveChessRewards();
        }
    }

    @Override
    protected void saveData(PlayerBlobDb playerBlob) {
        TimePlayModelDb timePlayModelDb = new TimePlayModelDb();
        timePlayModelDb.setTodayNumMap(todayNumMap);
        timePlayModelDb.setChessAssistBattleTimes(chessAssistBattleTimes);
        timePlayModelDb.setChessJoinTimes(chessJoinTimes);
        timePlayModelDb.setHadReceiveChessRewards(hadReceiveChessRewards);
        playerBlob.setTimePlayModelDb(timePlayModelDb);
    }

    @Override
    public PlayerEventType[] getEventTypes() {
        return new PlayerEventType[]{
                PlayerEventType.day5Refresh,
                PlayerEventType.registerAfter,
        };
    }

    @Override
    public void handle(PlayerEvent event) {
        switch (event.getEventType()) {
            case day5Refresh: {
                day5Refresh();
                break;
            }
            case registerAfter:{
                chessAssistBattleTimes = ConstantService.getInt(ConstantType.珍珑棋局每日助战次数上限);
                break;
            }
        }
    }

    private void day5Refresh() {
        for (Integer id : new HashSet<>(todayNumMap.keySet())) {
            TimePlayTemplate timePlayTemplate = TimePlayService.getTimePlayTemplateMap().get(id);
            if (timePlayTemplate.resetType == 1) {
                todayNumMap.remove(id);
            }
        }
        chessAssistBattleTimes = ConstantService.getInt(ConstantType.珍珑棋局每日助战次数上限);

        //每周一清除参加次数
//        DayOfWeek dayOfWeek = ServerConstants.getCurrentTimeLocalDateTime().getDayOfWeek();
//        if(dayOfWeek == DayOfWeek.MONDAY){
//            //检查补发
//            ChessConstant constant = ChessService.getChessConstant();
//            List<RewardTemplate> rewardTemplateList = new ArrayList<>();
//            Map<Integer, List<RewardTemplate>> chessBattleNumberRewards = constant.chessBattleNumberRewards;
//            for (Integer times : chessBattleNumberRewards.keySet()) {
//                if(chessJoinTimes >= times && !hadReceiveChessRewards.contains(times)){
//                    rewardTemplateList.addAll(chessBattleNumberRewards.get(times));
//                }
//            }
//            if(CollectionUtil.isNotEmpty(rewardTemplateList)){
//                MailType mailType = MailType.chessReward;
//                //发放帮派奖励
//                PbCommons.PbText title = Text.genText(mailType.getTitleId()).build();
//                PbCommons.PbText content = Text.genText(mailType.getContentId(), new TextParamText("")).build();
//                MailManager.sendMail(mailType,getPlayerId(), title, content, Reward.templateCollectionToReward(rewardTemplateList));
//            }
//            //清除数据
//            chessJoinTimes = 0;
//            hadReceiveChessRewards.clear();
//        }
    }

    public void addTodayNum(StageType stageType) {
        TimePlayTemplate timePlayTemplate = TimePlayService.getTimePlayTemplateByStageMap().get(stageType.getId());
        if (Objects.isNull(timePlayTemplate)) {
            CommonLogger.error(String.format("addTodayNum is error, stageType is not existed!stageType:%s", stageType));
            return;
        }
        todayNumMap.merge(timePlayTemplate.id, 1, Integer::sum);
    }

    public void reduceTodayNum(StageType stageType){
        TimePlayTemplate timePlayTemplate = TimePlayService.getTimePlayTemplateByStageMap().get(stageType.getId());
        todayNumMap.put(timePlayTemplate.id, todayNumMap.get(timePlayTemplate.id) - 1);
    }

    public int getTodayNum(int id) {
        return todayNumMap.getOrDefault(id, 0);
    }

    public int getTodayNum(StageType stageType) {
        TimePlayTemplate timePlayTemplate = TimePlayService.getTimePlayTemplateByStageMap().get(stageType.getId());
        if (Objects.isNull(timePlayTemplate)) {
            CommonLogger.error(String.format("getTodayNum is error, stageType is not existed!stageType:%s", stageType));
            return 0;
        }
        return todayNumMap.getOrDefault(timePlayTemplate.id, 0);
    }

    /**
     * 次数是否足够
     *
     * @param stageType 战斗类型
     * @return true 足够
     */
    public boolean isEnough(StageType stageType) {
        int todayNum = getTodayNum(stageType);
        TimePlayTemplate timePlayTemplate = TimePlayService.getTimePlayTemplateByStageMap().get(stageType.getId());
        return todayNum < timePlayTemplate.numberMax;
    }
}

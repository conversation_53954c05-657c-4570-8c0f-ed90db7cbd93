package com.gy.server.game.dataBalancing.template;

import com.gy.server.game.dataBalancing.bean.BalancingEnum;
import com.gy.server.game.equipment.EquipmentPositionType;
import com.gy.server.game.util.StringExtUtil;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Map;

/**
 * 数据拉平装备信息
 * <AUTHOR> - [Created on 2023/2/14 14:29]
 */
public class DataBalancingEquipTemplate {

    /**
     * 唯一id
     */
    public int id;

    /**
     * 装备位置
     */
    public EquipmentPositionType equipType;

    /**
     * 装备id
     */
    public int equipId;

    /**
     * 强化等级
     */
    public Pair<BalancingEnum, Integer> maxStrengthenLv;

    /**
     * 淬炼词条等级
     */
    public Pair<BalancingEnum, Integer> masterLv;

    public DataBalancingEquipTemplate(Map<String, String> map){
        this.id = Integer.parseInt(map.get("Id"));
        this.equipType = EquipmentPositionType.getTypeById(Integer.parseInt(map.get("equipType")));
        this.equipId = Integer.parseInt(map.get("equipId"));

        Pair<Integer, Integer> pair = StringExtUtil.string2Pair(map.get("maxStrengthenLv"), "_", Integer.class, Integer.class);
        this.maxStrengthenLv = Pair.of(BalancingEnum.of(pair.getLeft()), pair.getRight());

        pair = StringExtUtil.string2Pair(map.get("masterLv"), "_", Integer.class, Integer.class);
        this.masterLv = Pair.of(BalancingEnum.of(pair.getLeft()), pair.getRight());
    }
}

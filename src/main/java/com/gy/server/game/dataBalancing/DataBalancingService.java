package com.gy.server.game.dataBalancing;

import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.common.ConstantConfigReader;
import com.gy.server.game.dataBalancing.template.DataBalancingConst;
import com.gy.server.game.dataBalancing.template.DataBalancingEquipTemplate;
import com.gy.server.game.dataBalancing.template.DataBalancingTemplate;
import com.gy.server.game.service.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据拉平
 *
 * <AUTHOR> - [Created on 2023/2/14 14:29]
 */
public class DataBalancingService implements Service {

    /**
     * 数据拉平装备详细信息
     * key:配置唯一id
     * value：装备详细信息
     */
    private static Map<Integer, DataBalancingEquipTemplate> equipInfos = new HashMap<>();

    private static Map<Integer, DataBalancingTemplate> dataBalancingTemplateMap = new HashMap<>();

    private static DataBalancingConst dataBalancingConst;

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.dataBalancing_dataBalancing);
        Map<Integer, DataBalancingTemplate> dataBalancingTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            DataBalancingTemplate dataBalancingTemplate = new DataBalancingTemplate(map);
            dataBalancingTemplateMapTemp.put(dataBalancingTemplate.id, dataBalancingTemplate);
        }
        dataBalancingTemplateMap = dataBalancingTemplateMapTemp;

        mapList = ConfigReader.read(ConfigFile.dataBalancing_equip);
        Map<Integer, DataBalancingEquipTemplate> equipInfosTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            //装备信息
            DataBalancingEquipTemplate equipTemplate = new DataBalancingEquipTemplate(map);
            equipInfosTemp.put(equipTemplate.id, equipTemplate);
        }
        equipInfos = equipInfosTemp;

        Map<String, String> map = ConstantConfigReader.read(ConfigFile.dataBalancing_const);
        dataBalancingConst = new DataBalancingConst(map);
    }

    @Override
    public void clearConfigData() {
        dataBalancingTemplateMap.clear();
        equipInfos.clear();
    }

    public static Map<Integer, DataBalancingEquipTemplate> getEquipInfos() {
        return equipInfos;
    }

    public static Map<Integer, DataBalancingTemplate> getDataBalancingTemplateMap() {
        return dataBalancingTemplateMap;
    }

    public static DataBalancingConst getDataBalancingConst() {
        return dataBalancingConst;
    }

    public static List<DataBalancingEquipTemplate> getDataBalancingEquipTemplateListByIds(List<Integer> ids){
        List<DataBalancingEquipTemplate> list = new ArrayList<>();
        for (int id : ids) {
            if(equipInfos.containsKey(id)){
                list.add(equipInfos.get(id));
            }
        }
        return list;
    }
}

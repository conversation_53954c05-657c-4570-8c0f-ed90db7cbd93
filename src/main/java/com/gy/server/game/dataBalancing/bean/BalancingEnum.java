package com.gy.server.game.dataBalancing.bean;


import java.util.HashMap;
import java.util.Map;

/**
 *
 * 1：将属性拉至对应数值
 * 2：将低于此数值的属性拉至对应数值
 * 3:保留自身属性
 * 4使用equip的属性
 * <AUTHOR> 2024/11/25 17:57
 **/
public enum BalancingEnum {

    /**
     * 1：将属性拉至对应数值
     */
    pull(1),

    /**
     * 2：将低于此数值的属性拉至对应数值
     */
    selectPull(2),

    /**
     * 3:保留自身属性
     */
    reserve(3),

    /**
     * 4使用equip的属性
     */
    equip(4),
    ;

    int type;
//    BalancingDealImpl deal;

    BalancingEnum(int type){
        this.type = type;
//        this.deal = deal;
    }

    public static BalancingEnum of(String type){
        return of(Integer.parseInt(type));
    }

    public static BalancingEnum of(int type){
        for (BalancingEnum bean : BalancingEnum.values()) {
            if(type == bean.type){
                return bean;
            }
        }

        return null;
    }

    /**
     *
     * @param num 要比较的值
     * @param templateNum 配平值
     * @return
     */
    public int getValue(int num, int templateNum){
        switch (this){
            case pull: return templateNum;
            case selectPull: return num < templateNum ? templateNum : num;
        }
        return num;
    }

    /**
     *
     * @param martialArtsTechMap
     * @param right 配平值
     * @return
     */
    public Map<Integer, Integer> getValue(Map<Integer, Integer> martialArtsTechMap, Map<Integer, Integer> right) {
        Map<Integer, Integer> temp = new HashMap<>(right);
        for (Map.Entry<Integer, Integer> entry : temp.entrySet()) {
            temp.put(entry.getKey(), getValue(martialArtsTechMap.getOrDefault(entry.getKey(), 0), entry.getValue()));
        }
        return temp;
    }
}

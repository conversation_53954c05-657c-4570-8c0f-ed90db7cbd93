package com.gy.server.game.dataBalancing;

import com.gy.server.game.artifact.ArtifactService;
import com.gy.server.game.artifact.bean.ArtifactTemplate;
import com.gy.server.game.attribute.AttributeSourceType;
import com.gy.server.game.attribute.Attributes;
import com.gy.server.game.cheats.bean.Cheats;
import com.gy.server.game.combat.Formula;
import com.gy.server.game.combat.skill.Skill;
import com.gy.server.game.combat.unit.HeroUnitCreator;
import com.gy.server.game.combataddition.CombatAdditionHelper;
import com.gy.server.game.dataBalancing.bean.BalancingEnum;
import com.gy.server.game.dataBalancing.template.DataBalancingEquipTemplate;
import com.gy.server.game.dataBalancing.template.DataBalancingTemplate;
import com.gy.server.game.equipment.EquipmentPositionType;
import com.gy.server.game.equipment.EquipmentService;
import com.gy.server.game.equipment.bean.Equipment;
import com.gy.server.game.equipment.bean.EquipmentMaster;
import com.gy.server.game.equipment.template.EquipStrengthenTemplate;
import com.gy.server.game.equipment.template.EquipmentMasterAttrTemplate;
import com.gy.server.game.equipment.template.EquipmentTemplate;
import com.gy.server.game.gem.GemService;
import com.gy.server.game.gem.GemTemplate;
import com.gy.server.game.gem.bean.MosaicInfo;
import com.gy.server.game.hero.Hero;
import com.gy.server.game.heroSpectrum.PlayerHeroSpectrumModel;
import com.gy.server.game.item.ItemService;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.league.LeagueModelEnums;
import com.gy.server.game.leagueBoss.LeagueBossModel;
import com.gy.server.game.lineup.LineupService;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.lineup.bean.LineupInfoBean;
import com.gy.server.game.lineup.template.CaptainSkillTemplate;
import com.gy.server.game.lineup.template.ViceSkillTemplate;
import com.gy.server.game.martialArtsTech.PlayerMartialArtsTechModel;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.game.role.BookInfoBean;
import com.gy.server.game.role.PlayerRoleService;
import com.gy.server.game.role.template.ProtagonistTemplate;
import com.ttlike.server.tl.baselib.serialize.combatAddition.CombatAdditions;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;

import java.util.*;

/**
 * 数据拉平辅助类
 * 拉平规则：用需要拉平的属性对原有的属性覆盖，不需要拉平的则保持原属性不变
 *
 * <AUTHOR> - [Created on 2023/2/14 15:12]
 */
public class DataBalancingHelper {

    /**
     * 主角属性拉平
     */
    public static HeroUnitCreator calcRoleBalancing(Player player, int dataBalanceId, LineupType lineupType) {
        Map<AttributeSourceType, Attributes> attributesMap = new HashMap<>(player.getRoleModel().getAttributesMap());
        DataBalancingTemplate dataBalancingTemplate = DataBalancingService.getDataBalancingTemplateMap().get(dataBalanceId);

        // 等级模板属性
        Pair<BalancingEnum, Integer> pair = dataBalancingTemplate.level;
        int playerLevel = pair.getLeft().getValue(player.getLevel(), pair.getRight());
        Attributes templateAttributes = Formula.calRoleBaseAttributes(player);
        attributesMap.put(AttributeSourceType.英雄模板属性, templateAttributes);

        //装备
        Pair<BalancingEnum, List<Integer>> equipPair = dataBalancingTemplate.equipId;
        if(equipPair.getLeft() == BalancingEnum.pull){
            Map<EquipmentPositionType, Integer> equipMap = player.getRoleModel().getEquipments();
            List<DataBalancingEquipTemplate> list = DataBalancingService.getDataBalancingEquipTemplateListByIds(equipPair.getRight());

            Attributes equipAttributes = new Attributes();
            for (DataBalancingEquipTemplate bean : list) {
                Attributes.mergeAttributes(equipAttributes, calEquipAttributesByLevel(player, bean, equipMap.getOrDefault(bean.equipType, 0)));
            }
            attributesMap.put(AttributeSourceType.装备, equipAttributes);
        }

        //功法属性
        pair = dataBalancingTemplate.book;
        Map<Integer, BookInfoBean> bookInfoBeanMap = buildBookInfoMap(player, pair);
        Attributes bookAttributes = Formula.calculateBookAttributes(bookInfoBeanMap);
        attributesMap.put(AttributeSourceType.功法, bookAttributes);

        //突破等级
        pair = dataBalancingTemplate.breakLevel;
        int breakLevel = pair.getLeft().getValue(player.getRoleModel().getBreakLevel(), pair.getRight());
        Attributes breakAttributes = Formula.calcRoleBreakAttributes(player, breakLevel);
        attributesMap.put(AttributeSourceType.突破, breakAttributes);

        //主角没有星级
        //主角没有专属

        //宝石
        pair = dataBalancingTemplate.gemLevel;
        List<String> gemKeyList = GemService.getMosaicInfoKeys();
        Map<String, MosaicInfo> gemMap = player.getRoleModel().getGemMap();
        Attributes gemAttributes = new Attributes();
        for (String key : gemKeyList) {
            MosaicInfo mosaicInfo = gemMap.get(key);
            if(mosaicInfo != null && mosaicInfo.getGemId() != 0){
                GemTemplate gemTemplate = (GemTemplate) ItemService.getItemTemplate(mosaicInfo.getGemId());
                int gemLevel = pair.getLeft().getValue(gemTemplate.lv, pair.getRight());
                if(gemLevel != 0){
                    if(gemTemplate.lv != gemLevel){
                        gemTemplate = ItemService.getGemTemplateByTypeAndLv(gemTemplate.type, gemLevel);
                    }
                    Attributes.mergeAttributes(gemAttributes, gemTemplate.attr);
                }
            }else {
                GemTemplate gemTemplate = (GemTemplate) ItemService.getItemTemplate(DataBalancingService.getDataBalancingConst().gem);
                Attributes.mergeAttributes(gemAttributes, gemTemplate.attr);
            }
        }
        attributesMap.put(AttributeSourceType.宝石, breakAttributes);

        //秘籍
        pair = dataBalancingTemplate.esotericaLevel;
        Cheats wearCheats = player.getBagModel().getWearCheat(Cheats.main_Hero_Wear);
        int esotericaLevel = pair.getLeft().getValue(wearCheats != null ? wearCheats.getLevel() : 0, pair.getRight());
        Attributes esotericaAttributes = Formula.calculateCheatsAttributes(DataBalancingService.getDataBalancingConst().esoterica, esotericaLevel);
        attributesMap.put(AttributeSourceType.秘籍, esotericaAttributes);

        //神器-主角属性加成
        pair = dataBalancingTemplate.artifactLevel;
        int artifactLevel = pair.getLeft().getValue(player.getArtifactModel().getCurrentUnlockLevel(), pair.getRight());
        Attributes artifactAttributes = Formula.calcArtifactAttributes(player.getProfession(), artifactLevel);
        attributesMap.put(AttributeSourceType.神器主角, artifactAttributes);

        //头衔
        pair = dataBalancingTemplate.title;
        int titleId = pair.getLeft().getValue(player.getRoleModel().getHeroTitleId(), pair.getRight());
        Attributes titleAttributes = Formula.calcTitleAttributes(titleId);
        attributesMap.put(AttributeSourceType.头衔, titleAttributes);

        //全局属性
        CombatAdditions combatAdditions = null;
        if(lineupType != LineupType.Common){
            combatAdditions = new CombatAdditions();

            //神器-全体
            ArtifactTemplate artifactTemplate = ArtifactService.getArtifactTemplate(player.getProfession(), artifactLevel);
            if(artifactTemplate != null){
                CombatAdditions artifactCombatAdditions = CombatAdditionHelper.calcArtifactCombatAdditions(artifactLevel, Math.max(artifactTemplate.combatAdditions.size(), 1), player.getProfession());
                CombatAdditions.mergeCombatAdditions(combatAdditions, artifactCombatAdditions);
            }

            //器魂-全体
            pair = dataBalancingTemplate.soulLevel;
            int soulLevel = pair.getLeft().getValue(player.getWeaponSoulModel().getId(), pair.getRight());
            CombatAdditions weaponSoulCombatAdditions = CombatAdditionHelper.calcWeaponSoulCombatAdditions(soulLevel);
            CombatAdditions.mergeCombatAdditions(combatAdditions, weaponSoulCombatAdditions);

            //心法-全体
            Pair<BalancingEnum, Map<Integer, Integer>> mentalPair = dataBalancingTemplate.mentalMethod;
            PlayerMartialArtsTechModel artsTechModel = player.getModel(PlayerModelEnums.martialArtsTech);
            Map<Integer, Integer> mapTemp = mentalPair.getLeft().getValue(artsTechModel.getMartialArtsTechMap(), mentalPair.getRight());
            CombatAdditions mentalMethodCombatAdditions = CombatAdditionHelper.calcMartialArtsTechStarCombatAdditions(mapTemp);
            CombatAdditions.mergeCombatAdditions(combatAdditions, mentalMethodCombatAdditions);

            //英雄谱
            PlayerHeroSpectrumModel heroModel = player.getModel(PlayerModelEnums.heroSpectrum);
            CombatAdditions.mergeCombatAdditions(combatAdditions, CombatAdditionHelper.calcHeroSpectrumCombatAdditions(heroModel.getHeroSpectrumLvMap()));

            //门课堂
            if (LeagueManager.isJoinLeague(player)) {
                League league = LeagueManager.getLeagueByPlayer(player);
                LeagueBossModel leagueBossModel = league.getModel(LeagueModelEnums.boss);
                CombatAdditions.mergeCombatAdditions(combatAdditions, CombatAdditionHelper.calcLeagueBossPalCombatAdditions(leagueBossModel.getPalLevel()));
            }
        }

        Attributes combatAdditionAttrs = null;
        if(combatAdditions != null){
            Pair<Attributes, Map<Integer, Set<Integer>>> pairTemp = CombatAdditionHelper.getAttrs(player, null, lineupType, lineupType.getStageType(), combatAdditions);
            combatAdditionAttrs = pairTemp.getLeft();
        }

        Attributes totalAttributes = Formula.calTotalAttrs(attributesMap, combatAdditionAttrs);

        //技能最后计算 受多个养成影响
        Map<Integer, Integer> skillResult = new HashMap<>();
        pair = dataBalancingTemplate.skillLevel;

        ProtagonistTemplate protagonistTemplate = PlayerRoleService.getProtagonistTemplate(player.getProfession(), player.getSex());

        //默认技能
        Map<Integer, Integer> skillMapTemp = player.getRoleModel().getAllSkill();
        for (int skillId : protagonistTemplate.angerSkillList) {
            int skillLevel = pair.getLeft().getValue(skillMapTemp.getOrDefault(skillId, 1), pair.getRight());
            skillResult.put(skillId, skillLevel);
        }
        for (int skillId : protagonistTemplate.normalSkillList) {
            int skillLevel = pair.getLeft().getValue(skillMapTemp.getOrDefault(skillId, 1), pair.getRight());
            skillResult.put(skillId, skillLevel);
        }
        //神器
        for (Skill skill : player.getArtifactModel().getSkills(artifactLevel)) {
            int skillLevel = pair.getLeft().getValue(skillMapTemp.getOrDefault(skill.getTid(), 1), pair.getRight());
            skillResult.put(skill.getTid(), skillLevel);
        }

        HeroUnitCreator heroUnit = new HeroUnitCreator(player, lineupType.getStageType(), lineupType);
        heroUnit.setLevel(playerLevel);
        heroUnit.setAttributes(totalAttributes);
        heroUnit.getAttributes().refreshCurrentValue();

        //计算战力
        heroUnit.setFightingPower(totalAttributes.getAttrFightingPower());
        return getHeroUnitCreator(heroUnit, skillResult);
    }


    /**
     * 侠客属性拉平
     */
    public static HeroUnitCreator calcHeroBalancing(LineupInfoBean lineupInfoBean, Player player, Hero hero, int dataBalanceId, LineupType lineupType) {
        Map<AttributeSourceType, Attributes> attributesMap = new HashMap<>(hero.getAttributesMap());
        DataBalancingTemplate dataBalancingTemplate = DataBalancingService.getDataBalancingTemplateMap().get(dataBalanceId);
//        HeroTemplate template = hero.getItemTemplate();

        //装备
        Pair<BalancingEnum, List<Integer>> equipPair = dataBalancingTemplate.equipId;
        if(equipPair.getLeft() == BalancingEnum.pull){
            Map<EquipmentPositionType, Integer> equipMap = hero.getEquipments();
            List<DataBalancingEquipTemplate> list = DataBalancingService.getDataBalancingEquipTemplateListByIds(equipPair.getRight());

            Attributes equipAttributes = new Attributes();
            for (DataBalancingEquipTemplate bean : list) {
                Attributes.mergeAttributes(equipAttributes, calEquipAttributesByLevel(player, bean, equipMap.getOrDefault(bean.equipType, 0)));
            }
            attributesMap.put(AttributeSourceType.装备, equipAttributes);
        }

        //侠客星级
        Pair<BalancingEnum, Integer> pair = dataBalancingTemplate.heroStar;
        int heroStar = pair.getLeft().getValue(hero.getStar(), pair.getRight());

        //侠客等级
        pair = dataBalancingTemplate.heroLevel;
        int heroLevel = pair.getLeft().getValue(hero.getLevelWithPassGong(), pair.getRight());
        Attributes templateAttributes = Formula.calcHeroTemplateAttributes(hero.getItemTemplate(), heroLevel, heroStar, 0, hero.getQuality());
        attributesMap.put(AttributeSourceType.英雄模板属性, templateAttributes);

        //突破等级
        pair = dataBalancingTemplate.breakLevel;
        int breakLevel = pair.getLeft().getValue(hero.getBreakLevelWithPassGong(), pair.getRight());
        Attributes breakAttributes = Formula.calcHeroBreakAttributes(hero.getTemplateId(), hero.getQuality(), breakLevel);
        attributesMap.put(AttributeSourceType.突破, breakAttributes);

        //侠客专属
        pair = dataBalancingTemplate.exclusiveLevel;
        int exclusiveLevel = pair.getLeft().getValue(hero.getExclusiveLevel(), pair.getRight());
        Attributes exclusiveAttributes = Formula.calcExclusiveAttributes(hero.getTemplateId(), exclusiveLevel);
        attributesMap.put(AttributeSourceType.专属, exclusiveAttributes);

        //宝石
        pair = dataBalancingTemplate.gemLevel;
        List<String> gemKeyList = GemService.getMosaicInfoKeys();
        Map<String, MosaicInfo> gemMap = hero.getGemMap();
        Attributes gemAttributes = new Attributes();
        for (String key : gemKeyList) {
            MosaicInfo mosaicInfo = gemMap.get(key);
            if(mosaicInfo != null && mosaicInfo.getGemId() != 0){
                GemTemplate gemTemplate = (GemTemplate) ItemService.getItemTemplate(mosaicInfo.getGemId());
                int gemLevel = pair.getLeft().getValue(gemTemplate.lv, pair.getRight());
                if(gemLevel != 0){
                    if(gemTemplate.lv != gemLevel){
                        gemTemplate = ItemService.getGemTemplateByTypeAndLv(gemTemplate.type, gemLevel);
                    }
                    Attributes.mergeAttributes(gemAttributes, gemTemplate.attr);
                }
            }else {
                GemTemplate gemTemplate = (GemTemplate) ItemService.getItemTemplate(DataBalancingService.getDataBalancingConst().gem);
                Attributes.mergeAttributes(gemAttributes, gemTemplate.attr);
            }
        }
        attributesMap.put(AttributeSourceType.宝石, breakAttributes);

        //秘籍
        pair = dataBalancingTemplate.esotericaLevel;
        Cheats wearCheats = player.getBagModel().getWearCheat(hero.getItemId());
        int esotericaLevel = pair.getLeft().getValue(wearCheats != null ? wearCheats.getLevel() : 0, pair.getRight());
        Attributes esotericaAttributes = Formula.calculateCheatsAttributes(DataBalancingService.getDataBalancingConst().esoterica, esotericaLevel);
        attributesMap.put(AttributeSourceType.秘籍, esotericaAttributes);

        //头衔
        pair = dataBalancingTemplate.title;
        int titleId = pair.getLeft().getValue(player.getRoleModel().getHeroTitleId(), pair.getRight());
        Attributes titleAttributes = Formula.calcTitleAttributes(titleId);
        attributesMap.put(AttributeSourceType.头衔, titleAttributes);

        //全局属性
        CombatAdditions combatAdditions = null;
        if(lineupType != LineupType.Common){
            combatAdditions = new CombatAdditions();

            //神器-全体
            pair = dataBalancingTemplate.artifactLevel;
            int artifactLevel = pair.getLeft().getValue(player.getArtifactModel().getCurrentUnlockLevel(), pair.getRight());
            ArtifactTemplate artifactTemplate = ArtifactService.getArtifactTemplate(player.getProfession(), artifactLevel);
            if(artifactTemplate != null){
                CombatAdditions artifactCombatAdditions = CombatAdditionHelper.calcArtifactCombatAdditions(artifactLevel, Math.max(artifactTemplate.combatAdditions.size(), 1), player.getProfession());
                CombatAdditions.mergeCombatAdditions(combatAdditions, artifactCombatAdditions);
            }

            //器魂-全体
            pair = dataBalancingTemplate.soulLevel;
            int soulLevel = pair.getLeft().getValue(player.getWeaponSoulModel().getId(), pair.getRight());
            CombatAdditions weaponSoulCombatAdditions = CombatAdditionHelper.calcWeaponSoulCombatAdditions(soulLevel);
            CombatAdditions.mergeCombatAdditions(combatAdditions, weaponSoulCombatAdditions);

            //心法-全体
            Pair<BalancingEnum, Map<Integer, Integer>> mentalPair = dataBalancingTemplate.mentalMethod;
            PlayerMartialArtsTechModel artsTechModel = player.getModel(PlayerModelEnums.martialArtsTech);
            Map<Integer, Integer> mapTemp = mentalPair.getLeft().getValue(artsTechModel.getMartialArtsTechMap(), mentalPair.getRight());
            CombatAdditions mentalMethodCombatAdditions = CombatAdditionHelper.calcMartialArtsTechStarCombatAdditions(mapTemp);
            CombatAdditions.mergeCombatAdditions(combatAdditions, mentalMethodCombatAdditions);

            //英雄谱
            PlayerHeroSpectrumModel heroModel = player.getModel(PlayerModelEnums.heroSpectrum);
            CombatAdditions.mergeCombatAdditions(combatAdditions, CombatAdditionHelper.calcHeroSpectrumCombatAdditions(heroModel.getHeroSpectrumLvMap()));

            //门课堂
            if (LeagueManager.isJoinLeague(player)) {
                League league = LeagueManager.getLeagueByPlayer(player);
                LeagueBossModel leagueBossModel = league.getModel(LeagueModelEnums.boss);
                CombatAdditions.mergeCombatAdditions(combatAdditions, CombatAdditionHelper.calcLeagueBossPalCombatAdditions(leagueBossModel.getPalLevel()));
            }

        }

        Attributes combatAdditionAttrs = null;
        if(combatAdditions != null){
            Pair<Attributes, Map<Integer, Set<Integer>>> pairTemp = CombatAdditionHelper.getAttrs(player, null, lineupType, lineupType.getStageType(), combatAdditions);
            combatAdditionAttrs = pairTemp.getLeft();
        }

        Attributes totalAttributes = Formula.calTotalAttrs(attributesMap, combatAdditionAttrs);

        HeroUnitCreator heroUnit = new HeroUnitCreator(hero, player, lineupType.getStageType(), lineupType);
        heroUnit.setLevel(heroLevel);
        heroUnit.setAttributes(totalAttributes);
        heroUnit.getAttributes().refreshCurrentValue();
        
        //计算战力
        heroUnit.setFightingPower(totalAttributes.getAttrFightingPower());

        //技能最后计算 受多个养成影响
        Map<Integer, Integer> skillResult = new HashMap<>(hero.getSkillMap());

        //技能最后计算 受多个养成影响
//        LineupInfoBean lineInfoBean = lineupType.getLineupByHero(hero.getPlayer(), hero.getItemId());
        //设置队长技能和副队技能
        if (Objects.nonNull(lineupInfoBean)) {
            int captainSkill = lineupInfoBean.getCaptainSkill();
            int viceCaptainSkill = lineupInfoBean.getViceCaptainSkill();
            int captainHeroId;
            int viceCaptainHeroId;
            if (captainSkill != 0) {
                CaptainSkillTemplate captainSkillTemplate = LineupService.getCaptainSkillTemplate(captainSkill);
                captainHeroId = captainSkillTemplate.targetHero;
                if (captainHeroId == hero.getTemplateId()) {
                    skillResult.put(captainSkillTemplate.skillId, 1);
                }
            }
            if (viceCaptainSkill != 0) {
                ViceSkillTemplate viceSkillTemplate = LineupService.getViceSkillTemplate(viceCaptainSkill);
                viceCaptainHeroId = viceSkillTemplate.targetHero;
                if (viceCaptainHeroId == hero.getTemplateId()) {
                    skillResult.put(viceSkillTemplate.skillId, 1);
                }
            }
        }
        //设置buff
        {
            Map<Integer, Integer> buffs = hero.getBuffs(lineupType, lineupType.getStageType());
            heroUnit.getTotalBuffs().putAll(buffs);
        }

        pair = dataBalancingTemplate.skillLevel;
        for (Map.Entry<Integer, Integer> bean : skillResult.entrySet()) {
            int skillLevel = pair.getLeft().getValue(bean.getValue(), pair.getRight());
            bean.setValue(skillLevel);
        }

        return getHeroUnitCreator(heroUnit, skillResult);
    }

    @NotNull
    private static HeroUnitCreator getHeroUnitCreator(HeroUnitCreator heroUnitCreator, Map<Integer, Integer> skillResult) {
        for (Map.Entry<Integer, Integer> entry : skillResult.entrySet()) {
            Integer skillId = entry.getKey();
            Integer skillLevel = entry.getValue();
            heroUnitCreator.addSkill(skillId, skillLevel, 0);
        }
        return heroUnitCreator;
    }

    /**
     * 计算拉平装备总属性
     */
    private static Attributes calEquipAttributesByLevel(Player player, DataBalancingEquipTemplate bean, int wearEquipId) {
        Attributes totalAttributes = new Attributes();

        try {
            Equipment wearEquipment = player.getBagModel().getEquipmentById(wearEquipId);
            EquipmentTemplate equipmentTemplate = (EquipmentTemplate) ItemService.getItemTemplate(bean.equipId);

            // 初始属性
            Attributes attributes = new Attributes();
            Attributes.mergeAttributes(attributes, equipmentTemplate.baseAttr);

            // 强化属性
            Pair<BalancingEnum, Integer> temp = bean.maxStrengthenLv;
            int strengthenLv = temp.getLeft().getValue(wearEquipment == null ? 0 : wearEquipment.getStrengthenLv(), temp.getRight());

            Attributes strengthenAttributes = new Attributes();
            Map<Integer, EquipStrengthenTemplate> map = EquipmentService.getStrengthenTemplateMap().get(equipmentTemplate.getStrengthenKey());
            for (int i = 1; i < strengthenLv; i++) {
                Attributes.mergeAttributes(strengthenAttributes, map.get(i).baseAttribute);
            }

            for (Map.Entry<Integer, Long> entry : strengthenAttributes.getAttributeMap().entrySet()) {
                long value = (long) (entry.getValue() * equipmentTemplate.baseAttrGrowth.getOrDefault(entry.getKey(), 1d));
                attributes.addAttr(entry.getKey(), value);
            }

            //淬炼
            temp = bean.masterLv;
            for (int i = 0; i < equipmentTemplate.masterAttr.size(); i++) {
                int masterId = equipmentTemplate.masterAttr.get(i);
                int wearMasterLv = 0;
                if(wearEquipment != null){
                    EquipmentMaster master = wearEquipment.getMasterMap().get(masterId);
                    if(master != null){
                       wearMasterLv = master.getMasterLv();
                    }
                }
                int masterLv = temp.getLeft().getValue(wearMasterLv, temp.getRight());;

                EquipmentMasterAttrTemplate equipmentMasterAttrTemplate = EquipmentService.getEquipmentMasterAttrTemplate(masterId, masterLv);
                if (equipmentMasterAttrTemplate != null) {
                    Attributes.mergeAttributes(totalAttributes, equipmentMasterAttrTemplate.masterAttr);
                }
            }

            Attributes.mergeAttributes(totalAttributes, attributes);
        }catch (Exception e){
            e.printStackTrace();
        }

        return totalAttributes;
    }

    /**
     * 构造功法信息
     * 解锁所有功法 拉平到指定功法阶数升满
     *
     * @param player
     * @param pair
     * @return 功法信息
     */
    public static Map<Integer, BookInfoBean> buildBookInfoMap(Player player, Pair<BalancingEnum, Integer> pair) {
        ProtagonistTemplate protagonistTemplate = PlayerRoleService.getProtagonistTemplate(player);
        Set<Integer> bookIdSet = PlayerRoleService.getBookIdByTypeSet(protagonistTemplate.bookType);

        Map<Integer, BookInfoBean> bookMap = new HashMap<>();
        for (Integer bookId : bookIdSet) {
            int maxGrade = PlayerRoleService.getBookMaxGrade(bookId);
            int maxLv = PlayerRoleService.getBookMaxLv(bookId, maxGrade);
            BookInfoBean bookInfoBeanTemp = new BookInfoBean(bookId);
            for (Integer i = 1; i <= maxGrade; i++) {
                bookInfoBeanTemp.getGrade2Level().put(i, PlayerRoleService.getBookMaxLv(bookId, i));
            }
            bookMap.put(bookId, bookInfoBeanTemp);
        }
        return bookMap;
    }


}

package com.gy.server.game.dataBalancing.template;

import java.util.Map;

/**
 * 数据拉平常量类
 *
 * <AUTHOR> 2024/11/27 11:25
 **/
public class DataBalancingConst {

    /**
     * 默认宝石3级虎眼石ID
     */
    public int gem;

    /**
     * 默认秘籍雁行功ID
     */
    public int esoterica;

    public DataBalancingConst(Map<String, String> map) {
        this.gem = Integer.parseInt(map.get("gem"));
        this.esoterica = Integer.parseInt(map.get("esoterica"));
    }
}

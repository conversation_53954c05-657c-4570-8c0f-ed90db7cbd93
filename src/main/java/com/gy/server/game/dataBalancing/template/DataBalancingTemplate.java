package com.gy.server.game.dataBalancing.template;

import com.gy.server.game.dataBalancing.bean.BalancingEnum;
import com.gy.server.game.util.StringExtUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据拉平模板
 *
 * <AUTHOR> - [Created on 2023/2/14 15:30]
 */
public class DataBalancingTemplate {

    public int id;

    /**
     * 装备拉平
     * 不考虑类型为2的情况
     */
    public Pair<BalancingEnum, List<Integer>> equipId;

    /**
     * 主角等级
     */
    public Pair<BalancingEnum, Integer> level;

    /**
     * 主角功法阶数
     */
    public Pair<BalancingEnum, Integer> book;

    /**
     * 英雄等级
     */
    public Pair<BalancingEnum, Integer> heroLevel;

    /**
     * 英雄突破等级
     */
    public Pair<BalancingEnum, Integer> breakLevel;

    /**
     * 英雄星级
     */
    public Pair<BalancingEnum, Integer> heroStar;

    /**
     * 英雄专属
     */
    public Pair<BalancingEnum, Integer> exclusiveLevel;

    /**
     * 技能等级
     */
    public Pair<BalancingEnum, Integer> skillLevel;

    /**
     * 宝石等级
     * 玩家身上没有宝石的槽位，那就给他全换成3级的虎眼石
     */
    public Pair<BalancingEnum, Integer> gemLevel;

    /**
     * 秘籍等级
     */
    public Pair<BalancingEnum, Integer> esotericaLevel;

//    /**
//     * 秘籍星级
//     */
//    public Pair<BalancingEnum, Integer> esotericaStar;

    /**
     * 主角神器等级
     */
    public Pair<BalancingEnum, Integer> artifactLevel;

    /**
     * 头衔
     */
    public Pair<BalancingEnum, Integer> title;

    /**
     * 器魂
     */
    public Pair<BalancingEnum, Integer> soulLevel;

    /**
     * 心法
     */
    public Pair<BalancingEnum, Map<Integer, Integer>> mentalMethod;

    public DataBalancingTemplate(Map<String, String> map){
        this.id = Integer.parseInt(map.get("id"));

        String[] splitStrArr = StringUtils.split(map.get("equipId"), "_");
        List<Integer> list = StringExtUtil.string2List(splitStrArr[1], ",", Integer.class);
        this.equipId = Pair.of(BalancingEnum.of(splitStrArr[0]), list);

        Pair<Integer, Integer> pair = StringExtUtil.string2Pair(map.get("protagonistLevel"), "_", Integer.class, Integer.class);
        this.level = Pair.of(BalancingEnum.of(pair.getLeft()), pair.getRight());

        pair = StringExtUtil.string2Pair(map.get("bookGrade"), "_", Integer.class, Integer.class);
        this.book = Pair.of(BalancingEnum.of(pair.getLeft()), pair.getRight());

        pair = StringExtUtil.string2Pair(map.get("heroLevel"), "_", Integer.class, Integer.class);
        this.heroLevel = Pair.of(BalancingEnum.of(pair.getLeft()), pair.getRight());

        pair = StringExtUtil.string2Pair(map.get("BreakthroughLevel"), "_", Integer.class, Integer.class);
        this.breakLevel = Pair.of(BalancingEnum.of(pair.getLeft()), pair.getRight());

        pair = StringExtUtil.string2Pair(map.get("heroStar"), "_", Integer.class, Integer.class);
        this.heroStar = Pair.of(BalancingEnum.of(pair.getLeft()), pair.getRight());

        pair = StringExtUtil.string2Pair(map.get("exclusiveIevel"), "_", Integer.class, Integer.class);
        this.exclusiveLevel = Pair.of(BalancingEnum.of(pair.getLeft()), pair.getRight());

        pair = StringExtUtil.string2Pair(map.get("skillLevel"), "_", Integer.class, Integer.class);
        this.skillLevel = Pair.of(BalancingEnum.of(pair.getLeft()), pair.getRight());

        pair = StringExtUtil.string2Pair(map.get("gemGrade"), "_", Integer.class, Integer.class);
        this.gemLevel = Pair.of(BalancingEnum.of(pair.getLeft()), pair.getRight());

        pair = StringExtUtil.string2Pair(map.get("esotericaLevel"), "_", Integer.class, Integer.class);
        this.esotericaLevel = Pair.of(BalancingEnum.of(pair.getLeft()), pair.getRight());

//        pair = StringExtUtil.string2Pair(map.get("esotericaStar"), "_", Integer.class, Integer.class);
//        this.esotericaStar = Pair.of(BalancingEnum.of(pair.getLeft()), pair.getRight());

        pair = StringExtUtil.string2Pair(map.get("artifactLevel"), "_", Integer.class, Integer.class);
        this.artifactLevel = Pair.of(BalancingEnum.of(pair.getLeft()), pair.getRight());

        pair = StringExtUtil.string2Pair(map.get("title"), "_", Integer.class, Integer.class);
        this.title = Pair.of(BalancingEnum.of(pair.getLeft()), pair.getRight());

        pair = StringExtUtil.string2Pair(map.get("soul"), "_", Integer.class, Integer.class);
        this.soulLevel = Pair.of(BalancingEnum.of(pair.getLeft()), pair.getRight());

        splitStrArr = StringUtils.split(map.get("mentalMethod"), "_");
        Map<Integer, Integer> mapTemp = new HashMap<>();
        if(!splitStrArr[1].equals("-1")){
            mapTemp.putAll(StringExtUtil.string2Map(splitStrArr[1], ",", "|", Integer.class, Integer.class));
        }
        this.mentalMethod = Pair.of(BalancingEnum.of(splitStrArr[0]), mapTemp);
    }
    
}

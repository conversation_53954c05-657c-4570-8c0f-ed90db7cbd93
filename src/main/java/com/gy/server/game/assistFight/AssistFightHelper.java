package com.gy.server.game.assistFight;

import java.util.ArrayList;
import java.util.List;

import com.gy.server.common.redis.bean.RedisStringBean;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.game.assistFight.bean.AssistFightHero;
import com.gy.server.game.player.Player;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.serialize.RedisAssistant;

/**
 * 助战辅助类
 * <AUTHOR> - [Created on 2022-11-14 14:41]
 */
public class AssistFightHelper {

    /**
     * 获得助战英雄id
     */
    public static List<Integer> getAssistFightHeroId(long playerId){
        RedisAssistant redisAssistant = TLBase.getInstance().getRedisAssistant();
        List<Integer> result = new ArrayList<>();
        for (RedisStringBean string : redisAssistant.hashGetAllBeans(GsRedisKey.AssistFight.HERO_ID.getRedisKey(playerId), RedisStringBean.class).values()) {
            result.add(Integer.parseInt(string.getParam()));
        }
        return result;
    }

    /**
     * 更新助战英雄id
     */
    public static void updateAssistFightHeroId(long playerId, int index, int heroId){
        RedisAssistant redisAssistant = TLBase.getInstance().getRedisAssistant();
        redisAssistant.hashPut(GsRedisKey.AssistFight.HERO_ID.getRedisKey(playerId), index + "", RedisStringBean.newString(heroId));
    }

    /**
     * 获得助战英雄镜像数据
     */
    public static AssistFightHero getAssistFightHeroInfo(long playerId, int heroId){
        RedisAssistant redisAssistant = TLBase.getInstance().getRedisAssistant();
        return redisAssistant.hashGetBeans(GsRedisKey.AssistFight.HERO_INFO.getRedisKey(playerId), heroId + "", AssistFightHero.class);
    }

    /**
     * 更新英雄助战信息
     * @param playerId
     * @param heroInfo
     */
    public static void updateAssistFightHero(long playerId, AssistFightHero heroInfo){
        RedisAssistant redisAssistant = TLBase.getInstance().getRedisAssistant();
        redisAssistant.hashPut(GsRedisKey.AssistFight.HERO_INFO.getRedisKey(playerId), heroInfo.getHeroId() + "", heroInfo);
    }

    /**
     * 清除英雄助战信息
     */
    public static void clearAssistFightHero(long playerId){
        TLBase.getInstance().getRedisAssistant().del(GsRedisKey.AssistFight.HERO_ID.getRedisKey(playerId));
    }


}

package com.gy.server.game.assistFight.bean;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;
import com.gy.server.game.assistFight.AssistFightService;
import com.gy.server.game.attribute.AttributeSourceType;
import com.gy.server.game.attribute.Attributes;
import com.gy.server.game.combat.StageType;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.combat.unit.HeroUnitCreator;
import com.gy.server.game.combataddition.CombatAdditionHelper;
import com.gy.server.game.dataBalancing.DataBalancingHelper;
import com.gy.server.game.hero.Hero;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.player.Player;
import org.apache.commons.lang3.tuple.Pair;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 助战英雄信息
 *
 * <AUTHOR> - [Created on 2022-11-14 18:12]
 */
@ProtobufClass
public class AssistFightHero {

    /**
     * 英雄id
     */
    @Protobuf(order = 1)
    private int heroId;
    /**
     * 英雄模板id
     */
    @Protobuf(order = 2)
    private int heroTemplateId;
    /**
     * 等级
     */
    @Protobuf(order = 3)
    private int level;
    /**
     * 星级
     */
    @Protobuf(order = 4)
    private int star;

    /**
     * 品质
     */
    @Protobuf(order = 5)
    private int quality;

    /**
     * 属性
     */
    @Protobuf(order = 6)
    private Map<Integer, Attributes> attributesMap = new HashMap<>();

    /**
     * 技能id
     */
    @Protobuf(order = 7)
    private Map<Integer, Integer> skillInfos = new HashMap<>();
    /**
     * 所有buff
     */
    @Protobuf(order = 8)
    private Map<Integer, Integer> buffs = new HashMap<>();

    /**
     * 玩家名字
     */
    @Protobuf(order = 9)
    private String name;

    public int getHeroId() {
        return heroId;
    }

    public void setHeroId(int heroId) {
        this.heroId = heroId;
    }

    public int getHeroTemplateId() {
        return heroTemplateId;
    }

    public void setHeroTemplateId(int heroTemplateId) {
        this.heroTemplateId = heroTemplateId;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public int getStar() {
        return star;
    }

    public void setStar(int star) {
        this.star = star;
    }

    public int getQuality() {
        return quality;
    }

    public void setQuality(int quality) {
        this.quality = quality;
    }

    public Map<Integer, Attributes> getAttributesMap() {
        return attributesMap;
    }

    public void setAttributesMap(Map<Integer, Attributes> attributesMap) {
        this.attributesMap = attributesMap;
    }

    public Map<Integer, Integer> getSkillInfos() {
        return skillInfos;
    }

    public void setSkillInfos(Map<Integer, Integer> skillInfos) {
        this.skillInfos = skillInfos;
    }

    public Map<Integer, Integer> getBuffs() {
        return buffs;
    }

    public void setBuffs(Map<Integer, Integer> buffs) {
        this.buffs = buffs;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public HeroUnit create(StageType stageType, LineupType lineupType, Player player, Hero hero, long belongPlayerId) {
        return createUnitCreate(stageType, lineupType, player, hero, belongPlayerId).create();
    }

    public HeroUnitCreator createUnitCreate(StageType stageType, LineupType lineupType, Player player, Hero hero, long belongPlayerId) {
        Hero tmpHero = new Hero(this, player);
        if (Objects.nonNull(hero)) {
            tmpHero.modifyAttributes(AttributeSourceType.战斗属性培养, CombatAdditionHelper.getAttrs(player, hero, lineupType, stageType).getLeft());
        }
        Integer dataBalanceId = AssistFightService.getBalancingId(player.getLevel());

        HeroUnitCreator heroUnitCreator = DataBalancingHelper.calcHeroBalancing(null, player, tmpHero, dataBalanceId, lineupType);
        heroUnitCreator.setBelongPlayerId(belongPlayerId);
        heroUnitCreator.setPlayerId(player.getPlayerId());
        heroUnitCreator.setInstanceId(this.heroId);
        Pair<Attributes, Long> attributesAndFightingPower = tmpHero.getAttributesAndFightingPower(lineupType, stageType);
        heroUnitCreator.setAttributes(attributesAndFightingPower.getKey());
        heroUnitCreator.setFightingPower(attributesAndFightingPower.getValue());
        return heroUnitCreator;
    }

}

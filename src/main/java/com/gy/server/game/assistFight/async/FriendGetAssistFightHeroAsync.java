package com.gy.server.game.assistFight.async;

import com.gy.server.game.assistFight.AssistFightHelper;
import com.gy.server.game.assistFight.bean.AssistFightHero;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.newFriend.FriendGlobalData;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbHero;
import com.gy.server.packet.PbProtocol;

import java.util.*;

import org.apache.commons.lang3.tuple.Pair;

/**
 * 获得好友助战英雄
 * <AUTHOR> - [Created on 2022-11-15 14:43]
 */
public class FriendGetAssistFightHeroAsync extends AsyncCall {

    Player player;
    PbProtocol.FriendGetAssistFightHeroReq req;
//    List<HeroUnit> friendAssistFightHeroList = new ArrayList<>();
    List<PbHero.AssistFightHero> friendAssistFightHeroList = new ArrayList<>();
    long time;

    public FriendGetAssistFightHeroAsync(Player player, PbProtocol.FriendGetAssistFightHeroReq req, long time){
        this.player = player;
        this.req = req;
        this.time = time;
    }

    @Override
    public void asyncExecute() {
        LineupType type = LineupType.getType(req.getLineupType());
        if(type == null){
            return;
        }

        //好友
        FriendGlobalData globalData = GlobalDataManager.getData(GlobalDataType.friend);
        List<Long> allPlayerIds = new ArrayList<>(globalData.getFriendInfo(player.getPlayerId()).getFriendInfos().keySet());
        //盟友
        League leagueByPlayer = LeagueManager.getLeagueByPlayer(player);
        if(Objects.nonNull(leagueByPlayer)){
            allPlayerIds.addAll(leagueByPlayer.getMemberMap().keySet());
        }

        //获取所有英雄信息
        Map<Integer, List<Pair<String, HeroUnit>>> allHeroUnits = new HashMap<>();
        for (Long playerId : allPlayerIds) {
            List<Integer> assistFightHeroId = AssistFightHelper.getAssistFightHeroId(playerId);
            for (int heroId : assistFightHeroId) {
                if(heroId > 0){
                    AssistFightHero assistFightHeroInfo = AssistFightHelper.getAssistFightHeroInfo(playerId, heroId);
                    if(Objects.nonNull(assistFightHeroInfo)){
                        HeroUnit heroUnit = assistFightHeroInfo.create(type.getStageType(), type, player, player.getBagModel().getHeroByTemplateId(assistFightHeroInfo.getHeroTemplateId()), playerId);
                        if(!allHeroUnits.containsKey(heroUnit.getTid())){
                            allHeroUnits.put(heroUnit.getTid(), new ArrayList<>());
                        }
                        allHeroUnits.get(heroUnit.getTid()).add(Pair.of(assistFightHeroInfo.getName(), heroUnit));
                    }
                }
            }
        }
        //整理英雄信息（每个英雄留一个）
        for (List<Pair<String, HeroUnit>> heroUnits : allHeroUnits.values()) {
            heroUnits.sort((o1, o2) ->  Long.compare(o2.getValue().getFightingPower(), o1.getValue().getFightingPower()));
            Pair<String, HeroUnit> heroInfo = heroUnits.get(0);
            friendAssistFightHeroList.add(parseAssistFightHero(heroInfo.getKey(), heroInfo.getValue()));
        }

    }

    private PbHero.AssistFightHero parseAssistFightHero(String name, HeroUnit heroUnit){
        return  PbHero.AssistFightHero.newBuilder()
                .setHeroId(heroUnit.getHeroId())
                .setHeroTemplateId(heroUnit.getTid())
                .setPlayerId(heroUnit.getBelongPlayerId())
                .setLevel(heroUnit.getLevel())
                .setStar(heroUnit.getStar())
                .setPlayerName(name)
                .setQuality(heroUnit.getQuality())
                .setFightPower(heroUnit.getFightingPower()).build();
    }

    @Override
    public void execute() {
        LineupType type = LineupType.getType(req.getLineupType());
        player.send(PtCode.FRIEND_GET_ASSIST_FIGHT_HERO_RST, PbProtocol.FriendGetAssistFightHeroRst.newBuilder()
                .setResult(Text.genServerRstInfo(type == null ? Text.阵容不存在 : Text.没有异常))
                .addAllHeros(friendAssistFightHeroList)
                .setTimes(player.getAssistFightModel().getAssistNumByLineup(type))
                .setLineupType(req.getLineupType()).build(), time);
    }

}

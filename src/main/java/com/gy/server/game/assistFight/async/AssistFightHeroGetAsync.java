package com.gy.server.game.assistFight.async;

import com.gy.server.game.assistFight.AssistFightHelper;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.newFriend.FriendHelper;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;

import java.util.ArrayList;
import java.util.List;

/**
 * 获得助战英雄
 * <AUTHOR> - [Created on 2022-11-15 14:43]
 */
public class AssistFightHeroGetAsync extends AsyncCall {

    Player player;
    List<Integer> assistFightHeroIds;
    long time;

    public AssistFightHeroGetAsync(Player player, long time){
        this.player = player;
        this.time = time;
    }

    @Override
    public void asyncExecute() {
        this.assistFightHeroIds = new ArrayList<>(AssistFightHelper.getAssistFightHeroId(player.getPlayerId()));
    }

    @Override
    public void execute() {
        PbProtocol.AssistFightHeroGetRst.Builder rst = PbProtocol.AssistFightHeroGetRst.newBuilder().setResult(Text.genServerRstInfo(Text.没有异常));
        rst.addAllHeroIds(assistFightHeroIds);
        List<Reward> rewards = RewardTemplate.createRewards(FriendHelper.助战英雄每次被别人使用奖励());
        int rewardTimes = player.getAssistFightModel().getRewardTimes();
        for (Reward reward : rewards) {
            reward.modifyValueByMultiple(rewardTimes);
        }
        rst.addAllRewards(Reward.writeCollectionToPb(rewards));
        player.send(PtCode.ASSIST_FIGHT_HERO_GET_RST, rst.build(), time);
    }

}

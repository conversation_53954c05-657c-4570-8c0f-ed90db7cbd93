package com.gy.server.game.assistFight.async;

import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.assistFight.PlayerAssistFightModel;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.lineup.bean.LineupInfoBean;
import com.gy.server.game.lineup.bean.MirrorLineupInfoBean;
import com.gy.server.game.lineup.bean.StandHeroBean;
import com.gy.server.game.player.Player;
import com.gy.server.utils.CollectionUtil;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 检查助战次数释放足够
 *
 * <AUTHOR> - [Created on 2023-12-7 18:13]
 */
public class CheckAssistFightNumAsync extends AsyncCall {

    Player player;
    LineupType lineupType;

    List<LineupInfoBean> lineupInfoBeanList;
    List<Long> assistFightPlayerIds;

    public CheckAssistFightNumAsync(Player player, LineupType lineupType, List<Long> assistFightPlayerIds) {
        this.player = player;
        this.lineupType = lineupType;
        this.assistFightPlayerIds = assistFightPlayerIds;
    }

    @Override
    public void asyncExecute() {
        this.lineupInfoBeanList = lineupType.getLineup(player);
    }

    @Override
    public void execute() {
        //发送奖励
        for (Long assistFightPlayerId : assistFightPlayerIds) {
            if(Player.isCurServer(assistFightPlayerId)){
                ThreadPool.execute(new AssistFightAddRewardAsync(assistFightPlayerId));
            }else{
                //发送指令
                ServerCommandRequest request = CommandRequests.newServerCommandRequest("AssistFightCommandService.addAssistFightReward");
                TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, Player.getRealServerId(assistFightPlayerId), request, assistFightPlayerId);
            }
        }
        if (CollectionUtil.isEmpty(lineupInfoBeanList)) {
            return;
        }
        // 过滤掉组队
        if (lineupType.getFormationType().getPlayerNum() > 1) {
            return;
        }
        // 检查本周助战次数
        PlayerAssistFightModel assistFightModel = player.getAssistFightModel();
        if (assistFightModel.assistNumIsNotEnough(lineupType)) {
            for (LineupInfoBean lineupInfoBean : lineupInfoBeanList) {
                Map<Integer, StandHeroBean> standsMap = lineupInfoBean.getStandsMap();
                MirrorLineupInfoBean mirrorInfo = lineupInfoBean.getMirrorInfo();
                for (Map.Entry<Integer, StandHeroBean> entry : new HashSet<>(standsMap.entrySet())) {
                    StandHeroBean standHeroBean = entry.getValue();
                    if (standHeroBean.getBelongPlayerId() != player.getPlayerId()) {
                        standsMap.remove(entry.getKey());
                        if (Objects.nonNull(mirrorInfo)) {
                            mirrorInfo.getMirrorStandsMap().remove(entry.getKey());
                        }
                    }
                }
            }
            // 移除完保存一次
            lineupType.save(player, lineupInfoBeanList);
        }
    }

}

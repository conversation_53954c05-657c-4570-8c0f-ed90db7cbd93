package com.gy.server.game.assistFight.async;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.gy.server.game.assistFight.AssistFightHelper;
import com.gy.server.game.assistFight.bean.AssistFightHero;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.hero.Hero;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbHero;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.CollectionUtil;

/**
 * 助战英雄更新
 * <AUTHOR> - [Created on 2022-11-15 14:43]
 */
public class AssistFightHeroUpdateAsync extends AsyncCall {

    Player player;
    List<Integer> heroIds;
    PbProtocol.AssistFightHeroUpdateRst.Builder builder = PbProtocol.AssistFightHeroUpdateRst.newBuilder();
    long time;

    public AssistFightHeroUpdateAsync(Player player, List<Integer> heroIds, long time){
        this.player = player;
        this.heroIds = heroIds;
        this.time = time;
    }

    @Override
    public void asyncExecute() {
        if(CollectionUtil.containSameElement(heroIds) || heroIds.size() > PbHero.HeroProfession.values().length - 1){
            //数量不对
            builder.setResult(Text.genServerRstInfo(Text.助战英雄错误));
            return ;
        }
        List<Integer> checkHeroIds = new ArrayList<>();
        for (Integer heroId : heroIds) {
            if(heroId > 0){
                if(checkHeroIds.contains(heroId)){
                    builder.setResult(Text.genServerRstInfo(Text.助战英雄错误));
                    return ;
                }
                checkHeroIds.add(heroId);
            }
        }

        List<Integer> professions = new ArrayList<>();
        for (Integer heroId : heroIds) {
            if(heroId > 0){
                Hero heroById = player.getBagModel().getHeroById(heroId);
                if(Objects.isNull(heroById)){
                    //英雄不存在
                    builder.setResult(Text.genServerRstInfo(Text.英雄不存在));
                    return ;
                }
                //检查职业
                PbHero.HeroProfession profession = heroById.getItemTemplate().profession;
                if(professions.contains(profession.getNumber())){
                    builder.setResult(Text.genServerRstInfo(Text.助战英雄不能存在相同职业));
                    return ;
                }
                professions.add(profession.getNumber());
            }
        }
        AssistFightHelper.clearAssistFightHero(player.getPlayerId());
        for (int i = 0; i < heroIds.size(); i++) {
            int heroId = heroIds.get(i);
            AssistFightHelper.updateAssistFightHeroId(player.getPlayerId(), i, heroId);
            if(heroId > 0){
                Hero heroById = player.getBagModel().getHeroById(heroId);
                AssistFightHero assistFightHero = heroById.createAssistFightHero(player.getName());
                //修改助战信息
                AssistFightHelper.updateAssistFightHero(player.getPlayerId(), assistFightHero);
            }
        }
        builder.addAllHeroIds(heroIds);
    }

    @Override
    public void execute() {
        player.send(PtCode.ASSIST_FIGHT_HERO_UPDATE_RST, builder.build(), time);
    }

}

package com.gy.server.game.assistFight;

import com.gy.server.annotation.MessageMethod;
import com.gy.server.annotation.MessageServiceBean;
import com.gy.server.core.MessageServerType;
import com.gy.server.core.MethodInvokeType;
import com.gy.server.core.command.CommandRequestParams;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.assistFight.async.AssistFightAddRewardAsync;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

/**
 * 助战rpc处理
 * @author: gbk
 * @date: 2024-08-09 16:24
 */
@MessageServiceBean(description = "助战rpc处理", messageServerType = MessageServerType.game)
public class AssistFightCommandService {


    @MessageMethod(description = "增加奖励", invokeType = MethodInvokeType.sync)
    private static void addAssistFightReward(ServerCommandRequest request, CommandRequestParams params) {
        long playerId = params.getParam(0);
        ThreadPool.execute(new AssistFightAddRewardAsync(playerId));
    }

}

package com.gy.server.game.assistFight;

import com.gy.server.game.assistFight.async.CheckAssistFightNumAsync;
import com.gy.server.game.combat.unit.Camp;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.combat.unit.TeamUnit;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.log.constant.LogFriendOperationType;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModel;
import com.gy.server.game.player.event.PlayerEvent;
import com.gy.server.game.player.event.PlayerEventHandler;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.utils.StringUtil;
import com.ttlike.server.tl.baselib.serialize.player.AssistFightModelDb;
import com.ttlike.server.tl.baselib.serialize.player.PlayerBlobDb;
import com.ttlike.server.tl.baselib.thread.ThreadPool;
import com.ttlike.server.tl.baselib.util.StringConcat;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static com.gy.server.game.player.event.PlayerEventType.lineupFight;
import static com.gy.server.game.player.event.PlayerEventType.week5Refresh;

/**
 * 助战
 *
 * <AUTHOR> - [Created on 2022-11-14 13:36]
 */
public class PlayerAssistFightModel extends PlayerModel implements PlayerEventHandler {
    private static final PlayerEventType[] eventTypes = new PlayerEventType[]{week5Refresh, lineupFight};

    /**
     * 助战已用次数(周重置)
     */
    private Map<LineupType, Integer> lineupLimitMap = new ConcurrentHashMap<>();

    //奖励次数
    private int rewardTimes;

    public Map<LineupType, Integer> getLineupLimitMap() {
        return lineupLimitMap;
    }

    public void setLineupLimitMap(Map<LineupType, Integer> lineupLimitMap) {
        this.lineupLimitMap = lineupLimitMap;
    }

    public int getRewardTimes() {
        return rewardTimes;
    }

    public void addRewardTimes() {
        rewardTimes++;
    }

    public void setRewardTimes(int rewardTimes) {
        this.rewardTimes = rewardTimes;
    }

    /**
     * 根据布阵类型获得已助战次数
     *
     * @param lineupType 布阵类型
     * @return 已消耗次数
     */
    public int getAssistNumByLineup(LineupType lineupType) {
        return lineupLimitMap.getOrDefault(lineupType, 0);
    }

    /**
     * 本周助战次数是否充足
     */
    public boolean assistNumIsEnough(LineupType lineupType) {
        Map<LineupType, Integer> functionTemplateLimitMap = AssistFightService.getLineupLimitMap();
        if (!functionTemplateLimitMap.containsKey(lineupType)) {
            return false;
        }
        int weekNum = getAssistNumByLineup(lineupType);
        return weekNum < functionTemplateLimitMap.get(lineupType);
    }

    public boolean assistNumIsNotEnough(LineupType lineupType) {
        return !assistNumIsEnough(lineupType);
    }

    public PlayerAssistFightModel(Player player) {
        super(player);
    }

    @Override
    protected void loadData(PlayerBlobDb playerBlob) {
        AssistFightModelDb assistFightModelDb = playerBlob.getAssistFightModelDb();
        if (Objects.nonNull(assistFightModelDb)) {
            Map<Integer, Integer> lineupLimitDbMap = assistFightModelDb.getLineupLimitMap();
            for (Map.Entry<Integer, Integer> entry : lineupLimitDbMap.entrySet()) {
                Integer lineupId = entry.getKey();
                LineupType lineupType = LineupType.getType(lineupId);
                // 已经初始化的不用重复初始化
                if (lineupLimitMap.containsKey(lineupType)) {
                    continue;
                }
                Map<LineupType, List<LineupType>> lineupCollectionMap = AssistFightService.getLineupCollectionMap();
                List<LineupType> lineupList = lineupCollectionMap.get(lineupType);
                for (LineupType initLineup : lineupList) {
                    this.lineupLimitMap.put(initLineup, entry.getValue());
                }
            }
            this.rewardTimes = assistFightModelDb.getRewardTimes();
        }
    }

    @Override
    protected void saveData(PlayerBlobDb playerBlob) {
        AssistFightModelDb assistFightModelDb = new AssistFightModelDb();
        for (Map.Entry<LineupType, Integer> entry : lineupLimitMap.entrySet()) {
            assistFightModelDb.getLineupLimitMap().put(entry.getKey().getType(), entry.getValue());
        }
        assistFightModelDb.setRewardTimes(rewardTimes);
        playerBlob.setAssistFightModelDb(assistFightModelDb);
    }

    @Override
    public PlayerEventType[] getEventTypes() {
        return eventTypes;
    }

    @Override
    public void handle(PlayerEvent event) {
        switch (event.getEventType()) {
            case week5Refresh: {
                lineupLimitMap.clear();
                break;
            }
            case lineupFight: {
                LineupType lineupType = event.getParam(0);
                Camp atk = event.getParam(1);
                Camp def = event.getParam(2);


                if (!AssistFightService.getLineupLimitMap().containsKey(lineupType)) {
                    return ;
                }
                boolean isAssistFight = false;
                List<TeamUnit> teams = atk.getTeams();
                StringConcat assistFight = StringConcat.on(",");
                List<Long> assistFightPlayerIds = new ArrayList<>();
                for (TeamUnit teamUnit : teams) {
                    Collection<HeroUnit> units = teamUnit.getUnits();
                    for (HeroUnit unit : units) {
                        if (unit.getPlayerId() == getPlayerId() && unit.getBelongPlayerId() != getPlayerId()) {
                            isAssistFight = true;
                            assistFight.add(unit.getBelongPlayerId());
                            assistFightPlayerIds.add(unit.getBelongPlayerId());
                        }
                    }
                }
                if(!isAssistFight){
                    return;
                }
                Integer value = this.lineupLimitMap.get(lineupType);
                // 初始化
                if (value == null) {
                    value = 0;
                    Map<LineupType, List<LineupType>> lineupCollectionMap = AssistFightService.getLineupCollectionMap();
                    List<LineupType> lineupList = lineupCollectionMap.get(lineupType);
                    for (LineupType initLineup : lineupList) {
                        this.lineupLimitMap.put(initLineup, value);
                    }
                }
                // 增加一次次数
//                if (value < lineupLimitMap.get(lineupType)) {
                    lineupLimitMap.put(lineupType, value + 1);
//                }
                ThreadPool.execute(new CheckAssistFightNumAsync(getPlayer(), lineupType, assistFightPlayerIds));
                String targetPlayerId = assistFight.toString();
                //日志
                GameLogger.friend(getPlayer(), LogFriendOperationType.AssistFighting, StringUtil.splitToLongArray(targetPlayerId, ","));
            }
            default: {
                break;
            }
        }
    }
}

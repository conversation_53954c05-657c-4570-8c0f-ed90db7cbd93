package com.gy.server.game.assistFight.async;

import com.gy.server.game.assistFight.PlayerAssistFightModel;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;

/**
 * 增加助战奖励
 * @author: gbk
 * @date: 2024-08-09 17:22
 */
public class AssistFightAddRewardAsync extends AsyncCall {
    long playerId;
    Player player;

    public AssistFightAddRewardAsync(long playerId){
        this.playerId = playerId;
    }

    @Override
    public void asyncExecute() {
        player = PlayerManager.getPlayer(playerId);
    }

    @Override
    public void execute() {
        PlayerAssistFightModel assistFightModel = player.getAssistFightModel();
        assistFightModel.addRewardTimes();
        if(!PlayerManager.isOnline(playerId)){
            player.saveOnce();
        }
    }
}
package com.gy.server.game.assistFight;

import com.google.common.collect.Sets;
import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.assistFight.async.AssistFightHeroGetAsync;
import com.gy.server.game.assistFight.async.AssistFightHeroUpdateAsync;
import com.gy.server.game.assistFight.async.FriendGetAssistFightHeroAsync;
import com.gy.server.game.constant.ConstantService;
import com.gy.server.game.constant.ConstantType;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.lineup.LineupService;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.newFriend.FriendHelper;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.service.Service;
import com.gy.server.game.text.Text;
import com.gy.server.game.util.StringExtUtil;
import com.gy.server.packet.PbProtocol;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.*;

/**
 * 助战
 *
 * <AUTHOR> - [Created on 2022-11-15 14:35]
 */
public class AssistFightService extends PlayerPacketHandler implements Service {

    /**
     * 助战配置
     * key：主角等级
     * value：拉平ID
     */
    private static Map<Integer, Integer> assistFightingMap = new HashMap<>();

    /**
     * Key 对应布阵类型 Value:可用助战次数上限
     */
    private static Map<LineupType, Integer> lineupLimitMap = new HashMap<>();
    /**
     * Key 对应布阵类型 Value:对应布阵集合 比如多个功能公用一个次数
     */
    private static Map<LineupType, List<LineupType>> lineupCollectionMap = new HashMap<>();

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.assistFighting_assistFighting);
        Map<Integer, Integer> assistFightingMapTemp = new HashMap();
        for (Map<String, String> map : mapList) {
            assistFightingMapTemp.put(Integer.parseInt(map.get("level")), Integer.parseInt(map.get("dataBalancingId")));
        }
        assistFightingMap = assistFightingMapTemp;

        String string = ConstantService.getString(ConstantType.可使用助战功能及每周次数上限);
        String[] strBigArr = string.split(",");
        Map<LineupType, Integer> lineupLimitMapTemp = new HashMap();
        Map<LineupType, List<LineupType>> lineupCollectionMapTemp = new HashMap();
        for (String strBig : strBigArr) {
            String[] strMiddleArr = strBig.split("[|]");
            List<LineupType> lineupList = new ArrayList<>();
            List<Integer> lineupIdList = StringExtUtil.string2List(strMiddleArr[0], "_", Integer.class);
            for (Integer lineupId : lineupIdList) {
                LineupType lineupType = LineupType.getType(lineupId);
                if (Objects.nonNull(lineupType)) {
                    lineupList.add(lineupType);
                    lineupLimitMapTemp.put(lineupType, Integer.parseInt(strMiddleArr[1]));
                }
            }
            for (LineupType lineupType : lineupList) {
                lineupCollectionMapTemp.put(lineupType, lineupList);
            }
        }
        lineupLimitMap = lineupLimitMapTemp;
        lineupCollectionMap = lineupCollectionMapTemp;
    }

    public static Map<LineupType, Integer> getLineupLimitMap() {
        return lineupLimitMap;
    }

    public static Map<LineupType, List<LineupType>> getLineupCollectionMap() {
        return lineupCollectionMap;
    }

    @SuppressWarnings("unchecked")
    @Override
    public Set<Class<? extends Service>> preServices() {
        return Sets.newHashSet(ConstantService.class, LineupService.class);
    }

    public static Integer getBalancingId(int level) {
        Integer maxLevel = Collections.max(assistFightingMap.keySet());
        level = Integer.min(maxLevel, level);
        return assistFightingMap.get(level);
    }

    @Override
    public void clearConfigData() {
        assistFightingMap.clear();
        lineupLimitMap.clear();
        lineupCollectionMap.clear();
    }

    /**
     * 拉取好友助战英雄
     */
    @Handler(PtCode.FRIEND_GET_ASSIST_FIGHT_HERO_REQ)
    private void getFriendAll(Player player, PbProtocol.FriendGetAssistFightHeroReq req, long time) {
        ThreadPool.execute(new FriendGetAssistFightHeroAsync(player, req, time));
    }

    /**
     * 修改助战英雄
     */
    @Handler(PtCode.ASSIST_FIGHT_HERO_UPDATE_REQ)
    private void update(Player player, PbProtocol.AssistFightHeroUpdateReq req, long time) {
        ThreadPool.execute(new AssistFightHeroUpdateAsync(player, req.getHeroIdsList(), time));
    }

    /**
     * 获得助战英雄
     */
    @Handler(PtCode.ASSIST_FIGHT_HERO_GET_REQ)
    private void get(Player player, PbProtocol.AssistFightHeroGetReq req, long time) {
        ThreadPool.execute(new AssistFightHeroGetAsync(player, time));
    }

    @Handler(PtCode.ASSIST_FIGHT_GET_REWARD_REQ)
    private void addReward(Player player, PbProtocol.AssistFightGetRewardReq req, long time) {
        PbProtocol.AssistFightGetRewardRst.Builder rst = PbProtocol.AssistFightGetRewardRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            List<Reward> rewards = RewardTemplate.createRewards(FriendHelper.助战英雄每次被别人使用奖励());
            PlayerAssistFightModel assistFightModel = player.getAssistFightModel();
            int rewardTimes = assistFightModel.getRewardTimes();
            if(rewardTimes <= 0){
                rst.setResult(Text.genServerRstInfo(Text.奖励已领取过));
                break logic;
            }
            for (Reward reward : rewards) {
                reward.modifyValueByMultiple(rewardTimes);
            }
            assistFightModel.setRewardTimes(0);
            Reward.add(rewards, player, BehaviorType.GmCommand);
        }
        player.send(PtCode.ASSIST_FIGHT_GET_REWARD_RST, rst.build(), time);
    }

}

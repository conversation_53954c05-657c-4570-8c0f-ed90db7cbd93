package com.gy.server.game.divination.template;

import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.liberty.effect.LibertyTemplate;

import java.util.List;
import java.util.Map;

/**
 * 占卜表
 *
 * <AUTHOR> - [Created on 2023/11/3 15:15]
 */
public class DivinationListTemplate {

    public final int id;

    /**
     * 占卜池id
     */
    public int bank;

    /**
     * 加成权益信息
     */
    public final List<LibertyTemplate> effectTypeList;

    /**
     * 签文类型
     */
    public final int type;

    /**
     * 随机权重
     */
    public final int randomWeight;

    /**
     * function表id
     */
    public final int function;

    /**
     * 单次占卜奖励
     */
    public final List<RewardTemplate> reward;


    public DivinationListTemplate(Map<String, String> map) {
        this.id = Integer.parseInt(map.get("id"));
        this.bank = Integer.parseInt(map.get("bank"));
        this.effectTypeList = LibertyTemplate.readLibertyListFromText(map.get("effectype"));
        this.type = Integer.parseInt(map.get("type"));
        this.randomWeight = Integer.parseInt(map.get("randomWeight"));
        this.function = Integer.parseInt(map.get("function"));
        this.reward = RewardTemplate.readListFromText(map.get("reward"));
    }
}

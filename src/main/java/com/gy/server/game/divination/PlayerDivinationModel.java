package com.gy.server.game.divination;

import com.gy.server.game.divination.template.DivinationListTemplate;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.liberty.PlayerLibertyModel;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModel;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.game.player.event.PlayerEvent;
import com.gy.server.game.player.event.PlayerEventHandler;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.reddot.RedDot;
import com.gy.server.packet.PbDivination;
import com.gy.server.packet.PbLiberty;
import com.gy.server.utils.CollectionUtil;
import com.ttlike.server.tl.baselib.serialize.divination.PlayerDivinationModelDb;
import com.ttlike.server.tl.baselib.serialize.player.PlayerBlobDb;

import java.util.*;

/**
 * 占卜玩家数据 
 *
 * <AUTHOR> - [Created on 2023/11/3 15:42]
 */
public class PlayerDivinationModel extends PlayerModel implements PlayerEventHandler {

    /**
     * 累计摇中次数
     * Key:签ID  value:累计次数
     */
    private Map<Integer, Integer> countMap = new HashMap<>();
    /**
     * 今日是否领取五连奖励
     */
    private boolean hasReward5;
    /**
     * 今日摇签次数
     */
    private int hasCount;
    /**
     * 当前生效的特权UID
     */
    private List<String> libertyUid = new ArrayList<>();
    /**
     * 当前签ID
     */
    private int curDivinationId;
    /**
     * 待替换的签ID
     */
    private int changeDivinationId;

    /**
     * 占卜次数
     */
    private int useNum;

    public PlayerDivinationModel(Player player) {
        super(player);
    }

    @Override
    protected void loadData(PlayerBlobDb playerBlob) {
        PlayerDivinationModelDb divinationModelDb = playerBlob.getDivinationModelDb();
        if (Objects.nonNull(divinationModelDb)) {
            this.countMap.putAll(divinationModelDb.getCountMap());
            this.hasReward5 = divinationModelDb.isHasReward5();
            this.hasCount = divinationModelDb.getHasCount();
            this.libertyUid = divinationModelDb.getLibertyUid();
            this.curDivinationId = divinationModelDb.getCurDivinationId();
            this.changeDivinationId = divinationModelDb.getChangeDivinationId();
            this.useNum = divinationModelDb.getUseNum();
        }
    }

    @Override
    protected void saveData(PlayerBlobDb playerBlob) {
        PlayerDivinationModelDb db = new PlayerDivinationModelDb();
        db.getCountMap().putAll(countMap);
        db.setHasReward5(hasReward5);
        db.setHasCount(hasCount);
        db.setLibertyUid(libertyUid);
        db.setCurDivinationId(curDivinationId);
        db.setChangeDivinationId(changeDivinationId);
        db.setUseNum(useNum);
        playerBlob.setDivinationModelDb(db);
    }

    @Override
    public PlayerEventType[] getEventTypes() {
        return new PlayerEventType[]{
                PlayerEventType.day5Refresh,
                PlayerEventType.day0Refresh,
        };
    }

    @Override
    public void handle(PlayerEvent event) {
        switch (event.getEventType()) {
            case day0Refresh: {
                day5Refresh();
                break;
            }
        }
    }

    /**
     * 发奖逻辑
     *
     * @param divinationId 签ID
     */
    public List<Reward> drawReward(int divinationId, BehaviorType behaviorType) {
        DivinationListTemplate divinationListTemplate = DivinationService.getDivinationListTemplateMap().get(divinationId);
        List<Reward> rewardList = new ArrayList<>();
        rewardList.addAll(Reward.templateCollectionToReward(divinationListTemplate.reward));

        int count = countMap.getOrDefault(divinationId, 0);
        Reward reward = DivinationService.getConstant().getXLinkReward(count);
        if(reward != null){
            rewardList.add(reward);
        }

        if(count == 5){
            // 清空五连的签次数
            countMap.remove(divinationId);
        }

        if(CollectionUtil.isNotEmpty(rewardList)){
            Reward.merge(rewardList);
            Reward.add(rewardList, getPlayer(), behaviorType);
        }
        return rewardList;
    }

    public PbDivination.DivinationInfo genCurDivinationInfoPb() {
        PbDivination.DivinationInfo.Builder builder = PbDivination.DivinationInfo.newBuilder();
        if(curDivinationId != 0){
            PlayerLibertyModel libertyModel = getPlayer().getModel(PlayerModelEnums.libertyModel);
            List<PbLiberty.LibertyInfo> libertyInfos = libertyModel.genLibertyListByUidPb(libertyUid);
            if(!libertyInfos.isEmpty()){
                builder.addAllLibertyInfo(libertyInfos);
                builder.setId(curDivinationId).setNum(countMap.getOrDefault(curDivinationId, 0));
            }else {
                //
                libertyUid.clear();
                curDivinationId = 0;
            }
        }
        return builder.build();
    }

    public PbDivination.DivinationInfo genChangeDivinationInfoPb() {
        PbDivination.DivinationInfo.Builder builder = PbDivination.DivinationInfo.newBuilder()
                .setId(changeDivinationId)
                .setNum(countMap.getOrDefault(changeDivinationId, 0));
        DivinationListTemplate divinationListTemplate = DivinationService.getDivinationListTemplateMap().get(changeDivinationId);
        PlayerLibertyModel libertyModel = getPlayer().getModel(PlayerModelEnums.libertyModel);
        List<PbLiberty.LibertyInfo> libertyInfos = libertyModel.genLibertyListByTemplatePb(divinationListTemplate.effectTypeList);
        builder.addAllLibertyInfo(libertyInfos);
        return builder.build();
    }

    public void day5Refresh() {
        this.hasReward5 = false;
        this.hasCount = 0;
        this.libertyUid.clear();
        this.curDivinationId = 0;
        this.changeDivinationId = 0;
        this.countMap.clear();

        //同步红点
        RedDot.DivinationFreeTime.sync(getPlayer());
    }

    public void addUseNum() {
        this.useNum++;
    }

    public Map<Integer, Integer> getCountMap() {
        return countMap;
    }

    public void setCountMap(Map<Integer, Integer> countMap) {
        this.countMap = countMap;
    }

    public boolean isHasReward5() {
        return hasReward5;
    }

    public void setHasReward5(boolean hasReward5) {
        this.hasReward5 = hasReward5;
    }

    public int getHasCount() {
        return hasCount;
    }

    public void setHasCount(int hasCount) {
        this.hasCount = hasCount;
    }

    public List<String> getLibertyUid() {
        return libertyUid;
    }

    public void setLibertyUid(List<String> libertyUid) {
        this.libertyUid = libertyUid;
    }

    public int getCurDivinationId() {
        return curDivinationId;
    }

    public void setCurDivinationId(int curDivinationId) {
        this.curDivinationId = curDivinationId;
    }

    public int getChangeDivinationId() {
        return changeDivinationId;
    }

    public void setChangeDivinationId(int changeDivinationId) {
        this.changeDivinationId = changeDivinationId;
    }

    public int getUseNum() {
        return useNum;
    }

    public void setUseNum(int useNum) {
        this.useNum = useNum;
    }

}

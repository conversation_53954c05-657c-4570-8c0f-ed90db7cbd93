package com.gy.server.game.divination;

import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.common.ConstantConfigReader;
import com.gy.server.game.divination.template.DivinationConst;
import com.gy.server.game.divination.template.DivinationListTemplate;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.function.Function;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.liberty.PlayerLibertyModel;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.reddot.RedDot;
import com.gy.server.game.service.Service;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.MathUtil;

import java.util.*;

/**
 * 占卜
 *
 * <AUTHOR> - [Created on 2023/11/3 14:44]
 */
public class DivinationService extends PlayerPacketHandler implements Service {

    /**
     * 常量表
     */
    private static DivinationConst constant;

    /**
     * 占卜表
     */
    private static Map<Integer, DivinationListTemplate> divinationListTemplateMap = new HashMap<>();

    /**
     * 占卜随机池map
     * key:占卜池id    value:占卜id集合
     */
    private static Map<Integer, Set<Integer>> randomPoolMap = new HashMap<>();

    /**
     * 占卜信息
     */
    @Handler(PtCode.DIVINATION_INFO_CLIENT)
    private void info(Player player, long time) {
        PbProtocol.DivinationInfoRst.Builder rst = PbProtocol.DivinationInfoRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:
        {
            if (!Function.divination.isOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            PlayerDivinationModel divinationModel = player.getModel(PlayerModelEnums.divination);
            boolean isChange = divinationModel.getChangeDivinationId() != 0;
            rst.putAllDivinationInfo(divinationModel.getCountMap())
                    .setTodayNum(divinationModel.getHasCount())
                    .setCurInfo(divinationModel.genCurDivinationInfoPb())
                    .setIsChange(isChange);
            if (isChange) {
                rst.setChangeInfo(divinationModel.genChangeDivinationInfoPb());
            }

        }
        player.send(PtCode.DIVINATION_INFO_SERVER, rst.build(), time);
    }

    /**
     * 占卜
     */
    @Handler(PtCode.DIVINATION_SHAKE_CLIENT)
    private void shake(Player player, long time) throws Exception {
        PbProtocol.DivinationShakeRst.Builder rst = PbProtocol.DivinationShakeRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:
        {
            if (!Function.divination.isOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            PlayerDivinationModel divinationModel = player.getModel(PlayerModelEnums.divination);
            int changeDivinationId = divinationModel.getChangeDivinationId();
            if (changeDivinationId > 0) {
                rst.setResult(Text.genServerRstInfo(Text.请先更换或者放弃再占卜));
                break logic;
            }

            SortedMap<Integer, Integer> sortedMap = constant.randomDivinatioBank.tailMap(divinationModel.getUseNum());
            int randomPoolId = CollectionUtil.isEmpty(sortedMap) ? constant.randomDivinatioBank.lastEntry().getValue() : constant.randomDivinatioBank.get(sortedMap.firstKey());
            int nextNum = divinationModel.getHasCount() + 1;    //新占卜是今天第多少次
            RewardTemplate costRewardTemplate = constant.consumptionItem.get(constant.consumptionItem.size() >= nextNum ? nextNum - 1 : Math.max(constant.consumptionItem.size() - 1, 0));
            if(Objects.nonNull(costRewardTemplate)){
                Reward cost = costRewardTemplate.createReward();
                if (cost.check(player) != -1) {
                    rst.setResult(Text.genServerRstInfo(Text.消耗不足));
                    break logic;
                }
                cost.remove(player, BehaviorType.divinationCost);
            }

            divinationModel.setHasCount(nextNum);
            divinationModel.addUseNum();

            //处理随机池内容
            Map<DivinationListTemplate, Integer> weightMap = new HashMap<>();
            Set<Integer> set = randomPoolMap.get(randomPoolId);
            for (int id : set) {
                DivinationListTemplate template = divinationListTemplateMap.get(id);
                if(template.function > 0){
                    Function function = Function.getFunctionById(template.function);
                    if(function == null || (function != null && !function.isOpen(player))){
                        continue;
                    }
                }
                weightMap.put(template, template.randomWeight);
            }

            if(weightMap.isEmpty()){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            DivinationListTemplate divinationListTemplate = MathUtil.weightRandom(weightMap);

            if (divinationModel.getCurDivinationId() > 0) {
                //当前有签
                rst.setIsChange(true);
                divinationModel.setChangeDivinationId(divinationListTemplate.id);
                rst.setInfo(divinationModel.genChangeDivinationInfoPb());
            } else {
                //当前无签
                divinationModel.getCountMap().merge(divinationListTemplate.id, 1, Integer::sum);

                divinationModel.setCurDivinationId(divinationListTemplate.id);
                PlayerLibertyModel playerLibertyModel = player.getModel(PlayerModelEnums.libertyModel);
                // 特权生效
                List<String> effectIdList = playerLibertyModel.addEffectValue(divinationListTemplate.effectTypeList);
                divinationModel.setLibertyUid(effectIdList);
                rst.setInfo(divinationModel.genCurDivinationInfoPb());

                //随机出现算x连奖励
                List<Reward> rewards = divinationModel.drawReward(divinationListTemplate.id, BehaviorType.divinationAdd);
                rst.addAllRewards(Reward.writeCollectionToPb(rewards));
            }

            rst.putAllDivinationInfo(divinationModel.getCountMap());

            //同步
            RedDot.DivinationFreeTime.sync(player);
            //红点
            player.postEvent(PlayerEventType.divinationUse);
        }
        player.send(PtCode.DIVINATION_SHAKE_SERVER, rst.build(), time);
    }

    /**
     * 替换或者放弃占卜
     */
    @Handler(PtCode.DIVINATION_CHANGE_CLIENT)
    private void change(Player player, PbProtocol.DivinationChangeReq req, long time) throws Exception {
        PbProtocol.DivinationChangeRst.Builder rst = PbProtocol.DivinationChangeRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:
        {
            int optType = req.getOptType();
            if (optType != 1 && optType != 2) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }

            if (!Function.divination.isOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }

            PlayerDivinationModel divinationModel = player.getModel(PlayerModelEnums.divination);
            int changeDivinationId = divinationModel.getChangeDivinationId();
            if (changeDivinationId <= 0) {
                rst.setResult(Text.genServerRstInfo(Text.没有可替换的签));
                break logic;
            }

            DivinationListTemplate divinationListTemplate = divinationListTemplateMap.get(changeDivinationId);

            // 替换
            if (optType == 1) {
//                DivinationListTemplate divinationListTemplate = divinationListTemplateMap.get(changeDivinationId);
                PlayerLibertyModel playerLibertyModel = player.getModel(PlayerModelEnums.libertyModel);
                //移除已经生效的特权
                playerLibertyModel.removeUid(divinationModel.getLibertyUid());
                // 特权生效
                List<String> effectIdList = playerLibertyModel.addEffectValue(divinationListTemplate.effectTypeList);
                divinationModel.setLibertyUid(effectIdList);
                divinationModel.setCurDivinationId(changeDivinationId);
                rst.setInfo(divinationModel.genCurDivinationInfoPb());
            }

            divinationModel.getCountMap().merge(divinationListTemplate.id, 1, Integer::sum);

            //随机出现算x连奖励
            List<Reward> rewards = divinationModel.drawReward(divinationListTemplate.id, BehaviorType.divinationAdd);
            rst.addAllRewards(Reward.writeCollectionToPb(rewards));

            divinationModel.setChangeDivinationId(0);
            rst.setOptType(optType);
            rst.putAllDivinationInfo(divinationModel.getCountMap());
        }
        player.send(PtCode.DIVINATION_CHANGE_SERVER, rst.build(), time);
    }

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.divination_divinationList);
        Map<Integer, DivinationListTemplate> divinationListTemplateMapTemp = new HashMap();
        Map<DivinationListTemplate, Integer> weightMapTemp = new HashMap();
        Map<Integer, Set<Integer>> randomPoolMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            DivinationListTemplate template = new DivinationListTemplate(map);
            divinationListTemplateMapTemp.put(template.id, template);
            weightMapTemp.put(template, template.randomWeight);
            randomPoolMapTemp.computeIfAbsent(template.bank, set -> new HashSet<>()).add(template.id);
        }
        divinationListTemplateMap = divinationListTemplateMapTemp;
        randomPoolMap = randomPoolMapTemp;

        Map<String, String> map = ConstantConfigReader.read(ConfigFile.divination_const);
        constant = new DivinationConst(map);

        //检查随机池
        if(!randomPoolMap.keySet().containsAll(constant.randomDivinatioBank.values())){
            throw new IllegalArgumentException("divination random pool is error, " + randomPoolMapTemp.keySet() + "     " + constant.randomDivinatioBank.values());
        }
    }

    @Override
    public void clearConfigData() {
        divinationListTemplateMap.clear();
        randomPoolMap.clear();
    }


    public static Map<Integer, DivinationListTemplate> getDivinationListTemplateMap() {
        return divinationListTemplateMap;
    }

    public static DivinationConst getConstant() {
        return constant;
    }

    public static Map<Integer, Set<Integer>> getRandomPoolMap() {
        return randomPoolMap;
    }

}

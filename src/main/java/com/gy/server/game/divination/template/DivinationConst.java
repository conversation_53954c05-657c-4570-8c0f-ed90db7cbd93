package com.gy.server.game.divination.template;

import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.util.StringExtUtil;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.MathUtil;

import java.util.*;

/**
 * 占卜常量表
 *
 * <AUTHOR> - [Created on 2023/11/3 15:11]
 */
public class DivinationConst {
    /**
     * 占卜消耗道具
     */
    public final List<RewardTemplate> consumptionItem;

    private String continuous3ExtraReward;
    private String continuous4ExtraReward;
    private String continuous5ExtraReward;

    /**
     * x连奖励权重map
     * key:奖励   value:权重
     */
    public Map<RewardTemplate, Integer> weight3Map = new HashMap<>();
    public Map<RewardTemplate, Integer> weight4Map = new HashMap<>();
    public Map<RewardTemplate, Integer> weight5Map = new HashMap<>();

    /**
     * 随机数对应池库（次数，占卜池id  对应divinationList页签bank字段）
     * key:次数   value:占卜池id
     */
    public TreeMap<Integer, Integer> randomDivinatioBank = new TreeMap<>();

    public DivinationConst(Map<String, String> map) {
        this.consumptionItem = RewardTemplate.readListFromText(map.get("consumptionItem"));
        this.continuous3ExtraReward = map.get("continuous3ExtraReward");
        this.continuous4ExtraReward = map.get("continuous4ExtraReward");
        this.continuous5ExtraReward = map.get("continuous5ExtraReward");
        this.weight3Map = StringExtUtil.string2Map(continuous3ExtraReward, ",", "_", RewardTemplate.class, Integer.class);
        this.weight4Map = StringExtUtil.string2Map(continuous4ExtraReward, ",", "_", RewardTemplate.class, Integer.class);
        this.weight5Map = StringExtUtil.string2Map(continuous5ExtraReward, ",", "_", RewardTemplate.class, Integer.class);
        this.randomDivinatioBank.putAll(StringExtUtil.string2Map(map.get("randomDivinatioBank"), ",", "|", Integer.class, Integer.class));
    }

    /**
     * 获取x连奖励
     * @return
     */
    public Reward getXLinkReward(int count){
        Map<RewardTemplate, Integer> weightMap = null;
        switch (count){
            case 3:{
                weightMap = weight3Map;
                break;
            }
            case 4:{
                weightMap = weight4Map;
                break;
            }
            case 5:{
                weightMap = weight5Map;
                break;
            }
        }

        if(CollectionUtil.isNotEmpty(weightMap)){
            RewardTemplate rewardTemplate = RewardTemplate.readListFromText(String.valueOf(MathUtil.weightRandom(weightMap))).get(0);
            return rewardTemplate.createReward();
        }

        return null;
    }
}

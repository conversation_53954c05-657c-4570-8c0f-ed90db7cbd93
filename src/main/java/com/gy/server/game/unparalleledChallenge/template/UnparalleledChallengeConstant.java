package com.gy.server.game.unparalleledChallenge.template;

import com.gy.server.common.base.AbsTemplate;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.utils.StringUtil;
import com.gy.server.utils.time.DateTimeUtil;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 群龙聚首-常量
 * @author: gbk
 * @date: 2024-12-03 10:56
 */
public class UnparalleledChallengeConstant implements AbsTemplate {

    //参赛资格：剑会群雄排名前X
    public int eligibility;
    //小组赛锁定阵容时间
    public String lockedTimes_1;
    //小组赛查对战时间
    public String viewableTime_1;
    //淘汰赛锁定阵容时间
    public String lockedTimes_2;
    //淘汰赛查看对战时间
    public String viewableTime_2;
    //竞猜开始时间
    public String guessingTime_1;
    //竞猜结束时间
    public String guessingTime_2;

    //竞猜赔率
    public String guessingOdds;
    //竞猜代币消耗
    public List<RewardTemplate> guessingCost;
    //竞猜成功代币转换（1代币=）
    public List<RewardTemplate> guessing2Rewards;
    //竞猜成功额外奖励（可无）
    public List<RewardTemplate> guessingRewards_1;
    //竞猜失败安慰奖（可无）
    public List<RewardTemplate> guessingRewards_2;
    //全猜中额外奖励
    public List<RewardTemplate> guessingRewards_3;


    @Override
    public void readTxt(Map<String, String> map) {
        eligibility = Integer.parseInt(map.get("eligibility"));
        lockedTimes_1 = map.get("lockedTimes_1");
        viewableTime_1 = map.get("viewableTime_1");
        lockedTimes_2 = map.get("lockedTimes_2");
        viewableTime_2 = map.get("viewableTime_2");
        guessingTime_1 = map.get("guessingTime_1");
        guessingTime_2 = map.get("guessingTime_2");
        guessingOdds = map.get("guessingOdds");
        guessingCost = RewardTemplate.readListFromText(map.get("guessingCost"));
        guessing2Rewards = RewardTemplate.readListFromText(map.get("guessing2Rewards"));
        guessingRewards_1 = RewardTemplate.readListFromText(map.get("guessingRewards_1"));
        guessingRewards_2 = RewardTemplate.readListFromText(map.get("guessingRewards_2"));
        guessingRewards_3 = RewardTemplate.readListFromText(map.get("guessingRewards_3"));
    }

    public long modifyTime(LocalDateTime nowDateTime, String time, boolean isNextDay){
        int[] ints = StringUtil.splitToIntArray(time, "\\|");
        if(isNextDay){
            return DateTimeUtil.toMillis(nowDateTime.plusDays(1L).withHour(ints[0]).withMinute(ints[1]).withSecond(0).withNano(0));
        }else{
            return DateTimeUtil.toMillis(nowDateTime.withHour(ints[0]).withMinute(ints[1]).withSecond(0).withNano(0));
        }

    }

}
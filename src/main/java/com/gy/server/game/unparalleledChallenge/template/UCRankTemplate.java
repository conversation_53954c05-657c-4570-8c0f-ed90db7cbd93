package com.gy.server.game.unparalleledChallenge.template;

import com.gy.server.common.base.AbsTemplate;
import com.gy.server.game.drop.RewardTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 排名奖励
 * @author: gbk
 * @date: 2025-01-09 10:21
 */
public class UCRankTemplate implements AbsTemplate {

    //最小排名
    public int minRank;
    //最大排名
    public int maxRank;
    //奖励
    public List<RewardTemplate> rewardList = new ArrayList<>();
    //帮派额外奖励
    public List<RewardTemplate> leagueExtraRewardList = new ArrayList<>();

    @Override
    public void readTxt(Map<String, String> map) {
        String[] split = map.get("ranking").split(",");
        if(split.length == 1){
            minRank = Integer.parseInt(split[0]);
            maxRank = minRank;
        }else{
            minRank = Integer.parseInt(split[0]);
            maxRank = Integer.parseInt(split[1]);
        }
        rewardList = RewardTemplate.readListFromText(map.get("rewards"));
        leagueExtraRewardList = RewardTemplate.readListFromText(map.get("extraRewards"));
    }
}
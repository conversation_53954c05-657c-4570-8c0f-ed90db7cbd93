package com.gy.server.game.unparalleledChallenge.template;

import com.gy.server.common.base.AbsTemplate;
import com.gy.server.game.drop.RewardTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 头像信息
 * @author: gbk
 * @date: 2025-01-09 10:21
 */
public class UCRahmensTemplate implements AbsTemplate {


    public int goalId;
    //头像框
    public int unlockRahmen;

    @Override
    public void readTxt(Map<String, String> map) {
        goalId = Integer.parseInt(map.get("goal"));
        unlockRahmen = Integer.parseInt(map.get("unlockRahmen"));
    }
}
package com.gy.server.game.unparalleledChallenge;

import com.google.protobuf.InvalidProtocolBufferException;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.callback.response.CallbackResponse;
import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.common.ConstantConfigReader;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.service.Service;
import com.gy.server.game.text.Text;
import com.gy.server.game.unparalleledChallenge.bean.UCBetAsyncCall;
import com.gy.server.game.unparalleledChallenge.bean.UCGetBetAsyncCall;
import com.gy.server.game.unparalleledChallenge.bean.UCGetMainAsyncCall;
import com.gy.server.game.unparalleledChallenge.template.UCRahmensTemplate;
import com.gy.server.game.unparalleledChallenge.template.UCRankTemplate;
import com.gy.server.game.unparalleledChallenge.template.UnparalleledChallengeConstant;
import com.gy.server.game.unparalleledChallenge.template.UnparalleledChallengeSeasonTemplate;
import com.gy.server.game.world.async.WorldMessageCallBackAsync;
import com.gy.server.packet.PbProtocol;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * PVP群龙聚首
 * @author: gbk
 * @date: 2024-12-02 17:33
 */
public class UnparalleledChallengeService extends PlayerPacketHandler implements Service {

    private static UnparalleledChallengeConstant constant;
    private static Map<Integer, UnparalleledChallengeSeasonTemplate> seasonTemplates = new HashMap<>();
    private static List<UCRahmensTemplate> rahmensTemplates = new ArrayList<>();
    private static List<UCRankTemplate> rankTemplates = new ArrayList<>();

    public static UnparalleledChallengeConstant getConstant() {
        return constant;
    }

    public static Map<Integer, UnparalleledChallengeSeasonTemplate> getSeasonTemplates() {
        return seasonTemplates;
    }

    public static void setSeasonTemplates(Map<Integer, UnparalleledChallengeSeasonTemplate> seasonTemplates) {
        UnparalleledChallengeService.seasonTemplates = seasonTemplates;
    }

    public static void setConstant(UnparalleledChallengeConstant constant) {
        UnparalleledChallengeService.constant = constant;
    }

    public static List<UCRahmensTemplate> getRahmensTemplates() {
        return rahmensTemplates;
    }

    public static void setRahmensTemplates(List<UCRahmensTemplate> rahmensTemplates) {
        UnparalleledChallengeService.rahmensTemplates = rahmensTemplates;
    }

    public static List<UCRankTemplate> getRankTemplates() {
        return rankTemplates;
    }

    public static void setRankTemplates(List<UCRankTemplate> rankTemplates) {
        UnparalleledChallengeService.rankTemplates = rankTemplates;
    }

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.unparalleledChallenge_season);
        Map<Integer, UnparalleledChallengeSeasonTemplate> seasonTemplatesList = new HashMap<>();
        for (Map<String, String> map : mapList) {
            UnparalleledChallengeSeasonTemplate seasonTemplate = new UnparalleledChallengeSeasonTemplate();
            seasonTemplate.readTxt(map);
            seasonTemplatesList.put(seasonTemplate.seasonId, seasonTemplate);
        }
        seasonTemplates = seasonTemplatesList;
        List<UCRankTemplate> rankTemplatesList = new ArrayList<>();
        mapList = ConfigReader.read(ConfigFile.unparalleledChallenge_rankRewards);
        for (Map<String, String> map : mapList) {
            UCRankTemplate template = new UCRankTemplate();
            template.readTxt(map);
            rankTemplatesList.add(template);
        }
        rankTemplates = rankTemplatesList;
        List<UCRahmensTemplate> rahmensTemplatesList = new ArrayList<>();
        mapList = ConfigReader.read(ConfigFile.unparalleledChallenge_rahmens);
        for (Map<String, String> map : mapList) {
            UCRahmensTemplate template = new UCRahmensTemplate();
            template.readTxt(map);
            rahmensTemplatesList.add(template);
        }
        rahmensTemplates = rahmensTemplatesList;
        UnparalleledChallengeConstant constantTemp = new UnparalleledChallengeConstant();
        Map<String, String> map = ConstantConfigReader.read(ConfigFile.unparalleledChallenge_const);
        constantTemp.readTxt(map);
        constant = constantTemp;
    }

    @Handler(PtCode.UNPARALLELEDCHALLENGE_MAININFO_REQ)
    private void getMainInfo(Player player, PbProtocol.UnparalleledChallengeMainInfoReq req, long time) {
        PbProtocol.UnparalleledChallengeMainInfoRst.Builder rst = PbProtocol.UnparalleledChallengeMainInfoRst.newBuilder().setResult(Text.genOkServerRstInfo());

        WorldMessageCallBackAsync.deal(player.getPlayerId(), time, "WorldUCCommandService.getMainInfo",
                new MainInfoCallBackTask(player, time, rst), "");

    }

    @Handler(PtCode.UNPARALLELEDCHALLENGE_BETINFO_REQ)
    private void betInfo(Player player, PbProtocol.UnparalleledChallengeBetInfoReq req, long time) {
        PbProtocol.UnparalleledChallengeBetInfoRst.Builder rst = PbProtocol.UnparalleledChallengeBetInfoRst.newBuilder().setResult(Text.genOkServerRstInfo());

        WorldMessageCallBackAsync.deal(player.getPlayerId(), time, "WorldUCCommandService.betInfo",
                new BetInfoCallBackTask(player, time, rst), "");

    }

    @Handler(PtCode.UNPARALLELEDCHALLENGE_BET_REQ)
    private void bet(Player player, PbProtocol.UnparalleledChallengeBetReq req, long time) {
        PbProtocol.UnparalleledChallengeBetRst.Builder rst = PbProtocol.UnparalleledChallengeBetRst.newBuilder().setResult(Text.genOkServerRstInfo());

        logic:{
            //预扣除消耗
            UnparalleledChallengeConstant constant = getConstant();
            long startTime = constant.modifyTime(ServerConstants.getCurrentTimeLocalDateTime(), constant.guessingTime_1, false);
            long endTime = constant.modifyTime(ServerConstants.getCurrentTimeLocalDateTime(), constant.guessingTime_2, false);
            if(ServerConstants.getCurrentTimeMillis() < startTime || ServerConstants.getCurrentTimeMillis() > endTime){
                rst.setResult(Text.genServerRstInfo(Text.群龙聚首_竞猜失败));
                break logic;
            }
            int preCostId = player.getPreCostModel().pre(Reward.templateCollectionToReward(constant.guessingCost), BehaviorType.uc_guess_Cos);
            if(preCostId <= 0){
                rst.setResult(Text.genServerRstInfo(Text.消耗不足));
                break logic;
            }
            WorldMessageCallBackAsync.deal(player.getPlayerId(), time, "WorldUCCommandService.bet",
                    new BetCallBackTask(player, preCostId, time, rst), req.getIsBefore());
            return;
        }
        player.send(PtCode.UNPARALLELEDCHALLENGE_BET_RST, rst.build(), time);
    }

    @Handler(PtCode.UNPARALLELEDCHALLENGE_RECEIVEBETREWARD_REQ)
    private void receiveBetReward(Player player, PbProtocol.UnparalleledChallengeReceiveBetRewardReq req, long time) {
        PbProtocol.UnparalleledChallengeReceiveBetRewardRst.Builder rst = PbProtocol.UnparalleledChallengeReceiveBetRewardRst.newBuilder().setResult(Text.genOkServerRstInfo());

        WorldMessageCallBackAsync.deal(player.getPlayerId(), time, "WorldUCCommandService.receiveBetReward",
                new ReceiveBetRewardCallBackTask(player, time, rst), "");
    }


    @Override
    public boolean isWorldServer() {
        return true;
    }

    private static class MainInfoCallBackTask implements WorldMessageCallBackAsync.CallBackTask {
        private final Player player;
        private final long time;
        private final PbProtocol.UnparalleledChallengeMainInfoRst.Builder rst;

        public MainInfoCallBackTask(Player player, long time, PbProtocol.UnparalleledChallengeMainInfoRst.Builder rst) {
            this.player = player;
            this.time = time;
            this.rst = rst;
        }

        @Override
        public void complete(CallbackResponse response) {
            byte[] bytes = response.getParam(0);
            try {
                ThreadPool.execute(new UCGetMainAsyncCall(PbProtocol.UnparalleledChallengeMainInfoRst.parseFrom(bytes), player, time));
            } catch (InvalidProtocolBufferException e) {
                throw new RuntimeException(e);
            }
        }

        @Override
        public void timeout() {
            rst.setResult(Text.genServerRstInfo(Text.服务器异常));
            player.send(PtCode.UNPARALLELEDCHALLENGE_MAININFO_RST, rst.build(), time);
        }
    }

    private static class BetInfoCallBackTask implements WorldMessageCallBackAsync.CallBackTask {
        private final Player player;
        private final long time;
        private final PbProtocol.UnparalleledChallengeBetInfoRst.Builder rst;

        public BetInfoCallBackTask(Player player, long time, PbProtocol.UnparalleledChallengeBetInfoRst.Builder rst) {
            this.player = player;
            this.time = time;
            this.rst = rst;
        }

        @Override
        public void complete(CallbackResponse response) {
            byte[] bytes = response.getParam(0);
            try {
                ThreadPool.execute(new UCGetBetAsyncCall(PbProtocol.UnparalleledChallengeBetInfoRst.parseFrom(bytes).toBuilder(), player, time));
            } catch (InvalidProtocolBufferException e) {
                throw new RuntimeException(e);
            }
        }

        @Override
        public void timeout() {
            rst.setResult(Text.genServerRstInfo(Text.服务器异常));
            player.send(PtCode.UNPARALLELEDCHALLENGE_BETINFO_RST, rst.build(), time);
        }
    }

    private static class BetCallBackTask implements WorldMessageCallBackAsync.CallBackTask {
        private final Player player;
        private final int preCostId;
        private final long time;
        private final PbProtocol.UnparalleledChallengeBetRst.Builder rst;

        public BetCallBackTask(Player player, int preCostId, long time, PbProtocol.UnparalleledChallengeBetRst.Builder rst) {
            this.player = player;
            this.preCostId = preCostId;
            this.time = time;
            this.rst = rst;
        }

        @Override
        public void complete(CallbackResponse response) {
            byte[] bytes = response.getParam(0);
            try {
                PbProtocol.UnparalleledChallengeBetRst.Builder rst = PbProtocol.UnparalleledChallengeBetRst.parseFrom(bytes).toBuilder();
                player.getPreCostModel().remove(preCostId, rst.getResult().getResult());
                ThreadPool.execute(new UCBetAsyncCall(rst, player, time));
            } catch (InvalidProtocolBufferException e) {
                throw new RuntimeException(e);
            }
        }

        @Override
        public void timeout() {
            rst.setResult(Text.genServerRstInfo(Text.服务器异常));
            player.send(PtCode.UNPARALLELEDCHALLENGE_BET_RST, rst.build(), time);
        }
    }

    private static class ReceiveBetRewardCallBackTask implements WorldMessageCallBackAsync.CallBackTask {
        private final Player player;
        private final long time;
        private final PbProtocol.UnparalleledChallengeReceiveBetRewardRst.Builder rst;

        public ReceiveBetRewardCallBackTask(Player player, long time, PbProtocol.UnparalleledChallengeReceiveBetRewardRst.Builder rst) {
            this.player = player;
            this.time = time;
            this.rst = rst;
        }

        @Override
        public void complete(CallbackResponse response) {
            byte[] bytes = response.getParam(0);
            try {
                PbProtocol.UnparalleledChallengeReceiveBetRewardRst.Builder builder = PbProtocol.UnparalleledChallengeReceiveBetRewardRst.parseFrom(bytes).toBuilder();
                if(builder.getResult().getResult()){
                    //领取奖励
                    UnparalleledChallengeConstant constant = getConstant();
                    builder.addAllRewards(Reward.writeCollectionToPb(Reward.addFromTemplates(constant.guessingRewards_3, player, BehaviorType.uc_guess_all)));
                }
                player.send(PtCode.UNPARALLELEDCHALLENGE_RECEIVEBETREWARD_RST, builder.build(), time);
            } catch (InvalidProtocolBufferException e) {
                throw new RuntimeException(e);
            }
        }

        @Override
        public void timeout() {
            rst.setResult(Text.genServerRstInfo(Text.服务器异常));
            player.send(PtCode.UNPARALLELEDCHALLENGE_RECEIVEBETREWARD_RST, rst.build(), time);
        }
    }
}
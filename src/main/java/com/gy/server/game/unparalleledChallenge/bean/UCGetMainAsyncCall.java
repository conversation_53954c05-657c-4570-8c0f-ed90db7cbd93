package com.gy.server.game.unparalleledChallenge.bean;

import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.packet.PbLeague;
import com.gy.server.packet.PbProtocol;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 获取主界面信息
 * @author: gbk
 * @date: 2024-12-18 13:55
 */
public class UCGetMainAsyncCall extends AsyncCall {

    Player player;
    long time;
    PbProtocol.UnparalleledChallengeMainInfoRst rst;

    public UCGetMainAsyncCall(PbProtocol.UnparalleledChallengeMainInfoRst rst, Player player, long time) {
        this.rst = rst;
        this.player = player;
        this.time = time;
    }


    Map<Long, MiniGamePlayer> players = new HashMap<>();
    @Override
    public void asyncExecute() {
        Set<Long> playerIds = new HashSet<>();
        PbLeague.UnparalleledChallengeMainInfo mainInfo = rst.getMainInfo();
        for (PbLeague.UnparalleledChallengeGroupInfo groupInfo : mainInfo.getGroupInfosList()) {
            fillPlayerIds(playerIds, groupInfo);
        }
        for (PbLeague.UnparalleledChallengeGroupInfo groupInfo : mainInfo.getAdvanceInfosList()) {
            fillPlayerIds(playerIds, groupInfo);
        }
        for (PbLeague.UnparalleledChallengeGroupInfo groupInfo : mainInfo.getFinalInfoList()) {
            fillPlayerIds(playerIds, groupInfo);
        }
        players.putAll(PlayerHelper.getMiniPlayersForMap(playerIds));
    }

    private void fillPlayerIds(Set<Long> playerIds, PbLeague.UnparalleledChallengeGroupInfo groupInfo){
        for (PbLeague.UnparalleledChallengeUnitInfo unitInfo : groupInfo.getUnitInfosList()) {
            playerIds.add(unitInfo.getPlayerId());
        }
    }

    private void fillGroupPlayerInfo(int j, PbLeague.UnparalleledChallengeGroupInfo.Builder groupInfosBuilder){
        PbLeague.UnparalleledChallengeUnitInfo.Builder unitInfoBuilder = groupInfosBuilder.getUnitInfosBuilder(j);
        unitInfoBuilder.setMiniUser(players.get(unitInfoBuilder.getPlayerId()).genMinMiniUser());
        groupInfosBuilder.setUnitInfos(j, unitInfoBuilder.build());
    }

    @Override
    public void execute() {
        PbLeague.UnparalleledChallengeMainInfo.Builder mainInfo = rst.getMainInfo().toBuilder();
        for (int i = 0; i < mainInfo.getGroupInfosList().size(); i++) {
            PbLeague.UnparalleledChallengeGroupInfo.Builder groupInfosBuilder = mainInfo.getGroupInfosBuilder(i);
            for (int j = 0; j < groupInfosBuilder.getUnitInfosList().size(); j++) {
                fillGroupPlayerInfo(j, groupInfosBuilder);
            }
            mainInfo.setGroupInfos(i, groupInfosBuilder.build());
        }
        for (int i = 0; i < mainInfo.getAdvanceInfosList().size(); i++) {
            PbLeague.UnparalleledChallengeGroupInfo.Builder groupInfosBuilder = mainInfo.getAdvanceInfosBuilder(i);
            for (int j = 0; j < groupInfosBuilder.getUnitInfosList().size(); j++) {
                fillGroupPlayerInfo(j, groupInfosBuilder);
            }
            mainInfo.setAdvanceInfos(i, groupInfosBuilder.build());
        }
        for (int i = 0; i < mainInfo.getFinalInfoList().size(); i++) {
            PbLeague.UnparalleledChallengeGroupInfo.Builder groupInfosBuilder = mainInfo.getFinalInfoBuilder(i);
            for (int j = 0; j < groupInfosBuilder.getUnitInfosList().size(); j++) {
                fillGroupPlayerInfo(j, groupInfosBuilder);
            }
            mainInfo.setFinalInfo(i, groupInfosBuilder.build());
        }

        player.send(PtCode.UNPARALLELEDCHALLENGE_MAININFO_RST, rst.toBuilder().setMainInfo(mainInfo.build()).build(), time);
    }
}
package com.gy.server.game.unparalleledChallenge.bean;

import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.packet.PbLeague;
import com.gy.server.packet.PbProtocol;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 竞猜
 * @author: gbk
 * @date: 2024-12-18 13:55
 */
public class UCBetAsyncCall extends AsyncCall {

    Map<Long, MiniGamePlayer> players = new HashMap<>();
    PbProtocol.UnparalleledChallengeBetRst.Builder rst;
    Player player;
    long time;

    public UCBetAsyncCall(PbProtocol.UnparalleledChallengeBetRst.Builder rst, Player player, long time) {
        this.rst = rst;
        this.player = player;
        this.time = time;
    }

    @Override
    public void asyncExecute() {
        Set<Long> playerIds = new HashSet<>();
        PbLeague.UnparalleledChallengeBetInfo betInfo = rst.getBetInfo();
        playerIds.add(betInfo.getAfterPlayerInfo().getPlayerId());
        playerIds.add(betInfo.getBeforePlayerInfo().getPlayerId());
        players.putAll(PlayerHelper.getMiniPlayersForMap(playerIds));

    }

    @Override
    public void execute() {
        PbLeague.UnparalleledChallengeBetInfo.Builder betInfoBuilder = rst.getBetInfoBuilder();
        betInfoBuilder.setBeforePlayerInfo(betInfoBuilder.getBeforePlayerInfoBuilder().setMiniUser(players.get(betInfoBuilder.getBeforePlayerInfo().getPlayerId()).genMinMiniUser()));
        betInfoBuilder.setAfterPlayerInfo(betInfoBuilder.getAfterPlayerInfoBuilder().setMiniUser(players.get(betInfoBuilder.getAfterPlayerInfo().getPlayerId()).genMinMiniUser()));
        rst.setBetInfo(betInfoBuilder.build());
        player.send(PtCode.UNPARALLELEDCHALLENGE_BET_RST, rst.build(), time);
    }

}
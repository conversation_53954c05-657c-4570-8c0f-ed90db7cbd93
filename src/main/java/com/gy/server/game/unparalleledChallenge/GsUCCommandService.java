package com.gy.server.game.unparalleledChallenge;

import com.gy.server.annotation.MessageMethod;
import com.gy.server.annotation.MessageServiceBean;
import com.gy.server.core.MessageServerType;
import com.gy.server.core.MethodInvokeType;
import com.gy.server.core.command.CommandRequestParams;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.currency.Currency;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.mail.MailHelper;
import com.gy.server.game.mail.globalMail.GlobalMailType;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.unparalleledChallenge.template.UnparalleledChallengeConstant;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.world.unparalleledChallenge.enums.UCPhaseType;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 *  PVP群龙聚首
 * @author: gbk
 * @date: 2024-12-16 10:41
 */
@MessageServiceBean(description = "PVP群龙聚首", messageServerType = MessageServerType.game)
public class GsUCCommandService {

    public static final String separator = "_";

    @MessageMethod(description = "排名奖励", invokeType = MethodInvokeType.sync)
    private static void rankReward(ServerCommandRequest request, CommandRequestParams params) {
        int seasonId  = params.getParam(0);
        int raceId  = params.getParam(1);
        Map<Long, Integer> rankInfos = params.getParam(2);
        //排名奖励
        MailHelper.addGlobalMail(GlobalMailType.ucRank.build(seasonId, raceId, rankInfos));
        //帮派奖励
        for (Long playerId : rankInfos.keySet()) {
            League league = LeagueManager.getLeagueByPlayerId(playerId);
            MiniGamePlayer miniPlayer = PlayerManager.getMiniPlayer(playerId);
            if(Objects.nonNull(league) && Objects.nonNull(miniPlayer)){
                MailHelper.addGlobalMail(GlobalMailType.ucRankLeague.build(seasonId, raceId
                        , miniPlayer.getName(), rankInfos.get(playerId), new ArrayList<>(league.getAllMember())));
            }
        }

    }

    @MessageMethod(description = "竞猜奖励", invokeType = MethodInvokeType.sync)
    private static void betReward(ServerCommandRequest request, CommandRequestParams params) {
        int seasonId  = params.getParam(0);
        int raceId  = params.getParam(1);
        //阶段
        UCPhaseType phaseType = UCPhaseType.valueOf(params.getParam(2).toString());
        //竞猜成功
        List<Long> winPlayerIds = params.getParam(3);
        List<Long> losePlayerIds = params.getParam(4);
        double odds = params.getParam(5);
        //胜利奖励
        MailHelper.addGlobalMail(GlobalMailType.ucBetWinReward.build(seasonId, raceId, phaseType, winPlayerIds, odds));
        //失败奖励
        UnparalleledChallengeConstant constant = UnparalleledChallengeService.getConstant();
        if(CollectionUtil.isNotEmpty(constant.guessingRewards_2)){
            MailHelper.addGlobalMail(GlobalMailType.ucBetLoseReward.build(seasonId, raceId, phaseType, losePlayerIds));
        }
    }

    @MessageMethod(description = "竞猜补发", invokeType = MethodInvokeType.sync)
    private static void betReissue(ServerCommandRequest request, CommandRequestParams params) {
        List<Long> playerIds = params.getParam(0);
        //竞猜补发
        MailHelper.addGlobalMail(GlobalMailType.ucBetReissue.build(playerIds));
    }

    public static void fillBetCoin(Player player){
        UnparalleledChallengeConstant constant = UnparalleledChallengeService.getConstant();
        RewardTemplate rewardTemplate = constant.guessingCost.get(0);
        Currency currency = Currency.getCurrencyById(rewardTemplate.type);
        long maxValue = rewardTemplate.value;
        long nowValue = player.getCurrencyModel().getCurrency(currency);
        if(maxValue - nowValue > 0){
            player.getCurrencyModel().increaseCurrency(currency, maxValue - nowValue, BehaviorType.ucAutoFull);
        }
    }

}
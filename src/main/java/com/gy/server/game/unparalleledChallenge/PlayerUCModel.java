package com.gy.server.game.unparalleledChallenge;

import com.gy.server.game.function.Function;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModel;
import com.gy.server.game.player.event.PlayerEvent;
import com.gy.server.game.player.event.PlayerEventHandler;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.unparalleledChallenge.template.UCRahmensTemplate;
import com.ttlike.server.tl.baselib.serialize.player.PlayerBlobDb;

import java.util.HashSet;
import java.util.Set;

import static com.gy.server.game.player.event.PlayerEventType.functionCheck;

/**
 * 群龙聚首信息
 * @author: gbk
 * @date: 2025-01-09 13:39
 */
public class PlayerUCModel extends PlayerModel implements PlayerEventHandler {

    public PlayerUCModel(Player player) {
        super(player);
    }

    //历史最高排名
    private int historyHighRank = 9999;
    //冠军次数
    private int championTimes;
    //已经解锁头像框id信息
    private Set<Integer> hadUnlockRahmenIds = new HashSet<>();

    /**
     * 记录排名信息
     * @param rank
     */
    public void recordRank(int rank){
        //记录最小段位
        historyHighRank = Math.min(rank, historyHighRank);
        if(rank == 1){
            //增加冠军次数
            championTimes++;
        }
        checkRahmen();
    }

    /**
     * 检查是否解锁了新头像信息
     */
    public void checkRahmen(){
    }

    public int getHistoryHighRank() {
        return historyHighRank;
    }

    public void setHistoryHighRank(int historyHighRank) {
        this.historyHighRank = historyHighRank;
    }

    public int getChampionTimes() {
        return championTimes;
    }

    public void setChampionTimes(int championTimes) {
        this.championTimes = championTimes;
    }

    public Set<Integer> getHadUnlockRahmenIds() {
        return hadUnlockRahmenIds;
    }

    public void setHadUnlockRahmenIds(Set<Integer> hadUnlockRahmenIds) {
        this.hadUnlockRahmenIds = hadUnlockRahmenIds;
    }

    @Override
    protected void loadData(PlayerBlobDb playerBlob) {

    }

    @Override
    protected void saveData(PlayerBlobDb playerBlob) {

    }

    @Override
    public PlayerEventType[] getEventTypes() {
        return new PlayerEventType[]{PlayerEventType.goalFinish, functionCheck};
    }

    @Override
    public void handle(PlayerEvent event) {
        Player player = event.getSource();
        switch (event.getEventType()) {
            case goalFinish: {
                int goalId = event.getParam(0);
                for (UCRahmensTemplate rahmensTemplate : UnparalleledChallengeService.getRahmensTemplates()) {
                    if(rahmensTemplate.goalId == goalId){
                        player.getRoleModel().unlockHeadFrame(rahmensTemplate.unlockRahmen, -1, BehaviorType.ucRank);
                    }

                }
                break;
            }
            case functionCheck: {
                if(Function.unparalleledChallenge.isOpen(player)){
                    GsUCCommandService.fillBetCoin(player);
                }
            }
        }
    }
}
package com.gy.server.game.unparalleledChallenge.template;

import com.gy.server.common.base.AbsTemplate;
import com.gy.server.game.combat.StageType;
import com.gy.server.game.lineup.LineupType;

import java.util.Map;

/**
 * 赛季信息
 * @author: gbk
 * @date: 2024-12-11 18:04
 */
public class UnparalleledChallengeSeasonTemplate implements AbsTemplate {

    //赛季id
    public int seasonId;
    //战斗类型
    public StageType stageType;
    //布阵类型
    public LineupType lineupType;
    //buffId
    public int buffId;
    //战斗场景id
    public int battleCollectId;


    @Override
    public void readTxt(Map<String, String> map) {
        seasonId = Integer.parseInt(map.get("id"));
        stageType = StageType.getStageTypeById(Integer.parseInt(map.get("stage")));
        lineupType = LineupType.getType(Integer.parseInt(map.get("lineUse")));
        buffId = Integer.parseInt(map.get("buff"));
        battleCollectId = Integer.parseInt(map.get("battleCollect"));
    }
}
package com.gy.server.game.normalitem.effect.impl;

import com.gy.server.game.currency.Currency;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.normalitem.effect.NormalItemEffect;
import com.gy.server.game.normalitem.effect.NormalItemEffectResult;
import com.gy.server.game.player.Player;
import com.gy.server.packet.PbCommons;

/**
 * <AUTHOR> - [Created on 2018/3/2 16:47]
 */
public class IncreaseCurrencyEffect extends NormalItemEffect {

    private int currencyId;

    private int count;


    @Override
    protected void parseParam(String[] paramValues) {
        currencyId = Integer.parseInt(paramValues[0]);
        count = Integer.parseInt(paramValues[1]);
        if (Currency.getCurrencyById(currencyId) == null || count < 1) {
            throw new IllegalArgumentException("IncreaseCurrencyEffect error -> " + currencyId + ", " + count);
        }
    }

    @Override
    public NormalItemEffectResult use(Player player, Object... params) {
        player.increaseNumeric(currencyId, count, BehaviorType.ItemUse);
        return new NormalItemEffectResult(PbCommons.NotifyAcquireType.Dialog, Reward.create(currencyId, -1, count));
    }

    public int getCurrencyId() {
        return currencyId;
    }
}

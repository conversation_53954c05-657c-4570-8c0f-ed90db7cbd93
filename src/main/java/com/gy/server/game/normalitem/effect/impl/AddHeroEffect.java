package com.gy.server.game.normalitem.effect.impl;

import com.gy.server.game.drop.Drop;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.item.ItemService;
import com.gy.server.game.item.ItemTemplate;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.normalitem.effect.NormalItemEffect;
import com.gy.server.game.normalitem.effect.NormalItemEffectResult;
import com.gy.server.game.player.Player;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbCommons;

/**
 * <AUTHOR> - [Created on 2018/3/5 17:30]
 */
public class AddHeroEffect extends NormalItemEffect {

    private int templateId;

    private int count;


    @Override
    protected void parseParam(String[] paramValues) {
        templateId = Integer.parseInt(paramValues[0]);
        count = Integer.parseInt(paramValues[1]);
        if (count < 1) {
            throw new IllegalArgumentException("AddItemEffect count error -> " + count);
        }
    }

    @Override
    public NormalItemEffectResult use(Player player, Object... params) {
        ItemTemplate template = ItemService.getItemTemplate(templateId);
        if (template == null) {
            return new NormalItemEffectResult(Text.物品不存在);
        }

        Reward reward = Reward.create(Drop.DROP_TYPE_STAR_HERO, templateId, count);
        reward.add(player, BehaviorType.ItemUse);
        return new NormalItemEffectResult(PbCommons.NotifyAcquireType.Dialog, reward);
    }
}

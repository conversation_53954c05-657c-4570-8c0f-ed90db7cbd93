package com.gy.server.game.normalitem.effect;

import com.gy.server.game.player.Player;
import com.gy.server.utils.function.StringCaster;

import org.apache.commons.lang3.StringUtils;

/**
 * 普通物品使用效果
 *
 * <AUTHOR> - [Created on 2018/3/2 16:41]
 */
public abstract class NormalItemEffect {

    protected String[] params = new String[0];

    public void parseParam(String text) {
        if (!StringUtils.equals(text, "-1")) {
            params = StringUtils.split(text, ",");
            parseParam(params);
        }
    }

    public String[] getParams() {
        return params;
    }

    public String getParam(int index) {
        if (index < 0 || index >= params.length) {
            return null;
        }
        return params[index];
    }

    public <T> T getParam(int index, StringCaster<T> caster) {
        String param = getParam(index);
        if (param == null) {
            return null;
        }

        return caster.cast(param);
    }

    protected abstract void parseParam(String[] paramValues);

    public abstract NormalItemEffectResult use(Player player, Object... params);

}

package com.gy.server.game.normalitem;

import java.util.ArrayList;
import java.util.List;

import com.gy.server.game.drop.RewardTemplate;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> - [Created on 2018/3/5 14:11]
 */
public class NormalItemBagTemplate {

    public int templateId;

    private List<RewardTemplate> rewards = new ArrayList<>();

    public void setRewards(String text) {
        if (!StringUtils.equals(text, "-1")) {
            String[] array = text.split(",");
            for (String rewardInfo : array) {
                rewards.add(new RewardTemplate(rewardInfo));
            }
        }
    }

    public int getRewardCount() {
        return rewards.size();
    }

    public RewardTemplate getRewardTemplateByIndex(int index) {
        if (index < 0 || index >= rewards.size()) {
            return null;
        }
        return rewards.get(index);
    }
}

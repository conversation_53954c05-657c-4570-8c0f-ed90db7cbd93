package com.gy.server.game.normalitem.effect;

import com.gy.server.game.normalitem.effect.impl.*;

import java.util.function.Supplier;

/**
 * <AUTHOR> - [Created on 2018/3/2 16:42]
 */
public enum NormalItemEffectType {

    IncreaseCurrency(IncreaseCurrencyEffect::new),
    AddItem(AddItemEffect::new),
    Drop(DropItemEffect::new),
    IncreaseHeroExp(IncreaseHeroExpEffect::new),
    AddHero(AddHeroEffect::new),
    // 主线副本挂机道具X小时
    InstanceHandUP(InstanceHandUpEffect::new),
    ;

    Supplier<NormalItemEffect> supplier;

    NormalItemEffectType(Supplier<NormalItemEffect> supplier) {
        this.supplier = supplier;
    }

    public NormalItemEffect createNormalItemEffect() {
        return supplier.get();
    }

    public static NormalItemEffectType getNormalItemEffectType(String type) {
        try {
            return NormalItemEffectType.valueOf(type);
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException(String.format("unsupported NormalItemEffectType. (%s)", type));
        }

    }

}

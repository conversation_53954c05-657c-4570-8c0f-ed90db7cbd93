package com.gy.server.game.normalitem.effect;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import com.gy.server.game.drop.Reward;
import com.gy.server.game.text.Text;
import com.gy.server.game.text.TextParam;
import com.gy.server.packet.PbCommons;
import com.gy.server.utils.CollectionUtil;

/**
 * 普通物品使用结果
 *
 * <AUTHOR> - [Created on 2018/3/5 15:23]
 */
public class NormalItemEffectResult {

    public final boolean result;

    public final PbCommons.NotifyAcquireType notifyAcquireType;

    public final List<Reward> rewards = new ArrayList<>();

    public final PbCommons.PbText errInfo;

    public NormalItemEffectResult(PbCommons.NotifyAcquireType type, Reward... rewards) {
        this(type, CollectionUtil.toCollection(rewards, ArrayList::new));
    }

    public NormalItemEffectResult(PbCommons.NotifyAcquireType type, Collection<Reward> rewards) {
        result = true;
        notifyAcquireType = type;
        this.rewards.addAll(rewards);
        errInfo = null;
    }

    public NormalItemEffectResult(int messageId, TextParam... params) {
        this(messageId, CollectionUtil.toCollection(params, ArrayList::new));
    }

    public NormalItemEffectResult(int messageId, List<TextParam> params) {
        result = false;
        notifyAcquireType = PbCommons.NotifyAcquireType.Word;
        errInfo = Text.genText(messageId, params).build();
    }


}

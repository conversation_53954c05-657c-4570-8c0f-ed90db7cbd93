package com.gy.server.game.normalitem;

import java.util.ArrayList;
import java.util.List;

import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.item.ItemTemplate;
import com.gy.server.game.item.ItemType;
import com.gy.server.game.item.ItemUseType;
import com.gy.server.game.normalitem.effect.NormalItemEffect;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> - [Created on 2018/2/10 13:42]
 */
public class NormalItemTemplate extends ItemTemplate {

    public List<RewardTemplate> sellPrice = new ArrayList<>();

    public boolean canUse;

    public int useLevel;

    public int vipLevel;

    public int quality;

    public int maxCount;

    public ItemUseType useType;

    public List<RewardTemplate> needItemRewards = new ArrayList<>();


    public NormalItemEffect effect;

    /**
     * GM指令allItem是否发送
     */
    public boolean send;

    /**
     * gm可发送最大数量
     */
    public int sendingLimit;

    //类型参数1
    public String param1;

    public int libertyType;

    /**
     * 特殊参数
     * 产出地点
     */
    public List<Integer> specialParam = new ArrayList<>();

    public NormalItemTemplate(int id, String name) {
        super(id, name, ItemType.NORMALITEM);
    }

    public void setNeedItemRewards(String text) {
        if (!StringUtils.equals(text, "-1")) {
            String[] array = text.split(",");
            for (String str : array) {
                needItemRewards.add(new RewardTemplate(str));
            }
        }
    }


    public boolean canSell() {
        return !sellPrice.isEmpty();
    }

    @Override
    public boolean isHeapItem() {
        return true;
    }
}

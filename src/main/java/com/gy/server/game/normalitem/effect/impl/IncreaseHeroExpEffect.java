package com.gy.server.game.normalitem.effect.impl;

import com.gy.server.game.hero.Hero;
import com.gy.server.game.normalitem.effect.NormalItemEffect;
import com.gy.server.game.normalitem.effect.NormalItemEffectResult;
import com.gy.server.game.player.Player;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbCommons;

import org.apache.commons.lang3.ArrayUtils;

/**
 * <AUTHOR> - [Created on 2018/3/5 19:33]
 */
public class IncreaseHeroExpEffect extends NormalItemEffect {

    private int exp;

    @Override
    protected void parseParam(String[] paramValues) {
        exp = Integer.parseInt(paramValues[0]);
        if (exp < 1) {
            throw new IllegalArgumentException("IncreaseHeroExpEffect exp error -> " + exp);
        }
    }

    @Override
    public NormalItemEffectResult use(Player player, Object... params) {
        if (ArrayUtils.isEmpty(params) || !(params[0] instanceof Hero)) {
            return new NormalItemEffectResult(Text.英雄不存在);
        }
        Hero hero = (Hero) params[0];
//        hero.increaseExp(exp);
        return new NormalItemEffectResult(PbCommons.NotifyAcquireType.Dialog);
    }

    public int getExp() {
        return exp;
    }
}

package com.gy.server.game.normalitem.effect.impl;

import com.gy.server.game.drop.DropService;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.function.Function;
import com.gy.server.game.instance.InstanceService;
import com.gy.server.game.instance.PlayerInstanceModel;
import com.gy.server.game.instance.template.HandUpFixReward;
import com.gy.server.game.instance.template.InstanceConstant;
import com.gy.server.game.instance.template.InstanceHandUpItemTemplate;
import com.gy.server.game.instance.template.InstanceHandUpTemplate;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.normalitem.effect.NormalItemEffect;
import com.gy.server.game.normalitem.effect.NormalItemEffectResult;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbCommons;
import com.gy.server.utils.time.DateTimeUtil;
import org.apache.commons.lang3.tuple.Pair;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 主线副本掉落X小时道具
 *
 * <AUTHOR> - [Created on 2023/1/31 10:21]
 */
public class InstanceHandUpEffect extends NormalItemEffect {

    /**
     * 掉落X秒
     */
    private long time;
    /**
     * 道具使用类型ID
     */
    private int itemEffectId;

    @Override
    protected void parseParam(String[] paramValues) {
        this.time = Integer.parseInt(paramValues[0]) * DateTimeUtil.SecondsOfMinute;
        this.itemEffectId = Integer.parseInt(paramValues[1]);
    }

    @Override
    public NormalItemEffectResult use(Player player, Object... params) {
        if (Function.instanceHandUp.isNotOpen(player)) {
            return new NormalItemEffectResult(Text.功能未开启);
        }
        Map<Integer, InstanceHandUpItemTemplate> itemEffectTemplateMap = InstanceService.getHandUpItemTemplateMap();
        PlayerInstanceModel instanceModel = player.getModel(PlayerModelEnums.instance);
        InstanceHandUpTemplate curHandUpTemplate = instanceModel.getCurHandUpTemplate();
        InstanceHandUpItemTemplate itemEffectTemplate = itemEffectTemplateMap.get(itemEffectId);

        InstanceConstant constant = InstanceService.getConstant();
        int baseTime = constant.handUpTime;
        // 掉落次数
        int dropNum = (int) (time / baseTime);
        List<Reward> rewardList = new ArrayList<>();
        // 固定奖励
        for (Integer fixedNumber : itemEffectTemplate.fixedNumberList) {
            if (curHandUpTemplate.fixRewardList.size() < fixedNumber) {
                continue;
            }
            HandUpFixReward handUpFixReward = curHandUpTemplate.fixRewardList.get(fixedNumber - 1);
            int type = handUpFixReward.getType();
            int templateId = handUpFixReward.getTemplateId();
            double addValue = handUpFixReward.getValue() * dropNum;
            if (addValue > 1) {
                rewardList.add(Reward.create(type, templateId, (long) addValue));
            }
        }
        //随机奖励
        for (Integer dropNumber : itemEffectTemplate.dropNumberList) {
            if (curHandUpTemplate.dropRewardList.size() < dropNumber) {
                continue;
            }
            Pair<Integer, Integer> dropPair = curHandUpTemplate.dropRewardList.get(dropNumber - 1);
            Integer dropId = dropPair.getLeft();
            int dropRewardNum = dropNum / dropPair.getRight();
            for (int j = 0; j < dropRewardNum; j++) {
                List<Reward> dropReward = DropService.executeDrop(player, dropId, BehaviorType.ItemUse, false, true);
                rewardList.addAll(dropReward);
            }
        }
        Reward.merge(rewardList);
        Reward.add(rewardList, player, BehaviorType.ItemUse);
        return new NormalItemEffectResult(PbCommons.NotifyAcquireType.Dialog, rewardList.toArray(new Reward[0]));
    }
}

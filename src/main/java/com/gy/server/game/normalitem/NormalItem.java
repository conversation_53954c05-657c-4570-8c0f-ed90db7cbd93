package com.gy.server.game.normalitem;

import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;
import com.gy.server.core.log.CommonLogger;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.item.HeapItem;
import com.gy.server.game.item.ItemUseType;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.normalitem.effect.NormalItemEffectResult;
import com.gy.server.game.player.Player;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbItem;
import com.gy.server.utils.CollectionUtil;
import com.ttlike.server.tl.baselib.serialize.player.PlayerBagModelDB;

/**
 * 普通物品
 *
 * <AUTHOR> - [Created on 2018/2/10 13:40]
 */
public class NormalItem extends HeapItem {



    public NormalItem() {
    }
    public NormalItem(int templateId) {
        super(templateId);
    }

    public NormalItemEffectResult use(Player player, Object... params) {
        return this.use(player, BehaviorType.ItemUse, params);
    }

    public NormalItemEffectResult use(Player player, BehaviorType behaviorType, Object... params) {
        NormalItemTemplate template = (NormalItemTemplate) this.getItemTemplate();
        if (template == null || template.effect == null) {
            return new NormalItemEffectResult(Text.物品使用数据异常);
        }

        boolean remove = true;
        NormalItemEffectResult result;
        try {
            result = template.effect.use(player, params);
            remove = result.result;
        } catch (Exception e) {
            CommonLogger.error(e);
            result = new NormalItemEffectResult(Text.物品使用数据异常);
        }

        if (remove) {
            //从背包里移除自己
            player.getBagModel().removeItem(this, behaviorType);

            //移除使用自己所需要的其他物品
            if (CollectionUtil.isNotEmpty(template.needItemRewards) && template.useType == ItemUseType.COST_USE) {
                for (RewardTemplate needItemReward : template.needItemRewards) {
                    Reward.create(needItemReward).remove(player, behaviorType);
                }
            }
        }

        return result;
    }

    public void remove0(Player player, BehaviorType behaviorType, NormalItemTemplate template) {
        /*NormalItemTemplate itemTemplate = getItemTemplate();

        // 类型8将不移除自身
        if (itemTemplate.useType != EUseType.CanCompose) {
            //从背包里移除自己
            player.bagModel.removeItem(this, behaviorType);
        }

        //移除使用自己所需要的其他物品
        if (CollectionUtil.isNotEmpty(template.needItemRewards)) {
            for (RewardTemplate needItemReward : template.needItemRewards) {
                Reward.create(needItemReward).remove(player, behaviorType);
            }
        }*/

        //从背包里移除自己
        player.getBagModel().removeItem(this, behaviorType);
    }

    @Override
    public PbItem.Item genPb() {
        PbItem.NormalItem.Builder itemBuilder = PbItem.NormalItem.newBuilder()
                .setTemplateId(templateId);

        return PbItem.Item.newBuilder().setItemType(PbItem.Item.ItemType.NormalItem)
                .setNormalItem(itemBuilder).build();
    }

    @Override
    protected void subWriteToPb2(PlayerBagModelDB.BagDB.BagGridDB.ItemDB itemDB) {
        
    }

    @Override
    protected void subReadFromPb2(PlayerBagModelDB.BagDB.BagGridDB.ItemDB itemDB) {

    }

}

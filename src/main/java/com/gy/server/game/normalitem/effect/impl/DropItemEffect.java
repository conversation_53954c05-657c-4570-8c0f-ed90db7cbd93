package com.gy.server.game.normalitem.effect.impl;

import java.util.List;

import com.gy.server.game.drop.DropService;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.normalitem.effect.NormalItemEffect;
import com.gy.server.game.normalitem.effect.NormalItemEffectResult;
import com.gy.server.game.player.Player;
import com.gy.server.packet.PbCommons;

/**
 * <AUTHOR> - [Created on 2018/3/5 19:23]
 */
public class DropItemEffect extends NormalItemEffect {

    private int dropId;

    @Override
    protected void parseParam(String[] paramValues) {
        dropId = Integer.parseInt(paramValues[0]);
    }

    @Override
    public NormalItemEffectResult use(Player player, Object... params) {
        List<Reward> rewards = DropService.executeDrop(player, dropId, BehaviorType.ItemUse);
        return new NormalItemEffectResult(PbCommons.NotifyAcquireType.Dialog, rewards);
    }

    public int getDropId() {
        return dropId;
    }
}

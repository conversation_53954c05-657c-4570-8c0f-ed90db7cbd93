package com.gy.server.game.normalitem;

import java.util.*;

import com.google.common.collect.Sets;
import com.google.protobuf.InvalidProtocolBufferException;
import com.gy.server.core.Configuration;
import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.game.broadcast.BroadCastType;
import com.gy.server.game.constant.ConstantService;
import com.gy.server.game.constant.ConstantType;
import com.gy.server.game.drop.Drop;
import com.gy.server.game.drop.DropService;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.item.ItemService;
import com.gy.server.game.item.ItemTemplate;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.normalitem.effect.NormalItemEffectResult;
import com.gy.server.game.normalitem.effect.impl.DropItemEffect;
import com.gy.server.game.normalitem.effect.impl.IncreaseCurrencyEffect;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.service.Service;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbItem;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.MathUtil;

/**
 * <AUTHOR> - [Created on 2018/3/5 14:26]
 */
public class NormalItemService extends PlayerPacketHandler implements Service {

    @Override
    public void startup() throws Exception {
        for (ItemTemplate template : ItemService.getItemTemplates()) {
            if (template instanceof NormalItemTemplate && ((NormalItemTemplate) template).effect instanceof DropItemEffect) {
                int dropId = ((DropItemEffect) ((NormalItemTemplate) template).effect).getDropId();
                if (DropService.getDropGroup(dropId) == null) {
                    if (Configuration.runMode.isTest()) {
                        System.err.println("DropItemEffect drop not find -> " + dropId);
                    } else {
                        throw new IllegalArgumentException("DropItemEffect drop not find -> " + dropId);
                    }
                }
            }
        }
    }

    /**
     * 物品使用
     */
    @Handler(PtCode.NORMAL_ITEM_USE_CLIENT)
    private void use(Player player, PbProtocol.NormalItemUseReq req, long time) throws InvalidProtocolBufferException {
        PbProtocol.NormalItemUseRst.Builder rst = PbProtocol.NormalItemUseRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());

        int templateId = req.getTemplateId();
        int count = req.getCount();


        NormalItemTemplate template = (NormalItemTemplate) ItemService.getItemTemplate(templateId);
        if (template == null) {
            rst.setResult(Text.genServerRstInfo(Text.物品不存在));
        } else if (!template.canUse) {
            rst.setResult(Text.genServerRstInfo(Text.该物品不能使用));
        } else if (count < 1 || count > ConstantService.getInt(ConstantType.普通物品使用上限, 50)) {
            rst.setResult(Text.genServerRstInfo(Text.使用物品数量异常));
        } else if (player.getLevel() < template.useLevel) {
            rst.setResult(Text.genServerRstInfo(Text.等级不足));
        } else if (player.getBagModel().getItemCount(templateId) < count) {
            rst.setResult(Text.genServerRstInfo(Text.物品数量不足));
        } else if (player.getVipLevel() < template.vipLevel) {
            rst.setResult(Text.genServerRstInfo(Text.Vip等级不足));
        } else if (!checkPowerItemCanUse(player, template)) {
            rst.setResult(Text.genServerRstInfo(Text.体力值将超过上限));
        } else if (CollectionUtil.isNotEmpty(template.needItemRewards)) {
            List<Reward> needRewards = new ArrayList<>();
            for (RewardTemplate rewardTemplate : template.needItemRewards) {
                needRewards.add(Reward.create(rewardTemplate));
            }
            for (Reward reward : needRewards) {
                if (reward.getItemTemplateId() > 0) {
                    long totalCount = reward.getValue() * count;
                    if (reward.getItemTemplateId() == templateId) {//如果消耗的是使用的物品本身，把自己这几个再加进总数里去
                        totalCount += count;
                    }
                    if (player.getBagModel().getItemCount(reward.getItemTemplateId()) < totalCount) {
                        rst.setResult(Text.genServerRstInfo(Text.需要消耗的物品数量不足));
                        break;
                    }
                }
            }
        }
        int useItemTemplateId = 0;

        if (rst.getResult().getResult()) {
            NormalItem item = (NormalItem) player.getBagModel().getItemByTemplateId(templateId).get(0);

            List<Reward> acquireItems = new ArrayList<>();
            for (int i = 0; i < count; i++) {

                //☆☆☆☆ 核心逻辑：物品使用 ☆☆☆☆
                NormalItemEffectResult result = item.use(player, rst, item);

                if (result.result) {
                    acquireItems.addAll(result.rewards);
                    BroadCastType.useItem.broadCast(result.rewards, player, templateId);
                } else {
                    rst.setResult(Text.genServerRstInfo(result.errInfo));
                    break;
                }
            }

            if (rst.getResult().getResult()) {
                Reward.merge(acquireItems);
                rst.addAllRewards(Reward.writeCollectionToPb(acquireItems));
            }
        }

        player.send(PtCode.NORMAL_ITEM_USE_SERVER, rst.build(), time);
    }

    /**
     * 判断玩家使用物品是否是增加体力物品，并且能否使用，如果能使用则返回true，否则返回false
     *
     * @return 能否使用，如果物品不是增加体力物品，则返回true，否则判断玩家体力是否达到一级体力上限，如果达到则返回false，否则返回true
     */
    private boolean checkPowerItemCanUse(Player player, NormalItemTemplate template) {
        if (template.effect instanceof IncreaseCurrencyEffect) {
            int currency = ((IncreaseCurrencyEffect) template.effect).getCurrencyId();
//            if (currency == Currency.power.getId()) {
//                //物品是增加体力的物品，判断玩家体力是否达到一级体力上限
//                return player.getNumeric(Currency.power) < ConstantService.getInt(ConstantType.玩家购买体力上限);
//            }
        }

        return true;
    }

    /**
     * 道具选择包使用
     */
    @Handler(PtCode.NORMAL_ITEM_BAG_USE_CLIENT)
    private void normalItemBagUse(Player player, PbProtocol.NormalItemBagUseReq req, long time) throws InvalidProtocolBufferException {
        PbProtocol.NormalItemBagUseRst.Builder rst = PbProtocol.NormalItemBagUseRst.newBuilder().setResult(Text.genOkServerRstInfo());
        int templateId = req.getTemplateId();
        int index = req.getIndex();
        int count = req.getCount();

        logic:{
            List<Reward> costList = new ArrayList<>();
            List<Reward> rewardList = new ArrayList<>();

            int text = bagUseCheck(player, templateId, index, count, costList, rewardList);
            if(text != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(text));
                break logic;
            }

            if(costList.isEmpty() || rewardList.isEmpty()){
                rst.setResult(Text.genServerRstInfo(Text.数据异常));
                break logic;
            }

            //合并数据
            Reward.merge(rewardList);
            Reward.merge(costList);

            //消耗
            if(Reward.check(player, costList) != -1){
                rst.setResult(Text.genServerRstInfo(Text.消耗不足));
                break logic;
            }

            //扣除物品
            Reward.remove(costList, player, BehaviorType.NormalItemBagUse);
            //发奖
            Reward.add(rewardList, player, BehaviorType.NormalItemBagUse);

            GameLogger.activityItem(player, Reward.writeCollectionToPb(costList), BehaviorType.BoxDrop, false);
            GameLogger.activityItem(player, Reward.writeCollectionToPb(rewardList), BehaviorType.BoxDrop, true);

            //构造返回
            rst.addAllRewards(Reward.writeCollectionToPb(rewardList));
        }

        player.send(PtCode.NORMAL_ITEM_BAG_USE_SERVER, rst.build(), time);
    }

    /**
     * 使用多个道具选择包
     */
    @Handler(PtCode.NORMAL_ITEM_BAGS_USE_CLIENT)
    private void normalItemBagsUse(Player player, PbProtocol.NormalItemBagsUseReq req, long time) throws InvalidProtocolBufferException {
        PbProtocol.NormalItemBagsUseRst.Builder rst = PbProtocol.NormalItemBagsUseRst.newBuilder().setResult(Text.genOkServerRstInfo());
        List<PbItem.OptionalItemBagUse> list = req.getUsesList();
        String pageName = req.getPageName();
        rst.setPageName(pageName);

        logic:{
            if(CollectionUtil.isEmpty(list)){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }

            List<Reward> costList = new ArrayList<>();
            List<Reward> rewardList = new ArrayList<>();

            for (PbItem.OptionalItemBagUse bean : list) {
                int templateId = bean.getTemplateId();
                int index = bean.getIndex();
                int count = bean.getCount();

                int text = bagUseCheck(player, templateId, index, count, costList, rewardList);
                if(text != Text.没有异常){
                    rst.setResult(Text.genServerRstInfo(text));
                    break logic;
                }
            }

            if(costList.isEmpty() || rewardList.isEmpty()){
                rst.setResult(Text.genServerRstInfo(Text.数据异常));
                break logic;
            }

            //合并数据
            Reward.merge(rewardList);
            Reward.merge(costList);

            //消耗
            if(Reward.check(player, costList) != -1){
                rst.setResult(Text.genServerRstInfo(Text.消耗不足));
                break logic;
            }

            //扣除物品
            Reward.remove(costList, player, BehaviorType.NormalItemBagUse);
            //发奖
            Reward.add(rewardList, player, BehaviorType.NormalItemBagUse);

            GameLogger.activityItem(player, Reward.writeCollectionToPb(costList), BehaviorType.BoxDrop, false);
            GameLogger.activityItem(player, Reward.writeCollectionToPb(rewardList), BehaviorType.BoxDrop, true);

            //构造返回
            rst.addAllRewards(Reward.writeCollectionToPb(rewardList));
        }

        player.send(PtCode.NORMAL_ITEM_BAGS_USE_SERVER, rst.build(), time);
    }

    private int bagUseCheck(Player player, int templateId, int index, int count, List<Reward> costList, List<Reward> rewardList){
        NormalItemBagTemplate template = ItemService.getNormalItemBagTemplate(templateId);
        if (template == null) {
            //物品不存在
            return Text.物品不存在;
        }

        if (count < 1 || count > ConstantService.getInt(ConstantType.道具选择包使用上限, 50)) {
            return Text.使用物品数量异常;
        }

        if (index < 0) {
            return Text.参数异常;
        }

        if (player.getBagModel().getItemCount(templateId) < count) {
            //数量不足
            return Text.物品数量不足;
        }

        if (index > template.getRewardCount() - 1) {
            //选择索引有误
            return Text.道具选择包选择索引有误;
        }

        //取出奖励
        RewardTemplate rewardTemplate = template.getRewardTemplateByIndex(index);
        Reward reward = rewardTemplate.createReward();
        reward.setValue(reward.getValue() * count);
        rewardList.add(reward);

        //消耗
        costList.add(Reward.create(Drop.DROP_TYPE_ITEM, templateId, count));
        return Text.没有异常;
    }

    /**
     * 出售
     */
    @Handler(PtCode.NORMAL_ITEM_SELL_CLIENT)
    private void sell(Player player, PbProtocol.NormalItemSellReq req, long time) {
        PbProtocol.NormalItemSellRst.Builder rst = PbProtocol.NormalItemSellRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        logic:
        {
            //待出售集合，key：物品模板，value：出售数量
            Map<NormalItemTemplate, Integer> sellMap = new HashMap<>();
            int totalCount = 0;
            boolean isBatch = req.getItemsCount() > 1;
            for (PbProtocol.NormalItemSellReq.Item item : req.getItemsList()) {
                NormalItemTemplate template = (NormalItemTemplate) ItemService.getItemTemplate(item.getTemplateId());
                if (sellMap.containsKey(template)) {
                    //这里说明客户端发来的数据，把同一个物品出售信息发了两次，认为是作弊的情况，抛出异常
                    throw new IllegalArgumentException("normalItem sell duplicate item, player:" + player.getPlayerId());
                }
                int sellCount = item.getCount();
                if (!isBatch) {
                    if (sellCount < 1 || sellCount > ConstantService.getInt(ConstantType.道具单个出售上限, 50)) {
                        rst.setResult(Text.genServerRstInfo(Text.出售物品数量异常));
                        break logic;
                    }
                }

                totalCount += sellCount;
                if (totalCount < 1 || totalCount > ConstantService.getInt(ConstantType.道具批量出售上限, 50)) {
                    rst.setResult(Text.genServerRstInfo(Text.出售物品数量异常));
                    break logic;
                }
                int ownCount = player.getBagModel().getItemCount(item.getTemplateId());
                if (ownCount > 0 && template.canSell()) {
                    sellMap.put(template, MathUtil.min(ownCount, item.getCount()));
                }
            }

            if (CollectionUtil.isNotEmpty(sellMap)) {
                Map<String, Long> sellRewardList = new HashMap<>();
                for (Map.Entry<NormalItemTemplate, Integer> entry : sellMap.entrySet()) {
                    int sellCount = entry.getValue();
                    NormalItemTemplate template = entry.getKey();

                    //扣除物品
                    player.getBagModel().removeItem(template.getId(), sellCount, BehaviorType.NormalItemSell);
                    for (RewardTemplate rewardTemplate : template.sellPrice) {
                        String itemId = rewardTemplate.itemTemplateId + "_" + rewardTemplate.type;
                        sellRewardList.put(itemId, sellRewardList.getOrDefault(itemId, 0L) + rewardTemplate.value * sellCount);
                    }
                }

                if (!sellRewardList.isEmpty()) {
                    List<RewardTemplate> rewardTemplateList = new ArrayList<>();
                    for (String itemId : sellRewardList.keySet()) {
                        String[] s = itemId.split("_");
                        rewardTemplateList.add(new RewardTemplate(Integer.parseInt(s[1]), Integer.parseInt(s[0]), sellRewardList.get(itemId)));
                    }
                    List<Reward> rewards = Reward.templateCollectionToReward(rewardTemplateList);
                    Reward.merge(rewards);
                    Reward.add(rewards, player, BehaviorType.NormalItemSell);
                    //返回
                    rst.addAllRewards(Reward.writeCollectionToPb(rewards));
                }
            }
        }
        player.send(PtCode.NORMAL_ITEM_SELL_SERVER, rst.build(), time);
    }

    @SuppressWarnings("unchecked")
    @Override
    public Set<Class<? extends Service>> preServices() {
        return Sets.newHashSet(ItemService.class, DropService.class);
    }

}

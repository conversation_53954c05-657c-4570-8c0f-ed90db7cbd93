package com.gy.server.game.smallGroup.bean.makeAcquaintances;

/**
 * 结识类型
 * <AUTHOR> 2025/1/2 16:53
 **/
public enum SmallGroupMakeAcquaintancesEnum {

    /**
     * 1、结拜-个人发布
     */
    brotherPerson(1),

    /**
     * 2、结拜-金兰发布
     */
    brotherGroup(2),

    /**
     * 3、结婚
     */
    marry(3),

    /**
     * 4、师徒找徒弟
     */
    findDisciple(4),

    /**
     * 5、徒弟找师傅
     */
    findMaster(5),
    ;

    int type;

    SmallGroupMakeAcquaintancesEnum(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }

    public static SmallGroupMakeAcquaintancesEnum getSmallGroupMakeAcquaintancesEnum(int type){
        switch (type){
            case 1: return brotherPerson;
            case 2: return brotherGroup;
            case 3: return marry;
            case 4: return findDisciple;
            case 5: return findMaster;
            default : return null;
        }
    }
}

package com.gy.server.game.smallGroup.async.marry;

import com.gy.server.common.distributedlock.DistributedLockUtilManager;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.SmallGroupGlobalData;
import com.gy.server.game.smallGroup.SmallGroupService;
import com.gy.server.game.smallGroup.async.SmallGroupInfoSyncAsync;
import com.gy.server.game.smallGroup.bean.PlayerSmallGroupIdInfo;
import com.gy.server.game.smallGroup.bean.marry.SmallGroupMarryInfo;
import com.gy.server.game.team.TeamManager;
import com.gy.server.game.team.TeamService;
import com.gy.server.game.team.enums.TeamStageEnums;
import com.gy.server.game.team.template.TeamTemplate;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.thread.ThreadPool;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 情缘解散 异步处理
 *
 * <AUTHOR> 2025/3/1 11:26
 **/
public class SmallGroupMarryDisbandAsync extends AsyncCall {

    private Player player;
    private boolean force;
    private long time;

    private int text = Text.没有异常;
    private List<Reward> rewardList;

    public SmallGroupMarryDisbandAsync(Player player, PbProtocol.SmallGroupMarryDisbandReq req, long time) {
        this.player = player;
        this.force = req.getForce();
        this.time = time;

        if(force){
            List<RewardTemplate> rewardTemplateList = SmallGroupService.getSmallGroupConst().mandatoryDivorceConsume;
            rewardList = Reward.templateCollectionToReward(rewardTemplateList);
            if(Reward.check(player, rewardList) != -1){
                text = Text.消耗不足;
                return;
            }

            Reward.remove(rewardList, player, BehaviorType.smallGroupMarryDisband);
        }
    }

    @Override
    public void execute() {
        PbProtocol.SmallGroupMarryDisbandRst.Builder rst = PbProtocol.SmallGroupMarryDisbandRst.newBuilder().setResult(Text.genServerRstInfo(text));
        if(text != Text.没有异常 && CollectionUtil.isNotEmpty(rewardList)){
            Reward.add(rewardList, player, BehaviorType.smallGroupMarryDisbandFailBack);
        }

        player.send(PtCode.SMALL_GROUP_MARRY_DISBAND_RST, rst.build(), time);
    }

    @Override
    public void asyncExecute() {
        if(text != Text.没有异常){
            return;
        }

        if(!force){
            Pair<Long, Integer> teamIdByPlayerId = TeamManager.getInstance().getTeamIdByPlayerId(player.getPlayerId());
            if (teamIdByPlayerId == null) {
                text = Text.组队你不在队伍;
                return;
            }

            TeamTemplate teamTemplate = TeamService.getTeamConfigs().get(teamIdByPlayerId.getRight());
            if (Objects.isNull(teamTemplate)) {
                text = Text.对应的模板数据找不到;
                return;
            }

            if (teamTemplate.stageEnums != TeamStageEnums.SmallGroupMarryLeave) {
                text = Text.组队类型不对;
                return;
            }
        }

        String groupIdInfoRedisKey = GsRedisKey.SmallGroup.player_small_group_id_info.getRedisKey(player.getPlayerId());
        PlayerSmallGroupIdInfo idInfo = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, groupIdInfoRedisKey);
        if(idInfo == null || (idInfo != null && idInfo.getMarryId() == 0)){
            text = Text.当前没有情缘;
            return;
        }

        String marryRedisKey = GsRedisKey.SmallGroup.small_group_info.getRedisKey(idInfo.getMarryId());
        DistributedLockUtilManager.operate(() -> {
            SmallGroupMarryInfo marryInfo = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupMarryInfo.class, marryRedisKey);
            if(marryInfo == null){
                text = Text.当前没有情缘;
                return;
            }

            if(marryInfo.getPeriod() > 0l){
                text = Text.情缘正在解散冷静期;
                return;
            }

            if(force){
                //强制解散
                int second = SmallGroupService.getSmallGroupConst().mandatoryDivorceResolutionTime;
                marryInfo.setPeriod(ServerConstants.getCurrentTimeMillis() + second * DateTimeUtil.MillisOfSecond);
            }else {
                //协议解散
                if(!marryInfo.getAgreeList().contains(player.getPlayerId())){
                    marryInfo.getAgreeList().add(player.getPlayerId());

                    if(marryInfo.getAgreeList().size() == marryInfo.getMemberList().size()){
                        int second = SmallGroupService.getSmallGroupConst().agreementDivorceResolutionTime;
                        marryInfo.setPeriod(ServerConstants.getCurrentTimeMillis() + second * DateTimeUtil.MillisOfSecond);
                    }
                }
            }

            TLBase.getInstance().getRedisAssistant().setBean(marryRedisKey, marryInfo);
            ThreadPool.execute(new SmallGroupInfoSyncAsync(marryInfo));

            SmallGroupGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroup);
            globalData.getLeaveMap().computeIfAbsent(player.getPlayerId(), map -> new ConcurrentHashMap<>()).put(marryInfo.getId(), marryInfo.getType().getType());
        }, marryRedisKey);
    }
}

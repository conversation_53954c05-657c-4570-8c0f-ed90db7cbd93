package com.gy.server.game.smallGroup.async.brother.create;

import com.gy.server.common.distributedlock.DistributedLockUtilManager;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.SmallGroupGlobalData;
import com.gy.server.game.smallGroup.SmallGroupHelper;
import com.gy.server.game.smallGroup.SmallGroupService;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherName;
import com.gy.server.game.smallGroup.bean.SmallGroupTypeEnum;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherCreate;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbSmallGroup;
import com.ttlike.server.tl.baselib.TLBase;

/**
 * 金兰创建金兰名号异步处理
 *
 * <AUTHOR> 2024/12/18 17:29
 **/
public class SmallGroupBrotherCreateNameAsync extends AsyncCall {

    private Player player;
    private SmallGroupBrotherName brotherName;
    private long time;

    private long createId;

    private SmallGroupBrotherCreate brotherCreate;
    private int text = Text.没有异常;

    public SmallGroupBrotherCreateNameAsync(Player player, PbProtocol.SmallGroupBrotherCreateNameReq req, long time){
        this.player = player;
        this.time = time;
        brotherName = new SmallGroupBrotherName(req.getFirst(), req.getSecond(), req.getThird(), req.getFourth(), 0);

        SmallGroupGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroup);
        this.createId = globalData.getCreateSignInfoIdByPlayerId(player.getPlayerId(), SmallGroupTypeEnum.brother);
    }

    @Override
    public void execute() {
        PbProtocol.SmallGroupBrotherCreateNameRst.Builder rst = PbProtocol.SmallGroupBrotherCreateNameRst.newBuilder().setResult(Text.genServerRstInfo(text));
        player.send(PtCode.SMALL_GROUP_BROTHER_CREATE_NAME_RST, rst.build(), time);

        if(text == Text.没有异常){
            SmallGroupHelper.smallGroupCreateInfoNotify(brotherCreate);
        }
    }

    @Override
    public void asyncExecute() {
        if (createId == 0) {
            text = Text.金兰创建未进行;
            return;
        }

        String redisKey = GsRedisKey.SmallGroup.small_group_create.getRedisKey(createId);
        DistributedLockUtilManager.operate(() -> {
            this.brotherCreate = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupBrotherCreate.class, redisKey);
            if (brotherCreate == null) {
                text = Text.金兰创建未进行;
                return;
            }

            if(player.getPlayerId() != brotherCreate.getLeaderId()){
                text = Text.当前仅队长能操作;
                return;
            }

            if (brotherCreate.getType() != SmallGroupTypeEnum.brother) {
                text = Text.组队类型不对;
                return;
            }

            if (brotherCreate.getNodeType() != PbSmallGroup.SgbNodeType.sgbName) {
                text = Text.金兰前置任务未完成;
                return;
            }

            int check = SmallGroupService.smallGroupBrotherNameCheck(brotherName);
            if (check != Text.没有异常) {
                text = check;
                return;
            }

            brotherName.setNum(brotherCreate.getMemberList().size());
            brotherCreate.setName(brotherName);
            brotherCreate.sgbNodeTypeNext();
            TLBase.getInstance().getRedisAssistant().setBean(redisKey, brotherCreate);
        }, redisKey);


    }
}

package com.gy.server.game.smallGroup.bean.brother;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.smallGroup.SmallGroupService;
import com.gy.server.game.smallGroup.template.SmallGroupBrotherResolutionEnum;
import com.gy.server.packet.PbSmallGroup;
import com.gy.server.utils.time.DateTimeUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * 金兰决议抽象类
 *
 * <AUTHOR> 2024/12/26 15:49
 **/
@ProtobufClass
public class SmallGroupBrotherResolutionBase {

    @Protobuf(order = 1)
    private long id;

    /**
     * 决议过期时间
     */
    @Protobuf(order = 2)
    private long end;

    @Protobuf(order = 3)
    private int type;

    /**
     * 发起者id
     * 有可能不是自己金兰内发布的
     */
    @Protobuf(order = 4)
    private long initiatorId;

    /**
     * 发起者名称
     * 有可能不是自己金兰内发布的
     */
    @Protobuf(order = 5)
    private String initiatorName;

    /**
     * 成员投票map
     * value: -1反对  0弃权  1同意
     */
    @Protobuf(order = 6)
    private Map<Long, Integer> voteMap = new HashMap<>();

    /**
     * 决议结果
     * 0无结果  1成功  2失败
     */
    @Protobuf(order = 7)
    private int voteResult;

    public SmallGroupBrotherResolutionBase() {
    }

    public SmallGroupBrotherResolutionBase(SmallGroupBrotherResolutionEnum type, long initiatorId, String initiatorName) {
        this.id = ServerConstants.getCurrentTimeMillis();
        this.end = ServerConstants.getCurrentTimeMillis() + SmallGroupService.getSmallGroupConst().swornBrothersResolutionTime * DateTimeUtil.MillisOfSecond;
        this.type = type.getType();
        this.initiatorId = initiatorId;
        this.initiatorName = initiatorName;
    }

    public PbSmallGroup.SmallGroupBrotherResolution genPb(long brotherId){
        PbSmallGroup.SmallGroupBrotherResolution.Builder builder = PbSmallGroup.SmallGroupBrotherResolution.newBuilder();
        builder.setId(id);
        builder.setEnd(end);
        builder.setType(type);
        builder.setInitiatorId(initiatorId);
        builder.setInitiatorName(initiatorName);
        builder.putAllVoteMap(voteMap);
        builder.setVoteResult(voteResult);
        writePb(builder, brotherId);
        return builder.build();
    }

    public void writePb(PbSmallGroup.SmallGroupBrotherResolution.Builder builder, long brotherId){

    }

    public SmallGroupBrotherResolutionEnum getSmallGroupBrotherResolutionEnum(){
        return SmallGroupBrotherResolutionEnum.getSmallGroupBrotherResolutionEnum(type);
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getEnd() {
        return end;
    }

    public void setEnd(long end) {
        this.end = end;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public long getInitiatorId() {
        return initiatorId;
    }

    public void setInitiatorId(long initiatorId) {
        this.initiatorId = initiatorId;
    }

    public String getInitiatorName() {
        return initiatorName;
    }

    public void setInitiatorName(String initiatorName) {
        this.initiatorName = initiatorName;
    }

    public Map<Long, Integer> getVoteMap() {
        return voteMap;
    }

    public void setVoteMap(Map<Long, Integer> voteMap) {
        this.voteMap = voteMap;
    }

    public int getVoteResult() {
        return voteResult;
    }

    public void setVoteResult(int voteResult) {
        this.voteResult = voteResult;
    }
}

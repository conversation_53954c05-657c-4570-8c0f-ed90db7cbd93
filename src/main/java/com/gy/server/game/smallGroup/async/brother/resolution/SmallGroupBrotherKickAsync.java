package com.gy.server.game.smallGroup.async.brother.resolution;

import com.gy.server.common.distributedlock.DistributedLockUtilManager;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.game.smallGroup.SmallGroupService;
import com.gy.server.game.smallGroup.async.SmallGroupInfoSyncAsync;
import com.gy.server.game.smallGroup.bean.PlayerSmallGroupIdInfo;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherInfo;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherResolutionKick;
import com.gy.server.game.smallGroup.template.SmallGroupBrotherResolutionEnum;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

/**
 * 金兰踢人异步处理
 * <AUTHOR> 2025/1/2 11:47
 **/
public class SmallGroupBrotherKickAsync extends AsyncCall {

    private Player player;
    private long targetId;
    private long time;

    private int text = Text.没有异常;

    public SmallGroupBrotherKickAsync(Player player, PbProtocol.SmallGroupBrotherKickReq req, long time) {
        this.player = player;
        this.targetId = req.getTargetId();
        this.time = time;
    }

    @Override
    public void execute() {
        PbProtocol.SmallGroupBrotherKickRst.Builder rst = PbProtocol.SmallGroupBrotherKickRst.newBuilder().setResult(Text.genServerRstInfo(text));
        player.send(PtCode.SMALL_GROUP_BROTHER_KICK_RST, rst.build(), time);
    }

    @Override
    public void asyncExecute() {
        String redisKey = GsRedisKey.SmallGroup.player_small_group_id_info.getRedisKey(player.getPlayerId());
        PlayerSmallGroupIdInfo playerSmallGroupIdInfo = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, redisKey);
        if(playerSmallGroupIdInfo == null){
            text = Text.金兰不存在;
            return;
        }

        if(playerSmallGroupIdInfo.getBrotherId() == 0){
            text = Text.金兰不存在;
            return;
        }

        MiniGamePlayer targetPlayer = PlayerHelper.getMiniPlayer(targetId);
        if(targetPlayer == null){
            text = Text.玩家不存在;
            return;
        }

        String redisKeyTemp = GsRedisKey.SmallGroup.small_group_info.getRedisKey(playerSmallGroupIdInfo.getBrotherId());
        DistributedLockUtilManager.operate(() -> {
            SmallGroupBrotherInfo brotherInfo = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupBrotherInfo.class, redisKeyTemp);
            if(brotherInfo == null){
                text = Text.金兰不存在;
                return;
            }

            if(!brotherInfo.getMemberMap().containsKey(targetId)){
                text = Text.玩家不在金兰内;
                return;
            }

            if(brotherInfo.getMemberMap().size() - 1 < SmallGroupService.getSmallGroupConst().swornBrothersMixNum){
                text = Text.请离后不满足金兰最小人数;
                return;
            }

            int textTemp = brotherInfo.canAddResolution(SmallGroupBrotherResolutionEnum.kick, player.getPlayerId());
            if(textTemp != Text.没有异常){
                text = textTemp;
                return;
            }

            MiniGamePlayer miniGamePlayer = PlayerHelper.getMiniPlayer(targetId);
            SmallGroupBrotherResolutionKick resolutionKick = new SmallGroupBrotherResolutionKick(player, targetId, miniGamePlayer == null ? "" : miniGamePlayer.getName());
            brotherInfo.addResolution(resolutionKick);

            TLBase.getInstance().getRedisAssistant().setBean(redisKeyTemp, brotherInfo);

            ThreadPool.execute(new SmallGroupInfoSyncAsync(brotherInfo));
        }, redisKeyTemp);
    }
}

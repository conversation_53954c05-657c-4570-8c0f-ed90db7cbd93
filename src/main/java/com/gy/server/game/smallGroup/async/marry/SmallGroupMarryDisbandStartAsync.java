package com.gy.server.game.smallGroup.async.marry;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.Configuration;
import com.gy.server.core.callback.response.CallbackResponse;
import com.gy.server.core.command.PlayerCommandRequest;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.SmallGroupHelper;
import com.gy.server.game.smallGroup.bean.PlayerSmallGroupIdInfo;
import com.gy.server.game.smallGroup.bean.marry.SmallGroupMarryInfo;
import com.gy.server.game.team.TeamManager;
import com.gy.server.game.team.TeamService;
import com.gy.server.game.team.enums.TeamStageEnums;
import com.gy.server.game.team.template.TeamTemplate;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.world.team.base.TeamInfo;
import com.gy.server.world.team.base.TeamMemberInfo;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.rpc.TLMessageCallbackTask;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 情缘解散开始异步处理
 *
 * <AUTHOR> 2025/6/5 18:06
 **/
public class SmallGroupMarryDisbandStartAsync extends AsyncCall {

    private Player player;
    private long time;
    private int text = Text.没有异常;

    public SmallGroupMarryDisbandStartAsync(Player player, long time) {
        this.player = player;
        this.time = time;
    }

    @Override
    public void execute() {
        PbProtocol.SmallGroupMarryDisbandStartRst rst = PbProtocol.SmallGroupMarryDisbandStartRst.newBuilder().setResult(Text.genServerRstInfo(text)).build();
        player.send(PtCode.SMALL_GROUP_MARRY_DISBAND_START_RST, rst, time);
    }

    @Override
    public void asyncExecute() {
        logic:
        {
            Pair<Long, Integer> teamIdByPlayerId = TeamManager.getInstance().getTeamIdByPlayerId(player.getPlayerId());
            if (teamIdByPlayerId == null) {
                text = Text.组队你不在队伍;
                break logic;
            }

            TeamTemplate teamTemplate = TeamService.getTeamConfigs().get(teamIdByPlayerId.getRight());
            if (Objects.isNull(teamTemplate)) {
                text = Text.对应的模板数据找不到;
                break logic;
            }

            if (teamTemplate.stageEnums != TeamStageEnums.SmallGroupMarryLeave) {
                text = Text.组队类型不对;
                break logic;
            }

            if (teamTemplate.crossType == 0) {
                TeamInfo teamInfo = TeamManager.getInstance().getTeamInfo(teamIdByPlayerId.getLeft());
                if (teamInfo == null) {
                    text = Text.组队队伍不存在;
                    break logic;
                }


//                List<TeamMemberInfo> memberInfoList = new ArrayList<>();
//                for (Long memberId : TeamManager.getInstance().getMembers(teamInfo.getTeamId())) {
//                    TeamMemberInfo memberInfo = TeamManager.getInstance().getMemberInfo(memberId);
//                    if (memberInfo != null) {
//                        memberInfoList.add(memberInfo);
//                    }
//                }

                Set<Long> set = TeamManager.getInstance().getMembers(teamInfo.getTeamId());
                disbandStart(player, teamInfo.getLeaderId(), set);
            } else {
                int masterWorldId = CommonUtils.getWorldMasterServerId();
                ServerCommandRequest request = CommandRequests.newServerCommandRequest("WorldTeamCommandService.getTeamRelateInfo");
                TLBase.getInstance().getRpcUtil().sendToNodeWithCallBack(new ExecuteCallbackTask(), ServerType.WORLD, masterWorldId, request, teamIdByPlayerId.getLeft());
            }
            return;
        }


    }

    private void disbandStart(Player player, long leaderId, Collection<Long> set) {
        if(player.getPlayerId() != leaderId){
            text = Text.当前仅队长能操作;
            return;
        }

        String idInfoKey = GsRedisKey.SmallGroup.player_small_group_id_info.getRedisKey(player.getPlayerId());
        PlayerSmallGroupIdInfo idInfo = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, idInfoKey);
        if(idInfo == null || (idInfo != null && idInfo.getMarryId() == 0)){
            text = Text.当前没有情缘;
            return;
        }

        String marryKey = GsRedisKey.SmallGroup.small_group_info.getRedisKey(idInfo.getMarryId());
        SmallGroupMarryInfo marryInfo = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupMarryInfo.class, marryKey);
        if(marryInfo == null){
            text = Text.当前没有情缘;
            return;
        }

        List<Long> marryMemberList = marryInfo.getMemberList();
        if(!(marryMemberList.containsAll(set) && marryMemberList.size() == set.size())){
            text = Text.情缘组队数量不对;
            return;
        }

        //同步
        PbProtocol.SmallGroupMarryDisbandStartNotify notify = PbProtocol.SmallGroupMarryDisbandStartNotify.newBuilder().setId(marryInfo.getId()).build();
        for (long playerId : marryMemberList) {
            int serverId = Player.getServerId(playerId);
            if(serverId == Configuration.serverId){
                SmallGroupHelper.marryDisbandStartNotify(playerId, notify);
            }else {
                PlayerCommandRequest request = CommandRequests.newPlayerCommandRequest("SmallGroupRstCommandService.marryDisbandStartNotify", playerId);
                TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, serverId, request, notify);
            }
        }
    }

    private class ExecuteCallbackTask extends TLMessageCallbackTask {
        @Override
        public void complete(CallbackResponse callbackResponse) {
            long leaderId = callbackResponse.getParam(0);
            List<TeamMemberInfo> memberInfoList = callbackResponse.getParam(1);

            disbandStart(player, leaderId, memberInfoList.stream().map(TeamMemberInfo::getPlayerId).collect(Collectors.toList()));
        }

        @Override
        public void timeout() {
            text = Text.金兰请求超时;
        }
    }
}

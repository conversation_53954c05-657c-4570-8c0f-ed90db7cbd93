package com.gy.server.game.smallGroup.async.marry;

import com.gy.server.common.distributedlock.DistributedLockUtilManager;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.game.activity.ActivityModule;
import com.gy.server.game.activity.ActivityType;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.newFriend.FriendGlobalData;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.SmallGroupGlobalData;
import com.gy.server.game.smallGroup.SmallGroupHelper;
import com.gy.server.game.smallGroup.bean.SmallGroupCreateSignInfo;
import com.gy.server.game.smallGroup.bean.SmallGroupTypeEnum;
import com.gy.server.game.smallGroup.bean.marry.SmallGroupMarryCreate;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbSmallGroup;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.world.activityTeam.ActivityTeamHelper;
import com.gy.server.world.activityTeam.ActivityTeamManager;
import com.gy.server.world.activityTeam.bean.ActivityTeam;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.thread.ThreadPool;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Collection;
import java.util.List;

import static com.gy.server.game.activity.team.ActivityTeamService.getLocalWarZoneId;

/**
 * 情缘邀请宾客 异步处理
 *
 * <AUTHOR> 2025/2/19 14:00
 **/
public class SmallGroupMarryInviteGuestsAsync extends AsyncCall {
    private final int 无需下行 = -9999;

    private Player player;
    private long beInviterId;
    private long time;

    private int text = Text.没有异常;
    private PbSmallGroup.InviteFunction function;

    public SmallGroupMarryInviteGuestsAsync(Player player, PbProtocol.SmallGroupMarryInviteGuestsReq req, long time) {
        this.player = player;
        this.function = req.getFunction();
        this.beInviterId = req.getBeInviterId();
        this.time = time;
    }

    @Override
    public void execute() {
        if(text != 无需下行){
            responseAsync(text);
        }
    }

    private void responseAsync(int code){
        PbProtocol.SmallGroupMarryInviteGuestsRst.Builder rst = PbProtocol.SmallGroupMarryInviteGuestsRst.newBuilder().setResult(Text.genServerRstInfo(code));
        rst.setFunction(function);
        player.sendAsync(PtCode.SMALL_GROUP_MARRY_INVITE_GUESTS_RST, rst.build(), time);
    }

    private void inviteSmallGroup(){
        SmallGroupGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroup);
        SmallGroupCreateSignInfo createSignInfo = globalData.getCreateSignInfo(player.getPlayerId(), SmallGroupTypeEnum.marry);
        if (createSignInfo == null || (createSignInfo != null && createSignInfo.getId() == 0)) {
            text = Text.情缘当前无创建;
            return;
        }

        String redisKey = GsRedisKey.SmallGroup.small_group_create.getRedisKey(createSignInfo.getId());
        DistributedLockUtilManager.operate(() -> {
            SmallGroupMarryCreate marryCreate = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupMarryCreate.class, redisKey);
            if (marryCreate.getInviteIdSet().contains(beInviterId)) {
                text = Text.已经邀请过该玩家;
                return;
            }

            if (marryCreate.getMemberList().contains(beInviterId)) {
                text = Text.参数异常;
                return;
            }

            if (marryCreate.getInviteIdSet().size() >= marryCreate.getInviteNum()) {
                text = Text.当前阶段不能放烟花;
                return;
            }

            marryCreate.getInviteIdSet().add(beInviterId);
            TLBase.getInstance().getRedisAssistant().setBean(redisKey, marryCreate);

            SmallGroupHelper.smallGroupCreateInfoNotify(marryCreate);
            SmallGroupHelper.marryInviteNotify(beInviterId, marryCreate.getId(), function, player.getPlayerId());
        }, redisKey);
    }

    private void inviteActivityTeam(){
        //无量秘境、四人组队
        int activityId = ActivityTeamHelper.getOpenActivityID(player);
        if (activityId > 0) {
            ActivityTeamHelper.business(
                    errorCode -> {
                        responseAsync(errorCode);
                    },
                    response -> {
                        int code = response.getParam(0);
                        if (code == Text.没有异常) {
                            long teamId = response.getParam(1);
                            SmallGroupHelper.marryInviteNotify(beInviterId, teamId, function, player.getPlayerId());
                        }

                        responseAsync(code);

                    },
                    () -> {
                        //本地逻辑
                        Pair<Integer, Long> pair = ActivityTeamHelper.invitePlayer(player.getPlayerId(), beInviterId, activityId, getLocalWarZoneId());
                        if (pair.getLeft() == Text.没有异常) {
                            SmallGroupHelper.marryInviteNotify(beInviterId, pair.getRight(), function, player.getPlayerId());
                            responseAsync(Text.没有异常);
                        } else {
                            responseAsync(pair.getLeft());
                        }
                    }
                    , player, activityId, "ActivityTeamWorldCommandService.invitePlayer");
            text = 无需下行;
            return;
        }

        text = Text.参数异常;
    }

    @Override
    public void asyncExecute() {
        //检查是否是好友
        FriendGlobalData friendGlobalData = GlobalDataManager.getData(GlobalDataType.friend);
        League league = LeagueManager.getLeagueByPlayer(player);
        //不是好友也不是同帮派成员
        boolean isLeague = league != null && league.getMemberMap().containsKey(beInviterId);
        if (!friendGlobalData.isFriend(player.getPlayerId(), beInviterId) && !isLeague) {
            text = Text.参数异常;
            return;
        }

        if (function == PbSmallGroup.InviteFunction.SmallGroup) {
            inviteSmallGroup();
        } else if (function == PbSmallGroup.InviteFunction.InfiniteRealm) {
            inviteActivityTeam();
        }
    }
}

package com.gy.server.game.smallGroup.async.marry;

import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.callback.response.CallbackResponse;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.SmallGroupGlobalData;
import com.gy.server.game.smallGroup.bean.SmallGroupCreateSignInfo;
import com.gy.server.game.smallGroup.bean.SmallGroupTypeEnum;
import com.gy.server.game.smallGroup.bean.marry.MarryCruiseApplyInfo;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.rpc.TLMessageCallbackTask;

import java.util.List;

/**
 * 情缘巡游申请信息 异步处理
 *
 * <AUTHOR> 2025/2/11 16:54
 **/
public class SmallGroupMarryCruiseApplyInfoAsync extends AsyncCall {

    private Player player;
    private long time;

    public SmallGroupMarryCruiseApplyInfoAsync(Player player, long time) {
        this.player = player;
        this.time = time;
    }

    @Override
    public void execute() {
        PbProtocol.SmallGroupMarryCruiseApplyInfoRst.Builder rst = PbProtocol.SmallGroupMarryCruiseApplyInfoRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            int masterWorldId = CommonUtils.getWorldMasterServerId();
            if(masterWorldId == 0){
                rst.setResult(Text.genServerRstInfo(Text.巡游请求超时));
                break logic;
            }

            SmallGroupGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroup);
            SmallGroupCreateSignInfo createSignInfo = globalData.getCreateSignInfo(player.getPlayerId(), SmallGroupTypeEnum.marry);
            if(createSignInfo != null){
                rst.setCreateId(createSignInfo.getId());
            }

            ServerCommandRequest request = CommandRequests.newServerCommandRequest("SmallGroupWorldCommandService.cruiseApplyInfo");
            TLBase.getInstance().getRpcUtil().sendToNodeWithCallBack(new ExecuteCallbackTask(rst), ServerType.WORLD, masterWorldId, request);
        }

        player.send(PtCode.SMALL_GROUP_MARRY_CRUISE_APPLY_INFO_RST, rst.build(), time);
    }

    @Override
    public void asyncExecute() {

    }

    private static class ExecuteCallbackTask extends TLMessageCallbackTask {
        private final PbProtocol.SmallGroupMarryCruiseApplyInfoRst.Builder rst;

        public ExecuteCallbackTask(PbProtocol.SmallGroupMarryCruiseApplyInfoRst.Builder rst) {
            this.rst = rst;
        }

        @Override
        public void complete(CallbackResponse response) {
            List<MarryCruiseApplyInfo> list = response.getParam(0);
            for (MarryCruiseApplyInfo applyInfo : list) {
                rst.addApplyInfo(applyInfo.genPb());
            }
        }

        @Override
        public void timeout() {
            rst.setResult(Text.genServerRstInfo(Text.巡游请求超时));
        }
    }
}

package com.gy.server.game.smallGroup.async.brother.create;

import com.gy.server.common.distributedlock.DistributedLockUtilManager;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.SmallGroupGlobalData;
import com.gy.server.game.smallGroup.SmallGroupHelper;
import com.gy.server.game.smallGroup.bean.SmallGroupTypeEnum;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherCreate;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbSmallGroup;
import com.ttlike.server.tl.baselib.TLBase;

/**
 * 金兰成员职位投票异步处理
 *
 * <AUTHOR> 2024/12/18 19:38
 **/
public class SmallGroupBrotherMemberJobVoteAsync extends AsyncCall {

    private Player player;
    private long targetPlayerId;
    private long time;

    private long createId;

    private SmallGroupBrotherCreate brotherCreate;
    private int text = Text.没有异常;


    public SmallGroupBrotherMemberJobVoteAsync(Player player, PbProtocol.SmallGroupBrotherMemberJobVoteReq req, long time){
        this.player = player;
        this.targetPlayerId = req.getTargetPlayerId();
        this.time = time;

        SmallGroupGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroup);
        this.createId = globalData.getCreateSignInfoIdByPlayerId(player.getPlayerId(), SmallGroupTypeEnum.brother);
    }

    @Override
    public void execute() {
        PbProtocol.SmallGroupBrotherMemberJobVoteRst.Builder rst = PbProtocol.SmallGroupBrotherMemberJobVoteRst.newBuilder().setResult(Text.genServerRstInfo(text));
        player.send(PtCode.SMALL_GROUP_BROTHER_MEMBER_JOB_VOTE_RST, rst.build(), time);
    }

    @Override
    public void asyncExecute() {
        if (createId == 0) {
            text = Text.金兰创建未进行;
            return;
        }

        String redisKey = GsRedisKey.SmallGroup.small_group_create.getRedisKey(createId);
        DistributedLockUtilManager.operate(() -> {
            this.brotherCreate = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupBrotherCreate.class, redisKey);

            if (brotherCreate == null) {
                text = Text.金兰创建未进行;
                return;
            }

            if (brotherCreate.getType() != SmallGroupTypeEnum.brother) {
                text = Text.组队类型不对;
                return;
            }

            if (brotherCreate.getNodeType() != PbSmallGroup.SgbNodeType.sgbSort) {
                text = Text.金兰前置任务未完成;
                return;
            }

            if (brotherCreate.getVoteMap().containsKey(player.getPlayerId())) {
                text = Text.金兰已投票;
                return;
            }

            brotherCreate.getVoteMap().put(player.getPlayerId(), targetPlayerId);
            TLBase.getInstance().getRedisAssistant().setBean(redisKey, brotherCreate);

            SmallGroupHelper.smallGroupCreateInfoNotify(brotherCreate);
        }, redisKey);
    }
}

package com.gy.server.game.smallGroup.bean.makeAcquaintances;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.packet.PbSmallGroup;

/**
 * 申请信息
 * <AUTHOR> 2025/1/3 15:35
 **/
@ProtobufClass
public class MakeAcquaintancesApplyInfo {

    /**
     * platform 表id
     */
    @Protobuf(order = 1)
    private int id;

    /**
     * 发布人id
     */
    @Protobuf(order = 2)
    private long senderId;

    /**
     * 申请人id
     */
    @Protobuf(order = 3)
    private long applyId;

    @Protobuf(order = 4)
    private long createTime = ServerConstants.getCurrentTimeMillis();

    /**
     * 状态 -1拒绝 0未处理 1同意
     */
    @Protobuf(order = 5)
    private int state = 0;

    public MakeAcquaintancesApplyInfo() {
    }

    public MakeAcquaintancesApplyInfo(int id, long senderId, long applyId) {
        this.id = id;
        this.senderId = senderId;
        this.applyId = applyId;
    }

    public String getKey(){
        return id + "" + applyId;
    }

    /**
     * 异步使用
     * @return
     */
    public PbSmallGroup.MakeAcquaintancesApplyInfo genMakeAcquaintancesApplyInfo(){
        PbSmallGroup.MakeAcquaintancesApplyInfo.Builder builder = PbSmallGroup.MakeAcquaintancesApplyInfo.newBuilder();
        builder.setId(id);
        builder.setSenderId(senderId);

        MiniGamePlayer miniGamePlayer = PlayerHelper.getMiniPlayer(applyId);
        builder.setMiniUserApplier(PlayerHelper.genMinMiniUser(miniGamePlayer));
        builder.setCreateTime(createTime);
        return builder.build();
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public long getSenderId() {
        return senderId;
    }

    public void setSenderId(long senderId) {
        this.senderId = senderId;
    }

    public long getApplyId() {
        return applyId;
    }

    public void setApplyId(long applyId) {
        this.applyId = applyId;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }
}

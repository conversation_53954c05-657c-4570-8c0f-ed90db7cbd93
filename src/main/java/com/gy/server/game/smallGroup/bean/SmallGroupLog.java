package com.gy.server.game.smallGroup.bean;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;
import com.gy.server.core.ServerConstants;
import com.gy.server.packet.PbSmallGroup;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 小团体日志
 *
 * <AUTHOR> 2025/1/23 16:34
 **/
@ProtobufClass
public class SmallGroupLog {

    /**
     * 日志类型
     */
    @Protobuf(order = 1)
    private SmallGroupLogTypeEnum logType;

    /**
     * 日志发生时间点
     */
    @Protobuf(order = 2)
    private long time = ServerConstants.getCurrentTimeMillis();

    @Protobuf(order = 3)
    private List<String> params = new ArrayList<>();

    /**
     * 0团队日志，其他成员id
     */
    @Protobuf(order = 4)
    private long belong = 0l;

    public SmallGroupLog() {
    }

    public SmallGroupLog(SmallGroupLogTypeEnum logType, long time, List<String> params) {
        this.logType = logType;
        this.time = time;
        this.params = params;
    }

    public SmallGroupLog(SmallGroupLogTypeEnum logType, long playerId, String... params){
        this.logType = logType;
        if(logType.getBelongType() == 2){
            this.belong = playerId;
        }
        this.params = Arrays.asList(params);
        this.time = ServerConstants.getCurrentTimeMillis();
    }

    public PbSmallGroup.SmallGroupLog genPb(){
        PbSmallGroup.SmallGroupLog.Builder builder = PbSmallGroup.SmallGroupLog.newBuilder();
        builder.setLogType(logType.getId());
        builder.setTime(time);
        builder.addAllParam(params);
        return builder.build();
    }

    public SmallGroupLogTypeEnum getLogType() {
        return logType;
    }

    public void setLogType(SmallGroupLogTypeEnum logType) {
        this.logType = logType;
    }

    public long getTime() {
        return time;
    }

    public void setTime(long time) {
        this.time = time;
    }

    public List<String> getParams() {
        return params;
    }

    public void setParams(List<String> params) {
        this.params = params;
    }

    public long getBelong() {
        return belong;
    }

    public void setBelong(long belong) {
        this.belong = belong;
    }
}

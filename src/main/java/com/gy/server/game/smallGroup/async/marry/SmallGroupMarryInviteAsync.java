package com.gy.server.game.smallGroup.async.marry;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.newFriend.FriendGlobalData;
import com.gy.server.game.newFriend.FriendHelper;
import com.gy.server.game.newFriend.bean.FriendPersonInfo;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.game.smallGroup.SmallGroupGlobalData;
import com.gy.server.game.smallGroup.SmallGroupHelper;
import com.gy.server.game.smallGroup.SmallGroupService;
import com.gy.server.game.smallGroup.bean.PlayerSmallGroupIdInfo;
import com.gy.server.game.smallGroup.bean.SmallGroupCreateSignInfo;
import com.gy.server.game.smallGroup.bean.SmallGroupInviteInfo;
import com.gy.server.game.smallGroup.bean.SmallGroupTypeEnum;
import com.gy.server.game.smallGroup.template.SmallGroupMarryProposeConsumeTemplate;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.TLBase;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 情缘邀请异步处理
 *
 * <AUTHOR> 2025/2/6 16:49
 **/
public class SmallGroupMarryInviteAsync extends AsyncCall {

    private Player player;
    private long targetId;
    private int giftId;
    private long time;

    private int text = Text.没有异常;
    private MiniGamePlayer miniGamePlayer;
    private List<Reward> cost = new ArrayList<>();

    public SmallGroupMarryInviteAsync(Player player, PbProtocol.SmallGroupMarryInviteReq req, long time) {
        this.player = player;
        this.targetId = req.getTargetId();
        this.giftId = req.getGiftId();
        this.time = time;
    }


    @Override
    public void execute() {
        logic:{
            if(miniGamePlayer == null){
                text = Text.玩家不存在;
                break logic;
            }

            if(player.getPlayerId() == targetId){
                text = Text.不能邀请自己;
                break logic;
            }

            //TODO 临时注释
//            //检查性别
//            if(player.getSex() == miniGamePlayer.getGender()){
//                text = Text.情缘需要不同性别;
//                break logic;
//            }

            SmallGroupGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroup);
            if(globalData.getCreateSignInfoMap().getOrDefault(player.getPlayerId(), new ConcurrentHashMap<>()).containsKey(SmallGroupTypeEnum.marry.getType())){
                SmallGroupCreateSignInfo createSignInfo = globalData.getCreateSignInfoMap().getOrDefault(player.getPlayerId(), new ConcurrentHashMap<>()).get(SmallGroupTypeEnum.marry.getType());
                if(ServerConstants.getCurrentTimeMillis() < createSignInfo.getOverTime()){
                    text = Text.当前有情缘正在创建;
                    break logic;
                }
            }

            if(globalData.getInviteInfoMap().containsKey(targetId)){
                ConcurrentHashMap<String, SmallGroupInviteInfo> mapTemp = globalData.getInviteInfoMap().get(targetId);
                String key = player.getPlayerId() + "" + SmallGroupTypeEnum.marry.getType();
                if(mapTemp.containsKey(key)){
                    SmallGroupInviteInfo inviteInfo = mapTemp.get(key);
                    if(ServerConstants.getCurrentTimeMillis() < inviteInfo.getEndTime()){
                        text = Text.已经邀请过该玩家;
                        break logic;
                    }
                }
            }

            FriendGlobalData friendGlobalData = GlobalDataManager.getData(GlobalDataType.friend);
            if(!friendGlobalData.isFriend(player.getPlayerId(), targetId)){
                text = Text.该玩家不是你的好友;
                break logic;
            }
            FriendPersonInfo personInfo = FriendHelper.getFriendPersonInfo(player.getPlayerId());
            //检查好感度
            if(personInfo.getLikabilityMap().getOrDefault(targetId, 0) < SmallGroupService.getSmallGroupConst().marryLikabilityMixNum){
                text = Text.好感度不足;
                break logic;
            }

            //检查是否有情缘
            String idKey1 = GsRedisKey.SmallGroup.small_group_info.getRedisKey(player.getPlayerId());
            PlayerSmallGroupIdInfo idInfo1 = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, idKey1);
            if(idInfo1 != null && idInfo1.getMarryId() != 0){
                text = Text.当前已有情缘;
                break logic;
            }

            String idKey2 = GsRedisKey.SmallGroup.small_group_info.getRedisKey(targetId);
            PlayerSmallGroupIdInfo idInfo2 = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, idKey2);
            if(idInfo2 != null && idInfo2.getMarryId() != 0){
                text = Text.目标已有情缘;
                break logic;
            }

            SmallGroupMarryProposeConsumeTemplate proposeConsumeTemplate = SmallGroupService.getMarryProposeConsumeTemplateMap().get(giftId);
            if(proposeConsumeTemplate == null){
                text = Text.对应的模板数据找不到;
                break logic;
            }

            List<Reward> cost = Reward.templateCollectionToReward(proposeConsumeTemplate.consume);
            if(Reward.check(player, cost) != -1){
                text = Text.消耗不足;
                break logic;
            }

            Reward.remove(cost, player, BehaviorType.smallGroupMarriageStart);
            //创建邀请信息
            long endTime = ServerConstants.getCurrentTimeMillis() + SmallGroupService.getSmallGroupConst().swornBrothersInviteTime * DateTimeUtil.MillisOfSecond;
            SmallGroupHelper.smallGroupInviteDeal(SmallGroupTypeEnum.marry, targetId, player.getPlayerId(), player.getName(), endTime, giftId);
        }

        PbProtocol.SmallGroupMarryInviteRst.Builder rst = PbProtocol.SmallGroupMarryInviteRst.newBuilder().setResult(Text.genServerRstInfo(text));
        player.send(PtCode.SMALL_GROUP_MARRY_INVITE_RST, rst.build(), time);
    }

    @Override
    public void asyncExecute() {
        miniGamePlayer = PlayerHelper.getMiniPlayer(targetId);
    }
}

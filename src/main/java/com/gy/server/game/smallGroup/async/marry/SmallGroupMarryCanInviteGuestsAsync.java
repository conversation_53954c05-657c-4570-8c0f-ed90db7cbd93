package com.gy.server.game.smallGroup.async.marry;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.newFriend.FriendGlobalData;
import com.gy.server.game.newFriend.bean.FriendInfo;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.game.smallGroup.SmallGroupGlobalData;
import com.gy.server.game.smallGroup.bean.SmallGroupCreateSignInfo;
import com.gy.server.game.smallGroup.bean.SmallGroupTypeEnum;
import com.gy.server.game.smallGroup.bean.marry.SmallGroupMarryCreate;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbSmallGroup;
import com.ttlike.server.tl.baselib.TLBase;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 情缘可邀请宾客 异步处理
 *
 * <AUTHOR> 2025/2/19 10:46
 **/
public class SmallGroupMarryCanInviteGuestsAsync extends AsyncCall {

    private Player player;
    private long time;

    private List<MiniGamePlayer> friend = new ArrayList<>();
    private List<MiniGamePlayer> league = new ArrayList<>();

    private Set<Long> playerIdSet = new HashSet<>();

    private int text = Text.没有异常;

    private PbSmallGroup.InviteFunction func;

    public SmallGroupMarryCanInviteGuestsAsync(Player player, PbSmallGroup.InviteFunction func, long time) {
        this.player = player;
        this.time = time;
        this.func = func;
    }

    @Override
    public void execute() {
        PbProtocol.SmallGroupMarryCanInviteGuestsRst.Builder builder = PbProtocol.SmallGroupMarryCanInviteGuestsRst.newBuilder().setResult(Text.genServerRstInfo(text));
        builder.setFunction(func);
        if(text == Text.没有异常){
            friend.forEach(bean -> builder.addFriend(bean.genMinMiniUser()));
            league.forEach(bean -> builder.addLeagueMember(bean.genMinMiniUser()));
        }
        player.send(PtCode.SMALL_GROUP_MARRY_CAN_INVITE_GUESTS_RST, builder.build(), time);
    }

    @Override
    public void asyncExecute() {
        Set<Long> excludePlayerIdSet = new HashSet<>();
        if(func == PbSmallGroup.InviteFunction.SmallGroup) {
            SmallGroupGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroup);
            SmallGroupCreateSignInfo createSignInfo = globalData.getCreateSignInfo(player.getPlayerId(), SmallGroupTypeEnum.marry);
            if (createSignInfo == null || (createSignInfo != null && createSignInfo.getId() == 0)) {
                text = Text.情缘当前无创建;
                return;
            }

            String redisKey = GsRedisKey.SmallGroup.small_group_create.getRedisKey(createSignInfo.getId());
            SmallGroupMarryCreate marryCreate = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupMarryCreate.class, redisKey);
            if (marryCreate == null) {
                text = Text.情缘当前无创建;
                return;
            }

            excludePlayerIdSet.addAll(marryCreate.getJobMap().keySet());
        }else{
            excludePlayerIdSet.add(player.getPlayerId());
        }

        FriendGlobalData friendGlobalData = GlobalDataManager.getData(GlobalDataType.friend);
        Collection<FriendInfo> friendInfoList = friendGlobalData.getFriendInfo(player.getPlayerId()).getFriendInfos().values();
        if(!friendInfoList.isEmpty()){
            check(friendInfoList.stream().map(FriendInfo::getFriendId).collect(Collectors.toList()), excludePlayerIdSet, true);
        }

        League league = LeagueManager.getLeagueByPlayer(player);
        if(league != null){
            check(league.getMemberMap().keySet(), excludePlayerIdSet, false);
        }
    }

    private void check(Collection<Long> playerIdSet, Set<Long> setTemp, boolean isFriend){
        playerIdSet.removeAll(setTemp);
        List<MiniGamePlayer> list = PlayerHelper.getMiniPlayers(playerIdSet);
        for (MiniGamePlayer bean : list) {
            if(bean.getLastLoginTime() <= ServerConstants.getCurrentTimeMillis() && bean.getLastLoginTime() > bean.getLastLogoutTime()){
                if(isFriend){
                    friend.add(bean);
                }else {
                    league.add(bean);
                }
            }
        }
    }
}

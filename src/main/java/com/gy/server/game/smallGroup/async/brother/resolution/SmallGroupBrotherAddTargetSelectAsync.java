package com.gy.server.game.smallGroup.async.brother.resolution;

import com.gy.server.core.command.PlayerCommandRequest;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.newFriend.FriendGlobalData;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.SmallGroupGlobalData;
import com.gy.server.game.smallGroup.SmallGroupService;
import com.gy.server.game.smallGroup.bean.SmallGroupInviteInfo;
import com.gy.server.game.smallGroup.bean.SmallGroupTypeEnum;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 金兰纳新目标选择 异步处理
 * <AUTHOR> 2024/12/30 14:16
 **/
public class SmallGroupBrotherAddTargetSelectAsync extends AsyncCall {

    /**
     * 被邀请人
     */
    private Player player;
    /**
     * 邀请人id
     */
    private long inviterId;
    /**
     * 被邀请人是否同意
     */
    private boolean select;
    private long time;
    private SmallGroupInviteInfo inviteInfo;

    private int text = Text.没有异常;

    Map<Long, Integer> likabilityMap;

    public SmallGroupBrotherAddTargetSelectAsync(Player player, PbProtocol.SmallGroupBrotherAddTargetSelectReq req, long time, Map<Long, Integer> likabilityMap) {
        this.player = player;
        this.inviterId = req.getInviterId();
        this.select = req.getSelect();
        this.time = time;
        this.likabilityMap = likabilityMap;

        SmallGroupGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroup);
        if(globalData.getInviteInfoMap().containsKey(player.getPlayerId())){
            this.inviteInfo = globalData.getInviteInfoMap().get(player.getPlayerId()).get(inviterId + "" + SmallGroupTypeEnum.brother.getType());
        }
    }

    @Override
    public void execute() {
        PbProtocol.SmallGroupBrotherAddTargetSelectRst.Builder rst = PbProtocol.SmallGroupBrotherAddTargetSelectRst.newBuilder().setResult(Text.genServerRstInfo(text));
        player.send(PtCode.SMALL_GROUP_BROTHER_ADD_TARGET_SELECT_RST, rst.build(), time);
    }

    @Override
    public void asyncExecute() {
        if(inviteInfo == null){
            text = Text.金兰纳新邀请不存在;
            return;
        }

        FriendGlobalData friendGlobalData = GlobalDataManager.getData(GlobalDataType.friend);
        if(!friendGlobalData.isFriend(player.getPlayerId(), inviterId)){
            text = Text.该玩家不是你的好友;
            return;
        }

        int likability = likabilityMap.getOrDefault(inviterId, 0);
        if(likability < SmallGroupService.getSmallGroupConst().swornBrothersLikabilityMixNum){
            text = Text.好感度不足;
            return;
        }

        PlayerCommandRequest request = CommandRequests.newPlayerCommandRequest("SmallGroupRstCommandService.brotherAddTargetSelectDeal", inviteInfo.getInviterId());
        TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, Player.getServerId(inviteInfo.getInviterId()), request, player.getPlayerId(), player.getName(), select, inviteInfo.getInviterName());

        SmallGroupGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroup);
        if(globalData.getInviteInfoMap().containsKey(player.getPlayerId())){
            ConcurrentHashMap<String, SmallGroupInviteInfo> map = globalData.getInviteInfoMap().get(player.getPlayerId());
            map.remove(inviteInfo.getKey());
            if(map.isEmpty()){
                globalData.getInviteInfoMap().remove(player.getPlayerId());
            }
        }
    }
}

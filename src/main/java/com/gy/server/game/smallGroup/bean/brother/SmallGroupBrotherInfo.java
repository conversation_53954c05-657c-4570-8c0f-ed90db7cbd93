package com.gy.server.game.smallGroup.bean.brother;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.smallGroup.SmallGroupGlobalData;
import com.gy.server.game.smallGroup.SmallGroupHelper;
import com.gy.server.game.smallGroup.SmallGroupService;
import com.gy.server.game.smallGroup.bean.SmallGroupInfoBase;
import com.gy.server.game.smallGroup.bean.SmallGroupLogTypeEnum;
import com.gy.server.game.smallGroup.bean.SmallGroupMemberInfo;
import com.gy.server.game.smallGroup.bean.SmallGroupTypeEnum;
import com.gy.server.game.smallGroup.template.SmallGroupBrotherResolutionEnum;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbSmallGroup;
import com.gy.server.utils.time.DateTimeUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * 小团体-金兰信息
 * 金兰最多能有一个
 * <AUTHOR> 2024/12/13 16:31
 **/
@ProtobufClass
public class SmallGroupBrotherInfo extends SmallGroupInfoBase {

    /**
     * 小团体名号
     */
    @Protobuf(order = 51)
    private SmallGroupBrotherName smallGroupName;

    /**
     * 小团体成员名号map
     * key:成员id  value:名号
     */
    @Protobuf(order = 52)
    private Map<Long, String> titleMap = new HashMap<>();

    /**
     * 金兰宣言
     */
    @Protobuf(order = 53)
    private String notice = "";

    /**
     * 退出冷静期结束时间map
     * key:成员id value:退出冷静期结束时间
     */
    @Protobuf(order = 54)
    private Map<Long, Long> leaveMap = new HashMap<>();

    /**
     * 成员上次发起决议时间map
     * key:成员id value:下次可发起决议时间
     */
    @Protobuf(order = 55)
    private Map<Long, Long> resolutionTimeMap = new HashMap<>();

    /****************************************决议****************************************/

    /**
     * 纳新
     */
    @Protobuf(order = 56)
    private Map<Long, SmallGroupBrotherResolutionAdd> addMap = new HashMap<>();

    /**
     * 解散
     */
    @Protobuf(order = 57)
    private Map<Long, SmallGroupBrotherResolutionDisband> disbandMap = new HashMap<>();

    /**
     * 踢人
     */
    @Protobuf(order = 58)
    private Map<Long, SmallGroupBrotherResolutionKick> kickMap = new HashMap<>();

    /**
     * 合并
     */
    @Protobuf(order = 59)
    private Map<Long, SmallGroupBrotherResolutionMerge> mergeMap = new HashMap<>();

    /**
     * 改名
     */
    @Protobuf(order = 60)
    private Map<Long, SmallGroupBrotherResolutionRename> renameMap = new HashMap<>();


    public SmallGroupBrotherInfo() {
    }

    public SmallGroupBrotherInfo(Map<Long, SmallGroupMemberInfo> memberMap, Map<Long, String> memberNameMap, SmallGroupBrotherName smallGroupName) {
        super(SmallGroupTypeEnum.brother, memberMap);
        this.smallGroupName = smallGroupName;

        //小团体创建日志
        StringBuilder memberName = new StringBuilder();
        for (String name : memberNameMap.values()) {
            if(memberName.length() != 0){
                memberName.append(",");
            }
            memberName.append(name);
        }

        addSmallGroupLog(SmallGroupLogTypeEnum.swornBrothersCreate, 0, memberName.toString(), ServerConstants.getCurrentTimeMillis() + "");
    }

    /**
     * 能否增加决议
     * @param type
     * @param playerId
     * @return
     */
    public int canAddResolution(SmallGroupBrotherResolutionEnum type, long playerId){
        if(ServerConstants.getCurrentTimeMillis() < leaveMap.getOrDefault(playerId, 0l)){
            return Text.正在退出金兰冷静期;
        }

        if(ServerConstants.getCurrentTimeMillis() < resolutionTimeMap.getOrDefault(playerId, 0l)){
            return Text.发起决议冷却中;
        }

        if(type == SmallGroupBrotherResolutionEnum.merge){
            //检查是否有人想离开
            for (Long bean : leaveMap.values()) {
                if(ServerConstants.getCurrentTimeMillis() < bean){
                    return Text.金兰中有人正在退出金兰冷静期;
                }
            }

            for (SmallGroupBrotherResolutionBase bean : getResolutionMap().values()) {
                if(bean.getVoteResult() == 0 && bean.getEnd() > ServerConstants.getCurrentTimeMillis()){
                    return Text.当前有决议正在进行;
                }
            }
        }else {
            for (SmallGroupBrotherResolutionBase bean : getResolutionMap().values()) {
                if(bean.getSmallGroupBrotherResolutionEnum() == SmallGroupBrotherResolutionEnum.merge && bean.getVoteResult() == 0 && bean.getEnd() > ServerConstants.getCurrentTimeMillis()){
                    return Text.金兰合并中不能发起决议;
                }
            }
        }

        return Text.没有异常;
    }

    public void addResolution(SmallGroupBrotherResolutionBase resolution){
        addResolution(resolution, true);
    }

    public void addResolution(SmallGroupBrotherResolutionBase resolution, boolean addResolutionCheck) {
        switch (SmallGroupBrotherResolutionEnum.getSmallGroupBrotherResolutionEnum(resolution.getType())){
            case add:{
                SmallGroupBrotherResolutionAdd temp = (SmallGroupBrotherResolutionAdd) resolution;
                addMap.put(temp.getId(), temp);
                break;
            }
            case disband:{
                SmallGroupBrotherResolutionDisband temp = (SmallGroupBrotherResolutionDisband) resolution;
                disbandMap.put(temp.getId(), temp);
                break;
            }
            case kick:{
                SmallGroupBrotherResolutionKick temp = (SmallGroupBrotherResolutionKick) resolution;
                kickMap.put(temp.getId(), temp);
                break;
            }
            case merge:{
                SmallGroupBrotherResolutionMerge temp = (SmallGroupBrotherResolutionMerge) resolution;
                mergeMap.put(temp.getId(), temp);
                break;
            }
            case rename:{
                SmallGroupBrotherResolutionRename temp = (SmallGroupBrotherResolutionRename) resolution;
                renameMap.put(temp.getId(), temp);
                break;
            }
        }

        refreshResolutionTime(resolution.getInitiatorId());

        if(addResolutionCheck){
            SmallGroupGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroup);
            globalData.addResolutionSmallGroupId(getId());
        }
    }

    public void refreshResolutionTime(long playerId) {
        if(getMemberMap().containsKey(playerId)){
            resolutionTimeMap.put(playerId, ServerConstants.getCurrentTimeMillis() + SmallGroupService.getSmallGroupConst().personaResolutionCD * DateTimeUtil.MillisOfSecond);
        }
    }

    @Override
    public void writePb(PbSmallGroup.SmallGroupInfo.Builder builder) {
        PbSmallGroup.SmallGroupBrotherInfo.Builder brother = PbSmallGroup.SmallGroupBrotherInfo.newBuilder();
        brother.setName(smallGroupName.genPb());
        brother.putAllTitle(titleMap);
        brother.setNotice(notice);
        brother.putAllLeaveMap(leaveMap);
        brother.putAllResolutionTimeMap(resolutionTimeMap);

        for (SmallGroupBrotherResolutionBase bean : getResolutionMap().values()) {
            brother.addResolution(bean.genPb(getId()));
        }

        builder.setBrother(brother.build());
    }

    @Override
    public void addMember(SmallGroupMemberInfo bean) {
        super.addMember(bean);
        if(smallGroupName != null){
            updateSmallGroupNameNum();
        }
    }

    public void updateSmallGroupNameNum(){
        smallGroupName.setNum(getMemberList().size());
    }

    @Override
    public void removeMember(long memberId) {
        super.removeMember(memberId);
        resolutionTimeMap.remove(memberId);
        titleMap.remove(memberId);

        updateSmallGroupNameNum();

        SmallGroupHelper.smallGroupLeaveDeal(memberId, getType(), getId());
    }

    @Override
    public String getFriendGroupName() {
        return smallGroupName.getFriendGroupName();
    }

    public SmallGroupBrotherName getSmallGroupName() {
        return smallGroupName;
    }

    public void setSmallGroupName(SmallGroupBrotherName smallGroupName) {
        this.smallGroupName = smallGroupName;
    }

    public Map<Long, String> getTitleMap() {
        return titleMap;
    }

    public void setTitleMap(Map<Long, String> titleMap) {
        this.titleMap = titleMap;
    }

    public String getNotice() {
        return notice;
    }

    public void setNotice(String notice) {
        this.notice = notice;
    }

    public Map<Long, Long> getLeaveMap() {
        return leaveMap;
    }

    public void setLeaveMap(Map<Long, Long> leaveMap) {
        this.leaveMap = leaveMap;
    }

    public Map<Long, Long> getResolutionTimeMap() {
        return resolutionTimeMap;
    }

    public void setResolutionTimeMap(Map<Long, Long> resolutionTimeMap) {
        this.resolutionTimeMap = resolutionTimeMap;
    }

    public Map<Long, SmallGroupBrotherResolutionBase> getResolutionMap() {
        Map<Long, SmallGroupBrotherResolutionBase> map = new HashMap<>();
        map.putAll(addMap);
        map.putAll(disbandMap);
        map.putAll(kickMap);
        map.putAll(mergeMap);
        map.putAll(renameMap);
        return map;
    }

    public Map<Long, SmallGroupBrotherResolutionAdd> getAddMap() {
        return addMap;
    }

    public void setAddMap(Map<Long, SmallGroupBrotherResolutionAdd> addMap) {
        this.addMap = addMap;
    }

    public Map<Long, SmallGroupBrotherResolutionDisband> getDisbandMap() {
        return disbandMap;
    }

    public void setDisbandMap(Map<Long, SmallGroupBrotherResolutionDisband> disbandMap) {
        this.disbandMap = disbandMap;
    }

    public Map<Long, SmallGroupBrotherResolutionKick> getKickMap() {
        return kickMap;
    }

    public void setKickMap(Map<Long, SmallGroupBrotherResolutionKick> kickMap) {
        this.kickMap = kickMap;
    }

    public Map<Long, SmallGroupBrotherResolutionMerge> getMergeMap() {
        return mergeMap;
    }

    public void setMergeMap(Map<Long, SmallGroupBrotherResolutionMerge> mergeMap) {
        this.mergeMap = mergeMap;
    }

    public Map<Long, SmallGroupBrotherResolutionRename> getRenameMap() {
        return renameMap;
    }

    public void setRenameMap(Map<Long, SmallGroupBrotherResolutionRename> renameMap) {
        this.renameMap = renameMap;
    }
}

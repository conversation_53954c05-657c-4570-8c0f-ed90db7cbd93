package com.gy.server.game.smallGroup.async.brother.create;

import com.gy.server.common.distributedlock.DistributedLockUtilManager;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.keyword.KeyWordService;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.SmallGroupGlobalData;
import com.gy.server.game.smallGroup.SmallGroupHelper;
import com.gy.server.game.smallGroup.SmallGroupService;
import com.gy.server.game.smallGroup.bean.SmallGroupTypeEnum;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherCreate;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbSmallGroup;
import com.ttlike.server.tl.baselib.TLBase;

/**
 * 金兰成员创建自己名号异步处理
 *
 * <AUTHOR> 2024/12/18 18:14
 **/
public class SmallGroupBrotherMemberCreateNameAsync extends AsyncCall {

    private Player player;
    private String name;
    private long time;

    private long createId;

    private int text = Text.没有异常;

    public SmallGroupBrotherMemberCreateNameAsync(Player player, PbProtocol.SmallGroupBrotherMemberCreateNameReq req, long time){
        this.player = player;
        this.name = req.getName();
        this.time = time;

        SmallGroupGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroup);
        this.createId = globalData.getCreateSignInfoIdByPlayerId(player.getPlayerId(), SmallGroupTypeEnum.brother);
    }

    @Override
    public void execute() {
        PbProtocol.SmallGroupBrotherMemberCreateNameRst.Builder rst = PbProtocol.SmallGroupBrotherMemberCreateNameRst.newBuilder().setResult(Text.genServerRstInfo(text));
        player.send(PtCode.SMALL_GROUP_BROTHER_MEMBER_CREATE_NAME_RST, rst.build(), time);
    }

    @Override
    public void asyncExecute() {
        if (createId == 0) {
            text = Text.金兰创建未进行;
            return;
        }

        String redisKey = GsRedisKey.SmallGroup.small_group_create.getRedisKey(createId);
        DistributedLockUtilManager.operate(() -> {
            SmallGroupBrotherCreate brotherCreate = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupBrotherCreate.class, redisKey);

            if (brotherCreate == null) {
                text = Text.金兰创建未进行;
                return;
            }

            if (brotherCreate.getType() != SmallGroupTypeEnum.brother) {
                text = Text.组队类型不对;
                return;
            }

            if (brotherCreate.getNodeType() != PbSmallGroup.SgbNodeType.sgbMemberName) {
                text = Text.金兰前置任务未完成;
                return;
            }

            //检查空字符
            if (name.isEmpty()) {
                text = Text.名称不能为空;
                return;
            }

            //检查字符长度 名称长度超出限制
            if (name.length() > SmallGroupService.getSmallGroupConst().lastNameNum) {
                text = Text.名称长度超出限制;
                return;
            }

            //检查字符合法
            if (KeyWordService.contains(name)) {
                text = Text.名称不可用;
                return;
            }

            if (brotherCreate.getTitleMap().containsKey(player.getPlayerId())) {
                text = Text.名号已提交;
                return;
            }

            //检查是否重复
            if (brotherCreate.getTitleMap().containsValue(name)) {
                text = Text.此名号已被使用;
                return;
            }

            brotherCreate.getTitleMap().put(player.getPlayerId(), name);
            if (brotherCreate.getTitleMap().size() == brotherCreate.getMemberList().size()) {
                brotherCreate.sgbNodeTypeNext();
            }
            TLBase.getInstance().getRedisAssistant().setBean(GsRedisKey.SmallGroup.small_group_create.getRedisKey(brotherCreate.getId()), brotherCreate);

            SmallGroupHelper.smallGroupCreateInfoNotify(brotherCreate);
        }, redisKey);


    }
}

package com.gy.server.game.smallGroup.async.makeAcquaintances;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.PlayerSmallGroupModel;
import com.gy.server.game.smallGroup.bean.makeAcquaintances.MakeAcquaintancesApplyInfo;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.ttlike.server.tl.baselib.TLBase;

import java.util.*;

/**
 * 我的申请列表
 * <AUTHOR> 2025/1/3 17:16
 **/
public class MakeAcquaintancesMyApplyListAsync extends AsyncCall {

    private Player player;
    private long time;

    private List<MakeAcquaintancesApplyInfo> list = new ArrayList<>();

    public MakeAcquaintancesMyApplyListAsync(Player player, long time) {
        this.player = player;
        this.time = time;
    }

    @Override
    public void execute() {
        PbProtocol.MakeAcquaintancesMyApplyListRst.Builder rst = PbProtocol.MakeAcquaintancesMyApplyListRst.newBuilder().setResult(Text.genOkServerRstInfo());
        list.forEach(bean -> rst.addApplyInfo(bean.genMakeAcquaintancesApplyInfo()));
        player.send(PtCode.MAKE_ACQUAINTANCES_MY_APPLY_LIST_RST, rst.build(), time);
    }

    @Override
    public void asyncExecute() {
        PlayerSmallGroupModel model = player.getPlayerSmallGroupModel();
        Map<Integer, Set<Long>> map = new HashMap<>(model.getMyApplyMap());

        for (Map.Entry<Integer, Set<Long>> entry : map.entrySet()) {
            for (Long id : entry.getValue()) {
                String redisKey = GsRedisKey.SmallGroup.make_acquaintances_apply_info.getRedisKey(id);
                String hashKey = entry.getKey() + "" + player.getPlayerId();

                MakeAcquaintancesApplyInfo applyInfo = TLBase.getInstance().getRedisAssistant().hashGetBeans(redisKey, hashKey, MakeAcquaintancesApplyInfo.class);
                if(applyInfo == null){
                   if(model.getMyApplyMap().containsKey(entry.getKey())){
                       model.getMyApplyMap().get(entry.getKey()).remove(id);
                   }
                   continue;
                }

                list.add(applyInfo);
            }
        }
    }
}

package com.gy.server.game.smallGroup.template;

import com.gy.server.game.util.StringExtUtil;

import java.util.List;
import java.util.Map;

/**
 * 情缘巡游时间段模板
 *
 * <AUTHOR> 2025/1/16 20:36
 **/
public class SmallGroupMarryCruiseReservationTemplate {

    public int id;

    /**
     * 开始时间 当天0点开始到时间点总分钟数
     */
    public int startTime;

    /**
     * 结束时间 当天0点开始到时间点总分钟数
     */
    public int endTime;

    public SmallGroupMarryCruiseReservationTemplate(Map<String, String> map) {
        this.id = Integer.parseInt(map.get("id"));

        List<Integer> startTimeList = StringExtUtil.string2List(map.get("startTime"), ":", Integer.class);
        this.startTime = startTimeList.get(0) * 60 + startTimeList.get(1);

        List<Integer> endTimeList = StringExtUtil.string2List(map.get("endTime"), ":", Integer.class);
        this.endTime = endTimeList.get(0) * 60 + endTimeList.get(1);
    }

}



package com.gy.server.game.smallGroup.bean.brother;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;
import com.gy.server.game.smallGroup.template.SmallGroupBrotherResolutionEnum;
import com.gy.server.packet.PbSmallGroup;

/**
 * 金兰决议改名
 *
 * <AUTHOR> 2025/1/2 11:20
 **/
@ProtobufClass
public class SmallGroupBrotherResolutionRename extends SmallGroupBrotherResolutionBase {

    @Protobuf(order = 11)
    private SmallGroupBrotherName name;

    public SmallGroupBrotherResolutionRename() {
    }

    public SmallGroupBrotherResolutionRename(long initiatorId, String initiatorName, SmallGroupBrotherName name) {
        super(SmallGroupBrotherResolutionEnum.rename, initiatorId, initiatorName);
        this.name = name;
    }

    @Override
    public void writePb(PbSmallGroup.SmallGroupBrotherResolution.Builder builder, long brotherId) {
        PbSmallGroup.SmallGroupBrotherResolutionRename.Builder rename = PbSmallGroup.SmallGroupBrotherResolutionRename.newBuilder();
        rename.setName(name.genPb());
        builder.setRename(rename);
    }

    public SmallGroupBrotherName getName() {
        return name;
    }

    public void setName(SmallGroupBrotherName name) {
        this.name = name;
    }
}

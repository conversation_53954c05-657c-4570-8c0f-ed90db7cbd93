package com.gy.server.game.smallGroup.template;

import com.gy.server.game.drop.RewardTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 情缘烟花模板
 * <AUTHOR> 2025/1/17 9:52
 **/
public class SmallGroupMarryFireworksClassTemplate {

    public int id;

    /**
     * 奖励
     */
    public int reward;

    /**
     * 消耗
     */
    public List<RewardTemplate> consume = new ArrayList<>();

    public SmallGroupMarryFireworksClassTemplate(Map<String, String> map) {
        this.id = Integer.parseInt(map.get("id"));
        this.reward = Integer.parseInt(map.get("reward"));
        this.consume = RewardTemplate.readListFromText(map.get("consume"));;
    }
}

package com.gy.server.game.smallGroup.async.brother.resolution;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.newFriend.FriendGlobalData;
import com.gy.server.game.newFriend.FriendHelper;
import com.gy.server.game.newFriend.bean.FriendInfo;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.SmallGroupService;
import com.gy.server.game.smallGroup.bean.PlayerSmallGroupIdInfo;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbFriend;
import com.gy.server.packet.PbProtocol;
import com.ttlike.server.tl.baselib.TLBase;

import java.util.Collection;
import java.util.Map;

/**
 * 金兰纳新可用信息异步处理
 *
 * <AUTHOR> 2024/12/26 18:05
 **/
public class SmallGroupBrotherCanAddInfoAsync extends AsyncCall {
    private Player player;
    private long time;

    private PbProtocol.SmallGroupBrotherCanAddInfoRst.Builder rst;

    Map<Long, Integer> likabilityMap;

    public SmallGroupBrotherCanAddInfoAsync(Player player, long time, Map<Long, Integer> likabilityMap) {
        this.player = player;
        this.time = time;
        this.rst = PbProtocol.SmallGroupBrotherCanAddInfoRst.newBuilder().setResult(Text.genOkServerRstInfo());
        this.likabilityMap = likabilityMap;
    }

    @Override
    public void execute() {
        player.send(PtCode.SMALL_GROUP_BROTHER_CAN_ADD_INFO_RST, rst.build(), time);
    }

    @Override
    public void asyncExecute() {
        FriendGlobalData globalData = GlobalDataManager.getData(GlobalDataType.friend);
        Collection<FriendInfo> friendInfos = globalData.getFriendInfo(player.getPlayerId()).getFriendInfos().values();

        for (FriendInfo bean : friendInfos) {
            if(likabilityMap.getOrDefault(bean.getFriendId(), 0) >= SmallGroupService.getSmallGroupConst().swornBrothersLikabilityMixNum){
                PbFriend.FriendInfo friendInfo = FriendHelper.fillFriendInfo(bean.getFriendId(), likabilityMap);
                if(friendInfo.getLastLogoutTime() < friendInfo.getLastLoginTime()){
                    String redisKey = GsRedisKey.SmallGroup.player_small_group_id_info.getRedisKey(player.getPlayerId());
                    PlayerSmallGroupIdInfo playerSmallGroupIdInfo = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, redisKey);
                    if (playerSmallGroupIdInfo == null || (playerSmallGroupIdInfo != null && playerSmallGroupIdInfo.getBrotherId() != 0)) {
                        rst.addFriendInfos(friendInfo);
                    }
                }
            }
        }
    }
}

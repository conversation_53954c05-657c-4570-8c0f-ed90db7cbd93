package com.gy.server.game.smallGroup;

import com.gy.server.common.distributedlock.DistributedLockUtilManager;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.core.Configuration;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.bean.makeAcquaintances.SmallGroupMakeAcquaintancesInfo;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;

import java.util.*;


/**
 * 结识辅助类
 *
 * <AUTHOR> 2025/1/14 11:41
 **/
public class MakeAcquaintancesHelper {

    /**
     * 删除结识
     * @param platFormId
     * @param senderId
     */
    public static void dealMakeAcquaintances(int platFormId, Long senderId){
        String redisKey = GsRedisKey.SmallGroup.make_acquaintances_info.getRedisKey(platFormId);
        DistributedLockUtilManager.operate(() -> {
            String hashKey = senderId + "";
            SmallGroupMakeAcquaintancesInfo info = TLBase.getInstance().getRedisAssistant().hashGetBeans(redisKey, hashKey, SmallGroupMakeAcquaintancesInfo.class);
            if(info != null){
                List<Long> playerIdSet = new ArrayList<>(info.getApplyIdSet());

                TLBase.getInstance().getRedisAssistant().hashRemove(redisKey, hashKey);
                dealMakeAcquaintancesApply(platFormId, playerIdSet, senderId);
            }
        }, redisKey);
    }

    /**
     * 删除结识申请
     * @param platFormId 表id
     * @param applierIdList 申请人id集合
     * @param senderId 发布人id
     */
    public static void dealMakeAcquaintancesApply(int platFormId, List<Long> applierIdList, Long senderId){
        Map<Integer, Set<Long>> serverPlayerIdSetMap = new HashMap<>();

        String[] hashKeys = new String[applierIdList.size()];
        for (int i = 0; i < applierIdList.size(); i++){
            long applierId = applierIdList.get(i);
            hashKeys[i] = platFormId + "" + applierId;
            int serverId = Player.getServerId(applierId);
            serverPlayerIdSetMap.computeIfAbsent(serverId, set -> new HashSet<>()).add(applierId);
        }
        String redisKey = GsRedisKey.SmallGroup.make_acquaintances_apply_info.getRedisKey(senderId);
        TLBase.getInstance().getRedisAssistant().hashRemove(redisKey, hashKeys);

        //删除申请人申请记录
        for (Map.Entry<Integer, Set<Long>> entry : serverPlayerIdSetMap.entrySet()) {
            if(entry.getKey() == Configuration.serverId){
                ServerCommandRequest request = CommandRequests.newServerCommandRequest("SmallGroupRstCommandService.removeApplyRecord");
                TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, entry.getKey(), request, entry.getValue(), senderId, platFormId);
            }else {
                SmallGroupHelper.removeApplyRecord(entry.getValue(), senderId, platFormId);
            }
        }
    }
}

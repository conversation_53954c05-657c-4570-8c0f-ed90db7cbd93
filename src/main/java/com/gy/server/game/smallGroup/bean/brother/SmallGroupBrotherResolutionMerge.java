package com.gy.server.game.smallGroup.bean.brother;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.game.smallGroup.template.SmallGroupBrotherResolutionEnum;
import com.gy.server.packet.PbSmallGroup;
import com.ttlike.server.tl.baselib.TLBase;

import java.util.List;

/**
 * 金兰决议合并
 *
 * <AUTHOR> 2024/12/30 16:31
 **/
@ProtobufClass
public class SmallGroupBrotherResolutionMerge extends SmallGroupBrotherResolutionBase {

    /**
     * 要合并的金兰id
     */
    @Protobuf(order = 11)
    private long targetSmallGroupId;

    /**
     * 主金兰id
     * 合并到这个金兰中
     */
    @Protobuf(order = 12)
    private long masterSmallGroupId;

    /**
     * 另一方金兰名称
     */
    @Protobuf(order = 13)
    private SmallGroupBrotherName antherName;

    public SmallGroupBrotherResolutionMerge() {
    }

    public SmallGroupBrotherResolutionMerge(long initiatorId, String initiatorName, long targetSmallGroupId, long masterSmallGroupId, SmallGroupBrotherName antherName) {
        super(SmallGroupBrotherResolutionEnum.merge, initiatorId, initiatorName);
        this.targetSmallGroupId = targetSmallGroupId;
        this.masterSmallGroupId = masterSmallGroupId;
        this.antherName = antherName;
    }

    /**
     * 异步使用
     * @param builder
     */
    @Override
    public void writePb(PbSmallGroup.SmallGroupBrotherResolution.Builder builder, long brotherId) {
        PbSmallGroup.SmallGroupBrotherResolutionMerge.Builder merge = PbSmallGroup.SmallGroupBrotherResolutionMerge.newBuilder();
        merge.setAntherName(antherName.genPb());

        long antherId = getAntherBrotherId(brotherId);
        String redisKey = GsRedisKey.SmallGroup.small_group_info.getRedisKey(antherId);
        SmallGroupBrotherInfo brotherInfo = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupBrotherInfo.class, redisKey);
        if(brotherInfo != null){
            SmallGroupBrotherResolutionMerge resolutionMerge = brotherInfo.getMergeMap().get(getId());
            if(resolutionMerge != null){
                List<MiniGamePlayer> list = PlayerHelper.getMiniPlayers(brotherInfo.getMemberList());
                for (MiniGamePlayer bean : list) {
                    merge.putAntherMemberNameMap(bean.getPlayerId(), bean.getName());
                }
                merge.putAllAntherVoteMap(resolutionMerge.getVoteMap());
            }
        }

        builder.setMerge(merge.build());
    }

    /**
     * 获取另一方
     * @param id
     * @return
     */
    public long getAntherBrotherId(long id){
        return id == masterSmallGroupId ? targetSmallGroupId : masterSmallGroupId;
    }

    public long getTargetSmallGroupId() {
        return targetSmallGroupId;
    }

    public void setTargetSmallGroupId(long targetSmallGroupId) {
        this.targetSmallGroupId = targetSmallGroupId;
    }

    public long getMasterSmallGroupId() {
        return masterSmallGroupId;
    }

    public void setMasterSmallGroupId(long masterSmallGroupId) {
        this.masterSmallGroupId = masterSmallGroupId;
    }

    public SmallGroupBrotherName getAntherName() {
        return antherName;
    }

    public void setAntherName(SmallGroupBrotherName antherName) {
        this.antherName = antherName;
    }
}

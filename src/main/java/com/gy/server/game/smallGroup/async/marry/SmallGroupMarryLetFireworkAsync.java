package com.gy.server.game.smallGroup.async.marry;

import com.gy.server.common.distributedlock.DistributedLockUtilManager;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.SmallGroupService;
import com.gy.server.game.smallGroup.bean.marry.SmallGroupMarryCreate;
import com.gy.server.game.smallGroup.template.SmallGroupMarryFireworksClassTemplate;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbSmallGroup;
import com.ttlike.server.tl.baselib.TLBase;

import java.util.ArrayList;
import java.util.List;

/**
 * 放烟花 异步处理
 *
 * <AUTHOR> 2025/2/18 19:39
 **/
public class SmallGroupMarryLetFireworkAsync extends AsyncCall {

    private Player player;
    private long createId;
    private int id;
    private int num;
    private long time;

    private List<Reward> cost = new ArrayList<>();
    private int text = Text.没有异常;

    public SmallGroupMarryLetFireworkAsync(Player player, PbProtocol.SmallGroupMarryLetFireworkReq req, long time) {
        this.player = player;
        this.createId = req.getCreateId();
        this.id = req.getId();
        this.num = req.getNum();
        this.time = time;
    }

    @Override
    public void execute() {
        if(text == Text.没有异常){
            Reward.remove(cost, player, BehaviorType.smallGroupMarryFireWork);
        }

        PbProtocol.SmallGroupMarryLetFireworkRst.Builder rst = PbProtocol.SmallGroupMarryLetFireworkRst.newBuilder().setResult(Text.genServerRstInfo(text));
        player.send(PtCode.SMALL_GROUP_MARRY_LET_FIREWORK_RST, rst.build(), time);
    }

    @Override
    public void asyncExecute() {
        SmallGroupMarryFireworksClassTemplate fireworksClassTemplate = SmallGroupService.getMarryFireworksClassTemplateMap().get(id);
        if(fireworksClassTemplate == null){
            text = Text.对应的模板数据找不到;
            return;
        }

        if(num < 0 || num > 9999){
            text = Text.参数异常;
            return;
        }

        for (int i = 0; i < num; i++) {
            cost.addAll(Reward.templateCollectionToReward(fireworksClassTemplate.consume));
        }

        if(Reward.check(player, cost) != -1){
            text = Text.消耗不足;
            return;
        }

        String redisKey = GsRedisKey.SmallGroup.small_group_create.getRedisKey(createId);
        DistributedLockUtilManager.operate(() -> {
            SmallGroupMarryCreate marryCreate = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupMarryCreate.class, redisKey);
            if(marryCreate == null){
                text = Text.参数异常;
                return;
            }

            if(!marryCreate.getMemberList().contains(player.getPlayerId()) && !marryCreate.getInviteIdSet().contains(player.getPlayerId())){
                text = Text.当前未参加结情缘;
                return;
            }

            //核对节点
            if(marryCreate.getNodeType() != PbSmallGroup.SgmNodeType.sgmWait && marryCreate.getNodeType() != PbSmallGroup.SgmNodeType.sgmCruise){
                text = Text.当前阶段不能放烟花;
                return;
            }

            marryCreate.addFirework(id, num);
            TLBase.getInstance().getRedisAssistant().setBean(redisKey, marryCreate);
        }, redisKey);
    }
}

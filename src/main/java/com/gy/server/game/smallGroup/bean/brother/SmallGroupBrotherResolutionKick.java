package com.gy.server.game.smallGroup.bean.brother;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.game.smallGroup.template.SmallGroupBrotherResolutionEnum;
import com.gy.server.packet.PbSmallGroup;

/**
 * 金兰决议踢人
 *
 * <AUTHOR> 2025/1/2 11:37
 **/
@ProtobufClass
public class SmallGroupBrotherResolutionKick extends SmallGroupBrotherResolutionBase {

    /**
     * 要踢的目标id
     */
    @Protobuf(order = 11)
    private long targetPlayerId;

    /**
     * 要踢得目标名字
     */
    @Protobuf(order = 12)
    private String targetName;

    public SmallGroupBrotherResolutionKick() {
    }

    public SmallGroupBrotherResolutionKick(Player player, long targetPlayerId, String targetName) {
        super(SmallGroupBrotherResolutionEnum.kick, player.getPlayerId(), player.getName());
        this.targetPlayerId = targetPlayerId;
        this.targetName = targetName;
    }

    @Override
    public void writePb(PbSmallGroup.SmallGroupBrotherResolution.Builder builder, long brotherId) {
        PbSmallGroup.SmallGroupBrotherResolutionKick.Builder kick = PbSmallGroup.SmallGroupBrotherResolutionKick.newBuilder();
        kick.setTargetPlayerId(targetPlayerId);
        MiniGamePlayer miniGamePlayer = PlayerHelper.getMiniPlayer(targetPlayerId);
        if(miniGamePlayer != null){
            kick.setTargetPlayerName(miniGamePlayer.getName());
        }
        builder.setKick(kick);
    }

    public long getTargetPlayerId() {
        return targetPlayerId;
    }

    public void setTargetPlayerId(long targetPlayerId) {
        this.targetPlayerId = targetPlayerId;
    }

    public String getTargetName() {
        return targetName;
    }

    public void setTargetName(String targetName) {
        this.targetName = targetName;
    }
}

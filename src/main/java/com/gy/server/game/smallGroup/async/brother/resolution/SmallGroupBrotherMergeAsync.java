package com.gy.server.game.smallGroup.async.brother.resolution;

import com.gy.server.common.distributedlock.DistributedLockUtilManager;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.SmallGroupService;
import com.gy.server.game.smallGroup.async.SmallGroupInfoSyncAsync;
import com.gy.server.game.smallGroup.bean.PlayerSmallGroupIdInfo;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherInfo;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherResolutionMerge;
import com.gy.server.game.smallGroup.template.SmallGroupBrotherResolutionEnum;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

/**
 * 金兰合并决议异步处理
 * <AUTHOR> 2024/12/31 11:23
 **/
public class SmallGroupBrotherMergeAsync extends AsyncCall {

    private Player player;
    private long targetId; //目标金兰id
    private long time;

    private int text = Text.没有异常;

    public SmallGroupBrotherMergeAsync(Player player, PbProtocol.SmallGroupBrotherMergeReq req, long time) {
        this.player = player;
        this.targetId = req.getTargetId();
        this.time = time;
    }

    @Override
    public void execute() {
        PbProtocol.SmallGroupBrotherMergeRst.Builder rst = PbProtocol.SmallGroupBrotherMergeRst.newBuilder().setResult(Text.genServerRstInfo(text));
        player.send(PtCode.SMALL_GROUP_BROTHER_MERGE_RST, rst.build(), time);
    }

    @Override
    public void asyncExecute() {
        String redisKeySmallGroupId = GsRedisKey.SmallGroup.player_small_group_id_info.getRedisKey(player.getPlayerId());
        PlayerSmallGroupIdInfo playerSmallGroupIdInfo = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, redisKeySmallGroupId);
        if(playerSmallGroupIdInfo == null){
            text = Text.金兰不存在;
            return;
        }

        if(playerSmallGroupIdInfo.getBrotherId() == 0){
            text = Text.金兰不存在;
            return;
        }

        String redisKey1 = GsRedisKey.SmallGroup.small_group_info.getRedisKey(playerSmallGroupIdInfo.getBrotherId());
        String redisKey2 = GsRedisKey.SmallGroup.small_group_info.getRedisKey(targetId);

        DistributedLockUtilManager.operate(() -> {
            SmallGroupBrotherInfo brotherInfo1 = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupBrotherInfo.class, redisKey1);
            SmallGroupBrotherInfo brotherInfo2 = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupBrotherInfo.class, redisKey2);

            if(brotherInfo1 == null){
                text = Text.金兰不存在;
                return;
            }

            if(brotherInfo2 == null){
                text = Text.目标金兰不存在;
                return;
            }

            //过滤自己
            if(brotherInfo1.getId() == brotherInfo2.getId()){
                text = Text.金兰不能合并自己;
                return;
            }

            int textTemp1 = brotherInfo1.canAddResolution(SmallGroupBrotherResolutionEnum.merge, player.getPlayerId());
            if(textTemp1 != Text.没有异常){
                text = textTemp1;
                return;
            }

            int textTemp2 = brotherInfo1.canAddResolution(SmallGroupBrotherResolutionEnum.merge, player.getPlayerId());
            if(textTemp2 != Text.没有异常){
                if(textTemp2 == Text.当前有决议正在进行){
                    text = Text.目标金兰当前有决议正在进行;
                    return;
                }

                if(textTemp2 == Text.金兰中有人正在退出金兰冷静期){
                    text = Text.目标金兰中有人正在退出金兰冷静期;
                    return;
                }

                text = Text.目标金兰当前有决议正在进行;
                return;
            }

            //核对最大人数
            int maxNum = SmallGroupService.getBrotherMaxNum(brotherInfo1.getExp());
            if(brotherInfo1.getMemberMap().size() + brotherInfo2.getMemberMap().size() > maxNum){
                text = Text.合并后人数超上限;
                return;
            }

            long masterSmallGroupId = brotherInfo1.getId();
            long targetSmallGroupId = brotherInfo2.getId();

            SmallGroupBrotherResolutionMerge resolutionMerge1 = new SmallGroupBrotherResolutionMerge(player.getPlayerId(), player.getName(), targetSmallGroupId, masterSmallGroupId, brotherInfo2.getSmallGroupName());
            SmallGroupBrotherResolutionMerge resolutionMerge2 = new SmallGroupBrotherResolutionMerge(player.getPlayerId(), player.getName(), targetSmallGroupId, masterSmallGroupId, brotherInfo1.getSmallGroupName());
            resolutionMerge2.setId(resolutionMerge1.getId());

            brotherInfo1.addResolution(resolutionMerge1);
            brotherInfo2.addResolution(resolutionMerge2, false);

            TLBase.getInstance().getRedisAssistant().setBean(redisKey1, brotherInfo1);
            TLBase.getInstance().getRedisAssistant().setBean(redisKey2, brotherInfo2);

            ThreadPool.execute(new SmallGroupInfoSyncAsync(brotherInfo1));
            ThreadPool.execute(new SmallGroupInfoSyncAsync(brotherInfo2));
        }, redisKey1, redisKey2);
    }
}

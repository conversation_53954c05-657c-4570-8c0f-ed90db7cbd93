package com.gy.server.game.smallGroup.template;

import com.ttlike.server.tl.baselib.serialize.combatAddition.CombatAdditions;

import java.util.Map;

/**
 * 金兰技能模板
 *
 * <AUTHOR> 2025/1/23 14:32
 **/
public class SmallGroupBrothersSkillTemplate {

    public int id;

    /**
     *加成属性
     */
    public CombatAdditions combatAddition;

    public SmallGroupBrothersSkillTemplate(Map<String, String> map) {
        this.id = Integer.parseInt(map.get("id"));
        this.combatAddition = CombatAdditions.readFromStr(map.get("combatAddition"));
    }
}

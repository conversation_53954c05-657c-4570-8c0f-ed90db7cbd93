package com.gy.server.game.smallGroup.async.brother.create;

import com.gy.server.common.distributedlock.DistributedLockUtilManager;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.SmallGroupGlobalData;
import com.gy.server.game.smallGroup.SmallGroupHelper;
import com.gy.server.game.smallGroup.bean.SmallGroupInfoBase;
import com.gy.server.game.smallGroup.bean.SmallGroupTypeEnum;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherCreate;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbSmallGroup;
import com.ttlike.server.tl.baselib.TLBase;

/**
 * 小团体-金兰成员确认
 *
 * <AUTHOR> 2024/12/20 11:22
 **/
public class SmallGroupBrotherSureAsync extends AsyncCall {

    private Player player;
    private long time;

    private long createId;

    private SmallGroupBrotherCreate brotherCreate;
    private int text = Text.没有异常;

    public SmallGroupBrotherSureAsync(Player player, long time) {
        this.player = player;
        this.time = time;

        SmallGroupGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroup);
        this.createId = globalData.getCreateSignInfoIdByPlayerId(player.getPlayerId(), SmallGroupTypeEnum.brother);

    }


    @Override
    public void execute() {
        PbProtocol.SmallGroupBrotherSureRst.Builder rst = PbProtocol.SmallGroupBrotherSureRst.newBuilder().setResult(Text.genServerRstInfo(text));
        player.send(PtCode.SMALL_GROUP_BROTHER_SURE_RST, rst.build(), time);

        if(text == Text.没有异常){
            SmallGroupHelper.smallGroupCreateInfoNotify(brotherCreate);
        }

    }

    @Override
    public void asyncExecute() {
        if (createId == 0) {
            text = Text.金兰创建未进行;
            return;
        }

        String redisKey = GsRedisKey.SmallGroup.small_group_create.getRedisKey(createId);
        DistributedLockUtilManager.operate(() -> {
            this.brotherCreate = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupBrotherCreate.class, redisKey);
            if (brotherCreate == null) {
                text = Text.金兰创建未进行;
                return;
            }

            if (brotherCreate.getType() != SmallGroupTypeEnum.brother) {
                text = Text.组队类型不对;
                return;
            }

            if (brotherCreate.getNodeType() != PbSmallGroup.SgbNodeType.sgbSure) {
                text = Text.金兰前置任务未完成;
                return;
            }

            if (brotherCreate.getSureMemberIdSet().contains(player.getPlayerId())) {
                text = Text.金兰已宣誓;
                return;
            }

            brotherCreate.getSureMemberIdSet().add(player.getPlayerId());
            if (brotherCreate.getSureMemberIdSet().size() == brotherCreate.getMemberList().size()) {
                brotherCreate.sgbNodeTypeNext();
                SmallGroupInfoBase brotherInfo = brotherCreate.create();
                SmallGroupHelper.smallGroupCrateFinishHandler(brotherInfo);

                brotherCreate.afterHandler();
            }
            TLBase.getInstance().getRedisAssistant().setBean(redisKey, brotherCreate);
        }, redisKey);

    }
}

package com.gy.server.game.smallGroup.async.marry;

import com.gy.server.common.distributedlock.DistributedLockUtilManager;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.async.SmallGroupInfoSyncAsync;
import com.gy.server.game.smallGroup.bean.PlayerSmallGroupIdInfo;
import com.gy.server.game.smallGroup.bean.marry.SmallGroupMarryInfo;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

/**
 * 情缘解散取消异步处理
 *
 * <AUTHOR> 2025/3/25 16:59
 **/
public class SmallGroupMarryDisbandCancelAsync extends AsyncCall {

    private Player player;
    private long time;

    private int text = Text.没有异常;

    public SmallGroupMarryDisbandCancelAsync(Player player, long time) {
        this.player = player;
        this.time = time;
    }

    @Override
    public void execute() {
        PbProtocol.SmallGroupMarryDisbandCancelRst.Builder rst = PbProtocol.SmallGroupMarryDisbandCancelRst.newBuilder().setResult(Text.genServerRstInfo(text));
        player.send(PtCode.SMALL_GROUP_MARRY_DISBAND_CANCEL_RST, rst.build(), time);
    }

    @Override
    public void asyncExecute() {
        if(text != Text.没有异常){
            return;
        }

        String groupIdInfoRedisKey = GsRedisKey.SmallGroup.player_small_group_id_info.getRedisKey(player.getPlayerId());
        PlayerSmallGroupIdInfo idInfo = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, groupIdInfoRedisKey);
        if(idInfo == null || (idInfo != null && idInfo.getMarryId() == 0)){
            text = Text.当前没有情缘;
            return;
        }

        String marryRedisKey = GsRedisKey.SmallGroup.small_group_info.getRedisKey(idInfo.getMarryId());
        DistributedLockUtilManager.operate(() -> {
            SmallGroupMarryInfo marryInfo = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupMarryInfo.class, marryRedisKey);
            if(marryInfo == null){
                text = Text.当前没有情缘;
                return;
            }

            if(marryInfo.getPeriod() <= 0l){
                text = Text.情缘不在解散冷静期;
                return;
            }

            marryInfo.setPeriod(0l);
            marryInfo.getAgreeList().clear();

            TLBase.getInstance().getRedisAssistant().setBean(marryRedisKey, marryInfo);
            ThreadPool.execute(new SmallGroupInfoSyncAsync(marryInfo));
        }, marryRedisKey);
    }
}

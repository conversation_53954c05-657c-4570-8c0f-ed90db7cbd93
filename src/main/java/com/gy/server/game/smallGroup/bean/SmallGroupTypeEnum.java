package com.gy.server.game.smallGroup.bean;


/**
 * <AUTHOR> 2024/12/10 14:32
 **/
public enum SmallGroupTypeEnum {

    /**
     * 金兰
     */
    brother(1),

    /**
     * 情缘
     */
    marry(2),

    /**
     * 师徒
     */
    ts(3),
    ;

    private int type;

    SmallGroupTypeEnum(int type) {
        this.type = type;
    }

    public static SmallGroupTypeEnum getSmallGroupTypeEnum(String type){
        return getSmallGroupTypeEnum(Integer.parseInt(type));
    }

    public static SmallGroupTypeEnum getSmallGroupTypeEnum(int type){
        for (SmallGroupTypeEnum bean : SmallGroupTypeEnum.values()) {
            if(bean.type == type){
                return bean;
            }
        }
        return null;
    }

    public int getType() {
        return type;
    }

}

package com.gy.server.game.smallGroup.async.brother;

import com.gy.server.common.distributedlock.DistributedLockUtilManager;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.keyword.KeyWordService;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.SmallGroupHelper;
import com.gy.server.game.smallGroup.SmallGroupService;
import com.gy.server.game.smallGroup.async.SmallGroupInfoSyncAsync;
import com.gy.server.game.smallGroup.bean.PlayerSmallGroupIdInfo;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherInfo;
import com.gy.server.game.smallGroup.template.SmallGroupBrotherResolutionEnum;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbSmallGroup;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.ArrayList;
import java.util.List;

/**
 * 小团体-金兰修改公告
 *
 * <AUTHOR> 2024/12/24 9:45
 **/
public class SmallGroupBrotherChangeNoticeAsync extends AsyncCall {

    Player player;
    String notice;
    long time;

    int text = Text.没有异常;

    List<PbSmallGroup.SmallGroupInfo> list = new ArrayList<>();

    public SmallGroupBrotherChangeNoticeAsync(Player player, PbProtocol.SmallGroupBrotherChangeNoticeReq req, long time) {
        this.player = player;
        this.notice = req.getNotice();
        this.time = time;
    }

    @Override
    public void execute() {
        PbProtocol.SmallGroupBrotherLeaveRst.Builder rst = PbProtocol.SmallGroupBrotherLeaveRst.newBuilder().setResult(Text.genServerRstInfo(text));
        player.send(PtCode.SMALL_GROUP_BROTHER_CHANGE_NOTICE_RST, rst.build(), time);
    }

    @Override
    public void asyncExecute() {
        String redisKey = GsRedisKey.SmallGroup.player_small_group_id_info.getRedisKey(player.getPlayerId());
        PlayerSmallGroupIdInfo playerSmallGroupIdInfo = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, redisKey);
        if(playerSmallGroupIdInfo == null){
            text = Text.金兰不存在;
            return;
        }

        if(playerSmallGroupIdInfo.getBrotherId() == 0){
            text = Text.金兰不存在;
            return;
        }

        String redisKeyTemp = GsRedisKey.SmallGroup.small_group_info.getRedisKey(playerSmallGroupIdInfo.getBrotherId());
        DistributedLockUtilManager.operate(() -> {
            SmallGroupBrotherInfo brotherInfo = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupBrotherInfo.class, redisKeyTemp);
            if(brotherInfo == null){
                text = Text.金兰不存在;
                return;
            }

            int textTemp = brotherInfo.canAddResolution(SmallGroupBrotherResolutionEnum.disband, player.getPlayerId());
            if(textTemp != Text.没有异常){
                text = textTemp;
                return;
            }

            if(brotherInfo.getResolutionTimeMap().containsKey(player.getPlayerId())){
                text = Text.正在退出金兰冷静期;
                return;
            }

            if(notice.length() > SmallGroupService.getSmallGroupConst().swornBrothersLength / 2){
                text = Text.金兰宣言超长;
                return;
            }

            KeyWordService.mark(notice);
            brotherInfo.setNotice(notice);
            TLBase.getInstance().getRedisAssistant().setBean(redisKeyTemp, brotherInfo);

            list.add(SmallGroupHelper.genSmallGroupInfo(brotherInfo));
            ThreadPool.execute(new SmallGroupInfoSyncAsync(brotherInfo));
        }, redisKeyTemp);
    }
}

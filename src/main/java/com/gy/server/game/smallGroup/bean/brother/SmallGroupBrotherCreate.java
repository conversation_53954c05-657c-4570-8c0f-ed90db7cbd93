package com.gy.server.game.smallGroup.bean.brother;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.smallGroup.SmallGroupHelper;
import com.gy.server.game.smallGroup.bean.*;
import com.gy.server.game.smallGroup.bean.SmallGroupCreateBase;
import com.gy.server.game.team.TeamHelper;
import com.gy.server.packet.PbSmallGroup;
import com.gy.server.utils.time.DateTimeUtil;
import org.apache.logging.log4j.core.jmx.Server;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 小团体-金兰创建
 * 流程
 * 开始 -> 金兰进度任务 -> 长幼排序（职位投票） -> 称号选取 -> 字号选取 -> 金兰人员确定
 * <AUTHOR> 2024/12/12 17:11
 **/
@ProtobufClass
public class SmallGroupBrotherCreate extends SmallGroupCreateBase {

    /**
     * 小团体称号
     */
    @Protobuf(order = 11)
    private SmallGroupBrotherName name;

    /**
     * 成员名号map
     */
    @Protobuf(order = 12)
    private Map<Long, String> titleMap = new HashMap<>();

    /**
     * 职位map
     * key:玩家id  value:职位
     */
    @Protobuf(order = 13)
    private Map<Long, Integer> jobMap = new HashMap<>();

    /**
     * 当前投票职位
     */
    @Protobuf(order = 14)
    private SmallGroupJobEnum voteJob;

    /**
     * 投票map
     * key:投票人  value:被投票人
     */
    @Protobuf(order = 15)
    private Map<Long, Long> voteMap = new HashMap<>();

    /**
     * 确定人员集合
     */
    @Protobuf(order = 16)
    private Set<Long> sureMemberIdSet = new HashSet<>();

    @Protobuf(order = 17)
    private PbSmallGroup.SgbNodeType nodeType = PbSmallGroup.SgbNodeType.sgbStart;

    /**
     * 本轮操作结束时间
     * 职位投票有多轮操作，每轮单独计时
     */
    @Protobuf(order = 18)
    private long endTime;


    public SmallGroupBrotherCreate() {
    }

    public SmallGroupBrotherCreate(int createTaskId) {
        super(SmallGroupTypeEnum.brother, createTaskId);
        changeNode(PbSmallGroup.SgbNodeType.sgbSort);
    }

    /**
     * 金兰创建切换节点
     */
    public void sgbNodeTypeNext(){
        switch (nodeType){
            case sgbStart:{
                changeNode(PbSmallGroup.SgbNodeType.sgbTask);
                break;
            }
            case sgbTask:{
                changeNode(PbSmallGroup.SgbNodeType.sgbSort);
                break;
            }
            case sgbSort:{
                changeNode(PbSmallGroup.SgbNodeType.sgbName);
                break;
            }
            case sgbName:{
                changeNode(PbSmallGroup.SgbNodeType.sgbMemberName);
                break;
            }
            case sgbMemberName:{
                changeNode(PbSmallGroup.SgbNodeType.sgbSure);
                break;
            }
            case sgbSure:{
                changeNode(PbSmallGroup.SgbNodeType.sgbFinish);
                break;
            }
        }

        SmallGroupHelper.smallGroupCreateInfoNotify(this);
    }

    public void changeNode(PbSmallGroup.SgbNodeType newNodeType){
        this.nodeType = newNodeType;
        if(newNodeType == PbSmallGroup.SgbNodeType.sgbSort){
            this.voteJob = SmallGroupJobEnum.brother1;
        }
        refreshEndTime();
    }

    public void refreshEndTime() {
        this.endTime = ServerConstants.getCurrentTimeMillis() + 20 * DateTimeUtil.MillisOfSecond;
    }

    @Override
    public SmallGroupInfoBase create() {
        //创建小团体
        Map<Long, SmallGroupMemberInfo> memberMap = new HashMap<>();
        jobMap.forEach((key, value) -> {
            SmallGroupMemberInfo memberInfo = new SmallGroupMemberInfo(key, SmallGroupJobEnum.getSmallGroupJobEnum(value));
            memberMap.put(memberInfo.getPlayerId(), memberInfo);
        });
        return new SmallGroupBrotherInfo(memberMap, getMemberNameMap(), name);
    }

    @Override
    public void writePb(PbSmallGroup.SmallGroupCreateInfo.Builder builder) {
        PbSmallGroup.SmallGroupBrotherCreateInfo.Builder brother = PbSmallGroup.SmallGroupBrotherCreateInfo.newBuilder();
        if(name != null){
            brother.setName(name.genPb());
        }

        brother.putAllTitle(titleMap);
        for (Map.Entry<Long, Integer> entry : jobMap.entrySet()) {
            brother.putJobMap(entry.getKey(), SmallGroupJobEnum.getSmallGroupJobEnum(entry.getValue()).genPb());
        }

        if(voteJob != null){
            brother.setVoteJob(voteJob.genPb());
        }
        brother.putAllVoteMap(voteMap);
        brother.setEndTime(endTime);
        brother.addAllSureMemberId(sureMemberIdSet);
        brother.setNodeType(nodeType);
        builder.setBrother(brother);
    }

    @Override
    public void afterHandler() {
        //解散组队
        TeamHelper.commonDestroyTeamLogic(0, getId(), ServerConstants.getCurrentTimeMillis());
    }

    public SmallGroupBrotherName getName() {
        return name;
    }

    public void setName(SmallGroupBrotherName name) {
        this.name = name;
    }

    public Map<Long, String> getTitleMap() {
        return titleMap;
    }

    public void setTitleMap(Map<Long, String> titleMap) {
        this.titleMap = titleMap;
    }

    public Map<Long, Integer> getJobMap() {
        return jobMap;
    }

    public void setJobMap(Map<Long, Integer> jobMap) {
        this.jobMap = jobMap;
    }

    public SmallGroupJobEnum getVoteJob() {
        return voteJob;
    }

    public void setVoteJob(SmallGroupJobEnum voteJob) {
        this.voteJob = voteJob;
    }

    public Map<Long, Long> getVoteMap() {
        return voteMap;
    }

    public void setVoteMap(Map<Long, Long> voteMap) {
        this.voteMap = voteMap;
    }

    public Set<Long> getSureMemberIdSet() {
        return sureMemberIdSet;
    }

    public void setSureMemberIdSet(Set<Long> sureMemberIdSet) {
        this.sureMemberIdSet = sureMemberIdSet;
    }

    public PbSmallGroup.SgbNodeType getNodeType() {
        return nodeType;
    }

    public void setNodeType(PbSmallGroup.SgbNodeType nodeType) {
        this.nodeType = nodeType;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

}

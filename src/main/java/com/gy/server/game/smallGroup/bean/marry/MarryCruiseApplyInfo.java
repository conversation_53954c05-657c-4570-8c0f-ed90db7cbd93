package com.gy.server.game.smallGroup.bean.marry;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;
import com.gy.server.packet.PbSmallGroup;
import com.ttlike.server.tl.baselib.serialize.smallGroup.MarryCruiseApplyInfoDb;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 情缘巡游申请信息
 * 全服
 * <AUTHOR> 2025/2/11 15:34
 **/
@ProtobufClass
public class MarryCruiseApplyInfo {

    /**
     * 申请的日期当天6点的时间戳
     */
    @Protobuf(order = 1)
    private long time;

    /**
     * key:巡游时间id   value:情缘创建id
     */
    @Protobuf(order = 2)
    private Map<Integer, Long> applyMap = new ConcurrentHashMap<>();

    /**
     * key:情缘创建id   value:开始时间
     */
    @Protobuf(order = 3)
    private Map<Long, Long> startMap = new HashMap<>();

    public MarryCruiseApplyInfo() {

    }

    public MarryCruiseApplyInfo(long time) {
        this.time = time;
    }

    public MarryCruiseApplyInfo(MarryCruiseApplyInfoDb db) {
        this.time = db.getTime();
        this.applyMap.putAll(db.getApplyMap());
        this.startMap.putAll(db.getStartMap());
    }

    public PbSmallGroup.CruiseApplyInfo genPb(){
        PbSmallGroup.CruiseApplyInfo.Builder builder = PbSmallGroup.CruiseApplyInfo.newBuilder();
        builder.setTime(time);
        builder.putAllApplyMap(applyMap);
        return builder.build();
    }

    public MarryCruiseApplyInfoDb genDb(){
        return new MarryCruiseApplyInfoDb(time, applyMap, startMap);
    }

    public long getTime() {
        return time;
    }

    public void setTime(long time) {
        this.time = time;
    }

    public Map<Integer, Long> getApplyMap() {
        return applyMap;
    }

    public void setApplyMap(Map<Integer, Long> applyMap) {
        this.applyMap = applyMap;
    }

    public Map<Long, Long> getStartMap() {
        return startMap;
    }

    public void setStartMap(Map<Long, Long> startMap) {
        this.startMap = startMap;
    }
}

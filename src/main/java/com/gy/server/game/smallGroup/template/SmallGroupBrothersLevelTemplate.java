package com.gy.server.game.smallGroup.template;

import com.gy.server.game.util.StringExtUtil;

import java.util.List;
import java.util.Map;

/**
 * 小团体-结义等级模板
 *
 * <AUTHOR> 2024/12/11 11:52
 **/
public class SmallGroupBrothersLevelTemplate {

    /**
     * 金兰等级
     */
    public int id;

    /**
     * 需求金兰经验
     */
    public int exp;

    /**
     * 金兰最大人数
     */
    public int maxNum;

    /**
     * 金兰技能
     */
    public List<Integer> skill;

    public SmallGroupBrothersLevelTemplate(Map<String, String> map){
        this.id = Integer.parseInt(map.get("id"));
        this.exp = Integer.parseInt(map.get("exp"));
        this.maxNum = Integer.parseInt(map.get("maxNum"));
        this.skill = StringExtUtil.string2List(map.get("skill"), ",", Integer.class);
    }

}

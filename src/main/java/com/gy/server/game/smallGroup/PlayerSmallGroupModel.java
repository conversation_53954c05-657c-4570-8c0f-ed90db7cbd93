package com.gy.server.game.smallGroup;

import com.gy.server.common.distributedlock.DistributedLockUtilManager;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.callback.response.CallbackResponse;
import com.gy.server.core.command.PlayerCommandRequest;
import com.gy.server.game.arena.ArenaRankType;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModel;
import com.gy.server.game.player.event.PlayerEvent;
import com.gy.server.game.player.event.PlayerEventHandler;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.smallGroup.bean.*;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherCreate;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherInfo;
import com.gy.server.game.smallGroup.bean.marry.SmallGroupMarryCreate;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbScene;
import com.gy.server.packet.PbTask;
import com.gy.server.utils.CollectionUtil;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.rpc.TLMessageCallbackTask;
import com.ttlike.server.tl.baselib.serialize.base.RedisListLongBean;
import com.ttlike.server.tl.baselib.serialize.player.PlayerBlobDb;
import com.ttlike.server.tl.baselib.serialize.smallGroup.PlayerSmallGroupModelDb;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 小团体模块
 *
 * <AUTHOR> 2024/12/27 11:08
 **/
public class PlayerSmallGroupModel extends PlayerModel implements PlayerEventHandler {

    /**
     * 金兰等级 内存中
     */
    private AtomicInteger brotherLevel = new AtomicInteger(0);

    /**
     * 情缘等级 内存中
     */
    private AtomicInteger marryLevel = new AtomicInteger(0);

    /**
     * 我的申请map
     * key:表id  value:结识发布者id
     */
    private Map<Integer, CopyOnWriteArraySet<Long>> myApplyMap = new ConcurrentHashMap<>();

    /**
     * 下次可发送结识时间
     */
    private AtomicLong nextTime = new AtomicLong(0);

    /**
     * 小团体任务类型解锁
     */
    private Set<PbTask.TaskType> taskTypeSet = new CopyOnWriteArraySet<>();

    public void updatePlayerSmallGroupIdInfo(PbTask.TaskType type, boolean add) {
        if(add){
            taskTypeSet.add(type);
            getPlayer().getTaskModel().addTaskByType(type);
        }else {
            taskTypeSet.remove(type);
            getPlayer().getTaskModel().removeTaskByType(type);
        }
    }

    public PlayerSmallGroupModel(Player player) {
        super(player);
    }

    public void changeSmallGroupMemoryLevel(SmallGroupTypeEnum type, int newLevel) {
        switch (type){
            case brother: {
                brotherLevel.set(newLevel);
                break;
            }
            case marry: {
                marryLevel.set(newLevel);
                break;
            }
            //TODO:
        }

        getPlayer().postEvent(PlayerEventType.smallGroupUp, type);
    }

    @Override
    protected void loadData(PlayerBlobDb playerBlob) {
        PlayerSmallGroupModelDb db = playerBlob.getSmallGroupModelDb();
        if(db != null){
            for (Map.Entry<Integer, RedisListLongBean> entry : db.getApplyInfoMap().entrySet()) {
                myApplyMap.computeIfAbsent(entry.getKey(), set -> new CopyOnWriteArraySet<>()).addAll(entry.getValue().getList());
            }

            nextTime = new AtomicLong(db.getNextTime());

            for (Integer type : db.getTaskTypeList()) {
                PbTask.TaskType taskType = PbTask.TaskType.forNumber(type);
                if(taskType != null){
                    taskTypeSet.add(taskType);
                }
            }
        }
    }

    @Override
    protected void saveData(PlayerBlobDb playerBlob) {
        PlayerSmallGroupModelDb db = new PlayerSmallGroupModelDb();
        for (Map.Entry<Integer, CopyOnWriteArraySet<Long>> entry : myApplyMap.entrySet()) {
            RedisListLongBean listLongBean = new RedisListLongBean();
            listLongBean.getList().addAll(entry.getValue());
            db.getApplyInfoMap().put(entry.getKey(), listLongBean);
        }
        db.setNextTime(nextTime.get());
        for (PbTask.TaskType taskType : taskTypeSet) {
            db.getTaskTypeList().add(taskType.getNumber());
        }
        playerBlob.setSmallGroupModelDb(db);
    }

    @Override
    public PlayerEventType[] getEventTypes() {
        return new PlayerEventType[]{PlayerEventType.login, PlayerEventType.loginAfter, PlayerEventType.week5Refresh, PlayerEventType.arenaChallenge, PlayerEventType.enterScene};
    }

    @Override
    public void handle(PlayerEvent event) {
        switch (event.getEventType()){
            case login:
            case loginAfter:{
                createInfoDeal();
                inviteInfoDeal();
                cruiseInfoDeal();
                break;
            }
            case week5Refresh:{
                ThreadPool.execute(() -> {
                    PlayerSmallGroupIdInfo idInfo = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, GsRedisKey.SmallGroup.player_small_group_id_info.getRedisKey(getPlayerId()));
                    if(idInfo != null && idInfo.getBrotherId() != 0){
                        String redisKey = GsRedisKey.SmallGroup.small_group_info.getRedisKey(idInfo.getBrotherId());
                        SmallGroupBrotherInfo brotherInfo = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupBrotherInfo.class, redisKey);
                        if(brotherInfo != null){
                            if(ServerConstants.getCurrentTimeMillis() > brotherInfo.getCanRefreshWeekExpTime()){
                                brotherInfo.setWeekExp(0);
                                brotherInfo.setCanRefreshWeekExpTime(ServerConstants.getFirstDayByTime(ServerConstants.getCurrentTimeLocalDateTime(), CommonUtils.getRefreshTimeHour()));
                            }

                            brotherLevel = new AtomicInteger(SmallGroupService.getBrothersLevel(brotherInfo.getExp()));
                        }
                    }
                });
                break;
            }
            case arenaChallenge:{
                //竞技场挑战：1.次数，2.是否胜利，3.排名，4.是否是攻击方 5.排行榜类型ArenaRankType.type
                boolean isWin = event.getParam(1);
                if(isWin){
                    int rank = event.getParam(2);
                    ArenaRankType rankType = ArenaRankType.of(event.getParam(4));
                    if(rank <= 10){
                        String sgIdInfoKey = GsRedisKey.SmallGroup.player_small_group_id_info.getRedisKey(event.getSource().getPlayerId());
                        PlayerSmallGroupIdInfo sgIdInfo = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, sgIdInfoKey);
                        if(sgIdInfo != null && sgIdInfo.getBrotherId() != 0){
                            String brotherInfoKey = GsRedisKey.SmallGroup.small_group_info.getRedisKey(sgIdInfo.getBrotherId());
                            DistributedLockUtilManager.operate(() -> {
                                SmallGroupBrotherInfo brotherInfo = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupBrotherInfo.class, brotherInfoKey);
                                if(brotherInfo != null){
                                    long playerId = event.getSource().getPlayerId();
                                    brotherInfo.addSmallGroupLog(SmallGroupLogTypeEnum.swornBrothersArenaRank, playerId, playerId + "", String.valueOf(rankType.getType()), String.valueOf(rank));
                                }
                            }, brotherInfoKey);
                        }
                    }
                }
                break;
            }
            case teamBattle:{
                //组队战斗 1.组队类型 2.是否胜利 3.成员id
                boolean isWin = event.getParam(1);
                List<Long> memberList = event.getParam(2);
                if(isWin){
                    ThreadPool.execute(() -> {
                        String idKey = GsRedisKey.SmallGroup.player_small_group_id_info.getRedisKey(event.getSource().getPlayerId());
                        PlayerSmallGroupIdInfo idInfo = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, idKey);
                        if(idInfo != null && idInfo.getBrotherId() != 0l){
                            String brotherKey = GsRedisKey.SmallGroup.small_group_info.getRedisKey(idInfo.getBrotherId());
                            DistributedLockUtilManager.operate(() -> {
                                SmallGroupBrotherInfo brotherInfo = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupBrotherInfo.class, brotherKey);
                                if(brotherInfo != null){
                                    memberList.retainAll(brotherInfo.getMemberMap().keySet());
                                    if(!memberList.isEmpty()){
                                        brotherInfo.addSmallGroupLog(SmallGroupLogTypeEnum.swornBrothersPersonBattleTime, event.getSource().getPlayerId());
                                    }
                                }
                            }, brotherKey);
                        }
                    });
                }
                break;
            }
            case enterScene:{
                PbScene.SceneType sceneType = event.getParam(0);
                if(sceneType == PbScene.SceneType.mainCity){
                    cruiseInfoDeal();
                }
                break;
            }
        }
    }

    private void inviteInfoDeal() {
        SmallGroupGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroup);
        Map<String, SmallGroupInviteInfo> map = globalData.getInviteInfoMap().get(getPlayer().getPlayerId());
        if(CollectionUtil.isNotEmpty(map)){
            for (SmallGroupInviteInfo info : map.values()) {
                if(ServerConstants.getCurrentTimeMillis() < info.getEndTime() || info.getEndTime() == -1l){
                    SmallGroupHelper.smallGroupInviteNotify(getPlayer(), info);
                }
            }
        }
    }

    public void cruiseInfoDeal(){
        PlayerCommandRequest request = CommandRequests.newPlayerCommandRequest("SmallGroupWorldCommandService.getProgressCruiseInfo", getPlayer().getPlayerId());
        TLBase.getInstance().getRpcUtil().sendToNodeWithCallBack(new CruiseInfoCallbackTask(), ServerType.WORLD, CommonUtils.getWorldMasterServerId(), request);
    }

    private void createInfoDeal() {
        SmallGroupGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroup);
        ConcurrentHashMap<Integer, SmallGroupCreateSignInfo> map = globalData.getCreateSignInfoMap().get(getPlayerId());
        if(CollectionUtil.isNotEmpty(map)){
            for (SmallGroupCreateSignInfo bean : map.values()) {
                ThreadPool.execute(() -> {
                    String key = GsRedisKey.SmallGroup.small_group_create.getRedisKey(bean.getId());
                    switch (bean.getType()){
                        case brother:{
                            SmallGroupBrotherCreate create = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupBrotherCreate.class, key);
                            if(create != null){
                                SmallGroupHelper.smallGroupCreateInfoNotify(create);
                            }
                            break;
                        }
                        case marry:{
                            SmallGroupMarryCreate create = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupMarryCreate.class, key);
                            if(create != null){
                                SmallGroupHelper.smallGroupCreateInfoNotify(create);
                            }
                            break;
                        }
                    }


                });
            }
        }
    }

    public boolean canAcceptTask(PbTask.TaskType type) {
        return taskTypeSet.contains(type);
    }

    public void addApply(int id, long senderId) {
        myApplyMap.computeIfAbsent(id, set -> new CopyOnWriteArraySet<>()).add(senderId);
    }

    public void removeApply(int id, long senderId){
        Set<Long> set = myApplyMap.get(id);
        if(set != null){
            set.remove(senderId);
            if(set.isEmpty()){
                myApplyMap.remove(id);
            }
        }
    }

    public Map<Integer, CopyOnWriteArraySet<Long>> getMyApplyMap() {
        return myApplyMap;
    }

    public void setMyApplyMap(Map<Integer, CopyOnWriteArraySet<Long>> myApplyMap) {
        this.myApplyMap = myApplyMap;
    }

    public AtomicLong getNextTime() {
        return nextTime;
    }

    public void setNextTime(AtomicLong nextTime) {
        this.nextTime = nextTime;
    }

    public AtomicInteger getBrotherLevel() {
        return brotherLevel;
    }

    public void setBrotherLevel(AtomicInteger brotherLevel) {
        this.brotherLevel = brotherLevel;
    }

    public Set<PbTask.TaskType> getTaskTypeSet() {
        return taskTypeSet;
    }

    public void setTaskTypeSet(Set<PbTask.TaskType> taskTypeSet) {
        this.taskTypeSet = taskTypeSet;
    }

    public AtomicInteger getMarryLevel() {
        return marryLevel;
    }

    public void setMarryLevel(AtomicInteger marryLevel) {
        this.marryLevel = marryLevel;
    }

    private class CruiseInfoCallbackTask extends TLMessageCallbackTask {
        @Override
        public void complete(CallbackResponse response) {
            if(response.getParams().size() > 0){
                PbProtocol.SmallGroupMarryCruiseNotify notify = response.getParam(0);
                getPlayer().send(PtCode.SMALL_GROUP_MARRY_CRUISE_NOTIFY, notify);
            }
        }

        @Override
        public void timeout() {

        }
    }
}

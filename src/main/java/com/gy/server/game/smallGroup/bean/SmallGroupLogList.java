package com.gy.server.game.smallGroup.bean;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 2025/1/24 14:17
 **/
@ProtobufClass
public class SmallGroupLogList {

    @Protobuf(order = 1)
    private List<SmallGroupLog> list = new ArrayList<>();

    public SmallGroupLogList() {
    }

    public List<SmallGroupLog> getList() {
        return list;
    }

    public void setList(List<SmallGroupLog> list) {
        this.list = list;
    }

    public void add(SmallGroupLog smallGroupLog) {
        list.add(smallGroupLog);
    }

    public void add(SmallGroupLogTypeEnum logType, long playerId, String[] params) {
        list.add(new SmallGroupLog(logType, playerId, params));
    }
}

package com.gy.server.game.smallGroup.async.makeAcquaintances;

import com.gy.server.common.distributedlock.DistributedLockUtilManager;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.SmallGroupService;
import com.gy.server.game.smallGroup.bean.PlayerSmallGroupIdInfo;
import com.gy.server.game.smallGroup.bean.makeAcquaintances.MakeAcquaintancesApplyInfo;
import com.gy.server.game.smallGroup.bean.makeAcquaintances.SmallGroupMakeAcquaintancesEnum;
import com.gy.server.game.smallGroup.bean.makeAcquaintances.SmallGroupMakeAcquaintancesInfo;
import com.gy.server.game.smallGroup.template.SmallGroupPlatformTemplate;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.ttlike.server.tl.baselib.TLBase;

/**
 * 结识申请异步处理
 * <AUTHOR> 2025/1/3 11:36
 **/
public class MakeAcquaintancesApplyAsync extends AsyncCall {

    private Player player;
    private SmallGroupPlatformTemplate template;
    private long senderId;
    private long time;

    private int text = Text.没有异常;

    public MakeAcquaintancesApplyAsync(Player player, PbProtocol.MakeAcquaintancesApplyReq req, long time) {
        this.player = player;
        this.template = SmallGroupService.getPlatformTemplateMap().get(req.getId());
        this.senderId = req.getSenderId();
        this.time = time;
    }

    @Override
    public void execute() {
        PbProtocol.MakeAcquaintancesApplyRst.Builder rst = PbProtocol.MakeAcquaintancesApplyRst.newBuilder().setResult(Text.genServerRstInfo(text));
        player.send(PtCode.MAKE_ACQUAINTANCES_APPLY_RST, rst.build(), time);
    }

    @Override
    public void asyncExecute() {
        String redisKey = GsRedisKey.SmallGroup.make_acquaintances_info.getRedisKey(template.id);
        DistributedLockUtilManager.operate(() -> {
            SmallGroupMakeAcquaintancesInfo info = TLBase.getInstance().getRedisAssistant().hashGetBeans(redisKey, senderId + "", SmallGroupMakeAcquaintancesInfo.class);
            if(info == null){
                text = Text.参数异常;
                return;
            }

            if(info.getSenderId() == player.getPlayerId()){
                text = Text.不能结识自己;
                return;
            }

            if(template.type == SmallGroupMakeAcquaintancesEnum.brotherPerson){
                PlayerSmallGroupIdInfo playerSmallGroupIdInfo = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class,
                        GsRedisKey.SmallGroup.player_small_group_id_info.getRedisKey(player.getPlayerId()));
                if(playerSmallGroupIdInfo != null && playerSmallGroupIdInfo.getBrotherId() != 0){
                    text = Text.已有金兰不能结识个人;
                    return;
                }
            }

            MakeAcquaintancesApplyInfo applyInfo = new MakeAcquaintancesApplyInfo(info.getId(), info.getSenderId(), player.getPlayerId());
            String redisKeyTemp = GsRedisKey.SmallGroup.make_acquaintances_apply_info.getRedisKey(info.getSenderId());
            TLBase.getInstance().getRedisAssistant().hashPut(redisKeyTemp, applyInfo.getKey(), applyInfo);

            info.getApplyIdSet().add(player.getPlayerId());
            TLBase.getInstance().getRedisAssistant().hashPut(redisKey, senderId + "", info);

            player.getPlayerSmallGroupModel().addApply(template.id, senderId);
        }, redisKey);
    }
}

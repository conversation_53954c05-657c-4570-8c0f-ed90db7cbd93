package com.gy.server.game.smallGroup.bean;

import com.gy.server.core.ServerConstants;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.serialize.smallGroup.SmallGroupCreateSignInfoDb;

/**
 * 小团体创建简易信息
 *
 * <AUTHOR> 2024/12/19 15:45
 **/
public class SmallGroupCreateSignInfo {
    private long playerId;

    /**
     * 是否是队长
     */
    private boolean isLeader = false;

    private SmallGroupTypeEnum type;

    /**
     * 创建信息唯一标识
     */
    private long id;

    /**
     * 超时时间
     */
    private long overTime;

    public SmallGroupCreateSignInfo(long playerId, SmallGroupTypeEnum type, boolean isLeader, long createId) {
        this.playerId = playerId;
        this.isLeader = isLeader;
        this.type = type;
        this.id = createId;
        this.overTime = ServerConstants.getCurrentTimeMillis() + DateTimeUtil.MillisOfDay * 3;
    }

    public SmallGroupCreateSignInfo(SmallGroupCreateSignInfoDb bean) {
        this.playerId = bean.getPlayerId();
        this.isLeader = bean.isLeader();
        this.type = SmallGroupTypeEnum.getSmallGroupTypeEnum(bean.getType());
        this.id = bean.getId();
        this.overTime = bean.getOverTime();
    }

    public SmallGroupCreateSignInfoDb genDb(){
        return new SmallGroupCreateSignInfoDb(playerId, isLeader, type.getType(), id, overTime);
    }

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public boolean isLeader() {
        return isLeader;
    }

    public void setLeader(boolean leader) {
        isLeader = leader;
    }

    public SmallGroupTypeEnum getType() {
        return type;
    }

    public void setType(SmallGroupTypeEnum type) {
        this.type = type;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getOverTime() {
        return overTime;
    }

    public void setOverTime(long overTime) {
        this.overTime = overTime;
    }
}

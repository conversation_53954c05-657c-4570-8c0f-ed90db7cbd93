package com.gy.server.game.smallGroup.async;

import com.gy.server.core.Configuration;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.smallGroup.SmallGroupHelper;
import com.gy.server.game.smallGroup.bean.SmallGroupInfoBase;
import com.gy.server.packet.PbSmallGroup;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 小团体同步异步处理
 *
 * <AUTHOR> 2024/12/23 11:55
 **/
public class SmallGroupInfoSyncAsync extends Async<PERSON>all {

    private List<SmallGroupInfoBase> groupInfoList = new ArrayList<>();

    private List<PbSmallGroup.SmallGroupInfo> builderList = new ArrayList<>();

    public SmallGroupInfoSyncAsync(SmallGroupInfoBase groupInfo) {
        this.groupInfoList.add(groupInfo);
    }

    public SmallGroupInfoSyncAsync(List<SmallGroupInfoBase> groupInfoList) {
        this.groupInfoList.addAll(groupInfoList);
    }

    @Override
    public void execute() {
        //key:成员id
        Map<Long, List<PbSmallGroup.SmallGroupInfo>> map = new HashMap<>();
        for (PbSmallGroup.SmallGroupInfo bean : builderList) {
            for (PbSmallGroup.SmallGroupMemberInfo memberInfo : bean.getMemberInfoList()) {
                map.computeIfAbsent(memberInfo.getUser().getId(), list -> new ArrayList<>()).add(bean);
            }
        }

        for (Map.Entry<Long, List<PbSmallGroup.SmallGroupInfo>> entry : map.entrySet()) {
            int serverId = Player.getServerId(entry.getKey());
            if(Configuration.serverId == serverId){
                Player onlinePlayer = PlayerManager.getOnlinePlayer(entry.getKey());
                if(onlinePlayer != null){
                    onlinePlayer.dataSyncModule.syncSmallGroup(entry.getValue());
                }
            }else {
                ServerCommandRequest request = CommandRequests.newServerCommandRequest("SmallGroupRstCommandService.syncSmallGroup");
                TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, serverId, request, entry.getKey(), entry.getValue());
            }
        }
    }

    @Override
    public void asyncExecute() {
        groupInfoList.forEach(bean -> builderList.add(SmallGroupHelper.genSmallGroupInfo(bean)));
    }
}

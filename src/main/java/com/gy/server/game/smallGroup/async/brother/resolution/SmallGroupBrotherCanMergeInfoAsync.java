package com.gy.server.game.smallGroup.async.brother.resolution;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.newFriend.FriendGlobalData;
import com.gy.server.game.newFriend.bean.FriendInfo;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.SmallGroupService;
import com.gy.server.game.smallGroup.bean.PlayerSmallGroupIdInfo;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherName;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherInfo;
import com.gy.server.game.smallGroup.template.SmallGroupBrotherResolutionEnum;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.ttlike.server.tl.baselib.TLBase;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * 金兰能合并信息异步处理
 *
 * <AUTHOR> 2024/12/28 13:42
 **/
public class SmallGroupBrotherCanMergeInfoAsync extends AsyncCall {

    private Player player;
    private long time;
    private int text = Text.没有异常;
    private Map<Long, SmallGroupBrotherName> brotherNameMap = new HashMap<>();

    Map<Long, Integer> likabilityMap;

    public SmallGroupBrotherCanMergeInfoAsync(Player player, long time, Map<Long, Integer> likabilityMap) {
        this.player = player;
        this.time = time;
        this.likabilityMap = likabilityMap;
    }

    @Override
    public void execute() {
        PbProtocol.SmallGroupBrotherCanMergeInfoRst.Builder rst = PbProtocol.SmallGroupBrotherCanMergeInfoRst.newBuilder().setResult(Text.genServerRstInfo(text));
        brotherNameMap.forEach((key, value) -> {
            rst.putIdName(key, value.genPb());
        });
        player.send(PtCode.SMALL_GROUP_BROTHER_CAN_MERGE_INFO_RST, rst.build(), time);
    }

    @Override
    public void asyncExecute() {
        String redisKey = GsRedisKey.SmallGroup.player_small_group_id_info.getRedisKey(player.getPlayerId());
        PlayerSmallGroupIdInfo playerSmallGroupIdInfo = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, redisKey);
        if(playerSmallGroupIdInfo == null){
            text = Text.金兰不存在;
            return;
        }

        if(playerSmallGroupIdInfo.getBrotherId() == 0){
            text = Text.金兰不存在;
            return;
        }

        redisKey = GsRedisKey.SmallGroup.small_group_info.getRedisKey(playerSmallGroupIdInfo.getBrotherId());
        SmallGroupBrotherInfo brotherInfo = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupBrotherInfo.class, redisKey);
        if(brotherInfo == null){
            text = Text.金兰不存在;
            return;
        }

        int differNum = SmallGroupService.getBrotherMaxNum(brotherInfo.getExp()) - brotherInfo.getMemberMap().size();

        FriendGlobalData globalData = GlobalDataManager.getData(GlobalDataType.friend);
        Collection<FriendInfo> friendInfos = globalData.getFriendInfo(player.getPlayerId()).getFriendInfos().values();

        brotherNameMap = new HashMap<>();

        for (FriendInfo bean : friendInfos) {
            if(likabilityMap.getOrDefault(bean.getFriendId(), 0) >= SmallGroupService.getSmallGroupConst().swornBrothersLikabilityMixNum){
                String redisKeyTemp = GsRedisKey.SmallGroup.player_small_group_id_info.getRedisKey(bean.getFriendId());
                PlayerSmallGroupIdInfo playerSmallGroupIdInfoTemp = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, redisKeyTemp);
                if(playerSmallGroupIdInfoTemp != null && playerSmallGroupIdInfoTemp.getBrotherId() != 0){
                    //核对人数
                    String brotherRedisKey = GsRedisKey.SmallGroup.small_group_info.getRedisKey(playerSmallGroupIdInfoTemp.getBrotherId());
                    SmallGroupBrotherInfo brotherInfoTemp = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupBrotherInfo.class, brotherRedisKey);
                    if(brotherInfoTemp != null){
                        //过滤自己
                        if(brotherInfo.getId() == brotherInfoTemp.getId()){
                            continue;
                        }

                        if(brotherInfoTemp.getMemberMap().size() > differNum){
                            continue;
                        }

                        if(brotherInfoTemp.canAddResolution(SmallGroupBrotherResolutionEnum.merge, 0) == Text.没有异常){
                            brotherNameMap.put(brotherInfoTemp.getId(), brotherInfoTemp.getSmallGroupName());
                        }
                    }
                }
            }
        }
    }
}

package com.gy.server.game.smallGroup.template;

import com.gy.server.game.drop.RewardTemplate;

import java.util.List;
import java.util.Map;

/**
 *
 * 情缘巡游类型模板
 * <AUTHOR> 2025/1/16 20:46
 **/
public class SmallGroupMarryCruiseClassTemplate {

    public int id;

    /**
     * 奖励
     */
    public List<RewardTemplate> reward;

    /**
     * 消耗
     */
    public List<RewardTemplate> consume;

    /**
     * 宾客默认邀请数量
     */
    public int inviteGuestsNum;

    public SmallGroupMarryCruiseClassTemplate(Map<String, String> map) {
        this.id = Integer.parseInt(map.get("id"));
        this.reward = RewardTemplate.readListFromText(map.get("reward"));
        this.consume = RewardTemplate.readListFromText(map.get("consume"));;
        this.inviteGuestsNum = Integer.parseInt(map.get("inviteGuestsNum"));
    }
}

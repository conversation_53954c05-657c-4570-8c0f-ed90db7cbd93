package com.gy.server.game.smallGroup.commandService;

import com.gy.server.annotation.MessageMethod;
import com.gy.server.annotation.MessageServiceBean;
import com.gy.server.core.MessageServerType;
import com.gy.server.core.command.CommandRequestParams;
import com.gy.server.core.command.PlayerCommandRequest;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.smallGroup.SmallGroupGlobalData;
import com.gy.server.game.smallGroup.SmallGroupHelper;
import com.gy.server.game.smallGroup.async.brother.resolution.SmallGroupBrotherAddInviteSelectDealAsync;
import com.gy.server.game.smallGroup.bean.SmallGroupTypeEnum;
import com.gy.server.packet.PbCommons;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbSmallGroup;
import com.gy.server.packet.PbTask;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.*;
/**
 * gs响应
 * <AUTHOR> 2024/12/17 15:52
 **/
@MessageServiceBean(description = "小团体回应", messageServerType = MessageServerType.game)
public class SmallGroupRstCommandService {

    @MessageMethod(description = "响应处理")
    private static void smallGroupRstDeal(ServerCommandRequest request, CommandRequestParams params){
        Set<Long> memberIdSet = params.getParam(0);
        int ptCode = params.getParam(1);
        PbProtocol.SmallGroupCreateInfoNotify notify = params.getParam(2);

        SmallGroupHelper.gsSendMessage(memberIdSet, ptCode, notify);
    }

    @MessageMethod(description = "小团体创建开始处理")
    private static void smallGroupCreateStartDeal(ServerCommandRequest request, CommandRequestParams params){
        Set<Long> memberIdSet = params.getParam(0);
        int type = params.getParam(1);
        long leaderId = params.getParam(2);
        long createId = params.getParam(3);

        SmallGroupGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroup);
        globalData.addPlayerToSmallGroupCreateInfoMap(memberIdSet, type, leaderId, createId);
    }

    @MessageMethod(description = "删除小团体创建指向标记信息")
    private static void removeCreateSign(ServerCommandRequest request, CommandRequestParams params){
        //key:玩家id value:小团体类型集合
        Map<Long, Set<Integer>> map = params.getParam(0);
        SmallGroupGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroup);
        for (Map.Entry<Long, Set<Integer>> entry : map.entrySet()) {
            globalData.removeCreateSign(entry.getKey(), entry.getValue());
        }
    }

    @MessageMethod(description = "同步小团体")
    private static void syncSmallGroup(ServerCommandRequest request, CommandRequestParams params){
        long playerId = params.getParam(0);
        List<PbSmallGroup.SmallGroupInfo> list = params.getParam(1);

        Player onlinePlayer = PlayerManager.getOnlinePlayer(playerId);
        if(onlinePlayer != null){
            onlinePlayer.dataSyncModule.syncSmallGroup(list);
        }
    }

    @MessageMethod(description = "更新玩家小团体关联")
    private static void updatePlayerSmallGroupMap(ServerCommandRequest request, CommandRequestParams params) {
        Set<Long> playerSet = params.getParam(0);
        int type = params.getParam(1);

        SmallGroupGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroup);
        globalData.smallGroupCrateFinishHandler(playerSet, type);
    }

    @MessageMethod(description = "金兰纳新邀请")
    private static void smallGroupInvite(PlayerCommandRequest request, CommandRequestParams params){
        long targetId = request.getPlayerId();
        int type = params.getParam(0);
        long inviterId = params.getParam(1);
        String inviterName = params.getParam(2);
        long endTime = params.getParam(3);

        SmallGroupHelper.smallGroupInviteDeal(SmallGroupTypeEnum.getSmallGroupTypeEnum(type), targetId, inviterId, inviterName, endTime);
    }

    @MessageMethod(description = "金兰纳新被邀请者回应处理")
    private static void brotherAddTargetSelectDeal(PlayerCommandRequest request, CommandRequestParams params){
        long inviterPlayerId = request.getPlayerId();
        long targetPlayerId = params.getParam(0);
        String targetName = params.getParam(1);
        boolean select = params.getParam(2);
        String inviterName = params.getParam(3);

        ThreadPool.execute(new SmallGroupBrotherAddInviteSelectDealAsync(inviterPlayerId, inviterName, targetPlayerId, targetName, select));
    }

    @MessageMethod(description = "删除申请者身上的记录")
    private static void removeApplyRecord(ServerCommandRequest request, CommandRequestParams params){
        Set<Long> playerIdSet = params.getParam(0);
        long senderId = params.getParam(1);
        int id = params.getParam(2);

        SmallGroupHelper.removeApplyRecord(playerIdSet, senderId, id);
    }

    @MessageMethod(description = "更新小团体任务类型")
    private static void updatePlayerSmallGroupIdInfo(PlayerCommandRequest request, CommandRequestParams params){
        long playerId = request.getPlayerId();
        PbTask.TaskType taskType = params.getParam(0);
        boolean add = params.getParam(1);

        SmallGroupHelper.smallGroupTaskDeal(playerId, taskType, add);
    }

    @MessageMethod(description = "更新小团体内存等级")
    private static void changeSmallGroupMemoryLevel(PlayerCommandRequest request, CommandRequestParams params){
        long playerId = request.getPlayerId();
        int type = params.getParam(0);
        int newLevel = params.getParam(1);

        SmallGroupHelper.changeSmallGroupMemoryLevel(SmallGroupTypeEnum.getSmallGroupTypeEnum(type), newLevel, playerId);
    }

    @MessageMethod(description = "提亲结果通知")
    private static void marryInviterNotify(PlayerCommandRequest request, CommandRequestParams params){
        long playerId = request.getPlayerId();
        boolean result = params.getParam(0);
        int consumeId = params.getParam(1);

        SmallGroupHelper.sendMarryCrateResultMail(playerId, result, consumeId);
    }

    @MessageMethod(description = "情缘巡游通知")
    private static void marryCruiseNotify(ServerCommandRequest request, CommandRequestParams params){
        Set<Long> playerIdSet = params.getParam(0);
        PbProtocol.SmallGroupMarryCruiseNotify notify = params.getParam(1);

        for (long playerId : playerIdSet) {
            Player onlinePlayer = PlayerManager.getOnlinePlayer(playerId);
            if(onlinePlayer != null){
                onlinePlayer.send(PtCode.SMALL_GROUP_MARRY_CRUISE_NOTIFY, notify);
            }
        }
    }

    @MessageMethod(description = "情缘邀请通知")
    private static void marryInviteNotify(PlayerCommandRequest request, CommandRequestParams params){
        long playerId = request.getPlayerId();
        long createId = params.getParam(0);
        PbSmallGroup.InviteFunction fun = params.getParam(1);
        PbCommons.MinMiniUser minMiniUser = params.getParam(2);

        SmallGroupHelper.marryInviteNotify(playerId, createId, fun, minMiniUser);
    }

    @MessageMethod(description = "小团体解散通知")
    private static void smallGroupDisbandNotify(ServerCommandRequest request, CommandRequestParams params){
        Set<Long> memberSet = params.getParam(0);
        int type = params.getParam(1);
        long id = params.getParam(2);
        SmallGroupHelper.smallGroupDisbandNotify(memberSet, type, id);
    }

    @MessageMethod(description = "小团体离开处理")
    private static void smallGroupLeaveGsDeal(PlayerCommandRequest request, CommandRequestParams params){
        long playerId = params.getParam(0);
        SmallGroupTypeEnum type = params.getParam(1);
        long smallGroupId = params.getParam(2);

        SmallGroupHelper.smallGroupLeaveGsDeal(playerId, type, smallGroupId);
    }

    @MessageMethod(description = "通知所有人巡游信息")
    private static void notifyCruiseInfo(ServerCommandRequest request, CommandRequestParams params){
        PbProtocol.SmallGroupMarryCruiseNotify notify = params.getParam(0);
        Set<Long> playerIdSet = params.getParam(1);

        Collection<Player> onlinePlayerSet = new ArrayList<>();
        if(playerIdSet.isEmpty()){
            onlinePlayerSet = PlayerManager.getOnlinePlayers();
        }else {
            for (long playerId : playerIdSet) {
                Player temp = PlayerManager.getOnlinePlayer(playerId);
                if(temp != null){
                    onlinePlayerSet.add(temp);
                }
            }
        }

        for (Player player : onlinePlayerSet) {
            player.send(PtCode.SMALL_GROUP_MARRY_CRUISE_NOTIFY, notify);
        }
    }

    @MessageMethod(description = "通知情缘解除开始")
    private static void marryDisbandStartNotify(PlayerCommandRequest request, CommandRequestParams params){
        PbProtocol.SmallGroupMarryDisbandStartNotify notify = params.getParam(0);
        long playerId = request.getPlayerId();

        SmallGroupHelper.marryDisbandStartNotify(playerId, notify);
    }
}


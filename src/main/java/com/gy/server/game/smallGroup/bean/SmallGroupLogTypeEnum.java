package com.gy.server.game.smallGroup.bean;


import com.gy.server.core.ServerConstants;

import java.util.HashMap;
import java.util.Map;

/**
 * 小团体日志类型
 * <AUTHOR> 2024/12/10 16:25
 **/
public enum SmallGroupLogTypeEnum {

    /**
     * 金兰团队-创建时的人名和创建时间
     * 服务器处理数据 1,2
     */
    swornBrothersCreate,

    /**
     * 金兰团队-至今天数和人数
     * 服务器不处理
     */
    swornBrothersTime,

    /**
     * 金兰团队-华山论剑玩法进入榜单前10
     * 服务器处理数据 1,2
     */
    swornBrothersArenaRank(true){
        @Override
        public void mergeLog(SmallGroupLogList logList, long playerId, String... params) {
            for (SmallGroupLog log : logList.getList()) {
                if(log.getBelong() == playerId){
                    int newRankId = Integer.parseInt(params[1]);
                    int newRank = Integer.parseInt(params[2]);

                    int oldRankId = Integer.parseInt(log.getParams().get(1));
                    int oldRank = Integer.parseInt(log.getParams().get(2));

                    if(newRankId > oldRankId || (newRankId == oldRankId && newRank > oldRank)){
                        log.getParams().set(1, newRankId + "");
                        log.getParams().set(2, newRank + "");
                    }

                    return;
                }
            }

            logList.add(this, playerId, params);
        }
    },

    /**
     * 金兰团队-成员生日
     * 服务器不处理
     */
    swornBrothersBirthday,

    /**
     * 金兰个人-加入时间，至今天数
     * 服务器处理数据 1
     */
    swornBrothersPersonTime,

    /**
     * 金兰个人-一起战斗次数
     * 服务器处理数据 1
     */
    swornBrothersPersonBattleTime(true){
        @Override
        public void mergeLog(SmallGroupLogList logList, long playerId, String... params) {
            for (SmallGroupLog log : logList.getList()) {
                if(log.getLogType().getId() == getId()){
                    int num = Integer.parseInt(log.getParams().get(0));
                    log.getParams().set(0, String.valueOf(num + 1));
                    log.setTime(ServerConstants.getCurrentTimeMillis());
                    return;
                }
            }

            logList.add(this, playerId, params);
        }
    },

    /**
     * 情缘-创建时间
     */
    marryCreateTime,

    /**
     * 情缘-第几对
     */
    marryCreateNum,

    /**
     * 情缘-证婚人
     */
    marryCreateWitnesses,
    ;

    private int id;

    /**
     * 日志类型
     * 1、团队日志
     * 2、个人日志
     */
    private int belongType;

    /**
     * 是否需要合并日志
     */
    private boolean needMerge = false;

    SmallGroupLogTypeEnum() {
    }

    SmallGroupLogTypeEnum(boolean needMerge) {
        this.needMerge = needMerge;
    }

    private static final Map<Integer, SmallGroupLogTypeEnum> MAP = new HashMap<>();

    public static SmallGroupLogTypeEnum of(int type) {
        return MAP.get(type);
    }

    public void load(Map<String, String> map) {
        this.id = Integer.parseInt(map.get("id"));
        this.belongType = Integer.parseInt(map.get("type"));
        MAP.put(id, this);
    }

    public void mergeLog(SmallGroupLogList logList, long playerId, String... params){

    }

    public int getId() {
        return id;
    }

    public int getBelongType() {
        return belongType;
    }

    public boolean isNeedMerge() {
        return needMerge;
    }
}

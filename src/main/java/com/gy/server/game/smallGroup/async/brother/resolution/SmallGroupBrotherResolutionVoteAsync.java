package com.gy.server.game.smallGroup.async.brother.resolution;

import com.gy.server.common.distributedlock.DistributedLockUtilManager;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.async.SmallGroupInfoSyncAsync;
import com.gy.server.game.smallGroup.bean.PlayerSmallGroupIdInfo;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherInfo;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherResolutionBase;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherResolutionMerge;
import com.gy.server.game.smallGroup.template.SmallGroupBrotherResolutionEnum;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.thread.ThreadPool;
import org.apache.commons.lang3.tuple.Pair;

/**
 * 小团体金兰决议投票
 *
 * <AUTHOR> 2024/12/27 15:58
 **/
public class SmallGroupBrotherResolutionVoteAsync extends AsyncCall {

    private Player player;
    private long resolutionId;
    private int select;
    private long time;

    int text = Text.没有异常;


    public SmallGroupBrotherResolutionVoteAsync(Player player, PbProtocol.SmallGroupBrotherResolutionVoteReq req, long time) {
        this.player = player;
        this.resolutionId = req.getResolutionId();
        this.select = req.getSelect();
        this.time = time;
    }

    @Override
    public void execute() {
        player.send(PtCode.SMALL_GROUP_BROTHER_RESOLUTION_VOTE_RST,
                PbProtocol.SmallGroupBrotherResolutionVoteRst.newBuilder().setResult(Text.genServerRstInfo(text)).build(), time);

    }

    @Override
    public void asyncExecute() {
        if(select != -1 && select != 0 && select != 1){
            text = Text.参数异常;
            return;
        }

        String redisKey = GsRedisKey.SmallGroup.player_small_group_id_info.getRedisKey(player.getPlayerId());
        PlayerSmallGroupIdInfo playerSmallGroupIdInfo = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, redisKey);
        if(playerSmallGroupIdInfo == null){
            text = Text.金兰不存在;
            return;
        }

        if(playerSmallGroupIdInfo.getBrotherId() == 0){
            text = Text.金兰不存在;
            return;
        }

        String redisKeyTemp = GsRedisKey.SmallGroup.small_group_info.getRedisKey(playerSmallGroupIdInfo.getBrotherId());
        DistributedLockUtilManager.operate(() -> {
            SmallGroupBrotherInfo brotherInfo = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupBrotherInfo.class, redisKeyTemp);
            if(brotherInfo == null){
                text = Text.金兰不存在;
                return;
            }

            SmallGroupBrotherResolutionBase resolution = brotherInfo.getResolutionMap().get(resolutionId);
            if(resolution == null){
                text = Text.金兰决议不存在;
                return;
            }

            if(resolution.getVoteMap().containsKey(player.getPlayerId())){
                text = Text.金兰决议已经投票;
                return;
            }

            SmallGroupBrotherResolutionEnum resolutionEnum = resolution.getSmallGroupBrotherResolutionEnum();
            //能否投票
            if(!resolutionEnum.canVoteCheck(player.getPlayerId(), resolution)){
                text = Text.此决议没有权限投票;
                return;
            }

            resolution.getVoteMap().put(player.getPlayerId(), select);

            //检查投票是否完成
            Pair<Boolean, Boolean> finish = resolutionEnum.voteFinishCheck(resolution, brotherInfo);
            if(finish.getLeft()){
                resolution.setVoteResult(finish.getRight() ? 1 : -1);
                if(finish.getRight()){
                    resolutionEnum.voteFinishDeal(brotherInfo, resolution);
                }else {
                    if(resolutionEnum.getType() == SmallGroupBrotherResolutionEnum.merge.getType()){
                        SmallGroupBrotherResolutionMerge merge = (SmallGroupBrotherResolutionMerge) resolution;
                        long antherBrotherId = merge.getAntherBrotherId(brotherInfo.getId());
                        String antherRedisKeyTemp = GsRedisKey.SmallGroup.small_group_info.getRedisKey(antherBrotherId);
                        DistributedLockUtilManager.operate(() -> {
                            SmallGroupBrotherInfo antherBrotherInfo = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupBrotherInfo.class, antherRedisKeyTemp);
                            if(antherBrotherInfo != null){
                                SmallGroupBrotherResolutionMerge antherMerge = antherBrotherInfo.getMergeMap().get(merge.getId());
                                if(antherMerge != null){
                                    antherMerge.setVoteResult(-1);
                                    TLBase.getInstance().getRedisAssistant().setBean(antherRedisKeyTemp, antherBrotherInfo);
                                }
                            }
                        }, antherRedisKeyTemp);
                    }
                }
            }

            TLBase.getInstance().getRedisAssistant().setBean(redisKeyTemp, brotherInfo);
            if(resolutionEnum != SmallGroupBrotherResolutionEnum.merge || resolutionEnum != SmallGroupBrotherResolutionEnum.disband){
                ThreadPool.execute(new SmallGroupInfoSyncAsync(brotherInfo));
            }
        }, redisKeyTemp);
    }
}

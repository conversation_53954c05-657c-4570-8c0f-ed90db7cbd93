package com.gy.server.game.smallGroup.bean.brother;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;
import com.gy.server.packet.PbSmallGroup;

/**
 * 小团体金兰名号
 *
 * <AUTHOR> 2024/12/10 15:38
 **/
@ProtobufClass
public class SmallGroupBrotherName {

    @Protobuf(order = 1)
    private long first;

    @Protobuf(order = 2)
    private String second;

    @Protobuf(order = 3)
    private String third;

    @Protobuf(order = 4)
    private String fourth;

    /**
     * 金兰人数
     */
    private int num;

    public SmallGroupBrotherName() {
    }

    public SmallGroupBrotherName(long first, String second, String third, String fourth, int num) {
        this.first = first;
        this.second = second;
        this.third = third;
        this.fourth = fourth;
        this.num = num;
    }

    public String getFriendGroupName() {
        return first + second + third + "%s" + fourth;
    }

    public PbSmallGroup.SmallGroupBrotherName genPb() {
        PbSmallGroup.SmallGroupBrotherName.Builder builder = PbSmallGroup.SmallGroupBrotherName.newBuilder();
        builder.setFirst(first);
        builder.setSecond(second);
        builder.setThird(third);
        builder.setFourth(fourth);
        builder.setNum(num);
        return builder.build();
    }

    public long getFirst() {
        return first;
    }

    public void setFirst(long first) {
        this.first = first;
    }

    public String getSecond() {
        return second;
    }

    public void setSecond(String second) {
        this.second = second;
    }

    public String getThird() {
        return third;
    }

    public void setThird(String third) {
        this.third = third;
    }

    public String getFourth() {
        return fourth;
    }

    public void setFourth(String fourth) {
        this.fourth = fourth;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

}

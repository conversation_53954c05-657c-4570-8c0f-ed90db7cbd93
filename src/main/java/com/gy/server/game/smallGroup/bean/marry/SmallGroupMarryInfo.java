package com.gy.server.game.smallGroup.bean.marry;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;
import com.gy.server.game.smallGroup.SmallGroupHelper;
import com.gy.server.game.smallGroup.bean.*;
import com.gy.server.packet.PbSmallGroup;
import com.ttlike.server.tl.baselib.serialize.base.RedisListIntegerBean;

import java.util.*;

/**
 * 小团体-情缘信息
 *
 * <AUTHOR> 2025/2/18 14:25
 **/
@ProtobufClass
public class SmallGroupMarryInfo extends SmallGroupInfoBase {

    /**
     * 情缘等级奖励领取map
     * key:成员id  value:已领取奖励id
     */
    @Protobuf(order = 51)
    private Map<Long, RedisListIntegerBean> levelRewardMap = new HashMap<>();

    /**
     * 是否强制离开
     */
    @Protobuf(order = 52)
    private boolean force;

    /**
     * 退出冷静期
     */
    @Protobuf(order = 53)
    private long period;

    /**
     * 协议解散同意成员列表
     */
    @Protobuf(order = 54)
    private List<Long> agreeList = new ArrayList<>();

    public SmallGroupMarryInfo() {

    }

    public SmallGroupMarryInfo(Map<Long, SmallGroupMemberInfo> memberMap) {
        super(SmallGroupTypeEnum.marry, memberMap);
    }

    @Override
    public void writePb(PbSmallGroup.SmallGroupInfo.Builder builder) {
        PbSmallGroup.SmallGroupMarryInfo.Builder marryInfo = PbSmallGroup.SmallGroupMarryInfo.newBuilder();
        for (Map.Entry<Long, RedisListIntegerBean> entry : levelRewardMap.entrySet()) {
            PbSmallGroup.MarryLevelRewardReceiveInfo.Builder temp = PbSmallGroup.MarryLevelRewardReceiveInfo.newBuilder();
            temp.setPlayerId(entry.getKey());
            temp.addAllLevel(entry.getValue().getList());
            marryInfo.setLevelRewardReceiveInfo(temp.build());
        }
        marryInfo.setForce(force);
        marryInfo.setPeriod(period);
        marryInfo.addAllAgree(agreeList);
        builder.setMarry(marryInfo.build());
    }

    public void removeAllMember() {
        for (long memberId : getMemberList()) {
            SmallGroupHelper.smallGroupLeaveDeal(memberId, getType(), getId());
        }
    }

    @Override
    public String getFriendGroupName() {
        return super.getFriendGroupName();
    }

    public Map<Long, RedisListIntegerBean> getLevelRewardMap() {
        return levelRewardMap;
    }

    public void setLevelRewardMap(Map<Long, RedisListIntegerBean> levelRewardMap) {
        this.levelRewardMap = levelRewardMap;
    }

    public boolean isForce() {
        return force;
    }

    public void setForce(boolean force) {
        this.force = force;
    }

    public long getPeriod() {
        return period;
    }

    public void setPeriod(long period) {
        this.period = period;
    }

    public List<Long> getAgreeList() {
        return agreeList;
    }

    public void setAgreeList(List<Long> agreeList) {
        this.agreeList = agreeList;
    }

}

package com.gy.server.game.smallGroup.bean;

import com.gy.server.packet.PbSmallGroup;

/**
 * 小团体职位枚举
 *
 * <AUTHOR> 2024/12/10 14:45
 **/
public enum SmallGroupJobEnum {

    /**************************金兰****************************/
    brother1(101),
    brother2(102),
    brother3(103),
    brother4(104),
    brother5(105),
    brother6(106),
    brother7(107),
    brother8(108),
    brother9(109),


    /**************************情缘****************************/
    marry1(201),
    marry2(202),


    /**************************师徒****************************/
    teacher1(301),   //师
    teacher2(302),   //徒

    ;

    /**
     * 小团体内标记
     */
    private int sign;

    SmallGroupJobEnum(int sign) {
//        this.type = type;
        this.sign = sign;
    }

    public static SmallGroupJobEnum getSmallGroupJobEnum(int type){
        switch (type){
            case 101: return brother1;
            case 102: return brother2;
            case 103: return brother3;
            case 104: return brother4;
            case 105: return brother5;
            case 106: return brother6;
            case 107: return brother7;
            case 108: return brother8;
            case 109: return brother9;
            case 201: return marry1;
            case 202: return marry2;
            default : return null;
        }
    }

    public static SmallGroupJobEnum getSmallGroupJobEnum(SmallGroupTypeEnum type, int index) {
        for (SmallGroupJobEnum bean : SmallGroupJobEnum.values()) {
            if(bean.getSmallGroupTypeEnum() == type && bean.getSign() == index){
                return bean;
            }
        }
        return null;
    }

    public SmallGroupJobEnum next(){
        for (SmallGroupJobEnum bean : SmallGroupJobEnum.values()) {
            if(bean.getSmallGroupTypeEnum() == this.getSmallGroupTypeEnum() && bean.sign == this.sign + 1){
                return bean;
            }
        }

        return null;
    }

    public PbSmallGroup.SmallGroupJob genPb() {
        PbSmallGroup.SmallGroupJob.Builder builder = PbSmallGroup.SmallGroupJob.newBuilder();
        builder.setType(getSmallGroupTypeEnum().getType());
        builder.setSign(sign);
        return builder.build();
    }

    public SmallGroupTypeEnum getSmallGroupTypeEnum() {
        switch (this){
            case brother1:
            case brother2:
            case brother3:
            case brother4:
            case brother5:
            case brother6:
            case brother7:
            case brother8:
            case brother9:{
                return SmallGroupTypeEnum.brother;
            }
            case marry1:
            case marry2:{
                return SmallGroupTypeEnum.marry;
            }
            default : return null;
        }
    }

    public int getSign() {
        return sign;
    }

    public void setSign(int sign) {
        this.sign = sign;
    }

}

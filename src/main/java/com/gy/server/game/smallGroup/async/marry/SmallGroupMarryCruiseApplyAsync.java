package com.gy.server.game.smallGroup.async.marry;

import com.gy.server.common.distributedlock.DistributedLockUtilManager;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.callback.response.CallbackResponse;
import com.gy.server.core.command.PlayerCommandRequest;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.SmallGroupGlobalData;
import com.gy.server.game.smallGroup.SmallGroupHelper;
import com.gy.server.game.smallGroup.SmallGroupService;
import com.gy.server.game.smallGroup.bean.PlayerSmallGroupIdInfo;
import com.gy.server.game.smallGroup.bean.SmallGroupCreateSignInfo;
import com.gy.server.game.smallGroup.bean.SmallGroupTypeEnum;
import com.gy.server.game.smallGroup.bean.marry.SmallGroupMarryCreate;
import com.gy.server.game.smallGroup.template.SmallGroupMarryCruiseClassTemplate;
import com.gy.server.game.smallGroup.template.SmallGroupMarryCruiseReservationTemplate;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbSmallGroup;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.rpc.TLMessageCallbackTask;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 情缘巡游申请
 * <AUTHOR> 2025/2/11 11:26
 **/
public class SmallGroupMarryCruiseApplyAsync extends AsyncCall {

    private Player player;

    private long cruiseTime;
    private int timeId;
    private int cruiseId;

    private long time;

    private List<Reward> cost = new ArrayList<>();

    private int text = Text.没有异常;

    public SmallGroupMarryCruiseApplyAsync(Player player, PbProtocol.SmallGroupMarryCruiseApplyReq req, long time) {
        this.player = player;
        this.cruiseTime = req.getTime();
        this.timeId = req.getTimeId();
        this.cruiseId = req.getCruiseId();
        this.time = time;
    }

    @Override
    public void execute() {
        PbProtocol.SmallGroupMarryCruiseApplyRst.Builder rst = PbProtocol.SmallGroupMarryCruiseApplyRst.newBuilder().setResult(Text.genServerRstInfo(text));
        player.send(PtCode.SMALL_GROUP_MARRY_CRUISE_APPLY_RST, rst.build(), time);

        if(text == Text.没有异常){
            Reward.remove(cost, player, BehaviorType.smallGroupMarryCruiseApply);
        }else {
            if(!cost.isEmpty() && text != Text.消耗不足){
                Reward.add(cost, player, BehaviorType.smallGroupMarryCruiseApply);
            }
        }
    }

    @Override
    public void asyncExecute() {
        LocalDateTime cruiseDateTime = DateTimeUtil.toLocalDateTime(cruiseTime);
        LocalDateTime cruiseTimeTemp = ServerConstants.getOneDayOneHour(cruiseDateTime, CommonUtils.getRefreshTimeHour());

        LocalDateTime now6 = ServerConstants.getOneDayOneHour(ServerConstants.getCurrentTimeLocalDateTime(), CommonUtils.getRefreshTimeHour());
        if(cruiseTimeTemp.isBefore(now6)){
            text = Text.此时段已过期;
            return;
        }

        LocalDateTime maxTime = now6.plusDays(14);
        if(cruiseTimeTemp.isAfter(maxTime)){
            text = Text.此时段尚未开放预约;
            return;
        }

        SmallGroupGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroup);
        SmallGroupCreateSignInfo createSignInfo = globalData.getCreateSignInfo(player.getPlayerId(), SmallGroupTypeEnum.marry);
        if(createSignInfo == null){
            text = Text.参数异常;
            return;
        }

        String redisKey1 = GsRedisKey.SmallGroup.player_small_group_id_info.getRedisKey(player.getPlayerId());
        PlayerSmallGroupIdInfo idInfo = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, redisKey1);
        if(idInfo != null && idInfo.getMarryId() != 0){
            text = Text.当前已有情缘;
            return;
        }

        SmallGroupMarryCruiseReservationTemplate reservationTemplate = SmallGroupService.getMarryCruiseReservationTemplateMap().get(timeId);
        if(reservationTemplate == null && cruiseId != 1){
            text = Text.对应的模板数据找不到;
            return;
        }

        SmallGroupMarryCruiseClassTemplate cruiseClassTemplate = SmallGroupService.getMarryCruiseClassTemplateMap().get(cruiseId);
        if(cruiseClassTemplate == null){
            text = Text.对应的模板数据找不到;
            return;
        }

        String redisKey2 = GsRedisKey.SmallGroup.small_group_create.getRedisKey(createSignInfo.getId());
        DistributedLockUtilManager.operate(() -> {
            SmallGroupMarryCreate marryCreate = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupMarryCreate.class, redisKey2);
            if(marryCreate == null){
                text = Text.情缘当前无创建;
                return;
            }

            if(marryCreate.getNodeType() == PbSmallGroup.SgmNodeType.sgmCruise && !marryCreate.isCruiseResult()){
                text = Text.巡游正在进行中;
                return;
            }

            if(marryCreate.getNodeType() != PbSmallGroup.SgmNodeType.sgmWait){
                text = Text.巡游已结束;
                return;
            }

            if(marryCreate.getCruiseId() != 0 && ServerConstants.getCurrentTimeMillis() < marryCreate.getEndTime()){
                text = Text.巡游类型已选择;
                return;
            }

            //最低档次不进行巡游
            if(cruiseId == 1){
                marryCreate.setCruiseResult(true);
                marryCreate.setInviteNum(cruiseClassTemplate.inviteGuestsNum);
                marryCreate.changeNode(PbSmallGroup.SgmNodeType.sgmCruise);
                marryCreate.setEndTime(ServerConstants.getCurrentTimeMillis());
                TLBase.getInstance().getRedisAssistant().setBean(redisKey2, marryCreate);

                SmallGroupHelper.smallGroupCreateInfoNotify(marryCreate);
                return;
            }

            long start = DateTimeUtil.toMillis(cruiseTimeTemp) - DateTimeUtil.MillisOfHour * 6 + DateTimeUtil.MillisOfMinute * reservationTemplate.startTime;
            if(ServerConstants.getCurrentTimeMillis() >= start){
                text = Text.此时段已过期;
                return;
            }

            //检查消耗
            cost = Reward.templateCollectionToReward(cruiseClassTemplate.consume);
            if(Reward.check(player, cost) != -1){
                text = Text.消耗不足;
                cost.clear();
                return;
            }
            Reward.remove(cost, player, BehaviorType.smallGroupMarryCruiseApply);

            int masterWorldId = CommonUtils.getWorldMasterServerId();
            PlayerCommandRequest request = CommandRequests.newPlayerCommandRequest("SmallGroupWorldCommandService.marryCruiseApply", player.getPlayerId());
            TLBase.getInstance().getRpcUtil().sendToNodeWithCallBack(new ExecuteCallbackTask(marryCreate, cruiseClassTemplate, start, reservationTemplate, redisKey2, createSignInfo), ServerType.WORLD, masterWorldId, request, DateTimeUtil.toMillis(cruiseTimeTemp), timeId, marryCreate.getId(), start);
        }, redisKey2);
    }

    private class ExecuteCallbackTask extends TLMessageCallbackTask {
        private final SmallGroupMarryCreate marryCreate;
        private final SmallGroupMarryCruiseClassTemplate cruiseClassTemplate;
        private final long start;
        private final SmallGroupMarryCruiseReservationTemplate reservationTemplate;
        private final String redisKey2;
        private final SmallGroupCreateSignInfo createSignInfo;

        public ExecuteCallbackTask(SmallGroupMarryCreate marryCreate, SmallGroupMarryCruiseClassTemplate cruiseClassTemplate, long start, SmallGroupMarryCruiseReservationTemplate reservationTemplate, String redisKey2, SmallGroupCreateSignInfo createSignInfo) {
            this.marryCreate = marryCreate;
            this.cruiseClassTemplate = cruiseClassTemplate;
            this.start = start;
            this.reservationTemplate = reservationTemplate;
            this.redisKey2 = redisKey2;
            this.createSignInfo = createSignInfo;
        }

        @Override
        public void complete(CallbackResponse callbackResponse) {
            text = callbackResponse.getParam(0);
            if(text != Text.没有异常){
                return;
            }
            int marryNum = callbackResponse.getParams().size() > 1 ? callbackResponse.getParam(1) : 1;

            marryCreate.setMarryNum(marryNum);
            marryCreate.setCruiseId(cruiseId);
            marryCreate.setInviteNum(cruiseClassTemplate.inviteGuestsNum);
            marryCreate.setCruiseStart(start);
            if(marryCreate.getNodeType() == PbSmallGroup.SgmNodeType.sgmCruise){
                marryCreate.changeNode(PbSmallGroup.SgmNodeType.sgmWait);
            }

            long end = start + DateTimeUtil.MillisOfMinute * (reservationTemplate.endTime - reservationTemplate.startTime);
            marryCreate.setEndTime(end);
            TLBase.getInstance().getRedisAssistant().setBean(redisKey2, marryCreate);

            //超时时间延长
            createSignInfo.setOverTime(createSignInfo.getOverTime() + DateTimeUtil.MillisOfDay * 3);

            SmallGroupHelper.smallGroupCreateInfoNotify(marryCreate);
        }

        @Override
        public void timeout() {
            text = Text.巡游请求超时;
        }
    }
}
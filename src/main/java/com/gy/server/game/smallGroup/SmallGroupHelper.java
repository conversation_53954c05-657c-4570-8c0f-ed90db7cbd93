package com.gy.server.game.smallGroup;

import com.google.protobuf.AbstractMessage;
import com.gy.server.common.distributedlock.DistributedLockUtilManager;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.core.Configuration;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.command.PlayerCommandRequest;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.core.log.CommonLogger;
import com.gy.server.game.currency.Currency;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.mail.MailManager;
import com.gy.server.game.mail.MailType;
import com.gy.server.game.newFriend.FriendHelper;
import com.gy.server.game.newFriend.bean.FriendGroupInfo;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.smallGroup.async.SmallGroupInfoSyncAsync;
import com.gy.server.game.smallGroup.bean.*;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherInfo;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherCreate;
import com.gy.server.game.smallGroup.bean.SmallGroupCreateBase;
import com.gy.server.game.smallGroup.bean.marry.SmallGroupMarryCreate;
import com.gy.server.game.smallGroup.bean.marry.SmallGroupMarryInfo;
import com.gy.server.game.smallGroup.template.SmallGroupMarryProposeConsumeTemplate;
import com.gy.server.game.text.Text;
import com.gy.server.packet.*;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;


/**
 * 小团体辅助类
 *
 * <AUTHOR> 2024/12/17 15:34
 **/
public class SmallGroupHelper {

    public static void smallGroupCreateInfoNotify(SmallGroupCreateBase create){
        smallGroupCreateInfoNotify(create, false);
    }

    public static void smallGroupCreateInfoNotify(SmallGroupCreateBase create, boolean createStart){
        ThreadPool.execute(() -> {
            Map<Integer, Set<Long>> serverPlayerIdMap = create.getServerAllMemberMap();

            PbProtocol.SmallGroupCreateInfoNotify notify = create.genPb();
            smallGroupCreateInfoNotify(serverPlayerIdMap, notify);

            if(createStart){
                for (Map.Entry<Integer, Set<Long>> entry : serverPlayerIdMap.entrySet()) {
                    if(entry.getKey() == Configuration.serverId){
                        SmallGroupGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroup);
                        globalData.addPlayerToSmallGroupCreateInfoMap(entry.getValue(), create.getType(), create.getLeaderId(), create.getId());
                    }else {
                        ServerCommandRequest request = CommandRequests.newServerCommandRequest("SmallGroupRstCommandService.smallGroupCreateStartDeal");
                        TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, entry.getKey(), request, entry.getValue(), create.getType().getType(), create.getLeaderId(), create.getId());
                    }
                }
            }
        });
    }

    public static void smallGroupCreateInfoNotify(Map<Integer, Set<Long>> serverPlayerIdMap, PbProtocol.SmallGroupCreateInfoNotify notify){
        int ptCode = PtCode.SMALL_GROUP_CREATE_INFO_NOTIFY;
        for (Map.Entry<Integer, Set<Long>> entry : serverPlayerIdMap.entrySet()) {
            if(Configuration.serverId == entry.getKey()){
                gsSendMessage(entry.getValue(), ptCode, notify);
//                System.out.println("SmallGroupHelper smallGroupCreateInfoNotify " + notify);
            }else {
                ServerCommandRequest request = CommandRequests.newServerCommandRequest("SmallGroupRstCommandService.smallGroupRstDeal");
                TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, entry.getKey(), request, entry.getValue(), ptCode, notify);
            }
        }
    }

    public static void gsSendMessage(Collection<Long> memberIdSet, int ptCode, PbProtocol.SmallGroupCreateInfoNotify notify){
        for (Long memberId : memberIdSet) {
            gsSendMessage(memberId, ptCode, notify);
        }
    }

    public static void gsSendMessage(long playerId, int ptCode, AbstractMessage msg){
        Player player = PlayerManager.getOnlinePlayer(playerId);
        if(player != null){
            player.send(ptCode, msg);
        }
    }

    public static void smallGroupBrotherCreateFailNotify(SmallGroupBrotherCreate brotherCreate) {
        Map<Integer, Set<Long>> serverPlayerIdMap = brotherCreate.getServerAllMemberMap();
        PbProtocol.SmallGroupCreateInfoNotify.Builder notify = PbProtocol.SmallGroupCreateInfoNotify.newBuilder().setResult(Text.genServerRstInfo(Text.金兰创建失败));
        smallGroupCreateInfoNotify(serverPlayerIdMap, notify.build());
    }

    //key:服务器id     value:key:玩家id value:小团体类型集合
    public static void removeCreateDeal(Map<Integer, Map<Long, Set<Integer>>> removeMap, SmallGroupTypeEnum type) {
//        Map<Integer, Set<Long>> serverPlayerIdSet = new HashMap<>();
        for (Map.Entry<Integer, Map<Long, Set<Integer>>> entry : removeMap.entrySet()) {
            int serverId = entry.getKey();
            Map<Long, Set<Integer>> map = entry.getValue();

            if(serverId == Configuration.serverId){
                SmallGroupGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroup);

                for (Map.Entry<Long, Set<Integer>> temp : map.entrySet()) {
                    globalData.removeCreateSign(temp.getKey(), temp.getValue());
                }
            }else{
                ServerCommandRequest request = CommandRequests.newServerCommandRequest("SmallGroupRstCommandService.removeCreateSign");
                TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, entry.getKey(), request, map);
            }
        }
    }

    /**
     * 异步使用
     * @param groupInfo
     * @returns
     */
    public static PbSmallGroup.SmallGroupInfo genSmallGroupInfo(SmallGroupInfoBase groupInfo){
        PbSmallGroup.SmallGroupInfo.Builder builder = PbSmallGroup.SmallGroupInfo.newBuilder();
        builder.setId(groupInfo.getId());
        builder.setType(groupInfo.getType().getType());
        builder.setExp(groupInfo.getExp());
        builder.setWeekExp(groupInfo.getWeekExp());

        List<MiniGamePlayer> miniGamePlayerList = PlayerHelper.getMiniPlayers(groupInfo.getMemberMap().keySet());
        groupInfo.getMemberMap().values().forEach(bean -> {
            builder.addMemberInfo(genSmallGroupMemberInfo(bean, miniGamePlayerList));
        });

        for (SmallGroupLogList list : groupInfo.getCommonLogMap().values()) {
            for (SmallGroupLog bean : list.getList()) {
                builder.addLog(bean.genPb());
            }
        }

        for (SmallGroupLogList list : groupInfo.getPersonLogMap().values()) {
            for (SmallGroupLog bean : list.getList()) {
                builder.addLog(bean.genPb());
            }
        }

        String groupIdKey = GsRedisKey.SmallGroup.small_group_group_id.getRedisKey(groupInfo.getId());
        long groupId = TLBase.getInstance().getRedisAssistant().getLong(groupIdKey);
        if(groupId > 0l){
            builder.setGroupId(groupId);
        }

        groupInfo.writePb(builder);
        return builder.build();
    }

    public static PbSmallGroup.SmallGroupMemberInfo genSmallGroupMemberInfo(SmallGroupMemberInfo bean, List<MiniGamePlayer> list) {
        PbSmallGroup.SmallGroupMemberInfo.Builder builder =  PbSmallGroup.SmallGroupMemberInfo.newBuilder();
        list.forEach(miniGamePlayer -> {
            if(miniGamePlayer.getPlayerId() == bean.getPlayerId()){
                builder.setUser(miniGamePlayer.genMinMiniUser());
            }
        });
        builder.setJob(bean.getJob().genPb());
        builder.setJoinTime(bean.getJoinTime());
        return builder.build();
    }

    /**
     * 小团体创建后置处理
     * @param groupInfo
     */
    public static void smallGroupCrateFinishHandler(SmallGroupInfoBase groupInfo){
        //redis保存
        ThreadPool.execute(() -> {
            TLBase.getInstance().getRedisAssistant().setBean(GsRedisKey.SmallGroup.small_group_info.getRedisKey(groupInfo.getId()), groupInfo);

            String[] redisKeys = new String[groupInfo.getMemberMap().size()];
            List<Long> idList = new ArrayList<>(groupInfo.getMemberMap().keySet());
            for(int i = 0; i < idList.size(); i++){
                redisKeys[i] = GsRedisKey.SmallGroup.player_small_group_id_info.getRedisKey(idList.get(i));
            }

            DistributedLockUtilManager.operate(() -> {
                for (String redisKey : redisKeys) {
                    PlayerSmallGroupIdInfo groupIdInfo = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, redisKey);
                    if(groupIdInfo == null){
                        groupIdInfo = new PlayerSmallGroupIdInfo();
                    }

                    switch (groupInfo.getType()){
                        case brother:{
                            groupIdInfo.setBrotherId(groupInfo.getId());
                            break;
                        }
                        case marry:{
                            groupIdInfo.setMarryId(groupInfo.getId());
                            break;
                        }
                    }
                    TLBase.getInstance().getRedisAssistant().setBean(redisKey, groupIdInfo);
                }
            }, redisKeys);
        });

        //更新SmallGroupGlobalData指向
        SmallGroupGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroup);
        globalData.smallGroupCrateFinishHandler(groupInfo);

        //同步
        ThreadPool.execute(new SmallGroupInfoSyncAsync(groupInfo));

        smallGroupGroupCreateDeal(groupInfo);
    }

    public static void smallGroupGroupCreateDeal(SmallGroupInfoBase groupInfo){
        //创建群聊
        long groupId = FriendHelper.group().create(groupInfo.getFriendGroupName(), 0, -1, 2);
        //创建群聊
        for (Long friendId : groupInfo.getMemberMap().keySet()) {
            FriendHelper.lockFriendGroupOperate(() -> {
                //加入成员
                FriendHelper.group().addMember(groupId, friendId);
            }, friendId);
        }
        //
        String groupIdKey = GsRedisKey.SmallGroup.small_group_group_id.getRedisKey(groupInfo.getId());
        TLBase.getInstance().getRedisAssistant().setLong(groupIdKey, groupId);

        //处理小团体任务
        smallGroupTaskDeal(groupInfo.getMemberMap().keySet(), groupInfo.getType(), true);

        //构建推送消息
        FriendGroupInfo friendGroupInfo = FriendHelper.group().getGroupById(groupId);

        PbProtocol.FriendGroupUpdateNotify notify = PbProtocol.FriendGroupUpdateNotify.newBuilder()
                .setIsDestroy(false)
                .setGroupInfo(FriendHelper.fillGroupInfo(friendGroupInfo)).build();

        //玩家推送
        for (Long notifyPlayerId : friendGroupInfo.getMemberIds()) {
            FriendHelper.sendMessage(notifyPlayerId, PtCode.FRIEND_GROUP_UPDATE_NOTIFY, notify);
        }
    }

    /**
     * 群聊加入处理
     */
    public static void smallGroupJoinDeal(long playerId, long smallGroupId){
        String redisKey = GsRedisKey.SmallGroup.small_group_group_id.getRedisKey(smallGroupId);
        long groupId = TLBase.getInstance().getRedisAssistant().getLong(redisKey);
        if(groupId != 0l){
            FriendHelper.group().addMember(groupId, playerId);

            //构建推送消息
            FriendGroupInfo friendGroupInfo = FriendHelper.group().getGroupById(groupId);
            if(friendGroupInfo != null){
                PbProtocol.FriendGroupUpdateNotify notify = PbProtocol.FriendGroupUpdateNotify.newBuilder()
                        .setIsDestroy(false)
                        .setGroupInfo(FriendHelper.fillGroupInfo(friendGroupInfo)).build();
                FriendHelper.sendMessage(playerId, PtCode.FRIEND_GROUP_UPDATE_NOTIFY, notify);
            }
        }
    }

    /**
     * 小团体离开处理
     */
    public static void smallGroupLeaveDeal(long playerId, SmallGroupTypeEnum type, long smallGroupId){
        int serverId = Player.getServerId(playerId);
        if(serverId == Configuration.serverId){
            smallGroupLeaveGsDeal(playerId, type, smallGroupId);
        }else {
            PlayerCommandRequest request = CommandRequests.newPlayerCommandRequest("SmallGroupRstCommandService.smallGroupLeaveGsDeal", playerId);
            TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, serverId, request, playerId, type, smallGroupId);
        }

    }

    public static void smallGroupLeaveGsDeal(long playerId, SmallGroupTypeEnum type, long smallGroupId){
        int serverId = Player.getServerId(playerId);
        if(serverId == Configuration.serverId){
            Player player = PlayerManager.getOnlinePlayer(playerId);
            if(player != null){
                smallGroupLeaveGsDeal(player, type, smallGroupId);
            }else {
                ThreadPool.execute(() -> {
                    Player temp = PlayerManager.getPlayer(playerId);
                    if(temp != null){
                        smallGroupLeaveGsDeal(temp, type, smallGroupId);
                    }
                });
            }
        }
    }

    public static void smallGroupLeaveGsDeal(Player player, SmallGroupTypeEnum type, long smallGroupId){
        //称号处理
        smallGroupLeaveTitleDeal(player, type);
        //任务处理
        smallGroupTaskDeal(player, type, false);
        //聊天群处理
        smallGroupLeaveGroupDeal(player.getPlayerId(), type, smallGroupId);
    }

    /**
     * 离开小团体群聊处理
     * @param playerId
     * @param smallGroupId
     */
    public static void smallGroupLeaveGroupDeal(long playerId, SmallGroupTypeEnum type, long smallGroupId){
        if(smallGroupId != 0l){
            String redisKey = GsRedisKey.SmallGroup.small_group_group_id.getRedisKey(smallGroupId);
            long groupId = TLBase.getInstance().getRedisAssistant().getLong(redisKey);
            if(groupId != 0l){
                LeaveGroupDeal(playerId, groupId);
            }
        }else {
            List<FriendGroupInfo> list = FriendHelper.group().getAllGroupByPlayerId(playerId);
            for (FriendGroupInfo bean : list) {
                //TODO
                if(type == SmallGroupTypeEnum.brother && bean.getType() == 2){
                    LeaveGroupDeal(playerId, bean.getId());
                }
            }
        }
    }

    private static void LeaveGroupDeal(long playerId, long groupId){
        //删除
        FriendHelper.group().removeMember(groupId, playerId);
        FriendHelper.secretChatChange(true, playerId, groupId);

        FriendGroupInfo bean = FriendHelper.group().getGroupById(groupId);
        bean.getMemberIds().remove(playerId);
        if(bean.getMemberIds().isEmpty()){
            FriendHelper.group().remove(bean.getId());
        }
    }

    /**
     * 离开小团体称号处理
     */
    public static void smallGroupLeaveTitleDeal(Player player, SmallGroupTypeEnum type){
        switch (type){
            case brother:{
                player.getRoleModel().removeDesignation(SmallGroupService.getSmallGroupConst().swornBrothersDesignation, BehaviorType.smallGroupBrotherLeave);
                break;
            }
            case marry:{
                player.getRoleModel().removeDesignation(SmallGroupService.getSmallGroupConst().marryDesignation, BehaviorType.smallGroupMarryDisband);
                break;
            }
        }
    }

    /**
     * 处理任务
     */
    public static void smallGroupTaskDeal(Collection<Long> playerIds, SmallGroupTypeEnum type, boolean add){
        PbTask.TaskType taskType;
        switch (type){
            case brother:{
                taskType = PbTask.TaskType.TASK_BROTHER;
                break;
            }
            default: return;
        }

        for (Long playerId : playerIds) {
            int serverId = Player.getServerId(playerId);
            if(serverId == Configuration.serverId){
                smallGroupTaskDeal(playerId, taskType, add);
            }else {
                PlayerCommandRequest request = CommandRequests.newPlayerCommandRequest("SmallGroupRstCommandService.updatePlayerSmallGroupIdInfo", playerId);
                TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, serverId, request, taskType, true);
            }
        }
    }

    public static void smallGroupTaskDeal(long playerId, PbTask.TaskType type, boolean add){
        Player player = PlayerManager.getOnlinePlayer(playerId);
        if(player != null){
            smallGroupTaskDeal(player, type, add);
        }else {
            ThreadPool.execute(() -> {
                Player temp = PlayerManager.getPlayer(playerId);
                if(temp != null){
                    smallGroupTaskDeal(temp, type, add);
                }
            });
        }
    }

    private static void smallGroupTaskDeal(Player player, SmallGroupTypeEnum type, boolean add) {
        PbTask.TaskType taskType;
        switch (type){
            case brother:{
                taskType = PbTask.TaskType.TASK_BROTHER;
                break;
            }
            default: return;
        }

        smallGroupTaskDeal(player, taskType, add);
    }

    public static void smallGroupTaskDeal(Player player, PbTask.TaskType type, boolean add){
        player.getPlayerSmallGroupModel().updatePlayerSmallGroupIdInfo(type, add);
    }

    public static void smallGroupInviteNotify(Player player, SmallGroupInviteInfo info) {
        PbProtocol.SmallGroupInviteNotify builder = info.genNotify();
        player.send(PtCode.SMALL_GROUP_INVITE_NOTIFY, builder);
    }

    /**
     * 刷新成员职位，异步使用
     * @param brotherInfo
     */
    public static void refreshMemberJob(SmallGroupBrotherInfo brotherInfo){
        List<SmallGroupMemberInfo> list = new ArrayList<>(brotherInfo.getMemberMap().values());
        list.sort(Comparator.comparingInt(o -> o.getJob().getSign()));

        SmallGroupJobEnum job = SmallGroupJobEnum.brother1;
        for (SmallGroupMemberInfo bean : list) {
            SmallGroupMemberInfo memberInfo = brotherInfo.getMemberMap().get(bean.getPlayerId());
            if(memberInfo != null){
                memberInfo.setJob(job);
                job = job.next();
            }
        }

        brotherInfo.getSmallGroupName().setNum(brotherInfo.getMemberMap().size());
    }

    public static void removeApplyRecord(Set<Long> playerIdSet, long senderId, int id){
        ThreadPool.execute(() -> {
            for (Long playerId : playerIdSet) {
                Player player = PlayerManager.getPlayer(playerId);
                if(player != null){
                    PlayerSmallGroupModel model = player.getPlayerSmallGroupModel();
                    model.removeApply(id, senderId);
                }
            }
        });
    }

    /**
     * 增加金兰经验值
     * @param addValue
     */
    public static void addSmallGroupExp(long playerId, long addValue, Currency currency) {
        if(!Configuration.serverType.isCombat()){
            ThreadPool.execute(() -> {
                String redisKey = GsRedisKey.SmallGroup.player_small_group_id_info.getRedisKey(playerId);
                PlayerSmallGroupIdInfo groupIdInfo = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, redisKey);

                if(groupIdInfo == null){
                    return;
                }

                switch (currency){
                    case swornBrothersExp:{
                        if(groupIdInfo.getBrotherId() != 0){
                            String redisKeyTemp = GsRedisKey.SmallGroup.small_group_info.getRedisKey(groupIdInfo.getBrotherId());
                            addExp(redisKeyTemp, addValue);
                        }
                        break;
                    }
                    case coupleExp:
                    case friendshipExp:
                }
            });
        }
    }

    private static void addExp(String redisKey, long addValue){
        DistributedLockUtilManager.operate(() -> {
            SmallGroupInfoBase groupInfo = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupBrotherInfo.class, redisKey);
            if(groupInfo != null){
                int oldLevel = SmallGroupService.getBrothersLevel(groupInfo.getExp());

                groupInfo.addExp((int) addValue);
                TLBase.getInstance().getRedisAssistant().setBean(redisKey, groupInfo);

                int newLevel = SmallGroupService.getBrothersLevel(groupInfo.getExp());
                if(newLevel > oldLevel){
                    //更改金兰在线成员小团体模块内存数据
                    SmallGroupHelper.changeSmallGroupMemoryLevel(groupInfo.getType(), newLevel, groupInfo.getMemberMap().keySet());
                }
            }
        }, redisKey);
    }

    /**
     * 更改金兰在线成员小团体模块内存数据
     * @param type
     * @param newLevel
     * @param memberIds 成员id集合
     */
    private static void changeSmallGroupMemoryLevel(SmallGroupTypeEnum type, int newLevel, Collection<Long> memberIds) {
        for (Long memberId : memberIds) {
            int serverId = Player.getServerId(memberId);
            if(serverId == Configuration.serverId){
                changeSmallGroupMemoryLevel(type, newLevel, memberId);
            }else {
                PlayerCommandRequest request = CommandRequests.newPlayerCommandRequest("SmallGroupRstCommandService.changeSmallGroupMemoryLevel", memberId);
                TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, serverId, request, type.getType(), newLevel);
            }
        }
    }

    /**
     * 改金兰在线成员小团体模块内存数据
     * @param type
     * @param newLevel
     * @param memberId 成员id
     */
    public static void changeSmallGroupMemoryLevel(SmallGroupTypeEnum type, int newLevel, long memberId){
        Player onlinePlayer = PlayerManager.getOnlinePlayer(memberId);
        if(onlinePlayer != null){
            onlinePlayer.getPlayerSmallGroupModel().changeSmallGroupMemoryLevel(type, newLevel);
        }
    }

    public static void sendTaskToWorld(){
//        WorldAllocation.getInstance().getWorldServerId();
    }

    public static void smallGroupInviteDeal(SmallGroupTypeEnum type, long targetId, long playerId, String playerName, long endTime){
        smallGroupInviteDeal(type, targetId, playerId, playerName, endTime, 0);
    }

    /**
     * 被邀请人处理
     * @param targetId 被邀请人id
     * @param playerId 邀请人id
     * @param playerName 邀请人名字
     * @param endTime 邀请有效期
     */
    public static void smallGroupInviteDeal(SmallGroupTypeEnum type, long targetId, long playerId, String playerName, long endTime, int consumeId) {
        int serverId = Player.getServerId(targetId);
        if(serverId == Configuration.serverId){
            SmallGroupInviteInfo info = new SmallGroupInviteInfo(type.getType(), targetId, playerId, playerName, endTime, consumeId);
            SmallGroupGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroup);
            globalData.getInviteInfoMap().computeIfAbsent(info.getTargetId(), map -> new ConcurrentHashMap<>()).put(info.getKey(), info);

            Player targetPlayer = PlayerManager.getOnlinePlayer(info.getTargetId());
            if(targetPlayer != null){
                SmallGroupHelper.smallGroupInviteNotify(targetPlayer, info);
            }
        }else {
            PlayerCommandRequest request = CommandRequests.newPlayerCommandRequest("SmallGroupRstCommandService.smallGroupInvite", targetId);
            TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, Player.getServerId(targetId), request, type.getType(), playerId, playerName, endTime);
        }
    }

    public static void sendMarryCrateResultMail(long playerId, boolean result, int consumeId){
        MailType mailType = result ? MailType.proposeMarriageSuccess : MailType.proposeMarriageFail;
        List<Reward> rewardList = new ArrayList<>();

        SmallGroupMarryProposeConsumeTemplate proposeConsumeTemplate = SmallGroupService.getMarryProposeConsumeTemplateMap().get(consumeId);
        if(proposeConsumeTemplate == null){
            CommonLogger.error("SmallGroupMarryProposeConsumeTemplate is null, id is " + consumeId);
            return;
        }

        rewardList.addAll(Reward.templateCollectionToReward(result ? proposeConsumeTemplate.reward : proposeConsumeTemplate.consume));
        MailManager.sendMail(mailType, playerId, Text.genText(mailType.getTitleId()).build(), Text.genText(mailType.getContentId()).build(),
                ServerConstants.getCurrentTimeMillis(), rewardList);
    }

    /**
     * 创建情缘关系处理
     * @param marryCreate
     */
    public static void crateMarryInfoDeal(SmallGroupMarryCreate marryCreate) {
        SmallGroupInfoBase marryInfo = marryCreate.create();

        SmallGroupHelper.smallGroupCreateInfoNotify(marryCreate);
        SmallGroupHelper.smallGroupCrateFinishHandler(marryInfo);
    }

    /**
     * 情缘邀请通知
     */
    public static void marryInviteNotify(long beInviterId, long targetTeamId, PbSmallGroup.InviteFunction function, long invitor) {
        int serverId = Player.getServerId(beInviterId);
        MiniGamePlayer miniGamePlayer = PlayerManager.getMiniPlayer(invitor);
        if(serverId == Configuration.serverId){
            marryInviteNotify(beInviterId, targetTeamId, function, miniGamePlayer.genMinMiniUser());
        }else {
            PlayerCommandRequest request = CommandRequests.newPlayerCommandRequest("SmallGroupRstCommandService.marryInviteNotify", beInviterId);
            TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, serverId, request, targetTeamId, function, miniGamePlayer.genMinMiniUser());
        }
    }

    public static void marryInviteNotify(long beInviterId, long createId, PbSmallGroup.InviteFunction function, PbCommons.MinMiniUser invitor){
        Player player = PlayerManager.getOnlinePlayer(beInviterId);
        if(player != null){
            PbProtocol.SmallGroupMarryInviteNotify.Builder notify = PbProtocol.SmallGroupMarryInviteNotify.newBuilder();
            notify.setCreateId(createId);
            notify.setFunction(function);
            notify.setInvitor(invitor);
            player.send(PtCode.SMALL_GROUP_MARRY_INVITE_NOTIFY, notify.build());
        }
    }

    /**
     * 小团体解散通知
     * @param info
     */
    public static void smallGroupDisbandNotify(SmallGroupInfoBase info){
        smallGroupDisbandNotify(info.getServerMemberMap(), info.getType().getType(), info.getId());
    }

    /**
     * 小团体解散通知
     * @param serverMemberMap
     * @param type
     * @param id
     */
    public static void smallGroupDisbandNotify(Map<Integer, Set<Long>> serverMemberMap, int type, long id){
        for (Map.Entry<Integer, Set<Long>> entry : serverMemberMap.entrySet()) {
            if(entry.getKey() == Configuration.serverId){
                smallGroupDisbandNotify(entry.getValue(), type, id);
            }else {
                ServerCommandRequest request = CommandRequests.newServerCommandRequest("SmallGroupRstCommandService.smallGroupDisbandNotify");
                TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, entry.getKey(), request, entry.getValue(), type, id);
            }
        }
    }

    public static void smallGroupDisbandNotify(Set<Long> memberSet, int type, long id){
        PbProtocol.SmallGroupDisbandNotify.Builder notify = PbProtocol.SmallGroupDisbandNotify.newBuilder();
        notify.setType(type);
        notify.setId(id);

        for (long memberId : memberSet) {
            Player player = PlayerManager.getOnlinePlayer(memberId);
            if(player != null){
                player.send(PtCode.SMALL_GROUP_DISBAND_NOTIFY, notify.build());
            }
        }
    }

    /**
     * 异步使用
     * @param bean
     * @return
     */
    public static PbProtocol.SmallGroupMarryCruiseNotify genSmallGroupMarryCruiseNotify(PbSmallGroup.CruiseInfo bean){
        PbProtocol.SmallGroupMarryCruiseNotify.Builder notify = PbProtocol.SmallGroupMarryCruiseNotify.newBuilder();
        notify.setCruise(bean);
        List<MiniGamePlayer> miniGamePlayerList = PlayerHelper.getMiniPlayers(bean.getJobMapMap().keySet());
        for (MiniGamePlayer miniGamePlayer : miniGamePlayerList) {
            notify.addMember(miniGamePlayer.genMinMiniUser());
        }
        return notify.build();
    }

    public static void dealTeamDestroy(long leaderId) {
        ThreadPool.execute(() -> {
            String idInfoRedisKey = GsRedisKey.SmallGroup.player_small_group_id_info.getRedisKey(leaderId);
            PlayerSmallGroupIdInfo idInfo = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, idInfoRedisKey);
            if(idInfo != null){
                if(idInfo.getMarryId() != 0){
                    String redisKey = GsRedisKey.SmallGroup.small_group_info.getRedisKey(idInfo.getMarryId());
                    SmallGroupMarryInfo marryInfo = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupMarryInfo.class, redisKey);
                    if(marryInfo != null){
                        if(marryInfo.getPeriod() == 0 && marryInfo.getAgreeList().size() != marryInfo.getMemberList().size()){
                            marryInfo.getAgreeList().clear();
                            TLBase.getInstance().getRedisAssistant().setBean(redisKey, marryInfo);
                        }
                    }
                }
            }
        });
    }

    public static void marryDisbandStartNotify(long playerId, PbProtocol.SmallGroupMarryDisbandStartNotify notify){
        Player onlinePlayer = PlayerManager.getOnlinePlayer(playerId);
        if(onlinePlayer != null){
            onlinePlayer.send(PtCode.SMALL_GROUP_MARRY_DISBAND_START_NOTIFY, notify);

//            System.out.println("marryDisbandStartNotify " + notify + ", playerId is " + playerId);
        }
    }
}

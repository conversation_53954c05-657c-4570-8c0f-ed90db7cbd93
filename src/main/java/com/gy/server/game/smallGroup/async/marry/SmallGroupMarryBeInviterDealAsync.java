package com.gy.server.game.smallGroup.async.marry;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.core.Configuration;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.command.PlayerCommandRequest;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.newFriend.FriendGlobalData;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.game.role.PlayerRoleService;
import com.gy.server.game.role.template.ProtagonistTemplate;
import com.gy.server.game.smallGroup.SmallGroupGlobalData;
import com.gy.server.game.smallGroup.SmallGroupHelper;
import com.gy.server.game.smallGroup.bean.PlayerSmallGroupIdInfo;
import com.gy.server.game.smallGroup.bean.SmallGroupInviteInfo;
import com.gy.server.game.smallGroup.bean.SmallGroupJobEnum;
import com.gy.server.game.smallGroup.bean.SmallGroupTypeEnum;
import com.gy.server.game.smallGroup.bean.marry.SmallGroupMarryCreate;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 情缘被邀请人处理 异步处理
 * <AUTHOR> 2025/2/8 17:32
 **/
public class SmallGroupMarryBeInviterDealAsync extends AsyncCall {

    private Player player;
    private long inviterId;
    private boolean select;
    private long time;

    private int consumeId;

    private int text = Text.没有异常;

    public SmallGroupMarryBeInviterDealAsync(Player player, PbProtocol.SmallGroupMarryBeInviterDealReq req, long time) {
        this.player = player;
        this.inviterId = req.getInviterId();
        this.select = req.getSelect();
        this.time = time;
    }

    @Override
    public void execute() {
        PbProtocol.SmallGroupMarryBeInviterDealRst.Builder rst = PbProtocol.SmallGroupMarryBeInviterDealRst.newBuilder().setResult(Text.genServerRstInfo(text));
        player.send(PtCode.SMALL_GROUP_MARRY_BE_INVITER_DEAL_RST, rst.build(), time);

        boolean result = text == Text.没有异常;
        marryCreateResultMailDeal(result, inviterId);
        marryCreateResultMailDeal(result, player.getPlayerId());
    }

    /**
     * 情缘创建结果邮件处理
     * @param createResult
     * @param playerId
     */
    private void marryCreateResultMailDeal(boolean createResult, long playerId){
        if(!createResult && playerId != inviterId){
            return;
        }

        int serverId = Player.getServerId(playerId);
        if(serverId == Configuration.serverId){
            SmallGroupHelper.sendMarryCrateResultMail(playerId, createResult, consumeId);
        }else {
            PlayerCommandRequest request = CommandRequests.newPlayerCommandRequest("SmallGroupRstCommandService.marryInviterNotify", player.getPlayerId());
            TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, serverId, request, createResult, consumeId);
        }
    }



    @Override
    public void asyncExecute() {
        FriendGlobalData friendGlobalData = GlobalDataManager.getData(GlobalDataType.friend);
        if(!friendGlobalData.isFriend(player.getPlayerId(), inviterId)){
            text = Text.该玩家不是你的好友;
            return;
        }

        SmallGroupGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroup);
        if(globalData.getCreateSignInfo(player.getPlayerId(), SmallGroupTypeEnum.marry) != null){
            text = Text.当前有情缘正在创建;
            return;
        }

        String idInfoKey = GsRedisKey.SmallGroup.player_small_group_id_info.getRedisKey(player.getPlayerId());
        PlayerSmallGroupIdInfo idInfo = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, idInfoKey);
        if(idInfo != null && idInfo.getMarryId() != 0l){
            text = Text.当前已有情缘;
            return;
        }

        ConcurrentHashMap<String, SmallGroupInviteInfo> map = globalData.getInviteInfoMap().get(player.getPlayerId());
        if(CollectionUtil.isNotEmpty(map)){
            String key = inviterId + "" + SmallGroupTypeEnum.marry.getType();
            SmallGroupInviteInfo inviteInfo = map.get(key);
            if(inviteInfo != null){
                consumeId = inviteInfo.getConsumeId();

                if(select){
                    //同意
                    SmallGroupMarryCreate marryCreate = new SmallGroupMarryCreate(0);
                    marryCreate.addServerMember(Player.getServerId(player.getPlayerId()), player.getPlayerId(), player.getName(), false);
                    marryCreate.addServerMember(Player.getServerId(inviteInfo.getInviterId()), inviteInfo.getInviterId(), inviteInfo.getInviterName(), true);

                    SmallGroupJobEnum job = player.getSex() == 1 ? SmallGroupJobEnum.marry1 : SmallGroupJobEnum.marry2;
                    marryCreate.getJobMap().put(player.getPlayerId(), job.getSign());
                    marryCreate.getJobMap().put(inviteInfo.getInviterId(), (job == SmallGroupJobEnum.marry1 ? SmallGroupJobEnum.marry2 : SmallGroupJobEnum.marry1).getSign());

                    for (Map.Entry<Long, Integer> entry : marryCreate.getJobMap().entrySet()) {
                        if(entry.getValue() == SmallGroupJobEnum.marry2.getSign()){
                            MiniGamePlayer miniGamePlayer = PlayerHelper.getMiniPlayer(entry.getKey());
                            if(miniGamePlayer != null){
                                ProtagonistTemplate protagonistTemplate = PlayerRoleService.getProtagonistTemplate(miniGamePlayer.getProfession(), miniGamePlayer.getGender());
                                marryCreate.setProfessionId(protagonistTemplate.id);
                            }
                        }
                    }

                    long duration = DateTimeUtil.MillisOfDay * 3;
                    marryCreate.setEndTime(ServerConstants.getCurrentTimeMillis() + duration);

                    String redisKey = GsRedisKey.SmallGroup.small_group_create.getRedisKey(marryCreate.getId());
                    TLBase.getInstance().getRedisAssistant().setBean(redisKey, marryCreate);
                    TLBase.getInstance().getRedisAssistant().pexpire(redisKey, duration);

                    SmallGroupHelper.smallGroupCreateInfoNotify(marryCreate, true);
                }
            }

            //删除邀请信息
            map.remove(key);
            if(map.isEmpty()){
                globalData.getInviteInfoMap().remove(player.getPlayerId());
            }

            return;
        }

        text = Text.未找到邀请信息或邀请已过期;
    }
}

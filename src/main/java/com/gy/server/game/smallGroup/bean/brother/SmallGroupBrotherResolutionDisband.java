package com.gy.server.game.smallGroup.bean.brother;

import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.template.SmallGroupBrotherResolutionEnum;

/**
 * 金兰决议解散
 *
 * <AUTHOR> 2025/1/2 15:12
 **/
@ProtobufClass
public class SmallGroupBrotherResolutionDisband extends SmallGroupBrotherResolutionBase {

    public SmallGroupBrotherResolutionDisband() {
    }

    public SmallGroupBrotherResolutionDisband(long initiatorId, String initiatorName) {
        super(SmallGroupBrotherResolutionEnum.disband, initiatorId, initiatorName);
    }

    public SmallGroupBrotherResolutionDisband(Player player) {
        super(SmallGroupBrotherResolutionEnum.disband, player.getPlayerId(), player.getName());
    }
}

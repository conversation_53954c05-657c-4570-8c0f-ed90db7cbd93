package com.gy.server.game.smallGroup.async.makeAcquaintances;

import com.gy.server.common.distributedlock.DistributedLockUtilManager;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.newFriend.FriendGlobalData;
import com.gy.server.game.newFriend.FriendHelper;
import com.gy.server.game.newFriend.bean.PlayerFriendInfo;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.MakeAcquaintancesHelper;
import com.gy.server.game.smallGroup.bean.makeAcquaintances.MakeAcquaintancesApplyInfo;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.Objects;

/**
 * 收到的结识申请处理
 * <AUTHOR> 2025/1/3 17:41
 **/
public class MakeAcquaintancesApplyDealAsync extends AsyncCall {

    private Player player;
    /**
     * 结识platform id
     */
    private int id;
    /**
     * 申请人id
     */
    private long applyId;
    /**
     * true同意/false拒绝
     */
    private boolean agree;
    private long time;

    private int text = Text.没有异常;
    MakeAcquaintancesApplyInfo applyInfo;

    public MakeAcquaintancesApplyDealAsync(Player player, PbProtocol.MakeAcquaintancesApplyDealReq req, long time) {
        this.player = player;
        this.id = req.getId();
        this.applyId = req.getApplyId();
        this.agree = req.getAgree();
        this.time = time;
    }

    @Override
    public void execute() {
        if(Objects.nonNull(applyInfo)){
            logic:{
                if (agree) {
                    FriendGlobalData globalData = GlobalDataManager.getData(GlobalDataType.friend);
                    PlayerFriendInfo friendInfo = globalData.getFriendInfo(player.getPlayerId());
                    if(friendInfo.getFriendInfos().containsKey(applyId)){
                        text = Text.已经是好友关系;
                        break logic;
                    }
                    if(friendInfo.getFriendBlackInfos().containsKey(applyId)){
                        text = Text.黑名单列表中不能添加好友;
                        break logic;
                    }
                    if(friendInfo.getFriendInfos().size() >= FriendHelper.好友数量上限()){
                        text = Text.好友数量已达上限;
                        break logic;
                    }
                    //开始添加好友
                    FriendHelper.dealApply(player, applyId, -1, null);

                    //删除结识
                    MakeAcquaintancesHelper.dealMakeAcquaintances(id, player.getPlayerId());

                    applyInfo.setState(1);
                }else {
                    applyInfo.setState(-1);
                }
            }
            ThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    String redisKey = GsRedisKey.SmallGroup.make_acquaintances_apply_info.getRedisKey(player.getPlayerId());
                    String hashKey = id + "" + applyId;
                    TLBase.getInstance().getRedisAssistant().hashPut(redisKey, hashKey, applyInfo);
                }
            });
        }
        PbProtocol.MakeAcquaintancesApplyDealRst.Builder rst = PbProtocol.MakeAcquaintancesApplyDealRst.newBuilder().setResult(Text.genServerRstInfo(text));
        player.send(PtCode.MAKE_ACQUAINTANCES_APPLY_DEAL_RST, rst.build(), time);
    }

    @Override
    public void asyncExecute() {
        String redisKey = GsRedisKey.SmallGroup.make_acquaintances_apply_info.getRedisKey(player.getPlayerId());
        DistributedLockUtilManager.operate(() -> {
            String hashKey = id + "" + applyId;
            applyInfo = TLBase.getInstance().getRedisAssistant().hashGetBeans(redisKey, hashKey, MakeAcquaintancesApplyInfo.class);
            if(applyInfo == null){
                text = Text.结识申请不存在;
            }
        }, redisKey);
    }




}

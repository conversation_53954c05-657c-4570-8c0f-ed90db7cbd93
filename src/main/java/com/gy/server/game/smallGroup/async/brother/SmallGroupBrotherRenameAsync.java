package com.gy.server.game.smallGroup.async.brother;

import com.gy.server.common.distributedlock.DistributedLockUtilManager;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.SmallGroupService;
import com.gy.server.game.smallGroup.async.SmallGroupInfoSyncAsync;
import com.gy.server.game.smallGroup.bean.PlayerSmallGroupIdInfo;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherName;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherInfo;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherResolutionRename;
import com.gy.server.game.smallGroup.template.SmallGroupBrotherResolutionEnum;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

/**
 *
 * 金兰改名异步处理
 * <AUTHOR> 2024/12/31 17:40
 **/
public class SmallGroupBrotherRenameAsync extends AsyncCall {

    private Player player;
    private SmallGroupBrotherName brotherName;
    private long time;

    private int text = Text.没有异常;

    public SmallGroupBrotherRenameAsync(Player player, PbProtocol.SmallGroupBrotherRenameReq req, long time) {
        this.player = player;
        this.brotherName = new SmallGroupBrotherName(req.getFirst(), req.getSecond(), req.getThird(), req.getFourth(), 0);
        this.time = time;
    }

    @Override
    public void execute() {
        PbProtocol.SmallGroupBrotherRenameRst.Builder rst = PbProtocol.SmallGroupBrotherRenameRst.newBuilder().setResult(Text.genServerRstInfo(text));
        player.send(PtCode.SMALL_GROUP_BROTHER_RENAME_RST, rst.build(), time);
    }

    @Override
    public void asyncExecute() {
        int check = SmallGroupService.smallGroupBrotherNameCheck(brotherName);
        if(check != Text.没有异常){
            text = check;
            return;
        }

        String redisKeyPlayerSmallGroupIdInfo = GsRedisKey.SmallGroup.player_small_group_id_info.getRedisKey(player.getPlayerId());
        PlayerSmallGroupIdInfo playerSmallGroupIdInfo = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, redisKeyPlayerSmallGroupIdInfo);
        if(playerSmallGroupIdInfo == null){
            text = Text.金兰不存在;
            return;
        }

        if(playerSmallGroupIdInfo.getBrotherId() == 0){
            text = Text.金兰不存在;
            return;
        }

        String redisKeySmallGroupInfo = GsRedisKey.SmallGroup.small_group_info.getRedisKey(playerSmallGroupIdInfo.getBrotherId());
        DistributedLockUtilManager.operate(() -> {
            SmallGroupBrotherInfo brotherInfo = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupBrotherInfo.class, redisKeySmallGroupInfo);
            if(brotherInfo == null){
                text = Text.金兰不存在;
                return;
            }

            int textTemp = brotherInfo.canAddResolution(SmallGroupBrotherResolutionEnum.rename, player.getPlayerId());
            if(textTemp != Text.没有异常){
                text = textTemp;
                return;
            }

            brotherName.setNum(brotherInfo.getMemberMap().size());
            SmallGroupBrotherResolutionRename resolution = new SmallGroupBrotherResolutionRename(player.getPlayerId(), player.getName(), brotherName);
            brotherInfo.addResolution(resolution);
            TLBase.getInstance().getRedisAssistant().setBean(redisKeySmallGroupInfo, brotherInfo);

            ThreadPool.execute(new SmallGroupInfoSyncAsync(brotherInfo));
        }, redisKeySmallGroupInfo);
    }
}

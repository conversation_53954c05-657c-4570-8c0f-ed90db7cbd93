package com.gy.server.game.smallGroup.async.brother.create;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.callback.response.CallbackResponse;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.newFriend.FriendHelper;
import com.gy.server.game.newFriend.PlayerFriendModel;
import com.gy.server.game.newFriend.bean.FriendPersonInfo;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.SmallGroupGlobalData;
import com.gy.server.game.smallGroup.SmallGroupHelper;
import com.gy.server.game.smallGroup.SmallGroupService;
import com.gy.server.game.smallGroup.bean.PlayerSmallGroupIdInfo;
import com.gy.server.game.smallGroup.bean.SmallGroupTypeEnum;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherCreate;
import com.gy.server.game.smallGroup.template.SmallGroupConst;
import com.gy.server.game.team.TeamManager;
import com.gy.server.game.team.TeamService;
import com.gy.server.game.team.enums.TeamStageEnums;
import com.gy.server.game.team.template.TeamTemplate;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.time.DateTimeUtil;
import com.gy.server.world.team.base.TeamInfo;
import com.gy.server.world.team.base.TeamMemberInfo;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.rpc.TLMessageCallbackTask;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;

/**
 * 金兰创建异步处理
 *
 * <AUTHOR> 2024/12/18 15:48
 **/
public class SmallGroupBrotherStartAsync extends AsyncCall {

    private Player player;
    private long time;

    private int masterWorldId;

    private int text = Text.没有异常;

    public SmallGroupBrotherStartAsync(Player player, long time) {
        this.player = player;
        this.time = time;
    }

    @Override
    public void execute() {
        masterWorldId = CommonUtils.getWorldMasterServerId();
        logic:{
            Pair<Long, Integer> teamIdByPlayerId = TeamManager.getInstance().getTeamIdByPlayerId(player.getPlayerId());
            if (teamIdByPlayerId == null) {
                text = Text.组队你不在队伍;
                break logic;
            }

            TeamTemplate teamTemplate = TeamService.getTeamConfigs().get(teamIdByPlayerId.getRight());
            if (Objects.isNull(teamTemplate)) {
                text = Text.对应的模板数据找不到;
                break logic;
            }

            if (teamTemplate.stageEnums != TeamStageEnums.SmallGroupBrother) {
                text = Text.组队类型不对;
                break logic;
            }

            SmallGroupGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroup);
            if(globalData.getCreateSignInfo(player.getPlayerId(), SmallGroupTypeEnum.brother) != null){
                text = Text.当前有正在创建的金兰;
                break logic;
            }

            if (teamTemplate.crossType == 0) {
                TeamInfo teamInfo = TeamManager.getInstance().getTeamInfo(teamIdByPlayerId.getLeft());
                if (teamInfo == null) {
                    text = Text.组队队伍不存在;
                    break logic;
                }

                List<TeamMemberInfo> memberInfoList = new ArrayList<>();
                for (Long memberId : TeamManager.getInstance().getMembers(teamInfo.getTeamId())) {
                    TeamMemberInfo memberInfo = TeamManager.getInstance().getMemberInfo(memberId);
                    if (memberInfo != null) {
                        memberInfoList.add(memberInfo);
                    }
                }

                long leaderId = teamInfo.getLeaderId();

                smallGroupBrotherStart(player, leaderId, memberInfoList);
            } else {
                if(masterWorldId == 0){
                    text = Text.组队跨服未分配;
                    break logic;
                }

                ServerCommandRequest request = CommandRequests.newServerCommandRequest("WorldTeamCommandService.getTeamRelateInfo");
                TLBase.getInstance().getRpcUtil().sendToNodeWithCallBack(new ExecuteCallbackTask(), ServerType.WORLD, masterWorldId, request, teamIdByPlayerId.getLeft());
            }
            return;
        }

        PbProtocol.SmallGroupBrotherStartRst.Builder rst = PbProtocol.SmallGroupBrotherStartRst.newBuilder().setResult(Text.genServerRstInfo(text));
        player.send(PtCode.SMALL_GROUP_BROTHER_START_RST, rst.build(), time);
    }

    @Override
    public void asyncExecute() {
    }

    private void smallGroupBrotherStart(Player player, long leaderId, List<TeamMemberInfo> memberInfoList){
        if(leaderId == 0l || memberInfoList.isEmpty()){
            text = Text.组队队伍不存在;
            return;
        }

        //检查是否有金兰
        for (TeamMemberInfo memberInfo : memberInfoList) {
            PlayerSmallGroupIdInfo playerSmallGroupIdInfo = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, GsRedisKey.SmallGroup.player_small_group_id_info.getRedisKey(memberInfo.getPlayerId()));
            if (playerSmallGroupIdInfo != null && playerSmallGroupIdInfo.getBrotherId() != 0) {
                text = Text.成员已有金兰;
                return;
            }
        }

        if (player.getPlayerId() != leaderId) {
            text = Text.组队没有权限;
            return;
        }

        //检查人数
        int num = memberInfoList.size();
        SmallGroupConst smallGroupConst = SmallGroupService.getSmallGroupConst();
        if (num < smallGroupConst.swornBrothersMixNum) {
            text = Text.金兰创建人数不足;
            return;
        }

        if (num >= smallGroupConst.swornBrothersMaxNum) {
            text = Text.金兰创建人数超出;
            return;
        }

        //检查队长对所有人好感度
        FriendPersonInfo personInfo = FriendHelper.getFriendPersonInfo(player.getPlayerId());
        for (TeamMemberInfo memberInfo : memberInfoList) {
            //不检查自己
            if (memberInfo.getPlayerId() == player.getPlayerId()) {
                continue;
            }

            if (personInfo.getLikabilityMap().getOrDefault(memberInfo.getPlayerId(), 0) < smallGroupConst.swornBrothersLikabilityMixNum) {
                text = Text.与成员好感度不足;
                return;
            }
        }

        //检查消耗
        List<Reward> cost = Reward.templateCollectionToReward(SmallGroupService.getSmallGroupConst().swornBrothersConsume);
        if (Reward.check(player, cost) != -1) {
            text = Text.消耗不足;
            return;
        }
        Reward.remove(cost, player, BehaviorType.smallGroupBrother);

        //TODO:发往世界服处理

        //创建结义信息
        SmallGroupBrotherCreate brotherCreate = new SmallGroupBrotherCreate(SmallGroupService.getSmallGroupConst().swornBrothersTask);

        //暂时不进行创建任务,后续有特殊处理
        brotherCreate.setCreateTaskId(-1);
        brotherCreate.addServerMember(memberInfoList, leaderId);

        //放入redis
        String redisKey = GsRedisKey.SmallGroup.small_group_create.getRedisKey(brotherCreate.getId());
        TLBase.getInstance().getRedisAssistant().setBean(redisKey, brotherCreate);
        TLBase.getInstance().getRedisAssistant().pexpire(redisKey, DateTimeUtil.MillisOfDay * 3);

        SmallGroupHelper.smallGroupCreateInfoNotify(brotherCreate, true);
    }

    private class ExecuteCallbackTask extends TLMessageCallbackTask {
        @Override
        public void complete(CallbackResponse callbackResponse) {
            long leaderId = callbackResponse.getParam(0);
            List<TeamMemberInfo> memberInfoList = callbackResponse.getParam(1);

            smallGroupBrotherStart(player, leaderId, memberInfoList);
            PbProtocol.SmallGroupBrotherStartRst.Builder rst = PbProtocol.SmallGroupBrotherStartRst.newBuilder().setResult(Text.genServerRstInfo(text));
            player.send(PtCode.SMALL_GROUP_BROTHER_START_RST, rst.build(), time);
        }

        @Override
        public void timeout() {
            text = Text.金兰请求超时;
        }
    }
}

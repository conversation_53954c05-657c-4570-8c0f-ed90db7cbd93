package com.gy.server.game.smallGroup.async.brother;

import com.gy.server.common.distributedlock.DistributedLockUtilManager;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.SmallGroupGlobalData;
import com.gy.server.game.smallGroup.async.SmallGroupInfoSyncAsync;
import com.gy.server.game.smallGroup.bean.PlayerSmallGroupIdInfo;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherInfo;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.concurrent.ConcurrentHashMap;

/**
 * 小团体-金兰离开异步处理
 *
 * <AUTHOR> 2024/12/23 17:49
 **/
public class SmallGroupBrotherLeaveAsync extends AsyncCall {

    private Player player;
    private boolean leave;
    private long time;

    private int text = Text.没有异常;

    public SmallGroupBrotherLeaveAsync(Player player, PbProtocol.SmallGroupBrotherLeaveReq req, long time) {
        this.player = player;
        this.leave = req.getLeave();
        this.time = time;
    }

    @Override
    public void execute() {
        PbProtocol.SmallGroupBrotherLeaveRst.Builder rst = PbProtocol.SmallGroupBrotherLeaveRst.newBuilder().setResult(Text.genServerRstInfo(text));
        player.send(PtCode.SMALL_GROUP_BROTHER_LEAVE_RST, rst.build(), time);
    }

    @Override
    public void asyncExecute() {
        String redisKey = GsRedisKey.SmallGroup.player_small_group_id_info.getRedisKey(player.getPlayerId());
        PlayerSmallGroupIdInfo playerSmallGroupIdInfo = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, redisKey);
        if(playerSmallGroupIdInfo == null){
            text = Text.金兰不存在;
            return;
        }

        if(playerSmallGroupIdInfo.getBrotherId() == 0){
            text = Text.金兰不存在;
            return;
        }

        String redisKeyTemp = GsRedisKey.SmallGroup.small_group_info.getRedisKey(playerSmallGroupIdInfo.getBrotherId());
        DistributedLockUtilManager.operate(() -> {
            SmallGroupBrotherInfo brotherInfo = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupBrotherInfo.class, redisKeyTemp);
            if(brotherInfo == null){
                text = Text.金兰不存在;
                return;
            }

            if(leave && brotherInfo.getLeaveMap().containsKey(player.getPlayerId())){
                text = Text.正在退出金兰冷静期;
                return;
            }

            if(leave){
                brotherInfo.getLeaveMap().put(player.getPlayerId(), ServerConstants.getCurrentTimeMillis() + DateTimeUtil.MillisOfHour * 12);
            }else {
                brotherInfo.getLeaveMap().remove(player.getPlayerId());
            }
            TLBase.getInstance().getRedisAssistant().setBean(redisKeyTemp, brotherInfo);

            SmallGroupGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroup);
            globalData.getLeaveMap().computeIfAbsent(player.getPlayerId(), map -> new ConcurrentHashMap<>()).put(brotherInfo.getId(), brotherInfo.getType().getType());

            ThreadPool.execute(new SmallGroupInfoSyncAsync(brotherInfo));
        }, redisKeyTemp);

    }
}

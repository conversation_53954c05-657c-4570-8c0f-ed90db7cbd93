package com.gy.server.game.smallGroup.template;

import com.gy.server.game.smallGroup.bean.makeAcquaintances.SmallGroupMakeAcquaintancesEnum;
import com.gy.server.game.util.StringExtUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * 结识标签选项模版
 *
 * <AUTHOR> 2024/12/23 16:04
 **/
public class SmallGroupPlatformTemplate {

    public int id;

    /**
     * 解释类型
     * 1、结拜-个人发布
     * 2、结拜-金兰发布
     * 3、结婚
     * 4、师徒找徒弟
     * 5、徒弟找师傅
     */
    public SmallGroupMakeAcquaintancesEnum type;

    /**
     * 标签分类要求
     * key:标签类id  value:条数
     */
    public Map<Integer, Integer> labelMap = new HashMap<>();

    /**
     * 是否有人数要求
     */
    public boolean isNum;

    /**
     * 是否有性别要求
     */
    public boolean isGender;

    public SmallGroupPlatformTemplate(Map<String, String> map) {
        this.id = Integer.parseInt(map.get("id"));
        this.type = SmallGroupMakeAcquaintancesEnum.getSmallGroupMakeAcquaintancesEnum(Integer.parseInt(map.get("type")));
        this.labelMap = StringExtUtil.string2Map(map.get("label"), ",", "|", Integer.class, Integer.class);
        this.isNum = map.get("isNum").equals("1");
        this.isGender = map.get("isGender").equals("1");
    }
}

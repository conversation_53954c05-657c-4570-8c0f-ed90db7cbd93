package com.gy.server.game.smallGroup.async.brother.resolution;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.smallGroup.async.SmallGroupInfoSyncAsync;
import com.gy.server.game.smallGroup.bean.PlayerSmallGroupIdInfo;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherInfo;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherResolutionAdd;
import com.gy.server.game.smallGroup.template.SmallGroupBrotherResolutionEnum;
import com.gy.server.game.text.Text;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

/**
 * 金兰纳新邀请目标回应异步处理
 *
 * <AUTHOR> 2024/12/27 15:18
 **/
public class SmallGroupBrotherAddInviteSelectDealAsync extends AsyncCall {

    private long playerId;
    private String playerName;
    private long targetPlayerId;
    private String targetName;
    private boolean select;

    public SmallGroupBrotherAddInviteSelectDealAsync(long playerId, String playerName, long targetPlayerId, String targetName, boolean select) {
        this.playerId = playerId;
        this.playerName = playerName;
        this.targetPlayerId = targetPlayerId;
        this.targetName = targetName;
        this.select = select;
    }

    @Override
    public void execute() {

    }

    @Override
    public void asyncExecute() {
        if(select){
            String idInfoRedisKey = GsRedisKey.SmallGroup.player_small_group_id_info.getRedisKey(playerId);
            PlayerSmallGroupIdInfo idInfo = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, idInfoRedisKey);
            if(idInfo != null && idInfo.getBrotherId() != 0){
                String redisKey = GsRedisKey.SmallGroup.small_group_info.getRedisKey(idInfo.getBrotherId());
                SmallGroupBrotherInfo brotherInfo = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupBrotherInfo.class, redisKey);
                if(brotherInfo != null){
                    if(brotherInfo.canAddResolution(SmallGroupBrotherResolutionEnum.add, playerId) == Text.没有异常){
                        SmallGroupBrotherResolutionAdd resolution = new SmallGroupBrotherResolutionAdd(playerId, playerName, targetPlayerId, targetName);
                        brotherInfo.addResolution(resolution);
                        TLBase.getInstance().getRedisAssistant().setBean(redisKey, brotherInfo);

                        //同步
                        ThreadPool.execute(new SmallGroupInfoSyncAsync(brotherInfo));
                    }
                }
            }
        }
    }
}

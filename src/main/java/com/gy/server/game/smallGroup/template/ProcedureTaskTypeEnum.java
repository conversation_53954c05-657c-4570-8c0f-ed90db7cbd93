package com.gy.server.game.smallGroup.template;

/**
 * 创建任务类型
 *
 * <AUTHOR> 2024/12/16 9:44
 **/
public enum ProcedureTaskTypeEnum {

    /**
     * 战斗
     */
    stage(1),

    /**
     * 送东西
     */
    sendItem(2),
    ;

    private int type;

    ProcedureTaskTypeEnum(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }

    public static ProcedureTaskTypeEnum getProcedureTaskTypeEnum(String type){
        return getProcedureTaskTypeEnum(Integer.parseInt(type));
    }

    public static ProcedureTaskTypeEnum getProcedureTaskTypeEnum(int type){
        for (ProcedureTaskTypeEnum bean : ProcedureTaskTypeEnum.values()) {
            if(bean.type == type){
                return bean;
            }
        }
        return null;
    }
}

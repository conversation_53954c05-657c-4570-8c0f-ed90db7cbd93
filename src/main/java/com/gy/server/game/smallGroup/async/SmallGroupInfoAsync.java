package com.gy.server.game.smallGroup.async;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.SmallGroupHelper;
import com.gy.server.game.smallGroup.bean.PlayerSmallGroupIdInfo;
import com.gy.server.game.smallGroup.bean.SmallGroupTypeEnum;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherInfo;
import com.gy.server.game.smallGroup.bean.marry.SmallGroupMarryInfo;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbSmallGroup;
import com.ttlike.server.tl.baselib.TLBase;

import java.util.*;

/**
 * 小团体信息异步处理
 *
 * <AUTHOR> 2024/12/17 16:37
 **/
public class SmallGroupInfoAsync extends AsyncCall {

    private Player player;
    private long time;

    private int type;
    private List<PbSmallGroup.SmallGroupInfo> smallGroupInfoList = new ArrayList<>();

    public SmallGroupInfoAsync(Player player, PbProtocol.SmallGroupInfoReq req, long time) {
        this.player = player;
        this.type = req.getType();
        this.time = time;
    }

    @Override
    public void execute() {
        PbProtocol.SmallGroupInfoRst.Builder rst = PbProtocol.SmallGroupInfoRst.newBuilder().setResult(Text.genOkServerRstInfo());
        rst.addAllGroupInfo(smallGroupInfoList);

        player.send(PtCode.SMALL_GROUP_INFO_RST, rst.build(), time);
    }

    @Override
    public void asyncExecute() {

        String redisKey = GsRedisKey.SmallGroup.player_small_group_id_info.getRedisKey(player.getPlayerId());
        PlayerSmallGroupIdInfo playerSmallGroupIdInfo = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, redisKey);
        if(playerSmallGroupIdInfo == null){
            return;
        }

        Map<SmallGroupTypeEnum, Set<Long>> map = playerSmallGroupIdInfo.getSmallGroupIdMap(type);
        for (Map.Entry<SmallGroupTypeEnum, Set<Long>> entry : map.entrySet()) {
            switch (entry.getKey()){
                case brother:{
                    for (long id : entry.getValue()) {
                        String redisKeyTemp = GsRedisKey.SmallGroup.small_group_info.getRedisKey(id);
                        SmallGroupBrotherInfo groupInfo = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupBrotherInfo.class, redisKeyTemp);
                        if(groupInfo == null){
                            continue;
                        }

                        smallGroupInfoList.add(SmallGroupHelper.genSmallGroupInfo(groupInfo));
                    }
                    break;
                }
                case marry:{
                    for (long id : entry.getValue()) {
                        String redisKeyTemp = GsRedisKey.SmallGroup.small_group_info.getRedisKey(id);
                        SmallGroupMarryInfo groupInfo = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupMarryInfo.class, redisKeyTemp);
                        if(groupInfo == null){
                            continue;
                        }

                        smallGroupInfoList.add(SmallGroupHelper.genSmallGroupInfo(groupInfo));
                    }
                    break;
                }
            }
        }
    }

}

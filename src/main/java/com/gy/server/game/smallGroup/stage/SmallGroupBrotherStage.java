//package com.gy.server.game.smallGroup.stage;
//
//import com.gy.server.core.Configuration;
//import com.gy.server.game.combat.AbstractStage;
//import com.gy.server.game.friend.async.FriendTeamBattleLikabilityDealAsync;
//import com.gy.server.game.lineup.LineupType;
//import com.gy.server.game.packet.PtCode;
//import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherCreate;
//import com.gy.server.game.team.TeamHelper;
//import com.gy.server.packet.PbProtocol;
//import com.gy.server.world.team.base.TeamMemberInfo;
//import com.ttlike.server.tl.baselib.thread.ThreadPool;
//
//import java.util.HashMap;
//import java.util.Map;
//
///**
// * 金兰创建战斗任务
// *
// * <AUTHOR> 2024/12/17 9:54
// **/
//public class SmallGroupBrotherStage extends AbstractStage {
//
//    private long teamId;
//    private LineupType lineupType;
//
//    private Map<Long, TeamMemberInfo> memberInfoMap = new HashMap();
//
//    private SmallGroupBrotherCreate brotherCreate;
//
//    public SmallGroupBrotherStage(long teamId){
////        this.teamId = teamId;
////
////        String redisKey = GsRedisKey.SmallGroup.small_group_create.getRedisKey(teamId);
////        this.brotherCreate = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupBrotherCreate.class, redisKey);
////        int createTaskId = brotherCreate.getCreateTaskId();
////
////        SmallGroupProcedureTaskTemplate procedureTaskTemplate = SmallGroupService.getProcedureTaskTemplateMap().get(createTaskId);
////        this.template = procedureTaskTemplate;
////
////        Set<Long> memberIdSet = TeamManager.getInstance().getMembers(this.teamId);
////        for (Long memberId : memberIdSet) {
////            TeamMemberInfo memberInfo = TeamManager.getInstance().getMemberInfo(memberId);
////            this.memberInfoMap.put(memberInfo.getPlayerId(), memberInfo);
////        }
////        this.lineupType = LineupType.getType(template.stageParam.getLeft().get(memberIdSet.size() - 1));
//    }
//
//
//    @Override
//    public void init() {
////        // 获取队伍平均等级
////        int totalLevel = 0;
////        for (TeamMemberInfo memberInfo : memberInfoMap.values()) {
////            totalLevel += memberInfo.getLevel();
////        }
////        // 根据平均等级计算属性
////        int avgLevel = totalLevel / memberInfoMap.size();
////
////        TeamLineupSaveImpl teamLineupSave = (TeamLineupSaveImpl) lineupType.getSaveType().getLineupSave();
////        List<LineupInfoBean> lineupInfoBeans = teamLineupSave.getLineupByTeamId(teamId, lineupType);
////
////        List<HeroUnit> atkUnits = new ArrayList<>();
////        if (Objects.nonNull(lineupInfoBeans)) {
////            atkUnits = lineupInfoBeans.get(0).createHeroUnitsByMirror();
////        }
////        TeamUnit atkTeam = new TeamUnit(getNewId(), atkUnits);
////
////        int battleCollectId = template.stageParam.getRight();
////        List<HeroUnit> defUnitList = BattleCollectService.createHeroUnits(battleCollectId, avgLevel);
////        TeamUnit defTeam = new TeamUnit(getNewId(), defUnitList);
////        init(battleCollectId, lineupType.getStageType(), atkTeam, defTeam, lineupType);
//    }
//
//    @Override
//    public void afterFinish() {
////        brotherCreate.setCreateTaskId(template.backTask);
////        //发送结果
////        combatSettlementNotify();
//    }
//
//    private void combatSettlementNotify() {
//        int ptCode = PtCode.COMBAT_SETTLEMENT_NOTIFY;
//        PbProtocol.CombatSettlementNotify.Builder rst = genCombatSettlement();
//        if (Configuration.isWorldServer()) {
//            TeamHelper.batchWorldSendMsg(memberInfoMap.keySet(), ptCode, rst.build());
//        } else {
//            for (TeamMemberInfo bean : memberInfoMap.values()) {
//                if(Configuration.serverId == bean.getServerId()){
//                    TeamHelper.gsSendMsg(bean.getPlayerId(), ptCode, rst.build());
//                }else {
//                    TeamHelper.worldSendMsg(bean.getPlayerId(), ptCode, rst.build());
//                }
//            }
//        }
//
//        //组队战斗好感度处理
//        ThreadPool.execute(new FriendTeamBattleLikabilityDealAsync(memberInfoMap.keySet()));
//    }
//}

package com.gy.server.game.smallGroup.bean.makeAcquaintances;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherName;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherInfo;
import com.gy.server.packet.PbSmallGroup;
import com.ttlike.server.tl.baselib.serialize.base.RedisListIntegerBean;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 小团体结识信息
 *
 * <AUTHOR> 2025/1/2 15:54
 **/
@ProtobufClass
public class SmallGroupMakeAcquaintancesInfo {

    /**
     * platform 表id
     */
    @Protobuf(order = 1)
    private int id;

    /**
     * 发送人
     */
    @Protobuf(order = 2)
    private long senderId;

    /**
     * 发布金兰id
     */
    @Protobuf(order = 3)
    private long brotherId;

    /**
     * 发布金兰名字
     */
    @Protobuf(order = 4)
    private SmallGroupBrotherName brotherName;

    /**
     * 标签信息map
     */
    @Protobuf(order = 5)
    private Map<Integer, RedisListIntegerBean> labelMap = new HashMap<>();

    /**
     * 宣言
     */
    @Protobuf(order = 6)
    private String context;

    /**
     * 目标人数
     *
     */
    @Protobuf(order = 7)
    private int needNum;

    /**
     * 性别要求
     */
    @Protobuf(order = 8)
    private int gender;

    @Protobuf(order = 9)
    private long createTime = ServerConstants.getCurrentTimeMillis();

    /**
     * 申请人id集合
     */
    @Protobuf(order = 10)
    private Set<Long> applyIdSet = new HashSet<>();

    public SmallGroupMakeAcquaintancesInfo() {
    }

    public SmallGroupMakeAcquaintancesInfo(int id, Player player, SmallGroupBrotherInfo brotherInfo, Map<Integer, Set<Integer>> labelMap, String context, int needNum, int gender) {
        this.id = id;
        this.senderId = player.getPlayerId();
        if(brotherInfo != null){
            this.brotherId = brotherInfo.getId();
            this.brotherName = brotherInfo.getSmallGroupName();
        }

        labelMap.forEach((key, value) -> {
            RedisListIntegerBean bean = new RedisListIntegerBean();
            bean.getList().addAll(value);
            this.labelMap.put(key, bean);
        });
        this.context = context;
        this.needNum = needNum;
        this.gender = gender;
    }

    /**
     * 异步使用
     * @param playerId
     * @return
     */
    public PbSmallGroup.MakeAcquaintancesInfo genMakeAcquaintancesInfo(long playerId){
        PbSmallGroup.MakeAcquaintancesInfo.Builder builder = PbSmallGroup.MakeAcquaintancesInfo.newBuilder();
        builder.setId(id);
        MiniGamePlayer miniGamePlayer = PlayerHelper.getMiniPlayer(senderId);
        builder.setMiniUserSender(PlayerHelper.genMinMiniUser(miniGamePlayer));
        builder.setBrotherId(brotherId);
        if(brotherName != null){
            builder.setBrotherName(brotherName.genPb());
        }
        for (Map.Entry<Integer, RedisListIntegerBean> entry : labelMap.entrySet()) {
            PbSmallGroup.MakeAcquaintancesLabelInfo.Builder labelInfo = PbSmallGroup.MakeAcquaintancesLabelInfo.newBuilder();
            labelInfo.setLabelClassId(entry.getKey());
            labelInfo.addAllLabelId(entry.getValue().getList());
            builder.addLabelInfo(labelInfo.build());
        }
        builder.setNeedNum(needNum);
        builder.setGender(gender);
        builder.setCreateTime(createTime);
        builder.setApply(applyIdSet.contains(playerId));
        builder.setContext(context);
        return builder.build();
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public long getSenderId() {
        return senderId;
    }

    public void setSenderId(long senderId) {
        this.senderId = senderId;
    }

    public Map<Integer, RedisListIntegerBean> getLabelMap() {
        return labelMap;
    }

    public void setLabelMap(Map<Integer, RedisListIntegerBean> labelMap) {
        this.labelMap = labelMap;
    }

    public int getNeedNum() {
        return needNum;
    }

    public void setNeedNum(int needNum) {
        this.needNum = needNum;
    }

    public int getGender() {
        return gender;
    }

    public void setGender(int gender) {
        this.gender = gender;
    }

    public long getBrotherId() {
        return brotherId;
    }

    public void setBrotherId(long brotherId) {
        this.brotherId = brotherId;
    }

    public SmallGroupBrotherName getBrotherName() {
        return brotherName;
    }

    public void setBrotherName(SmallGroupBrotherName brotherName) {
        this.brotherName = brotherName;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public Set<Long> getApplyIdSet() {
        return applyIdSet;
    }

    public void setApplyIdSet(Set<Long> applyIdSet) {
        this.applyIdSet = applyIdSet;
    }

    public String getContext() {
        return context;
    }

    public void setContext(String context) {
        this.context = context;
    }
}

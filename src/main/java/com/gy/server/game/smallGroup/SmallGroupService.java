package com.gy.server.game.smallGroup;

import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.common.ConstantConfigReader;
import com.gy.server.game.function.Function;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.keyword.KeyWordService;
import com.gy.server.game.newFriend.FriendHelper;
import com.gy.server.game.newFriend.bean.FriendPersonInfo;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.service.Service;
import com.gy.server.game.smallGroup.async.SmallGroupInfoAsync;
import com.gy.server.game.smallGroup.async.brother.*;
import com.gy.server.game.smallGroup.async.brother.create.*;
import com.gy.server.game.smallGroup.async.brother.resolution.*;
import com.gy.server.game.smallGroup.async.makeAcquaintances.*;
import com.gy.server.game.smallGroup.async.marry.*;
import com.gy.server.game.smallGroup.bean.SmallGroupLogTypeEnum;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherName;
import com.gy.server.game.smallGroup.template.*;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbSmallGroup;
import com.gy.server.world.activityTeam.ActivityTeamHelper;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.gy.server.game.activity.team.ActivityTeamService.getLocalWarZoneId;

/**
 * 小团体服务类
 *
 * <AUTHOR> 2024/12/11 10:24
 **/
public class SmallGroupService extends PlayerPacketHandler implements Service {

    private static SmallGroupConst smallGroupConst;

    /*********************************************金兰***************************************************/

    /**
     * 金兰等级模板
     * key:经验   value:
     */
    private static Map<Integer, SmallGroupBrothersLevelTemplate> brothersLevelTemplateMap = new HashMap<>();

    /**
     * 金兰技能模板
     */
    private static Map<Integer, SmallGroupBrothersSkillTemplate> brothersSkillTemplateMap = new HashMap<>();

    /***********************************************结识***********************************************************/

    /**
     * 结识标签选项模版
     */
    private static Map<Integer, SmallGroupPlatformTemplate> platformTemplateMap = new HashMap<>();

    /**
     * 结识标签类模板
     */
    private static Map<Integer, SmallGroupLabelClassTemplate> labelClassMap = new HashMap<>();

    /************************************************情缘************************************************************/

    /**
     * 提亲道具map
     */
    private static Map<Integer, SmallGroupMarryProposeConsumeTemplate> marryProposeConsumeTemplateMap = new HashMap<>();

    /**
     * 巡游时间段模板map
     * key:id
     */
    private static Map<Integer, SmallGroupMarryCruiseReservationTemplate> marryCruiseReservationTemplateMap = new HashMap<>();

    /**
     * 巡游模板map
     * key:id
     */
    private static Map<Integer, SmallGroupMarryCruiseClassTemplate> marryCruiseClassTemplateMap = new HashMap<>();

    /**
     * 烟花模板map
     * key:id
     */
    private static Map<Integer, SmallGroupMarryFireworksClassTemplate> marryFireworksClassTemplateMap = new HashMap<>();

    /**
     * 菜品模板map
     * key:id
     */
    private static Map<Integer, SmallGroupMarryBanquetTemplate> marryBanquetTemplateMap = new HashMap<>();

    /**
     * 情缘等级map
     * key:id 等级
     */
    private static Map<Integer, SmallGroupMarryLevelTemplate> marryLevelTemplateMap = new HashMap<>();

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.relationship_swornBrothersLevel);
        Map<Integer, SmallGroupBrothersLevelTemplate> brothersLevelTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            SmallGroupBrothersLevelTemplate template = new SmallGroupBrothersLevelTemplate(map);
            brothersLevelTemplateMapTemp.put(template.exp, template);
        }
        brothersLevelTemplateMap = brothersLevelTemplateMapTemp;

        mapList = ConfigReader.read(ConfigFile.relationship_swornBrothersSkill);
        Map<Integer, SmallGroupBrothersSkillTemplate> brothersSkillTemplateMapTemp = new HashMap<>();
        for(Map<String, String> map : mapList){
            SmallGroupBrothersSkillTemplate template = new SmallGroupBrothersSkillTemplate(map);
            brothersSkillTemplateMapTemp.put(template.id, template);
        }
        brothersSkillTemplateMap = brothersSkillTemplateMapTemp;

        mapList = ConfigReader.read(ConfigFile.relationship_log);
        for (Map<String, String> map : mapList) {
            SmallGroupLogTypeEnum logType = SmallGroupLogTypeEnum.valueOf(map.get("serverType"));
            logType.load(map);
        }

        mapList = ConfigReader.read(ConfigFile.relationship_platform);
        Map<Integer, SmallGroupPlatformTemplate> platformTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            SmallGroupPlatformTemplate template = new SmallGroupPlatformTemplate(map);
            platformTemplateMapTemp.put(template.id, template);
        }
        platformTemplateMap = platformTemplateMapTemp;

        mapList = ConfigReader.read(ConfigFile.relationship_labelClass);
        Map<Integer, SmallGroupLabelClassTemplate> labelClassMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            SmallGroupLabelClassTemplate template = new SmallGroupLabelClassTemplate(map);
            labelClassMapTemp.put(template.id, template);
        }
        labelClassMap = labelClassMapTemp;

        mapList = ConfigReader.read(ConfigFile.relationship_marryProposeConsume);
        Map<Integer, SmallGroupMarryProposeConsumeTemplate> marryProposeConsumeTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            SmallGroupMarryProposeConsumeTemplate template = new SmallGroupMarryProposeConsumeTemplate(map);
            marryProposeConsumeTemplateMapTemp.put(template.id, template);
        }
        marryProposeConsumeTemplateMap = marryProposeConsumeTemplateMapTemp;

        mapList = ConfigReader.read(ConfigFile.relationship_marryCruiseReservation);
        Map<Integer, SmallGroupMarryCruiseReservationTemplate> marryCruiseReservationTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            SmallGroupMarryCruiseReservationTemplate template = new SmallGroupMarryCruiseReservationTemplate(map);
            marryCruiseReservationTemplateMapTemp.put(template.id, template);
        }
        marryCruiseReservationTemplateMap = marryCruiseReservationTemplateMapTemp;

        mapList = ConfigReader.read(ConfigFile.relationship_marryCruiseClass);
        Map<Integer, SmallGroupMarryCruiseClassTemplate> marryCruiseClassTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            SmallGroupMarryCruiseClassTemplate template = new SmallGroupMarryCruiseClassTemplate(map);
            marryCruiseClassTemplateMapTemp.put(template.id, template);
        }
        marryCruiseClassTemplateMap = marryCruiseClassTemplateMapTemp;

        mapList = ConfigReader.read(ConfigFile.relationship_marryFireworksClass);
        Map<Integer, SmallGroupMarryFireworksClassTemplate> marryFireworksClassTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            SmallGroupMarryFireworksClassTemplate template = new SmallGroupMarryFireworksClassTemplate(map);
            marryFireworksClassTemplateMapTemp.put(template.id, template);
        }
        marryFireworksClassTemplateMap = marryFireworksClassTemplateMapTemp;

        mapList = ConfigReader.read(ConfigFile.relationship_marryBanquet);
        Map<Integer, SmallGroupMarryBanquetTemplate> marryBanquetTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            SmallGroupMarryBanquetTemplate template = new SmallGroupMarryBanquetTemplate(map);
            marryBanquetTemplateMapTemp.put(template.id, template);
        }
        marryBanquetTemplateMap = marryBanquetTemplateMapTemp;

        mapList = ConfigReader.read(ConfigFile.relationship_marryLevel);
        Map<Integer, SmallGroupMarryLevelTemplate> marryLevelTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            SmallGroupMarryLevelTemplate template = new SmallGroupMarryLevelTemplate(map);
            marryLevelTemplateMapTemp.put(template.id, template);
        }
        marryLevelTemplateMap = marryLevelTemplateMapTemp;

        Map<String, String> map = ConstantConfigReader.read(ConfigFile.relationship_const);
        smallGroupConst = new SmallGroupConst(map);
    }

    @Override
    public void clearConfigData() {
        brothersLevelTemplateMap.clear();

        platformTemplateMap.clear();
        labelClassMap.clear();

        marryProposeConsumeTemplateMap.clear();
        marryCruiseReservationTemplateMap.clear();
        marryCruiseClassTemplateMap.clear();
        marryFireworksClassTemplateMap.clear();
        marryBanquetTemplateMap.clear();
        marryLevelTemplateMap.clear();

        smallGroupConst = null;
    }

    /************************************************** 金兰 *******************************************************/

    /**
     * 小团体信息
     */
    @Handler(PtCode.SMALL_GROUP_INFO_REQ)
    private void smallGroupInfoReq(Player player, PbProtocol.SmallGroupInfoReq req, long time){
        ThreadPool.execute(new SmallGroupInfoAsync(player, req, time));
    }

    /**
     * 小团体-金兰创建开始
     */
    @Handler(PtCode.SMALL_GROUP_BROTHER_START_REQ)
    private void smallGroupBrotherStartReq(Player player, PbProtocol.SmallGroupBrotherStartReq req, long time){
        ThreadPool.execute(new SmallGroupBrotherStartAsync(player, time));
    }

    /**
     * 小团体-金兰成员职位投票
     */
    @Handler(PtCode.SMALL_GROUP_BROTHER_MEMBER_JOB_VOTE_REQ)
    private void smallGroupMemberJobVoteReq(Player player, PbProtocol.SmallGroupBrotherMemberJobVoteReq req, long time){
        ThreadPool.execute(new SmallGroupBrotherMemberJobVoteAsync(player, req, time));
    }

    /**
     * 小团体-金兰创建金兰名号
     */
    @Handler(PtCode.SMALL_GROUP_BROTHER_CREATE_NAME_REQ)
    public void SmallGroupBrotherCreateNameReq(Player player, PbProtocol.SmallGroupBrotherCreateNameReq req, long time){
        ThreadPool.execute(new SmallGroupBrotherCreateNameAsync(player, req, time));
    }

    /**
     * 小团体金兰成员创建自己名号
     */
    @Handler(PtCode.SMALL_GROUP_BROTHER_MEMBER_CREATE_NAME_REQ)
    private void smallGroupBrotherMemberCreateNameReq(Player player, PbProtocol.SmallGroupBrotherMemberCreateNameReq req, long time){
        ThreadPool.execute(new SmallGroupBrotherMemberCreateNameAsync(player, req, time));
    }

    /**
     * 小团体金兰成员确定
     */
    @Handler(PtCode.SMALL_GROUP_BROTHER_SURE_REQ)
    private void smallGroupBrotherSureReq(Player player, PbProtocol.SmallGroupBrotherSureReq req, long time){
        ThreadPool.execute(new SmallGroupBrotherSureAsync(player, time));
    }

    /**
     * 小团体金兰成员离开
     */
    @Handler(PtCode.SMALL_GROUP_BROTHER_LEAVE_REQ)
    private void smallGroupBrotherLeaveReq(Player player, PbProtocol.SmallGroupBrotherLeaveReq req, long time){
        ThreadPool.execute(new SmallGroupBrotherLeaveAsync(player, req, time));
    }

    /**
     * 金兰修改公告
     */
    @Handler(PtCode.SMALL_GROUP_BROTHER_CHANGE_NOTICE_REQ)
    private void smallGroupBrotherChangeNoticeReq(Player player, PbProtocol.SmallGroupBrotherChangeNoticeReq req, long time){
        ThreadPool.execute(new SmallGroupBrotherChangeNoticeAsync(player, req, time));
    }

    /**
     * 小团体金兰纳新可用信息
     */
    @Handler(PtCode.SMALL_GROUP_BROTHER_CAN_ADD_INFO_REQ)
    private void smallGroupBrotherCanAddInfoReq(Player player, PbProtocol.SmallGroupBrotherCanAddInfoReq req, long time){
        FriendPersonInfo personInfo = FriendHelper.getFriendPersonInfo(player.getPlayerId());
        ThreadPool.execute(new SmallGroupBrotherCanAddInfoAsync(player, time, personInfo.getLikabilityMap()));
    }

    /**
     * 小团体金兰纳新
     */
    @Handler(PtCode.SMALL_GROUP_BROTHER_ADD_REQ)
    private void smallGroupBrotherAddReq(Player player, PbProtocol.SmallGroupBrotherAddReq req, long time){
        ThreadPool.execute(new SmallGroupBrotherAddAsync(player, req, time));
    }

    /**
     * 小团体金兰纳新目标选择
     */
    @Handler(PtCode.SMALL_GROUP_BROTHER_ADD_TARGET_SELECT_REQ)
    private void smallGroupBrotherAddTargetSelectReq(Player player, PbProtocol.SmallGroupBrotherAddTargetSelectReq req, long time){
        FriendPersonInfo personInfo = FriendHelper.getFriendPersonInfo(player.getPlayerId());
        ThreadPool.execute(new SmallGroupBrotherAddTargetSelectAsync(player, req, time, personInfo.getLikabilityMap()));
    }

    /**
     * 小团体金兰决议投票
     */
    @Handler(PtCode.SMALL_GROUP_BROTHER_RESOLUTION_VOTE_REQ)
    private void smallGroupBrotherResolutionVoteReq(Player player, PbProtocol.SmallGroupBrotherResolutionVoteReq req, long time){
        ThreadPool.execute(new SmallGroupBrotherResolutionVoteAsync(player, req, time));
    }

    /**
     * 小团体金兰可用合并信息
     */
    @Handler(PtCode.SMALL_GROUP_BROTHER_CAN_MERGE_INFO_REQ)
    private void smallGroupBrotherCanMergeInfoReq(Player player, PbProtocol.SmallGroupBrotherCanMergeInfoReq req, long time){
        FriendPersonInfo personInfo = FriendHelper.getFriendPersonInfo(player.getPlayerId());
        ThreadPool.execute(new SmallGroupBrotherCanMergeInfoAsync(player, time, personInfo.getLikabilityMap()));
    }

    /**
     * 小团体金兰合并
     */
    @Handler(PtCode.SMALL_GROUP_BROTHER_MERGE_REQ)
    private void smallGroupBrotherMergeReq(Player player, PbProtocol.SmallGroupBrotherMergeReq req, long time){
        ThreadPool.execute(new SmallGroupBrotherMergeAsync(player, req, time));
    }

    /**
     * 金兰改名
     */
    @Handler(PtCode.SMALL_GROUP_BROTHER_RENAME_REQ)
    private void smallGroupBrotherChangeNameReq(Player player, PbProtocol.SmallGroupBrotherRenameReq req, long time){
        ThreadPool.execute(new SmallGroupBrotherRenameAsync(player, req, time));
    }

    /**
     * 金兰踢人
     */
    @Handler(PtCode.SMALL_GROUP_BROTHER_KICK_REQ)
    private void smallGroupBrotherKickReq(Player player, PbProtocol.SmallGroupBrotherKickReq req, long time){
        ThreadPool.execute(new SmallGroupBrotherKickAsync(player, req, time));
    }

    /**
     * 金兰解散
     */
    @Handler(PtCode.SMALL_GROUP_BROTHER_DISBAND_REQ)
    private void smallGroupBrotherDisbandReq(Player player, PbProtocol.SmallGroupBrotherDisbandReq req, long time){
        ThreadPool.execute(new SmallGroupBrotherDisbandAsync(player, time));
    }

    /**
     * 结识列表
     */
    @Handler(PtCode.MAKE_ACQUAINTANCES_LIST_REQ)
    private void makeAcquaintancesListReq(Player player, PbProtocol.MakeAcquaintancesListReq req, long time){
        ThreadPool.execute(new MakeAcquaintancesListAsync(player, req, time));
    }

    /**
     * 发布结识
     */
    @Handler(PtCode.MAKE_ACQUAINTANCES_RELEASE_REQ)
    private void makeAcquaintancesReleaseReq(Player player, PbProtocol.MakeAcquaintancesReleaseReq req, long time){
        ThreadPool.execute(new MakeAcquaintancesReleaseAsync(player, req, time));
    }

    /**
     * 结识申请
     */
    @Handler(PtCode.MAKE_ACQUAINTANCES_APPLY_REQ)
    private void makeAcquaintancesReq(Player player, PbProtocol.MakeAcquaintancesApplyReq req, long time){
        ThreadPool.execute(new MakeAcquaintancesApplyAsync(player, req, time));
    }

    /**
     * 收到的结识申请列表
     */
    @Handler(PtCode.MAKE_ACQUAINTANCES_APPLY_LIST_REQ)
    private void makeAcquaintancesApplyListReq(Player player, PbProtocol.MakeAcquaintancesApplyListReq req, long time){
        ThreadPool.execute(new MakeAcquaintancesApplyListAsync(player, time));
    }

    /**
     * 我的申请列表
     */
    @Handler(PtCode.MAKE_ACQUAINTANCES_MY_APPLY_LIST_REQ)
    private void makeAcquaintancesMyApplyListReq(Player player, PbProtocol.MakeAcquaintancesMyApplyListReq req, long time){
        ThreadPool.execute(new MakeAcquaintancesMyApplyListAsync(player, time));
    }

    /**
     * 收到的结识申请处理
     */
    @Handler(PtCode.MAKE_ACQUAINTANCES_APPLY_DEAL_REQ)
    private void makeAcquaintancesApplyDealReq(Player player, PbProtocol.MakeAcquaintancesApplyDealReq req, long time){
        ThreadPool.execute(new MakeAcquaintancesApplyDealAsync(player, req, time));
    }

    /**
     * 金兰默认进队设置
     */
    @Handler(PtCode.SMALL_GROUP_BROTHER_DEFAULT_TEAM_ENTRY_REQ)
    private void smallGroupBrotherDefaultTeamEntryReq(Player player, PbProtocol.SmallGroupBrotherDefaultTeamEntryReq req, long time){
        ThreadPool.execute(new SmallGroupBrotherDefaultTeamEntryAsync(player, req, time));
    }

    /************************************************** 情缘 *******************************************************/

    /**
     * 获取情缘可邀请信息
     */
    @Handler(PtCode.SMALL_SMALL_MARRY_CAN_INVITE_INFO_REQ)
    private void smallGroupMarryCanInviteInfoReq(Player player, PbProtocol.SmallGroupMarryCanInviteInfoReq req, long time){
        FriendPersonInfo personInfo = FriendHelper.getFriendPersonInfo(player.getPlayerId());
        ThreadPool.execute(new SmallGroupMarryCanInviteInfoAsync(player, time, personInfo.getLikabilityMap()));
    }

    /**
     * 情缘邀请
     */
    @Handler(PtCode.SMALL_GROUP_MARRY_INVITE_REQ)
    private void smallGroupMarryInviteReq(Player player, PbProtocol.SmallGroupMarryInviteReq req, long time){
        ThreadPool.execute(new SmallGroupMarryInviteAsync(player, req, time));
    }

    private void responseInviteDeal(Player player, PbSmallGroup.InviteFunction function, int textId, long time){
        PbProtocol.SmallGroupMarryBeInviterDealRst.Builder rst = PbProtocol.SmallGroupMarryBeInviterDealRst.newBuilder()
                .setResult(Text.genServerRstInfo(textId))
                .setFunction(function);
        player.send(PtCode.SMALL_GROUP_MARRY_BE_INVITER_DEAL_RST, rst.build(), time);
    }

    /**
     * 情缘被邀请人处理
     */
    @Handler(PtCode.SMALL_GROUP_MARRY_BE_INVITER_DEAL_REQ)
    private void smallGroupMarryBeInviterDealReq(Player player, PbProtocol.SmallGroupMarryBeInviterDealReq req, long time){
        if(req.getFunction() == PbSmallGroup.InviteFunction.SmallGroup) {
            //原情缘处理逻辑
            ThreadPool.execute(new SmallGroupMarryBeInviterDealAsync(player, req, time));
        }else if(req.getFunction() == PbSmallGroup.InviteFunction.InfiniteRealm){
            //四人组队
            if(!Function.infiniteSecretRealm.isOpen(player)){
                responseInviteDeal(player, req.getFunction(), Text.功能未开启, time);
                return;
            }

            int activityId = ActivityTeamHelper.getOpenActivityID(player);
            if (activityId > 0) {
                ActivityTeamHelper.business(
                        errorCode -> {
                            responseInviteDeal(player, req.getFunction(), errorCode, time);
                        },
                        response -> {
                            int code = response.getParam(0);
                            responseInviteDeal(player, req.getFunction(), code, time);

                        },
                        () -> {
                            //本地逻辑
                            int code = ActivityTeamHelper.inviteAgree(req.getCreateId(), player.getPlayerId(), activityId, getLocalWarZoneId(),req.getSelect());
                            responseInviteDeal(player, req.getFunction(), code, time);
                        }
                        , player, activityId, "ActivityTeamWorldCommandService.inviteAgree", req.getCreateId(), req.getSelect());
            }
        }
    }

    /**
     * 情缘巡游申请信息
     */
    @Handler(PtCode.SMALL_GROUP_MARRY_CRUISE_APPLY_INFO_REQ)
    private void smallGroupMarryCruiseApplyInfoReq(Player player, PbProtocol.SmallGroupMarryCruiseApplyInfoReq req, long time){
        ThreadPool.execute(new SmallGroupMarryCruiseApplyInfoAsync(player, time));
    }

    /**
     * 情缘巡游申请
     */
    @Handler(PtCode.SMALL_GROUP_MARRY_CRUISE_APPLY_REQ)
    private void smallGroupMarryCruiseApplyForReq(Player player, PbProtocol.SmallGroupMarryCruiseApplyReq req, long time){
        ThreadPool.execute(new SmallGroupMarryCruiseApplyAsync(player, req, time));
    }

    /**
     * 情缘可邀请邀请宾客
     */
    @Handler(PtCode.SMALL_GROUP_MARRY_CAN_INVITE_GUESTS_REQ)
    private void smallGroupMarryCanInviteGuestsReq(Player player, PbProtocol.SmallGroupMarryCanInviteGuestsReq req, long time){
        PbSmallGroup.InviteFunction func = req.getFunction();
        ThreadPool.execute(new SmallGroupMarryCanInviteGuestsAsync(player, func, time));
    }

    /**
     * 情缘邀请宾客
     */
    @Handler(PtCode.SMALL_GROUP_MARRY_INVITE_GUESTS_REQ)
    private void smallGroupMarryInviteGuestsReq(Player player, PbProtocol.SmallGroupMarryInviteGuestsReq req, long time){
        ThreadPool.execute(new SmallGroupMarryInviteGuestsAsync(player, req, time));
    }

    /**
     * 情缘增加邀请数
     */
    @Handler(PtCode.SMALL_GROUP_MARRY_ADD_INVITE_NUM_REQ)
    private void smallGroupMarryAddInviteNumReq(Player player, PbProtocol.SmallGroupMarryAddInviteNumReq req, long time){
        ThreadPool.execute(new SmallGroupMarryAddInviteNumAsync(player, req, time));
    }

    /**
     * 放烟花
     */
    @Handler(PtCode.SMALL_GROUP_MARRY_LET_FIREWORK_REQ)
    private void smallGroupMarryLetFireworkReq(Player player, PbProtocol.SmallGroupMarryLetFireworkReq req, long time){
        ThreadPool.execute(new SmallGroupMarryLetFireworkAsync(player, req, time));
    }

    /**
     * 吃菜肴
     */
    @Handler(PtCode.SMALL_GROUP_MARRY_EAT_FEAST_REQ)
    private void smallGroupMarryEatFeastReq(Player player, PbProtocol.SmallGroupMarryEatFeastReq req, long time){
        ThreadPool.execute(new SmallGroupMarryEatFeastAsync(player, req, time));
    }

    /**
     * 情缘成员确定
     */
    @Handler(PtCode.SMALL_GROUP_MARRY_SURE_REQ)
    private void smallGroupMarrySureReq(Player player, PbProtocol.SmallGroupMarrySureReq req, long time){
        ThreadPool.execute(new SmallGroupMarrySureAsync(player, req, time));
    }

    /**
     * 情缘解散
     */
    @Handler(PtCode.SMALL_GROUP_MARRY_DISBAND_REQ)
    private void smallGroupMarryDisbandReq(Player player, PbProtocol.SmallGroupMarryDisbandReq req, long time){
        ThreadPool.execute(new SmallGroupMarryDisbandAsync(player, req, time));
    }

    /**
     * 情缘解散取消
     */
    @Handler(PtCode.SMALL_GROUP_MARRY_DISBAND_CANCEL_REQ)
    private void smallGroupMarryDisbandCancelReq(Player player, PbProtocol.SmallGroupMarryDisbandCancelReq req, long time){
        ThreadPool.execute(new SmallGroupMarryDisbandCancelAsync(player, time));
    }

    /**
     * 情缘解散开始
     */
    @Handler(PtCode.SMALL_GROUP_MARRY_DISBAND_START_REQ)
    private void smallGroupMarryDisbandStartReq(Player player, PbProtocol.SmallGroupMarryDisbandStartReq req, long time){
        ThreadPool.execute(new SmallGroupMarryDisbandStartAsync(player, time));
    }


    /************************************************** 师徒 *******************************************************/

//    /**
//     * 获取师徒可邀请信息
//     */
//    @Handler(PtCode.SMALL_GROUP_TS_CAN_INVITE_INFO_REQ)
//    private void smallGroupTSCanInviteInfoReq(Player player, PbProtocol.SmallGroupTSCanInviteInfoReq req, long time){
//        ThreadPool.execute(new SmallGroupTSCanInviteInfoAsync(player, time));
//    }
//
//    /**
//     * 师徒创建
//     */
//    @Handler(PtCode.SMALL_GROUP_TS_CREATE_REQ)
//    private void smallGroupTSCreateReq(Player player, PbProtocol.SmallGroupTSCreateReq req, long time){
//        ThreadPool.execute(new SmallGroupTSCreateAsync(player, time));
//    }

    /**
     * 金兰名称检查
     * @param brotherName
     * @return
     */
    public static int smallGroupBrotherNameCheck(SmallGroupBrotherName brotherName){
        if(!smallGroupConst.firstNameSyslang.contains(brotherName.getFirst())){
            return Text.参数异常;
        }

        //检查空字符
        if(brotherName.getSecond().isEmpty() || brotherName.getThird().isEmpty() || brotherName.getFourth().isEmpty()){
            return Text.金兰名称不能为空;
        }

        //检查字符长度 名称长度超出限制
        if(brotherName.getSecond().length() > smallGroupConst.nameNum || brotherName.getThird().length() > smallGroupConst.nameNum
                || brotherName.getFourth().length() > smallGroupConst.nameNum){
            return Text.金兰名称超出限制;
        }

        //检查字符合法
        if(KeyWordService.contains(brotherName.getSecond()) || KeyWordService.contains(brotherName.getThird()) || KeyWordService.contains(brotherName.getFourth())){
            return Text.名称不可用;
        }

        if(KeyWordService.contains(brotherName.getSecond() + brotherName.getThird())){
            return Text.名称不可用;
        }

        return Text.没有异常;
    }

    /**
     * 获取金兰等级
     * @param exp
     * @return
     */
    public static int getBrothersLevel(int exp){
        int level = 0;
        for (SmallGroupBrothersLevelTemplate bean : brothersLevelTemplateMap.values()) {
            if(exp >= bean.exp){
                level = Math.max(level, bean.id);
            }
        }
        return level;
    }

    /**
     * 获取金兰等级模板，需要判空
     * @param exp
     * @return
     */
    public static SmallGroupBrothersLevelTemplate getBrothersLevelTemplate(int exp) {
        return brothersLevelTemplateMap.get(getBrothersLevel(exp));
    }

    /**
     * 获取金兰当前最大人数
     * @param exp
     * @return
     */
    public static int getBrotherMaxNum(int exp){
        SmallGroupBrothersLevelTemplate template = getBrothersLevelTemplate(exp);
        return template == null ? smallGroupConst.swornBrothersMaxNum : template.maxNum;
    }

    public static Map<Integer, SmallGroupBrothersLevelTemplate> getBrothersLevelTemplateMap() {
        return brothersLevelTemplateMap;
    }

    public static Map<Integer, SmallGroupPlatformTemplate> getPlatformTemplateMap() {
        return platformTemplateMap;
    }

    public static Map<Integer, SmallGroupLabelClassTemplate> getLabelClassMap() {
        return labelClassMap;
    }

    public static SmallGroupConst getSmallGroupConst() {
        return smallGroupConst;
    }

    public static Map<Integer, SmallGroupBrothersSkillTemplate> getBrothersSkillTemplateMap() {
        return brothersSkillTemplateMap;
    }

    public static Map<Integer, SmallGroupMarryProposeConsumeTemplate> getMarryProposeConsumeTemplateMap() {
        return marryProposeConsumeTemplateMap;
    }

    public static Map<Integer, SmallGroupMarryCruiseReservationTemplate> getMarryCruiseReservationTemplateMap() {
        return marryCruiseReservationTemplateMap;
    }

    public static Map<Integer, SmallGroupMarryCruiseClassTemplate> getMarryCruiseClassTemplateMap() {
        return marryCruiseClassTemplateMap;
    }

    public static Map<Integer, SmallGroupMarryFireworksClassTemplate> getMarryFireworksClassTemplateMap() {
        return marryFireworksClassTemplateMap;
    }

    public static Map<Integer, SmallGroupMarryBanquetTemplate> getMarryBanquetTemplateMap() {
        return marryBanquetTemplateMap;
    }

    public static Map<Integer, SmallGroupMarryLevelTemplate> getMarryLevelTemplateMap() {
        return marryLevelTemplateMap;
    }

    @Override
    public boolean isGameServer() {
        return true;
    }

    @Override
    public boolean isWorldServer() {
        return true;
    }
}

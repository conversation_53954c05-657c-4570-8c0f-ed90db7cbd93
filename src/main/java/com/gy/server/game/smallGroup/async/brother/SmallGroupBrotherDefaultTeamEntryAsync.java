package com.gy.server.game.smallGroup.async.brother;

import com.gy.server.common.distributedlock.DistributedLockUtilManager;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.SmallGroupHelper;
import com.gy.server.game.smallGroup.bean.PlayerSmallGroupIdInfo;
import com.gy.server.game.smallGroup.bean.SmallGroupMemberInfo;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherInfo;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.ttlike.server.tl.baselib.TLBase;

/**
 * 金兰默认进队 异步处理
 *
 * <AUTHOR> 2025/1/23 14:17
 **/
public class SmallGroupBrotherDefaultTeamEntryAsync extends AsyncCall {

    private Player player;
    private boolean entry;
    private long time;

    private PbProtocol.SmallGroupBrotherDefaultTeamEntryRst.Builder rst;

    public SmallGroupBrotherDefaultTeamEntryAsync(Player player, PbProtocol.SmallGroupBrotherDefaultTeamEntryReq req, long time) {
        this.player = player;
        this.entry = req.getEntry();
        this.time = time;
        this.rst = PbProtocol.SmallGroupBrotherDefaultTeamEntryRst.newBuilder().setResult(Text.genOkServerRstInfo());
    }

    @Override
    public void execute() {
        player.send(PtCode.SMALL_GROUP_BROTHER_DEFAULT_TEAM_ENTRY_RST, rst.build(), time);
    }

    @Override
    public void asyncExecute() {
        String sgIdKey = GsRedisKey.SmallGroup.player_small_group_id_info.getRedisKey(player.getPlayerId());
        PlayerSmallGroupIdInfo smallGroupIdInfo = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, sgIdKey);
        if(smallGroupIdInfo == null || (smallGroupIdInfo != null && smallGroupIdInfo.getBrotherId() != 0)){
            rst.setResult(Text.genServerRstInfo(Text.金兰不存在));
            return;
        }

        String sgInfoKey = GsRedisKey.SmallGroup.small_group_info.getRedisKey(smallGroupIdInfo.getBrotherId());

        DistributedLockUtilManager.operate(() -> {
            SmallGroupBrotherInfo brotherInfo = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupBrotherInfo.class, sgInfoKey);
            if(brotherInfo == null || (brotherInfo != null && !brotherInfo.getMemberMap().containsKey(player.getPlayerId()))){
                rst.setResult(Text.genServerRstInfo(Text.金兰不存在));
                return;
            }

            SmallGroupMemberInfo memberInfo = brotherInfo.getMemberMap().get(player.getPlayerId());
            memberInfo.setDefaultEntry(entry);
            TLBase.getInstance().getRedisAssistant().setBean(sgInfoKey, brotherInfo);

            rst.setGroupInfo(SmallGroupHelper.genSmallGroupInfo(brotherInfo));
        }, sgInfoKey);
    }
}

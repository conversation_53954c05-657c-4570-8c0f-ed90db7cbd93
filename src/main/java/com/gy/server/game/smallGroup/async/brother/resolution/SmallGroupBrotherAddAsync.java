package com.gy.server.game.smallGroup.async.brother.resolution;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.newFriend.FriendGlobalData;
import com.gy.server.game.newFriend.FriendHelper;
import com.gy.server.game.newFriend.bean.FriendPersonInfo;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.SmallGroupHelper;
import com.gy.server.game.smallGroup.SmallGroupService;
import com.gy.server.game.smallGroup.bean.PlayerSmallGroupIdInfo;
import com.gy.server.game.smallGroup.bean.SmallGroupTypeEnum;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherInfo;
import com.gy.server.game.smallGroup.template.SmallGroupBrotherResolutionEnum;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.TLBase;

/**
 * 金兰纳新异步处理
 *
 * <AUTHOR> 2024/12/26 19:59
 **/
public class SmallGroupBrotherAddAsync extends AsyncCall {

    private Player player;
    private long targetId;
    private long time;

    private int text = Text.没有异常;

    public SmallGroupBrotherAddAsync(Player player, PbProtocol.SmallGroupBrotherAddReq req, long time) {
        this.player = player;
        this.targetId = req.getTargetId();
        this.time = time;
    }

    @Override
    public void execute() {
        PbProtocol.SmallGroupBrotherAddRst.Builder rst = PbProtocol.SmallGroupBrotherAddRst.newBuilder().setResult(Text.genServerRstInfo(text));
        player.send(PtCode.SMALL_GROUP_BROTHER_ADD_RST, rst.build(), time);
    }

    @Override
    public void asyncExecute() {
        if(player.getPlayerId() == targetId){
            text = Text.不能邀请自己;
            return;
        }

        String redisKey = GsRedisKey.SmallGroup.player_small_group_id_info.getRedisKey(player.getPlayerId());
        PlayerSmallGroupIdInfo playerSmallGroupIdInfo = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, redisKey);
        if(playerSmallGroupIdInfo == null){
            text = Text.金兰不存在;
            return;
        }

        if(playerSmallGroupIdInfo.getBrotherId() == 0){
            text = Text.金兰不存在;
            return;
        }

        FriendGlobalData globalData = GlobalDataManager.getData(GlobalDataType.friend);
        if(!globalData.isFriend(player.getPlayerId(), targetId)){
            text = Text.该玩家不是你的好友;
            return;
        }
        FriendPersonInfo personInfo = FriendHelper.getFriendPersonInfo(player.getPlayerId());
        int likability = personInfo.getLikabilityMap().getOrDefault(targetId, 0);
        if(likability < SmallGroupService.getSmallGroupConst().swornBrothersLikabilityMixNum){
            text = Text.好感度不足;
            return;
        }

        redisKey = GsRedisKey.SmallGroup.small_group_info.getRedisKey(playerSmallGroupIdInfo.getBrotherId());
        SmallGroupBrotherInfo brotherInfo = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupBrotherInfo.class, redisKey);
        if(brotherInfo == null){
            text = Text.金兰不存在;
            return;
        }

        int maxNum = SmallGroupService.getBrotherMaxNum(brotherInfo.getExp());
        if(brotherInfo.getMemberMap().size() >= maxNum){
            text = Text.金兰人数已满;
            return;
        }

        int textTemp = brotherInfo.canAddResolution(SmallGroupBrotherResolutionEnum.add, player.getPlayerId());
        if(textTemp != Text.没有异常){
            text = textTemp;
            return;
        }

        long endTime = ServerConstants.getCurrentTimeMillis() + SmallGroupService.getSmallGroupConst().swornBrothersInviteTime * DateTimeUtil.MillisOfSecond;
        SmallGroupHelper.smallGroupInviteDeal(SmallGroupTypeEnum.brother, targetId, player.getPlayerId(), player.getName(), endTime);
    }
}

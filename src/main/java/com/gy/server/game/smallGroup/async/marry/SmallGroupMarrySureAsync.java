package com.gy.server.game.smallGroup.async.marry;

import com.gy.server.common.distributedlock.DistributedLockUtilManager;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.SmallGroupGlobalData;
import com.gy.server.game.smallGroup.SmallGroupHelper;
import com.gy.server.game.smallGroup.bean.PlayerSmallGroupIdInfo;
import com.gy.server.game.smallGroup.bean.SmallGroupCreateSignInfo;
import com.gy.server.game.smallGroup.bean.SmallGroupTypeEnum;
import com.gy.server.game.smallGroup.bean.marry.SmallGroupMarryCreate;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbSmallGroup;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.serialize.base.RedisSetLongBean;


/**
 * 情缘成员确定 异步处理
 *
 * <AUTHOR> 2025/2/20 13:30
 **/
public class SmallGroupMarrySureAsync extends AsyncCall {

    private Player player;
    private long time;

    /**
     * 1宣誓 2.指纹
     */
    private int type;

    private int text = Text.没有异常;

    public SmallGroupMarrySureAsync(Player player, PbProtocol.SmallGroupMarrySureReq req, long time) {
        this.type = req.getType();
        this.player = player;
        this.time = time;
    }

    @Override
    public void execute() {
        PbProtocol.SmallGroupMarrySureRst.Builder rst = PbProtocol.SmallGroupMarrySureRst.newBuilder().setResult(Text.genServerRstInfo(text));
        player.send(PtCode.SMALL_GROUP_MARRY_SURE_RST, rst.build(), time);
    }

    @Override
    public void asyncExecute() {
        if(type != 1 && type != 2){
            text = Text.参数异常;
            return;
        }

        SmallGroupGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroup);
        SmallGroupCreateSignInfo createSignInfo = globalData.getCreateSignInfo(player.getPlayerId(), SmallGroupTypeEnum.marry);
        if(createSignInfo == null){
            text = Text.参数异常;
            return;
        }

        String redisKey1 = GsRedisKey.SmallGroup.player_small_group_id_info.getRedisKey(player.getPlayerId());
        PlayerSmallGroupIdInfo idInfo = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, redisKey1);
        if(idInfo != null && idInfo.getMarryId() != 0){
            text = Text.当前已有情缘;
            return;
        }

        String redisKey2 = GsRedisKey.SmallGroup.small_group_create.getRedisKey(createSignInfo.getId());
        DistributedLockUtilManager.operate(() -> {
            SmallGroupMarryCreate marryCreate = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupMarryCreate.class, redisKey2);
            if(marryCreate == null){
                text = Text.情缘当前无创建;
                return;
            }
            
            if(marryCreate.getNodeType() != PbSmallGroup.SgmNodeType.sgmBlessing){
                text = Text.当前阶段不能进行;
                return;
            }

            if(type == 2 && marryCreate.getSureMap().getOrDefault(1, new RedisSetLongBean()).getSet().size() < marryCreate.getMemberList().size()){
                text = Text.宣誓未完成;
                return;
            }

            RedisSetLongBean setLongBean = marryCreate.getSureMap().computeIfAbsent(type, bean -> new RedisSetLongBean());
            setLongBean.getSet().add(player.getPlayerId());

            if(type == 2 && marryCreate.getSureMap().getOrDefault(2, new RedisSetLongBean()).getSet().size() == marryCreate.getMemberList().size()){
                marryCreate.sgmNodeTypeNext(false);

                //创建成功
                SmallGroupHelper.crateMarryInfoDeal(marryCreate);
            }

            TLBase.getInstance().getRedisAssistant().setBean(redisKey2, marryCreate);
            SmallGroupHelper.smallGroupCreateInfoNotify(marryCreate);
        }, redisKey2);
    }
}

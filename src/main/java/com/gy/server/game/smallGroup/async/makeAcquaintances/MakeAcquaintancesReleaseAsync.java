package com.gy.server.game.smallGroup.async.makeAcquaintances;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.keyword.KeyWordService;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.PlayerSmallGroupModel;
import com.gy.server.game.smallGroup.SmallGroupService;
import com.gy.server.game.smallGroup.bean.PlayerSmallGroupIdInfo;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherInfo;
import com.gy.server.game.smallGroup.bean.makeAcquaintances.SmallGroupMakeAcquaintancesEnum;
import com.gy.server.game.smallGroup.bean.makeAcquaintances.SmallGroupMakeAcquaintancesInfo;
import com.gy.server.game.smallGroup.template.SmallGroupLabelClassTemplate;
import com.gy.server.game.smallGroup.template.SmallGroupPlatformTemplate;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbSmallGroup;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.TLBase;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 发布结识异步处理
 * <AUTHOR> 2025/1/2 19:29
 **/
public class MakeAcquaintancesReleaseAsync extends AsyncCall {

    private Player player;
    private SmallGroupPlatformTemplate template;
    private List<PbSmallGroup.MakeAcquaintancesLabelInfo> labelInfoList = new ArrayList<>();
    private String context;
    private int needNum;
    private int gender;
    private boolean release;
    private long time;

    /**
     * key:顺序   value:标签id集合
     */
    private Map<Integer, Set<Integer>> labelMap = new HashMap<>();
    private int text = Text.没有异常;

    public MakeAcquaintancesReleaseAsync(Player player, PbProtocol.MakeAcquaintancesReleaseReq req, long time) {
        this.player = player;
        this.template = SmallGroupService.getPlatformTemplateMap().get(req.getId());
        this.labelInfoList.addAll(req.getLabelInfoList());
        this.context = req.getContext();
        this.needNum = req.getNeedNum();
        this.gender = req.getGender();
        this.release = req.getRelease();
        this.time = time;
    }

    @Override
    public void execute() {
        PbProtocol.MakeAcquaintancesReleaseRst.Builder rst = PbProtocol.MakeAcquaintancesReleaseRst.newBuilder().setResult(Text.genServerRstInfo(text));
        player.send(PtCode.MAKE_ACQUAINTANCES_RELEASE_RST, rst.build(), time);
    }

    @Override
    public void asyncExecute() {
        if(release){
            if(template == null){
                text = Text.参数异常;
                return;
            }

            PlayerSmallGroupModel model = player.getPlayerSmallGroupModel();
            if(ServerConstants.getCurrentTimeMillis() < model.getNextTime().get()){
                text = Text.发布结识CD中;
                return;
            }

            for (PbSmallGroup.MakeAcquaintancesLabelInfo bean : labelInfoList) {
                if(!template.labelMap.containsKey(bean.getLabelClassId())){
                    continue;
                }

                Set<Integer> labelIdSet = new HashSet<>();
                int limitNum = template.labelMap.get(bean.getLabelClassId());
                List<Integer> labelIdList = bean.getLabelIdList();
                for (Integer id : labelIdList) {
                    if(labelIdSet.size() >= limitNum){
                        continue;
                    }

                    SmallGroupLabelClassTemplate labelClassTemplate = SmallGroupService.getLabelClassMap().get(bean.getLabelClassId());
                    if(labelClassTemplate == null){
                        continue;
                    }

                    if(!labelClassTemplate.value.contains(id)){
                        continue;
                    }

                    labelIdSet.add(id);
                }

                if(!labelIdSet.isEmpty()){
                    labelMap.put(bean.getLabelClassId(), labelIdSet);
                }
            }

            if(context.length() > 20){
                text = Text.结识宣言超长;
                return;
            }

            if (KeyWordService.contains(context)) {
                text = Text.结识宣言不可用;
                return;
            }

            if(!template.isNum && needNum != 0){
                needNum = 0;
            }

            PlayerSmallGroupIdInfo playerSmallGroupIdInfo = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, GsRedisKey.SmallGroup.player_small_group_id_info.getRedisKey(player.getPlayerId()));
            if(template.type == SmallGroupMakeAcquaintancesEnum.brotherPerson){
                if(playerSmallGroupIdInfo != null && playerSmallGroupIdInfo.getBrotherId() != 0){
                    text = Text.参数异常;
                    return;
                }
            }

            if(template.type == SmallGroupMakeAcquaintancesEnum.brotherGroup){
                if(playerSmallGroupIdInfo == null ||(playerSmallGroupIdInfo != null && playerSmallGroupIdInfo.getBrotherId() == 0)){
                    text = Text.参数异常;
                    return;
                }
            }

            SmallGroupBrotherInfo brotherInfo = null;
            if(template.type == SmallGroupMakeAcquaintancesEnum.brotherGroup && template.isNum){
                if(playerSmallGroupIdInfo == null || (playerSmallGroupIdInfo != null && playerSmallGroupIdInfo.getBrotherId() == 0)){
                    text = Text.金兰不存在;
                    return;
                }

                brotherInfo = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupBrotherInfo.class, GsRedisKey.SmallGroup.small_group_info.getRedisKey(playerSmallGroupIdInfo.getBrotherId()));
                if(brotherInfo == null){
                    text = Text.金兰不存在;
                    return;
                }

                int maxNum = SmallGroupService.getBrotherMaxNum(brotherInfo.getExp());
                if(needNum > maxNum - brotherInfo.getMemberMap().size() || needNum == 0){
                    text = Text.结识需求人数不对;
                    return;
                }
            }

            if(!template.isGender && gender != 0){
                gender = 0;
            }

            SmallGroupMakeAcquaintancesInfo makeAcquaintancesInfo = new SmallGroupMakeAcquaintancesInfo(template.id, player, brotherInfo, labelMap, context, needNum, gender);
            String redisKey = GsRedisKey.SmallGroup.make_acquaintances_info.getRedisKey(template.id);
            TLBase.getInstance().getRedisAssistant().hashPut(redisKey, makeAcquaintancesInfo.getSenderId() + "", makeAcquaintancesInfo);
            model.setNextTime(new AtomicLong(ServerConstants.getCurrentTimeMillis() + DateTimeUtil.MillisOfMinute * 30));
        }else {
            String redisKey = GsRedisKey.SmallGroup.make_acquaintances_info.getRedisKey(template.id);
            TLBase.getInstance().getRedisAssistant().hashRemove(redisKey, player.getPlayerId() + "");
        }
    }

}

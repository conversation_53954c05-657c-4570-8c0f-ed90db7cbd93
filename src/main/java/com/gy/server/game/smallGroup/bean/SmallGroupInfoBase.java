package com.gy.server.game.smallGroup.bean;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.SmallGroupService;
import com.gy.server.packet.PbSmallGroup;
import com.gy.server.utils.MathUtil;

import java.util.*;

/**
 * <AUTHOR> 2024/12/10 14:42
 **/
@ProtobufClass
public class SmallGroupInfoBase {

    /**
     * 小团体唯一id
     */
    @Protobuf(order = 1)
    private long id;

    /**
     * 小团体类型
     */
    @Protobuf(order = 2)
    private SmallGroupTypeEnum type;

    /**
     * 经验
     */
    @Protobuf(order = 3)
    private int exp;

    /**
     * 本周经验值
     */
    @Protobuf(order = 4)
    private int weekExp;

    /**
     * 可刷新本周经验值事件
     */
    @Protobuf(order = 5)
    private long canRefreshWeekExpTime;

    /**
     * key:player id  value:成员信息
     */
    @Protobuf(order = 6)
    private Map<Long, SmallGroupMemberInfo> memberMap = new HashMap<>();

    /**
     * 创建时间
     */
    @Protobuf(order = 7)
    private long createTime;

    /**
     * 公共日志
     * key:种类
     */
    @Protobuf(order = 8)
    private Map<Integer, SmallGroupLogList> commonLogMap = new HashMap<>();

    /**
     * 个人日志
     * key:玩家id
     */
    @Protobuf(order = 9)
    private Map<Long, SmallGroupLogList> personLogMap = new HashMap<>();

    public SmallGroupInfoBase() {
    }

    public SmallGroupInfoBase(SmallGroupTypeEnum type, Map<Long, SmallGroupMemberInfo> memberMap) {
        this.id = Long.parseLong(type.getType() + "" + MathUtil.randomInt(100) + "" + ServerConstants.getCurrentTimeMillis());
        this.type = type;
        this.memberMap = memberMap;

        //成员加入日志
        for (SmallGroupMemberInfo bean : memberMap.values()) {
            addMember(bean);
//            addSmallGroupLog(SmallGroupLogTypeEnum.swornBrothersPersonTime, bean.getPlayerId());
        }
    }

    public void addSmallGroupLog(List<SmallGroupLog> logList){
        for (SmallGroupLog bean : logList) {
            addSmallGroupLog(bean);
        }
    }

    public void addSmallGroupLog(SmallGroupLog log){
        addSmallGroupLog(log.getLogType(), log.getBelong(), log.getParams().toArray(new String[0]));
    }

    /**
     * 增加日志
     * @param logType
     * @param playerId
     * @param params
     */
    public void addSmallGroupLog(SmallGroupLogTypeEnum logType, long playerId, String... params){
        SmallGroupLogList logList = null;
        switch (logType.getBelongType()){
            case 1:{
                logList = commonLogMap.computeIfAbsent(logType.getId(), bean -> new SmallGroupLogList());
                break;
            }
            case 2:{
                logList = personLogMap.computeIfAbsent(playerId, bean -> new SmallGroupLogList());
                break;
            }
        }

        if(logList == null){
            return;
        }

        if(logType.isNeedMerge()){
            logType.mergeLog(logList, playerId, params);
            return;
        }

        logList.add(logType, playerId, params);
    }

    /**
     * 增加小团体经验
     * @param addValue
     */
    public void addExp(int addValue) {
        switch (type){
            case brother:{
                int temp = SmallGroupService.getSmallGroupConst().swornBrothersExpMaxWeekly - weekExp;
                int addNum = addValue > temp ? temp : addValue;

                weekExp = weekExp + addNum;
                exp = exp + addNum;
                break;
            }
            default:{
                exp = exp + addValue;
                break;
            }
        }
    }

    /**
     * 获取新群聊名称
     * @return
     */
    public String getFriendGroupName(){
        return "";
    }

    public void writePb(PbSmallGroup.SmallGroupInfo.Builder builder){

    }

    /**
     * 移除成员
     * @param memberId
     */
    public void removeMember(long memberId) {
        memberMap.remove(memberId);
        personLogMap.remove(memberId);
    }

    /**
     * 增加成员
     * @param bean
     */
    public void addMember(SmallGroupMemberInfo bean){
        //
        memberMap.put(bean.getPlayerId(), bean);
        addSmallGroupLog(SmallGroupLogTypeEnum.swornBrothersPersonTime, bean.getPlayerId());
    }

    public List<Long> getMemberList(){
        return new ArrayList<>(memberMap.keySet());
    }

    public Map<Integer, Set<Long>> getServerMemberMap(){
        Map<Integer, Set<Long>> map = new HashMap<>();
        for (long playerId : memberMap.keySet()) {
            map.computeIfAbsent(Player.getServerId(playerId), set -> new HashSet<>()).add(playerId);
        }
        return map;
    }

    public void weekRefresh() {
        weekExp = 0;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public int getExp() {
        return exp;
    }

    public void setExp(int exp) {
        this.exp = exp;
    }

    public int getWeekExp() {
        return weekExp;
    }

    public void setWeekExp(int weekExp) {
        this.weekExp = weekExp;
    }

    /**
     * 只获取，成员增减不用这
     * @return
     */
    public Map<Long, SmallGroupMemberInfo> getMemberMap() {
        return memberMap;
    }

    public void setMemberMap(Map<Long, SmallGroupMemberInfo> memberMap) {
        this.memberMap = memberMap;
    }

    public SmallGroupTypeEnum getType() {
        return type;
    }

    public void setType(SmallGroupTypeEnum type) {
        this.type = type;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public long getCanRefreshWeekExpTime() {
        return canRefreshWeekExpTime;
    }

    public void setCanRefreshWeekExpTime(long canRefreshWeekExpTime) {
        this.canRefreshWeekExpTime = canRefreshWeekExpTime;
    }

    public Map<Integer, SmallGroupLogList> getCommonLogMap() {
        return commonLogMap;
    }

    public void setCommonLogMap(Map<Integer, SmallGroupLogList> commonLogMap) {
        this.commonLogMap = commonLogMap;
    }

    public Map<Long, SmallGroupLogList> getPersonLogMap() {
        return personLogMap;
    }

    public void setPersonLogMap(Map<Long, SmallGroupLogList> personLogMap) {
        this.personLogMap = personLogMap;
    }

}

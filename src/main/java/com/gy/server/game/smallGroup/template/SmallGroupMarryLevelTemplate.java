package com.gy.server.game.smallGroup.template;

import com.gy.server.game.drop.RewardTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 情缘等级模板
 *
 * <AUTHOR> 2025/1/17 10:01
 **/
public class SmallGroupMarryLevelTemplate {

    public int id;

    /**
     * 需求同心值
     */
    public int exp;

    /**
     * 同心值等级奖励
     */
    public List<RewardTemplate> reward = new ArrayList<>();

    public SmallGroupMarryLevelTemplate(Map<String, String> map){
        this.id = Integer.parseInt(map.get("id"));
        this.exp = Integer.parseInt(map.get("exp"));
        this.reward = RewardTemplate.readListFromText(map.get("reward"));
    }
}

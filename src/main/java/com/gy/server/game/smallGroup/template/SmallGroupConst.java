package com.gy.server.game.smallGroup.template;

import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.util.StringExtUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 小团体常量表
 *
 * <AUTHOR> 2024/12/11 10:44
 **/
public class SmallGroupConst {

    /**
     * 首次金兰最小人数
     */
    public int swornBrothersMixNum;

    /**
     * 首次金兰最大人数
     */
    public int swornBrothersMaxNum;

    /**
     * 金兰需要最低好感度
     */
    public int swornBrothersLikabilityMixNum;

    /**
     * 金兰申请，队长消耗道具
     */
    public List<RewardTemplate> swornBrothersConsume = new ArrayList<>();

    /**
     * 金兰个人退出金兰冷静期时间（秒）
     */
    public int personalExitTime;

    /**
     * 金兰决议总时长（秒）
     */
    public int swornBrothersResolutionTime;

    /**
     * 金兰个人发起决议冷却时间（秒）
     */
    public int personaResolutionCD;

    /**
     * 金兰名称第一个字可选库（syslangID）
     */
    public List<Long> firstNameSyslang = new ArrayList<>();

    /**
     * 金兰名称第后面3格每个汉字数量
     */
    public int nameNum;

    /**
     * 金兰字号玩家可输入汉字数量
     */
    public int lastNameNum;

    /**
     * 金兰结拜任务(procedureTask表初始id)
     */
    public int swornBrothersTask;

    /**
     * 金兰结拜成功称号ID
     */
    public int swornBrothersDesignation;

    /**
     * 金兰宣言文本字节长度（1汉字2字节）
     */
    public int swornBrothersLength;

    /**
     * 金兰邀请等待保留时长（秒）
     */
    public int swornBrothersInviteTime;

    /**
     * 金兰值每周获取上限
     */
    public int swornBrothersExpMaxWeekly;

    /**
     * 情缘提亲需要最低好感度
     */
    public int marryLikabilityMixNum;

    /**
     * 强制离婚冷静期时间（秒）
     */
    public int mandatoryDivorceResolutionTime;

    /**
     * 强制离婚消耗代币
     */
    public List<RewardTemplate> mandatoryDivorceConsume = new ArrayList<>();

    /**
     * 协议离婚冷静期时间（秒）
     */
    public int agreementDivorceResolutionTime;

    /**
     * 宾客邀请最大数量上限
     */
    public int inviteGuestsMaxNum;

    /**
     * 宾客邀请增加人数价格
     */
    public List<RewardTemplate> inviteGuestsIncreaseQuantityConsume = new ArrayList<>();

    /**
     * 婚礼各阶段冷却时间（秒）
     */
    public int weddingCoolingTime;

    /**
     * 情缘称号
     */
    public int marryDesignation;


    public SmallGroupConst(Map<String, String> map){
        this.swornBrothersMixNum = Integer.parseInt(map.get("swornBrothersMixNum"));
        this.swornBrothersMaxNum = Integer.parseInt(map.get("swornBrothersMaxNum"));
        this.swornBrothersLikabilityMixNum = Integer.parseInt(map.get("swornBrothersLikabilityMixNum"));
        this.swornBrothersConsume = RewardTemplate.readListFromText(map.get("swornBrothersConsume"));
        this.personalExitTime = Integer.parseInt(map.get("personalExitTime"));
        this.swornBrothersResolutionTime = Integer.parseInt(map.get("swornBrothersResolutionTime"));
        this.personaResolutionCD = Integer.parseInt(map.get("personaResolutionCD"));
        this.firstNameSyslang = StringExtUtil.string2List(map.get("firstNameSyslang"), ",", Long.class);
        this.nameNum = Integer.parseInt(map.get("nameNum"));
        this.lastNameNum = Integer.parseInt(map.get("lastNameNum"));
        this.swornBrothersTask = Integer.parseInt(map.get("swornBrothersTask"));
        this.swornBrothersDesignation = Integer.parseInt(map.get("swornBrothersDesignation"));
        this.swornBrothersLength = Integer.parseInt(map.get("swornBrothersLength"));
        this.swornBrothersInviteTime = Integer.parseInt(map.get("swornBrothersInviteTime"));
        this.swornBrothersExpMaxWeekly = Integer.parseInt(map.get("swornBrothersExpMaxWeekly"));
        this.marryLikabilityMixNum = Integer.parseInt(map.get("marryLikabilityMixNum"));
        this.mandatoryDivorceResolutionTime = Integer.parseInt(map.get("mandatoryDivorceResolutionTime"));
        this.mandatoryDivorceConsume = RewardTemplate.readListFromText(map.get("mandatoryDivorceConsume"));
        this.agreementDivorceResolutionTime = Integer.parseInt(map.get("agreementDivorceResolutionTime"));
        this.inviteGuestsMaxNum = Integer.parseInt(map.get("inviteGuestsMaxNum"));
        this.inviteGuestsIncreaseQuantityConsume = RewardTemplate.readListFromText(map.get("inviteGuestsIncreaseQuantityConsume"));
        this.weddingCoolingTime = Integer.parseInt(map.get("weddingCoolingTime"));
        this.marryDesignation = Integer.parseInt(map.get("marryDesignation"));
    }

}

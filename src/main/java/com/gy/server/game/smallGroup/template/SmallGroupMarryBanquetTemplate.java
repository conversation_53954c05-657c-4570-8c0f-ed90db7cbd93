package com.gy.server.game.smallGroup.template;

import com.gy.server.game.drop.RewardTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 情缘菜品模板
 *
 * <AUTHOR> 2025/1/17 9:56
 **/
public class SmallGroupMarryBanquetTemplate {

    public int id;

    /**
     * 奖励
     */
    public List<RewardTemplate> reward = new ArrayList<>();

    public SmallGroupMarryBanquetTemplate(Map<String, String> map){
        this.id = Integer.parseInt(map.get("id"));
        this.reward = RewardTemplate.readListFromText(map.get("reward"));
    }
}

package com.gy.server.game.smallGroup.async.marry;

import com.gy.server.common.distributedlock.DistributedLockUtilManager;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.SmallGroupGlobalData;
import com.gy.server.game.smallGroup.SmallGroupService;
import com.gy.server.game.smallGroup.bean.SmallGroupCreateSignInfo;
import com.gy.server.game.smallGroup.bean.SmallGroupTypeEnum;
import com.gy.server.game.smallGroup.bean.marry.SmallGroupMarryCreate;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbSmallGroup;
import com.ttlike.server.tl.baselib.TLBase;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 情缘增加邀请数
 * <AUTHOR> 2025/3/26 13:44
 **/
public class SmallGroupMarryAddInviteNumAsync extends AsyncCall {

    private Player player;
    private int addNum;
    private long time;

    private int text = Text.没有异常;

    public SmallGroupMarryAddInviteNumAsync(Player player, PbProtocol.SmallGroupMarryAddInviteNumReq req, long time) {
        this.player = player;
        this.addNum = req.getAddNum();
        this.time = time;
    }

    @Override
    public void execute() {
        PbProtocol.SmallGroupMarryAddInviteNumRst.Builder rst = PbProtocol.SmallGroupMarryAddInviteNumRst.newBuilder().setResult(Text.genServerRstInfo(text));
        player.send(PtCode.SMALL_GROUP_MARRY_ADD_INVITE_NUM_RST, rst.build(), time);
    }

    @Override
    public void asyncExecute() {
        int inviteNum = SmallGroupService.getSmallGroupConst().inviteGuestsMaxNum;
        if(addNum <= 0 || addNum > inviteNum){
            text = Text.参数异常;
            return;
        }

        SmallGroupGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroup);
        SmallGroupCreateSignInfo signInfo = globalData.getCreateSignInfoMap().getOrDefault(player.getPlayerId(), new ConcurrentHashMap<>()).get(SmallGroupTypeEnum.marry.getType());
        if(signInfo == null){
            text = Text.情缘当前无创建;
            return;
        }

        long createId = signInfo.getId();
        String redisKey = GsRedisKey.SmallGroup.small_group_create.getRedisKey(createId);
        DistributedLockUtilManager.operate(() -> {
            SmallGroupMarryCreate marryCreate = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupMarryCreate.class, redisKey);
            if(marryCreate == null){
                text = Text.情缘当前无创建;
                return;
            }

            if(marryCreate.getNodeType() != PbSmallGroup.SgmNodeType.sgmMarriageWait){
                text = Text.情缘创建当前阶段不能增加邀请数;
                return;
            }

            if(marryCreate.getInviteNum() >= inviteNum){
                text = Text.情缘创建邀请数已达最大数量;
                return;
            }

            if(marryCreate.getInviteNum() + addNum > inviteNum){
                text = Text.情缘创建邀请后超过最大数量;
                return;
            }

            List<Reward> rewardList = Reward.templateCollectionToReward(SmallGroupService.getSmallGroupConst().inviteGuestsIncreaseQuantityConsume);
            for (Reward reward : rewardList) {
                reward.setValue(reward.getValue() * addNum);
            }

            if(Reward.check(player, rewardList) != -1){
                text = Text.消耗不足;
                return;
            }
            Reward.remove(rewardList, player, BehaviorType.smallGroupMarryAddInviteNum);

            marryCreate.setInviteNum(marryCreate.getInviteNum() + addNum);
            TLBase.getInstance().getRedisAssistant().setBean(redisKey, marryCreate);
        }, redisKey);
    }
}

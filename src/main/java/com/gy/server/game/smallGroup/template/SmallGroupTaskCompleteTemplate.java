package com.gy.server.game.smallGroup.template;

import com.gy.server.game.drop.RewardTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 小团体任务积分奖励
 *
 * <AUTHOR> 2024/12/11 14:26
 **/
public class SmallGroupTaskCompleteTemplate {

    public int id;

    /**
     * 积分数量
     */
    public int pointNumber;

    /**
     * 奖励
     */
    public List<RewardTemplate> reward = new ArrayList<>();

    public SmallGroupTaskCompleteTemplate(Map<String, String> map){
        this.id = Integer.parseInt(map.get("id"));
        this.pointNumber = Integer.parseInt(map.get("pointNumber"));
        this.reward = RewardTemplate.readListFromText(map.get("reward"));
    }
}

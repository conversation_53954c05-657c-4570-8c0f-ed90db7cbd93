package com.gy.server.game.smallGroup.bean;

import com.gy.server.packet.PbProtocol;
import com.ttlike.server.tl.baselib.serialize.smallGroup.SmallGroupInviteInfoDb;

/**
 * 小团体邀请信息
 *
 * <AUTHOR> 2025/2/8 14:59
 **/
public class SmallGroupInviteInfo {

    private int type;

    /**
     * 被邀请者
     */
    private long targetId;

    /**
     * 邀请者
     */
    private long inviterId;

    /**
     * 邀请者名称
     */
    private String inviterName;

    /**
     * 结束时间
     */
    private long endTime;

    /**
     * 提亲档次id，仅在类型为情缘时有效
     */
    private int consumeId;

    public SmallGroupInviteInfo() {
    }

    public SmallGroupInviteInfo(int type, long targetId, long inviterId, String inviterName, long endTime, int consumeId) {
        this.type = type;
        this.targetId = targetId;
        this.inviterId = inviterId;
        this.inviterName = inviterName;
        this.endTime = endTime;
        this.consumeId = consumeId;
    }

    public SmallGroupInviteInfo(SmallGroupInviteInfoDb bean) {
        this.type = bean.getType();
        this.targetId = bean.getTargetId();
        this.inviterId = bean.getInviterId();
        this.inviterName = bean.getInviterName();
        this.endTime = bean.getEndTime();
    }

    public PbProtocol.SmallGroupInviteNotify genNotify(){
        PbProtocol.SmallGroupInviteNotify.Builder notify = PbProtocol.SmallGroupInviteNotify.newBuilder();
        notify.setType(type);
        notify.setInviterId(inviterId);
        notify.setInviterName(inviterName);
        notify.setEndTime(endTime);
        notify.setConsumeId(consumeId);
        return notify.build();
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public long getTargetId() {
        return targetId;
    }

    public void setTargetId(long targetId) {
        this.targetId = targetId;
    }

    public long getInviterId() {
        return inviterId;
    }

    public void setInviterId(long inviterId) {
        this.inviterId = inviterId;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public String getInviterName() {
        return inviterName;
    }

    public void setInviterName(String inviterName) {
        this.inviterName = inviterName;
    }

    public String getKey(){
        return inviterId + "" + type;
    }

    public int getConsumeId() {
        return consumeId;
    }

    public void setConsumeId(int consumeId) {
        this.consumeId = consumeId;
    }
}

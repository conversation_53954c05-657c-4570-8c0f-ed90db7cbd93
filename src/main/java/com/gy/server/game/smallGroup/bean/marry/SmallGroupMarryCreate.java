package com.gy.server.game.smallGroup.bean.marry;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.SmallGroupHelper;
import com.gy.server.game.smallGroup.SmallGroupService;
import com.gy.server.game.smallGroup.bean.*;
import com.gy.server.game.smallGroup.template.SmallGroupMarryFireworksClassTemplate;
import com.gy.server.packet.PbCommons;
import com.gy.server.packet.PbSmallGroup;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.serialize.base.RedisListIntegerBean;
import com.ttlike.server.tl.baselib.serialize.base.RedisSetLongBean;

import java.util.*;

/**
 * 情缘创建信息
 *
 * <AUTHOR> 2025/1/17 13:22
 **/
@ProtobufClass
public class SmallGroupMarryCreate extends SmallGroupCreateBase {

    /**
     * 巡游类型id
     */
    @Protobuf(order = 11)
    private int cruiseId;

    /**
     * 巡游开始时间
     */
    @Protobuf(order = 12)
    private long cruiseStart;

    /**
     * 巡游结果
     */
    @Protobuf(order = 13)
    private boolean cruiseResult = true;

    /**
     * 职位map
     * key:玩家id  value:职位
     */
    @Protobuf(order = 14)
    private Map<Long, Integer> jobMap = new HashMap<>();

    /**
     * 烟花map
     * key:烟花id value:个数
     */
    @Protobuf(order = 15)
    private Map<Integer, Integer> fireworkMap = new HashMap<>();

    /**
     * 可邀请数量
     */
    @Protobuf(order = 16)
    private int inviteNum;

    /**
     * 已邀请
     */
    @Protobuf(order = 17)
    private Set<Long> inviteIdSet = new HashSet<>();

    /**
     * 当前节点
     */
    @Protobuf(order = 18)
    private PbSmallGroup.SgmNodeType nodeType = PbSmallGroup.SgmNodeType.sgmWait;

    /**
     * 当前节点结束时间
     * -1为等待巡游预约
     */
    @Protobuf(order = 19)
    private long endTime = -1;

    /**
     * 已经吃过菜肴的人map
     * key:玩家id     value:已经吃过的菜肴id
     */
    @Protobuf(order = 20)
    private Map<Long, RedisListIntegerBean> banquetMap = new HashMap<>();

    /**
     * 确认map
     * key:1宣誓 2.指纹 value:玩家id集合
     */
    @Protobuf(order = 21)
    private Map<Integer, RedisSetLongBean> sureMap = new HashMap<>();

    /**
     * 第几对
     */
    @Protobuf(order = 22)
    private int marryNum;

    /**
     * 女方职业
     */
    @Protobuf(order = 23)
    private int professionId;

    @Protobuf(order = 24)
    private List<SmallGroupLog> logList = new ArrayList<>();

    public SmallGroupMarryCreate() {
        super(SmallGroupTypeEnum.marry, -1);
    }

    public SmallGroupMarryCreate(int createTaskId) {
        super(SmallGroupTypeEnum.marry, -1);
    }

    @Override
    public void writePb(PbSmallGroup.SmallGroupCreateInfo.Builder builder) {
        PbSmallGroup.SmallGroupMarryCreateInfo.Builder createInfo = PbSmallGroup.SmallGroupMarryCreateInfo.newBuilder();
        createInfo.setCruiseId(cruiseId);
        createInfo.setCruiseStartTime(cruiseStart);
        for (Map.Entry<Long, Integer> entry : jobMap.entrySet()) {
            SmallGroupJobEnum job = SmallGroupJobEnum.getSmallGroupJobEnum(entry.getValue());
            createInfo.putJobMap(entry.getKey(), job.genPb());
        }
        createInfo.putAllFireworkMap(fireworkMap);
        createInfo.setInviteNum(inviteNum);
        createInfo.addAllInviteIds(inviteIdSet);
        createInfo.setNodeType(nodeType);
        createInfo.setEndTime(endTime);
        for (Map.Entry<Long, RedisListIntegerBean> entry : banquetMap.entrySet()) {
            createInfo.putBanquetMap(entry.getKey(), PbCommons.ListIntegerBean.newBuilder().addAllNum(entry.getValue().getList()).build());
        }
        for (Map.Entry<Integer, RedisSetLongBean> entry : sureMap.entrySet()) {
            createInfo.putSureMap(entry.getKey(), PbCommons.ListLongBean.newBuilder().addAllNum(entry.getValue().getSet()).build());
        }

        for (SmallGroupLog bean : logList) {
            builder.addLog(bean.genPb());
        }
        builder.setMarry(createInfo.build());
    }

    public PbSmallGroup.CruiseInfo genCruiseInfoPb() {
        PbSmallGroup.CruiseInfo.Builder builder = PbSmallGroup.CruiseInfo.newBuilder();
        for (Map.Entry<Long, Integer> entry : jobMap.entrySet()) {
            builder.putJobMap(entry.getKey(), SmallGroupJobEnum.getSmallGroupJobEnum(entry.getValue()).genPb());
        }
        builder.setCreateId(getId());
        builder.setCruiseId(cruiseId);
        builder.setCruiseStartTime(cruiseStart);
        builder.setCruiseEndTime(cruiseStart + cruiseStart + 5 * DateTimeUtil.MillisOfSecond);
        builder.putAllFireworkMap(fireworkMap);
        return builder.build();
    }

    /**
     * 金兰创建切换节点
     */
    public void sgmNodeTypeNext(){
        sgmNodeTypeNext(true);
    }

    /**
     * 金兰创建切换节点
     */
    public void sgmNodeTypeNext(boolean sync){
        switch (nodeType){
            case sgmWait:{
                changeNode(PbSmallGroup.SgmNodeType.sgmCruise);
                break;
            }
            case sgmCruise:{
                changeNode(PbSmallGroup.SgmNodeType.sgmMarriageWait);
                break;
            }
            case sgmMarriageWait:{
                changeNode(PbSmallGroup.SgmNodeType.sgmEatingFeast);
                break;
            }
            case sgmEatingFeast:{
                changeNode(PbSmallGroup.SgmNodeType.sgmBlessing);
                break;
            }
            case sgmBlessing:{
                changeNode(PbSmallGroup.SgmNodeType.sgmChapel);
                break;
            }
            case sgmChapel:{
                changeNode(PbSmallGroup.SgmNodeType.sgmFinish);
                createLog();
                break;
            }
        }

        if(sync){
            SmallGroupHelper.smallGroupCreateInfoNotify(this);
        }
    }

    /**
     * 创建日志，同意后前端有动画播放，创建日志需要先知道
     */
    private void createLog() {
        logList.add(new SmallGroupLog(SmallGroupLogTypeEnum.marryCreateTime, 0, ServerConstants.getCurrentTimeMillis() + ""));
        logList.add(new SmallGroupLog(SmallGroupLogTypeEnum.marryCreateNum, 0, marryNum + ""));

        for (Map.Entry<Long, Integer> entry : jobMap.entrySet()) {
            if(entry.getValue() == SmallGroupJobEnum.marry2.getSign()){
                logList.add(new SmallGroupLog(SmallGroupLogTypeEnum.marryCreateWitnesses, 0, professionId + ""));
                return;
            }
        }
    }

    public void changeNode(PbSmallGroup.SgmNodeType newNodeType){
        this.nodeType = newNodeType;
        refreshEndTime();
    }

    public void refreshEndTime() {
        switch (nodeType){
            case sgmCruise:
            case sgmMarriageWait:
            case sgmEatingFeast:
            case sgmBlessing:
            case sgmChapel:{
                this.endTime = ServerConstants.getCurrentTimeMillis() + SmallGroupService.getSmallGroupConst().weddingCoolingTime * DateTimeUtil.MillisOfSecond;
                break;
            }
        }
    }

    @Override
    public SmallGroupInfoBase create() {
        //创建小团体
        Map<Long, SmallGroupMemberInfo> memberMap = new HashMap<>();
        jobMap.forEach((key, value) -> {
            SmallGroupMemberInfo memberInfo = new SmallGroupMemberInfo(key, SmallGroupJobEnum.getSmallGroupJobEnum(value));
            memberMap.put(memberInfo.getPlayerId(), memberInfo);
        });

        SmallGroupMarryInfo marryInfo = new SmallGroupMarryInfo(memberMap);
        //创建日志改为创建信息处理，前端需要日志信息
        marryInfo.addSmallGroupLog(logList);

        //发放烟花奖励
        int addNum = 0;
        for (Map.Entry<Integer, Integer> entry : fireworkMap.entrySet()) {
            SmallGroupMarryFireworksClassTemplate template = SmallGroupService.getMarryFireworksClassTemplateMap().get(entry.getKey());
            if(template != null){
                addNum = template.reward * entry.getValue();
            }
        }

        marryInfo.setExp(addNum);
        return marryInfo;
    }

    @Override
    public void afterHandler() {

    }

    /**
     * 增加烟花
     * @param id
     * @param num
     */
    public void addFirework(int id, int num) {
        fireworkMap.put(id, fireworkMap.getOrDefault(id, 0) + num);
    }

    /**
     * 是否已经吃过菜肴
     * @param playerId
     * @param id
     * @return
     */
    public boolean hadEatBanquet(long playerId, int id) {
        if(banquetMap.containsKey(playerId)){
            RedisListIntegerBean bean = banquetMap.get(playerId);
            return bean.getList().contains(id);
        }
        return false;
    }

    public void addBanquet(long playerId, int id) {
        banquetMap.computeIfAbsent(playerId, bean -> new RedisListIntegerBean()).getList().add(id);
    }

    @Override
    public Map<Integer, Set<Long>> getServerAllMemberMap() {
        Map<Integer, Set<Long>> map = getServerMemberMap();
        for (Long playerId : inviteIdSet) {
            int serverId = Player.getServerId(playerId);
            map.computeIfAbsent(serverId, set -> new HashSet<>()).add(playerId);
        }
        return map;
    }

    public int getCruiseId() {
        return cruiseId;
    }

    public void setCruiseId(int cruiseId) {
        this.cruiseId = cruiseId;
    }

    public long getCruiseStart() {
        return cruiseStart;
    }

    public void setCruiseStart(long cruiseStart) {
        this.cruiseStart = cruiseStart;
    }

    public boolean isCruiseResult() {
        return cruiseResult;
    }

    public void setCruiseResult(boolean cruiseResult) {
        this.cruiseResult = cruiseResult;
    }

    public Map<Long, Integer> getJobMap() {
        return jobMap;
    }

    public void setJobMap(Map<Long, Integer> jobMap) {
        this.jobMap = jobMap;
    }

    public Map<Integer, Integer> getFireworkMap() {
        return fireworkMap;
    }

    public void setFireworkMap(Map<Integer, Integer> fireworkMap) {
        this.fireworkMap = fireworkMap;
    }

    public PbSmallGroup.SgmNodeType getNodeType() {
        return nodeType;
    }

    public void setNodeType(PbSmallGroup.SgmNodeType nodeType) {
        this.nodeType = nodeType;
    }

    public int getInviteNum() {
        return inviteNum;
    }

    public void setInviteNum(int inviteNum) {
        this.inviteNum = inviteNum;
    }

    public Set<Long> getInviteIdSet() {
        return inviteIdSet;
    }

    public void setInviteIdSet(Set<Long> inviteIdSet) {
        this.inviteIdSet = inviteIdSet;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public Map<Long, RedisListIntegerBean> getBanquetMap() {
        return banquetMap;
    }

    public void setBanquetMap(Map<Long, RedisListIntegerBean> banquetMap) {
        this.banquetMap = banquetMap;
    }

    public Map<Integer, RedisSetLongBean> getSureMap() {
        return sureMap;
    }

    public void setSureMap(Map<Integer, RedisSetLongBean> sureMap) {
        this.sureMap = sureMap;
    }

    public int getMarryNum() {
        return marryNum;
    }

    public void setMarryNum(int marryNum) {
        this.marryNum = marryNum;
    }

    public int getProfessionId() {
        return professionId;
    }

    public void setProfessionId(int professionId) {
        this.professionId = professionId;
    }

    public List<SmallGroupLog> getLogList() {
        return logList;
    }

    public void setLogList(List<SmallGroupLog> logList) {
        this.logList = logList;
    }
}

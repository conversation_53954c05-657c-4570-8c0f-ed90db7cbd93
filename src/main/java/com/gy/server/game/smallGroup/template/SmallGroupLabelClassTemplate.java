package com.gy.server.game.smallGroup.template;

import com.gy.server.game.util.StringExtUtil;

import java.util.List;
import java.util.Map;

/**
 * 结识标签类模板
 *
 * <AUTHOR> 2024/12/23 16:16
 **/
public class SmallGroupLabelClassTemplate {

    public int id;

    /**
     * 标签列表
     */
    public List<Integer> value;

    public SmallGroupLabelClassTemplate(Map<String, String> map) {
        this.id = Integer.parseInt(map.get("id"));
        this.value = StringExtUtil.string2List(map.get("value"), ",", Integer.class);
    }
}

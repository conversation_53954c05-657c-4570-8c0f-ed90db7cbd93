package com.gy.server.game.smallGroup.bean;


import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;
import com.gy.server.core.ServerConstants;

/**
 * 小团体成员
 *
 * <AUTHOR> 2024/12/10 15:04
 **/
@ProtobufClass
public class SmallGroupMemberInfo {

    @Protobuf(order = 1)
    private long playerId;

    /**
     * 职位
     */
    @Protobuf(order = 2)
    private SmallGroupJobEnum job;

    /**
     * 加入时间
     */
    @Protobuf(order = 3)
    private long joinTime = ServerConstants.getCurrentTimeMillis();

    /**
     * 默认进队
     */
    @Protobuf(order = 4)
    private boolean defaultEntry = false;

    public SmallGroupMemberInfo() {
    }

    public SmallGroupMemberInfo(long playerId, SmallGroupJobEnum job) {
        this.playerId = playerId;
        this.job = job;
    }

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public long getJoinTime() {
        return joinTime;
    }

    public void setJoinTime(long joinTime) {
        this.joinTime = joinTime;
    }

    public SmallGroupJobEnum getJob() {
        return job;
    }

    public void setJob(SmallGroupJobEnum job) {
        this.job = job;
    }

    public boolean isDefaultEntry() {
        return defaultEntry;
    }

    public void setDefaultEntry(boolean defaultEntry) {
        this.defaultEntry = defaultEntry;
    }
}

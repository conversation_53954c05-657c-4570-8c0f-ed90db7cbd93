package com.gy.server.game.smallGroup.bean.brother;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;
import com.gy.server.game.smallGroup.template.SmallGroupBrotherResolutionEnum;
import com.gy.server.packet.PbSmallGroup;

/**
 * 金兰决议纳新
 *
 * <AUTHOR> 2024/12/26 16:04
 **/
@ProtobufClass
public class SmallGroupBrotherResolutionAdd extends SmallGroupBrotherResolutionBase {

    /**
     * 目标id
     */
    @Protobuf(order = 11)
    private long targetId;

    /**
     * 目标名字
     */
    @Protobuf(order = 12)
    private String targetName;

    public SmallGroupBrotherResolutionAdd() {
    }

    public SmallGroupBrotherResolutionAdd(long initiatorId, String initiatorName, long targetId, String targetName) {
        super(SmallGroupBrotherResolutionEnum.add, initiatorId, initiatorName);
        this.targetId = targetId;
        this.targetName = targetName;
    }

    @Override
    public void writePb(PbSmallGroup.SmallGroupBrotherResolution.Builder builder, long brotherId) {
        PbSmallGroup.SmallGroupBrotherResolutionAdd.Builder add = PbSmallGroup.SmallGroupBrotherResolutionAdd.newBuilder();
        add.setTargetId(targetId);
        add.setTargetName(targetName);
        builder.setAdd(add.build());
    }

    public long getTargetId() {
        return targetId;
    }

    public void setTargetId(long targetId) {
        this.targetId = targetId;
    }

    public String getTargetName() {
        return targetName;
    }

    public void setTargetName(String targetName) {
        this.targetName = targetName;
    }
}

package com.gy.server.game.smallGroup.template;

import com.gy.server.game.drop.RewardTemplate;

import java.util.List;
import java.util.Map;

/**
 * 情缘邀请礼物模板
 * 失败邮件退消耗
 * <AUTHOR> 2025/1/20 19:55
 **/
public class SmallGroupMarryProposeConsumeTemplate {

    public int id;

    public List<RewardTemplate> reward;

    /**
     * 消耗
     */
    public List<RewardTemplate> consume;


    public SmallGroupMarryProposeConsumeTemplate(Map<String, String> map){
        this.id = Integer.parseInt(map.get("id"));
        this.reward = RewardTemplate.readListFromText(map.get("reward"));
        this.consume = RewardTemplate.readListFromText(map.get("consume"));
    }
}

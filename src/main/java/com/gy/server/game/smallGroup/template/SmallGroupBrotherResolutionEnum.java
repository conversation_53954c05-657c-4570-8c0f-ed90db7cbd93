package com.gy.server.game.smallGroup.template;

import com.gy.server.common.distributedlock.DistributedLockUtilManager;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.game.smallGroup.MakeAcquaintancesHelper;
import com.gy.server.game.smallGroup.SmallGroupHelper;
import com.gy.server.game.smallGroup.async.SmallGroupInfoSyncAsync;
import com.gy.server.game.smallGroup.bean.PlayerSmallGroupIdInfo;
import com.gy.server.game.smallGroup.bean.SmallGroupJobEnum;
import com.gy.server.game.smallGroup.bean.SmallGroupMemberInfo;
import com.gy.server.game.smallGroup.bean.SmallGroupTypeEnum;
import com.gy.server.game.smallGroup.bean.brother.*;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.thread.ThreadPool;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;

/**
 * 金兰决议枚举
 * 1、纳新
 * 2、合并
 * 3、改名
 * 4、请离
 * 5、解散
 * <AUTHOR> 2024/12/26 9:51
 **/
public enum SmallGroupBrotherResolutionEnum {

    /**
     * 1、纳新
     */
    add(1) {
        @Override
        public boolean needRefreshJob() {
            return true;
        }

        @Override
        public void voteFinishSpecialDeal(SmallGroupBrotherInfo brotherInfo, SmallGroupBrotherResolutionBase resolution) {
            SmallGroupBrotherResolutionAdd add = (SmallGroupBrotherResolutionAdd) resolution;
            String idInfoRedisKey = GsRedisKey.SmallGroup.player_small_group_id_info.getRedisKey(add.getTargetId());

            DistributedLockUtilManager.operate(() -> {
                PlayerSmallGroupIdInfo playerSmallGroupIdInfo = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, idInfoRedisKey);
                if(playerSmallGroupIdInfo == null){
                    playerSmallGroupIdInfo = new PlayerSmallGroupIdInfo();
                }

                if(playerSmallGroupIdInfo.getBrotherId() == 0){
                    SmallGroupJobEnum maxJob = brotherInfo.getMemberMap().values().stream().map(SmallGroupMemberInfo::getJob).max((Comparator.comparingInt(SmallGroupJobEnum::getSign))).orElse(SmallGroupJobEnum.brother1);
                    brotherInfo.addMember(new SmallGroupMemberInfo(add.getTargetId(), maxJob.next()));

                    playerSmallGroupIdInfo.setBrotherId(brotherInfo.getId());
                    TLBase.getInstance().getRedisAssistant().setBean(idInfoRedisKey, playerSmallGroupIdInfo);
                }
            }, idInfoRedisKey);

            //处理小团体任务
            Set<Long> playerIdSet = new HashSet<>();
            playerIdSet.add(add.getTargetId());
            SmallGroupHelper.smallGroupTaskDeal(playerIdSet, SmallGroupTypeEnum.brother, true);

            SmallGroupHelper.smallGroupJoinDeal(add.getTargetId(), brotherInfo.getId());
        }

        @Override
        public Pair<Boolean, Boolean> voteFinishSpecialCheck(SmallGroupBrotherResolutionBase resolution, SmallGroupBrotherInfo brotherInfo) {
            boolean left = resolution.getVoteMap().size() == brotherInfo.getMemberMap().size();
            boolean right = !resolution.getVoteMap().containsValue("-1");
            return Pair.of(left, right);
        }

        @Override
        public boolean canVoteSpecialCheck(long playerId, SmallGroupBrotherResolutionBase resolution) {
            return true;
        }
    },

    /**
     * 2、合并
     */
    merge(2) {
        @Override
        public boolean needRefreshJob() {
            //合并时已处理职位
            return false;
        }

        @Override
        protected void voteFinishSpecialDeal(SmallGroupBrotherInfo brotherInfo, SmallGroupBrotherResolutionBase resolution) {
            //合并
            SmallGroupBrotherResolutionMerge merge = (SmallGroupBrotherResolutionMerge) resolution;
            long antherId = merge.getAntherBrotherId(brotherInfo.getId());
            String redisKey = GsRedisKey.SmallGroup.small_group_info.getRedisKey(antherId);
            DistributedLockUtilManager.operate(() -> {
                SmallGroupBrotherInfo antherBrotherInfo = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupBrotherInfo.class, redisKey);

                SmallGroupBrotherInfo master = brotherInfo.getId() == merge.getMasterSmallGroupId() ? brotherInfo : antherBrotherInfo;
                SmallGroupBrotherInfo target = antherBrotherInfo.getId() == merge.getMasterSmallGroupId() ? brotherInfo : antherBrotherInfo;

                //合并职位调整特殊处理，主金兰在前，被合并在后
                List<SmallGroupMemberInfo> memberInfoList = new ArrayList<>();
                List<SmallGroupMemberInfo> temp = new ArrayList<>(master.getMemberMap().values());
                temp.sort((Comparator.comparingInt(o -> o.getJob().getSign())));
                memberInfoList.addAll(temp);
                temp = new ArrayList<>(target.getMemberMap().values());
                temp.sort((Comparator.comparingInt(o -> o.getJob().getSign())));
                memberInfoList.addAll(temp);

                SmallGroupJobEnum job = SmallGroupJobEnum.brother1;
                for (SmallGroupMemberInfo memberInfo : memberInfoList) {
                    memberInfo.setJob(job);
                    master.addMember(memberInfo);
                    job = job.next();
                }

                for (Map.Entry<Long, String> entry : target.getTitleMap().entrySet()) {
                    master.getTitleMap().put(entry.getKey(), master.getTitleMap().containsValue(entry.getValue()) ? entry.getValue() + "1" : entry.getValue());
                }

                Set<Long> playerIdList = new HashSet<>();
                playerIdList.addAll(master.getMemberList());
                playerIdList.addAll(target.getMemberList());

                for (long memberId : target.getMemberList()) {
                    SmallGroupHelper.smallGroupLeaveGroupDeal(memberId, target.getType(), target.getId());
                    SmallGroupHelper.smallGroupJoinDeal(memberId, master.getId());
                }

                //目标合并决议投票合并到主金兰
                SmallGroupBrotherResolutionMerge targetMerge = target.getMergeMap().get(merge.getId());
                SmallGroupBrotherResolutionMerge masterMerge = master.getMergeMap().get(merge.getId());
                masterMerge.getVoteMap().putAll(targetMerge.getVoteMap());
                masterMerge.setVoteResult(1);

                TLBase.getInstance().getRedisAssistant().setBean(GsRedisKey.SmallGroup.small_group_info.getRedisKey(master.getId()), master);
                TLBase.getInstance().getRedisAssistant().del(GsRedisKey.SmallGroup.small_group_info.getRedisKey(target.getId()));

                List<String> list = new ArrayList<>();
                for (long id : playerIdList) {
                    list.add(GsRedisKey.SmallGroup.player_small_group_id_info.getRedisKey(id));
                }

                DistributedLockUtilManager.operate(() -> {
                    for (String key : list) {
                        PlayerSmallGroupIdInfo groupIdInfo = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, key);
                        if(groupIdInfo == null){
                            groupIdInfo = new PlayerSmallGroupIdInfo();
                        }

                        groupIdInfo.setBrotherId(master.getId());
                        TLBase.getInstance().getRedisAssistant().setBean(key, groupIdInfo);
                    }
                }, list.toArray(new String[0]));

                ThreadPool.execute(new SmallGroupInfoSyncAsync(master));
            }, redisKey);
        }

        @Override
        protected Pair<Boolean, Boolean> voteFinishSpecialCheck(SmallGroupBrotherResolutionBase resolution, SmallGroupBrotherInfo brotherInfo) {

            boolean left = resolution.getVoteMap().size() == brotherInfo.getMemberMap().size();
            boolean right = !resolution.getVoteMap().containsValue("-1");

            if(resolution.getVoteMap().size() == brotherInfo.getMemberMap().size() && !resolution.getVoteMap().containsValue(-1)){
                //己方投票完成再检查另一方
                SmallGroupBrotherResolutionMerge merge = (SmallGroupBrotherResolutionMerge) resolution;
                long antherId = merge.getAntherBrotherId(brotherInfo.getId());
                SmallGroupBrotherInfo antherBrotherInfo = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupBrotherInfo.class, GsRedisKey.SmallGroup.small_group_info.getRedisKey(antherId));
                SmallGroupBrotherResolutionBase resolutionTemp = antherBrotherInfo.getResolutionMap().get(resolution.getId());
                if(resolutionTemp != null){
                    SmallGroupBrotherResolutionMerge mergeTemp = (SmallGroupBrotherResolutionMerge) resolutionTemp;
                    left = left && mergeTemp.getVoteMap().size() == antherBrotherInfo.getMemberMap().size();
                    right = right && !mergeTemp.getVoteMap().containsValue(-1);
                }
            }
            return Pair.of(left, right);
        }

        @Override
        protected boolean canVoteSpecialCheck(long playerId, SmallGroupBrotherResolutionBase resolution) {
            return true;
        }
    },

    /**
     * 3、改名
     */
    rename(3) {
        @Override
        protected boolean needRefreshJob() {
            return false;
        }

        @Override
        protected void voteFinishSpecialDeal(SmallGroupBrotherInfo brotherInfo, SmallGroupBrotherResolutionBase resolution) {
            SmallGroupBrotherResolutionRename rename = (SmallGroupBrotherResolutionRename) resolution;
            brotherInfo.setSmallGroupName(rename.getName());

            //群聊改名

        }

        @Override
        protected Pair<Boolean, Boolean> voteFinishSpecialCheck(SmallGroupBrotherResolutionBase resolution, SmallGroupBrotherInfo brotherInfo) {
            boolean left = resolution.getVoteMap().size() == brotherInfo.getMemberMap().size();
            boolean right = !resolution.getVoteMap().containsValue("-1");
            return Pair.of(left, right);
        }

        @Override
        protected boolean canVoteSpecialCheck(long playerId, SmallGroupBrotherResolutionBase resolution) {
            return true;
        }
    },

    /**
     * 4、请离
     */
    kick(4) {
        @Override
        protected boolean needRefreshJob() {
            return true;
        }

        @Override
        protected void voteFinishSpecialDeal(SmallGroupBrotherInfo brotherInfo, SmallGroupBrotherResolutionBase resolution) {
            SmallGroupBrotherResolutionKick kick = (SmallGroupBrotherResolutionKick) resolution;
            brotherInfo.removeMember(kick.getTargetPlayerId());

            String redisKey = GsRedisKey.SmallGroup.player_small_group_id_info.getRedisKey(kick.getTargetPlayerId());
            DistributedLockUtilManager.operate(() -> {
                PlayerSmallGroupIdInfo playerSmallGroupIdInfo = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, redisKey);
                playerSmallGroupIdInfo.setBrotherId(0);
                TLBase.getInstance().getRedisAssistant().setBean(redisKey, playerSmallGroupIdInfo);
            }, redisKey);

            SmallGroupHelper.smallGroupTaskDeal(brotherInfo.getMemberList(), brotherInfo.getType(), false);
        }

        @Override
        protected Pair<Boolean, Boolean> voteFinishSpecialCheck(SmallGroupBrotherResolutionBase resolution, SmallGroupBrotherInfo brotherInfo) {
            boolean left = resolution.getVoteMap().size() + 1 == brotherInfo.getMemberList().size();
            boolean right = !resolution.getVoteMap().containsValue("-1");
            return Pair.of(left, right);
        }

        @Override
        protected boolean canVoteSpecialCheck(long playerId, SmallGroupBrotherResolutionBase resolution) {
            SmallGroupBrotherResolutionKick kick = (SmallGroupBrotherResolutionKick) resolution;
            return playerId != kick.getTargetPlayerId();
        }
    },

    /**
     * 5、解散
     */
    disband(5) {
        @Override
        protected boolean needRefreshJob() {
            return false;
        }

        @Override
        protected void voteFinishSpecialDeal(SmallGroupBrotherInfo brotherInfo, SmallGroupBrotherResolutionBase resolution) {
            List<String> redisKeyList = new ArrayList<>();
            for (Long playerId : brotherInfo.getMemberList()) {
                redisKeyList.add(GsRedisKey.SmallGroup.player_small_group_id_info.getRedisKey(playerId));
            }

            DistributedLockUtilManager.operate(() -> {
                for (String redisKey : redisKeyList) {
                    PlayerSmallGroupIdInfo playerSmallGroupIdInfo = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, redisKey);
                    playerSmallGroupIdInfo.setBrotherId(0);
                    TLBase.getInstance().getRedisAssistant().setBean(redisKey, playerSmallGroupIdInfo);
                }
            }, redisKeyList.toArray(new String[0]));

            //处理小团体任务
            SmallGroupHelper.smallGroupTaskDeal(brotherInfo.getMemberMap().keySet(), SmallGroupTypeEnum.brother, false);

            //解散通知
            SmallGroupHelper.smallGroupDisbandNotify(brotherInfo);

            TLBase.getInstance().getRedisAssistant().del(GsRedisKey.SmallGroup.small_group_info.getRedisKey(brotherInfo.getId()));
        }

        @Override
        protected Pair<Boolean, Boolean> voteFinishSpecialCheck(SmallGroupBrotherResolutionBase resolution, SmallGroupBrotherInfo brotherInfo) {
            boolean left = resolution.getVoteMap().size() == brotherInfo.getMemberMap().size();
            boolean right = !resolution.getVoteMap().containsValue("-1");
            return Pair.of(left, right);
        }

        @Override
        protected boolean canVoteSpecialCheck(long playerId, SmallGroupBrotherResolutionBase resolution) {
            return true;
        }
    },
    ;

    private int type;

    SmallGroupBrotherResolutionEnum(int type) {
        this.type = type;
    }

    public static SmallGroupBrotherResolutionEnum getSmallGroupBrotherResolutionEnum(String type){
        return getSmallGroupBrotherResolutionEnum(Integer.parseInt(type));
    }

    public static SmallGroupBrotherResolutionEnum getSmallGroupBrotherResolutionEnum(int type){
        switch (type){
            case 1: return add;
            case 2: return merge;
            case 3: return rename;
            case 4: return kick;
            case 5: return disband;
            default : return null;
        }
    }

    public int getType() {
        return type;
    }

    /**
     * 投票完成处理
     * @param brotherInfo
     * @param resolution
     */
    public void voteFinishDeal(SmallGroupBrotherInfo brotherInfo, SmallGroupBrotherResolutionBase resolution){
        List<SmallGroupMemberInfo> list = new ArrayList<>(brotherInfo.getMemberMap().values());

        //先进行后置处理
        voteFinishSpecialDeal(brotherInfo, resolution);

        //再看是否刷新成员职位
        if(needRefreshJob()){
            SmallGroupHelper.refreshMemberJob(brotherInfo);
        }

        //删除发布结识
        if(needRefreshJob() || resolution.getSmallGroupBrotherResolutionEnum() == disband){
            for (SmallGroupMemberInfo bean : list) {
                MakeAcquaintancesHelper.dealMakeAcquaintances(1, bean.getPlayerId());
            }
        }
    }

    protected abstract boolean needRefreshJob();

    /**
     * 投票完成类型特殊处理
     * @param brotherInfo
     * @param resolution
     */
    protected abstract void voteFinishSpecialDeal(SmallGroupBrotherInfo brotherInfo, SmallGroupBrotherResolutionBase resolution);

    /**
     * 检查投票是否完成且通过
     * @param resolution
     * @param brotherInfo
     * @return
     */
    public Pair<Boolean, Boolean> voteFinishCheck(SmallGroupBrotherResolutionBase resolution, SmallGroupBrotherInfo brotherInfo){
        return voteFinishSpecialCheck(resolution, brotherInfo);
    }

    /**
     * 检查投票是否完成且类型特殊检查通过
     * @param resolution
     * @param brotherInfo
     * @return key:投票是否完成  value:投票结果
     */
    protected abstract Pair<Boolean, Boolean> voteFinishSpecialCheck(SmallGroupBrotherResolutionBase resolution, SmallGroupBrotherInfo brotherInfo);

    protected abstract boolean canVoteSpecialCheck(long playerId, SmallGroupBrotherResolutionBase resolution);

    /**
     * 检查能否投票
     * @param playerId
     * @param resolution
     * @return
     */
    public boolean canVoteCheck(long playerId, SmallGroupBrotherResolutionBase resolution){
        return !resolution.getVoteMap().containsKey(playerId) && canVoteSpecialCheck(playerId, resolution);
    }

}

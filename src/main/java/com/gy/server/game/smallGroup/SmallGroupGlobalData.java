package com.gy.server.game.smallGroup;

import com.gy.server.core.Configuration;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.game.global.GlobalData;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.mail.MailManager;
import com.gy.server.game.mail.MailType;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.smallGroup.bean.SmallGroupCreateSignInfo;
import com.gy.server.game.smallGroup.bean.SmallGroupInfoBase;
import com.gy.server.game.smallGroup.bean.SmallGroupInviteInfo;
import com.gy.server.game.smallGroup.bean.SmallGroupTypeEnum;
import com.gy.server.game.text.Text;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.serialize.base.RedisMapLongIntegerBean;
import com.ttlike.server.tl.baselib.serialize.smallGroup.SmallGroupGlobalDataDb;
import com.ttlike.server.tl.baselib.serialize.smallGroup.SmallGroupInviteInfoDb;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;


/**
 * 小团体-全局数据
 * <AUTHOR> 2024/12/12 17:19
 **/
public class SmallGroupGlobalData extends GlobalData {

    /**
     * 待离开map
     * key:玩家id     value:key:小团体id value:小团体类型
     */
    private Map<Long, ConcurrentHashMap<Long, Integer>> leaveMap = new ConcurrentHashMap<>();

    /**
     * 玩家id与小团体创建id（组队id）map
     * key:玩家id value:key:小团体类型 value:小团体创建标记信息
     */
    private Map<Long, ConcurrentHashMap<Integer, SmallGroupCreateSignInfo>> createSignInfoMap = new ConcurrentHashMap<>();

    /**
     * 小团体邀请map
     * key:被邀请人id   value:key:邀请人id + 类型  value:
     */
    private Map<Long, ConcurrentHashMap<String, SmallGroupInviteInfo>> inviteInfoMap = new ConcurrentHashMap<>();

    /**
     * 有决议正在进行的小团体id集合
     * 决议谁提出，放在谁所在服
     */
    private Set<Long> resolutionSmallGroupIdSet = new CopyOnWriteArraySet<>();

    @Override
    public void readFromPb(byte[] bytes) throws Exception {
        if(bytes == null){
            return;
        }

        SmallGroupGlobalDataDb db = PbUtilCompress.decode(SmallGroupGlobalDataDb.class, bytes);
        if(db != null){
            db.getLeaveMap().forEach((key, value) -> {
                leaveMap.computeIfAbsent(key, map -> new ConcurrentHashMap<>()).putAll(value.getMap());
            });
            db.getCreateSignInfoDbList().forEach(bean -> {
                createSignInfoMap.computeIfAbsent(bean.getPlayerId(), map -> new ConcurrentHashMap<>()).put(bean.getType(), new SmallGroupCreateSignInfo(bean));
            });
            db.getInviteInfoDbList().forEach(bean -> {
                inviteInfoMap.computeIfAbsent(bean.getTargetId(), map -> new ConcurrentHashMap<>()).put(bean.getInviterId() + "" + bean.getType(), new SmallGroupInviteInfo(bean));
            });
            resolutionSmallGroupIdSet.addAll(db.getResolutionSmallGroupIdSet());
        }
    }

    @Override
    public byte[] writeToPb() {
        SmallGroupGlobalDataDb db = new SmallGroupGlobalDataDb();
        leaveMap.forEach((key, value) -> {
            db.getLeaveMap().put(key, new RedisMapLongIntegerBean(value));
        });
        createSignInfoMap.values().forEach(bean -> {
            bean.values().forEach(beanTemp -> {
                db.getCreateSignInfoDbList().add(beanTemp.genDb());
            });
        });
        inviteInfoMap.values().forEach(bean -> {
            bean.values().forEach(beanTemp -> {
                db.getInviteInfoDbList().add(new SmallGroupInviteInfoDb(beanTemp.getType(), beanTemp.getTargetId(), beanTemp.getInviterId(), beanTemp.getInviterName(), beanTemp.getEndTime()));
            });
        });
        db.getResolutionSmallGroupIdSet().addAll(resolutionSmallGroupIdSet);
        return PbUtilCompress.encode(db);
    }

    public void addPlayerToSmallGroupCreateIdMap(long playerId, SmallGroupTypeEnum type, boolean isLeader, long createId){
        SmallGroupCreateSignInfo createSignInfo = new SmallGroupCreateSignInfo(playerId, type, isLeader, createId);
        addPlayerToSmallGroupCreateIdMap(createSignInfo);
    }

    public void addPlayerToSmallGroupCreateIdMap(SmallGroupCreateSignInfo createSignInfo){
        createSignInfoMap.computeIfAbsent(createSignInfo.getPlayerId(), map -> new ConcurrentHashMap<>()).put(createSignInfo.getType().getType(), createSignInfo);
    }

    /**
     * 玩家小团体创建后置处理
     * @param bean
     */
    public void smallGroupCrateFinishHandler(SmallGroupInfoBase bean) {
        Map<Integer, Set<Long>> serverPlayerIdMap = new HashMap<>();
        for (Long playerId : bean.getMemberMap().keySet()) {
            int serverId = Player.getServerId(playerId);
            serverPlayerIdMap.computeIfAbsent(serverId, set -> new HashSet<>()).add(playerId);
        }

        smallGroupCrateFinishHandler(serverPlayerIdMap, bean.getType().getType());
    }

    public void smallGroupCrateFinishHandler(Map<Integer, Set<Long>> serverPlayerIdMap, int type){
        for (Map.Entry<Integer, Set<Long>> entry : serverPlayerIdMap.entrySet()) {
            if(entry.getKey() == Configuration.serverId){
                smallGroupCrateFinishHandler(entry.getValue(), type);
            }else {
                ServerCommandRequest request = CommandRequests.newServerCommandRequest("SmallGroupRstCommandService.updatePlayerSmallGroupMap");
                TLBase.getInstance().getRpcUtil().sendToNode(ServerType.GAME, entry.getKey(), request, entry.getValue(), type);
            }
        }
    }

    public void smallGroupCrateFinishHandler(Set<Long> playerIdSet, int type){
        switch (SmallGroupTypeEnum.getSmallGroupTypeEnum(type)){
            case brother:{
                smallGroupBrotherCrateFinishHandler(playerIdSet);
                break;
            }
            case marry:{
                smallGroupMarryCrateFinishHandler(playerIdSet);
            }
        }
    }

    private void smallGroupMarryCrateFinishHandler(Set<Long> playerIdSet) {
        //TODO:
        playerIdSet.forEach(playerId -> {
            //发邮件

            //发称号
            int titleId = SmallGroupService.getSmallGroupConst().marryDesignation;
            ThreadPool.execute(() -> {
                Player player = PlayerManager.getPlayer(playerId);
                if(player != null){
                    player.getRoleModel().unlockDesignation(titleId, -1, BehaviorType.smallGroupBrother);
                }
            });
        });
    }

    public void smallGroupBrotherCrateFinishHandler(Set<Long> playerIdSet){
        playerIdSet.forEach(playerId -> {
            //发送金兰成功邮件
            MailType mailType = MailType.swornBrothersSuccess;
            MailManager.sendMail(
                    mailType,
                    playerId,
                    Text.genText(mailType.getTitleId()).build(),
                    Text.genText(mailType.getContentId()).build(),
                    ServerConstants.getCurrentTimeMillis()
            );

            //发称号
            int titleId = SmallGroupService.getSmallGroupConst().swornBrothersDesignation;
            ThreadPool.execute(() -> {
                Player player = PlayerManager.getPlayer(playerId);
                if(player != null){
                    player.getRoleModel().unlockDesignation(titleId, -1, BehaviorType.smallGroupBrother);
                }
            });
        });
    }

    /**
     * 删除玩家id和小团体创建id关联
     * @param playerIdSet
     */
    public void removeCreateSign(Set<Long> playerIdSet, int type) {
        for (Long playerId : playerIdSet) {
            removeCreateSign(playerId, type);
        }
    }

    public void removeCreateSign(long playerId, int type){
        if(createSignInfoMap.containsKey(playerId)){
            ConcurrentHashMap<Integer, SmallGroupCreateSignInfo> map = createSignInfoMap.get(playerId);
            map.remove(type);
            if(map.isEmpty()){
                createSignInfoMap.remove(playerId);
            }
        }
    }

    public void removeCreateSign(long playerId, Set<Integer> set) {
        for (int type : set) {
            removeCreateSign(playerId, type);
        }
    }

    /**
     * 增加玩家id和小团体创建id关联
     * @param set
     * @param type
     * @param leaderId
     * @param createId
     */
    public void addPlayerToSmallGroupCreateInfoMap(Collection<Long> set, int type, long leaderId, long createId) {
        addPlayerToSmallGroupCreateInfoMap(set, SmallGroupTypeEnum.getSmallGroupTypeEnum(type), leaderId, createId);
    }

    /**
     * 增加玩家id和小团体创建id关联
     * @param set
     * @param type
     * @param leaderId
     */
    public void addPlayerToSmallGroupCreateInfoMap(Collection<Long> set, SmallGroupTypeEnum type, long leaderId, long createId){
        for (long playerId : set) {
            addPlayerToSmallGroupCreateIdMap(playerId, type, playerId == leaderId, createId);
        }
    }

    /**
     * 待离开map删除数据
     * @param removeMap
     */
    public void removeLeaveMap(Map<Long, Set<Long>> removeMap) {
        for (Map.Entry<Long, Set<Long>> entry : removeMap.entrySet()) {
            if(leaveMap.containsKey(entry.getKey())){
                Map<Long, Integer> map = leaveMap.get(entry.getKey());
                entry.getValue().forEach(id -> map.remove(id));
                if(map.isEmpty()){
                    leaveMap.remove(entry.getKey());
                }
            }
        }
    }

    /**
     * 有决议正在进行的小团体id集合增加id
     * @param id
     */
    public void addResolutionSmallGroupId(long id) {
        resolutionSmallGroupIdSet.add(id);
    }

    public long getCreateSignInfoIdByPlayerId(long playerId, SmallGroupTypeEnum type){
        SmallGroupCreateSignInfo signInfo = getCreateSignInfo(playerId, type);
        return signInfo == null ? 0 : signInfo.getId();
    }

    public SmallGroupCreateSignInfo getCreateSignInfo(long playerId, SmallGroupTypeEnum type){
        if(createSignInfoMap.containsKey(playerId)){
            ConcurrentHashMap<Integer, SmallGroupCreateSignInfo> map = createSignInfoMap.get(playerId);
            if(map.containsKey(type.getType())){
                return map.get(type.getType());
            }
        }
        return null;
    }

    public Map<Long, ConcurrentHashMap<Integer, SmallGroupCreateSignInfo>> getCreateSignInfoMap() {
        return createSignInfoMap;
    }

    public void setCreateSignInfoMap(Map<Long, ConcurrentHashMap<Integer, SmallGroupCreateSignInfo>> createSignInfoMap) {
        this.createSignInfoMap = createSignInfoMap;
    }

    public Map<Long, ConcurrentHashMap<Long, Integer>> getLeaveMap() {
        return leaveMap;
    }

    public void setLeaveMap(Map<Long, ConcurrentHashMap<Long, Integer>> leaveMap) {
        this.leaveMap = leaveMap;
    }

    public Map<Long, ConcurrentHashMap<String, SmallGroupInviteInfo>> getInviteInfoMap() {
        return inviteInfoMap;
    }

    public void setInviteInfoMap(Map<Long, ConcurrentHashMap<String, SmallGroupInviteInfo>> inviteInfoMap) {
        this.inviteInfoMap = inviteInfoMap;
    }

    public Set<Long> getResolutionSmallGroupIdSet() {
        return resolutionSmallGroupIdSet;
    }

    public void setResolutionSmallGroupIdSet(Set<Long> resolutionSmallGroupIdSet) {
        this.resolutionSmallGroupIdSet = resolutionSmallGroupIdSet;
    }

}

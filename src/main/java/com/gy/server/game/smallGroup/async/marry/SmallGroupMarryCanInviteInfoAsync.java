package com.gy.server.game.smallGroup.async.marry;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.newFriend.FriendGlobalData;
import com.gy.server.game.newFriend.FriendHelper;
import com.gy.server.game.newFriend.bean.FriendInfo;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.game.smallGroup.SmallGroupGlobalData;
import com.gy.server.game.smallGroup.SmallGroupService;
import com.gy.server.game.smallGroup.bean.PlayerSmallGroupIdInfo;
import com.gy.server.game.smallGroup.bean.SmallGroupTypeEnum;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbFriend;
import com.gy.server.packet.PbProtocol;
import com.ttlike.server.tl.baselib.TLBase;

import java.util.Collection;
import java.util.Map;

/**
 * 获取情缘可邀请信息 异步处理
 * <AUTHOR> 2025/1/20 17:28
 **/
public class SmallGroupMarryCanInviteInfoAsync extends AsyncCall {

    private Player player;
    private long time;

    Map<Long, Integer> likabilityMap;
    private PbProtocol.SmallGroupMarryCanInviteInfoRst.Builder rst = PbProtocol.SmallGroupMarryCanInviteInfoRst.newBuilder().setResult(Text.genOkServerRstInfo());

    public SmallGroupMarryCanInviteInfoAsync(Player player, long time, Map<Long, Integer> likabilityMap) {
        this.player = player;
        this.time = time;
        this.likabilityMap = likabilityMap;
    }

    @Override
    public void execute() {
        player.send(PtCode.SMALL_SMALL_MARRY_CAN_INVITE_INFO_RST, rst.build(), time);
    }

    @Override
    public void asyncExecute() {
        Map<Long, Integer> map = likabilityMap;

        FriendGlobalData friendGlobalData = GlobalDataManager.getData(GlobalDataType.friend);
        Collection<FriendInfo> friendInfoList = friendGlobalData.getFriendInfo(player.getPlayerId()).getFriendInfos().values();
        for (FriendInfo bean : friendInfoList) {
            //检查好感度
            if(map.getOrDefault(bean.getFriendId(), 0) < SmallGroupService.getSmallGroupConst().marryLikabilityMixNum){
                continue;
            }

            //检查性别
            MiniGamePlayer miniGamePlayer = PlayerHelper.getMiniPlayer(bean.getFriendId());
            if(miniGamePlayer == null){
                continue;
            }

            //不一定是本服成员，但还是检测下
            SmallGroupGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroup);
            if(globalData.getCreateSignInfo(player.getPlayerId(), SmallGroupTypeEnum.marry) != null){
                continue;
            }

            String key = GsRedisKey.SmallGroup.player_small_group_id_info.getRedisKey(player.getPlayerId());
            PlayerSmallGroupIdInfo idInfo = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, key);
            if(idInfo != null && idInfo.getMarryId() != 0l){
                continue;
            }

            //TODO 临时注释
//            if(player.getSex() == miniGamePlayer.getGender()){
//                continue;
//            }

            PbFriend.FriendInfo friendInfo = FriendHelper.fillFriendInfo(bean.getFriendId(), map);
            if(friendInfo.getLastLoginTime() <= ServerConstants.getCurrentTimeMillis() && friendInfo.getLastLoginTime() > friendInfo.getLastLogoutTime()){
                rst.addFriendInfos(friendInfo);
            }
        }
    }
}

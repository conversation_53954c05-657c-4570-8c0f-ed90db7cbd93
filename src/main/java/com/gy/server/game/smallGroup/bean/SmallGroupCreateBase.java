package com.gy.server.game.smallGroup.bean;


import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;
import com.gy.server.core.Configuration;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbSmallGroup;
import com.gy.server.world.team.base.TeamMemberInfo;

import java.util.*;

/**
 * 小团体创建信息
 *
 * <AUTHOR> 2024/12/12 16:16
 **/
@ProtobufClass
public class SmallGroupCreateBase {

    /**
     * 组队id / 创建信息唯一id（发起人id）
     */
    @Protobuf(order = 1)
    private long id;

    /**
     * 小团体类型
     */
    @Protobuf(order = 2)
    private SmallGroupTypeEnum type;

    /**
     * 创建任务 -1时为无任务
     */
    @Protobuf(order = 3)
    private int createTaskId;

    /**
     * 队长服务器id / 发起人服务器
     * 创建过程在此处理
     */
    @Protobuf(order = 4)
    private int leaderServerId;

    /**
     * 队长id / 发起人id
     */
    @Protobuf(order = 5)
    private long leaderId;

    @Protobuf(order = 6)
    private long start = ServerConstants.getCurrentTimeMillis();

    /**
     * key:玩家id  value:玩家名字
     */
    @Protobuf(order = 7)
    private Map<Long, String> memberNameMap = new HashMap<>();

    public SmallGroupCreateBase() {
    }

    public SmallGroupCreateBase(SmallGroupTypeEnum type, int createTaskId) {
        this.id = Long.parseLong(Configuration.serverId + "" + type.getType() + "" + ServerConstants.getCurrentTimeMillis());
        this.type = type;
        this.createTaskId = createTaskId;
    }

    public SmallGroupInfoBase create() {
        return null;
    }

    public void addServerMember(int serverId, long playerId, String playerName, boolean isLeader){
        if(isLeader){
            this.leaderServerId = serverId;
            this.leaderId = playerId;
        }

        memberNameMap.put(playerId, playerName);
    }

    public void addServerMember(TeamMemberInfo memberInfo, long leaderId){
        addServerMember(memberInfo.getServerId(), memberInfo.getPlayerId(), memberInfo.getName(), memberInfo.getPlayerId() == leaderId);
    }

    public void addServerMember(List<TeamMemberInfo> memberInfoList, long leaderId) {
        for (TeamMemberInfo memberInfo : memberInfoList) {
            addServerMember(memberInfo, leaderId);
        }
    }

    public void writePb(PbSmallGroup.SmallGroupCreateInfo.Builder builder){

    }

    public void afterHandler() {

    }

    public PbProtocol.SmallGroupCreateInfoNotify genPb() {
        PbProtocol.SmallGroupCreateInfoNotify.Builder notify = PbProtocol.SmallGroupCreateInfoNotify.newBuilder();
        notify.setResult(Text.genOkServerRstInfo());

        PbSmallGroup.SmallGroupCreateInfo.Builder createInfo = PbSmallGroup.SmallGroupCreateInfo.newBuilder();
        //通用字段
        createInfo.setType(type.getType());
        createInfo.setCreateTaskId(createTaskId);
        createInfo.setStart(start);

        for (MiniGamePlayer bean : PlayerHelper.getMiniPlayers(memberNameMap.keySet())) {
            createInfo.addMinUser(bean.genMinMiniUser());
        }
        createInfo.setLeaderId(leaderId);
        createInfo.setId(id);

        writePb(createInfo);
        notify.setCreateInfo(createInfo);
        return notify.build();
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public SmallGroupTypeEnum getType() {
        return type;
    }

    public int getCreateTaskId() {
        return createTaskId;
    }

    public void setCreateTaskId(int createTaskId) {
        this.createTaskId = createTaskId;
    }

    public void setType(SmallGroupTypeEnum type) {
        this.type = type;
    }

    public long getStart() {
        return start;
    }

    public void setStart(long start) {
        this.start = start;
    }

    public int getLeaderServerId() {
        return leaderServerId;
    }

    public void setLeaderServerId(int leaderServerId) {
        this.leaderServerId = leaderServerId;
    }

    public long getLeaderId() {
        return leaderId;
    }

    public void setLeaderId(long leaderId) {
        this.leaderId = leaderId;
    }

    /**
     * 获取创建成员map
     * @return key:serverId value:玩家id集合
     */
    public Map<Integer, Set<Long>> getServerMemberMap() {
        Map<Integer, Set<Long>> map = new HashMap<>();
        for (Long playerId : getMemberList()) {
            int serverId = Player.getServerId(playerId);
            map.computeIfAbsent(serverId, set -> new HashSet<>()).add(playerId);
        }
        return map;
    }

    /**
     * 获取所有相关玩家map
     * @return key:serverId value:玩家id集合
     */
    public Map<Integer, Set<Long>> getServerAllMemberMap() {
        return getServerMemberMap();
    }

    public Map<Long, String> getMemberNameMap() {
        return memberNameMap;
    }

    public void setMemberNameMap(Map<Long, String> memberNameMap) {
        this.memberNameMap = memberNameMap;
    }

    public List<Long> getMemberList(){
        return new ArrayList<>(memberNameMap.keySet());
    }
}

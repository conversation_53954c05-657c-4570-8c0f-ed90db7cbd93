package com.gy.server.game.smallGroup.async.makeAcquaintances;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.bean.makeAcquaintances.MakeAcquaintancesApplyInfo;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbSmallGroup;
import com.ttlike.server.tl.baselib.TLBase;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 收到的结识申请列表
 * <AUTHOR> 2025/1/3 16:11
 **/
public class MakeAcquaintancesApplyListAsync extends AsyncCall {

    private Player player;
    private long time;

    private List<PbSmallGroup.MakeAcquaintancesApplyInfo> list = new ArrayList<>();

    public MakeAcquaintancesApplyListAsync(Player player, long time) {
        this.player = player;
        this.time = time;
    }

    @Override
    public void execute() {
        PbProtocol.MakeAcquaintancesApplyListRst.Builder rst = PbProtocol.MakeAcquaintancesApplyListRst.newBuilder().setResult(Text.genOkServerRstInfo());
        rst.addAllApplyInfo(list);
        player.send(PtCode.MAKE_ACQUAINTANCES_APPLY_LIST_RST, rst.build(), time);

    }

    @Override
    public void asyncExecute() {
        String redisKey = GsRedisKey.SmallGroup.make_acquaintances_apply_info.getRedisKey(player.getPlayerId());
        Map<String, MakeAcquaintancesApplyInfo> map = TLBase.getInstance().getRedisAssistant().hashGetAllBeans(redisKey, MakeAcquaintancesApplyInfo.class);

        map.values().forEach(bean -> list.add(bean.genMakeAcquaintancesApplyInfo()));
    }
}

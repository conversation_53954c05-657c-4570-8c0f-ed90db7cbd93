package com.gy.server.game.smallGroup;

import com.gy.server.common.distributedlock.DistributedLockUtilManager;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.log.CommonLogger;
import com.gy.server.core.thread.AbstractRunner;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.smallGroup.bean.*;
import com.gy.server.game.smallGroup.async.SmallGroupInfoSyncAsync;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherInfo;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherResolutionBase;
import com.gy.server.game.smallGroup.bean.brother.SmallGroupBrotherCreate;
import com.gy.server.game.smallGroup.bean.marry.SmallGroupMarryCreate;
import com.gy.server.game.smallGroup.bean.marry.SmallGroupMarryInfo;
import com.gy.server.packet.PbSmallGroup;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 小团体管理类
 *
 * <AUTHOR> 2024/12/18 9:41
 **/
public class SmallGroupManger extends AbstractRunner {

    private static final SmallGroupManger instance = new SmallGroupManger();

    public static SmallGroupManger getInstance(){
        return instance;
    }

    SmallGroupManger(){

    }

    @Override
    public String getRunnerName() {
        return "SmallGroupManger";
    }

    @Override
    protected void subRunnerExecute() throws Exception {
        try {
            checkCreate();
        }catch (Exception e){
            CommonLogger.error("SmallGroupManger checkCreate error:" + e.getMessage());
        }

        try {
            checkMemberLeave();
        }catch (Exception e){
            CommonLogger.error("SmallGroupManger checkMemberLeave error:" + e.getMessage());
        }

        try {
            checkInvite();
        }catch (Exception e){
            CommonLogger.error("SmallGroupManger checkInvite error:" + e.getMessage());
        }

        try {
            checkResolution();
        }catch (Exception e){
            CommonLogger.error("SmallGroupManger checkResolution error:" + e.getMessage());
        }

    }

    /**
     * 检查决议
     * 金兰独有
     */
    private void checkResolution() {
        SmallGroupGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroup);
        Set<Long> smallGroupIdSet = globalData.getResolutionSmallGroupIdSet();

        Set<Long> removeIdSet = new HashSet<>();
        for (Long id : smallGroupIdSet) {
            String redisKey = GsRedisKey.SmallGroup.small_group_info.getRedisKey(id);
            DistributedLockUtilManager.operate(() -> {
                SmallGroupBrotherInfo brotherInfo = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupBrotherInfo.class, redisKey);
                if(brotherInfo == null){
                    removeIdSet.add(id);
                    return;
                }

                List<SmallGroupBrotherResolutionBase> resolutionList = new ArrayList<>(brotherInfo.getResolutionMap().values());
                for (SmallGroupBrotherResolutionBase resolution : resolutionList) {
                    if(resolution.getEnd() < ServerConstants.getCurrentTimeMillis()){
                        brotherInfo.getResolutionMap().remove(resolution.getId());
                    }
                }

                if(brotherInfo.getResolutionMap().size() == 0){
                    removeIdSet.add(id);
                }
            }, redisKey);
        }

        if(!removeIdSet.isEmpty()){
            globalData.getResolutionSmallGroupIdSet().removeAll(removeIdSet);
        }
    }

    /**
     * 检查邀请是否过期
     */
    private void checkInvite() {
        SmallGroupGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroup);

        List<SmallGroupInviteInfo> infoList = new ArrayList<>();
        Map<Long, Map<String, SmallGroupInviteInfo>> inviteInfoMap = new HashMap<>(globalData.getInviteInfoMap());
        for (Map<String, SmallGroupInviteInfo> map : inviteInfoMap.values()) {
            for (SmallGroupInviteInfo bean : map.values()) {
                if(ServerConstants.getCurrentTimeMillis() > bean.getEndTime() && bean.getEndTime() != -1l){
                    infoList.add(bean);
                }
            }
        }

        for (SmallGroupInviteInfo bean : infoList) {
            Map<String, SmallGroupInviteInfo> map = globalData.getInviteInfoMap().get(bean.getTargetId());
            if(map == null){
                continue;
            }

            map.remove(bean.getInviterId());
            if(map.isEmpty()){
                globalData.getInviteInfoMap().remove(bean.getTargetId());
            }
        }
    }

    /**
     * 检查成员离开
     */
    private void checkMemberLeave() {
        SmallGroupGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroup);
        Map<Long, Set<Long>> removeMap = new HashMap<>();

        for (Map.Entry<Long, ConcurrentHashMap<Long, Integer>> entry : globalData.getLeaveMap().entrySet()) {
            for (Map.Entry<Long, Integer> tempEntry : entry.getValue().entrySet()) {
                switch (SmallGroupTypeEnum.getSmallGroupTypeEnum(tempEntry.getValue())){
                    case brother:{
                        checkMemberLeaveBrother(entry.getKey(), tempEntry.getKey(), removeMap);
                        break;
                    }
                    case marry:{
                        checkMemberLeaveMarry(entry.getKey(), tempEntry.getKey(), removeMap);
                        break;
                    }
                }
            }
        }

        if(!removeMap.isEmpty()){
            globalData.removeLeaveMap(removeMap);
        }
    }

    /**
     * 检查成员离开-情缘
     * @param playerId
     * @param id
     * @param removeMap
     */
    private void checkMemberLeaveMarry(long playerId, long id, Map<Long, Set<Long>> removeMap) {
        String redisKey = GsRedisKey.SmallGroup.small_group_info.getRedisKey(id);
        DistributedLockUtilManager.operate(() -> {
            SmallGroupMarryInfo marryInfo = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupMarryInfo.class, redisKey);
            if(marryInfo == null){
                removeMap.computeIfAbsent(playerId, set -> new HashSet<>()).add(id);
                return;
            }

            if(marryInfo.getPeriod() == 0l){
                return;
            }

            if(ServerConstants.getCurrentTimeMillis() >= marryInfo.getPeriod()){
                String redisKeyTemp = GsRedisKey.SmallGroup.player_small_group_id_info.getRedisKey(playerId);
                DistributedLockUtilManager.operate(() -> {
                    PlayerSmallGroupIdInfo groupIdInfo = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, redisKeyTemp);
                    groupIdInfo.setMarryId(0);
                    TLBase.getInstance().getRedisAssistant().setBean(redisKeyTemp, groupIdInfo);
                }, redisKeyTemp);

                removeMap.computeIfAbsent(playerId, set -> new HashSet<>()).add(id);

//                //同步
//                ThreadPool.execute(new SmallGroupInfoSyncAsync(marryInfo));
                marryInfo.removeAllMember();
                TLBase.getInstance().getRedisAssistant().del(redisKey);

            }else {
                removeMap.computeIfAbsent(playerId, set -> new HashSet<>()).add(id);
            }
        }, redisKey);
    }

    /**
     * 检查成员离开-金兰
     * @param playerId
     * @param id
     * @param removeMap
     */
    private void checkMemberLeaveBrother(long playerId, long id, Map<Long, Set<Long>> removeMap){
        String redisKey = GsRedisKey.SmallGroup.small_group_info.getRedisKey(id);
        DistributedLockUtilManager.operate(() -> {
            SmallGroupBrotherInfo brotherInfo = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupBrotherInfo.class, redisKey);
            if(brotherInfo == null){
                removeMap.computeIfAbsent(playerId, set -> new HashSet<>()).add(id);
                return;
            }

            Map<Long, Long> leaveMap = brotherInfo.getLeaveMap();
            if(leaveMap.containsKey(playerId)){
                if(ServerConstants.getCurrentTimeMillis() >= leaveMap.get(playerId)){
                    brotherInfo.removeMember(playerId);

                    //核对剩余成员称号
                    SmallGroupHelper.refreshMemberJob(brotherInfo);

                    TLBase.getInstance().getRedisAssistant().setBean(redisKey, brotherInfo);

                    String redisKeyTemp = GsRedisKey.SmallGroup.player_small_group_id_info.getRedisKey(playerId);
                    DistributedLockUtilManager.operate(() -> {
                        PlayerSmallGroupIdInfo groupIdInfo = TLBase.getInstance().getRedisAssistant().getBean(PlayerSmallGroupIdInfo.class, redisKeyTemp);
                        groupIdInfo.setBrotherId(0);
                        TLBase.getInstance().getRedisAssistant().setBean(redisKeyTemp, groupIdInfo);
                    }, redisKeyTemp);

                    removeMap.computeIfAbsent(playerId, set -> new HashSet<>()).add(id);

                    //同步
                    ThreadPool.execute(new SmallGroupInfoSyncAsync(brotherInfo));
                }
            }else {
                removeMap.computeIfAbsent(playerId, set -> new HashSet<>()).add(id);
            }
        }, redisKey);
    }

    /**
     * 检查小团体创建
     */
    private void checkCreate() {
        //key:服务器id     value:key:玩家id value:小团体类型集合
        Map<Integer, Map<Long, Set<Integer>>> removeMap = new HashMap<>();
        long now = ServerConstants.getCurrentTimeMillis();

        SmallGroupGlobalData globalData = GlobalDataManager.getData(GlobalDataType.smallGroup);
        for (ConcurrentHashMap<Integer, SmallGroupCreateSignInfo> map : globalData.getCreateSignInfoMap().values()) {
            for (SmallGroupCreateSignInfo bean : map.values()) {
                if(bean.getOverTime() < now && bean.getOverTime() != -1){
                    addRemoveMap(removeMap, bean.getType().getType(), bean.getPlayerId());
                    continue;
                }

                //只在队长所在服检查
                if(!bean.isLeader()){
                    continue;
                }

                String redisKey = GsRedisKey.SmallGroup.small_group_create.getRedisKey(bean.getId());
                DistributedLockUtilManager.operate(() -> {
                    switch (bean.getType()){
                        case brother:{
                            checkBrotherCreate(redisKey, removeMap, bean);
                            break;
                        }
                        case marry:{
                            checkMarryCreate(redisKey, removeMap, bean);
                            break;
                        }
                    }
                }, redisKey);
            }
        }

        if(!removeMap.isEmpty()){
            //TODO
            SmallGroupHelper.removeCreateDeal(removeMap, SmallGroupTypeEnum.brother);
        }
    }

    /**
     * 检查情缘创建
     * @param redisKey
     * @param removeMap
     * @param bean
     */
    private void checkMarryCreate(String redisKey, Map<Integer, Map<Long, Set<Integer>>> removeMap, SmallGroupCreateSignInfo bean) {
        SmallGroupMarryCreate marryCreate = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupMarryCreate.class, redisKey);
        if(marryCreate == null){
            addRemoveMap(removeMap, bean.getType().getType(), bean.getPlayerId());
            return;
        }

        if(ServerConstants.getCurrentTimeMillis() < marryCreate.getEndTime()){
            return;
        }
        switch (marryCreate.getNodeType()){
            case sgmWait:{
                if(marryCreate.getCruiseId() == 0){
                    break;
                }

                //判断双方是否在线，一方不在则巡游失败
                long now = ServerConstants.getCurrentTimeMillis();
                Map<Long, MiniGamePlayer> miniPlayersForMap = PlayerHelper.getMiniPlayersForMap(marryCreate.getMemberList());
                for (Long playerId : marryCreate.getMemberList()) {
                    MiniGamePlayer miniGamePlayer = miniPlayersForMap.get(playerId);
                    if(miniGamePlayer != null && now >= miniGamePlayer.getLastLoginTime() && miniGamePlayer.getLastLoginTime() >= miniGamePlayer.getLastLogoutTime()){

                    }else {
                        //不在线，巡游失败,未超时可再次申请
                        marryCreate.setNodeType(PbSmallGroup.SgmNodeType.sgmWait);
                        marryCreate.setInviteNum(0);
                        marryCreate.setCruiseResult(false);
                        TLBase.getInstance().getRedisAssistant().setBean(redisKey, marryCreate);
                        break;
                    }
                }

                if(!marryCreate.isCruiseResult()){
                    break;
                }

                marryCreate.sgmNodeTypeNext();
                TLBase.getInstance().getRedisAssistant().setBean(redisKey, marryCreate);
                break;
            }
            case sgmCruise:{
                if(!marryCreate.isCruiseResult()){
                    break;
                }

                marryCreate.sgmNodeTypeNext();
                TLBase.getInstance().getRedisAssistant().setBean(redisKey, marryCreate);
                break;
            }
            case sgmMarriageWait:
            case sgmEatingFeast:
            case sgmBlessing:{
                marryCreate.sgmNodeTypeNext();
                TLBase.getInstance().getRedisAssistant().setBean(redisKey, marryCreate);
                break;
            }
            case sgmChapel:{
                //超时时，默认成功
                marryCreate.sgmNodeTypeNext(false);
                SmallGroupHelper.crateMarryInfoDeal(marryCreate);
                TLBase.getInstance().getRedisAssistant().setBean(redisKey, marryCreate);
                break;
            }
            case sgmFinish:{
                addRemoveMap(removeMap, marryCreate);
                break;
            }
        }
    }

    /**
     * 检查金兰创建
     * @param redisKey
     * @param removeMap
     * @param bean
     */
    private void checkBrotherCreate(String redisKey, Map<Integer, Map<Long, Set<Integer>>> removeMap, SmallGroupCreateSignInfo bean){
        SmallGroupBrotherCreate brotherCreate = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupBrotherCreate.class, redisKey);
        if(brotherCreate == null){
            addRemoveMap(removeMap, bean.getType().getType(), bean.getPlayerId());
            return;
        }

        if(ServerConstants.getCurrentTimeMillis() < brotherCreate.getEndTime()){
            return;
        }

        switch (brotherCreate.getNodeType()){
            case sgbTask: {
                if(brotherCreate.getCreateTaskId() == -1){
                    brotherCreate.sgbNodeTypeNext();
                }
                break;
            }
            case sgbSort:{
                if(brotherCreate.getJobMap().size() == brotherCreate.getMemberNameMap().size()){
                    brotherCreate.sgbNodeTypeNext();
                    break;
                }

                //投票情况
                if(brotherCreate.getVoteMap().isEmpty()){
                    //无人投票，默认选择左侧未选中的第一个
                    List<Long> playerIdList = brotherCreate.getMemberList();
                    playerIdList.sort(Long::compare);

                    for (long playerId : playerIdList) {
                        if(!brotherCreate.getJobMap().containsKey(playerId)){
                            voteResultDeal(brotherCreate, playerId);
                            if(brotherCreate.getJobMap().size() == brotherCreate.getMemberNameMap().size()){
                                brotherCreate.sgbNodeTypeNext();
                                break;
                            }
                            break;
                        }
                    }
                }else {
                    //分析票型
                    Map<Long, Integer> voteNum = new HashMap<>();
                    for (Map.Entry<Long, Long> vote : brotherCreate.getVoteMap().entrySet()) {
                        voteNum.put(vote.getValue(), voteNum.getOrDefault(vote.getValue(), 0) + 1);
                    }

                    Map.Entry<Long, Integer> maxEntry = voteNum.entrySet().stream().max((Comparator.comparingInt(Map.Entry::getValue))).get();
                    voteNum.remove(maxEntry.getKey());
                    if(!voteNum.isEmpty()){
                        Map.Entry<Long, Integer> maxEntry2 = voteNum.entrySet().stream().max((Comparator.comparingInt(Map.Entry::getValue))).get();
                        if(maxEntry.getValue() == maxEntry2.getValue()){
                            //存在平票，重新开始
                            brotherCreate.getVoteMap().clear();
                            brotherCreate.refreshEndTime();
                            SmallGroupHelper.smallGroupCreateInfoNotify(brotherCreate);
                            break;
                        }
                    }

                    voteResultDeal(brotherCreate, maxEntry.getKey());
                    if(brotherCreate.getJobMap().size() == brotherCreate.getMemberNameMap().size()){
                        brotherCreate.sgbNodeTypeNext();
                        break;
                    }
                }
                break;
            }
            case sgbName:{
                //决定金兰称号期间超时，结义失败
                if(brotherCreate.getName() == null){
                    SmallGroupHelper.smallGroupBrotherCreateFailNotify(brotherCreate);
                    addRemoveMap(removeMap, brotherCreate);
                    brotherCreateBackCost(brotherCreate.getLeaderId());
                }else {
                    brotherCreate.sgbNodeTypeNext();
                }
                break;
            }
            case sgbMemberName:{
                //决定金兰成员称号期间超时，结义失败
                if(brotherCreate.getTitleMap().size() < brotherCreate.getMemberList().size()){
                    SmallGroupHelper.smallGroupBrotherCreateFailNotify(brotherCreate);
                    addRemoveMap(removeMap, brotherCreate);
                    brotherCreateBackCost(brotherCreate.getLeaderId());
                }else {
                    brotherCreate.sgbNodeTypeNext();
                }
                break;
            }
            case sgbSure:{
                //超时时，默认成功
                if(brotherCreate.getSureMemberIdSet().size() < brotherCreate.getServerMemberMap().size()){
                    List<Long> playerIdList = brotherCreate.getMemberList();
                    brotherCreate.getSureMemberIdSet().addAll(playerIdList);
                    brotherCreate.sgbNodeTypeNext();

                    SmallGroupInfoBase brotherInfo = brotherCreate.create();
                    SmallGroupHelper.smallGroupCrateFinishHandler(brotherInfo);

                    brotherCreate.afterHandler();
                }
                break;
            }
            case sgbFinish:{
                addRemoveMap(removeMap, brotherCreate);
                break;
            }
        }

        TLBase.getInstance().getRedisAssistant().setBean(redisKey, brotherCreate);
    }

    /**
     * 金兰创建失败回退消耗
     * 创建检查是在队长服务器进行的
     * @param playerId
     */
    public void brotherCreateBackCost(long playerId){
        Player player = PlayerManager.getOnlinePlayer(playerId);
        if(player == null){
            ThreadPool.execute(() -> {
                Player temp = PlayerManager.getPlayer(playerId);
                brotherCreateBackCost(temp);
            });
        }else {
            brotherCreateBackCost(player);
        }
    }

    public void brotherCreateBackCost(Player player){
        List<Reward> cost = Reward.templateCollectionToReward(SmallGroupService.getSmallGroupConst().swornBrothersConsume);
        Reward.add(cost, player, BehaviorType.smallGroupBrotherFail);
    }

    public void addRemoveMap(Map<Integer, Map<Long, Set<Integer>>> removeMap, SmallGroupCreateBase createBase){
        addRemoveMap(removeMap, createBase.getType().getType(), createBase.getServerMemberMap());
    }

    /**
     *
     * @param removeMap key:服务器id     value:key:玩家id value:小团体类型集合
     * @param type
     * @param serverMemberMap
     */
    public void addRemoveMap(Map<Integer, Map<Long, Set<Integer>>> removeMap, int type, Map<Integer, Set<Long>> serverMemberMap){
        for (Map.Entry<Integer, Set<Long>> entry : serverMemberMap.entrySet()) {
            Map<Long, Set<Integer>> mapTemp = removeMap.computeIfAbsent(entry.getKey(), map -> new HashMap<>());

            for (long playerId : entry.getValue()) {
                mapTemp.computeIfAbsent(playerId, set -> new HashSet<>()).add(type);
            }
        }
    }

    private void addRemoveMap(Map<Integer, Map<Long, Set<Integer>>> removeMap, int type, long playerId) {
        int serverId = Player.getServerId(playerId);
        Map<Long, Set<Integer>> mapTemp = removeMap.computeIfAbsent(serverId, map -> new HashMap<>());
        mapTemp.computeIfAbsent(playerId, set -> new HashSet<>()).add(type);
    }

    private void voteResultDeal(SmallGroupBrotherCreate brotherCreate, long targetPlayerId){
        brotherCreate.getJobMap().put(targetPlayerId, brotherCreate.getVoteJob().getSign());
        brotherCreate.setVoteJob(brotherCreate.getVoteJob().next());
        brotherCreate.getVoteMap().clear();

        //最后一人不投票
        List<Long> memberList = brotherCreate.getMemberList();
        if(brotherCreate.getJobMap().size() + 1 == memberList.size()){
            memberList.removeAll(brotherCreate.getJobMap().keySet());
            if(!memberList.isEmpty()){
                brotherCreate.getJobMap().put(memberList.get(0), brotherCreate.getVoteJob().getSign());
                brotherCreate.setVoteJob(null);
            }
        }
        brotherCreate.refreshEndTime();

        SmallGroupHelper.smallGroupCreateInfoNotify(brotherCreate);
    }

    @Override
    public long getRunnerInterval() {
        return 1000L;
    }

}

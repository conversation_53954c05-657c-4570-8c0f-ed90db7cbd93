package com.gy.server.game.smallGroup.async.makeAcquaintances;

import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.SmallGroupService;
import com.gy.server.game.smallGroup.bean.makeAcquaintances.SmallGroupMakeAcquaintancesInfo;
import com.gy.server.game.smallGroup.template.SmallGroupPlatformTemplate;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbSmallGroup;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.serialize.base.RedisListIntegerBean;

import java.util.*;

/**
 * 结识列表异步处理
 * <AUTHOR> 2025/1/2 18:21
 **/
public class MakeAcquaintancesListAsync extends AsyncCall {

    private Player player;
    private SmallGroupPlatformTemplate template;
    private List<Integer> titleList = new ArrayList<>();
    private int needNum;
    private int gender;
    private long time;

    private int text = Text.没有异常;
    List<PbSmallGroup.MakeAcquaintancesInfo> list = new ArrayList<>();

    public MakeAcquaintancesListAsync(Player player, PbProtocol.MakeAcquaintancesListReq req, long time) {
        this.player = player;
        this.template = SmallGroupService.getPlatformTemplateMap().get(req.getId());
        this.titleList.addAll(req.getTitleList());
        this.needNum = req.getNeedNum();
        this.gender = req.getGender();
        this.time = time;
    }

    @Override
    public void execute() {
        PbProtocol.MakeAcquaintancesListRst.Builder rst = PbProtocol.MakeAcquaintancesListRst.newBuilder().setResult(Text.genServerRstInfo(text));
        if(!list.isEmpty()){
            rst.addAllInfo(list);
        }
        rst.setNextTime(player.getPlayerSmallGroupModel().getNextTime().get());
        player.send(PtCode.MAKE_ACQUAINTANCES_LIST_RST, rst.build(), time);
    }

    @Override
    public void asyncExecute() {
        if(template == null){
            text = Text.参数异常;
            return;
        }

        Map<String, SmallGroupMakeAcquaintancesInfo> map = TLBase.getInstance().getRedisAssistant().hashGetAllBeans(GsRedisKey.SmallGroup.make_acquaintances_info.getRedisKey(template.id), SmallGroupMakeAcquaintancesInfo.class);
        for (SmallGroupMakeAcquaintancesInfo bean : map.values()) {
            Set<Integer> labelSet = new HashSet<>();
            for (RedisListIntegerBean temp: bean.getLabelMap().values()) {
                labelSet.addAll(temp.getList());
            }

            if(!titleList.isEmpty()){
                if(!labelSet.containsAll(titleList)){
                    continue;
                }
            }

            if(bean.getNeedNum() != needNum && needNum != 0){
                continue;
            }

            if(bean.getGender() != gender && gender != 0){
                continue;
            }

            list.add(bean.genMakeAcquaintancesInfo(player.getPlayerId()));
        }
    }
}

package com.gy.server.game.smallGroup.async.marry;

import com.gy.server.common.distributedlock.DistributedLockUtilManager;
import com.gy.server.common.redis.key.GsRedisKey;
import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.smallGroup.SmallGroupHelper;
import com.gy.server.game.smallGroup.SmallGroupService;
import com.gy.server.game.smallGroup.bean.marry.SmallGroupMarryCreate;
import com.gy.server.game.smallGroup.template.SmallGroupMarryBanquetTemplate;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbSmallGroup;
import com.ttlike.server.tl.baselib.TLBase;

import java.util.ArrayList;
import java.util.List;

/**
 * 情缘吃席 异步处理
 *
 * <AUTHOR> 2025/2/19 15:53
 **/
public class SmallGroupMarryEatFeastAsync extends AsyncCall {

    private Player player;
    private long createId;
    private int id;
    private long time;

    private List<Reward> rewardList = new ArrayList<>();
    private int text = Text.没有异常;

    public SmallGroupMarryEatFeastAsync(Player player, PbProtocol.SmallGroupMarryEatFeastReq req, long time) {
        this.player = player;
        this.createId = req.getCreateId();
        this.id = req.getId();
        this.time = time;
    }

    @Override
    public void execute() {
        PbProtocol.SmallGroupMarryEatFeastRst.Builder rst = PbProtocol.SmallGroupMarryEatFeastRst.newBuilder().setResult(Text.genServerRstInfo(text));
        if(!rewardList.isEmpty()){
            rst.addAllReward(Reward.writeCollectionToPb(rewardList));
        }
        player.send(PtCode.SMALL_GROUP_MARRY_EAT_FEAST_RST, rst.build(), time);
    }

    @Override
    public void asyncExecute() {
        SmallGroupMarryBanquetTemplate banquetTemplate = SmallGroupService.getMarryBanquetTemplateMap().get(id);
        if(banquetTemplate == null){
            text = Text.对应的模板数据找不到;
            return;
        }

        String redisKey = GsRedisKey.SmallGroup.small_group_create.getRedisKey(createId);
        DistributedLockUtilManager.operate(() -> {
            SmallGroupMarryCreate marryCreate = null;
            try {
                marryCreate = TLBase.getInstance().getRedisAssistant().getBean(SmallGroupMarryCreate.class, redisKey);
            }catch (Exception e){
                text = Text.参数异常;
                return;
            }

            if(marryCreate == null){
                text = Text.参数异常;
                return;
            }

            if(!marryCreate.getMemberList().contains(player.getPlayerId()) && !marryCreate.getInviteIdSet().contains(player.getPlayerId())){
                text = Text.当前未参加结情缘;
                return;
            }

            //核对节点
            if(marryCreate.getNodeType() != PbSmallGroup.SgmNodeType.sgmEatingFeast){
                text = Text.当前阶段不能吃菜肴;
                return;
            }

            //检查是否已经吃过菜肴
            if(marryCreate.hadEatBanquet(player.getPlayerId(), id)){
                text = Text.当前菜肴已经吃过;
                return;
            }

            marryCreate.addBanquet(player.getPlayerId(), id);
            TLBase.getInstance().getRedisAssistant().setBean(redisKey, marryCreate);
            rewardList.addAll(Reward.templateCollectionToReward(banquetTemplate.reward));

            SmallGroupHelper.smallGroupCreateInfoNotify(marryCreate);
        }, redisKey);
    }
}

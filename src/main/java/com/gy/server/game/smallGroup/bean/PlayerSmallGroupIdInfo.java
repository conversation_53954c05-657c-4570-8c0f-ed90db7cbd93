package com.gy.server.game.smallGroup.bean;

import com.baidu.bjf.remoting.protobuf.annotation.Protobuf;
import com.baidu.bjf.remoting.protobuf.annotation.ProtobufClass;
import com.gy.server.utils.CollectionUtil;

import java.util.*;

/**
 * 玩家加入小团体id信息
 *
 * <AUTHOR> 2024/12/28 17:36
 **/
@ProtobufClass
public class PlayerSmallGroupIdInfo {

    /**
     * 金兰id
     */
    @Protobuf(order = 1)
    private long brotherId;

    /**
     * 情缘id
     */
    @Protobuf(order = 2)
    private long marryId;

    /**
     * 玩家老师 师徒关系id
     */
    @Protobuf(order = 3)
    private long teacherId;

    /**
     * 玩家徒弟 师徒关系id列表
     */
    @Protobuf(order = 4)
    private List<Long> studentIdList = new ArrayList<>();

    public PlayerSmallGroupIdInfo() {
    }

    public Set<Long> getSmallGroupIdByType(SmallGroupTypeEnum smallGroupType) {
        Set<Long> set = new HashSet<>();
        if(smallGroupType != null){
            switch (smallGroupType){
                case brother: {
                    set.add(brotherId);
                    break;
                }
                case marry: {
                    set.add(marryId);
                    break;
                }
                case ts: {
                    set.add(teacherId);
                    set.addAll(studentIdList);
                    break;
                }
            }
        }
        return set;
    }

    public Map<SmallGroupTypeEnum, Set<Long>> getSmallGroupIdMap(int type){
        Map<SmallGroupTypeEnum, Set<Long>> map = new HashMap<>();
        for (SmallGroupTypeEnum bean : SmallGroupTypeEnum.values()) {
            if(bean.getType() == type || type == 0){
                Set<Long> set = getSmallGroupIdByType(bean);
                if(CollectionUtil.isNotEmpty(set)){
                    map.put(bean, set);
                }
            }
        }
        return map;
    }

    public long getBrotherId() {
        return brotherId;
    }

    public void setBrotherId(long brotherId) {
        this.brotherId = brotherId;
    }

    public long getMarryId() {
        return marryId;
    }

    public void setMarryId(long marryId) {
        this.marryId = marryId;
    }

    public long getTeacherId() {
        return teacherId;
    }

    public void setTeacherId(long teacherId) {
        this.teacherId = teacherId;
    }

    public List<Long> getStudentIdList() {
        return studentIdList;
    }

    public void setStudentIdList(List<Long> studentIdList) {
        this.studentIdList = studentIdList;
    }

    public boolean canCreate(SmallGroupTypeEnum type) {
        switch (type){
            case brother:{
                return brotherId == 0;
            }
            case marry:{
                return marryId == 0;
            }
            case ts:{
                //TODO 判断能否创建师徒关系
                //检查关系数量是否已达上限
                return false;
            }
            default: return true;
        }
    }
}

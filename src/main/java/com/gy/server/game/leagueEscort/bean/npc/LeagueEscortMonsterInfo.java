package com.gy.server.game.leagueEscort.bean.npc;

import com.gy.server.core.ServerConstants;
import com.gy.server.packet.PbCommons;
import com.gy.server.packet.PbLeague;
import com.gy.server.scene.map.SceneMapService;
import com.ttlike.server.tl.baselib.serialize.league.escort.LeagueEscortModelDb;
import org.apache.commons.lang3.tuple.Pair;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 怪物信息
 * @author: gbk
 * @date: 2024-10-22 16:22
 */
public class LeagueEscortMonsterInfo extends LeagueEscortNpcInfo {

    //下次进攻时间
    private long nextCanAtkTime;

    //正在攻打的玩家id
    private Set<Long> combatPlayerIds = new HashSet<>();

    //出生节点唯一id
    int createNodeId;

    public int getCreateNodeId() {
        return createNodeId;
    }

    public void setCreateNodeId(int createNodeId) {
        this.createNodeId = createNodeId;
    }

    public Set<Long> getCombatPlayerIds() {
        return combatPlayerIds;
    }

    public void setCombatPlayerIds(Set<Long> combatPlayerIds) {
        this.combatPlayerIds = combatPlayerIds;
    }

    public long getNextCanAtkTime() {
        return nextCanAtkTime;
    }

    public void setNextCanAtkTime(long nextCanAtkTime) {
        this.nextCanAtkTime = nextCanAtkTime;
    }

    public PbLeague.LeagueEscortNpcInfo genPb(){
        PbLeague.LeagueEscortNpcInfo.Builder npcInfo = PbLeague.LeagueEscortNpcInfo.newBuilder();
        npcInfo.setId(getId());
        npcInfo.setTempId(getTemplateId());
        npcInfo.setCurPos(SceneMapService.convertPosition(getAmplifyCurPos()));
        npcInfo.setNextPos(SceneMapService.convertPosition(amplifyPos(calNextPos(getCurPos(), getTargetPos(), getSpeed()))));
        npcInfo.setTargetPos(SceneMapService.convertPosition(getAmplifyCurPos()));
        npcInfo.setCanMoveTime(getCanMoveTime());
        npcInfo.setSpeed(getSpeed());
        npcInfo.setCanMove(!getStatus(AbsLeagueEscortUnit.can_not_move));
        Map<Integer, Pair<Long, Long>> surplusHps = getSurplusHps();
        PbCommons.KeyValueDb.Builder builder = PbCommons.KeyValueDb.newBuilder();
        for (Integer id : surplusHps.keySet()) {
            Pair<Long, Long> surplusHp = surplusHps.get(id);
            builder.clear();
            builder.setLongKey(surplusHp.getKey());
            builder.setLongValue(surplusHp.getValue());
            npcInfo.putSurplusHps(id, builder.build());
        }
        npcInfo.setIsCombat(!combatPlayerIds.isEmpty());
        npcInfo.setCreateNodeId(createNodeId);
        return npcInfo.build();
    }

    @Override
    public void subReadFromDb(LeagueEscortModelDb.LeagueEscortNpcInfoDb npcInfoDb) {
        nextCanAtkTime = npcInfoDb.getNextCanAtkTime();
        combatPlayerIds = npcInfoDb.getCombatPlayerIds();
        createNodeId = npcInfoDb.getCreateNodeId();
    }

    @Override
    public void subWriteToDb(LeagueEscortModelDb.LeagueEscortNpcInfoDb npcInfoDb) {
        npcInfoDb.setNextCanAtkTime(nextCanAtkTime);
        npcInfoDb.setCombatPlayerIds(combatPlayerIds);
        npcInfoDb.setCreateNodeId(createNodeId);
    }

    public LeagueEscortMonsterInfo copyNew(){
        LeagueEscortMonsterInfo newMonsterInfo = new LeagueEscortMonsterInfo();
        newMonsterInfo.setId(ServerConstants.allocateRuntimeIdentifier());
        newMonsterInfo.setTemplateId(getTemplateId());
        newMonsterInfo.setCurPos(getCurPos());
        newMonsterInfo.setTargetPos(getTargetPos());
        newMonsterInfo.setCreateNodeId(getCreateNodeId());
        return newMonsterInfo;
    }

}
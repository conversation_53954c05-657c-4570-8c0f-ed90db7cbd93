package com.gy.server.game.leagueEscort.template;

import com.gy.server.common.base.AbsTemplate;
import com.gy.server.utils.MathUtil;
import com.gy.server.utils.StringUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * GVE-帮派运镖NPC信息
 * @author: gbk
 * @date: 2024-10-14 18:07
 */
public class LeagueEscortNpcTemplate implements AbsTemplate {

    public int npcId;
    //速度
    public double speed;
    //攻击力
    public int atk;
    //攻速
    public int atkSpd;
    //攻击范围
    public int range;
    //关卡id
    public int battleCollectId;
    //宝箱Id
    public Map<Integer, Integer> chestWeights = new HashMap<>();
    //类型
    public int monsterType;
    //宝箱掉落概率
    public int dropProbability;
    //是否组怪
    public boolean isGroup;

    @Override
    public void readTxt(Map<String, String> map) {
        this.npcId = Integer.parseInt(map.get("npcId"));
        this.isGroup = Integer.parseInt(map.get("isGroup")) == 1;
        this.speed = Double.parseDouble(map.get("movementSpeed"));
        this.atk = Integer.parseInt(map.get("atk"));
        this.atkSpd = Integer.parseInt(map.get("atkSpd"));
        this.range = Integer.parseInt(map.get("attackRange"));
        this.battleCollectId = Integer.parseInt(map.get("battleCollectId"));
        int[] chests = StringUtil.splitToIntArray(map.get("chest"), "\\|");
        int[] weights = StringUtil.splitToIntArray(map.get("weight"), "\\|");
        for (int i = 0; i < chests.length; i++) {
            chestWeights.put(chests[i], weights[i]);
        }
        this.monsterType = Integer.parseInt(map.get("monsterType"));
        this.dropProbability = Integer.parseInt(map.get("dropProbability"));
    }

    public boolean isBoss(){
        return monsterType == 1;
    }

}
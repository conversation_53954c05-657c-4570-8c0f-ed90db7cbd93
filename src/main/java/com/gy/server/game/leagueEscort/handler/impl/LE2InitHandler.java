package com.gy.server.game.leagueEscort.handler.impl;

import com.gy.server.common.util.CommonUtils;
import com.gy.server.game.leagueEscort.LeagueEscortModel;
import com.gy.server.game.leagueEscort.LeagueEscortService;
import com.gy.server.game.leagueEscort.bean.LeagueEscortSyncEnum;
import com.gy.server.game.leagueEscort.bean.npc.LeagueEscortCartInfo;
import com.gy.server.game.leagueEscort.bean.npc.LeagueEscortNpcInfo;
import com.gy.server.game.leagueEscort.handler.AbsLEHandler;
import com.gy.server.game.leagueEscort.handler.LEStatusEnums;
import com.gy.server.game.leagueEscort.template.LeagueEscortConstant;
import com.gy.server.game.leagueEscort.template.LeagueEscortNodeTemplate;
import com.ttlike.server.tl.baselib.serialize.scene.ScenePosition3D;
import org.apache.commons.lang3.tuple.Pair;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 初始化状态
 * @author: gbk
 * @date: 2024-10-16 18:01
 */
public class LE2InitHandler extends AbsLEHandler {

    @Override
    public LEStatusEnums curStatus() {
        return LEStatusEnums.init;
    }

    @Override
    public boolean tryChangeStatus(LeagueEscortModel escortModel) {
        modifyStatus(escortModel, LEStatusEnums.tick);
        return true;
    }

    private static final int init_node = 1;

    @Override
    public void initDeal(LeagueEscortModel escortModel) {
        //初始化镖车
        LeagueEscortConstant constant = LeagueEscortService.getConstant();
        Map<Integer, LeagueEscortNodeTemplate> nodeTemplateMap = LeagueEscortService.getNodeMapByDiffId(escortModel.getDifficultyId());
        LeagueEscortNodeTemplate curNodeTemp = nodeTemplateMap.get(init_node);
        LeagueEscortNodeTemplate nextNodeTemp = nodeTemplateMap.get(curNodeTemp.node + 1);
        escortModel.setCurNode(init_node);
        LeagueEscortCartInfo cart = LeagueEscortNpcInfo.createCart(constant.npcID, curNodeTemp.nodePosition, nextNodeTemp.nodePosition);
        escortModel.getNpcInfos().put(cart.getId(), cart);
        //随机生成玩家位置
        ScenePosition3D curPos = cart.getCurPos();
        for(long playerId : escortModel.getSignUpPlayerIds()){
            escortModel.initPlayer(playerId, curPos);
        }
        List<Long> moveNpcIds = new ArrayList<>();
        moveNpcIds.add(cart.getId());
        //同步数据
        escortModel.sync(LeagueEscortSyncEnum.move_le, moveNpcIds, new ArrayList<>());
    }

    public static ScenePosition3D randomPoint(ScenePosition3D curPos, int radius){
        Pair<Double, Double> position = CommonUtils.randomCalPoint(Pair.of((double) curPos.getX(), (double) curPos.getZ()), radius);
        ScenePosition3D result = new ScenePosition3D();
        result.setX((float)(double)position.getKey());
        result.setZ((float)(double)position.getValue());
        return result;
    }

    @Override
    public void tickDeal(LeagueEscortModel escortModel) {

    }
}
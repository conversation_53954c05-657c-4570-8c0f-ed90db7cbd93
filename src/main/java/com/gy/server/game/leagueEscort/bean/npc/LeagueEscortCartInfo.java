package com.gy.server.game.leagueEscort.bean.npc;

import com.gy.server.game.leagueEscort.LeagueEscortModel;
import com.gy.server.packet.PbLeague;
import com.gy.server.scene.map.SceneMapService;
import com.ttlike.server.tl.baselib.serialize.league.escort.LeagueEscortModelDb;

import java.util.HashSet;
import java.util.Set;

/**
 * 镖车信息
 * @author: gbk
 * @date: 2024-10-22 16:22
 */
public class LeagueEscortCartInfo extends LeagueEscortNpcInfo {

    //镖车血量
    private int cartBlood;
    //攻击怪物id
    private Set<Long> attackNpcIds = new HashSet<>();

    public Set<Long> getAttackNpcIds() {
        return attackNpcIds;
    }

    public void setAttackNpcIds(Set<Long> attackNpcIds) {
        this.attackNpcIds = attackNpcIds;
    }

    public int getCartBlood() {
        return cartBlood;
    }

    public void setCartBlood(int cartBlood) {
        this.cartBlood = cartBlood;
    }

    @Override
    public void move(LeagueEscortModel escortModel) {
        //先移动
        super.move(escortModel);
        //校验马车距离
        escortModel.cartMoveCheck(1);
    }

    public PbLeague.LeagueEscortNpcInfo genPb(){
        PbLeague.LeagueEscortNpcInfo.Builder npcInfo = PbLeague.LeagueEscortNpcInfo.newBuilder();
        npcInfo.setId(getId());
        npcInfo.setTempId(getTemplateId());
        npcInfo.setCurPos(SceneMapService.convertPosition(getAmplifyCurPos()));
        npcInfo.setNextPos(SceneMapService.convertPosition(amplifyPos(calNextPos(getCurPos(), getTargetPos(), getSpeed()))));
        npcInfo.setTargetPos(SceneMapService.convertPosition(getAmplifyTargetPos()));
        npcInfo.setCanMoveTime(getCanMoveTime());
        npcInfo.setSpeed(getSpeed());
        npcInfo.setCartBlood(cartBlood);
        npcInfo.setCanMove(!getStatus(AbsLeagueEscortUnit.can_not_move));
        return npcInfo.build();
    }

    @Override
    public void subReadFromDb(LeagueEscortModelDb.LeagueEscortNpcInfoDb npcInfoDb) {
        cartBlood = npcInfoDb.getCartBlood();
        attackNpcIds = npcInfoDb.getAttackNpcIds();
    }

    @Override
    public void subWriteToDb(LeagueEscortModelDb.LeagueEscortNpcInfoDb npcInfoDb) {
        npcInfoDb.setCartBlood(cartBlood);
        npcInfoDb.setAttackNpcIds(attackNpcIds);
    }
}
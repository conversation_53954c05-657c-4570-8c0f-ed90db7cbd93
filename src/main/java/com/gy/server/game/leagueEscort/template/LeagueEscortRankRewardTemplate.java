package com.gy.server.game.leagueEscort.template;

import com.gy.server.common.base.AbsTemplate;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.utils.StringUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @author: gbk
 * @date: 2024-10-14 19:40
 */
public class LeagueEscortRankRewardTemplate implements AbsTemplate {

    //最小排名
    public int minRank;
    //最大排名
    public int maxRank;
    //奖励信息
    public List<RewardTemplate> rewardList = new ArrayList<>();

    @Override
    public void readTxt(Map<String, String> map) {
        int[] ints = StringUtil.splitToIntArray(map.get("Ranking"), "\\|");
        this.minRank = ints[0];
        this.maxRank = ints[1];
        this.rewardList = RewardTemplate.readListFromText(map.get("rewardStr"));
    }
}
package com.gy.server.game.leagueEscort.bean.npc;

import com.gy.server.common.util.CommonUtils;
import com.gy.server.game.leagueEscort.LeagueEscortModel;
import com.gy.server.game.leagueEscort.LeagueEscortService;
import com.gy.server.game.leagueEscort.template.LeagueEscortConstant;
import com.gy.server.game.log.GameLogger;
import com.gy.server.packet.PbCommons;
import com.gy.server.packet.PbLeague;
import com.gy.server.scene.map.SceneMapService;
import com.ttlike.server.tl.baselib.serialize.league.escort.LeagueEscortModelDb;
import com.ttlike.server.tl.baselib.serialize.scene.ScenePosition3D;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Map;

/**
 * GVE-帮派运镖玩家信息
 * @author: gbk
 * @date: 2024-10-16 14:42
 */
public class LeagueEscortPlayerInfo extends AbsLeagueEscortUnit {

    //唯一id
    private long playerId;
    //当前位置
    private ScenePosition3D curPos;
    //是否托管
    private boolean isAuto;
    //下次可恢复血量时间
    private long nextCanRecoverHpTime;

    //上次传送时间
    private long lastTransmitTime;
    //积分
    private long point;
    //下次增加积分时间
    private long nextAddPointTime;
    //战斗标记
    private boolean isCombat;
    private boolean hadCombat;

    public PbLeague.LeagueEscortPlayerInfo genPb(LeagueEscortNpcInfo cartNpc){
        PbLeague.LeagueEscortPlayerInfo.Builder playerInfoBuilder = PbLeague.LeagueEscortPlayerInfo.newBuilder();
        playerInfoBuilder.setPlayerId(playerId);
        playerInfoBuilder.setCurPos(SceneMapService.convertPosition(getAmplifyCurPos()));
        if(isAuto){
            playerInfoBuilder.setNextPos(SceneMapService.convertPosition(amplifyPos(calNextPos(getCurPos(), cartNpc.getCurPos(), getSpeed()))));
            playerInfoBuilder.setTargetPos(SceneMapService.convertPosition(cartNpc.getAmplifyCurPos()));
        }
        playerInfoBuilder.setIsAuto(isAuto);
        playerInfoBuilder.setLastTransmitTime(lastTransmitTime);
        playerInfoBuilder.setPoint(point);
        playerInfoBuilder.setSpeed(getSpeed());
        Map<Integer, Pair<Long, Long>> surplusHps = getSurplusHps();
        PbCommons.KeyValueDb.Builder builder = PbCommons.KeyValueDb.newBuilder();
        for (Integer id : surplusHps.keySet()) {
            Pair<Long, Long> surplusHp = surplusHps.get(id);
            builder.clear();
            builder.setLongKey(surplusHp.getKey());
            builder.setLongValue(surplusHp.getValue());
            playerInfoBuilder.putSurplusHps(id, builder.build());
        }
        playerInfoBuilder.setSeriousInjury(getStatus(AbsLeagueEscortUnit.serious_injury));
        playerInfoBuilder.setIsCombat(isCombat);
        playerInfoBuilder.setHadCombat(hadCombat);
        return playerInfoBuilder.build();
    }

    public void readFromDb(LeagueEscortModelDb.LeagueEscortPlayerInfoDb playerInfoDb){
        this.playerId = playerInfoDb.getPlayerId();
        this.curPos = playerInfoDb.getCurPos();
        this.isAuto = playerInfoDb.isAuto();
        this.nextCanRecoverHpTime = playerInfoDb.getNextCanRecoverHpTime();
        this.lastTransmitTime = playerInfoDb.getLastTransmitTime();
        this.point = playerInfoDb.getPoint();
        this.nextAddPointTime = playerInfoDb.getNextAddPointTime();
        this.isCombat = playerInfoDb.isCombat();
        this.hadCombat = playerInfoDb.isHadCombat();
    }

    public LeagueEscortModelDb.LeagueEscortPlayerInfoDb writeToDb(){
        LeagueEscortModelDb.LeagueEscortPlayerInfoDb playerInfoDb = new LeagueEscortModelDb.LeagueEscortPlayerInfoDb();
        playerInfoDb.setPlayerId(playerId);
        playerInfoDb.setCurPos(curPos);
        playerInfoDb.setAuto(isAuto);
        playerInfoDb.setNextCanRecoverHpTime(nextCanRecoverHpTime);
        playerInfoDb.setLastTransmitTime(lastTransmitTime);
        playerInfoDb.setPoint(point);
        playerInfoDb.setNextAddPointTime(nextAddPointTime);
        playerInfoDb.setCombat(isCombat);
        playerInfoDb.setHadCombat(hadCombat);
        return playerInfoDb;
    }

    public long getPoint() {
        return point;
    }

    /**
     * 增加积分
     */
    public void addPoint(long point){
        this.point += point;
    }

    public void setPoint(long point) {
        this.point = point;
    }

    public long getNextAddPointTime() {
        return nextAddPointTime;
    }

    public void setNextAddPointTime(long nextAddPointTime) {
        this.nextAddPointTime = nextAddPointTime;
    }

    public long getLastTransmitTime() {
        return lastTransmitTime;
    }

    public void setLastTransmitTime(long lastTransmitTime) {
        this.lastTransmitTime = lastTransmitTime;
    }

    public long getNextCanRecoverHpTime() {
        return nextCanRecoverHpTime;
    }

    public void setNextCanRecoverHpTime(long nextCanRecoverHpTime) {
        this.nextCanRecoverHpTime = nextCanRecoverHpTime;
    }

    public boolean isAuto() {
        return isAuto;
    }

    public void setAuto(boolean auto) {
        isAuto = auto;
    }

    public boolean isHadCombat() {
        return hadCombat;
    }

    public void setHadCombat(boolean hadCombat) {
        this.hadCombat = hadCombat;
    }

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public boolean isCombat() {
        return isCombat;
    }

    public void setCombat(boolean combat) {
        isCombat = combat;
        if(combat){
            hadCombat = true;
        }
    }

    @Override
    public double getSpeed() {
        //ai跟随移速
        return getBaseSpeed() + getAddedSpeed();
    }

    @Override
    public double getBaseSpeed() {
        LeagueEscortConstant constant = LeagueEscortService.getConstant();
        return constant.initPlayerSpeed;
    }

    public ScenePosition3D getCurPos() {
        return curPos;
    }

    public ScenePosition3D getAmplifyCurPos(){
        return amplifyPos(getCurPos());
    }

    public void setCurPos(ScenePosition3D curPos) {
        GameLogger.leagueEscort(String.format("le update Player Pos, playerId : %s, %s", playerId, curPos));
        this.curPos = curPos;
    }


    @Override
    public void move(LeagueEscortModel escortModel) {
        //玩家自动移动，需要跟随镖车，超出ai跟车范围就移动
        LeagueEscortConstant constant = LeagueEscortService.getConstant();
        //ai跟随移速
        double speed = getSpeed();

        LeagueEscortNpcInfo cartNpc = escortModel.getCartNpc();
        ScenePosition3D cartNpcPos = cartNpc.getCurPos();

        //距离大于跟随距离才移动
        double dis = CommonUtils.calDistance(Pair.of((double)curPos.getX(), (double)curPos.getZ()), Pair.of((double)cartNpcPos.getX(), (double)cartNpcPos.getZ()));
        if(dis > (double)constant.FollowingRadius){
            //计算下一目标点，移动
            ScenePosition3D oldPos = curPos;
            curPos = calNextPos(curPos, cartNpcPos, speed);
            GameLogger.leagueEscort(String.format("le Player auto move, playerId : %s, %s", playerId, curPos));
        }
    }
}
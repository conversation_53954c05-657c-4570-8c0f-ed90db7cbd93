package com.gy.server.game.leagueEscort;

import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.function.Function;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueDataInterface;
import com.gy.server.game.league.LeagueModel;
import com.gy.server.game.league.event.LeagueEvent;
import com.gy.server.game.league.event.LeagueEventHandler;
import com.gy.server.game.league.event.LeagueEventType;
import com.gy.server.game.leagueEscort.bean.LeagueEscortChestInfo;
import com.gy.server.game.leagueEscort.bean.LeagueEscortSyncEnum;
import com.gy.server.game.leagueEscort.bean.npc.*;
import com.gy.server.game.leagueEscort.handler.AbsLEHandler;
import com.gy.server.game.leagueEscort.handler.LEStatusEnums;
import com.gy.server.game.leagueEscort.handler.impl.LE2InitHandler;
import com.gy.server.game.leagueEscort.template.LeagueEscortConstant;
import com.gy.server.game.leagueEscort.template.LeagueEscortNodeTemplate;
import com.gy.server.game.leagueEscort.template.LeagueEscortNpcTemplate;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.redBearRevenge.bean.BearDefendBoxInfo;
import com.gy.server.packet.PbLeague;
import com.gy.server.packet.PbProtocol;
import com.gy.server.scene.map.bean.SceneMapType;
import com.gy.server.utils.StringUtil;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.serialize.league.LeagueBlobDb;
import com.ttlike.server.tl.baselib.serialize.league.escort.LeagueEscortModelDb;
import com.ttlike.server.tl.baselib.serialize.scene.ScenePosition3D;
import org.apache.commons.lang3.tuple.Pair;

import java.time.DayOfWeek;
import java.util.*;

import static com.gy.server.game.league.event.LeagueEventType.*;

/**
 * GVE-帮派运镖玩法
 * @author: gbk
 * @date: 2024-10-11 09:42
 */
public class LeagueEscortModel extends LeagueModel implements LeagueEventHandler, LeagueDataInterface {

    public LeagueEscortModel(League league) {
        super(league);
    }

    //难度
    private int difficulty;
    //难度id
    private int difficultyId;
    //开启时间
    private long openTime;
    //报名玩家id
    private Set<Long> signUpPlayerIds = new HashSet<>();
    //npc信息
    private Map<Long, LeagueEscortNpcInfo> npcInfos = new HashMap<>();
    //玩家信息
    private Map<Long, LeagueEscortPlayerInfo> playerInfos = new HashMap<>();
    //当前状态
    private LEStatusEnums status;
    //当前节点
    private int curNode;
    //宝箱信息
    private List<LeagueEscortChestInfo> chestInfos = new ArrayList<>();
    //上次tick时间
    private long lastTickTime;
    //结束标记
    private boolean isEnd;

    //内存数据
    private Set<Long> inScenePlayerIds = new HashSet<>();

    @Override
    public void init() {
        if(difficultyId <= 0){
            //初始化默认难度
            LeagueEscortConstant constant = LeagueEscortService.getConstant();
            difficulty = constant.initDifficulty;
            status = LEStatusEnums.before;
        }
    }

    /**
     * 开启前处理
     */
    public void openBefore(){
        //根据当前难度随机难度id
        List<Integer> difficultyIds = LeagueEscortService.getDiff2Ids().get(difficulty);
        Collections.shuffle(difficultyIds);
        difficultyId = difficultyIds.get(0);
    }

    public boolean isEnd() {
        return isEnd;
    }

    public void setEnd(boolean end) {
        isEnd = end;
    }

    public long getLastTickTime() {
        return lastTickTime;
    }

    public void setLastTickTime(long lastTickTime) {
        this.lastTickTime = lastTickTime;
    }

    public LEStatusEnums getStatus() {
        return status;
    }

    public void setStatus(LEStatusEnums status) {
        this.status = status;
    }

    public Map<Long, LeagueEscortPlayerInfo> getPlayerInfos() {
        return playerInfos;
    }

    public void setPlayerInfos(Map<Long, LeagueEscortPlayerInfo> playerInfos) {
        this.playerInfos = playerInfos;
    }

    public int getCurNode() {
        return curNode;
    }

    public void setCurNode(int curNode) {
        this.curNode = curNode;
    }

    public Set<Long> getInScenePlayerIds() {
        return inScenePlayerIds;
    }

    public void setInScenePlayerIds(Set<Long> inScenePlayerIds) {
        this.inScenePlayerIds = inScenePlayerIds;
    }

    public PbLeague.LeagueEscortInfo genPb(){
        PbLeague.LeagueEscortInfo.Builder builder = PbLeague.LeagueEscortInfo.newBuilder();
        builder.setDifficultyId(difficultyId);
        builder.setDifficulty(difficulty);
        builder.setOpenTime(openTime);
        builder.addAllSignUpPlayerIds(signUpPlayerIds);
        for (LeagueEscortNpcInfo npcInfo : npcInfos.values()) {
            if(npcInfo instanceof LeagueEscortMonsterInfo){
                builder.addNpcInfos(((LeagueEscortMonsterInfo)npcInfo).genPb());
            }else{
                builder.addNpcInfos(((LeagueEscortCartInfo)npcInfo).genPb());
            }
        }
        LeagueEscortCartInfo cartNpc = getCartNpc();
        for (LeagueEscortPlayerInfo playerInfo : playerInfos.values()) {
            builder.addPlayerInfos(playerInfo.genPb(cartNpc));
        }
        builder.setCurNode(curNode);
        builder.setIsEnd(isEnd);
        for (LeagueEscortChestInfo chestInfo : chestInfos) {
            builder.addChestInfos(chestInfo.genPb());
        }
        return builder.build();
    }

    public LeagueEscortCartInfo getCartNpc(){
        LeagueEscortConstant constant = LeagueEscortService.getConstant();
        for (LeagueEscortNpcInfo npcInfo : npcInfos.values()) {
            if(constant.npcID == npcInfo.getTemplateId()){
                return (LeagueEscortCartInfo)npcInfo;
            }
        }
        return null;
    }

    public void syncAll(boolean onlyScenePlayer){
        League league = getLeague();
        PbProtocol.LeagueEscortInfoSync.Builder builder = PbProtocol.LeagueEscortInfoSync.newBuilder();
        builder.setInfo(genPb());
        league.notify2AllOnlineMember(PtCode.LEAGUE_ESCORT_INFO_SYNC, builder.build(), onlyScenePlayer);
    }

    public void sync(LeagueEscortSyncEnum syncEnum, Object... params){
        sync(true, syncEnum, params);
    }

    public void sync(boolean onlyScenePlayer, LeagueEscortSyncEnum syncEnum, Object... params){
        PbProtocol.LeagueEscortSingleInfoSync.Builder builder = PbProtocol.LeagueEscortSingleInfoSync.newBuilder();
        syncEnum.fillMsg(builder, this, params);
        League league = getLeague();
        if(onlyScenePlayer){
            league.notify2Member(inScenePlayerIds, PtCode.LEAGUE_ESCORT_SINGLE_INFO_SYNC, builder.build());
        }else{
            league.notify2Member(league.getAllMember(), PtCode.LEAGUE_ESCORT_SINGLE_INFO_SYNC, builder.build());
        }
    }

    public int getDifficultyId() {
        return difficultyId;
    }

    public int getDifficulty() {
        return difficulty;
    }

    /**
     * 移动到当前节点的终点
     */
    public boolean move2Focus(){
        Map<Integer, LeagueEscortNodeTemplate> nodeTemplateMap = LeagueEscortService.getNodeMapByDiffId(getDifficultyId());
        LeagueEscortNodeTemplate nodeTemplate = nodeTemplateMap.get(getCurNode() + 1);
        LeagueEscortNpcInfo cartNpc = getCartNpc();
        if(Objects.nonNull(nodeTemplate)){
            return nodeTemplate.nodePosition.equals(cartNpc.getCurPos());
        }
        return true;
    }

    public void initPlayer(long playerId){
        LeagueEscortCartInfo cart = getCartNpc();
        if(Objects.nonNull(cart)){
            ScenePosition3D curPos = cart.getCurPos();
            initPlayer(playerId, curPos);
        }
    }

    public void initPlayer(long playerId, ScenePosition3D curPos){
        LeagueEscortConstant constant = LeagueEscortService.getConstant();

        LeagueEscortPlayerInfo playerInfo = new LeagueEscortPlayerInfo();
        playerInfo.setPlayerId(playerId);
        ScenePosition3D position3D = LE2InitHandler.randomPoint(curPos, constant.GeneratingRadius);
        playerInfo.setCurPos(position3D);
        getPlayerInfos().put(playerInfo.getPlayerId(), playerInfo);
    }

    public void reset(){
        Set<Long> playPlayerIds = new HashSet<>();
        for (LeagueEscortChestInfo chestInfo : chestInfos) {
            playPlayerIds.addAll(chestInfo.getReceivePlayerIds());
        }
        League.addPlayerPlayTimes(playPlayerIds, Function.leagueEscort);
        this.difficultyId = 0;
        this.openTime = -1;
        this.signUpPlayerIds.clear();
        this.inScenePlayerIds.clear();
        this.npcInfos.clear();
        this.playerInfos.clear();
        status = LEStatusEnums.before;
        this.curNode = 0;
        this.chestInfos.clear();
        this.isEnd = false;
    }

    public void setDifficultyId(int difficultyId) {
        this.difficultyId = difficultyId;
    }

    public long getOpenTime() {
        return openTime;
    }

    public void setOpenTime(long openTime) {
        this.openTime = openTime;
    }

    public Set<Long> getSignUpPlayerIds() {
        return signUpPlayerIds;
    }

    public void setSignUpPlayerIds(Set<Long> signUpPlayerIds) {
        this.signUpPlayerIds = signUpPlayerIds;
    }

    public Map<Long, LeagueEscortNpcInfo> getNpcInfos() {
        return npcInfos;
    }

    public LeagueEscortMonsterInfo getMonsterNpcInfo(long npcId){
        if(!npcInfos.containsKey(npcId)){
            return null;
        }
        return (LeagueEscortMonsterInfo)npcInfos.get(npcId);
    }

    public void setNpcInfos(Map<Long, LeagueEscortNpcInfo> npcInfos) {
        this.npcInfos = npcInfos;
    }

    public List<LeagueEscortChestInfo> getChestInfos() {
        return chestInfos;
    }

    public void setChestInfos(List<LeagueEscortChestInfo> chestInfos) {
        this.chestInfos = chestInfos;
    }

    @Override
    protected void loadData(LeagueBlobDb leagueBlobDb) {
        LeagueEscortModelDb escortModelDb = leagueBlobDb.getEscortModelDb();
        if(Objects.nonNull(escortModelDb)){
            this.difficulty = escortModelDb.getDifficulty();
            this.difficultyId = escortModelDb.getDifficultyId();
            this.openTime = escortModelDb.getOpenTime();
            this.signUpPlayerIds = escortModelDb.getSignUpPlayerIds();
            LeagueEscortConstant constant = LeagueEscortService.getConstant();
            for (LeagueEscortModelDb.LeagueEscortNpcInfoDb npcInfoDb : escortModelDb.getNpcInfos()) {
                LeagueEscortNpcInfo npcInfo;
                if(npcInfoDb.getTemplateId() == constant.npcID){
                    npcInfo = new LeagueEscortCartInfo();
                }else{
                    npcInfo = new LeagueEscortMonsterInfo();
                }
                npcInfo.readFromDb(npcInfoDb);
                npcInfos.put(npcInfo.getId(), npcInfo);
            }
            for (LeagueEscortModelDb.LeagueEscortPlayerInfoDb playerInfoDb : escortModelDb.getPlayerInfos()) {
                LeagueEscortPlayerInfo playerInfo = new LeagueEscortPlayerInfo();
                playerInfo.readFromDb(playerInfoDb);
                playerInfos.put(playerInfo.getPlayerId(), playerInfo);
            }

            this.status = LEStatusEnums.valueOf(escortModelDb.getStatus());
            this.curNode = escortModelDb.getCurNode();
            for (LeagueEscortModelDb.LeagueEscortChestInfoDb chestInfoDb : escortModelDb.getChestInfos()) {
                LeagueEscortChestInfo chestInfo = new LeagueEscortChestInfo();
                chestInfo.readFromDb(chestInfoDb);
                this.chestInfos.add(chestInfo);
            }
            this.lastTickTime = escortModelDb.getLastTickTime();
            this.isEnd = escortModelDb.isEnd();
        }
    }

    @Override
    protected void saveData(LeagueBlobDb leagueBlobDb) {
        LeagueEscortModelDb escortModelDb = new LeagueEscortModelDb();
        escortModelDb.setDifficulty(difficulty);
        escortModelDb.setDifficultyId(difficultyId);
        escortModelDb.setOpenTime(openTime);
        escortModelDb.getSignUpPlayerIds().addAll(signUpPlayerIds);
        for (LeagueEscortNpcInfo npcInfo : npcInfos.values()) {
            escortModelDb.getNpcInfos().add(npcInfo.writeToDb());
        }
        for (LeagueEscortPlayerInfo playerInfo : playerInfos.values()) {
            escortModelDb.getPlayerInfos().add(playerInfo.writeToDb());
        }
        escortModelDb.setStatus(status.name());
        escortModelDb.setCurNode(curNode);
        for (LeagueEscortChestInfo chestInfo : chestInfos) {
            escortModelDb.getChestInfos().add(chestInfo.writeToDb());
        }
        escortModelDb.setLastTickTime(lastTickTime);
        escortModelDb.setEnd(isEnd);
        leagueBlobDb.setEscortModelDb(escortModelDb);
    }

    @Override
    public LeagueEventType[] getEventTypes() {
        return new LeagueEventType[]{enterScene, moveScene, day5Refresh};
    }

    @Override
    public void handle(LeagueEvent event) {
        switch (event.getEventType()) {
            case enterScene: {
                // 同步数据
                if(openTime > 0){
                    Long memberId = event.getParam(0);
//                    if (PlayerManager.isOnline(memberId)) {
//                        Player onlinePlayer = PlayerManager.getOnlinePlayer(memberId);
//                        if(Objects.nonNull(onlinePlayer)){
//                            PbProtocol.LeagueEscortInfoSync.Builder builder = PbProtocol.LeagueEscortInfoSync.newBuilder();
//                            builder.setInfo(genPb());
//                            onlinePlayer.send(PtCode.LEAGUE_ESCORT_INFO_SYNC, builder.build());
//                        }
//                    }
                }
                break;
            }

            case moveScene:{
                long playerId = event.getParam(0);
                SceneMapType mapType = event.getParam(1);
                ScenePosition3D position = event.getParam(2);
                if(openTime > 0 && mapType == SceneMapType.leagueEscort){
                    playerMove(playerId, position);

                }
                break;
            }
            case day5Refresh:{
                if(ServerConstants.getCurrentTimeLocalDateTime().getDayOfWeek() == DayOfWeek.MONDAY){
                    reset();
                }
                break;
            }
        }
    }

    /**
     * 玩家主动移动处理
     */
    public void playerMove(long playerId, ScenePosition3D position){
        //更新位置
        LeagueEscortPlayerInfo playerInfo = getPlayerInfos().get(playerId);
        playerInfo.setCurPos(position);
        LeagueEscortConstant constant = LeagueEscortService.getConstant();

        //检查玩家位置相关
        LeagueEscortCartInfo cartNpc = getCartNpc();
        ScenePosition3D cartNpcPos = cartNpc.getCurPos();
        ScenePosition3D playerCurPos = playerInfo.getCurPos();
        double dis = CommonUtils.calDistance(Pair.of((double) cartNpcPos.getX(), (double) cartNpcPos.getZ()), Pair.of((double) playerCurPos.getX(), (double) playerCurPos.getZ()));
        LeagueEscortNpcTemplate cartNpcTemplate = cartNpc.getTemplate();
        if(dis > cartNpcTemplate.range){
            //出圈
            //取消回血标记
            if(playerInfo.getStatus(AbsLeagueEscortUnit.player_blood_add)){
                playerInfo.setStatus(AbsLeagueEscortUnit.player_blood_add, false);
            }
            if(playerInfo.getStatus(AbsLeagueEscortUnit.add_point)){
                playerInfo.setStatus(AbsLeagueEscortUnit.add_point, false);
            }

            cartMoveCheck(1);

            cartAndMonsterSpeedCheck();
        }else if (dis <= cartNpcTemplate.range){
            //进圈
            //增加回血标记
            if(!playerInfo.getStatus(AbsLeagueEscortUnit.player_blood_add)){
                playerInfo.setStatus(AbsLeagueEscortUnit.player_blood_add, true);
                playerInfo.setNextCanRecoverHpTime(ServerConstants.getCurrentTimeMillis() + constant.playerRecoverRate);
            }

            //积分增加标记
            if(!playerInfo.getStatus(AbsLeagueEscortUnit.add_point)){
                playerInfo.setStatus(AbsLeagueEscortUnit.add_point, true);
                int[] ints = StringUtil.splitToIntArray(constant.BonusPoint, "\\|");
                playerInfo.setNextAddPointTime(ServerConstants.getCurrentTimeMillis() + ints[0] * DateTimeUtil.MillisOfSecond);
            }

            cartMoveCheck(1);

            cartAndMonsterSpeedCheck();
        }

    }

    /**
     * 马车移动检测
     */
    public void cartMoveCheck(int type){
        //1.检查镖车圈内是否还有玩家，如果没有玩家，镖车禁止移动
        //2.检查是否有怪物正在攻击镖车
        //3.检查是否有boss怪
        boolean canMove = true;
        LeagueEscortCartInfo cartNpc = getCartNpc();
        check:{
            //获取当前镖车位置
            ScenePosition3D cartNpcPos = cartNpc.getCurPos();
            LeagueEscortNpcTemplate cartNpcTemplate = cartNpc.getTemplate();
            //检查圈里是否有玩家
            int inPlayerSize = 0;
            for(LeagueEscortPlayerInfo playerInfo : getPlayerInfos().values()){
                ScenePosition3D playerCurPos = playerInfo.getCurPos();
                double dis = CommonUtils.calDistance(Pair.of((double) cartNpcPos.getX(), (double) cartNpcPos.getZ()), Pair.of((double) playerCurPos.getX(), (double) playerCurPos.getZ()));
                if(dis <= cartNpcTemplate.range){
                    inPlayerSize++;
                }
//                if(playerInfo.getStatus(AbsLeagueEscortUnit.player_blood_add)){
//                }
            }
            if(inPlayerSize == 0){
                //圈里没人，禁止移动
                canMove = false;
                break check;
            }

            //检查是否有怪物正在打马车
            for (LeagueEscortNpcInfo monsterNpcInfo : getNpcInfos().values()) {
                //检查是否还有boss怪物
                LeagueEscortNpcTemplate template = monsterNpcInfo.getTemplate();
                if(template.isBoss()){
                    //boss存活，禁止移动
                    canMove = false;
                    break check;
                }

                //战斗中的怪物不计算追到标记
                if(monsterNpcInfo.getId() == cartNpc.getId() || monsterNpcInfo.getStatus(AbsLeagueEscortUnit.monster_battle)){
                    continue;
                }
                ScenePosition3D npcNpcPos = monsterNpcInfo.getCurPos();
                double dis = CommonUtils.calDistance(Pair.of((double) cartNpcPos.getX(), (double) cartNpcPos.getZ()), Pair.of((double) npcNpcPos.getX(), (double) npcNpcPos.getZ()));
                if(template.range >= dis){
                    //有怪物正在打马车，禁止移动
                    canMove = false;
                    break check;
                }
            }

        }
        if(canMove && cartNpc.getStatus(AbsLeagueEscortUnit.can_not_move)){
            //恢复移动
            if(type == 1){
                AbsLEHandler.log("cart npc restore move : 圈内玩家");
            }else if(type == 2){
                AbsLEHandler.log("cart npc restore move : 怪物攻击镖车");
            }else{
                AbsLEHandler.log("cart npc restore move : boss怪");
            }
            cartNpc.setStatus(AbsLeagueEscortUnit.can_not_move, false);

            if(cartNpc.getCanMoveTime() < ServerConstants.getCurrentTimeMillis()){
                LeagueEscortConstant constant = LeagueEscortService.getConstant();
                long addTime = 0;
                if(type == 1){
                    addTime = constant.nobodyStops * DateTimeUtil.MillisOfSecond;
                }else if(type == 2){
                    addTime = constant.bossPullUp * DateTimeUtil.MillisOfSecond;
                }else if(type == 3){
                    addTime = constant.moveOn * DateTimeUtil.MillisOfSecond;
                }
                cartNpc.setCanMoveTime(ServerConstants.getCurrentTimeMillis() + addTime);

                sync(LeagueEscortSyncEnum.cart_can_move);
            }
        }else if(!canMove && !cartNpc.getStatus(AbsLeagueEscortUnit.can_not_move)){

            if(type == 1){
                AbsLEHandler.log("cart npc none move : 圈内玩家");
            }else if(type == 2){
                AbsLEHandler.log("cart npc none move : 怪物攻击镖车");
            }else{
                AbsLEHandler.log("cart npc none move : boss怪");
            }
            //禁止移动
            cartNpc.setStatus(AbsLeagueEscortUnit.can_not_move, true);

            sync(LeagueEscortSyncEnum.cart_non_move);
        }

    }

    /**
     * 检查镖车和马车速度 （玩家主动进圈或者出圈会检查）
     */
    private void cartAndMonsterSpeedCheck(){
        //计算在圈内的玩家id数量
        LeagueEscortCartInfo cartNpc = getCartNpc();
        ScenePosition3D cartNpcPos = cartNpc.getCurPos();
        LeagueEscortNpcTemplate cartNpcTemplate = cartNpc.getTemplate();
        //在圈内玩家Id
        Set<Long> inPlayerIds = new HashSet<>();
        for(LeagueEscortPlayerInfo playerInfo : getPlayerInfos().values()){
            if(!playerInfo.getStatus(AbsLeagueEscortUnit.player_blood_add)){
                ScenePosition3D playerCurPos = playerInfo.getCurPos();
                double dis = CommonUtils.calDistance(Pair.of((double) cartNpcPos.getX(), (double) cartNpcPos.getZ()), Pair.of((double) playerCurPos.getX(), (double) playerCurPos.getZ()));
                if(dis <= cartNpcTemplate.range){
                    inPlayerIds.add(playerInfo.getPlayerId());
                }
            }
        }
        boolean speedIsChange = false;
        LeagueEscortConstant constant = LeagueEscortService.getConstant();
        int[] playerAddedInfos = StringUtil.splitToIntArray(constant.SpeedIncrease, "\\|");
        int addedPercentage = inPlayerIds.size() / playerAddedInfos[0] * playerAddedInfos[1];
        //矫正速度
        List<LeagueEscortPlayerInfo> changePlayerInfos = new ArrayList<>();
        for(LeagueEscortPlayerInfo playerInfo : getPlayerInfos().values()){
            ScenePosition3D playerCurPos = playerInfo.getCurPos();
            double dis = CommonUtils.calDistance(Pair.of((double) cartNpcPos.getX(), (double) cartNpcPos.getZ()), Pair.of((double) playerCurPos.getX(), (double) playerCurPos.getZ()));
            //圈外没有增加额外速度，圈内增加额外速度
            double addedSpeed = 0D;
            if(dis <= cartNpcTemplate.range){
                addedSpeed = playerInfo.getBaseSpeed() * (double)addedPercentage / 100D;
            }
            if(playerInfo.getAddedSpeed() != addedSpeed){
                speedIsChange = true;
                playerInfo.setAddedSpeed(addedSpeed);
                changePlayerInfos.add(playerInfo);
            }
        }

        int[] monsterReduceInfos = StringUtil.splitToIntArray(constant.SpeedReduction, "\\|");
        int reducePercentage = inPlayerIds.size() / monsterReduceInfos[0] * monsterReduceInfos[1];

        List<LeagueEscortNpcInfo> changeNpcInfos = new ArrayList<>();
        for (LeagueEscortNpcInfo monsterNpcInfo : getNpcInfos().values()) {
            //战斗中的怪物不计算追到标记
            if(monsterNpcInfo.getId() == cartNpc.getId() || monsterNpcInfo.getStatus(AbsLeagueEscortUnit.monster_battle)){
                continue;
            }
            ScenePosition3D npcNpcPos = monsterNpcInfo.getCurPos();
            double dis = CommonUtils.calDistance(Pair.of((double) cartNpcPos.getX(), (double) cartNpcPos.getZ()), Pair.of((double) npcNpcPos.getX(), (double) npcNpcPos.getZ()));
            LeagueEscortNpcTemplate template = monsterNpcInfo.getTemplate();
            double reduceSpeed = 0D;
            if(template.range >= dis){
                //减速度
                reduceSpeed = -(monsterNpcInfo.getBaseSpeed() * (double)reducePercentage / 100D);
            }
            if(monsterNpcInfo.getAddedSpeed() != reduceSpeed){
                speedIsChange = true;
                monsterNpcInfo.setAddedSpeed(reduceSpeed);
                changeNpcInfos.add(monsterNpcInfo);
            }

        }

        if(speedIsChange){
            //同步
            sync(LeagueEscortSyncEnum.change_speed, changePlayerInfos, changeNpcInfos);
        }

    }

    @Override
    public boolean canMerge(long leagueId) {
        return true;
    }

    @Override
    public void merge(League selfLeague, League targetLeague) {

    }

    @Override
    public void removeLeague(League league) {

    }

    @Override
    public void quitLeague(long pid) {

    }
}
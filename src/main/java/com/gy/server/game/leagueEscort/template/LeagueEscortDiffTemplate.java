package com.gy.server.game.leagueEscort.template;

import com.gy.server.common.base.AbsTemplate;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.scene.map.SceneMapService;
import com.gy.server.scene.map.bean.SceneMapType;

import java.util.List;
import java.util.Map;

/**
 * GVE-帮派运镖玩法难度表
 * @author: gbk
 * @date: 2024-10-14 16:34
 */
public class LeagueEscortDiffTemplate implements AbsTemplate {

    //难度id
    public int difficultyId;
    //难度
    public int difficulty;
    //地图
    public SceneMapType mapType;
    //节点类型
    public int nodeGroup;

    public List<RewardTemplate> successReward;
    public List<RewardTemplate> loseReward;


    @Override
    public void readTxt(Map<String, String> map) {
        this.difficultyId = Integer.parseInt(map.get("difficultyId"));
        this.difficulty = Integer.parseInt(map.get("difficulty"));
        this.mapType = SceneMapService.getSceneId2Types(Integer.parseInt(map.get("map")));
        this.nodeGroup = Integer.parseInt(map.get("nodeGroup"));
        this.successReward = RewardTemplate.readListFromText(map.get("successReward"));
        this.loseReward = RewardTemplate.readListFromText(map.get("loseReward"));
    }
}
package com.gy.server.game.leagueEscort.handler;

import com.gy.server.game.leagueEscort.LeagueEscortModel;

/**
 * GVE-帮派运镖状态接口
 * @author: gbk
 * @date: 2024-10-16 16:46
 */
public interface ILEHandler {

    /**
     * 当前状态
     */
    public LEStatusEnums curStatus();

    public boolean tryChangeStatus(LeagueEscortModel escortModel);

    /**
     * 切换状态
     */
    public void modifyStatus(LeagueEscortModel escortModel, LEStatusEnums newStatus);

    /**
     * 初始化操作
     */
    public void initDeal(LeagueEscortModel escortModel);

    /**
     * tick操作
     */
    public void tickDeal(LeagueEscortModel escortModel);

}
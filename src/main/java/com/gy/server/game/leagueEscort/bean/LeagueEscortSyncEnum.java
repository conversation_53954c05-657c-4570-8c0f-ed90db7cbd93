package com.gy.server.game.leagueEscort.bean;

import com.gy.server.game.leagueEscort.LeagueEscortModel;
import com.gy.server.game.leagueEscort.bean.npc.LeagueEscortCartInfo;
import com.gy.server.game.leagueEscort.bean.npc.LeagueEscortMonsterInfo;
import com.gy.server.game.leagueEscort.bean.npc.LeagueEscortNpcInfo;
import com.gy.server.game.leagueEscort.bean.npc.LeagueEscortPlayerInfo;
import com.gy.server.packet.PbLeague;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.CollectionUtil;

import java.util.List;

/**
 * 同步
 * @author: gbk
 * @date: 2024-10-31 13:27
 */
public enum LeagueEscortSyncEnum {

    move_le((model, builder, param) -> {
        builder.setType(PbProtocol.LeagueEscortSingleInfoSync.LeagueEscortSyncType.move_le);
        List<Long> moveNpcIds = (List<Long>) param[0];
        List<Long> movePlayerIds = (List<Long>) param[1];
        if(CollectionUtil.isNotEmpty(moveNpcIds)){
            for (Long moveNpcId : moveNpcIds) {
                LeagueEscortNpcInfo npcInfo = model.getNpcInfos().get(moveNpcId);
                if(npcInfo instanceof LeagueEscortMonsterInfo){
                    builder.addNpcInfos(((LeagueEscortMonsterInfo)npcInfo).genPb());
                }else{
                    builder.addNpcInfos(((LeagueEscortCartInfo)npcInfo).genPb());
                }
            }
        }
        if(CollectionUtil.isNotEmpty(moveNpcIds)){
            LeagueEscortCartInfo cartNpc = model.getCartNpc();
            for (Long movePlayerId : movePlayerIds) {
                LeagueEscortPlayerInfo playerInfo = model.getPlayerInfos().get(movePlayerId);
                builder.addPlayerInfos(playerInfo.genPb(cartNpc));
            }
        }
        if(CollectionUtil.isEmpty(moveNpcIds) && CollectionUtil.isEmpty(movePlayerIds)){
            System.out.println("move null !!!");
        }
    }),

    add_npc_le((model, builder, param) -> {
        builder.setType(PbProtocol.LeagueEscortSingleInfoSync.LeagueEscortSyncType.add_npc_le);
        List<LeagueEscortMonsterInfo> allNpcInfos = (List<LeagueEscortMonsterInfo>) param[0];
        for (LeagueEscortMonsterInfo npcInfo : allNpcInfos) {
            builder.addNpcInfos(npcInfo.genPb());
        }
    }),

    /**
     * 镖车掉血
     */
    cart_blood((model, builder, param) -> {
        builder.setType(PbProtocol.LeagueEscortSingleInfoSync.LeagueEscortSyncType.cart_blood);
        LeagueEscortCartInfo cartNpc = model.getCartNpc();
        builder.addNpcInfos(cartNpc.genPb());
    }),

    player_blood((model, builder, param) -> {
        builder.setType(PbProtocol.LeagueEscortSingleInfoSync.LeagueEscortSyncType.player_blood);
        LeagueEscortPlayerInfo playerInfo = (LeagueEscortPlayerInfo) param[0];
        builder.addPlayerInfos(playerInfo.genPb(model.getCartNpc()));
    }),

    player_point((model, builder, param) -> {
        builder.setType(PbProtocol.LeagueEscortSingleInfoSync.LeagueEscortSyncType.player_point);
        LeagueEscortPlayerInfo playerInfo = (LeagueEscortPlayerInfo) param[0];
        builder.addPlayerInfos(playerInfo.genPb(model.getCartNpc()));
    }),

    cart_non_move((model, builder, param) -> {
        builder.setType(PbProtocol.LeagueEscortSingleInfoSync.LeagueEscortSyncType.cart_non_move);
        LeagueEscortCartInfo cartNpc = model.getCartNpc();
        PbLeague.LeagueEscortNpcInfo.Builder npcInfoBuilder = cartNpc.genPb().toBuilder();
        npcInfoBuilder.setTargetPos(npcInfoBuilder.getCurPos());
        builder.addNpcInfos(npcInfoBuilder.build());
    }),

    cart_can_move((model, builder, param) -> {
        builder.setType(PbProtocol.LeagueEscortSingleInfoSync.LeagueEscortSyncType.cart_can_move);
        LeagueEscortCartInfo cartNpc = model.getCartNpc();
        builder.addNpcInfos(cartNpc.genPb());
    }),

    change_speed((model, builder, param) -> {
        builder.setType(PbProtocol.LeagueEscortSingleInfoSync.LeagueEscortSyncType.change_speed);
        List<LeagueEscortPlayerInfo> changePlayerInfos = (List<LeagueEscortPlayerInfo>) param[0];
        List<LeagueEscortNpcInfo> changeNpcInfos = (List<LeagueEscortNpcInfo>) param[1];
        for (LeagueEscortNpcInfo npcInfo : changeNpcInfos) {
            if(npcInfo instanceof LeagueEscortMonsterInfo){
                builder.addNpcInfos(((LeagueEscortMonsterInfo)npcInfo).genPb());
            }else{
                builder.addNpcInfos(((LeagueEscortCartInfo)npcInfo).genPb());
            }
        }
        LeagueEscortCartInfo cartNpc = model.getCartNpc();
        for (LeagueEscortPlayerInfo changePlayerInfo : changePlayerInfos) {
            builder.addPlayerInfos(changePlayerInfo.genPb(cartNpc));
        }
    }),

    monster_dead((model, builder, param) -> {
        builder.setType(PbProtocol.LeagueEscortSingleInfoSync.LeagueEscortSyncType.monster_dead);
        Long monsterId = (Long) param[0];
        builder.setMonsterId(monsterId);
    }),

    combat_add((model, builder, param) -> {
        builder.setType(PbProtocol.LeagueEscortSingleInfoSync.LeagueEscortSyncType.combat_add);
        Long monsterId = (Long) param[0];
        Long playerId = (Long) param[1];
        builder.setMonsterId(monsterId);
        builder.setPlayerId(playerId);
    }),

    combat_remove((model, builder, param) -> {
        builder.setType(PbProtocol.LeagueEscortSingleInfoSync.LeagueEscortSyncType.combat_remove);
        Long monsterId = (Long) param[0];
        Long playerId = (Long) param[1];
        builder.setMonsterId(monsterId);
        builder.setPlayerId(playerId);
    }),

    chest_add((model, builder, param) -> {
        builder.setType(PbProtocol.LeagueEscortSingleInfoSync.LeagueEscortSyncType.chest_add);
        LeagueEscortChestInfo chestInfo = model.getChestInfos().get(model.getChestInfos().size() - 1);
        builder.setChestInfos(chestInfo.genPb());
    }),

    transmit((model, builder, param) -> {
        Long playerId = (Long) param[0];
        builder.setType(PbProtocol.LeagueEscortSingleInfoSync.LeagueEscortSyncType.transmit);
        LeagueEscortPlayerInfo playerInfo = model.getPlayerInfos().get(playerId);
        LeagueEscortCartInfo cartNpc = model.getCartNpc();
        builder.addPlayerInfos(playerInfo.genPb(cartNpc));
    }),

    finish((model, builder, param) -> {
        builder.setType(PbProtocol.LeagueEscortSingleInfoSync.LeagueEscortSyncType.finish);
        LeagueEscortCartInfo cartNpc = model.getCartNpc();
        for (LeagueEscortPlayerInfo playerInfo : model.getPlayerInfos().values()) {
            builder.addPlayerInfos(playerInfo.genPb(cartNpc));
        }
        for (LeagueEscortNpcInfo npcInfo : model.getNpcInfos().values()) {
            if(npcInfo instanceof LeagueEscortMonsterInfo){
                builder.addNpcInfos(((LeagueEscortMonsterInfo)npcInfo).genPb());
            }else{
                builder.addNpcInfos(((LeagueEscortCartInfo)npcInfo).genPb());
            }
        }
    }),

    change_node((model, builder, param) -> {
        builder.setType(PbProtocol.LeagueEscortSingleInfoSync.LeagueEscortSyncType.change_node);
        builder.setEscortInfo(model.genPb());
    }),

    monster_blood((model, builder, param) -> {
        long monsterId = (long) param[0];
        builder.setType(PbProtocol.LeagueEscortSingleInfoSync.LeagueEscortSyncType.monster_blood);
        LeagueEscortNpcInfo npcInfo = model.getNpcInfos().get(monsterId);
        builder.addNpcInfos(((LeagueEscortMonsterInfo) npcInfo).genPb());
    }),

    ;

    FillMsg inter;
    LeagueEscortSyncEnum(FillMsg inter){
        this.inter = inter;
    }

    public void fillMsg(PbProtocol.LeagueEscortSingleInfoSync.Builder builder, LeagueEscortModel model, Object... param){
        inter.fill(model, builder, param);
    }

    public static interface FillMsg{
        public void fill(LeagueEscortModel model, PbProtocol.LeagueEscortSingleInfoSync.Builder builder, Object... param);
    }
}
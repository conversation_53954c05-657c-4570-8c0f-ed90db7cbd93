package com.gy.server.game.leagueEscort.handler.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.league.League;
import com.gy.server.game.leagueEscort.LeagueEscortGlobalData;
import com.gy.server.game.leagueEscort.LeagueEscortModel;
import com.gy.server.game.leagueEscort.LeagueEscortService;
import com.gy.server.game.leagueEscort.bean.LeagueEscortRankInfo;
import com.gy.server.game.leagueEscort.bean.LeagueEscortSyncEnum;
import com.gy.server.game.leagueEscort.bean.npc.LeagueEscortCartInfo;
import com.gy.server.game.leagueEscort.bean.npc.LeagueEscortPlayerInfo;
import com.gy.server.game.leagueEscort.handler.AbsLEHandler;
import com.gy.server.game.leagueEscort.handler.LEStatusEnums;
import com.gy.server.game.leagueEscort.template.*;
import com.gy.server.game.mail.MailManager;
import com.gy.server.game.mail.MailType;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbCommons;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 结算
 * @author: gbk
 * @date: 2024-10-17 10:23
 */
public class LE4FinishHandler extends AbsLEHandler {

    @Override
    public LEStatusEnums curStatus() {
        return LEStatusEnums.finish;
    }

    @Override
    public boolean tryChangeStatus(LeagueEscortModel escortModel) {
        return false;
    }

    @Override
    public void initDeal(LeagueEscortModel escortModel) {

    }

    @Override
    public void tickDeal(LeagueEscortModel escortModel) {
        //开始结算逻辑
        LeagueEscortCartInfo cartNpc = escortModel.getCartNpc();
        LeagueEscortNpcTemplate cartNpcTemplate = cartNpc.getTemplate();
        //只有镖车到达终点，并且血量大于0才算成功
        Map<Integer, LeagueEscortNodeTemplate> nodeTemplateMap = LeagueEscortService.getNodeMapByDiffId(escortModel.getDifficultyId());
        LeagueEscortNodeTemplate maxNodeTemp = nodeTemplateMap.get(Collections.max(nodeTemplateMap.keySet()));

        LeagueEscortDiffTemplate diffTemplate = LeagueEscortService.getDiffTemplates().get(escortModel.getDifficultyId());
        MailType pointReward = MailType.LeagueEscortPointReward;
        MailType leagueReward = MailType.LeagueEscortLeagueReward;


        LeagueEscortConstant constant = LeagueEscortService.getConstant();

        League league = escortModel.getLeague();
        List<Reward> leagueRewardList;
        if(cartNpc.getCartBlood() > 0 && escortModel.getCurNode() == maxNodeTemp.node && escortModel.move2Focus()){
            //胜利
            leagueRewardList = Reward.templateCollectionToReward(diffTemplate.successReward);
            //进排行榜
            add2Rank(escortModel);

            //检查是否开启下一难度
            if(cartNpc.getCartBlood() * 10000 / constant.CarBlood >= constant.Unlock){
                escortModel.setDifficultyId(Math.min(Collections.max(LeagueEscortService.getDiff2Ids().keySet()), escortModel.getDifficulty() + 1));
            }
        }else{
            //失败
            leagueRewardList = Reward.templateCollectionToReward(diffTemplate.loseReward);

        }
        //发放帮派奖励
        PbCommons.PbText title = Text.genText(leagueReward.getTitleId()).build();
        PbCommons.PbText content = Text.genText(leagueReward.getContentId()).build();
        for (Long member : league.getMemberMap().keySet()) {
            MailManager.sendMail(
                    leagueReward,
                    member,
                    title,
                    content,
                    ServerConstants.getCurrentTimeMillis(),
                    leagueRewardList);
        }
        //发放个人积分奖励
        title = Text.genText(pointReward.getTitleId()).build();
        content = Text.genText(pointReward.getContentId()).build();
        List<LeagueEscortPointsRewardTemplate> rewardTemplateList = LeagueEscortService.getPointsRewardTemps().get(escortModel.getDifficultyId());
        for (LeagueEscortPlayerInfo playerInfo : escortModel.getPlayerInfos().values()) {
            for (LeagueEscortPointsRewardTemplate template : rewardTemplateList) {
                if(playerInfo.getPoint() >= template.points){
                    MailManager.sendMail(
                            pointReward,
                            playerInfo.getPlayerId(),
                            title,
                            content,
                            ServerConstants.getCurrentTimeMillis(),
                            RewardTemplate.createRewards(template.rewards));
                    break;
                }
            }
        }
        //重置信息
        escortModel.sync(LeagueEscortSyncEnum.finish);
        escortModel.setEnd(true);
        modifyStatus(escortModel,LEStatusEnums.before);
    }


    private void add2Rank(LeagueEscortModel escortModel){
        LeagueEscortCartInfo cartNpc = escortModel.getCartNpc();
        League league = escortModel.getLeague();
        LeagueEscortGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueEscort);
        LeagueEscortRankInfo rankInfo = new LeagueEscortRankInfo();
        rankInfo.setLeagueId(league.getLeagueId());
        rankInfo.setDifficulty(escortModel.getDifficulty());
        rankInfo.setCartBlood(cartNpc.getCartBlood());
        rankInfo.setCostTime(ServerConstants.getCurrentTimeMillis() - escortModel.getOpenTime());
        globalData.getRankInfos().add(rankInfo);
        globalData.sort();
    }

}
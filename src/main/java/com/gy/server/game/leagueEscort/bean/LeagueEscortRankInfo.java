package com.gy.server.game.leagueEscort.bean;

import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.packet.PbLeague;
import com.ttlike.server.tl.baselib.serialize.league.escort.LeagueEscortRankInfoDb;

import javax.persistence.Column;

/**
 * GVE帮派运镖-排名信息
 * @author: gbk
 * @date: 2024-10-30 14:31
 */
public class LeagueEscortRankInfo {

    //帮派id
    private long leagueId;
    //难度
    private int difficulty;
    //血量
    private int cartBlood;
    //花费时间
    private long costTime;

    public void readFromDb(LeagueEscortRankInfoDb infoDb){
        this.leagueId = infoDb.getLeagueId();
        this.difficulty = infoDb.getDifficulty();
        this.cartBlood = infoDb.getCartBlood();
        this.costTime = infoDb.getCostTime();
    }

    public LeagueEscortRankInfoDb writeToDb(){
        LeagueEscortRankInfoDb infoDb = new LeagueEscortRankInfoDb();
        infoDb.setLeagueId(leagueId);
        infoDb.setDifficulty(difficulty);
        infoDb.setCartBlood(cartBlood);
        infoDb.setCostTime(costTime);
        return infoDb;
    }

    public PbLeague.LeagueEscortRankInfo genPb(int rank){
        PbLeague.LeagueEscortRankInfo.Builder builder = PbLeague.LeagueEscortRankInfo.newBuilder();
        builder.setLeagueId(leagueId);
        League leagueById = LeagueManager.getLeagueById(leagueId);
        builder.setName(leagueById.getName());
        builder.setIconName(leagueById.getIconName());
        builder.setIconFrameId(leagueById.getIconFrameId());
        builder.setCartBlood(cartBlood);
        builder.setDifficulty(difficulty);
        builder.setCostTime(costTime);
        builder.setRank(rank);
        return builder.build();
    }

    public long getLeagueId() {
        return leagueId;
    }

    public void setLeagueId(long leagueId) {
        this.leagueId = leagueId;
    }

    public int getDifficulty() {
        return difficulty;
    }

    public void setDifficulty(int difficulty) {
        this.difficulty = difficulty;
    }

    public int getCartBlood() {
        return cartBlood;
    }

    public void setCartBlood(int cartBlood) {
        this.cartBlood = cartBlood;
    }

    public long getCostTime() {
        return costTime;
    }

    public void setCostTime(long costTime) {
        this.costTime = costTime;
    }

}
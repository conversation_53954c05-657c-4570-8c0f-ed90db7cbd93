package com.gy.server.game.leagueEscort.bean;

import com.gy.server.game.drop.DropService;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.leagueEscort.LeagueEscortModel;
import com.gy.server.game.leagueEscort.LeagueEscortService;
import com.gy.server.game.leagueEscort.bean.npc.AbsLeagueEscortUnit;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.player.Player;
import com.gy.server.packet.PbCommons;
import com.gy.server.packet.PbLeague;
import com.gy.server.scene.map.SceneMapService;
import com.ttlike.server.tl.baselib.serialize.league.escort.LeagueEscortModelDb;
import com.ttlike.server.tl.baselib.serialize.scene.ScenePosition3D;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 帮派运镖-宝箱信息
 * @author: gbk
 * @date: 2024-10-30 13:23
 */
public class LeagueEscortChestInfo extends AbsLeagueEscortUnit {

    //宝箱id
    private int chest;
    //领取玩家id
    private Set<Long> receivePlayerIds = new HashSet<>();
    //击杀者名字
    private String killerName;
    //宝箱位置
    private ScenePosition3D pos;

    public LeagueEscortChestInfo(){

    }

    public LeagueEscortChestInfo(int chest, String killerName, ScenePosition3D pos){
        this.chest = chest;
        this.killerName = killerName;
        this.pos = pos;
    }

    public void readFromDb(LeagueEscortModelDb.LeagueEscortChestInfoDb infoDb){
        this.chest = infoDb.getChest();
        this.receivePlayerIds = infoDb.getReceivePlayerIds();
        this.killerName = infoDb.getKillerName();
        this.pos = infoDb.getPos();
    }

    public LeagueEscortModelDb.LeagueEscortChestInfoDb writeToDb(){
        LeagueEscortModelDb.LeagueEscortChestInfoDb infoDb = new LeagueEscortModelDb.LeagueEscortChestInfoDb();
        infoDb.setChest(this.chest);
        infoDb.setReceivePlayerIds(this.receivePlayerIds);
        infoDb.setKillerName(this.killerName);
        infoDb.setPos(this.pos);
        return infoDb;
    }

    public PbLeague.LeagueEscortChestInfo genPb(){
        PbLeague.LeagueEscortChestInfo.Builder chestInfoBuilder = PbLeague.LeagueEscortChestInfo.newBuilder();
        chestInfoBuilder.setChest(chest);
        chestInfoBuilder.addAllReceivePlayerIds(receivePlayerIds);
        chestInfoBuilder.setKillerName(killerName);
        chestInfoBuilder.setPos(SceneMapService.convertPosition(amplifyPos(pos)));
        return chestInfoBuilder.build();
    }

    public int getDropId(){
        return LeagueEscortService.getChestTemplates().get(chest);
    }

    public List<PbCommons.PbReward> add2Player(Player player){
        getReceivePlayerIds().add(player.getPlayerId());
        List<Reward> rewards = DropService.executeDrop(player, getDropId(), BehaviorType.LeagueEscortChestReward);
        return Reward.writeCollectionToPb(rewards);
    }

    public String getKillerName() {
        return killerName;
    }

    public void setKillerName(String killerName) {
        this.killerName = killerName;
    }

    public LeagueEscortChestInfo(int chest){
        this.chest = chest;
    }

    public int getChest() {
        return chest;
    }

    public void setChest(int chest) {
        this.chest = chest;
    }

    public Set<Long> getReceivePlayerIds() {
        return receivePlayerIds;
    }

    public void setReceivePlayerIds(Set<Long> receivePlayerIds) {
        this.receivePlayerIds = receivePlayerIds;
    }

    @Override
    public double getSpeed() {
        return 0;
    }

    @Override
    public double getBaseSpeed() {
        return 0;
    }

    @Override
    public void move(LeagueEscortModel escortModel) {

    }
}
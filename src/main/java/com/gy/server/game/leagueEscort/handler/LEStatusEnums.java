package com.gy.server.game.leagueEscort.handler;

import com.gy.server.game.leagueEscort.LeagueEscortModel;
import com.gy.server.game.leagueEscort.handler.impl.LE1BeforeHandler;
import com.gy.server.game.leagueEscort.handler.impl.LE2InitHandler;
import com.gy.server.game.leagueEscort.handler.impl.LE3TickHandler;
import com.gy.server.game.leagueEscort.handler.impl.LE4FinishHandler;

/**
 * @author: gbk
 * @date: 2024-10-17 10:27
 */
public enum LEStatusEnums {

    before(0, new LE1BeforeHandler()),
    init(1, new LE2InitHandler()),
    tick(2, new LE3TickHandler()),
    finish(3, new LE4FinishHandler()),

    ;

    int status;
    ILEHandler handler;

    LEStatusEnums(int status, ILEHandler handler){
        this.status = status;
        this.handler = handler;
    }

    public ILEHandler getHandler() {
        return handler;
    }

    public void statusCheck(LeagueEscortModel escortModel){
        handler.tickDeal(escortModel);
        if(handler.tryChangeStatus(escortModel)){
            escortModel.getStatus().getHandler().initDeal(escortModel);
        }
    }

}
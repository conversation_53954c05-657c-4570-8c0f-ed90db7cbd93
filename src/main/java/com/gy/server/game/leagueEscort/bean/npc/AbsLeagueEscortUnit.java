package com.gy.server.game.leagueEscort.bean.npc;

import com.gy.server.common.util.CommonUtils;
import com.ttlike.server.tl.baselib.serialize.scene.ScenePosition3D;
import org.apache.commons.lang3.tuple.Pair;

import java.util.HashMap;
import java.util.Map;

/**
 * 运镖单位抽象类
 * @author: gbk
 * @date: 2024-10-22 10:06
 */
public abstract class AbsLeagueEscortUnit implements ILeagueEscortUnit {

    //镖车掉血
    public static final int cart_blood_loss = 0;
    //玩家回血
    public static final int player_blood_add = 1;
    //禁止移动
    public static final int can_not_move = 2;
    //怪物战斗标记
    public static final int monster_battle = 3;
    //重伤
    public static final int serious_injury = 4;
    //增加积分
    public static final int add_point = 5;

    private int status;

    //附加速度
    public double addedSpeed;

    //剩余血量 key:id value:当前血量 ， 最大血量
    private Map<Integer, Pair<Long, Long>> surplusHps = new HashMap<>();

    public Map<Integer, Pair<Long, Long>> getSurplusHps() {
        return surplusHps;
    }

    public void setSurplusHps(Map<Integer, Pair<Long, Long>> surplusHps) {
        this.surplusHps = surplusHps;
    }


    /**
     * 设置状态
     * @param statusType 状态类型
     * @param isReal 是否为真
     */
    public void setStatus(int statusType, boolean isReal){
        if(isReal){
            status |= (1 << statusType);
        }else{
            status &= ~(1 << statusType);
        }
    }

    /**
     * 获得状态类型
     */
    public boolean getStatus(int statusType){
        return (status & (1 << statusType)) > 0;
    }


    public ScenePosition3D calNextPos(ScenePosition3D curPos, ScenePosition3D targetPos, double speed){
        Pair<Double, Double> nextPoint = CommonUtils.calNextPoint(Pair.of((double) curPos.getX(), (double) curPos.getZ()), Pair.of((double) targetPos.getX(), (double) targetPos.getZ()), speed);
        ScenePosition3D position3D = new ScenePosition3D();
        position3D.setX((float)(double)nextPoint.getKey());
        position3D.setZ((float)(double)nextPoint.getValue());
        return position3D;
    }

    public double getAddedSpeed() {
        return addedSpeed;
    }

    public void setAddedSpeed(double addedSpeed) {
        this.addedSpeed = addedSpeed;
    }

    public ScenePosition3D amplifyPos(ScenePosition3D pos){
        ScenePosition3D result = new ScenePosition3D();
        result.setX(pos.getX() * 1000F);
        result.setY(pos.getY() * 1000F);
        result.setZ(pos.getZ() * 1000F);
        return result;
    }

    /**
     * 获取当前速度
     */
    public abstract double getSpeed();

    /**
     * 获取模版速度
     */
    public abstract double getBaseSpeed();

}
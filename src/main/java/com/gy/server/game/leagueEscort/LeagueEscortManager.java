package com.gy.server.game.leagueEscort;

import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.league.LeagueModelEnums;
import com.gy.server.game.leagueEscort.bean.LeagueEscortSyncEnum;
import com.gy.server.game.leagueEscort.bean.npc.AbsLeagueEscortUnit;
import com.gy.server.game.leagueEscort.bean.npc.LeagueEscortCartInfo;
import com.gy.server.game.leagueEscort.bean.npc.LeagueEscortNpcInfo;
import com.gy.server.game.leagueEscort.bean.npc.LeagueEscortPlayerInfo;
import com.gy.server.game.leagueEscort.template.LeagueEscortConstant;
import com.gy.server.game.leagueEscort.template.LeagueEscortNpcTemplate;
import com.gy.server.game.log.GameLogger;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.StringUtil;
import com.gy.server.utils.function.Ticker;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.serialize.scene.ScenePosition3D;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.core.jmx.Server;

import java.util.Map;
import java.util.Objects;

/**
 * GVE-帮派运镖管理类
 * @author: gbk
 * @date: 2024-10-10 17:01
 */
public class LeagueEscortManager implements Ticker {

    private static final LeagueEscortManager instance = new LeagueEscortManager();
    public static LeagueEscortManager getInstance(){
        return instance;
    }

    @Override
    public void tick() {
        for (League league : LeagueManager.getLeagues()) {
            LeagueEscortModel model = league.getModel(LeagueModelEnums.escort);
            if(model.getOpenTime() <= 0 || model.isEnd()){
                continue;
            }
            //状态切换和tick操作(固定频率)
            model.getStatus().statusCheck(model);

            if(ServerConstants.getCurrentTimeMillis() >= model.getOpenTime()){
                cartBloodLossCheck(model);
                playerBloodRecoverCheck(model);
            }
            try {
                Thread.sleep(1L);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * 镖车掉血检测
     */
    private void cartBloodLossCheck(LeagueEscortModel model){
        LeagueEscortCartInfo cartNpc = model.getCartNpc();
        if(Objects.nonNull(cartNpc) && cartNpc.getStatus(AbsLeagueEscortUnit.cart_blood_loss)){
            ScenePosition3D cartNpcPos = cartNpc.getCurPos();
            boolean isRealLoss = false;
            for (Long npcId : cartNpc.getAttackNpcIds()) {
                LeagueEscortNpcInfo monsterNpcInfo = model.getMonsterNpcInfo(npcId);
                ScenePosition3D npcNpcPos = monsterNpcInfo.getCurPos();
                //检查掉血频率
                //检查距离和是否怪物被攻击
                if(!monsterNpcInfo.getStatus(AbsLeagueEscortUnit.monster_battle)){
                    double dis = CommonUtils.calDistance(Pair.of((double) cartNpcPos.getX(), (double) cartNpcPos.getZ()), Pair.of((double) npcNpcPos.getX(), (double) npcNpcPos.getZ()));
                    LeagueEscortNpcTemplate template = monsterNpcInfo.getTemplate();
                    if(template.range >= dis){
                        if(ServerConstants.getCurrentTimeMillis() >= monsterNpcInfo.getCanMoveTime()){
                            monsterNpcInfo.setCanMoveTime(ServerConstants.getCurrentTimeMillis() + template.atkSpd);
                            //掉血
                            cartNpc.setCartBlood(cartNpc.getCartBlood() - template.atk);

                            GameLogger.leagueEscort(String.format("le cart reduce blood, monsterTid : %s, blood : %s", monsterNpcInfo.getId() , cartNpc.getCartBlood()));
                        }
                        //虽然不一定扣血，只是间隔没到，也算掉血
                        isRealLoss = true;
                    }
                }
            }
            if(isRealLoss){
                model.sync(LeagueEscortSyncEnum.cart_blood);
                //真实扣血
//                if(cartNpc.getCartBlood() <= 0){
//                    //直接结束
//                    return;
//                }
            }else{
                //没有掉血，恢复状态
                cartNpc.setStatus(AbsLeagueEscortUnit.cart_blood_loss, false);
            }
        }
    }

    /**
     * 玩家恢复血量
     */
    private void playerBloodRecoverCheck(LeagueEscortModel model){
        if(model.getOpenTime() <= 0){
            return;
        }
        LeagueEscortConstant constant = LeagueEscortService.getConstant();
        int[] pointInfos = StringUtil.splitToIntArray(constant.BonusPoint, "\\|");
        for (LeagueEscortPlayerInfo playerInfo : model.getPlayerInfos().values()) {
            //恢复血量
            if(playerInfo.getStatus(AbsLeagueEscortUnit.player_blood_add) && playerInfo.getNextCanRecoverHpTime() <= ServerConstants.getCurrentTimeMillis()){
                //heroId, value:剩余血量，最大血量
                Map<Integer, Pair<Long, Long>> heroSurplusHps = playerInfo.getSurplusHps();
                //heroSurplusHps为空，说明满血，不需要回血
                if(CollectionUtil.isNotEmpty(heroSurplusHps)){
                    //检测回血标记
                    playerInfo.setNextCanRecoverHpTime(ServerConstants.getCurrentTimeMillis() + constant.playerRecoverRate);
                    long allCurHp = 0;
                    long allMaxHp = 0;
                    boolean isRealAdd = false;
                    for (Integer id : heroSurplusHps.keySet()) {
                        Pair<Long, Long> hpInfo = heroSurplusHps.get(id);
                        if(Objects.equals(hpInfo.getKey(), hpInfo.getValue())){
                            continue;
                        }
                        isRealAdd = true;
                        //计算回血
                        long curHp = Math.min(hpInfo.getValue(), hpInfo.getKey() + (hpInfo.getValue() * constant.BloodReturnFrequency / 10000L));
                        allCurHp += curHp;
                        allMaxHp += hpInfo.getValue();
                        heroSurplusHps.put(id, Pair.of(curHp, hpInfo.getValue()));
                    }
                    //检查是否解除重伤状态
                    if(isRealAdd){
                        if(playerInfo.getStatus(AbsLeagueEscortUnit.serious_injury)){
                            if(allCurHp * 10000L / allMaxHp >= constant.ReleaseState){
                                playerInfo.setStatus(AbsLeagueEscortUnit.serious_injury, false);
                            }
                        }
                        model.sync(LeagueEscortSyncEnum.player_blood, playerInfo);
                    }
                }
            }
            //增加积分
            if(playerInfo.getStatus(AbsLeagueEscortUnit.add_point) && playerInfo.getNextAddPointTime() <= ServerConstants.getCurrentTimeMillis()){
                playerInfo.setNextAddPointTime(ServerConstants.getCurrentTimeMillis() + pointInfos[0] * DateTimeUtil.MillisOfSecond);
                playerInfo.addPoint(playerInfo.isAuto() ? pointInfos[2] : pointInfos[1]);

                model.sync(LeagueEscortSyncEnum.player_point, playerInfo);
            }
        }

    }


}
package com.gy.server.game.leagueEscort.template;

import com.gy.server.common.base.AbsTemplate;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;

import java.util.List;
import java.util.Map;

/**
 * GVE-帮派运镖难度积分表
 * @author: gbk
 * @date: 2024-10-14 16:50
 */
public class LeagueEscortPointsRewardTemplate implements AbsTemplate {

    public int difficult;

    public int points;

    public List<RewardTemplate> rewards;

    @Override
    public void readTxt(Map<String, String> map) {
        this.difficult = Integer.parseInt(map.get("difficulty"));
        this.points = Integer.parseInt(map.get("points"));
        this.rewards = RewardTemplate.readListFromText(map.get("rewards"));
    }
}
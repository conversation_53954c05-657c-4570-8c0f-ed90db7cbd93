package com.gy.server.game.leagueEscort.handler;

import com.gy.server.game.leagueEscort.LeagueEscortModel;
import com.gy.server.game.log.GameLogger;

/**
 * GVE-帮派运镖状态抽象类
 * @author: gbk
 * @date: 2024-10-16 17:44
 */
public abstract class AbsLEHandler implements ILEHandler {


    @Override
    public void modifyStatus(LeagueEscortModel escortModel, LEStatusEnums newStatus) {
        LEStatusEnums oldStatus = escortModel.getStatus();
        escortModel.setStatus(newStatus);
        log(String.format("LeagueEscort change status, old status : %s, new Status : %s", oldStatus.name(), newStatus.name()));
    }

    public static void log(String str){
        GameLogger.leagueEscort(str);
    }

}
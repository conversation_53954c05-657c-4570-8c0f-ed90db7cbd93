package com.gy.server.game.leagueEscort.handler.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.leagueEscort.LeagueEscortModel;
import com.gy.server.game.leagueEscort.handler.AbsLEHandler;
import com.gy.server.game.leagueEscort.handler.LEStatusEnums;

/**
 * 等待开始阶段
 * @author: gbk
 * @date: 2024-10-16 17:52
 */
public class LE1BeforeHandler extends AbsLEHandler {

    @Override
    public LEStatusEnums curStatus() {
        return LEStatusEnums.before;
    }

    @Override
    public boolean tryChangeStatus(LeagueEscortModel escortModel) {
        //设置的开启时间到了，就可以切换下一状态
        if(!escortModel.isEnd() && ServerConstants.getCurrentTimeMillis() >= escortModel.getOpenTime()){
            modifyStatus(escortModel, LEStatusEnums.init);
            return true;
        }
        return false;
    }

    @Override
    public void initDeal(LeagueEscortModel escortModel) {

    }

    @Override
    public void tickDeal(LeagueEscortModel escortModel) {

    }
}
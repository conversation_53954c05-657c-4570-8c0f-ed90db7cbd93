package com.gy.server.game.leagueEscort.template;

import com.gy.server.common.base.AbsTemplate;
import com.gy.server.common.util.CommonUtils;
import com.gy.server.game.leagueEscort.bean.npc.LeagueEscortMonsterInfo;
import com.gy.server.game.leagueEscort.bean.npc.LeagueEscortNpcInfo;
import com.ttlike.server.tl.baselib.serialize.scene.ScenePosition3D;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * GVE-帮派运镖难度积分表
 * @author: gbk
 * @date: 2024-10-14 17:10
 */
public class LeagueEscortNodeTemplate implements AbsTemplate {
    //节点唯一id
    public int nodeId;
    //节点组
    public int nodeGroup;
    //节点
    public int node;
    //节点坐标
    public ScenePosition3D nodePosition;
    //刷新npc信息
    public List<LERefreshNpcInfo> refreshNpcInfos = new ArrayList<>();

    public int getNodeId() {
        return nodeId;
    }

    public void setNodeId(int nodeId) {
        this.nodeId = nodeId;
    }

    public int getNodeGroup() {
        return nodeGroup;
    }

    public void setNodeGroup(int nodeGroup) {
        this.nodeGroup = nodeGroup;
    }

    public int getNode() {
        return node;
    }

    public void setNode(int node) {
        this.node = node;
    }

    public ScenePosition3D getNodePosition() {
        return nodePosition;
    }

    public void setNodePosition(ScenePosition3D nodePosition) {
        this.nodePosition = nodePosition;
    }

    public List<LERefreshNpcInfo> getRefreshNpcInfos() {
        return refreshNpcInfos;
    }

    public void setRefreshNpcInfos(List<LERefreshNpcInfo> refreshNpcInfos) {
        this.refreshNpcInfos = refreshNpcInfos;
    }

    @Override
    public void readTxt(Map<String, String> map) {
        this.nodeId = Integer.parseInt(map.get("nodeId"));
        this.nodeGroup = Integer.parseInt(map.get("nodeGroup"));
        this.nodePosition = CommonUtils.genPosition(map.get("coordinate"));
        this.node = Integer.parseInt(map.get("node"));
        //101_5_{2;1}|{3;1}|{4;1}|{5;1}|{6;1}|{7;1}|{8;1}|{9;1}
        // ,
        // 201_5_{2;1}|{3;1}|{4;1}|{5;1}|{6;1}|{7;1}|{8;1}|{9;2}
        String refreshCoordinateStr = map.get("refreshCoordinate");
        if(!"-1".equals(refreshCoordinateStr)){
            String[] refreshNpcInfoStrs = refreshCoordinateStr.split(",");
            for (String refreshNpcInfoStr : refreshNpcInfoStrs) {
                LeagueEscortNodeTemplate.LERefreshNpcInfo npcInfo = new LeagueEscortNodeTemplate.LERefreshNpcInfo();
                //NPCid_数量_{x坐标;y坐标}|{x坐标;y坐标},NPCid_数量_{x坐标;y坐标}|{x坐标;y坐标}
                String[] refreshNpcInfo = refreshNpcInfoStr.split("_");
                if(refreshNpcInfo.length != 3){
                    throw new IllegalArgumentException();
                }
                npcInfo.refreshNpcId = Integer.parseInt(refreshNpcInfo[0]);
                npcInfo.refreshNpcNum = Integer.parseInt(refreshNpcInfo[1]);
                String[] split = refreshNpcInfo[2].split("\\|");
                for (String s : split) {
                    String[] positions = s.split(";");
                    String x = positions[0];
                    String y = positions[1];
                    ScenePosition3D position3D = new ScenePosition3D();
                    position3D.setX(Float.parseFloat(x.substring(1, x.length())));
                    position3D.setZ(Float.parseFloat(y.substring(0, y.length() - 1)));
                    npcInfo.refreshNpcPositions.add(position3D);
                }
                this.refreshNpcInfos.add(npcInfo);
            }
        }

    }

    //刷新npc信息
    public static class LERefreshNpcInfo{
        //刷新npcId
        public int refreshNpcId;
        //刷新npc数量
        public int refreshNpcNum;
        //刷新npc位置
        public List<ScenePosition3D> refreshNpcPositions = new ArrayList<>();

        public int getRefreshNpcId() {
            return refreshNpcId;
        }

        public void setRefreshNpcId(int refreshNpcId) {
            this.refreshNpcId = refreshNpcId;
        }

        public int getRefreshNpcNum() {
            return refreshNpcNum;
        }

        public void setRefreshNpcNum(int refreshNpcNum) {
            this.refreshNpcNum = refreshNpcNum;
        }

        public List<ScenePosition3D> getRefreshNpcPositions() {
            return refreshNpcPositions;
        }

        public void setRefreshNpcPositions(List<ScenePosition3D> refreshNpcPositions) {
            this.refreshNpcPositions = refreshNpcPositions;
        }

        public List<LeagueEscortMonsterInfo> create(int createNodeId, ScenePosition3D targetPosition){
            List<LeagueEscortMonsterInfo> result = new ArrayList<>();
            //随机坐标点
            List<ScenePosition3D> refreshNpcPositionTemp = new ArrayList<>(refreshNpcPositions);
            Collections.shuffle(refreshNpcPositionTemp);
            for (int i = 0; i < refreshNpcNum; i++) {
                result.add(LeagueEscortNpcInfo.createMonster(createNodeId, refreshNpcId, refreshNpcPositionTemp.get(i), targetPosition));
            }
            return result;
        }

    }

}
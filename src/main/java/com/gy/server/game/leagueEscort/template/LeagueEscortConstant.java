package com.gy.server.game.leagueEscort.template;

import com.gy.server.common.base.AbsTemplate;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.utils.time.DateTimeUtil;

import java.util.List;
import java.util.Map;

/**
 * GVE-帮派运镖玩法常量
 * @author: gbk
 * @date: 2024-10-14 13:58
 */
public class LeagueEscortConstant implements AbsTemplate {

    //活动开启时间设置最小间隔（分钟）
    public int IntervalTime;
    //如果帮派高层未设置时间，将会在周六0点将活动时间自动设置为本周第x天的具体时间
    public String LatestTime;
    //站在光圈内的玩家每x人提升镖车移动速度y%
    public String SpeedIncrease;
    //每x人提升降低进入光圈的怪物移速x%
    public String SpeedReduction;
    //站在光圈内的玩家每x秒获得y点积分
    public String BonusPoint;
    //离线状态跟车仅会获得正常积分数量部分（万分比）
    public int CutDown;
    //Boss死亡后x秒车继续出发
    public int MoveOn;
    //玩家模型初始生成在镖车的半径范围x米内
    public int GeneratingRadius;
    //AI跟车自动跟在运镖车半径x米范围
    public int FollowingRadius;
    //开启所需帮派贡献
    public int ContributionValue;
    //可选择开启的时间段
    public String OpenTimePeriod;
    //活动最大时间（秒）超时则立即结算
    public int ActiveMaximumTime;
    //对局最低时间（秒），低于这个时间的对局报错，且不结算伤害
    public int GameTime;
    //回血频率，护送范围每秒回每个英雄血量的万分比
    public int BloodReturnFrequency;
    //回血大于x才可解除重伤状态（血量万分比）
    public int ReleaseState;
    //报名奖励
    public List<RewardTemplate> RegistrationBonus;
    //解锁下一难度所需血量需大于75%（万分比）
    public int Unlock;
    //镖车血量
    public int CarBlood;
    //镖车npcID
    public int npcID;
    //初始化难度
    public int initDifficulty;
    //玩家托管速度
    public double initPlayerSpeed;
    //玩家回血频率
    public long playerRecoverRate = 1000L;
    //镖车受到小怪攻击停止移动，x秒不受到攻击会继续前进
    public long moveOn;
    //传送cd
    public int transmitCD;
    //boss出现车停，boss死亡后x秒继续前进
    public int bossPullUp;
    //圈里没人车停，圈里有人x秒继续前进
    public int nobodyStops;
    //每造成1%的伤害可获得x点积分
    public long InjuryConversion;



    public boolean checkOpenTime(long time){
        String[] split = OpenTimePeriod.split("\\|");
        String[] minTimeArray = split[0].split(":");
        String[] maxTimeArray = split[1].split(":");
        long minTime = DateTimeUtil.toMillis(ServerConstants.getCurrentTimeLocalDateTime().withHour(Integer.parseInt(minTimeArray[0])).withMinute(Integer.parseInt(minTimeArray[1])).withSecond(0).withNano(0));
        long maxTime = DateTimeUtil.toMillis(ServerConstants.getCurrentTimeLocalDateTime().withHour(Integer.parseInt(maxTimeArray[0])).withMinute(Integer.parseInt(maxTimeArray[1])).withSecond(0).withNano(0));
        return minTime <= time && time <= maxTime;
    }

    @Override
    public void readTxt(Map<String, String> map) {
        if("IntervalTime".equals(map.get("key"))){
            this.IntervalTime = Integer.parseInt(map.get("value"));
        }else if("LatestTime".equals(map.get("key"))){
            this.LatestTime = map.get("value");
        }else if("OpenTimePeriod".equals(map.get("key"))){
            this.OpenTimePeriod = map.get("value");
        }else if("SpeedIncrease".equals(map.get("key"))){
            this.SpeedIncrease = map.get("value");
        }else if("SpeedReduction".equals(map.get("key"))){
            this.SpeedReduction = map.get("value");
        }else if("BonusPoint".equals(map.get("key"))){
            this.BonusPoint = map.get("value");
        }else if("MoveOn".equals(map.get("key"))){
            this.MoveOn = Integer.parseInt(map.get("value"));
        }else if("GeneratingRadius".equals(map.get("key"))){
            this.GeneratingRadius = Integer.parseInt(map.get("value"));
        }else if("FollowingRadius".equals(map.get("key"))){
            this.FollowingRadius = Integer.parseInt(map.get("value"));
        }else if("ContributionValue".equals(map.get("key"))){
            this.ContributionValue = Integer.parseInt(map.get("value"));
        }else if("OpenTimePeriod".equals(map.get("key"))){
            this.OpenTimePeriod = map.get("value");
        }else if("ActiveMaximumTime".equals(map.get("key"))){
            this.ActiveMaximumTime = Integer.parseInt(map.get("value"));
        }else if("GameTime".equals(map.get("key"))){
            this.GameTime = Integer.parseInt(map.get("value"));
        }else if("BloodReturnFrequency".equals(map.get("key"))){
            this.BloodReturnFrequency = Integer.parseInt(map.get("value"));
        }else if("ReleaseState".equals(map.get("key"))){
            this.ReleaseState = Integer.parseInt(map.get("value"));
        }else if("RegistrationBonus".equals(map.get("key"))){
            this.RegistrationBonus = RewardTemplate.readListFromText(map.get("value"));
        }else if("Unlock".equals(map.get("key"))){
            this.Unlock = Integer.parseInt(map.get("value"));
        }else if("CarBlood".equals(map.get("key"))){
            this.CarBlood = Integer.parseInt(map.get("value"));
        }else if("npcID".equals(map.get("key"))){
            this.npcID = Integer.parseInt(map.get("value"));
        }else if("InitialDifficulty".equals(map.get("key"))){
            this.initDifficulty = Integer.parseInt(map.get("value"));
        }else if("playerMovementSpeed".equals(map.get("key"))){
            this.initPlayerSpeed = Double.parseDouble(map.get("value"));
        }else if("moveOn".equals(map.get("key"))){
            this.moveOn = Integer.parseInt(map.get("value"));
        }else if("bossPullUp".equals(map.get("key"))){
            this.bossPullUp = Integer.parseInt(map.get("value"));
        }else if("nobodyStops".equals(map.get("key"))){
            this.nobodyStops = Integer.parseInt(map.get("value"));
        }else if("InjuryConversion".equals(map.get("key"))){
            this.InjuryConversion = Long.parseLong(map.get("value"));
        }


    }
}
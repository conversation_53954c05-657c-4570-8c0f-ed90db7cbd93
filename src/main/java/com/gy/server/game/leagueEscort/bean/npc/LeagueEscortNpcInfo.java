package com.gy.server.game.leagueEscort.bean.npc;

import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.leagueEscort.LeagueEscortModel;
import com.gy.server.game.leagueEscort.LeagueEscortService;
import com.gy.server.game.leagueEscort.template.LeagueEscortConstant;
import com.gy.server.game.leagueEscort.template.LeagueEscortNpcTemplate;
import com.gy.server.game.log.GameLogger;
import com.ttlike.server.tl.baselib.serialize.league.escort.LeagueEscortModelDb;
import com.ttlike.server.tl.baselib.serialize.scene.ScenePosition3D;
import org.apache.commons.lang3.tuple.Pair;

/**
 * GVE-帮派运镖npc信息
 * @author: gbk
 * @date: 2024-10-11 10:32
 */
public abstract class LeagueEscortNpcInfo extends AbsLeagueEscortUnit {

    //唯一id
    private long id;
    //模版id
    private int templateId;
    //当前位置
    private ScenePosition3D curPos;
    //目标位置
    private ScenePosition3D targetPos;

    //可以移动时间
    private long canMoveTime;

    public void readFromDb(LeagueEscortModelDb.LeagueEscortNpcInfoDb npcInfoDb){
        this.id = npcInfoDb.getId();
        this.templateId = npcInfoDb.getTemplateId();
        this.curPos = npcInfoDb.getCurPos();
        this.targetPos = npcInfoDb.getTargetPos();
        this.canMoveTime = npcInfoDb.getCanMoveTime();
    }

    public abstract void subReadFromDb(LeagueEscortModelDb.LeagueEscortNpcInfoDb npcInfoDb);

    public LeagueEscortModelDb.LeagueEscortNpcInfoDb writeToDb(){
        LeagueEscortModelDb.LeagueEscortNpcInfoDb npcInfoDb = new LeagueEscortModelDb.LeagueEscortNpcInfoDb();
        npcInfoDb.setId(id);
        npcInfoDb.setTemplateId(templateId);
        npcInfoDb.setCurPos(curPos);
        npcInfoDb.setTargetPos(targetPos);
        npcInfoDb.setCanMoveTime(canMoveTime);
        return npcInfoDb;
    }

    public abstract void subWriteToDb(LeagueEscortModelDb.LeagueEscortNpcInfoDb npcInfoDb);

    public boolean canMove(){
        return ServerConstants.getCurrentTimeMillis() >= canMoveTime && !getStatus(AbsLeagueEscortUnit.can_not_move);
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public int getTemplateId() {
        return templateId;
    }

    public LeagueEscortNpcTemplate getTemplate(){
        return LeagueEscortService.getNpcTemplates().get(getTemplateId());
    }

    public void setTemplateId(int templateId) {
        this.templateId = templateId;
    }

    @Override
    public double getSpeed() {
        return getBaseSpeed() + getAddedSpeed();
    }

    @Override
    public double getBaseSpeed() {
        LeagueEscortNpcTemplate template = getTemplate();
        return template.speed;
    }

    public ScenePosition3D getCurPos() {
        return curPos;
    }

    public ScenePosition3D getAmplifyCurPos(){
        return amplifyPos(getCurPos());
    }

    public void setCurPos(ScenePosition3D curPos) {
        this.curPos = curPos;
    }

    public ScenePosition3D getTargetPos() {
        return targetPos;
    }

    public ScenePosition3D getAmplifyTargetPos(){
        return amplifyPos(getTargetPos());
    }

    public void setTargetPos(ScenePosition3D targetPos) {
        this.targetPos = targetPos;
    }

    public long getCanMoveTime() {
        return canMoveTime;
    }

    public void setCanMoveTime(long canMoveTime) {
        this.canMoveTime = canMoveTime;
    }

    /**
     * 创建一个怪物Npc
     */
    public static LeagueEscortMonsterInfo createMonster(int createNodeId, int tempId, ScenePosition3D curPos, ScenePosition3D targetPos){
        LeagueEscortMonsterInfo npcInfo = new LeagueEscortMonsterInfo();
        npcInfo.setId(ServerConstants.allocateRuntimeIdentifier());
        npcInfo.setTemplateId(tempId);
        npcInfo.setCurPos(curPos);
        npcInfo.setTargetPos(targetPos);
        npcInfo.setCreateNodeId(createNodeId);
        return npcInfo;
    }

    /**
     * 创建一个镖车单位
     */
    public static LeagueEscortCartInfo createCart(int tempId, ScenePosition3D curPos, ScenePosition3D targetPos){
        LeagueEscortCartInfo npcInfo = new LeagueEscortCartInfo();
        npcInfo.setId(ServerConstants.allocateRuntimeIdentifier());
        npcInfo.setTemplateId(tempId);
        npcInfo.setCurPos(curPos);
        npcInfo.setTargetPos(targetPos);
        LeagueEscortConstant constant = LeagueEscortService.getConstant();
        npcInfo.setCartBlood(constant.CarBlood);
        return npcInfo;
    }

    /**
     * 是否怪物
     */
    public boolean isMonster(){
        return !isCart();
    }

    /**
     * 是否马车
     */
    public boolean isCart(){
        LeagueEscortConstant constant = LeagueEscortService.getConstant();
        return constant.npcID == this.templateId;
    }

    @Override
    public void move(LeagueEscortModel escortModel) {
        //与终点距离小于速度，直接走到终点
        double dis = CommonUtils.calDistance(Pair.of((double)curPos.getX(), (double)curPos.getZ()), Pair.of((double)targetPos.getX(), (double)targetPos.getZ()));
        double curSpeed = getSpeed();
        if(dis <= curSpeed){
            curPos = targetPos;
            return ;
        }
        ScenePosition3D oldPos = curPos;
        //计算下一目标点
        curPos = calNextPos(curPos, targetPos, curSpeed);

//        GameLogger.leagueEscort(String.format("le npc move, npcId : %s, %s", templateId, curPos));
        if(isCart()){
            //更新怪物目标点
            for (LeagueEscortNpcInfo npcInfo : escortModel.getNpcInfos().values()) {
                if(!npcInfo.isCart()){
                    npcInfo.setTargetPos(curPos);
                }
            }
        }

    }


}
package com.gy.server.game.leagueEscort;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.event.ServerEvent;
import com.gy.server.game.event.ServerEventHandler;
import com.gy.server.game.event.ServerEventType;
import com.gy.server.game.global.GlobalData;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueDataInterface;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.leagueEscort.bean.LeagueEscortRankInfo;
import com.gy.server.game.leagueEscort.template.LeagueEscortRankRewardTemplate;
import com.gy.server.game.mail.MailManager;
import com.gy.server.game.mail.MailType;
import com.gy.server.game.text.Text;
import com.gy.server.game.text.TextParamText;
import com.gy.server.packet.PbCommons;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.ttlike.server.tl.baselib.serialize.league.escort.LeagueEscortGlobalDataDb;
import com.ttlike.server.tl.baselib.serialize.league.escort.LeagueEscortRankInfoDb;
import com.ttlike.server.tl.baselib.serialize.monthTower.MonthTowerGlobalDataDb;

import java.time.DayOfWeek;
import java.util.*;

/**
 * GVE-帮派运镖全局数据
 * @author: gbk
 * @date: 2024-10-30 14:01
 */
public class LeagueEscortGlobalData extends GlobalData implements ServerEventHandler, LeagueDataInterface {
    private static final ServerEventType[] eventTypes = new ServerEventType[]{ServerEventType.day5clock};

    /**
     * 排名信息
     */
    List<LeagueEscortRankInfo> rankInfos = new ArrayList<>();

    public List<LeagueEscortRankInfo> getRankInfos() {
        return rankInfos;
    }

    public void sort(){
        Collections.sort(rankInfos, (o1, o2) -> {
            if(o1.getDifficulty() == o2.getDifficulty()){
                if(o1.getCartBlood() == o2.getCartBlood()){
                    if(o1.getCostTime() == o2.getCostTime()){
                        return (int)(o1.getCostTime() - o2.getCostTime());
                    }
                }
                return o2.getCartBlood() - o1.getCartBlood();
            }
            return o2.getDifficulty() - o1.getDifficulty();
        });
    }

    public void refresh(){
        if(ServerConstants.getCurrentTimeLocalDateTime().getDayOfWeek() == DayOfWeek.MONDAY){
            //周一刷新
            List<LeagueEscortRankRewardTemplate> rankRewardTemplates = LeagueEscortService.getRankRewardTemplates();
            MailType mailType = MailType.LeagueEscortRankReward;
            PbCommons.PbText title = Text.genText(mailType.getTitleId()).build();

            for (int i = 0; i < rankInfos.size(); i++) {
                int rank = i + 1;
                LeagueEscortRankInfo rankInfo = rankInfos.get(i);
                for (LeagueEscortRankRewardTemplate rankRewardTemplate : rankRewardTemplates) {
                    if(rankRewardTemplate.minRank <= rank && rankRewardTemplate.maxRank >= rank){
                        List<Reward> rewardTemplates = RewardTemplate.createRewards(rankRewardTemplate.rewardList);
                        sendReward(mailType, title, rank, rankInfo, rewardTemplates);
                        break;
                    }
                }
            }
            rankInfos.clear();
        }
    }

    public void sendReward(MailType mailType, PbCommons.PbText title, int rank
            , LeagueEscortRankInfo rankInfo, List<Reward> rewardTemplates){
        PbCommons.PbText content = Text.genText(mailType.getContentId(), new TextParamText(String.valueOf(rank))).build();
        League league = LeagueManager.getLeagueByPlayerId(rankInfo.getLeagueId());
        if(Objects.nonNull(league)){
            for (Long member : league.getMemberMap().keySet()) {
                MailManager.sendMail(
                        mailType,
                        member,
                        title,
                        content,
                        ServerConstants.getCurrentTimeMillis(),
                        rewardTemplates);
            }
        }
    }

    @Override
    public void readFromPb(byte[] bytes) throws Exception {
        LeagueEscortGlobalDataDb dataDb = PbUtilCompress.decode(LeagueEscortGlobalDataDb.class, bytes);
        for (LeagueEscortRankInfoDb rankInfoDb : dataDb.getRankInfos()) {
            LeagueEscortRankInfo rankInfo = new LeagueEscortRankInfo();
            rankInfo.readFromDb(rankInfoDb);
            rankInfos.add(rankInfo);
        }
    }

    @Override
    public byte[] writeToPb() {
        LeagueEscortGlobalDataDb dataDb = new LeagueEscortGlobalDataDb();
        for (LeagueEscortRankInfo rankInfo : rankInfos) {
            dataDb.getRankInfos().add(rankInfo.writeToDb());
        }
        return PbUtilCompress.encode(dataDb);
    }

    @Override
    public ServerEventType[] getEventTypes() {
        return eventTypes;
    }

    @Override
    public void handle(ServerEvent event) {
        switch (event.getEventType()){
            case day5clock:{
                refresh();
                break;
            }
        }
    }

    @Override
    public boolean canMerge(long leagueId) {
        return true;
    }

    @Override
    public void merge(League selfLeague, League targetLeague) {

    }

    @Override
    public void removeLeague(League league) {

    }

    @Override
    public void quitLeague(long pid) {

    }
}

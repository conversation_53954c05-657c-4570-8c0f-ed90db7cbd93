package com.gy.server.game.leagueEscort.bean;

import com.gy.server.game.attribute.AttributeKey;
import com.gy.server.game.battleCollect.BattleCollectService;
import com.gy.server.game.battleCollect.template.BattleCollectTemplate;
import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.combat.StageType;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.combat.unit.TeamUnit;
import com.gy.server.game.leagueEscort.LeagueEscortModel;
import com.gy.server.game.leagueEscort.LeagueEscortService;
import com.gy.server.game.leagueEscort.bean.npc.*;
import com.gy.server.game.leagueEscort.handler.AbsLEHandler;
import com.gy.server.game.leagueEscort.template.LeagueEscortConstant;
import com.gy.server.game.leagueEscort.template.LeagueEscortNpcTemplate;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.player.Player;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.MathUtil;
import org.apache.commons.lang3.tuple.Pair;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 帮派运镖-战斗
 * @author: gbk
 * @date: 2024-10-23 19:34
 */
public class LeagueEscortStage extends AbstractStage {

    Player player;
    LeagueEscortNpcInfo monsterNpcInfo;
    LeagueEscortModel escortModel;

    public LeagueEscortStage(Player player, LeagueEscortModel escortModel, LeagueEscortNpcInfo monsterNpcInfo){
        this.player = player;
        this.monsterNpcInfo = monsterNpcInfo;
        this.escortModel = escortModel;
    }
    //记录战斗前的血量
    Map<Integer, Pair<Long, Long>> surplusHpsBefore = new HashMap<>();

    @Override
    public void init() {
        LineupType lineupType = LineupType.leagueEscort;
        StageType stageType = StageType.leagueEscort;
        //进攻阵容
        List<HeroUnit> atks = player.getLineupModel().createHeroUnits(stageType, lineupType);
        LeagueEscortPlayerInfo playerInfo = escortModel.getPlayerInfos().get(player.getPlayerId());
        Map<Integer, Pair<Long, Long>> atkSurplusHps = playerInfo.getSurplusHps();
        for (HeroUnit atk : atks) {
            if(atkSurplusHps.containsKey(atk.getTid())){
                atk.getAttributes().setHp(atkSurplusHps.get(atk.getTid()).getKey());
            }
        }
        TeamUnit atkTeam = new TeamUnit(getNewId(), atks);
        atkTeam.setAutoMode(playerInfo.getPlayerId());
        //防守阵容
        LeagueEscortNpcTemplate template = monsterNpcInfo.getTemplate();
        BattleCollectTemplate battleCollectTemplate = BattleCollectService.getBattleCollectTemplateMap().get(template.battleCollectId);
        List<HeroUnit> defs = battleCollectTemplate.genHeroUnits();
        //修改怪物血量
        Map<Integer, Pair<Long, Long>> defSurplusHps = monsterNpcInfo.getSurplusHps();
        surplusHpsBefore.putAll(defSurplusHps);
        for (HeroUnit def : defs) {
            if(defSurplusHps.containsKey(def.getTid())){
                def.getAttributes().setHp(defSurplusHps.get(def.getTid()).getKey());
            }
        }
        TeamUnit defTeam = new TeamUnit(getNewId(), defs);

        init(template.battleCollectId, stageType,atkTeam, defTeam, lineupType);
    }

    @Override
    public void afterFinish() {
        notifyCombatSettlement(player.getPlayerId(), genCombatSettlement().build());
        if(escortModel.getOpenTime() > 0){
            //修改进攻方剩余血量
            LeagueEscortPlayerInfo playerInfo = escortModel.getPlayerInfos().get(player.getPlayerId());
            Map<Integer, Pair<Long, Long>> atkSurplusHps = playerInfo.getSurplusHps();
            TeamUnit atkTeam = getAtks().get(0);
            long atkMaxHp = -1;
            for (HeroUnit heroUnit : atkTeam.getFormation()) {
                if(Objects.nonNull(heroUnit)){
                    long maxHp = heroUnit.getAttributes().getValue(AttributeKey.最大生命);
                    long hpNow = heroUnit.getAttributes().getHp();
                    atkSurplusHps.put(heroUnit.getTid(), Pair.of(hpNow, maxHp));
                    atkMaxHp = Math.max(atkMaxHp, hpNow);
                }
            }
            if(atkMaxHp <= 0){
                playerInfo.setStatus(AbsLeagueEscortUnit.serious_injury, true);
            }
            //修改防守方剩余血量
            TeamUnit defTeam = getDefs().get(0);
            //总伤害就是血量变化值（策划说不会回血）
            long allAtk = 0;
            long allMaxHp = 0;
            LeagueEscortMonsterInfo monsterInfo = escortModel.getMonsterNpcInfo(monsterNpcInfo.getId());
            if(Objects.isNull(monsterInfo)){
                //怪物已经被别人打死
                for (HeroUnit heroUnit : defTeam.getFormation()) {
                    if(Objects.nonNull(heroUnit)){
                        long maxHp = heroUnit.getAttributes().getValue(AttributeKey.最大生命);
                        long hpBefore = surplusHpsBefore.containsKey(heroUnit.getTid()) ? surplusHpsBefore.get(heroUnit.getTid()).getKey() : maxHp;
                        long hpNow = heroUnit.getAttributes().getHp();
                        long nowAtk = hpBefore - hpNow;
                        allAtk += nowAtk;
                        allMaxHp += maxHp;
                    }
                }
            }else{
                boolean isAllDead = true;
    
                //根据本次战斗打掉的血量，减掉剩余血量
                Map<Integer, Pair<Long, Long>> defSurplusHps = monsterInfo.getSurplusHps();
                long defMaxHp = -1;
                for (HeroUnit heroUnit : defTeam.getFormation()) {
                    if(Objects.nonNull(heroUnit) && !heroUnit.isTeamSoul()){
                        long maxHp = heroUnit.getAttributes().getValue(AttributeKey.最大生命);
                        long hpBefore = surplusHpsBefore.containsKey(heroUnit.getId()) ? surplusHpsBefore.get(heroUnit.getId()).getKey() : maxHp;
                        long hpNow = heroUnit.getAttributes().getHp();
                        long nowAtk = hpBefore - hpNow;
                        allAtk += nowAtk;
                        allMaxHp += maxHp;
                        long hpFinal = Math.max(defSurplusHps.containsKey(heroUnit.getTid()) ? defSurplusHps.get(heroUnit.getTid()).getKey() : maxHp - nowAtk, 0L);
                        defSurplusHps.put(heroUnit.getTid(), Pair.of(hpFinal, maxHp));
                        defMaxHp = Math.max(defMaxHp, hpFinal);
                    }
                }
                //同步怪物血量
                escortModel.sync(LeagueEscortSyncEnum.monster_blood, monsterInfo.getId());
    
                //移除正在战斗玩家id
                monsterInfo.getCombatPlayerIds().remove(player.getPlayerId());
                playerInfo.setCombat(false);

                if(CollectionUtil.isEmpty(monsterInfo.getCombatPlayerIds())){
                    monsterInfo.setStatus(AbsLeagueEscortUnit.monster_battle, false);
                    monsterInfo.setStatus(AbsLeagueEscortUnit.can_not_move, false);
                    AbsLEHandler.log("monster npc restore move , combat : " + monsterNpcInfo.getId());

                    escortModel.sync(LeagueEscortSyncEnum.combat_remove, monsterInfo.getId(), player.getPlayerId());
                }

                if(defMaxHp <= 0){
                    //怪物被打死
                    escortModel.getNpcInfos().remove(monsterNpcInfo.getId());
                    LeagueEscortCartInfo cartNpc = escortModel.getCartNpc();
                    if(cartNpc.getAttackNpcIds().contains(monsterInfo.getId())){
                        cartNpc.getAttackNpcIds().remove((Long)monsterNpcInfo.getId());
                    }
                    escortModel.sync(LeagueEscortSyncEnum.monster_dead, monsterNpcInfo.getId());
    
                    //检查是否是boss怪
                    LeagueEscortNpcTemplate template = monsterNpcInfo.getTemplate();
                    if(template.isBoss()){
                        escortModel.cartMoveCheck(3);
                    }else{
                        escortModel.cartMoveCheck(2);
                    }
                    //检查掉落宝箱
                    Map<Integer, Integer> chestWeights = template.chestWeights;
                    if(CollectionUtil.isNotEmpty(chestWeights) && MathUtil.randomInt(template.dropProbability) < template.dropProbability){
                        Integer chest = MathUtil.weightRandom(chestWeights);
                        escortModel.getChestInfos().add(new LeagueEscortChestInfo(chest, player.getName(), monsterNpcInfo.getCurPos()));
                        escortModel.sync(LeagueEscortSyncEnum.chest_add);
                    }
    
                }
    
            }
            //计算积分
            LeagueEscortConstant constant = LeagueEscortService.getConstant();
            long finalPoint = allAtk * 100L / allMaxHp * constant.InjuryConversion;
            playerInfo.addPoint(finalPoint);
            escortModel.sync(LeagueEscortSyncEnum.player_point, playerInfo);
        }


    }

    @Override
    public void combatAbort(boolean isStart) {
        LeagueEscortPlayerInfo playerInfo = escortModel.getPlayerInfos().get(player.getPlayerId());
        playerInfo.setCombat(false);
        if(monsterNpcInfo instanceof LeagueEscortMonsterInfo){
            LeagueEscortMonsterInfo monsterInfo = (LeagueEscortMonsterInfo) monsterNpcInfo;
            monsterInfo.getCombatPlayerIds().add(player.getPlayerId());
            //设置怪物标记
            monsterInfo.setStatus(AbsLeagueEscortUnit.monster_battle, false);
            monsterInfo.setStatus(AbsLeagueEscortUnit.can_not_move, false);
        }
    }
}
package com.gy.server.game.leagueEscort;

import com.gy.server.common.redis.RedisScript;
import com.gy.server.core.Configuration;
import com.gy.server.core.ServerConstants;
import com.gy.server.core.command.ServerCommandRequest;
import com.gy.server.core.delay.MessageSystemHashInvoke;
import com.gy.server.core.delay.MessageSystemSyncInvoke;
import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.db.nosql.redis.GyRedisTop;
import com.gy.server.game.activity.chineseChess.ChineseChessActivityHelper;
import com.gy.server.game.combat.CombatManager;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.function.Function;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.league.LeagueModelEnums;
import com.gy.server.game.league.enums.LeagueJobTypeEnums;
import com.gy.server.game.leagueEscort.bean.LeagueEscortChestInfo;
import com.gy.server.game.leagueEscort.bean.LeagueEscortRankInfo;
import com.gy.server.game.leagueEscort.bean.LeagueEscortStage;
import com.gy.server.game.leagueEscort.bean.LeagueEscortSyncEnum;
import com.gy.server.game.leagueEscort.bean.npc.AbsLeagueEscortUnit;
import com.gy.server.game.leagueEscort.bean.npc.LeagueEscortCartInfo;
import com.gy.server.game.leagueEscort.bean.npc.LeagueEscortMonsterInfo;
import com.gy.server.game.leagueEscort.bean.npc.LeagueEscortPlayerInfo;
import com.gy.server.game.leagueEscort.handler.AbsLEHandler;
import com.gy.server.game.leagueEscort.handler.impl.LE2InitHandler;
import com.gy.server.game.leagueEscort.template.*;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.service.Service;
import com.gy.server.game.text.Text;
import com.gy.server.game.warZone.WarZoneService;
import com.gy.server.packet.PbCommons;
import com.gy.server.packet.PbProtocol;
import com.gy.server.scene.map.bean.SceneMapType;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.serialize.RedisAssistant;
import com.ttlike.server.tl.baselib.serialize.scene.ScenePosition3D;

import java.util.*;

/**
 * GVE-帮派运镖
 * @author: gbk
 * @date: 2024-10-09 20:29
 */
public class LeagueEscortService extends PlayerPacketHandler implements Service {

    /**
     * 常量信息
     */
    private static LeagueEscortConstant constant;
    /**
     * 难度表
     * key：difficulty唯一id value：难度信息
     */
    private static Map<Integer, LeagueEscortDiffTemplate> diffTemplates = new HashMap<>();
    /**
     * 难度对应表：
     * key：difficulty难度 value：难度唯一id
     */
    private static Map<Integer, List<Integer>> diff2Ids = new HashMap<>();
    /**
     * 积分奖励表
     * key：难度 value：积分奖励
     */
    private static Map<Integer, List<LeagueEscortPointsRewardTemplate>> pointsRewardTemps = new HashMap<>();
    /**
     * 节点信息表
     * key：节点类型 key：节点（非节点id） value节点信息
     */
    private static Map<Integer, Map<Integer, LeagueEscortNodeTemplate>> nodeTemplates = new HashMap<>();

    /**
     * npc信息 key:NpcId
     */
    private static Map<Integer, LeagueEscortNpcTemplate> npcTemplates = new HashMap<>();
    /**
     * 宝箱id对应dropId
     */
    private static Map<Integer, Integer> chestTemplates = new HashMap<>();
    /**
     * 排名奖励信息
     */
    private static List<LeagueEscortRankRewardTemplate> rankRewardTemplates = new ArrayList<>();


    public static LeagueEscortConstant getConstant(){
        return constant;
    }

    public static Map<Integer, LeagueEscortDiffTemplate> getDiffTemplates() {
        return diffTemplates;
    }

    public static Map<Integer, LeagueEscortNodeTemplate> getNodeMapByDiffId(int diffId){
        LeagueEscortDiffTemplate diffTemplate = getDiffTemplates().get(diffId);
        return getNodeTemplates().get(diffTemplate.nodeGroup);
    }

    public static Map<Integer, List<Integer>> getDiff2Ids() {
        return diff2Ids;
    }

    public static Map<Integer, List<LeagueEscortPointsRewardTemplate>> getPointsRewardTemps() {
        return pointsRewardTemps;
    }

    public static Map<Integer, Map<Integer, LeagueEscortNodeTemplate>> getNodeTemplates() {
        return nodeTemplates;
    }

    public static Map<Integer, LeagueEscortNpcTemplate> getNpcTemplates() {
        return npcTemplates;
    }

    public static Map<Integer, Integer> getChestTemplates() {
        return chestTemplates;
    }

    public static List<LeagueEscortRankRewardTemplate> getRankRewardTemplates() {
        return rankRewardTemplates;
    }

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.tissueEscort_summaryTable);
        Map<Integer, LeagueEscortDiffTemplate> diffTemplateMap = new HashMap<>();
        Map<Integer, List<Integer>> diff2IdMap = new HashMap<>();
        for (Map<String, String> map : mapList) {
            LeagueEscortDiffTemplate diffTemplate = new LeagueEscortDiffTemplate();
            diffTemplate.readTxt(map);
            diffTemplateMap.put(diffTemplate.difficultyId, diffTemplate);
            if(!diff2IdMap.containsKey(diffTemplate.difficulty)){
                diff2IdMap.put(diffTemplate.difficulty, new ArrayList<>());
            }
            diff2IdMap.get(diffTemplate.difficulty).add(diffTemplate.difficultyId);
        }
        diffTemplates = diffTemplateMap;
        diff2Ids = diff2IdMap;


        /**
         * 积分奖励表
         * key：难度 value：积分奖励
         */
        Map<Integer, List<LeagueEscortPointsRewardTemplate>> pointsRewardTempMap = new HashMap<>();
        mapList = ConfigReader.read(ConfigFile.tissueEscort_pointsReward);
        for (Map<String, String> map : mapList) {
            LeagueEscortPointsRewardTemplate template = new LeagueEscortPointsRewardTemplate();
            template.readTxt(map);
            if(!pointsRewardTempMap.containsKey(template.difficult)){
                pointsRewardTempMap.put(template.difficult, new ArrayList<>());
            }
            pointsRewardTempMap.get(template.difficult).add(template);
        }
        pointsRewardTemps = pointsRewardTempMap;
        //顺序按积分从大到小
        for (Integer diff : pointsRewardTemps.keySet()) {
            Collections.reverse(pointsRewardTemps.get(diff));
        }
        /**
         * 节点信息表
         * key：节点类型 key：节点（非节点id） value节点信息
         */
        Map<Integer, Map<Integer, LeagueEscortNodeTemplate>> nodeTemplateMap = new HashMap<>();
        mapList = ConfigReader.read(ConfigFile.tissueEscort_node);
        for (Map<String, String> map : mapList) {
            LeagueEscortNodeTemplate nodeTemplate = new LeagueEscortNodeTemplate();
            nodeTemplate.readTxt(map);
            if(!nodeTemplateMap.containsKey(nodeTemplate.nodeGroup)){
                nodeTemplateMap.put(nodeTemplate.nodeGroup, new HashMap<>());
            }
            nodeTemplateMap.get(nodeTemplate.nodeGroup).put(nodeTemplate.node, nodeTemplate);
        }
        nodeTemplates = nodeTemplateMap;
        /**
         * npc信息 key:NpcId
         */
        Map<Integer, LeagueEscortNpcTemplate> npcTemplateMap = new HashMap<>();
        mapList = ConfigReader.read(ConfigFile.tissueEscort_npc);
        for (Map<String, String> map : mapList) {
            LeagueEscortNpcTemplate npcTemplate = new LeagueEscortNpcTemplate();
            npcTemplate.readTxt(map);
            npcTemplateMap.put(npcTemplate.npcId, npcTemplate);
        }
        npcTemplates = npcTemplateMap;
        /**
         * 宝箱id对应dropId
         */
        Map<Integer, Integer> chestTemplateMap = new HashMap<>();
        mapList = ConfigReader.read(ConfigFile.tissueEscort_chest);
        for (Map<String, String> map : mapList) {
            chestTemplateMap.put(Integer.parseInt(map.get("chestId")), Integer.parseInt(map.get("dropId")));
        }
        chestTemplates = chestTemplateMap;
        /**
         * 排名奖励信息
         */
        List<LeagueEscortRankRewardTemplate> rankRewardTemplateList = new ArrayList<>();
        mapList = ConfigReader.read(ConfigFile.tissueEscort_rankingReward);
        for (Map<String, String> map : mapList) {
            LeagueEscortRankRewardTemplate rewardTemplate = new LeagueEscortRankRewardTemplate();
            rewardTemplate.readTxt(map);
            rankRewardTemplateList.add(rewardTemplate);
        }
        rankRewardTemplates = rankRewardTemplateList;
        LeagueEscortConstant constantTemp = new LeagueEscortConstant();
        mapList = ConfigReader.read(ConfigFile.tissueEscort_const);
        for (Map<String, String> map : mapList) {
            constantTemp.readTxt(map);
        }
        constant = constantTemp;

    }

    @Override
    public void clearConfigData() {
        this.diffTemplates.clear();
        this.diff2Ids.clear();
        this.pointsRewardTemps.clear();
        this.nodeTemplates.clear();
        this.npcTemplates.clear();
        this.chestTemplates.clear();
        this.rankRewardTemplates.clear();
    }


    private static int commonCheck(Player player) {
        //检查帮派限制
        League league = LeagueManager.getLeagueByPlayer(player);
        if(Objects.isNull(league)){
            return Text.未加入帮派;
        }

        //检查帮派限制
        if(Function.leagueEscort.isNotOpen(player)){
            return Text.功能未开启;
        }

        if(LeagueManager.canNotPlayLeague(player)){
            return Text.您退出帮派未满足24小时无法参与帮派活动;
        }
        if(!League.canPlay(player, Function.leagueEscort)){
            return Text.帮派玩法已经参加过;
        }
        return Text.没有异常;
    }

    /**
     * 获取地图信息
     */
    @Handler(PtCode.LEAGUE_ESCORT_ENTERMAP_REQ)
    private void enterMap(Player player, PbProtocol.LeagueEscortEnterMapReq req, long time){
        PbProtocol.LeagueEscortEnterMapRst.Builder rst = PbProtocol.LeagueEscortEnterMapRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            int text = commonCheck(player);
            if(text != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(text));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            LeagueEscortModel escortModel = league.getModel(LeagueModelEnums.escort);
            if(escortModel.getOpenTime() > 0 && !escortModel.isEnd()){
                //检查自己是否在地图
                LeagueEscortPlayerInfo playerInfo = escortModel.getPlayerInfos().get(player.getPlayerId());
                if(Objects.isNull(playerInfo)){
                    escortModel.initPlayer(player.getPlayerId());

                    //检查镖车移动
                    escortModel.cartMoveCheck(1);
                }else{
                    playerInfo.setAuto(false);
                }
            }
            rst.setInfo(escortModel.genPb());

        }
        player.send(PtCode.LEAGUE_ESCORT_ENTERMAP_RST, rst.build(), time);
    }

    /**
     * 设置开启时间
     */
    @Handler(PtCode.LEAGUE_ESCORT_SET_OPENTIME_REQ)
    private void setOpen(Player player, PbProtocol.LeagueEscortSetOpenTimeReq req, long time){
        PbProtocol.ThiefInvasionSetOpenTimeRst.Builder rst = PbProtocol.ThiefInvasionSetOpenTimeRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            long openTime = req.getOpenTime();
            int text = commonCheck(player);
            if(text != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(text));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            LeagueEscortModel escortModel = league.getModel(LeagueModelEnums.escort);
            //检查帮派限制
            if (!league.hasAuth(player.getPlayerId(), LeagueJobTypeEnums.设置活动开启时间)) {
                rst.setResult(Text.genServerRstInfo(Text.帮派权限不足));
                break logic;
            }
            //检查能否开启
            if(escortModel.getOpenTime() > 0){
                rst.setResult(Text.genServerRstInfo(Text.帮派运镖_正在进行中));
                break logic;
            }
            if(escortModel.isEnd()){
                rst.setResult(Text.genServerRstInfo(Text.帮派运镖_活动已结束));
                break logic;
            }
            if(ServerConstants.getCurrentTimeMillis() > req.getOpenTime()){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            //检查开启时间
            LeagueEscortConstant constant = getConstant();
            if(!constant.checkOpenTime(openTime)){
                rst.setResult(Text.genServerRstInfo(Text.帮派运镖_开启时间错误));
                break logic;
            }
            escortModel.setOpenTime(openTime);
            escortModel.openBefore();
            escortModel.syncAll(false);
            rst.setOpenTime(openTime);
        }
        player.send(PtCode.LEAGUE_ESCORT_SET_OPENTIME_RST, rst.build(), time);
    }

    /**
     * 报名
     */
    @Handler(PtCode.LEAGUE_ESCORT_SIGNUP_REQ)
    private void signUp(Player player, PbProtocol.LeagueEscortSignUpReq req, long time){
        PbProtocol.LeagueEscortSignUpRst.Builder rst = PbProtocol.LeagueEscortSignUpRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            //检查帮派限制
            int text = commonCheck(player);
            if(text != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(text));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            if(Objects.isNull(league)){
                rst.setResult(Text.genServerRstInfo(Text.未加入帮派));
                break logic;
            }
            //检查能否开启
            LeagueEscortModel escortModel = league.getModel(LeagueModelEnums.escort);
            if(escortModel.getOpenTime() <= 0){
                rst.setResult(Text.genServerRstInfo(Text.帮派运镖_活动未开始));
                break logic;
            }
            if(escortModel.getSignUpPlayerIds().contains(player.getPlayerId())){
                rst.setResult(Text.genServerRstInfo(Text.帮派运镖_不能重复报名));
                break logic;
            }
            if(escortModel.isEnd()){
                rst.setResult(Text.genServerRstInfo(Text.帮派运镖_活动已结束));
                break logic;
            }
            escortModel.getSignUpPlayerIds().add(player.getPlayerId());
            LeagueEscortConstant constant = getConstant();
            List<PbCommons.PbReward> pbRewards = RewardTemplate.addToPlayer(player, constant.RegistrationBonus, BehaviorType.leagueEscortSignUp);
            rst.addAllRewards(pbRewards);
        }
        player.send(PtCode.LEAGUE_ESCORT_SIGNUP_RST, rst.build(), time);
    }

    /**
     * 传送
     */
    @Handler(PtCode.LEAGUE_ESCORT_TRANSMIT_REQ)
    private void transmit(Player player, PbProtocol.LeagueEscortTransmitReq req, long time){
        PbProtocol.LeagueEscortTransmitRst.Builder rst = PbProtocol.LeagueEscortTransmitRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            //检查帮派限制
            int text = commonCheck(player);
            if(text != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(text));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            //检查能否开启
            LeagueEscortModel escortModel = league.getModel(LeagueModelEnums.escort);
            if(escortModel.getOpenTime() <= 0){
                rst.setResult(Text.genServerRstInfo(Text.帮派运镖_活动未开始));
                break logic;
            }
            if(escortModel.isEnd()){
                rst.setResult(Text.genServerRstInfo(Text.帮派运镖_活动已结束));
                break logic;
            }
            //检查cd
            LeagueEscortConstant constant = getConstant();
            LeagueEscortPlayerInfo playerInfo = escortModel.getPlayerInfos().get(player.getPlayerId());
            if(ServerConstants.getCurrentTimeMillis() < playerInfo.getLastTransmitTime() + constant.transmitCD * DateTimeUtil.MillisOfSecond){
                rst.setResult(Text.genServerRstInfo(Text.帮派运镖_传送正在cd中));
                break logic;
            }
            playerInfo.setLastTransmitTime(ServerConstants.getCurrentTimeMillis());
            //随机坐标点
            LeagueEscortCartInfo cartNpc = escortModel.getCartNpc();
            ScenePosition3D position3D = LE2InitHandler.randomPoint(cartNpc.getCurPos(), constant.GeneratingRadius);
            escortModel.playerMove(player.getPlayerId(), position3D);

            //通知场景服
            notifyScenePosition(player, position3D);

            escortModel.sync(LeagueEscortSyncEnum.transmit, player.getPlayerId());
        }
        player.send(PtCode.LEAGUE_ESCORT_TRANSMIT_RST, rst.build(), time);
    }

    private void notifyScenePosition(Player player, ScenePosition3D position3D){
        final long playerId = player.getPlayerId();
        int routeSceneId = SceneMapType.leagueEscort.getRouteSceneId(player);
        ServerCommandRequest commandRequest = CommandRequests.newServerCommandRequest("SceneOperationCommandService.notifyScenePosition", (int)playerId);
        TLBase.getInstance().getRpcUtil().sendToNode(ServerType.SCENE, routeSceneId, commandRequest, playerId, position3D);
    }

    /**
     * 进攻野怪
     */
    @Handler(PtCode.LEAGUE_ESCORT_ATTACK_REQ)
    private void attack(Player player, PbProtocol.LeagueEscortAttackReq req, long time){
        PbProtocol.LeagueEscortAttackRst.Builder rst = PbProtocol.LeagueEscortAttackRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            //检查帮派限制
            int text = commonCheck(player);
            if(text != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(text));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            //检查能否开启
            LeagueEscortModel escortModel = league.getModel(LeagueModelEnums.escort);
            if(escortModel.getOpenTime() <= 0){
                rst.setResult(Text.genServerRstInfo(Text.帮派运镖_活动未开始));
                break logic;
            }
            if(escortModel.isEnd()){
                rst.setResult(Text.genServerRstInfo(Text.帮派运镖_活动已结束));
                break logic;
            }
            LeagueEscortPlayerInfo playerInfo = escortModel.getPlayerInfos().get(player.getPlayerId());
            //检查是否重伤
            if(Objects.isNull(playerInfo)){
                rst.setResult(Text.genServerRstInfo(Text.帮派运镖_没有玩家npc));
                break logic;
            }
            if(playerInfo.getStatus(AbsLeagueEscortUnit.serious_injury)){
                rst.setResult(Text.genServerRstInfo(Text.帮派运镖_重伤状态不能攻打怪物));
                break logic;
            }

            long npcId = req.getNpcId();

            LeagueEscortMonsterInfo monsterNpcInfo = escortModel.getMonsterNpcInfo(npcId);
            if(Objects.isNull(monsterNpcInfo)){
                rst.setResult(Text.genServerRstInfo(Text.帮派运镖_NPC已经消失));
                break logic;
            }
            monsterNpcInfo.getCombatPlayerIds().add(player.getPlayerId());
            playerInfo.setCombat(true);
            //设置怪物标记
            monsterNpcInfo.setStatus(AbsLeagueEscortUnit.monster_battle, true);
            monsterNpcInfo.setStatus(AbsLeagueEscortUnit.can_not_move, true);
            AbsLEHandler.log("monster npc none move , battle : " + monsterNpcInfo.getId());
            LeagueEscortStage stage = new LeagueEscortStage(player, escortModel, monsterNpcInfo);
            stage.init();
            rst.setStage(stage.genAbstractPb());

            CombatManager.combatPrepare(stage);
            escortModel.getInScenePlayerIds().remove(player.getPlayerId());

            escortModel.sync(LeagueEscortSyncEnum.combat_add, monsterNpcInfo.getId(), player.getPlayerId());
        }
        player.send(PtCode.LEAGUE_ESCORT_ATTACK_RST, rst.build(), time);
    }


    /**
     * 领取奖励
     */
    @Handler(PtCode.LEAGUE_ESCORT_RECEIVEREWARD_REQ)
    private void receiveReward(Player player, PbProtocol.LeagueEscortReceiveRewardReq req, long time){
        PbProtocol.LeagueEscortReceiveRewardRst.Builder rst = PbProtocol.LeagueEscortReceiveRewardRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            //检查帮派限制
            int text = commonCheck(player);
            if(text != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(text));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            //检查能否开启
            LeagueEscortModel escortModel = league.getModel(LeagueModelEnums.escort);
            if(escortModel.getOpenTime() <= 0){
                rst.setResult(Text.genServerRstInfo(Text.帮派运镖_活动未开始));
                break logic;
            }
            if(escortModel.isEnd()){
                rst.setResult(Text.genServerRstInfo(Text.帮派运镖_活动已结束));
                break logic;
            }
            int index = req.getIndex();
            List<LeagueEscortChestInfo> chestInfos = escortModel.getChestInfos();
            if(index >= 0){
                if(index >= chestInfos.size()){
                    rst.setResult(Text.genServerRstInfo(Text.帮派运镖_宝箱异常));
                    break logic;
                }
                LeagueEscortChestInfo leagueEscortChestInfo = chestInfos.get(index);
                if(leagueEscortChestInfo.getReceivePlayerIds().contains(player.getPlayerId())){
                    rst.setResult(Text.genServerRstInfo(Text.帮派运镖_宝箱已领取));
                    break logic;
                }
                rst.addAllRewards(leagueEscortChestInfo.add2Player(player));

            }else{
                for (LeagueEscortChestInfo chestInfo : chestInfos) {
                    if(chestInfo.getReceivePlayerIds().contains(player.getPlayerId())){
                        continue ;
                    }
                    rst.addAllRewards(chestInfo.add2Player(player));
                }
            }


        }
        player.send(PtCode.LEAGUE_ESCORT_RECEIVEREWARD_RST, rst.build(), time);
    }

    @Handler(PtCode.LEAGUE_ESCORT_RANK_REQ)
    public void rank(Player player, PbProtocol.LeagueEscortRankReq req, long time){
        PbProtocol.LeagueEscortRankRst.Builder rst = PbProtocol.LeagueEscortRankRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:
        {
            //检查帮派限制
            if (Function.leagueEscort.isNotOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            if (Objects.isNull(league)) {
                rst.setResult(Text.genServerRstInfo(Text.未加入帮派));
                break logic;
            }
            //检查能否开启
            LeagueEscortModel escortModel = league.getModel(LeagueModelEnums.escort);
            if (escortModel.getOpenTime() <= 0) {
                rst.setResult(Text.genServerRstInfo(Text.帮派运镖_活动未开始));
                break logic;
            }
            if(escortModel.isEnd()){
                rst.setResult(Text.genServerRstInfo(Text.帮派运镖_活动已结束));
                break logic;
            }
            LeagueEscortGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueEscort);
            List<LeagueEscortRankInfo> rankInfos = globalData.getRankInfos();
            for (int i = 0; i < rankInfos.size(); i++) {
                LeagueEscortRankInfo rankInfo = rankInfos.get(i);
                rst.addRankInfos(rankInfo.genPb(i + 1));
            }
        }
        player.send(PtCode.LEAGUE_ESCORT_RANK_RST, rst.build(), time);
    }

    public static void main(String[] args) throws Exception {
        Configuration.init("");
        Configuration.serverId = 997;
        Configuration.startupMode = Configuration.StartupMode.xml;
        TLBase.getInstance().init(Configuration.serverId, ServerType.GAME, "game", Configuration.startupMode.name(), MessageSystemSyncInvoke.getInstance(), MessageSystemHashInvoke.getInstance());
//        new ActivityService().systemStartup();
        new WarZoneService().systemStartup();
        RedisScript.init();

        System.out.println("start : !!!!!!!!!");

        RedisAssistant redisAssistant = TLBase.getInstance().getRedisAssistant();
        int activityId = 20103;
        long playerId = 9970000004L;
        int addedScore = 10;
        boolean isPass = true;
        ChineseChessActivityHelper.addScore(activityId, playerId, addedScore, isPass);

        Thread.sleep(3 * 1000L);
        List<GyRedisTop> gyRedisTops = ChineseChessActivityHelper.genSourceRankNodeList(activityId, 100);
        for (GyRedisTop gyRedisTop : gyRedisTops) {
            int rank = gyRedisTop.getRank();
            String rankInfo = gyRedisTop.getScore().toString();
            long score = gyRedisTop.getScore().longValue();
            long memberId = Long.parseLong(gyRedisTop.getValue());
            System.out.println(rank + " : " + rankInfo + " : " + score + " : " + memberId );
        }



    }

}
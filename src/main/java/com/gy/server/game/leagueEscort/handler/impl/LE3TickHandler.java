package com.gy.server.game.leagueEscort.handler.impl;

import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.leagueEscort.LeagueEscortModel;
import com.gy.server.game.leagueEscort.LeagueEscortService;
import com.gy.server.game.leagueEscort.bean.LeagueEscortSyncEnum;
import com.gy.server.game.leagueEscort.bean.npc.*;
import com.gy.server.game.leagueEscort.handler.AbsLEHandler;
import com.gy.server.game.leagueEscort.handler.LEStatusEnums;
import com.gy.server.game.leagueEscort.template.LeagueEscortConstant;
import com.gy.server.game.leagueEscort.template.LeagueEscortNodeTemplate;
import com.gy.server.game.leagueEscort.template.LeagueEscortNpcTemplate;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.StringUtil;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.serialize.scene.ScenePosition3D;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;

/**
 * tick逻辑处理
 * @author: gbk
 * @date: 2024-10-16 18:01
 */
public class LE3TickHandler extends AbsLEHandler {
    @Override
    public LEStatusEnums curStatus() {
        return LEStatusEnums.tick;
    }

    @Override
    public boolean tryChangeStatus(LeagueEscortModel escortModel) {
        LeagueEscortConstant constant = LeagueEscortService.getConstant();
        //时间到期
        if(escortModel.getOpenTime() + constant.ActiveMaximumTime * DateTimeUtil.MillisOfSecond <= ServerConstants.getCurrentTimeMillis()){
            log("leagueEscort time is over, finish!!!");
            modifyStatus(escortModel, LEStatusEnums.finish);
            return true;
        }
        //镖车到终点
        Map<Integer, LeagueEscortNodeTemplate> nodeTemplateMap = LeagueEscortService.getNodeMapByDiffId(escortModel.getDifficultyId());
        LeagueEscortNodeTemplate maxNodeTemp = nodeTemplateMap.get(Collections.max(nodeTemplateMap.keySet()));
        if(escortModel.getCurNode() == maxNodeTemp.node && escortModel.move2Focus()){
            modifyStatus(escortModel, LEStatusEnums.finish);
            log("leagueEscort cart move end, finish!!!");
            return true;
        }
        //镖车没血
        LeagueEscortCartInfo cartNpc = escortModel.getCartNpc();
        if(cartNpc.getCartBlood() <= 0){
            log("leagueEscort cart none hp, finish!!!");
            modifyStatus(escortModel, LEStatusEnums.finish);
            return true;
        }
        return false;
    }

    @Override
    public void initDeal(LeagueEscortModel escortModel) {

    }

    private static final long tickTime = 1 * 1000L;

    @Override
    public void tickDeal(LeagueEscortModel escortModel) {
        //定时移动
        if(escortModel.getLastTickTime() + tickTime < ServerConstants.getCurrentTimeMillis()){
            //npc移动
            List<Long> moveNpcIds = new ArrayList<>();
            for (LeagueEscortNpcInfo npcInfo : escortModel.getNpcInfos().values()) {
                if(npcInfo.canMove()){
                    npcInfo.move(escortModel);
                    moveNpcIds.add(npcInfo.getId());
                }
            }
            //托管玩家移动
            List<Long> movePlayerIds = new ArrayList<>();
            for (LeagueEscortPlayerInfo playerInfo : escortModel.getPlayerInfos().values()) {
                if(playerInfo.isAuto()){
                    playerInfo.move(escortModel);
                    movePlayerIds.add(playerInfo.getPlayerId());
                }
            }
            escortModel.setLastTickTime(ServerConstants.getCurrentTimeMillis());
            if(CollectionUtil.isNotEmpty(movePlayerIds) || CollectionUtil.isNotEmpty(moveNpcIds)){
                escortModel.sync(LeagueEscortSyncEnum.move_le, moveNpcIds, movePlayerIds);
            }
        }
        //切换节点，生成新npc
        if(escortModel.move2Focus()){
            int nextNode = escortModel.getCurNode() + 1;
            Map<Integer, LeagueEscortNodeTemplate> nodeMapByDiffId = LeagueEscortService.getNodeMapByDiffId(escortModel.getDifficultyId());
            if(nodeMapByDiffId.containsKey(nextNode)){
                //设置下节点
                LeagueEscortNodeTemplate nodeTemplate = nodeMapByDiffId.get(nextNode);
                escortModel.setCurNode(nextNode);
                //生成节点怪物
                LeagueEscortNpcInfo cartNpc = escortModel.getCartNpc();
                LeagueEscortNodeTemplate nextNodeTemplate = nodeMapByDiffId.get(escortModel.getCurNode() + 1);
                if(Objects.nonNull(nextNodeTemplate)){
                    cartNpc.setTargetPos(nextNodeTemplate.nodePosition);
                }
                List<LeagueEscortMonsterInfo> allNpcInfos = new ArrayList<>();
                for (LeagueEscortNodeTemplate.LERefreshNpcInfo refreshNpcInfo : nodeTemplate.getRefreshNpcInfos()) {
                    List<LeagueEscortMonsterInfo> leagueEscortNpcInfos = refreshNpcInfo.create(nodeTemplate.nodeId, cartNpc.getCurPos());
                    for (LeagueEscortMonsterInfo newNpcInfo : leagueEscortNpcInfos) {
                        if(newNpcInfo.getTemplate().isGroup){
                            //生成怪物组
                            for (int i = 0; i < 3; i++){
                                LeagueEscortMonsterInfo newMonster = newNpcInfo.copyNew();
                                escortModel.getNpcInfos().put(newMonster.getId(), newMonster);
                                allNpcInfos.add(newMonster);
                            }
                        }else{
                            escortModel.getNpcInfos().put(newNpcInfo.getId(), newNpcInfo);
                            allNpcInfos.add(newNpcInfo);
                        }
                    }
                }
                escortModel.sync(LeagueEscortSyncEnum.add_npc_le, allNpcInfos);
                escortModel.sync(LeagueEscortSyncEnum.change_node);
            }
        }

        LeagueEscortConstant constant = LeagueEscortService.getConstant();
        //检测怪物追到镖车
        LeagueEscortCartInfo cartNpc = escortModel.getCartNpc();
        LeagueEscortNpcTemplate cartNpcTemplate = cartNpc.getTemplate();
        ScenePosition3D cartNpcPos = cartNpc.getCurPos();
        for (LeagueEscortNpcInfo monsterNpcInfo : escortModel.getNpcInfos().values()) {
            //战斗中的怪物不计算追到标记
            if(monsterNpcInfo.getId() == cartNpc.getId() || monsterNpcInfo.getStatus(AbsLeagueEscortUnit.monster_battle)){
                continue;
            }
            if(!cartNpc.getAttackNpcIds().contains(monsterNpcInfo.getId())){
                ScenePosition3D npcNpcPos = monsterNpcInfo.getCurPos();
                double dis = CommonUtils.calDistance(Pair.of((double) cartNpcPos.getX(), (double) cartNpcPos.getZ()), Pair.of((double) npcNpcPos.getX(), (double) npcNpcPos.getZ()));
                LeagueEscortNpcTemplate template = monsterNpcInfo.getTemplate();
                if(template.range >= dis){
                    //怪物追到镖车，镖车掉血标记
                    cartNpc.setStatus(AbsLeagueEscortUnit.cart_blood_loss, true);
                    cartNpc.getAttackNpcIds().add(monsterNpcInfo.getId());
                    //设置禁止移动标识，
                    cartNpc.setStatus(AbsLeagueEscortUnit.can_not_move, true);
                    monsterNpcInfo.setStatus(AbsLeagueEscortUnit.can_not_move, true);
                    AbsLEHandler.log("cat and monster npc none move , cart_blood_loss : " + monsterNpcInfo.getId());

                    escortModel.sync(LeagueEscortSyncEnum.cart_non_move);
                }
            }

        }

        //检测玩家回血
        for(LeagueEscortPlayerInfo playerInfo : escortModel.getPlayerInfos().values()){
            ScenePosition3D playerCurPos = playerInfo.getCurPos();
            double dis = CommonUtils.calDistance(Pair.of((double) cartNpcPos.getX(), (double) cartNpcPos.getZ()), Pair.of((double) playerCurPos.getX(), (double) playerCurPos.getZ()));
            if(dis <= cartNpcTemplate.range){
                if(!playerInfo.getStatus(AbsLeagueEscortUnit.player_blood_add)){
                    //回血，玩家回血标记
                    playerInfo.setStatus(AbsLeagueEscortUnit.player_blood_add, true);
                    playerInfo.setNextCanRecoverHpTime(ServerConstants.getCurrentTimeMillis() + constant.playerRecoverRate);

                }
                if(!playerInfo.getStatus(AbsLeagueEscortUnit.add_point)){
                    //积分增加标记
                    playerInfo.setStatus(AbsLeagueEscortUnit.add_point, true);
                    int[] ints = StringUtil.splitToIntArray(constant.BonusPoint, "\\|");
                    playerInfo.setNextAddPointTime(ServerConstants.getCurrentTimeMillis() + ints[0] * DateTimeUtil.MillisOfSecond);

                }
            }
        }


    }
}
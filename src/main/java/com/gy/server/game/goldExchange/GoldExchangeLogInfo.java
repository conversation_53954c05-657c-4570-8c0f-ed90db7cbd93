package com.gy.server.game.goldExchange;

import com.gy.server.packet.PbGoldExchange;
import com.ttlike.server.tl.baselib.serialize.player.GoldExchangeLogInfoDb;
import org.jetbrains.annotations.NotNull;

/**
 * 元宝兑换记录
 *
 * <AUTHOR> - [Created on 2023/3/30 18:03]
 */
public class GoldExchangeLogInfo implements Comparable<GoldExchangeLogInfo> {

    /**
     * 消耗元宝数量
     */
    private int costNum;
    /**
     * 兑换次数
     */
    private int count;
    /**
     * 获得交子数量
     */
    private int rewardNum;
    /**
     * 兑换时间
     */
    private long timestamp;

    public int getCostNum() {
        return costNum;
    }

    public void setCostNum(int costNum) {
        this.costNum = costNum;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public int getRewardNum() {
        return rewardNum;
    }

    public void setRewardNum(int rewardNum) {
        this.rewardNum = rewardNum;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public PbGoldExchange.GoldExchangeLog genPb() {
        return PbGoldExchange.GoldExchangeLog.newBuilder()
                .setCostNum(costNum)
                .setCount(count)
                .setRewardNum(rewardNum)
                .setTimestamp(timestamp).build();
    }

    public GoldExchangeLogInfoDb genDb() {
        GoldExchangeLogInfoDb db = new GoldExchangeLogInfoDb();
        db.setCostNum(costNum);
        db.setCount(count);
        db.setRewardNum(rewardNum);
        db.setTimestamp(timestamp);
        return db;
    }

    public GoldExchangeLogInfo(GoldExchangeLogInfoDb db) {
        this.costNum = db.getCostNum();
        this.count = db.getCount();
        this.rewardNum = db.getRewardNum();
        this.timestamp = db.getTimestamp();
    }

    public GoldExchangeLogInfo() {
    }

    @Override
    public int compareTo(@NotNull GoldExchangeLogInfo o) {
        return Long.compare(o.timestamp, this.timestamp);
    }
}

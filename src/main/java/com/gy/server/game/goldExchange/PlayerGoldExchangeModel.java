package com.gy.server.game.goldExchange;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.mail.MailManager;
import com.gy.server.game.mail.MailType;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModel;
import com.gy.server.game.player.event.PlayerEvent;
import com.gy.server.game.player.event.PlayerEventHandler;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.reddot.RedDot;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbCommons;
import com.ttlike.server.tl.baselib.serialize.player.GoldExchangeLogInfoDb;
import com.ttlike.server.tl.baselib.serialize.player.GoldExchangeModelDb;
import com.ttlike.server.tl.baselib.serialize.player.PlayerBlobDb;

import java.util.*;

/**
 * 元宝兑换模块
 *
 * <AUTHOR> - [Created on 2023/3/30 17:59]
 */
public class PlayerGoldExchangeModel extends PlayerModel implements PlayerEventHandler {
    /**
     * 今日已兑换次数
     */
    private int todayTimes;
    /**
     * 今日已兑换累计次数奖励
     */
    private Set<Integer> dailyTimesDrawSet = new HashSet<>();
    /**
     * 今日兑换记录
     */
    private List<GoldExchangeLogInfo> exchangeLogInfoList = new ArrayList<>();

    public PlayerGoldExchangeModel(Player player) {
        super(player);
    }

    @Override
    protected void loadData(PlayerBlobDb playerBlob) {
        GoldExchangeModelDb goldExchangeModelDb = playerBlob.getGoldExchangeModelDb();
        if (Objects.nonNull(goldExchangeModelDb)) {
            this.todayTimes = goldExchangeModelDb.getTodayTimes();
            this.dailyTimesDrawSet = goldExchangeModelDb.getDailyTimesDrawSet();
            for (GoldExchangeLogInfoDb goldExchangeLogInfoDb : goldExchangeModelDb.getExchangeLogInfoList()) {
                exchangeLogInfoList.add(new GoldExchangeLogInfo(goldExchangeLogInfoDb));
            }
        }
    }

    @Override
    protected void saveData(PlayerBlobDb playerBlob) {
        GoldExchangeModelDb goldExchangeModelDb = new GoldExchangeModelDb();
        goldExchangeModelDb.setTodayTimes(todayTimes);
        goldExchangeModelDb.setDailyTimesDrawSet(dailyTimesDrawSet);
        for (GoldExchangeLogInfo goldExchangeLogInfo : exchangeLogInfoList) {
            goldExchangeModelDb.getExchangeLogInfoList().add(goldExchangeLogInfo.genDb());
        }
        playerBlob.setGoldExchangeModelDb(goldExchangeModelDb);
    }

    public int getTodayTimes() {
        return todayTimes;
    }

    public void setTodayTimes(int todayTimes) {
        this.todayTimes = todayTimes;
    }

    public Set<Integer> getDailyTimesDrawSet() {
        return dailyTimesDrawSet;
    }

    public void setDailyTimesDrawSet(Set<Integer> dailyTimesDrawSet) {
        this.dailyTimesDrawSet = dailyTimesDrawSet;
    }

    public List<GoldExchangeLogInfo> getExchangeLogInfoList() {
        return exchangeLogInfoList;
    }

    public void setExchangeLogInfoList(List<GoldExchangeLogInfo> exchangeLogInfoList) {
        this.exchangeLogInfoList = exchangeLogInfoList;
    }

    /**
     * 获取剩余兑换次数
     *
     * @return 今日剩余可兑换次数
     */
    public int getRemainTimes() {
        GoldExchangeConstant constant = GoldExchangeService.getConstant();
        int totalTimes = constant.goldExchangeDailyMaxTime;
        return totalTimes - todayTimes;
    }

    @Override
    public PlayerEventType[] getEventTypes() {
        return new PlayerEventType[]{
                PlayerEventType.day5Refresh
        };
    }

    @Override
    public void handle(PlayerEvent event) {
        switch (event.getEventType()) {
            case day5Refresh: {
                day5Refresh();

                //同步红点
                RedDot.bankExchangeFreeTime.sync(getPlayer());
                RedDot.bankExchangeReward.sync(getPlayer());
                break;
            }
        }
    }

    private void day5Refresh() {
        // 补发未领取的累计次数奖励
        List<RewardTemplate> totalReward = new ArrayList<>();
        Map<Integer, List<RewardTemplate>> goldExchangeDailyTimeRewards = GoldExchangeService.getConstant().goldExchangeDailyTimeRewards;
        for (Map.Entry<Integer, List<RewardTemplate>> entry : goldExchangeDailyTimeRewards.entrySet()) {
            Integer times = entry.getKey();
            if (todayTimes >= times && !dailyTimesDrawSet.contains(times)) {
                totalReward.addAll(entry.getValue());
            }
        }
        if (!totalReward.isEmpty()) {
            List<Reward> rewardList = Reward.templateCollectionToReward(totalReward);
            Reward.merge(rewardList);
            //发邮件
            MailType mailType = MailType.gold_exchange_reward;
            PbCommons.PbText titleText = Text.genText(mailType.getTitleId()).build();
            PbCommons.PbText contentText = Text.genText(mailType.getContentId()).build();
            MailManager.sendMail(
                    mailType,
                    getPlayerId(),
                    titleText,
                    contentText,
                    ServerConstants.getCurrentTimeMillis(),
                    rewardList);
        }
        this.todayTimes = 0;
        this.dailyTimesDrawSet.clear();
        this.exchangeLogInfoList.clear();
    }
}

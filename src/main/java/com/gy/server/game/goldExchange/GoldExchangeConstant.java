package com.gy.server.game.goldExchange;

import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.util.StringExtUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * 元宝兑换常量类
 *
 * <AUTHOR> - [Created on 2023/3/30 17:41]
 */
public final class GoldExchangeConstant {
    /**
     * 每日最大兑换次数
     */
    public final int goldExchangeDailyMaxTime;
    /**
     * 每日免费兑换次数(不计入每日最大次数)
     */
    public final int goldExchangeDailyFreeTime;
    /**
     * 次数|消耗元宝数量
     */
    public final TreeMap<Integer, Integer> goldExchangeTimeConsume;
    /**
     * 暴击倍数|概率（万分比）
     */
    public final Map<Integer, Integer> criticalHitProbability;
    /**
     * 获得交子数量（EXP表ID）
     */
    public final int quantityObtained;
    /**
     * 每日累计次数奖励(次数1_奖励1,奖励2;次数2_奖励1,奖励2;)
     */
    public final Map<Integer, List<RewardTemplate>> goldExchangeDailyTimeRewards = new HashMap<>();
    /**
     * 消耗货币类型
     */
    public final int goldExchangeConsumeCurrency;
    /**
     * 获得货币类型
     */
    public final int goldExchangeGetCurrency;

    public GoldExchangeConstant(Map<String, String> map) {
        this.goldExchangeDailyMaxTime = Integer.parseInt(map.get("goldExchangeDailyMaxTime"));
        this.goldExchangeDailyFreeTime = Integer.parseInt(map.get("goldExchangeDailyFreeTime"));
        this.goldExchangeTimeConsume = new TreeMap<>(StringExtUtil.string2Map(map.get("goldExchangeTimeConsume"), ",", "|", Integer.class, Integer.class));
        this.criticalHitProbability = StringExtUtil.string2Map(map.get("criticalHitProbability"), ",", "|", Integer.class, Integer.class);
        this.quantityObtained = Integer.parseInt(map.get("quantityObtained"));
        String goldExchangeDailyTimeRewardsStr = map.get("goldExchangeDailyTimeRewards");
        String[] goldExchangeDailyTimeRewardsArr = goldExchangeDailyTimeRewardsStr.split(";");
        for (String goldExchangeDailyTimeReward : goldExchangeDailyTimeRewardsArr) {
            String[] goldExchangeDailyTimeRewardArr = goldExchangeDailyTimeReward.split("_");
            int times = Integer.parseInt(goldExchangeDailyTimeRewardArr[0]);
            goldExchangeDailyTimeRewards.put(times, RewardTemplate.readListFromText(goldExchangeDailyTimeRewardArr[1]));
        }
        this.goldExchangeConsumeCurrency = Integer.parseInt(map.get("goldExchangeConsumeCurrency"));
        this.goldExchangeGetCurrency = Integer.parseInt(map.get("goldExchangeGetCurrency"));
    }
}

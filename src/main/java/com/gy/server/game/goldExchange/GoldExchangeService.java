package com.gy.server.game.goldExchange;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.game.common.ConstantConfigReader;
import com.gy.server.game.currency.Currency;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.exp.ExpService;
import com.gy.server.game.function.Function;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.liberty.LibertyHelper;
import com.gy.server.game.liberty.effect.LibertyType;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.reddot.RedDot;
import com.gy.server.game.service.Service;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbGoldExchange;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.MathUtil;

import java.util.*;

/**
 * 元宝兑换 钱庄
 *
 * <AUTHOR> - [Created on 2023/3/30 17:17]
 */
public class GoldExchangeService extends PlayerPacketHandler implements Service {

    private static GoldExchangeConstant constant;

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        Map<String, String> map = ConstantConfigReader.read(ConfigFile.goldexchange_const);
        constant = new GoldExchangeConstant(map);
    }

    public static GoldExchangeConstant getConstant() {
        return constant;
    }

    /**
     * 元宝兑换信息查询
     */
    @Handler(PtCode.GOLD_EXCHANGE_INFO_CLIENT)
    private void info(Player player, long time) {
        PbProtocol.GoldExchangeInfoRst.Builder rst = PbProtocol.GoldExchangeInfoRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        logic:
        {
            if (Function.goldExchange.isNotOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            PlayerGoldExchangeModel goldExchangeModel = player.getModel(PlayerModelEnums.goldExchange);
            rst.setTodayTimes(goldExchangeModel.getTodayTimes());
            rst.addAllDailyTimesDraw(goldExchangeModel.getDailyTimesDrawSet());
        }
        player.send(PtCode.GOLD_EXCHANGE_INFO_SERVER, rst.build(), time);
    }

    /**
     * 元宝兑换
     */
    @Handler(PtCode.GOLD_EXCHANGE_CLIENT)
    private void exchange(Player player, PbProtocol.GoldExchangeReq req, long time) {
        PbProtocol.GoldExchangeRst.Builder rst = PbProtocol.GoldExchangeRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        logic:
        {
            if (Function.goldExchange.isNotOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            int count = req.getCount();
            if (count != 1 && count != 10) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            PlayerGoldExchangeModel goldExchangeModel = player.getModel(PlayerModelEnums.goldExchange);
            int remainTimes = goldExchangeModel.getRemainTimes();
            if (remainTimes <= 0) {
                rst.setResult(Text.genServerRstInfo(Text.元宝兑换次数不足));
                break logic;
            }
            // 如果小于10次 则剩余多少次全部使用
            if (count == 10 && remainTimes < count) {
                count = remainTimes;
            }
            int baseReward = (int) ExpService.getExp(constant.quantityObtained, player.getLevel());
            int todayTimes = goldExchangeModel.getTodayTimes();
            // 计算消耗
            List<PbGoldExchange.GoldExchangeReward> exchangeRewardList = new ArrayList<>();
            int totalCost = 0;
            int totalAdd = 0;
            for (int i = 1; i <= count; i++) {
                // 免费次数是否已使用
                if (todayTimes >= constant.goldExchangeDailyFreeTime) {
                    int payTimes = todayTimes + 1 - constant.goldExchangeDailyFreeTime;
                    // 本次消耗
                    Integer cost = constant.goldExchangeTimeConsume.headMap(payTimes, true).lastEntry().getValue();
                    totalCost += cost;
                }
                // 本次获得
                Integer per = MathUtil.weightRandom(constant.criticalHitProbability);
                int addValue = baseReward * per;
                totalAdd += addValue;
                exchangeRewardList.add(PbGoldExchange.GoldExchangeReward.newBuilder()
                        .setCritNum(per)
                        .setReward(Reward.create(Currency.getCurrencyById(constant.goldExchangeGetCurrency), addValue).writeToPb()).build());
                todayTimes++;
            }
            if (totalCost > 0) {
                Currency costCurrency = Currency.getCurrencyById(constant.goldExchangeConsumeCurrency);
                Reward cost = Reward.create(costCurrency, totalCost);
                if (cost.check(player) != -1) {
                    rst.setResult(Text.genServerRstInfo(Text.消耗不足));
                    break logic;
                }
                cost.remove(player, BehaviorType.goldExchangeCost);
            }

            Currency addCurrency = Currency.getCurrencyById(constant.goldExchangeGetCurrency);
            Reward add = Reward.create(addCurrency, totalAdd);

            //特权处理
            LibertyHelper.checkCurrencyRewardLiberty(LibertyType.goldExchangeRate, player, add);

            add.add(player, BehaviorType.goldExchangeAdd);

            goldExchangeModel.setTodayTimes(todayTimes);
            // 添加Log
            GoldExchangeLogInfo goldExchangeLogInfo = new GoldExchangeLogInfo();
            goldExchangeLogInfo.setCostNum(totalCost);
            goldExchangeLogInfo.setCount(count);
            goldExchangeLogInfo.setRewardNum(totalAdd);
            goldExchangeLogInfo.setTimestamp(ServerConstants.getCurrentTimeMillis());
            goldExchangeModel.getExchangeLogInfoList().add(goldExchangeLogInfo);
            rst.addAllReward(exchangeRewardList);
            rst.setTodayTimes(todayTimes);

            player.postEvent(PlayerEventType.goldExchange, count);

            //同步红点
            RedDot.bankExchangeFreeTime.sync(player);
            RedDot.bankExchangeReward.sync(player);
        }
        player.send(PtCode.GOLD_EXCHANGE_SERVER, rst.build(), time);
    }

    /**
     * 元宝累计次数奖励领取
     */
    @Handler(PtCode.GOLD_EXCHANGE_TIMES_CLIENT)
    private void timesDraw(Player player, PbProtocol.GoldExchangeTimesReq req, long time) {
        PbProtocol.GoldExchangeTimesRst.Builder rst = PbProtocol.GoldExchangeTimesRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        logic:
        {
            if (Function.goldExchange.isNotOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            int times = req.getTimes();
            List<RewardTemplate> rewardTemplates = constant.goldExchangeDailyTimeRewards.get(times);
            if (Objects.isNull(rewardTemplates)) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            PlayerGoldExchangeModel goldExchangeModel = player.getModel(PlayerModelEnums.goldExchange);
            if (goldExchangeModel.getDailyTimesDrawSet().contains(times)) {
                rst.setResult(Text.genServerRstInfo(Text.元宝兑换累计次数奖励已领取));
                break logic;
            }
            if (goldExchangeModel.getTodayTimes() < times) {
                rst.setResult(Text.genServerRstInfo(Text.元宝兑换次数不足));
                break logic;
            }
            goldExchangeModel.getDailyTimesDrawSet().add(times);
            List<Reward> rewards = Reward.addFromTemplates(rewardTemplates, player, BehaviorType.goldExchangeTimes);
            rst.addAllDailyTimesDraw(goldExchangeModel.getDailyTimesDrawSet())
                    .addAllReward(Reward.writeCollectionToPb(rewards));

            RedDot.bankExchangeReward.sync(player);
        }
        player.send(PtCode.GOLD_EXCHANGE_TIMES_SERVER, rst.build(), time);
    }

    /**
     * 元宝兑换记录
     */
    @Handler(PtCode.GOLD_EXCHANGE_LOG_CLIENT)
    private void log(Player player, long time) {
        PbProtocol.GoldExchangeLogRst.Builder rst = PbProtocol.GoldExchangeLogRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        logic:
        {
            if (Function.goldExchange.isNotOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            PlayerGoldExchangeModel goldExchangeModel = player.getModel(PlayerModelEnums.goldExchange);
            List<GoldExchangeLogInfo> exchangeLogInfoList = goldExchangeModel.getExchangeLogInfoList();
            Collections.sort(exchangeLogInfoList);
            for (GoldExchangeLogInfo goldExchangeLogInfo : exchangeLogInfoList) {
                rst.addExchangeLog(goldExchangeLogInfo.genPb());
            }
        }
        player.send(PtCode.GOLD_EXCHANGE_LOG_SERVER, rst.build(), time);
    }
}

package com.gy.server.game.statistics;

import java.text.NumberFormat;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.log.CommonLogger;
import com.gy.server.core.thread.AbstractRunner;
import com.gy.server.utils.MathUtil;

/**
 * <AUTHOR> - [Create on 2020/09/10 10:07]
 */
public final class PlayerOperationCountManager extends AbstractRunner {

    private static final PlayerOperationCountManager INSTANCE = new PlayerOperationCountManager();
    private static final int FREQUENCY = 10;

    /**
     * 用户操作数，<playerId: 操作数>
     */
    private static final Map<Long, Long> PLAYER_OPERATION_COUNTS = new ConcurrentHashMap<>();

    private static LocalDateTime nextStatisticsTime = ServerConstants.getCurrentTimeLocalDateTime().plusMinutes(FREQUENCY);

    private PlayerOperationCountManager() {}

    public static PlayerOperationCountManager getInstance() {
        return INSTANCE;
    }

    public static void addOperation(long playerId) {
        Long count = PLAYER_OPERATION_COUNTS.getOrDefault(playerId, 0L);
        PLAYER_OPERATION_COUNTS.put(playerId, ++count);
    }

    @Override
    public String getRunnerName() {
        return "PlayerOperationCountManager";
    }

    @Override
    protected void subRunnerExecute() throws Exception {
        LocalDateTime now = ServerConstants.getCurrentTimeLocalDateTime();
        if (!now.isBefore(nextStatisticsTime)) {
            nextStatisticsTime = now.plusMinutes(FREQUENCY);

            long totalCount = 0;
            int playerCount = 0;
            for (Map.Entry<Long, Long> entry : PLAYER_OPERATION_COUNTS.entrySet()) {
                totalCount += entry.getValue();
                playerCount++;
            }
            PLAYER_OPERATION_COUNTS.clear();

            NumberFormat numberFormat = NumberFormat.getNumberInstance();
            numberFormat.setMaximumFractionDigits(2);
            String everyMinuteFormat = numberFormat.format(totalCount / 1.0 / FREQUENCY / MathUtil.max(playerCount, 1));

            CommonLogger.warn(String.format("totalOperationCount:%s|playerCount:%s|everyMinuteOperationCount:%s",
                    totalCount,
                    playerCount,
                    everyMinuteFormat
            ));
        }
    }

    @Override
    public long getRunnerInterval() {
        return 2000;
    }

}

package com.gy.server.game.secretRealm.async;

import java.util.List;
import java.util.Objects;

import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.combat.CombatManager;
import com.gy.server.game.combat.StageType;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.secretRealm.SecretRealmService;
import com.gy.server.game.secretRealm.stage.SecretRealmStage;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;

/**
 * 秘境采集攻打战斗
 * <AUTHOR> - [Created on 2023-05-23 10:38]
 */
public class SecretRealmCollectBattleAsync extends AsyncCall {

    Player player;
    int mapId;
    String point;
    long defPlayerId;
    List<HeroUnit> defs;
    StageType stageType = StageType.secretRealmCollect;
    String defName = "";
    LineupType atkLineupType;
    LineupType defLineupType;
    long time;

    public SecretRealmCollectBattleAsync(Player player, int mapId, String point, long defPlayerId
            , LineupType atkLineupType, LineupType defLineupType, long time){
        this.player = player;
        this.mapId = mapId;
        this.point = point;
        this.defPlayerId = defPlayerId;
        this.atkLineupType = atkLineupType;
        this.defLineupType = defLineupType;
        this.time = time;
    }


    @Override
    public void asyncExecute() {
        Player defPlayer = PlayerManager.getPlayer(defPlayerId);
        if(Objects.nonNull(defPlayer)){
            defs = defPlayer.getLineupModel().createHeroUnits(stageType, defLineupType);
        }
        defName = defPlayer.getName();
    }

    @Override
    public void execute() {
        PbProtocol.SecretRealmCollectBattleRst.Builder rst = PbProtocol.SecretRealmCollectBattleRst.newBuilder().setResult(Text.genOkServerRstInfo());
        List<HeroUnit> atks = player.getLineupModel().createHeroUnits(stageType, atkLineupType);

        //初始化战斗
        SecretRealmStage stage = new SecretRealmStage(player, atks, defPlayerId, defs
                , mapId, point, stageType,false, SecretRealmService.getCollectionScene(), atkLineupType, defLineupType);
        stage.init();
        CombatManager.combatPrepare(stage);
        rst.setStage(stage.genAbstractPb());

        player.send(PtCode.SECRET_REALM_COLLECT_BATTLE_RST, rst.build(), time);


    }
}

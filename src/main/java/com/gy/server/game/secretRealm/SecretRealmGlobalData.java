package com.gy.server.game.secretRealm;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.event.ServerEvent;
import com.gy.server.game.event.ServerEventHandler;
import com.gy.server.game.event.ServerEventType;
import com.gy.server.game.global.GlobalData;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueDataInterface;
import com.gy.server.game.league.bean.LeagueMember;
import com.gy.server.game.secretRealm.bean.SecretRealmCollectInfo;
import com.gy.server.game.secretRealm.bean.SecretRealmMapInfo;
import com.gy.server.game.secretRealm.bean.SecretRealmMapLeagueInfo;
import com.gy.server.game.secretRealm.bean.SecretRealmPlayerCollectInfo;
import com.gy.server.packet.PbSecretRealm;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.ttlike.server.tl.baselib.serialize.world.SecretRealmGlobalDb;

/**
 * 帮派秘境信息存储
 * <AUTHOR> - [Created on 2023-05-12 17:57]
 */
public class SecretRealmGlobalData extends GlobalData implements LeagueDataInterface,ServerEventHandler {

    private static final ServerEventType[] eventTypes = new ServerEventType[]{ServerEventType.day5clock};
    /**
     * 秘境地图信息
     * key：mapId
     *  value:mapInfo
     */
    private Map<Integer, SecretRealmMapInfo> mapInfos = new ConcurrentHashMap<>();

    /**
     * 地图领土联盟信息
     * key：leagueId
     *  value:leagueInfo
     */
    Map<Long, SecretRealmMapLeagueInfo> mapLeagueInfos = new ConcurrentHashMap<>();

    /**
     * 地图收集玩家信息
     * key：playerId
     *  key:mapId_point
     *   value：玩家收集信息
     */
    Map<Long, SecretRealmPlayerCollectInfo> playerCollectInfos = new HashMap<>();


    /**
     * 正在进攻其他联盟的玩家id
     */
    private Map<Long, Map<Integer, String>> attackOtherPlayerInfos = new ConcurrentHashMap<>();

    /**
     * 参与排行榜的地图id
     */
    private List<Integer> rankMapIds = new ArrayList<>();

    public Map<Long, Map<Integer, String>> getAttackOtherPlayerInfos() {
        return attackOtherPlayerInfos;
    }
    public Map<Integer, String> getAttackOtherPlayerInfo(long playerId) {
        if(!attackOtherPlayerInfos.containsKey(playerId)){
            attackOtherPlayerInfos.put(playerId, new HashMap<>());
        }
        return attackOtherPlayerInfos.get(playerId);
    }

    public void setAttackOtherPlayerInfos(Map<Long, Map<Integer, String>> attackOtherPlayerInfos) {
        this.attackOtherPlayerInfos = attackOtherPlayerInfos;
    }

    /**
     * 获得秘境地图信息
     * @param mapId 秘境地图id
     * @return 秘境地图信息
     */
    public SecretRealmMapInfo getMapInfo(int mapId){
        initMapInfo(mapId);
        return mapInfos.get(mapId);
    }

    public Map<Integer, SecretRealmMapInfo> getMapInfos() {
        return mapInfos;
    }

    public void setMapInfos(Map<Integer, SecretRealmMapInfo> mapInfos) {
        this.mapInfos = mapInfos;
    }

    public Map<Long, SecretRealmMapLeagueInfo> getMapLeagueInfos() {
        return mapLeagueInfos;
    }

    public void setMapLeagueInfos(Map<Long, SecretRealmMapLeagueInfo> mapLeagueInfos) {
        this.mapLeagueInfos = mapLeagueInfos;
    }

    public Map<Long, SecretRealmPlayerCollectInfo> getPlayerCollectInfos() {
        return playerCollectInfos;
    }

    public SecretRealmPlayerCollectInfo getPlayerCollectInfo(long playerId) {
        if(!playerCollectInfos.containsKey(playerId)){
            SecretRealmPlayerCollectInfo collectInfo = new SecretRealmPlayerCollectInfo();
            collectInfo.setPlayerId(playerId);
            playerCollectInfos.put(playerId, collectInfo);
        }
        return playerCollectInfos.get(playerId);
    }

    /**
     * 初始化地图信息
     * @param mapId 秘境地图id
     */
    public void initMapInfo(int mapId){
        if(!mapInfos.containsKey(mapId) && SecretRealmService.mapTemplates.containsKey(mapId)){
            SecretRealmMapInfo mapInfo = new SecretRealmMapInfo();
            mapInfo.setMapId(mapId);
            mapInfos.put(mapId, mapInfo);
            //刷新采集点
            SecretRealmService.collectRefresh(mapId);
        }
    }

    public List<Integer> getRankMapIds() {
        return rankMapIds;
    }

    public void setRankMapIds(List<Integer> rankMapIds) {
        this.rankMapIds = rankMapIds;
    }

    public PbSecretRealm.SecretRealmMapInfo genSecretRealmMapInfoPb(int mapId){
        PbSecretRealm.SecretRealmMapInfo.Builder mapInfo = PbSecretRealm.SecretRealmMapInfo.newBuilder();
        mapInfo.setMapId(mapId);
        SecretRealmMapInfo secretRealmMapInfo = mapInfos.get(mapId);
        for (Long leagueId : secretRealmMapInfo.getMapGangInfos().values()) {
            SecretRealmMapLeagueInfo secretRealmMapLeagueInfo = mapLeagueInfos.get(leagueId);
            mapInfo.addGangInfos(secretRealmMapLeagueInfo.genPb());
        }
        for (SecretRealmCollectInfo collectInfo : secretRealmMapInfo.getMapCollectInfos().values()) {
            mapInfo.addCollectInfos(collectInfo.genPb());
        }
        return mapInfo.build();
    }

    public void day5refresh(){
        if(ServerConstants.getCurrentTimeLocalDateTime().getDayOfWeek().getValue() == 1){
            for (SecretRealmPlayerCollectInfo collectInfo : playerCollectInfos.values()) {
                collectInfo.setWeekCollectTime(0);
                collectInfo.setDayCollectTime(0);
            }
        }else{
            for (SecretRealmPlayerCollectInfo collectInfo : playerCollectInfos.values()) {
                collectInfo.setDayCollectTime(0);
            }
        }
    }

    @Override
    public void readFromPb(byte[] bytes) throws Exception {
        SecretRealmGlobalDb secretRealmGlobalDb = PbUtilCompress.decode(SecretRealmGlobalDb.class, bytes);
        if(Objects.nonNull(secretRealmGlobalDb)){
            for (SecretRealmGlobalDb.SecretRealmMapInfoDb mapInfoDb : secretRealmGlobalDb.getMapInfos().values()) {
                SecretRealmMapInfo mapInfo = new SecretRealmMapInfo();
                mapInfo.readFromPb(mapInfoDb);
                this.mapInfos.put(mapInfo.getMapId(), mapInfo);
            }
            for (SecretRealmGlobalDb.SecretRealmMapLeagueInfoDb leagueInfoDb : secretRealmGlobalDb.getMapLeagueInfos().values()) {
                SecretRealmMapLeagueInfo leagueInfo = new SecretRealmMapLeagueInfo();
                leagueInfo.readFromPb(leagueInfoDb);
                this.mapLeagueInfos.put(leagueInfo.getLeagueId(), leagueInfo);
            }
            for (SecretRealmGlobalDb.SecretRealmPlayerCollectInfoDb collectInfoDb : secretRealmGlobalDb.getPlayerCollectInfos().values()) {
                SecretRealmPlayerCollectInfo collectInfo = new SecretRealmPlayerCollectInfo();
                collectInfo.readFromPb(collectInfoDb);
                this.playerCollectInfos.put(collectInfo.getPlayerId(), collectInfo);
            }

            for (SecretRealmGlobalDb.SecretRealmAttackOtherPlayerInfoDb attackOtherPlayerInfo : secretRealmGlobalDb.getAttackOtherPlayerInfos()) {
                if(!this.attackOtherPlayerInfos.containsKey(attackOtherPlayerInfo.getPlayerId())){
                    this.attackOtherPlayerInfos.put(attackOtherPlayerInfo.getPlayerId(), new HashMap<>());
                }
                this.attackOtherPlayerInfos.get(attackOtherPlayerInfo.getPlayerId()).put(attackOtherPlayerInfo.getMapId(), attackOtherPlayerInfo.getGangPoint());
            }
            this.rankMapIds = secretRealmGlobalDb.getRankMapIds();

        }
    }

    @Override
    public byte[] writeToPb() {
        SecretRealmGlobalDb secretRealmGlobalDb = new SecretRealmGlobalDb();
        for (SecretRealmMapInfo mapInfo : this.mapInfos.values()) {
            SecretRealmGlobalDb.SecretRealmMapInfoDb secretRealmMapInfoDb = mapInfo.writeToPb();
            secretRealmGlobalDb.getMapInfos().put(secretRealmMapInfoDb.getMapId(), secretRealmMapInfoDb);
        }
        for (SecretRealmMapLeagueInfo leagueInfo : this.mapLeagueInfos.values()) {
            SecretRealmGlobalDb.SecretRealmMapLeagueInfoDb secretRealmMapLeagueInfoDb = leagueInfo.writeToPb();
            secretRealmGlobalDb.getMapLeagueInfos().put(secretRealmMapLeagueInfoDb.getLeagueId(), secretRealmMapLeagueInfoDb);
        }
        for (SecretRealmPlayerCollectInfo collectInfo : this.playerCollectInfos.values()) {
            SecretRealmGlobalDb.SecretRealmPlayerCollectInfoDb secretRealmPlayerCollectInfoDb = collectInfo.writeToPb();
            secretRealmGlobalDb.getPlayerCollectInfos().put(secretRealmPlayerCollectInfoDb.getPlayerId(), secretRealmPlayerCollectInfoDb);
        }
        for (long playerId : this.attackOtherPlayerInfos.keySet()) {
            Map<Integer, String> atkInfos = this.attackOtherPlayerInfos.get(playerId);
            atkInfos.forEach((k, v)->{
                SecretRealmGlobalDb.SecretRealmAttackOtherPlayerInfoDb db = new SecretRealmGlobalDb.SecretRealmAttackOtherPlayerInfoDb();
                db.setPlayerId(playerId);
                db.setMapId(k);
                db.setGangPoint(v);
                secretRealmGlobalDb.getAttackOtherPlayerInfos().add(db);
            });
        }
        secretRealmGlobalDb.setRankMapIds(rankMapIds);
        return PbUtilCompress.encode(secretRealmGlobalDb);
    }

    @Override
    public ServerEventType[] getEventTypes() {
        return eventTypes;
    }

    @Override
    public void handle(ServerEvent event) {
        switch (event.getEventType()){
            case day5clock:{
                day5refresh();
                break;
            }
        }
    }


    @Override
    public boolean canMerge(long leagueId) {
        return true;
    }

    @Override
    public void merge(League selfLeague, League targetLeague) {
        removeLeague(targetLeague);

        //同原来退出规则，保留玩家驻守采集点与防守队伍
    }

    @Override
    public void removeLeague(League league) {
        SecretRealmManager.removeLeague(league.getLeagueId());
    }

    @Override
    public void quitLeague(long pid) {

    }
}

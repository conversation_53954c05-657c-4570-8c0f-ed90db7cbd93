package com.gy.server.game.secretRealm.async;

import java.util.Objects;

import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.currency.Currency;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.secretRealm.stage.SecretRealmStageAfter;

/**
 * 秘境扣除行动力异步处理
 * <AUTHOR> - [Created on 2023-05-25 10:18]
 */
public class SecretRealmReduceDurableAsync extends AsyncCall {

    Player player;
    Player atkPlayer;
    long playerId;
    int mapId;
    String point;
    boolean isAtk;
    Reward defCost;
    boolean isWin;
    LineupType atkLineupType;
    LineupType defLineupType;

    public SecretRealmReduceDurableAsync(int mapId, String point, long playerId, Reward defCost, boolean isAtk
        , boolean isWin, Player atkPlayer, LineupType atkLineupType, LineupType defLineupType){
        this.mapId = mapId;
        this.point = point;
        this.playerId = playerId;
        this.isAtk = isAtk;
        this.defCost = defCost;
        this.isWin = isWin;
        this.atkPlayer = atkPlayer;
        this.atkLineupType = atkLineupType;
        this.defLineupType = defLineupType;
    }

    @Override
    public void asyncExecute() {
        player = PlayerManager.getPlayer(playerId);
    }

    @Override
    public void execute() {
        if(Objects.nonNull(player)){
            //扣除防守方行动力
            defCost.remove(player, isAtk ? BehaviorType.defGangAttackerAfter : BehaviorType.attackGangDefAfter);
            //检测是否还有行动力
            if(player.getCurrencyModel().getCurrency(Currency.actionPower) <= 0){
                SecretRealmStageAfter.secretRealmClear(mapId, point, playerId, isAtk, atkLineupType, defLineupType);
            }
            if(!isAtk){
                if(isWin){
                }else{
                }
            }else{
                if(isWin){
                }else{

                    //减少防守方剩余防守次数
                    player.getSecretRealmModel().reduceSurplusDefTimes();
                    if(player.getSecretRealmModel().getSurplusDefTimes() <= 0){
                        //清除防守军
                        SecretRealmStageAfter.secretRealmClear(mapId, point, playerId, isAtk, atkLineupType, defLineupType);
                    }
                }
            }

            player.saveOnce();
        }
    }
}

package com.gy.server.game.secretRealm.bean;

import com.gy.server.game.lineup.LineupType;

/**
 * 进攻信息
 * <AUTHOR> - [Created on 2024-03-21 10:40]
 */
public class SecretRealmDefInfo {

    //玩家id
    private long playerId;
    //进攻开始时间
    private long defStartTime;
    //展示头像
    private volatile int heroTempId;
    //布阵
    private int lineupType;

    public int getLineupType() {
        return lineupType;
    }

    public void setLineupType(int lineupType) {
        this.lineupType = lineupType;
    }

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public int getHeroTempId() {
        return heroTempId;
    }

    public void setHeroTempId(int heroTempId) {
        this.heroTempId = heroTempId;
    }

    public long getDefStartTime() {
        return defStartTime;
    }

    public void setDefStartTime(long defStartTime) {
        this.defStartTime = defStartTime;
    }
}

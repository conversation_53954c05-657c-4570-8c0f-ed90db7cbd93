package com.gy.server.game.secretRealm;

import java.util.Objects;

import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueDataInterface;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModel;
import com.gy.server.game.player.event.PlayerEvent;
import com.gy.server.game.player.event.PlayerEventHandler;
import com.gy.server.game.player.event.PlayerEventType;
import com.ttlike.server.tl.baselib.serialize.player.PlayerBlobDb;
import com.ttlike.server.tl.baselib.serialize.secretRealm.SecretRealmModelDb;

import static com.gy.server.game.player.event.PlayerEventType.*;

/**
 * 联盟-秘境
 * <AUTHOR> - [Created on 2023-05-18 19:30]
 */
public class PlayerSecretRealmModel extends PlayerModel implements PlayerEventHandler, LeagueDataInterface {

    private static final PlayerEventType[] eventTypes = new PlayerEventType[]{registerAfter
            , day5Refresh, passInstance, passMission, joinLeague};

    /**
     * 狂暴次数
     */
    private int rageTime;

    //剩余防守次数
    private int surplusDefTimes;

    //疲劳结束时间
    private long tiredEndTime;

    public PlayerSecretRealmModel(Player player) {
        super(player);
    }

    public int getRageTime() {
        return rageTime;
    }

    public void setRageTime(int rageTime) {
        this.rageTime = rageTime;
    }

    public void reduceRageTime(){
        this.rageTime--;
    }

    public long getTiredEndTime() {
        return tiredEndTime;
    }

    public void setTiredEndTime(long tiredEndTime) {
        this.tiredEndTime = tiredEndTime;
    }

    public void day5Refresh(){
        rageTime = SecretRealmService.getViolentNum();
        surplusDefTimes = SecretRealmService.getDefensiveTimes();
    }

    public int getSurplusDefTimes() {
        return surplusDefTimes;
    }

    public void setSurplusDefTimes(int surplusDefTimes) {
        this.surplusDefTimes = surplusDefTimes;
    }

    public void reduceSurplusDefTimes(){
        this.surplusDefTimes--;
    }

    @Override
    public PlayerEventType[] getEventTypes() {
        return eventTypes;
    }

    @Override
    public void handle(PlayerEvent event) {
        switch (event.getEventType()){
            case registerAfter:
            case day5Refresh:{
                day5Refresh();
                break;
            }
//            case passInstance:{
            case passMission:{
                //检测福灵榜
                int missionId = event.getParam(0);
                SecretRealmManager.checkRank(missionId);

                //检查系统帮派自动创建大阵
                checkSystemLeague();
                break;
            }
            case joinLeague:{
                checkSystemLeague();
                break;
            }
        }

    }

    /**
     * 检查系统帮派自动创建大阵
     */
    private void checkSystemLeague(){
        //检查系统帮派自动创建大阵
        League league = LeagueManager.getLeagueByPlayerId(getPlayerId());
        if(league!= null){
            //检查系统公会自动创建大阵
            if(league.isSystemLeague()){
                SecretRealmService.autoCreateGangCheck(league, getPlayer());
            }
        }
    }


    @Override
    protected void loadData(PlayerBlobDb playerBlob) {
        SecretRealmModelDb secretRealmModelDb = playerBlob.getSecretRealmModelDb();
        if(Objects.nonNull(secretRealmModelDb)){
            this.rageTime = secretRealmModelDb.getRageTime();
            this.surplusDefTimes = secretRealmModelDb.getSurplusDefTimes();
            this.tiredEndTime = secretRealmModelDb.getTiredEndTime();
        }
    }

    @Override
    protected void saveData(PlayerBlobDb playerBlob) {
        SecretRealmModelDb modelDb = new SecretRealmModelDb();
        modelDb.setRageTime(rageTime);
        modelDb.setSurplusDefTimes(surplusDefTimes);
        modelDb.setTiredEndTime(tiredEndTime);
        playerBlob.setSecretRealmModelDb(modelDb);
    }

    @Override
    public boolean canMerge(long leagueId) {
        return true;
    }

    @Override
    public void merge(League selfLeague, League targetLeague) {
    }

    @Override
    public void removeLeague(League league) {

    }

    @Override
    public void quitLeague(long pid) {
    }
}

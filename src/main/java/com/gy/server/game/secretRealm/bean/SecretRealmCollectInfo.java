package com.gy.server.game.secretRealm.bean;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.currency.Currency;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.mail.MailManager;
import com.gy.server.game.mail.MailType;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.secretRealm.SecretRealmGlobalData;
import com.gy.server.game.secretRealm.SecretRealmManager;
import com.gy.server.game.secretRealm.SecretRealmService;
import com.gy.server.game.secretRealm.template.SecretRealmCollectionTemplate;
import com.gy.server.game.text.Text;
import com.gy.server.game.text.TextParamText;
import com.gy.server.packet.PbCommons;
import com.gy.server.packet.PbSecretRealm;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.serialize.world.SecretRealmGlobalDb;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

/**
 * 秘境地图玩家采集点信息
 * <AUTHOR> - [Created on 2023-05-22 18:09]
 */
public class SecretRealmCollectInfo {

    /**
     * 地图id
     */
    private int mapId;
    /**
     * 收集点位信息
     */
    private String collectPoint;

    /**
     * 采集id
     */
    private int collectId;

    /**
     * 开始采集时间
     */
    private long startTime;

    /**
     * 剩余奖励时间（用于二次采集）
     */
    private long surplusRewardTime;

    /**
     * 消失时间（用于被攻打后无人采集消失）
     */
    private long disTime;

    /**
     * 占领玩家id
     */
    private long occupyPlayerId;

    private int lineupType;

    private int showHeroTempId;

    public int getLineupType() {
        return lineupType;
    }

    public void setLineupType(int lineupType) {
        this.lineupType = lineupType;
    }

    public int getMapId() {
        return mapId;
    }

    public void setMapId(int mapId) {
        this.mapId = mapId;
    }

    public String getCollectPoint() {
        return collectPoint;
    }

    public void setCollectPoint(String collectPoint) {
        this.collectPoint = collectPoint;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getSurplusRewardTime() {
        return surplusRewardTime;
    }

    public void setSurplusRewardTime(long surplusRewardTime) {
        this.surplusRewardTime = surplusRewardTime;
    }

    public long getDisTime() {
        return disTime;
    }

    public void setDisTime(long disTime) {
        this.disTime = disTime;
    }

    public long getOccupyPlayerId() {
        return occupyPlayerId;
    }

    public void setOccupyPlayerId(long occupyPlayerId) {
        this.occupyPlayerId = occupyPlayerId;
    }

    public int getCollectId() {
        return collectId;
    }

    public void setCollectId(int collectId) {
        this.collectId = collectId;
    }

    public int getShowHeroTempId() {
        return showHeroTempId;
    }

    public void setShowHeroTempId(int showHeroTempId) {
        this.showHeroTempId = showHeroTempId;
    }

    public PbSecretRealm.SecretRealmCollectInfo genPb(){
        PbSecretRealm.SecretRealmCollectInfo.Builder builder = PbSecretRealm.SecretRealmCollectInfo.newBuilder();
        builder.setCollectPoint(collectPoint);
        builder.setStartTime(startTime);
        builder.setSurplusRewardTime(surplusRewardTime);
        builder.setDisTime(disTime);
        builder.setCollectId(collectId);
        builder.setMapId(mapId);
        PbSecretRealm.SecretRealmPlayerListInfo secretRealmPlayerBaseInfo = SecretRealmManager.buildPlayerListInfo(occupyPlayerId, 0, 0, showHeroTempId, lineupType);
        if(Objects.nonNull(secretRealmPlayerBaseInfo)){
            builder.setOccupyPlayer(secretRealmPlayerBaseInfo);
        }
        return builder.build();
    }

    public void readFromPb(SecretRealmGlobalDb.SecretRealmMapInfoDb.SecretRealmCollectInfoDb db) {
        this.mapId = db.getMapId();
        this.collectPoint = db.getCollectPoint();
        this.collectId = db.getCollectId();
        this.startTime = db.getStartTime();
        this.surplusRewardTime = db.getSurplusRewardTime();
        this.disTime = db.getDisTime();
        this.occupyPlayerId = db.getOccupyPlayerId();
        this.showHeroTempId = db.getShowHeroTempId();
        this.lineupType = db.getLineupType();

    }

    public SecretRealmGlobalDb.SecretRealmMapInfoDb.SecretRealmCollectInfoDb writeToPb() {
        SecretRealmGlobalDb.SecretRealmMapInfoDb.SecretRealmCollectInfoDb db = new SecretRealmGlobalDb.SecretRealmMapInfoDb.SecretRealmCollectInfoDb();
        db.setMapId(mapId);
        db.setCollectPoint(collectPoint);
        db.setCollectId(collectId);
        db.setStartTime(startTime);
        db.setSurplusRewardTime(surplusRewardTime);
        db.setDisTime(disTime);
        db.setOccupyPlayerId(occupyPlayerId);
        db.setShowHeroTempId(showHeroTempId);
        db.setLineupType(lineupType);
        return db;
    }

    public void settlement(boolean isBattle){
        SecretRealmGlobalData globalData = GlobalDataManager.getData(GlobalDataType.secretRealm);

        long betweenTime = ServerConstants.getCurrentTimeMillis() - getStartTime();
        long betweenMinute = betweenTime / DateTimeUtil.MillisOfMinute;//向下取整
        //走时间了
        if(betweenMinute > 0){
            SecretRealmPlayerCollectInfo playerCollectInfo = globalData.getPlayerCollectInfo(getOccupyPlayerId());
            League league = LeagueManager.getLeagueByPlayerId(getOccupyPlayerId());
            long weekCanCollectTime = SecretRealmService.getWeekLimitTime(league) - playerCollectInfo.getWeekCollectTime();
            long dayCanCollectTime = SecretRealmService.getDayLimitTime() - playerCollectInfo.getDayCollectTime();
            long realBetweenMinute = Math.min(Math.min(Math.min(betweenMinute, surplusRewardTime), weekCanCollectTime), dayCanCollectTime);
            //记录采集时间
            playerCollectInfo.clearPlayerCollect(mapId, collectPoint);

            long rewardNum = 0l;
            if(realBetweenMinute > 0){
                playerCollectInfo.addCollectTime(realBetweenMinute);
                //开始补发奖励
                SecretRealmCollectionTemplate collectionTemplate = SecretRealmService.collectionTemplates.get(getCollectId());
                List<Reward> rewardList = new ArrayList<>();
                rewardNum = collectionTemplate.collectEfficiency * realBetweenMinute;
                if(rewardNum > 0){
                    rewardList.add(Reward.create(Currency.getCurrencyById(SecretRealmService.getOre()), rewardNum));
                    MailType mailType = MailType.secretRealmCollect;
                    PbCommons.PbText titleText = Text.genText(mailType.getTitleId()).build();
                    PbCommons.PbText contentText = Text.genText(mailType.getContentId()
                            //采集时间
                            , new TextParamText(String.valueOf(realBetweenMinute))
                            //采集数量
                            , new TextParamText(String.valueOf(rewardNum))
                            //是否被攻打
                            , new TextParamText(String.valueOf(isBattle))
                    ).build();
                    MailManager.sendMail(
                            mailType,
                            getOccupyPlayerId(),
                            titleText,
                            contentText,
                            ServerConstants.getCurrentTimeMillis(),
                            rewardList);
                }
                //设置消失时间
                setDisTime(DateTimeUtil.toMillis(ServerConstants.getCurrentTimeLocalDateTime().plusMinutes(SecretRealmService.getCollectDisTime())));
                //修改奖励剩余时间
                setSurplusRewardTime(getSurplusRewardTime() - realBetweenMinute);
            }

            postRealEvent(getOccupyPlayerId(), (int) rewardNum);
        }

        setOccupyPlayerId(-1);
        setStartTime(-1);
    }


    public void postRealEvent(long playerId, int num){
        Player player = PlayerManager.getOnlinePlayer(playerId);
        if(player != null){
            postRealEvent(player, num);
        }else {
            ThreadPool.execute(() -> {
                Player temp = PlayerManager.getPlayer(playerId);
                if(temp != null){
                    postRealEvent(temp, num);
                }
            });
        }
    }

    public void postRealEvent(Player player, int num){
        player.postEvent(PlayerEventType.secretRealmCollect, num);
    }
}

package com.gy.server.game.secretRealm.template;

import java.util.Objects;

import com.gy.server.game.league.League;
import com.gy.server.game.league.template.LeagueTGTypeEnums;

/**
 * 秘境属性相关
 * <AUTHOR> - [Created on 2023-05-11 16:17]
 */
public class SecretRealmCampAttTemplate {

    //帮派等级
    public int id;
    //护盾值
    public int shieldLife;
    //拓展值（像素）
    public float development;

    public int getShieldLife(League league){
        return shieldLife + (Objects.isNull(league) ? 0 : league.getTGAddedNumInt(LeagueTGTypeEnums.提高帮派秘境大阵的护盾值上限, 0));
    }


}

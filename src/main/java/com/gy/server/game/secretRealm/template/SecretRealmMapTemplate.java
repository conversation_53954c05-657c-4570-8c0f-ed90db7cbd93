package com.gy.server.game.secretRealm.template;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 秘境地图配置
 * <AUTHOR> - [Created on 2023-05-11 16:17]
 */
public class SecretRealmMapTemplate {

    //秘境序号
    public int id;
    //进入秘境限制
    public int openChapter;
    //创建限制
    public int limitGangLev;
    //建帮点位
    public List<String> createGangPoints = new ArrayList<>();
    //采集点位
    public List<String> collectPoints = new ArrayList<>();
    //采集物刷新
    public Map<Integer, Integer> collectUpdates = new HashMap<>();
    //福灵榜挂机收益
    public String hangUpProfit;
    public String hangUpTribute;




}

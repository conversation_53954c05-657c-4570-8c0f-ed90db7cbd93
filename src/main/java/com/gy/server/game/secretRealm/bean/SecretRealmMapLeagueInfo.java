package com.gy.server.game.secretRealm.bean;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.secretRealm.SecretRealmManager;
import com.gy.server.game.secretRealm.SecretRealmService;
import com.gy.server.packet.PbSecretRealm;
import com.ttlike.server.tl.baselib.serialize.world.SecretRealmGlobalDb;


/**
 * 秘境地图联盟信息
 * <AUTHOR> - [Created on 2023-05-15 11:35]
 */
public class SecretRealmMapLeagueInfo {

    //增长期（增长势力）
    public static final int status_increase = 0;
    //交叉期(增长停滞)
    public static final int status_cross = 1;
    //掉血期(进攻大阵效果)
    public static final int status_shed_blood = 2;
    //回血期(进攻大阵后效果)
    public static final int status_recover_blood = 3;
    //保护期(不可攻打)（当天内，势力值扣除到一定数值后，大阵进入免疫状态，次日系统日清除）
    public static final int status_protect = 6;
    //缩圈期(没有活跃)
    public static final int status_decrease = 7;
    //首次击破标记
    public static final int status_first_break = 8;

    //联盟id
    private long leagueId;

    //秘境地图id
    private int mapId;

    //地图坐标点
    private String gangPoint = "";

    //联盟势力半径，用于计算势力范围
    private volatile float radius;

    //入侵列表
    /**
     * 入侵列表
     * key:进攻玩家id
     *  value: 上次进攻时间， 固定伤害，开始进攻时间
     */
    private Map<Long, Map<Integer, SecretRealmAtkInfo>> attackPlayerIds = new ConcurrentHashMap<>();

    //驻守列表
    private Map<Long, Map<Integer, SecretRealmDefInfo>> defPlayerIds = new ConcurrentHashMap<>();

    //血量
    private volatile int hp;

    /**
     * 联盟状态
     * 使用位运算，含义从左到右：
     * 是否增长|是否交际|是否正在受击
     */
    private volatile int status;

    /**
     * 上次增长势力时间
     */
    private volatile long lastIncreaseRadiusTime;

    /**
     * 上次掉血时间
     */
    private volatile long lastShedBloodTime;

    /**
     * 天地守护结束时间(新buff加成时间)
     */
    private volatile long protectStateFinishTime;

    /**
     * 上次缩减势力时间
     */
    private volatile long lastDecreaseRadiusTime;

    /**
     * 上次受击结束时间
     */
    private volatile long lastAttackedFinishTime;

    /**
     * 上次回血时间
     */
    private volatile long lastRecoverBloodTime;

    /**
     * 创建据点时间
     */
    private long createTime;

    /**
     * 今日扣除势力值
     */
    private volatile long todayReduceForce;

    public long getTodayReduceForce() {
        return todayReduceForce;
    }

    public void setTodayReduceForce(long todayReduceForce) {
        this.todayReduceForce = todayReduceForce;
    }

    public SecretRealmMapLeagueInfo(){
    }
    public SecretRealmMapLeagueInfo(long createTime){
        this.createTime = createTime;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public long getLastRecoverBloodTime() {
        return lastRecoverBloodTime;
    }

    public void setLastRecoverBloodTime(long lastRecoverBloodTime) {
        this.lastRecoverBloodTime = lastRecoverBloodTime;
    }

    public long getLastAttackedFinishTime() {
        return lastAttackedFinishTime;
    }

    public void setLastAttackedFinishTime(long lastAttackedFinishTime) {
        this.lastAttackedFinishTime = lastAttackedFinishTime;
    }

    public long getLastDecreaseRadiusTime() {
        return lastDecreaseRadiusTime;
    }

    public void setLastDecreaseRadiusTime(long lastDecreaseRadiusTime) {
        this.lastDecreaseRadiusTime = lastDecreaseRadiusTime;
    }

    public long getProtectStateFinishTime() {
        return protectStateFinishTime;
    }

    public void setProtectStateFinishTime(long protectStateFinishTime) {
        this.protectStateFinishTime = protectStateFinishTime;
    }

    /**
     * 是否有buff加成
     */
    public boolean hasProtectStateBuff(){
        return protectStateFinishTime > ServerConstants.getCurrentTimeMillis();
    }

    /**
     * 获取联盟状态
     * @param statusType 状态类型
     * @return 结果是否
     */
    public boolean getStatus(int statusType){
        return (status & (1 << statusType)) > 0;
    }

    /**
     * 设置联盟状态
     * @param statusType 状态类型
     * @param isReal 是否为真 （1 or 0）
     */
    public void setStatus(int statusType, boolean isReal){
        if(isReal){
            status |= (1 << statusType);
        }else{
            status &= ~(1 << statusType);
        }
    }

    public long getLastIncreaseRadiusTime() {
        return lastIncreaseRadiusTime;
    }

    public void setLastIncreaseRadiusTime(long lastIncreaseRadiusTime) {
        this.lastIncreaseRadiusTime = lastIncreaseRadiusTime;
    }

    public long getLastShedBloodTime() {
        return lastShedBloodTime;
    }

    public void setLastShedBloodTime(long lastShedBloodTime) {
        this.lastShedBloodTime = lastShedBloodTime;
    }

    public long getLeagueId() {
        return leagueId;
    }

    public void setLeagueId(long leagueId) {
        this.leagueId = leagueId;
    }

    public int getMapId() {
        return mapId;
    }

    public void setMapId(int mapId) {
        this.mapId = mapId;
    }

    public String getGangPoint() {
        return gangPoint;
    }

    public void setGangPoint(String gangPoint) {
        this.gangPoint = gangPoint;
    }

    public float getRadius() {
        return radius;
    }

    public void setRadius(float radius) {
        this.radius = radius;
    }

    public Map<Long, Map<Integer, SecretRealmAtkInfo>> getAttackPlayerIds() {
        return attackPlayerIds;
    }
    public Map<Integer, SecretRealmAtkInfo> getAttackPlayerId(long playerId) {
        if(!attackPlayerIds.containsKey(playerId)){
            attackPlayerIds.put(playerId, new HashMap<>());
        }
        return attackPlayerIds.get(playerId);
    }

    public void checkAttackedFinish(){
        if(getAttackPlayerIds().size() <= 0){
            setLastAttackedFinishTime(ServerConstants.getCurrentTimeMillis());
            setStatus(SecretRealmMapLeagueInfo.status_shed_blood, false);
        }
    }

    public Map<Long, Map<Integer, SecretRealmDefInfo>> getDefPlayerIds() {
        return defPlayerIds;
    }

    public int getHp() {
        return hp;
    }

    public void setHp(int hp) {
        this.hp = hp;
    }

    public void reduceHp(int surplusReduceHp){
        this.hp -= surplusReduceHp;
        this.hp = Math.max(this.hp, 0);
    }

    public PbSecretRealm.SecretRealmGangInfo genPb(){
        PbSecretRealm.SecretRealmGangInfo.Builder builder = PbSecretRealm.SecretRealmGangInfo.newBuilder();
        builder.setGangPoint(gangPoint);
        builder.setLeagueId(leagueId);
        League leagueById = LeagueManager.getLeagueById(leagueId);
        if(Objects.nonNull(leagueById)){
            builder.setLeagueName(leagueById.getName());
            builder.setIconFrameId(leagueById.getIconFrameId());
            builder.setIconName(leagueById.getIconName());
            builder.setLeagueLevel(leagueById.getLevel());
        }
        builder.setRadius(radius);
        for (Long attackPlayerId : attackPlayerIds.keySet()) {
            Map<Integer, SecretRealmAtkInfo> lineupTypeSecretRealmAtkInfoMap = attackPlayerIds.get(attackPlayerId);
            for (Integer lineupType : lineupTypeSecretRealmAtkInfoMap.keySet()) {
                SecretRealmAtkInfo info = lineupTypeSecretRealmAtkInfoMap.get(lineupType);
                builder.addAttacks(SecretRealmManager.buildPlayerListInfo(attackPlayerId, info.getAtkStartTime(), info.realHurt(this), info.getHeroTempId(), info.getLineupType().getType()));
            }
        }
        builder.setShieldLife(hp);
        for (Long defPlayerId : defPlayerIds.keySet()) {
            Map<Integer, SecretRealmDefInfo> lineupTypeSecretRealmDefInfoMap = defPlayerIds.get(defPlayerId);
            for (Integer lineupType : lineupTypeSecretRealmDefInfoMap.keySet()) {
                SecretRealmDefInfo defPlayerInfo = lineupTypeSecretRealmDefInfoMap.get(lineupType);
                builder.addDefences(SecretRealmManager.buildPlayerListInfo(defPlayerId, defPlayerInfo.getDefStartTime(), 0, defPlayerInfo.getHeroTempId(), defPlayerInfo.getLineupType()));
            }
        }
        builder.setProtectStateFinishTime(getProtectStateFinishTime());
        return builder.build();
    }

    private static final String splitStr = "-";
    public void readFromPb(SecretRealmGlobalDb.SecretRealmMapLeagueInfoDb db) {
        this.leagueId = db.getLeagueId();
        this.mapId = db.getMapId();
        this.gangPoint = db.getGangPoint();
        this.radius = db.getRadius();
        for (SecretRealmGlobalDb.SecretRealmMapLeagueInfoDb.SecretRealmMapAttackPlayerInfoDb attackPlayerInfo : db.getAttackPlayerIds()) {
            SecretRealmAtkInfo atkInfo = new SecretRealmAtkInfo();
            long playerId = attackPlayerInfo.getPlayerId();
            atkInfo.setPlayerId(playerId);
            atkInfo.setAtkStartTime(attackPlayerInfo.getLastAttackHurtTime());
            atkInfo.setHurt(attackPlayerInfo.getHurtNum());
            atkInfo.setHeroTempId(attackPlayerInfo.getShowHeroTempId());
            atkInfo.setLineupType(LineupType.getType(attackPlayerInfo.getLineupType()));
            if(!this.attackPlayerIds.containsKey(playerId)){
                this.attackPlayerIds.put(playerId, new HashMap<>());
            }
            this.attackPlayerIds.get(playerId).put(attackPlayerInfo.getLineupType(), atkInfo);
        }
        List<SecretRealmGlobalDb.SecretRealmMapLeagueInfoDb.SecretRealmMapDefPlayerInfoDb> defPlayerInfos = db.getDefPlayerIds();
        for (SecretRealmGlobalDb.SecretRealmMapLeagueInfoDb.SecretRealmMapDefPlayerInfoDb defPlayerInfoDb : defPlayerInfos) {
            SecretRealmDefInfo defInfo = new SecretRealmDefInfo();
            defInfo.setDefStartTime(defPlayerInfoDb.getDefStartTime());
            defInfo.setLineupType(defPlayerInfoDb.getLineupType());
            defInfo.setHeroTempId(defPlayerInfoDb.getShowHeroTempId());
            long playerId = defPlayerInfoDb.getPlayerId();
            if(!this.defPlayerIds.containsKey(playerId)){
                this.defPlayerIds.put(playerId, new HashMap<>());
            }
            this.defPlayerIds.get(playerId).put(defPlayerInfoDb.getLineupType(), defInfo);
        }
        this.hp = db.getShieldLife();
        this.status = db.getStatus();
        this.lastIncreaseRadiusTime = db.getLastIncreaseRadiusTime();
        this.lastShedBloodTime = db.getLastShedBloodTime();
        this.protectStateFinishTime = db.getProtectStateFinishTime();
        this.lastDecreaseRadiusTime = db.getLastDecreaseRadiusTime();
        this.lastAttackedFinishTime = db.getLastAttackedTime();
        this.lastRecoverBloodTime = db.getLastRecoverBloodTime();

    }

    public SecretRealmGlobalDb.SecretRealmMapLeagueInfoDb writeToPb() {
        SecretRealmGlobalDb.SecretRealmMapLeagueInfoDb db = new SecretRealmGlobalDb.SecretRealmMapLeagueInfoDb();
        db.setLeagueId(leagueId);
        db.setMapId(mapId);
        db.setGangPoint(gangPoint);
        db.setRadius(radius);
        for (Long playerId : this.attackPlayerIds.keySet()) {
            Map<Integer, SecretRealmAtkInfo> lineupTypeSecretRealmAtkInfoMap = this.attackPlayerIds.get(playerId);
            for (SecretRealmAtkInfo atkInfo : lineupTypeSecretRealmAtkInfoMap.values()) {
                SecretRealmGlobalDb.SecretRealmMapLeagueInfoDb.SecretRealmMapAttackPlayerInfoDb infoDb = new SecretRealmGlobalDb.SecretRealmMapLeagueInfoDb.SecretRealmMapAttackPlayerInfoDb();
                infoDb.setLastAttackHurtTime(atkInfo.getAtkStartTime());
                infoDb.setHurtNum(atkInfo.getHurt());
                infoDb.setShowHeroTempId(atkInfo.getHeroTempId());
                infoDb.setLineupType(atkInfo.getLineupType().getType());
                infoDb.setPlayerId(playerId);
                db.getAttackPlayerIds().add(infoDb);
            }
        }
        for (Long playerId : this.defPlayerIds.keySet()) {
            for (SecretRealmDefInfo defInfo : this.defPlayerIds.get(playerId).values()) {
                SecretRealmGlobalDb.SecretRealmMapLeagueInfoDb.SecretRealmMapDefPlayerInfoDb defPlayerInfo = new SecretRealmGlobalDb.SecretRealmMapLeagueInfoDb.SecretRealmMapDefPlayerInfoDb();
                defPlayerInfo.setDefStartTime(defInfo.getDefStartTime());
                defPlayerInfo.setShowHeroTempId(defInfo.getHeroTempId());
                defPlayerInfo.setLineupType(defInfo.getLineupType());
                defPlayerInfo.setPlayerId(playerId);
                db.getDefPlayerIds().add(defPlayerInfo);
            }
        }
        db.setShieldLife(hp);
        db.setStatus(status);
        db.setLastIncreaseRadiusTime(lastIncreaseRadiusTime);
        db.setLastShedBloodTime(lastShedBloodTime);
        db.setProtectStateFinishTime(protectStateFinishTime);
        db.setLastDecreaseRadiusTime(lastDecreaseRadiusTime);
        db.setLastAttackedTime(lastAttackedFinishTime);
        db.setLastRecoverBloodTime(lastRecoverBloodTime);

        return db;
    }

    public void printStatus(){
        printIntBinary(status);
    }

    public static void printIntBinary(int num){
        for(int i = 31; i >= 0; i--){
            System.out.print((num & (1 << i)) == 0 ? "0" : "1");
        }
        System.out.println();
    }

    public long realHurt(long hurt){
        return hurt * (100 - SecretRealmService.getBearInjury() * getDefPlayerIds().size()) / 100;
    }

    public PbSecretRealm.SecretRealmGangDetailInfo genDetailInfo(){
        PbSecretRealm.SecretRealmGangDetailInfo.Builder detailInfo = PbSecretRealm.SecretRealmGangDetailInfo.newBuilder();
        detailInfo.setGangInfo(genPb());
        League leagueById = LeagueManager.getLeagueById(leagueId);
        detailInfo.setLeague(leagueById.genLeague());
        detailInfo.setMapId(mapId);
        return detailInfo.build();
    }

}

package com.gy.server.game.secretRealm.stage;

import java.util.*;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.combat.StageType;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.combat.unit.TeamUnit;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.packet.PbProtocol;

/**
 * 秘境采集战斗 （防守方需要记录收集时间）
 * 驱逐进攻战斗 (防守方需要扣除行动力)
 * 清除防守战斗 (防守方需要扣除行动力)
 * <AUTHOR> - [Created on 2023-05-23 10:43]
 */
public class SecretRealmStage extends AbstractStage {

    Player atkPlayer;
    List<HeroUnit> atks;
    long defPlayerId;
    List<HeroUnit> defs;
    int mapId;
    String point;
    StageType stageType;
    boolean isRage;
    int battleCollectId;
    LineupType atkLineupType;
    LineupType defLineupType;

    public SecretRealmStage(Player atkPlayer, List<HeroUnit> atks
            , long defPlayerId, List<HeroUnit> defs
            , int mapId, String point, StageType stageType, boolean isRage
            , int battleCollectId, LineupType atkLineupType, LineupType defLineupType){
        this.atkPlayer = atkPlayer;
        this.atks = atks;
        this.defPlayerId = defPlayerId;
        this.defs = defs;
        this.mapId = mapId;
        this.point = point;
        this.stageType = stageType;
        this.isRage = isRage;
        this.battleCollectId = battleCollectId;
        this.atkLineupType = atkLineupType;
        this.defLineupType = defLineupType;
    }

    @Override
    public void init() {
        TeamUnit attackerTeam = new TeamUnit(getNewId(), atks);
        attackerTeam.setAutoMode(atkPlayer.getPlayerId());
        TeamUnit defenderTeam = new TeamUnit(getNewId(), defs);
        defenderTeam.setAutoMode(defPlayerId);

        init(battleCollectId, stageType, attackerTeam, defenderTeam, atkLineupType);
    }

    @Override
    public void afterFinish() {
        notifyCombatSettlement(atkPlayer, null);

        SecretRealmStageAfter.deal(this, atkPlayer, defPlayerId, mapId, point, isRage, atkLineupType, defLineupType);

        PbProtocol.SecretRealmCombatNotify.Builder notify = PbProtocol.SecretRealmCombatNotify.newBuilder();
        notify.setAtkPlayerId(atkPlayer.getPlayerId());
        notify.setAtkLineupType(atkLineupType.getType());
        notify.setDefPlayerId(defPlayerId);
        notify.setDefLineupType(defLineupType.getType());
        notify.setIsWin(isWin());
        notify.setBattleTime(ServerConstants.getCurrentTimeMillis());
        int heroId = 0;
        long fightPower = 0;
        for (HeroUnit atk : atks) {
            if(atk.getFightingPower() > fightPower){
                fightPower = atk.getFightingPower();
                heroId = atk.getTid();
            }
        }
        notify.setAtkHeroId(heroId);
        heroId = 0;
        fightPower = 0;
        for (HeroUnit def : defs) {
            if(def.getFightingPower() > fightPower){
                fightPower = def.getFightingPower();
                heroId = def.getTid();
            }
        }
        notify.setDefHeroId(heroId);


        notifyCombat(notify.build());
    }

    private void notifyCombat(PbProtocol.SecretRealmCombatNotify notify){
        Set<Long> notifyOnlinePlayerIds = new HashSet<>();
        notifyOnlinePlayerIds.add(atkPlayer.getPlayerId());
        notifyOnlinePlayerIds.add(defPlayerId);
        League atkLeague = LeagueManager.getLeagueByPlayer(atkPlayer);
        if(Objects.nonNull(atkLeague)){
            for (Long playerId : atkLeague.getAllMember()) {
                if(PlayerManager.isOnline(playerId)){
                    notifyOnlinePlayerIds.add(playerId);
                }
            }
        }
        League defLeague = LeagueManager.getLeagueByPlayerId(defPlayerId);
        if(Objects.nonNull(defLeague)){
            for (Long playerId : defLeague.getAllMember()) {
                if(PlayerManager.isOnline(playerId)){
                    notifyOnlinePlayerIds.add(playerId);
                }
            }
        }
        for (Long notifyOnlinePlayerId : notifyOnlinePlayerIds) {
            Player onlinePlayer = PlayerManager.getOnlinePlayer(notifyOnlinePlayerId);
            if(Objects.nonNull(onlinePlayer)){
                onlinePlayer.send(PtCode.SECRET_REALM_COMBAT_NOTIFY, notify);
            }
        }
    }


}

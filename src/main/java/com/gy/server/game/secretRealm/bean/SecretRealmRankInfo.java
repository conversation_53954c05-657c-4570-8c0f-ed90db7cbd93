package com.gy.server.game.secretRealm.bean;

import java.util.Objects;

import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.packet.PbSecretRealm;
import com.ttlike.server.tl.baselib.serialize.world.SecretRealmGlobalDb;

/**
 * 排行榜信息
 * <AUTHOR> - [Created on 2023-05-31 18:19]
 */
public class SecretRealmRankInfo {

    long leagueId;
    private float radius;

    public SecretRealmRankInfo(){}
    public SecretRealmRankInfo(SecretRealmMapLeagueInfo leagueInfo){
        setLeagueId(leagueInfo.getLeagueId());
        setRadius(leagueInfo.getRadius());
    }

    public long getLeagueId() {
        return leagueId;
    }

    public void setLeagueId(long leagueId) {
        this.leagueId = leagueId;
    }

    public float getRadius() {
        return radius;
    }

    public void setRadius(float radius) {
        this.radius = radius;
    }

    public PbSecretRealm.SecretRealmRank genPb(){
        PbSecretRealm.SecretRealmRank.Builder builder = PbSecretRealm.SecretRealmRank.newBuilder();
        builder.setLeagueId(leagueId);
        League league = LeagueManager.getLeagueById(leagueId);
        builder.setName(Objects.isNull(league) ? "" : league.getName());
        builder.setRadius(radius);
        return builder.build();
    }

    public void readFromPb(SecretRealmGlobalDb.SecretRealmMapInfoDb.SecretRealmRankInfoDb db) {
        this.leagueId = db.getLeagueId();
        this.radius = db.getRadius();
    }

    public SecretRealmGlobalDb.SecretRealmMapInfoDb.SecretRealmRankInfoDb writeToPb() {
        SecretRealmGlobalDb.SecretRealmMapInfoDb.SecretRealmRankInfoDb db = new SecretRealmGlobalDb.SecretRealmMapInfoDb.SecretRealmRankInfoDb();
        db.setLeagueId(leagueId);
        db.setRadius(radius);
        return db;
    }
}

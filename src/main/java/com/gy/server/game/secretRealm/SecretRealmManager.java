package com.gy.server.game.secretRealm;

import java.time.LocalDateTime;
import java.util.*;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.delay.DelayTaskManager;
import com.gy.server.core.thread.AbstractRunner;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.league.log.LeagueLogType;
import com.gy.server.game.league.template.LeagueTGTypeEnums;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.secretRealm.bean.*;
import com.gy.server.game.secretRealm.template.SecretRealmCampAttTemplate;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbSecretRealm;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.time.DateTimeUtil;

import org.apache.commons.lang3.tuple.Pair;


/**
 * 帮派秘境
 * <AUTHOR> - [Created on 2023-05-11 18:02]
 */
public class SecretRealmManager extends AbstractRunner {


    @Override
    protected void subRunnerExecute() throws Exception {
        SecretRealmGlobalData globalData = GlobalDataManager.getData(GlobalDataType.secretRealm);
        long startTime = ServerConstants.getCurrentTimeMillis();
        //联盟大阵检测
        for (SecretRealmMapLeagueInfo leagueInfo : globalData.getMapLeagueInfos().values()) {
            //增长期
            if(leagueInfo.getStatus(SecretRealmMapLeagueInfo.status_increase)){
                //联盟大阵范围扩圈检测
                radiusIncreaseCheck(leagueInfo);
            }
            //交叉期
            if(leagueInfo.getStatus(SecretRealmMapLeagueInfo.status_cross)){
                //检查是否还有交叉的联盟势力
                crossCheck(leagueInfo);
            }
            //掉血期
            if(leagueInfo.getStatus(SecretRealmMapLeagueInfo.status_shed_blood)){
                //联盟进攻大阵伤害
                attackCheck(leagueInfo);
            }

            //联盟进攻大阵时长检测 删除时长检测
//            if(leagueInfo.getAttackPlayerIds().size() > 0){
//                atkGangTimeCheck(leagueInfo);
//            }
            //联盟防守大阵时长检测 删除时长检测
//            if(leagueInfo.getDefPlayerIds().size() > 0){
//                defGangTimeCheck(leagueInfo);
//            }

            //天地守护时间检测
//            if(leagueInfo.getProtectStateFinishTime() > 0 && leagueInfo.getProtectStateFinishTime() <= ServerConstants.getCurrentTimeMillis()){
//                protectStateCheck(leagueInfo);
//            }

            //缩圈期
            if(leagueInfo.getStatus(SecretRealmMapLeagueInfo.status_decrease)){
                radiusDecreaseCheck(leagueInfo);
            }

            //回血期
            if(leagueInfo.getStatus(SecretRealmMapLeagueInfo.status_recover_blood)){
                recoverBloodCheck(leagueInfo);
            }else{
                //检测进攻时期
                attackedTimeCheck(leagueInfo);
            }

            //无敌期
            if(leagueInfo.getStatus(SecretRealmMapLeagueInfo.status_protect)){
                //清除进攻者
                leagueInfo.getAttackPlayerIds().clear();
            }

        }
        //地图采集物检测
        for (SecretRealmMapInfo mapInfo : globalData.getMapInfos().values()) {
            Map<String, SecretRealmCollectInfo> mapCollectInfos = new HashMap<>(mapInfo.getMapCollectInfos());
            for (SecretRealmCollectInfo collectInfo : mapCollectInfos.values()) {
                //采集物采集结束检测
                if(collectInfo.getStartTime() > 0 && collectInfo.getOccupyPlayerId() > 0){
                    collectFinishCheck(mapInfo, collectInfo);
                    continue;
                }
                //采集物没有剩余检测
                if(collectInfo.getSurplusRewardTime() <= 0){
                    DelayTaskManager.addTask(()-> mapInfo.getMapCollectInfos().remove(collectInfo.getCollectPoint()));
                    continue;
                }
                //采集物消失检测
                if(collectInfo.getDisTime() > 0 && collectInfo.getDisTime() <= ServerConstants.getCurrentTimeMillis()){
                    DelayTaskManager.addTask(()-> mapInfo.getMapCollectInfos().remove(collectInfo.getCollectPoint()));
                    continue;
                }

            }
        }
        long betweenTime = ServerConstants.getCurrentTimeMillis() - startTime;
        if(betweenTime >= 100L){
            System.out.println("SecretRealmManager tick time is long : " + betweenTime);
        }
    }

    /**
     * 收集结束检测
     */
    public void collectFinishCheck(SecretRealmMapInfo mapInfo, SecretRealmCollectInfo collectInfo){
        //计算间隔时间
        long betweenTime = ServerConstants.getCurrentTimeMillis() - collectInfo.getStartTime();
        long betweenMinute = betweenTime / DateTimeUtil.MillisOfMinute;
        //采集点位没有剩余时间,结算单个
        SecretRealmGlobalData globalData = GlobalDataManager.getData(GlobalDataType.secretRealm);
        SecretRealmPlayerCollectInfo playerCollectInfo = globalData.getPlayerCollectInfo(collectInfo.getOccupyPlayerId());
        League league = LeagueManager.getLeagueByPlayerId(playerCollectInfo.getPlayerId());
        if(betweenMinute >= collectInfo.getSurplusRewardTime()
            && playerCollectInfo.getWeekCollectTime() < SecretRealmService.getWeekLimitTime(league)
            && playerCollectInfo.getDayCollectTime() < SecretRealmService.getDayLimitTime()){
            DelayTaskManager.addTask(()-> {
                //结算
                SecretRealmCollectInfo newCollectInfo = mapInfo.getMapCollectInfos().get(collectInfo.getCollectPoint());
                newCollectInfo.settlement(false);
            });
        }
        //玩家没有剩余时间
        if(playerCollectInfo.getWeekCollectTime() >= SecretRealmService.getWeekLimitTime(league)
                || playerCollectInfo.getDayCollectTime() >= SecretRealmService.getDayLimitTime()){
            DelayTaskManager.addTask(()-> {
                for (Pair<Integer, String> collect : playerCollectInfo.getCollects()) {
                    //结算
                    SecretRealmMapInfo secretRealmMapInfo = globalData.getMapInfo(collect.getKey());
                    SecretRealmCollectInfo newCollectInfo = secretRealmMapInfo.getMapCollectInfos().get(collect.getValue());
                    newCollectInfo.setStartTime(ServerConstants.getCurrentTimeMillis());
                    newCollectInfo.settlement(false);
                }
            });
        }
    }

    /**
     * 回血检测
     */
    public void recoverBloodCheck(SecretRealmMapLeagueInfo leagueInfo){
        League leagueById = LeagueManager.getLeagueById(leagueInfo.getLeagueId());
        SecretRealmCampAttTemplate campAtt = SecretRealmService.getCampAtt(leagueById.getLevel());
        if(leagueInfo.getLastRecoverBloodTime() + 1L * DateTimeUtil.MillisOfMinute <= ServerConstants.getCurrentTimeMillis()) {
            if (leagueInfo.getHp() > 0) {
                League league = LeagueManager.getLeagueById(leagueInfo.getLeagueId());
                int shieldLife = campAtt.getShieldLife(league);
                //回盾
                if (shieldLife > leagueInfo.getHp()) {
                    leagueInfo.setLastRecoverBloodTime(ServerConstants.getCurrentTimeMillis());
                    int campRecovery = SecretRealmService.getCampRecovery(league);
                    DelayTaskManager.addTask(() -> {
                        int addedAfterHp = shieldLife * campRecovery / 100 + leagueInfo.getHp();
                        if (addedAfterHp >= shieldLife) {
                            //停止增长
                            leagueInfo.setStatus(SecretRealmMapLeagueInfo.status_recover_blood, false);
                        }
                        //按百分比恢复血量
                        leagueInfo.setHp(Math.min(shieldLife, addedAfterHp));

                        GameLogger.secretRealm(String.format("%s : add shieldLife , leagueId : %s , addedAfterHp : %s ", ServerConstants.getCurrentTimeLocalDateTime(), leagueInfo.getLeagueId(), addedAfterHp));
                    });
                }
            }
        }
    }

    /**
     * 检查上次攻打时间
     */
    public void attackedTimeCheck(SecretRealmMapLeagueInfo leagueInfo){
        League leagueById = LeagueManager.getLeagueById(leagueInfo.getLeagueId());
        SecretRealmCampAttTemplate campAtt = SecretRealmService.getCampAtt(leagueById.getLevel());
        int shieldLife = campAtt.getShieldLife(leagueById);
        //检查上次被攻打时间，是不是超过了指定时间
        if(leagueInfo.getLastAttackedFinishTime() > 0 && leagueInfo.getLastAttackedFinishTime() + SecretRealmService.getRestoreState() * DateTimeUtil.MillisOfMinute <= ServerConstants.getCurrentTimeMillis()
                //或者血量上限改变
            || leagueInfo.getLastAttackedFinishTime() <= 0 && leagueInfo.getHp() < shieldLife){
            DelayTaskManager.addTask(()->{
                //超过进攻时间，开始恢复血量
                leagueInfo.setStatus(SecretRealmMapLeagueInfo.status_recover_blood, true);
                leagueInfo.setLastAttackedFinishTime(0);
            });
        }
    }

    /**
     * 联盟缩圈期
     */
    public void radiusDecreaseCheck(SecretRealmMapLeagueInfo leagueInfo){
        if(leagueInfo.getLastDecreaseRadiusTime() + SecretRealmService.getInfluenceTime() * DateTimeUtil.MillisOfMinute <= ServerConstants.getCurrentTimeMillis()){
            DelayTaskManager.addTask(()->{
                //缩圈
                leagueInfo.setLastDecreaseRadiusTime(ServerConstants.getCurrentTimeMillis());
                League leagueById = LeagueManager.getLeagueById(leagueInfo.getLeagueId());
                if(leagueById != null) {
                    SecretRealmCampAttTemplate secretRealmCampAttTemplate = SecretRealmService.getCampAtt(leagueById.getLevel());
                    float added = (float) leagueById.getForceValue() / 100F * SecretRealmService.getInfluenceGrowup() + secretRealmCampAttTemplate.development;
                    leagueInfo.setRadius(Math.max(leagueInfo.getRadius() - added * 2, SecretRealmService.initialArea()));
                    checkLeagueCross(leagueInfo.getMapId(), leagueInfo.getLeagueId());
                }
            });
        }
    }

//    /**
//     * 天地守护时间检测
//     */
//    public void protectStateCheck(SecretRealmMapLeagueInfo leagueInfo){
//        DelayTaskManager.addTask(()->{
//            leagueInfo.setProtectStateFinishTime(-1);
//            if(!leagueInfo.getStatus(SecretRealmMapLeagueInfo.status_protectState3)){
//                //去掉护盾，恢复血量
//                League leagueById = LeagueManager.getLeagueById(leagueInfo.getLeagueId());
//                SecretRealmCampAttTemplate campAttTemplate = SecretRealmService.getCampAtt(leagueById.getLevel());
//                leagueInfo.setHp(campAttTemplate.getShieldLife(leagueById));
//            }else{
//                //去掉保护状态
//                leagueInfo.setStatus(SecretRealmMapLeagueInfo.status_protect, false);
//            }
//            leagueInfo.setStatus(SecretRealmMapLeagueInfo.status_increase, true);
//        });
//    }


    /**
     * 联盟大阵扩圈检测
     */
    public void radiusIncreaseCheck(SecretRealmMapLeagueInfo leagueInfo){
        //时间到了，并且半径没到最大
        League leagueById = LeagueManager.getLeagueById(leagueInfo.getLeagueId());
        SecretRealmCampAttTemplate secretRealmCampAttTemplate = SecretRealmService.getCampAtt(leagueById.getLevel());
        if(leagueInfo.getLastIncreaseRadiusTime() + SecretRealmService.getInfluenceTime() * DateTimeUtil.MillisOfMinute <= ServerConstants.getCurrentTimeMillis()
            && leagueInfo.getRadius() < SecretRealmService.getMaxRange(leagueById.getForceValue())){
            DelayTaskManager.addTask(()->{
                SecretRealmGlobalData globalData = GlobalDataManager.getData(GlobalDataType.secretRealm);
                SecretRealmMapLeagueInfo mapLeagueInfo = globalData.getMapLeagueInfos().get(leagueInfo.getLeagueId());
                if(mapLeagueInfo.getStatus(SecretRealmMapLeagueInfo.status_increase)){
                    //开始扩圈
                    mapLeagueInfo.setLastIncreaseRadiusTime(ServerConstants.getCurrentTimeMillis());
                    float added = (float)leagueById.getForceValue() / 100F * SecretRealmService.getInfluenceGrowup()
                            + secretRealmCampAttTemplate.development
                            + leagueById.getTGAddedNumInt(LeagueTGTypeEnums.提高帮派秘境大阵的扩张速度, 0);
                    mapLeagueInfo.setRadius(Math.min(mapLeagueInfo.getRadius() + added, SecretRealmService.getMaxRange(leagueById.getForceValue())));
                    GameLogger.secretRealm(String.format("%s : radiusIncreaseCheck add, leagueId : %s, added %s, radius : %s", ServerConstants.getCurrentTimeLocalDateTime(), mapLeagueInfo.getLeagueId(), added, mapLeagueInfo.getRadius()));

                    //检查是否与地图其他联盟位置有交叉
                    SecretRealmMapInfo mapInfo = globalData.getMapInfo(mapLeagueInfo.getMapId());
                    for (long leagueId : mapInfo.getMapGangInfos().values()) {
                        if(leagueId > 0 && leagueId != mapLeagueInfo.getLeagueId()){
                            SecretRealmMapLeagueInfo targetLeagueInfo = globalData.getMapLeagueInfos().get(leagueId);
                            //检查是否交叉了
                            double leagueDistance = SecretRealmService.calMapPointDistance(targetLeagueInfo.getGangPoint(), mapLeagueInfo.getGangPoint());
                            if(leagueDistance <= targetLeagueInfo.getRadius() + mapLeagueInfo.getRadius()){
                                GameLogger.secretRealm(String.format("%s : radiusIncreaseCheck, leagueId : %s, targetLeagueId : %s  ", ServerConstants.getCurrentTimeLocalDateTime(), mapLeagueInfo.getLeagueId(), targetLeagueInfo.getLeagueId()));
                                targetLeagueInfo.setStatus(SecretRealmMapLeagueInfo.status_cross, true);
                                targetLeagueInfo.setStatus(SecretRealmMapLeagueInfo.status_increase, false);
                                mapLeagueInfo.setStatus(SecretRealmMapLeagueInfo.status_cross, true);
                                mapLeagueInfo.setStatus(SecretRealmMapLeagueInfo.status_increase, false);
                            }
                        }
                    }
                }
            });
        }
    }

    /**
     * 联盟交叉检测
     */
    public void crossCheck(SecretRealmMapLeagueInfo leagueInfo){
        //是否交叉可以完全交给异步线程，如果并发，增长状态那边会检测
        SecretRealmGlobalData globalData = GlobalDataManager.getData(GlobalDataType.secretRealm);
        SecretRealmMapInfo mapInfo = globalData.getMapInfo(leagueInfo.getMapId());
        for (Long leagueId : mapInfo.getMapGangInfos().values()) {
            SecretRealmMapLeagueInfo targetLeagueInfo = globalData.getMapLeagueInfos().get(leagueId);
            if(leagueId != leagueInfo.getLeagueId() && Objects.nonNull(targetLeagueInfo)){
                double leagueDistance = SecretRealmService.calMapPointDistance(targetLeagueInfo.getGangPoint(), leagueInfo.getGangPoint());
                if(leagueDistance <= targetLeagueInfo.getRadius() + leagueInfo.getRadius()){
                    return ;
                }
            }
        }
        DelayTaskManager.addTask(()->{

            GameLogger.secretRealm(String.format("%s : crossCheck is cross, leagueId : %s  ", ServerConstants.getCurrentTimeLocalDateTime(), leagueInfo.getLeagueId()));
            leagueInfo.setStatus(SecretRealmMapLeagueInfo.status_cross, false);

//            if(!leagueInfo.getStatus(SecretRealmMapLeagueInfo.status_protectState2) && !leagueInfo.getStatus(SecretRealmMapLeagueInfo.status_protectState3)){
//                leagueInfo.setStatus(SecretRealmMapLeagueInfo.status_increase, true);
//                leagueInfo.setLastIncreaseRadiusTime(ServerConstants.getCurrentTimeMillis());
//            }
        });
    }

    /**
     * 联盟掉血
     */
    public void attackCheck(SecretRealmMapLeagueInfo leagueInfo){
        long finalHurt = 0;
        Map<Long, Map<Integer, SecretRealmAtkInfo>> attackPlayerInfos = leagueInfo.getAttackPlayerIds();
        for (Long playerId : attackPlayerInfos.keySet()) {
            for (SecretRealmAtkInfo atkInfo : attackPlayerInfos.get(playerId).values()) {
                long atkTime = atkInfo.getAtkStartTime();
                long hurt = atkInfo.realHurt(leagueInfo);
                long betweenTime = (long)((SecretRealmService.getPauseTime() + SecretRealmService.getWaitTime()) * DateTimeUtil.MillisOfSecond);
                if(atkTime + betweenTime <= ServerConstants.getCurrentTimeMillis()){
                    long realHurt = leagueInfo.realHurt(hurt);
                    //掉血
                    finalHurt += realHurt;
                    //修改进攻时间
                    atkInfo.setAtkStartTime(ServerConstants.getCurrentTimeMillis());
                    League atkLeague = LeagueManager.getLeagueByPlayerId(playerId);
                    //增加日志
                    League defLeague = LeagueManager.getLeagueById(leagueInfo.getLeagueId());
                    MiniGamePlayer atkMiniPlayer = PlayerManager.getMiniPlayer(playerId);
                    if(Objects.nonNull(atkLeague)){
                        atkLeague.addLeagueLog(LeagueLogType.unionToAttack, atkMiniPlayer.getName(), defLeague.getName(), realHurt + "");
                        defLeague.addLeagueLog(LeagueLogType.unionBeAttack, atkLeague.getName(), atkMiniPlayer.getName(), realHurt + "");
                    }else{
                        defLeague.addLeagueLog(LeagueLogType.unionBeAttack, atkMiniPlayer.getName(), realHurt + "");
                    }

                }
            }
        }
        long finalHurt1 = finalHurt;
        DelayTaskManager.addTask(()->{
            leagueInfo.reduceHp((int)finalHurt1);
            GameLogger.secretRealm(String.format("%s : reduce hp , leagueId : %s , hurt : %s ", ServerConstants.getCurrentTimeLocalDateTime(), leagueInfo.getLeagueId(), finalHurt1));
            //检测大阵击破
            if(leagueInfo.getHp() <= 0){
                int reduce = SecretRealmService.getFactionAttackDown();
                //扣除势力值
                gangReduceForce(leagueInfo, reduce, true);
                GameLogger.secretRealm(String.format("%s : reduce hp break , leagueId : %s , campFailReduceFaction : %s ", ServerConstants.getCurrentTimeLocalDateTime(), leagueInfo.getLeagueId(), reduce));
                //广播通知
                breakNotify(leagueInfo);

                SecretRealmGlobalData globalData = GlobalDataManager.getData(GlobalDataType.secretRealm);
                for (Long playerId : leagueInfo.getAttackPlayerIds().keySet()) {
                    globalData.getAttackOtherPlayerInfos().remove(playerId);
                }
                //清除进攻者
                leagueInfo.getAttackPlayerIds().clear();
            }
        });
    }

    /**
     * 扣除势力值
     */
    public static void gangReduceForce(SecretRealmMapLeagueInfo leagueInfo, final int reduceForceValue, boolean canAddBuff){
        League leagueById = LeagueManager.getLeagueById(leagueInfo.getLeagueId());
        if(leagueById.getForceValue() > 0){
            if(canAddBuff){
                DelayTaskManager.addTask(()->{
                    int tempReduceForceValue = reduceForceValue;
                    //检查是否首次被击破
                    if(!leagueInfo.getStatus(SecretRealmMapLeagueInfo.status_first_break)){
                        leagueInfo.setStatus(SecretRealmMapLeagueInfo.status_first_break, true);
                        //修改自己的时间
                        String[] split = SecretRealmService.getLeagueProtect().split("\\|");
                        LocalDateTime finishTime = ServerConstants.getCurrentTimeLocalDateTime().plusHours(Integer.parseInt(split[1]));
                        leagueInfo.setProtectStateFinishTime(DateTimeUtil.toMillis(finishTime));
                        //首次击破扣除其他配置势力值
                        tempReduceForceValue =  SecretRealmService.getFactionAttackDown();
                    }
                    long oldForceValue = leagueById.getForceValue();
                    long newForceValue = Math.max(leagueById.getForceValue() - tempReduceForceValue, 0);
                    //扣除势力值
                    leagueById.setForceValue(newForceValue);

                    if(leagueById.getForceValue() > 0){
                        //累计今日扣除势力值
                        leagueInfo.setTodayReduceForce(leagueInfo.getTodayReduceForce() + (newForceValue - oldForceValue));

                        //根据势力值检查最大半径
                        leagueInfo.setRadius(Math.min(leagueInfo.getRadius(), SecretRealmService.getMaxRange(leagueById.getForceValue())));
                        //检查是否还在交叉
                        checkLeagueCross(leagueInfo.getMapId(), leagueInfo.getLeagueId());

                        if(leagueInfo.getTodayReduceForce() >= SecretRealmService.getForceUpperLimit()){
                            //无敌状态
                            leagueInfo.setStatus(SecretRealmMapLeagueInfo.status_protect, true);
                            leagueInfo.setStatus(SecretRealmMapLeagueInfo.status_shed_blood, false);
                            leagueById.addLeagueLog(LeagueLogType.unionProtect);
                        }
                    }else{
                        //直接清除势力
                        removeLeague(leagueInfo.getLeagueId());
                    }

                });
            }
        }else{
            //直接清除势力
            removeLeague(leagueInfo.getLeagueId());

        }

    }

    /**
     * 大阵击破通知
     */
    public void breakNotify(SecretRealmMapLeagueInfo leagueInfo){
        List<Long> notifyPlayerIds = new ArrayList<>();
        notifyPlayerIds.addAll(leagueInfo.getAttackPlayerIds().keySet());
        notifyPlayerIds.addAll(leagueInfo.getDefPlayerIds().keySet());

        PbProtocol.SecretRealmGangBreakNotify notify = PbProtocol.SecretRealmGangBreakNotify.newBuilder().setDetailInfo(leagueInfo.genDetailInfo()).build();
        
        for (Long notifyPlayerId : notifyPlayerIds) {
            Player onlinePlayer = PlayerManager.getOnlinePlayer(notifyPlayerId);
            if(Objects.nonNull(onlinePlayer)){
                onlinePlayer.send(PtCode.SECRET_REALM_GANG_BREAK_NOTIFY, notify);
            }
        }
        
    }


    /**
     * 检查联盟是否还在交叉
     */
    public static void checkLeagueCross(int mapId, long leagueId){
//        SecretRealmGlobalData globalData = GlobalDataManager.getData(GlobalDataType.secretRealm);
//        SecretRealmMapLeagueInfo curLeagueInfo = globalData.getMapLeagueInfos().get(leagueId);
//        if(Objects.nonNull(curLeagueInfo) && !curLeagueInfo.getStatus(SecretRealmMapLeagueInfo.status_cross)){
//            return;
//        }
//        SecretRealmMapInfo mapInfo = globalData.getMapInfo(mapId);
//        for (long curLeagueId : mapInfo.getMapGangInfos().values()) {
//            SecretRealmMapLeagueInfo leagueInfo = globalData.getMapLeagueInfos().get(curLeagueId);
//            if(leagueInfo.getStatus(SecretRealmMapLeagueInfo.status_cross)){
//                boolean isCross = false;
//                //需要重新检测是否与其他联盟交叉
//                for (long targetLeagueId : mapInfo.getMapGangInfos().values()) {
//                    if(targetLeagueId != curLeagueId){
//                        SecretRealmMapLeagueInfo targetLeagueInfo = globalData.getMapLeagueInfos().get(targetLeagueId);
//                        double leagueDistance = SecretRealmService.calMapPointDistance(targetLeagueInfo.getGangPoint(), leagueInfo.getGangPoint());
//                        if(leagueDistance <= targetLeagueInfo.getRadius() + leagueInfo.getRadius()){
//                            isCross = true;
//                            break;
//                        }
//                    }
//                }
//                if(!isCross){
//                    leagueInfo.setStatus(SecretRealmMapLeagueInfo.status_cross, false);
//                    if(!leagueInfo.getStatus(SecretRealmMapLeagueInfo.status_protectState2) && !leagueInfo.getStatus(SecretRealmMapLeagueInfo.status_protectState3)){
//                        leagueInfo.setStatus(SecretRealmMapLeagueInfo.status_increase, true);
//                    }
//                }
//            }
//
//        }
    }

    /**
     * 联盟升级操作
     * @param league 联盟信息
     * @param newLevel 新等级
     */
    public static void leagueLevelUp(League league, int newLevel){
        //增加血量
        SecretRealmGlobalData globalData = GlobalDataManager.getData(GlobalDataType.secretRealm);
        SecretRealmMapLeagueInfo mapLeagueInfo = globalData.getMapLeagueInfos().get(league.getLeagueId());
        if(Objects.nonNull(mapLeagueInfo)){
            int addHp = SecretRealmService.getCampAtt(newLevel).getShieldLife(league) - SecretRealmService.getCampAtt(newLevel - 1).getShieldLife(league);
            mapLeagueInfo.setHp(mapLeagueInfo.getHp() + addHp);
        }
    }

    /**
     * 联盟捐献
     */
    public static void leagueDonation(League league, int forceNum){
        //增加势力值
        league.setForceValue(Math.min(SecretRealmService.factionTemplates.getOrDefault(league.getLevel(), SecretRealmService.maxFactionLevel.get()), league.getForceValue() + forceNum));
        //记录上次捐献时间
        league.setLastDonationTime(ServerConstants.getCurrentTimeMillis());
        SecretRealmGlobalData globalData = GlobalDataManager.getData(GlobalDataType.secretRealm);
        SecretRealmMapLeagueInfo mapLeagueInfo = globalData.getMapLeagueInfos().get(league.getLeagueId());
        //去掉缩圈期
        if(Objects.nonNull(mapLeagueInfo) && mapLeagueInfo.getStatus(SecretRealmMapLeagueInfo.status_decrease)){
            mapLeagueInfo.setStatus(SecretRealmMapLeagueInfo.status_decrease, false);
            mapLeagueInfo.setStatus(SecretRealmMapLeagueInfo.status_increase, true);
            mapLeagueInfo.setLastDecreaseRadiusTime(-1);
        }
    }

    /**
     * 玩家退出联盟
     * @param leagueId 联盟id
     * @param playerId 玩家id
     */
    public static void quitLeague(long leagueId, long playerId){
        SecretRealmGlobalData globalData = GlobalDataManager.getData(GlobalDataType.secretRealm);
        SecretRealmMapLeagueInfo leagueInfo = globalData.getMapLeagueInfos().get(leagueId);
        if(Objects.nonNull(leagueInfo)){
            leagueInfo.getDefPlayerIds().remove(playerId);
        }
    }

    /**
     * 移除联盟
     * @param leagueId 联盟id
     */
    public static void removeLeague(long leagueId){
        SecretRealmGlobalData globalData = GlobalDataManager.getData(GlobalDataType.secretRealm);
        SecretRealmMapLeagueInfo leagueInfo = globalData.getMapLeagueInfos().get(leagueId);
        if(Objects.nonNull(leagueInfo)){
            SecretRealmMapInfo mapInfo = globalData.getMapInfo(leagueInfo.getMapId());

            mapInfo.clearLeague(leagueId);
            for (Long playerId : leagueInfo.getAttackPlayerIds().keySet()) {
                globalData.getAttackOtherPlayerInfos().remove(playerId);
            }
            globalData.getMapLeagueInfos().remove(leagueInfo.getLeagueId());
        }

        League league = LeagueManager.getLeagueById(leagueId);
        if(Objects.nonNull(league)){
            sync(league.getAllMember());
        }

    }

    public static void sync(Collection<Long> playerIds){
        for(long playerId : playerIds){
            if(PlayerManager.isOnline(playerId)){
                PlayerManager.getOnlinePlayer(playerId).send(PtCode.SECRET_REALM_SYNC_NOTIFY, PbProtocol.SecretRealmSyncNotify.newBuilder().build());
            }
        }
    }

    public static void checkRank(int passInstanceId){
        //检查地图id是否不进入排行榜
        Map<Integer, List<Integer>> mapChapterToIds = SecretRealmService.mapChapterToIds;
        if(!mapChapterToIds.containsKey(passInstanceId)){
            return;
        }
        SecretRealmGlobalData globalData = GlobalDataManager.getData(GlobalDataType.secretRealm);
        List<Integer> top4MapIds = globalData.getRankMapIds();
        List<Integer> mapIds = mapChapterToIds.get(passInstanceId);
        for (Integer mapId : mapIds) {
            if(top4MapIds.contains(mapId)){
                continue;
            }
            //检查是否不计入排行榜
            if(SecretRealmService.getRankClose().contains(mapId)){
                continue;
            }
            if(CollectionUtil.isNotEmpty(top4MapIds)
                && top4MapIds.get(top4MapIds.size() - 1) >= mapId){
                continue;
            }
            if(top4MapIds.size() >= SecretRealmService.getMapRankCount()
                && top4MapIds.get(0) < mapId){
                //删除清除第一个排行榜
                int removeMapId = top4MapIds.remove(0);
                globalData.getMapInfo(removeMapId).getRankInfos().clear();
            }
            //直接进入排行榜
            top4MapIds.add(mapId);
        }
    }




//    public static PbSecretRealm.SecretRealmPlayerBaseInfo buildPlayerBaseInfo(long playerId){
//        return buildPlayerBaseInfo(playerId, 0, 0, 0);
//    }
    public static PbSecretRealm.SecretRealmPlayerBaseInfo buildPlayerBaseInfo(long playerId, int surplusDefTimes, int showHeroId, int lineupType){
        MiniGamePlayer miniPlayer = PlayerManager.getMiniPlayer(playerId);
        if(Objects.nonNull(miniPlayer)){
            PbSecretRealm.SecretRealmPlayerBaseInfo.Builder builder = PbSecretRealm.SecretRealmPlayerBaseInfo.newBuilder();
            builder.setPlayerId(miniPlayer.getPlayerId());
            builder.setName(miniPlayer.getName());
            builder.setHeadId(miniPlayer.getHeadImage());
            builder.setHeadFrameId(miniPlayer.getHeadFrame());
            builder.setFightPower(miniPlayer.getFightingPower());
            builder.setLevel(miniPlayer.getLevel());
            builder.setSurplusDefTimes(surplusDefTimes);
            builder.setShowHeroTempId(showHeroId);
            builder.setLineupType(lineupType);
            return builder.build();
        }
        return null;
    }

    public static PbSecretRealm.SecretRealmPlayerListInfo buildPlayerListInfo(long playerId, long time, long hurt, int showHeroId, int lineupType){
        return buildPlayerListInfo(playerId, time, hurt, 0, showHeroId, lineupType);
    }
    public static PbSecretRealm.SecretRealmPlayerListInfo buildPlayerListInfo(long playerId, long time, long hurt, int surplusDefTimes, int showHeroId, int lineupType){
        PbSecretRealm.SecretRealmPlayerBaseInfo baseInfo = buildPlayerBaseInfo(playerId, surplusDefTimes, showHeroId, lineupType);
        if(Objects.nonNull(baseInfo)){
            PbSecretRealm.SecretRealmPlayerListInfo.Builder listInfo = PbSecretRealm.SecretRealmPlayerListInfo.newBuilder();
            listInfo.setBaseInfo(baseInfo);
            League league = LeagueManager.getLeagueByPlayerId(playerId);
            if(Objects.nonNull(league)){
                listInfo.setLeagueId(league.getLeagueId());
                listInfo.setLeagueName(league.getName());
            }
            listInfo.setTime(time);
            listInfo.setHurt(hurt);
            return listInfo.build();
        }
        return null;
    }

    @Override
    public String getRunnerName() {
        return "SecretRealmManager";
    }

    @Override
    public long getRunnerInterval() {
        return 500L;
    }

    private SecretRealmManager(){}
    private static final SecretRealmManager manager = new SecretRealmManager();
    public static SecretRealmManager getInstance(){
        return manager;
    }

}

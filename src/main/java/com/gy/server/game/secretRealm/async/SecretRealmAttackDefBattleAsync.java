package com.gy.server.game.secretRealm.async;

import java.util.List;
import java.util.Objects;

import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.combat.CombatManager;
import com.gy.server.game.combat.StageType;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.secretRealm.SecretRealmService;
import com.gy.server.game.secretRealm.stage.SecretRealmStage;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;

/**
 * 秘境清理驻守军
 * <AUTHOR> - [Created on 2023-05-24 9:52]
 */
public class SecretRealmAttackDefBattleAsync extends AsyncCall {

    Player player;
    int mapId;
    String point;
    long defPlayerId;
    List<HeroUnit> defs;
    StageType stageType = StageType.secretRealmDefence;
    boolean isRage;
    int protectBuffId;
    LineupType atkLineupType;
    LineupType defLineupType;
    long time;

    public SecretRealmAttackDefBattleAsync(Player player, int mapId, String point
            , long defPlayerId, boolean isRage, int protectBuffId
            , LineupType atkLineupType, LineupType defLineupType, long time){
        this.player = player;
        this.mapId = mapId;
        this.point = point;
        this.defPlayerId = defPlayerId;
        this.isRage = isRage;
        this.protectBuffId = protectBuffId;
        this.atkLineupType = atkLineupType;
        this.defLineupType = defLineupType;
        this.time = time;
    }

    @Override
    public void asyncExecute() {
        Player defPlayer = PlayerManager.getPlayer(defPlayerId);
        if(Objects.nonNull(defPlayer)){
            defs = defPlayer.getLineupModel().createHeroUnits(stageType, defLineupType);
        }
    }

    @Override
    public void execute() {
        PbProtocol.SecretRealmAttackGangDefRst.Builder rst = PbProtocol.SecretRealmAttackGangDefRst.newBuilder().setResult(Text.genOkServerRstInfo());
        List<HeroUnit> atks = player.getLineupModel().createHeroUnits(stageType, atkLineupType);

        if(protectBuffId > 0){
            for (HeroUnit def : defs) {
                def.addBuff(protectBuffId, 1);
            }
        }
        //初始化战斗
        SecretRealmStage stage = new SecretRealmStage(player, atks, defPlayerId, defs
                , mapId, point, stageType, isRage, SecretRealmService.getDefendScene(), atkLineupType, defLineupType);
        stage.init();
        CombatManager.combatPrepare(stage);
        rst.setStage(stage.genAbstractPb());
        rst.setRageTime(player.getSecretRealmModel().getRageTime());

        player.send(PtCode.SECRET_REALM_ATTACK_GANG_DEF_RST, rst.build(), time);
    }
}

package com.gy.server.game.secretRealm.stage;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.combat.StageType;
import com.gy.server.game.currency.Currency;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.league.log.LeagueLogType;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.secretRealm.PlayerSecretRealmModel;
import com.gy.server.game.secretRealm.SecretRealmGlobalData;
import com.gy.server.game.secretRealm.SecretRealmService;
import com.gy.server.game.secretRealm.async.SecretRealmReduceDurableAsync;
import com.gy.server.game.secretRealm.bean.SecretRealmCollectInfo;
import com.gy.server.game.secretRealm.bean.SecretRealmMapInfo;
import com.gy.server.game.secretRealm.bean.SecretRealmMapLeagueInfo;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

/**
 * 进攻后逻辑
 * <AUTHOR> - [Created on 2023-05-23 10:57]
 */
public enum SecretRealmStageAfter {

    /**
     * 进攻采集点
     */
    secretRealmCollect(StageType.secretRealmCollect, (stage, atkPlayer, defPlayerId, mapId, point, isRage, atkLineupType, defLineupType)->{
        SecretRealmGlobalData globalData = GlobalDataManager.getData(GlobalDataType.secretRealm);
        if(stage.isWin()){
            //清除采集点
            SecretRealmMapInfo mapInfo = globalData.getMapInfo(mapId);
            //计算防守玩家采集了多长时间
            SecretRealmCollectInfo collectInfo = mapInfo.getMapCollectInfos().get(point);
            //结算
            collectInfo.settlement(true);

            //增加日志
            League atkLeague = LeagueManager.getLeagueByPlayer(atkPlayer);
            League defLeague = LeagueManager.getLeagueByPlayerId(defPlayerId);
            MiniGamePlayer defPlayer = PlayerManager.getMiniPlayer(defPlayerId);
            if(Objects.nonNull(atkLeague)){
                if(Objects.nonNull(defLeague)){
                    defLeague.addLeagueLog(LeagueLogType.collectAttackLose, atkLeague.getName(), atkPlayer.getName(), defPlayer.getName(), mapId + "", point);
                    atkLeague.addLeagueLog(LeagueLogType.collectAttackLose, atkLeague.getName(), atkPlayer.getName(), defPlayer.getName(), mapId + "", point);
                }else{
                    atkLeague.addLeagueLog(LeagueLogType.collectAttackLose, atkLeague.getName(), atkPlayer.getName(), defPlayer.getName(), mapId + "", point);
                }
            }else{
                if(Objects.nonNull(defLeague)){
                    defLeague.addLeagueLog(LeagueLogType.collectAttackLose,  atkPlayer.getName(), defPlayer.getName(), mapId + "", point);
                }
            }
        }else{
            League atkLeague = LeagueManager.getLeagueByPlayer(atkPlayer);
            League defLeague = LeagueManager.getLeagueByPlayerId(defPlayerId);
            MiniGamePlayer defPlayer = PlayerManager.getMiniPlayer(defPlayerId);
            if(Objects.nonNull(atkLeague)){
                if(Objects.nonNull(defLeague)){
                    defLeague.addLeagueLog(LeagueLogType.collectAttackWin, atkLeague.getName(), atkPlayer.getName(), defPlayer.getName(), mapId + "", point);
                    atkLeague.addLeagueLog(LeagueLogType.collectAttackWin, atkLeague.getName(), atkPlayer.getName(), defPlayer.getName(), mapId + "", point);
                }else{
                    atkLeague.addLeagueLog(LeagueLogType.collectAttackWin, atkLeague.getName(), atkPlayer.getName(), defPlayer.getName(), mapId + "", point);
                }
            }else{
                if(Objects.nonNull(defLeague)){
                    defLeague.addLeagueLog(LeagueLogType.collectAttackWin,  atkPlayer.getName(), defPlayer.getName(), mapId + "", point);
                }
            }
        }

    }),

    /**
     * 秘境清理进攻
     */
    secretRealmGang(StageType.secretRealmGang, (stage, atkPlayer, defPlayerId, mapId, point, isRage, atkLineupType, defLineupType)->{

        PlayerSecretRealmModel secretRealmModel = atkPlayer.getSecretRealmModel();
        if(isRage){
            //扣除狂暴次数
            secretRealmModel.reduceRageTime();
        }

        Reward defCost = SecretRealmService.getCaDC().createReward();
        if(stage.isWin()){
            //进攻成功

            //直接清除进攻军
            secretRealmClear(mapId, point, defPlayerId, true, atkLineupType, defLineupType);

            //进入疲劳期
            atkPlayer.getSecretRealmModel().setTiredEndTime(DateTimeUtil.toMillis(ServerConstants.getCurrentTimeLocalDateTime().plusSeconds(SecretRealmService.getVictoryFatigue())));
        }else{

        }

        if(PlayerManager.isOnline(defPlayerId)){
            Player defPlayer = PlayerManager.getOnlinePlayer(defPlayerId);
            //扣除防守方行动力
            defCost.remove(defPlayer, BehaviorType.defGangAttackerAfter);
            //检测是否还有行动力
            if(defPlayer.getCurrencyModel().getCurrency(Currency.actionPower) <= 0){
                secretRealmClear(mapId, point, defPlayerId, true, atkLineupType, defLineupType);
            }
            //记录日志
            League atkLeague = LeagueManager.getLeagueByPlayer(atkPlayer);
            League defLeague = LeagueManager.getLeagueByPlayerId(defPlayerId);
            if(stage.isWin()){
                atkLeague.addLeagueLog(LeagueLogType.unionDefenseRepel
                        , Objects.isNull(defLeague) ? "" : defLeague.getName()
                        , defPlayer.getName()
                        , atkPlayer.getName());
                if(Objects.nonNull(defLeague)){
                    defLeague.addLeagueLog(LeagueLogType.unionAttackRepel, defPlayer.getName(), atkLeague.getName(), defPlayer.getName());
                }
            }else{
            }

        }else{
            ThreadPool.execute(new SecretRealmReduceDurableAsync(mapId, point, defPlayerId, defCost, true, stage.isWin(), atkPlayer, atkLineupType, defLineupType));
        }

    }),

    /**
     * 秘境清理驻守军
     */
    secretRealmDefence(StageType.secretRealmDefence, (stage, atkPlayer, defPlayerId, mapId, point, isRage, atkLineupType, defLineupType)->{
        PlayerSecretRealmModel secretRealmModel = atkPlayer.getSecretRealmModel();
        if(isRage){
            //扣除狂暴次数
            secretRealmModel.reduceRageTime();
        }

        Reward defCost = SecretRealmService.getCaDC().createReward();
        if(stage.isWin()){
            //进攻成功

            //直接清除进攻军
            secretRealmClear(mapId, point, defPlayerId, false, atkLineupType, defLineupType);

            //进入疲劳期
            atkPlayer.getSecretRealmModel().setTiredEndTime(DateTimeUtil.toMillis(ServerConstants.getCurrentTimeLocalDateTime().plusSeconds(SecretRealmService.getVictoryFatigue())));
        }else{

        }
        if(PlayerManager.isOnline(defPlayerId)){
            Player defPlayer = PlayerManager.getOnlinePlayer(defPlayerId);
            //扣除防守方行动力
            defCost.remove(defPlayer, BehaviorType.attackGangDefAfter);
            //检测是否还有行动力
            if(defPlayer.getCurrencyModel().getCurrency(Currency.actionPower) <= 0){
//                secretRealmClear(mapId, point, defPlayerId, false, atkLineupType, defLineupType);
                secretRealmClear(mapId, point, defPlayerId, false, atkLineupType, null);
            }
            League atkLeague = LeagueManager.getLeagueByPlayer(atkPlayer);
            League defLeague = LeagueManager.getLeagueByPlayerId(defPlayerId);
            if(stage.isWin()){
                if(Objects.nonNull(atkLeague)){
                    defLeague.addLeagueLog(LeagueLogType.unionDefenseLose, atkLeague.getName(), atkPlayer.getName(), defPlayer.getName());
                    atkLeague.addLeagueLog(LeagueLogType.unionAttackWin, atkLeague.getName(), atkPlayer.getName(), defPlayer.getName());
                }else{
                    defLeague.addLeagueLog(LeagueLogType.unionDefenseLose, atkPlayer.getName(), defPlayer.getName());
                }

            }else{
                if(Objects.nonNull(atkLeague)){
                    defLeague.addLeagueLog(LeagueLogType.unionDefenseWin, atkLeague.getName(), atkPlayer.getName(), defPlayer.getName());
                    atkLeague.addLeagueLog(LeagueLogType.unionAttackLose, atkLeague.getName(), atkPlayer.getName(), defPlayer.getName());
                }else{
                    defLeague.addLeagueLog(LeagueLogType.unionDefenseWin, atkPlayer.getName(), defPlayer.getName());
                }

                //减少防守方剩余防守次数
                defPlayer.getSecretRealmModel().reduceSurplusDefTimes();
                if(defPlayer.getSecretRealmModel().getSurplusDefTimes() <= 0){
                    //清除防守军
//                    secretRealmClear(mapId, point, defPlayerId, false, atkLineupType, defLineupType);
                    secretRealmClear(mapId, point, defPlayerId, false, atkLineupType, null);
                }
            }
        }else{
            ThreadPool.execute(new SecretRealmReduceDurableAsync(mapId, point, defPlayerId, defCost, false, stage.isWin(), atkPlayer, atkLineupType, defLineupType));
        }


    }),

    ;
    StageType stageType;
    StageAfterDeal deal;

    SecretRealmStageAfter(StageType stageType, StageAfterDeal deal){
        this.stageType = stageType;
        this.deal = deal;
    }

    /**
     * 清除进攻
     */
    public static void secretRealmClear(int mapId, String point, long targetPlayerId, boolean isAtk, LineupType atkLineupType, LineupType defLineupType){
        SecretRealmGlobalData globalData = GlobalDataManager.getData(GlobalDataType.secretRealm);
        SecretRealmMapInfo mapInfo = globalData.getMapInfo(mapId);
        long leagueId = mapInfo.getMapGangInfos().get(point);
        SecretRealmMapLeagueInfo mapLeagueInfo = globalData.getMapLeagueInfos().get(leagueId);
        if(isAtk){
            mapLeagueInfo.getAttackPlayerIds().get(targetPlayerId).remove(atkLineupType.getType());
            if(CollectionUtil.isEmpty(mapLeagueInfo.getAttackPlayerIds().get(targetPlayerId))){
                mapLeagueInfo.getAttackPlayerIds().remove(targetPlayerId);
            }
            mapLeagueInfo.checkAttackedFinish();
            globalData.getAttackOtherPlayerInfos().get(targetPlayerId).remove(mapId);
        }else{
            if(Objects.isNull(defLineupType)){
                mapLeagueInfo.getDefPlayerIds().remove(targetPlayerId);
            }else{
                mapLeagueInfo.getDefPlayerIds().get(targetPlayerId).remove(defLineupType.getType());
                if(CollectionUtil.isEmpty(mapLeagueInfo.getDefPlayerIds().get(targetPlayerId))){
                    mapLeagueInfo.getDefPlayerIds().remove(targetPlayerId);
                }
            }
        }
    }

    public static void deal(AbstractStage stage, Player atkPlayer, long defPlayerId, int mapId, String point, boolean isRage, LineupType atkLineupType, LineupType defLineupType){
        deals.get(stage.getStageType()).deal(stage, atkPlayer, defPlayerId, mapId, point, isRage, atkLineupType, defLineupType);
    }

    private static Map<StageType, StageAfterDeal> deals = new HashMap<>();
    static{
        for (SecretRealmStageAfter afterDeal : SecretRealmStageAfter.values()) {
            deals.put(afterDeal.stageType, afterDeal.deal);
        }
    }

    public interface StageAfterDeal{
        void deal(AbstractStage stage, Player atkPlayer
                , long defPlayerId, int mapId, String point, boolean isRage
                , LineupType atkLineupType, LineupType defLineupType);
    }


}

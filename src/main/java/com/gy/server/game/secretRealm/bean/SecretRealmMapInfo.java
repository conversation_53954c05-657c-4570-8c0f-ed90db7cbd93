package com.gy.server.game.secretRealm.bean;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import com.gy.server.game.league.League;
import com.gy.server.game.secretRealm.SecretRealmService;
import com.gy.server.game.secretRealm.template.SecretRealmMapTemplate;
import com.ttlike.server.tl.baselib.serialize.world.SecretRealmGlobalDb;

/**
 * 秘境地图信息
 * <AUTHOR> - [Created on 2023-05-15 11:34]
 */
public class SecretRealmMapInfo {

    //地图id
    int mapId;
    /**
     * 地图领土信息
     * key:point
     *  value:leagueId
     */
    Map<String, Long> mapGangInfos = new HashMap<>();

    /**
     * 地图收集信息
     * key:point
     *  value：收集信息
     */
    Map<String, SecretRealmCollectInfo> mapCollectInfos = new ConcurrentHashMap<>();

    /**
     * 排名信息
     */
    List<SecretRealmRankInfo> rankInfos = new ArrayList<>();

    /**
     * 统治战场联盟id
     *  （1）帮派大阵位于本秘境，且秘境为前四个最新秘境。
     *  （2）在上一次宗门乱斗中，宗门积分在本秘境内最高。
     */
    long reignLeagueId;

    public void clearLeague(long leagueId){
        String gang = null;
        for (String point : mapGangInfos.keySet()) {
            if (mapGangInfos.get(point) == leagueId) {
                gang = point;
                break;
            }
        }
        if (gang!= null) {
            mapGangInfos.remove(gang);
        }

        SecretRealmRankInfo rmRankInfo = null;
        for (SecretRealmRankInfo rankInfo : rankInfos) {
            if (rankInfo.getLeagueId() == leagueId) {
                rmRankInfo = rankInfo;
                break;
            }
        }
        if (rmRankInfo!= null) {
            rankInfos.remove(rmRankInfo);
        }

        if(leagueId == reignLeagueId){
            reignLeagueId = 0;
        }
    }

    public long getReignLeagueId() {
        return reignLeagueId;
    }

    public void setReignLeagueId(long reignLeagueId) {
        this.reignLeagueId = reignLeagueId;
    }

    public List<SecretRealmRankInfo> getRankInfos() {
        return rankInfos;
    }

    public void setRankInfos(List<SecretRealmRankInfo> rankInfos) {
        this.rankInfos = rankInfos;
    }

    public void setMapId(int mapId) {
        this.mapId = mapId;
    }

    public int getMapId(){
        return mapId;
    }

    public SecretRealmMapTemplate getTemplate(){
        return SecretRealmService.mapTemplates.get(mapId);
    }

    public Map<String, Long> getMapGangInfos() {
        return mapGangInfos;
    }

    public void setMapGangInfos(Map<String, Long> mapGangInfos) {
        this.mapGangInfos = mapGangInfos;
    }

    public Map<String, SecretRealmCollectInfo> getMapCollectInfos() {
        return mapCollectInfos;
    }


    public void readFromPb(SecretRealmGlobalDb.SecretRealmMapInfoDb db) {
        mapId = db.getMapId();
        this.mapGangInfos.putAll(db.getMapGangInfos());
        for (SecretRealmGlobalDb.SecretRealmMapInfoDb.SecretRealmCollectInfoDb infoDb : db.getMapCollectInfos().values()) {
            SecretRealmCollectInfo collectInfo = new SecretRealmCollectInfo();
            collectInfo.readFromPb(infoDb);
            this.mapCollectInfos.put(collectInfo.getCollectPoint(), collectInfo);
        }
        for (SecretRealmGlobalDb.SecretRealmMapInfoDb.SecretRealmRankInfoDb rankInfoDb : db.getRankInfos()) {
            SecretRealmRankInfo rankInfo = new SecretRealmRankInfo();
            rankInfo.readFromPb(rankInfoDb);
            this.rankInfos.add(rankInfo);
        }
    }

    public SecretRealmGlobalDb.SecretRealmMapInfoDb writeToPb() {
        SecretRealmGlobalDb.SecretRealmMapInfoDb db = new SecretRealmGlobalDb.SecretRealmMapInfoDb();
        db.setMapGangInfos(mapGangInfos);
        db.setMapId(mapId);
        for (SecretRealmCollectInfo collectInfo : mapCollectInfos.values()) {
            SecretRealmGlobalDb.SecretRealmMapInfoDb.SecretRealmCollectInfoDb infoDb = collectInfo.writeToPb();
            db.getMapCollectInfos().put(infoDb.getCollectPoint(), infoDb);
        }
        for (SecretRealmRankInfo rankInfo : this.rankInfos) {
            db.getRankInfos().add(rankInfo.writeToPb());
        }
        return db;
    }

}

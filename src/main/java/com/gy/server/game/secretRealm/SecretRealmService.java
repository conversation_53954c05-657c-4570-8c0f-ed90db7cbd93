package com.gy.server.game.secretRealm;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.combat.StageType;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.currency.Currency;
import com.gy.server.game.currency.PlayerCurrencyModel;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.event.ServerEvent;
import com.gy.server.game.event.ServerEventHandler;
import com.gy.server.game.event.ServerEventType;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.league.log.LeagueLogType;
import com.gy.server.game.league.template.LeagueTGTypeEnums;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.mail.MailManager;
import com.gy.server.game.mail.MailType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.secretRealm.async.SecretRealmAttackAtkBattleAsync;
import com.gy.server.game.secretRealm.async.SecretRealmAttackDefBattleAsync;
import com.gy.server.game.secretRealm.async.SecretRealmCollectBattleAsync;
import com.gy.server.game.secretRealm.async.SecretRealmGetDefenceAsync;
import com.gy.server.game.secretRealm.bean.*;
import com.gy.server.game.secretRealm.template.SecretRealmCampAttTemplate;
import com.gy.server.game.secretRealm.template.SecretRealmCollectionTemplate;
import com.gy.server.game.secretRealm.template.SecretRealmMapTemplate;
import com.gy.server.game.service.Service;
import com.gy.server.game.text.Text;
import com.gy.server.game.text.TextParamText;
import com.gy.server.packet.PbCommons;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbSecretRealm;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.StringUtil;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import org.apache.commons.lang3.tuple.Pair;

import static com.gy.server.game.event.ServerEventType.day5clock;

/**
 * 帮派秘境 （势力大阵（地图、对抗））
 * <AUTHOR> - [Created on 2023-05-11 14:56]
 */
public class SecretRealmService extends PlayerPacketHandler implements Service, ServerEventHandler {

    private static final ServerEventType[] eventTypes = new ServerEventType[]{day5clock};

    /**
     * 常量配置
     */
    private static volatile Map<String, String> constants = new HashMap<>();
    /**
     * 属性相关
     */
    public static volatile Map<Integer, SecretRealmCampAttTemplate> campAttTemplates = new HashMap<>();
    public static volatile AtomicInteger maxCampAttId = new AtomicInteger(-1);
    /**
     * 秘境采集配置
     */
    public static volatile Map<Integer, SecretRealmCollectionTemplate> collectionTemplates = new HashMap<>();
    /**
     * 秘境地图信息
     */
    public static volatile Map<Integer, SecretRealmMapTemplate> mapTemplates = new HashMap<>();
    public static volatile Map<Integer, List<Integer>> mapChapterToIds = new HashMap<>();
    /**
     * 势力值信息
     */
    public static volatile Map<Integer, Integer> factionTemplates = new HashMap<>();
    public static volatile AtomicInteger maxFactionLevel = new AtomicInteger(-1);
    /**
     * 伤害转换
     */
    public static volatile List<Pair<Long, Long>> hurtTransformations = new ArrayList<>();
    /**
     * 最大伤害
     */
    public static volatile Pair<Long, Long> maxHurtTransformation;

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        List<Map<String, String>> list = ConfigReader.read(ConfigFile.leagueSecretRealm_constant);
        Map<String, String> constantMap = new HashMap<>();
        for (Map<String, String> map : list) {
            constantMap.put(map.get("key"), map.get("value"));
        }
        constants = constantMap;

        list = ConfigReader.read(ConfigFile.leagueSecretRealm_campAtt);
        Map<Integer, SecretRealmCampAttTemplate> campAttTemplateMap = new HashMap<>();
        for (Map<String, String> map : list) {
            SecretRealmCampAttTemplate campAttTemplate = new SecretRealmCampAttTemplate();
            campAttTemplate.id = Integer.parseInt(map.get("id"));
            campAttTemplate.shieldLife = Integer.parseInt(map.get("shieldLife"));
            campAttTemplate.development = Float.parseFloat(map.get("development"));
            campAttTemplateMap.put(campAttTemplate.id, campAttTemplate);
            maxCampAttId.set(Math.max(maxCampAttId.get(), campAttTemplate.id));
        }
        campAttTemplates = campAttTemplateMap;

        list = ConfigReader.read(ConfigFile.leagueSecretRealm_collection);
        Map<Integer, SecretRealmCollectionTemplate> collectionTemplateMap = new HashMap<>();
        for (Map<String, String> map : list) {
            SecretRealmCollectionTemplate collectionTemplate = new SecretRealmCollectionTemplate();
            collectionTemplate.id = Integer.parseInt(map.get("id"));
            collectionTemplate.collectNum = Integer.parseInt(map.get("collectNum"));
            collectionTemplate.collectTime = Integer.parseInt(map.get("collectTime"));
            collectionTemplate.collectEfficiency = Integer.parseInt(map.get("collectEfficiency"));
            String collectCostStr = map.get("collectCost");
            if(!"-1".equals(collectCostStr)){
                collectionTemplate.collectCost.addAll(RewardTemplate.readListFromText(collectCostStr));
            }
            collectionTemplateMap.put(collectionTemplate.id, collectionTemplate);
        }
        collectionTemplates = collectionTemplateMap;

        list = ConfigReader.read(ConfigFile.leagueSecretRealm_secretRealm);
        Map<Integer, SecretRealmMapTemplate> mapTemplateMap = new HashMap<>();
        Map<Integer, List<Integer>> mapChapterToIdMap = new HashMap<>();
        for (Map<String, String> map : list) {
            SecretRealmMapTemplate mapTemplate = new SecretRealmMapTemplate();
            mapTemplate.id = Integer.parseInt(map.get("id"));
            mapTemplate.openChapter = Integer.parseInt(map.get("openChapter"));
            mapTemplate.limitGangLev = Integer.parseInt(map.get("limitGangLev"));
            mapTemplate.createGangPoints.addAll(Arrays.asList(map.get("createGangPoint").split(",")));
            mapTemplate.collectPoints.addAll(Arrays.asList(map.get("collectPoint").split(",")));
            for (String collectUpdate : map.get("collectUpdate").split(",")) {
                String[] split = collectUpdate.split("\\|");
                mapTemplate.collectUpdates.put(Integer.parseInt(split[0]), Integer.parseInt(split[1]));
            }
            mapTemplate.hangUpProfit = map.get("hangUpProfit");
            mapTemplate.hangUpTribute = map.get("hangUpTribute");
            mapTemplateMap.put(mapTemplate.id, mapTemplate);

            if(!mapChapterToIdMap.containsKey(mapTemplate.openChapter)){
                mapChapterToIdMap.put(mapTemplate.openChapter, new ArrayList<>());
            }
            mapChapterToIdMap.get(mapTemplate.openChapter).add(mapTemplate.id);
        }
        mapTemplates = mapTemplateMap;
        mapChapterToIds = mapChapterToIdMap;

        list = ConfigReader.read(ConfigFile.leagueSecretRealm_faction);
        Map<Integer, Integer> factionTemplate = new HashMap<>();
        for (Map<String, String> map : list) {
            int level = Integer.parseInt(map.get("id"));
            factionTemplate.put(level, Integer.parseInt(map.get("factionMax")));
            maxFactionLevel.set(Math.max(level, maxFactionLevel.get()));
        }
        factionTemplates = factionTemplate;

        list = ConfigReader.read(ConfigFile.leagueSecretRealm_hurtTransformation);
        List<Pair<Long, Long>> hurtTransformation = new ArrayList<>();
        Pair<Long, Long> curMaxHurtTransformation = null;
        for (Map<String, String> map : list) {
            Pair<Long, Long> curInfo = Pair.of(Long.parseLong(map.get("id")), Long.parseLong(map.get("hurtConversion")));
            curMaxHurtTransformation = curInfo;
            hurtTransformation.add(curInfo);
        }
        hurtTransformations = hurtTransformation;
        maxHurtTransformation = curMaxHurtTransformation;

    }

    public static SecretRealmCampAttTemplate getCampAtt(int id){
        SecretRealmCampAttTemplate campAttTemplate = SecretRealmService.campAttTemplates.get(id);
        if(Objects.isNull(campAttTemplate)){
            campAttTemplate = SecretRealmService.campAttTemplates.get(SecretRealmService.maxCampAttId.get());
        }
        return campAttTemplate;
    }

    /**
     * 根据战力获取伤害值
     * @param fightPower 战力
     * @return 伤害值
     */
    public static long getHurtByPower(long fightPower){
        if(Objects.nonNull(maxHurtTransformation) && maxHurtTransformation.getKey() <= fightPower){
            return maxHurtTransformation.getValue();
        }
        for (Pair<Long, Long> hurtTransformation : hurtTransformations) {
            if(hurtTransformation.getKey() >= fightPower){
                return hurtTransformation.getValue();
            }
        }
        return 0;
    }

    /**
     * 获得秘境信息
     */
    @Handler(PtCode.SECRET_REALM_GET_INFO_REQ)
    private void getInfo(Player player, PbProtocol.SecretRealmGetInfoReq req, long time) {
        PbProtocol.SecretRealmGetInfoRst.Builder rst = PbProtocol.SecretRealmGetInfoRst.newBuilder().setResult(Text.genOkServerRstInfo());
        int mapId = req.getMapId();
        logic:{
            SecretRealmGlobalData globalData = GlobalDataManager.getData(GlobalDataType.secretRealm);
            SecretRealmMapInfo mapInfo = globalData.getMapInfo(mapId);
            if(Objects.isNull(mapInfo) || Objects.isNull(mapInfo.getTemplate())){
                rst.setResult(Text.genServerRstInfo(Text.秘境不存在));
                break logic;
            }

            //校验秘境地图是否解锁（章节）
            SecretRealmMapTemplate template = mapInfo.getTemplate();
            SecretRealmMapLeagueInfo myLeagueInfo = globalData.getMapLeagueInfos().get(LeagueManager.getLeagueId(player));
            if(!player.getInstanceModel().isPassMission(template.openChapter)
                && (Objects.isNull(myLeagueInfo) || myLeagueInfo.getMapId() != mapId)){
                rst.setResult(Text.genServerRstInfo(Text.秘境限制不能进入));
                break logic;
            }

            //检查服务器人数导量限制
            for (String str : SecretRealmService.getMapOpen().split(",")) {
                String[] split = str.split("\\|");
                if(Integer.parseInt(split[1]) == mapId && PlayerManager.getRegisteredPlayerCount() < Integer.parseInt(split[0])){
                    rst.setResult(Text.genServerRstInfo(Text.秘境限制不能进入));
                    break logic;
                }
            }

            rst.setMapInfo(globalData.genSecretRealmMapInfoPb(mapId));
            SecretRealmPlayerCollectInfo playerCollectInfo = globalData.getPlayerCollectInfo(player.getPlayerId());
            if(Objects.nonNull(playerCollectInfo)){
                for (Pair<Integer, String> collect : playerCollectInfo.getCollects()) {
                    int collectMapId = collect.getKey();
                    String collectPoint = collect.getValue();
                    SecretRealmCollectInfo collectInfo = globalData.getMapInfo(collectMapId).getMapCollectInfos().get(collectPoint);
                    rst.addCollecInfos(collectInfo.genPb());
                }
            }

            Map<Integer, String> atkInfos = globalData.getAttackOtherPlayerInfos().getOrDefault(player.getPlayerId(), new HashMap<>());
            if(CollectionUtil.isNotEmpty(atkInfos)){
                atkInfos.forEach((k, v)->{
                    Long leagueId = globalData.getMapInfo(k).getMapGangInfos().get(v);
                    SecretRealmMapLeagueInfo mapLeagueInfo = globalData.getMapLeagueInfos().get(leagueId);
                    if(Objects.nonNull(mapLeagueInfo)){
                        for (Integer lineupType : mapLeagueInfo.getAttackPlayerId(player.getPlayerId()).keySet()) {
                            PbCommons.KeyValueDb.Builder atkInfo = PbCommons.KeyValueDb.newBuilder();
                            atkInfo.setStringKey(k+"_"+v);
                            atkInfo.setIntValue(lineupType);
                            rst.addAtkInfos(atkInfo);
                        }
                    }
                });
            }

            rst.addAllRankMapIds(globalData.getRankMapIds());
            rst.setServerRegisterNum(PlayerManager.getRegisteredPlayerCount());
        }
        player.send(PtCode.SECRET_REALM_GET_INFO_RST, rst.build(), time);
    }

    public static void autoCreateGangCheck(League league, Player player){
        SecretRealmGlobalData globalData = GlobalDataManager.getData(GlobalDataType.secretRealm);
        SecretRealmMapLeagueInfo leagueInfo = globalData.getMapLeagueInfos().get(league.getLeagueId());
        if(leagueInfo == null){
            //没有，检查能否自动创建大阵
            List<SecretRealmMapTemplate> maps = new ArrayList<>(mapTemplates.values());
            Collections.sort(maps, Comparator.comparingInt(o -> o.openChapter));

            for (SecretRealmMapTemplate template : maps) {
                if(player.getInstanceModel().isPassMission(template.openChapter)){
                    //能，创建大阵
                    SecretRealmMapInfo mapInfo = globalData.getMapInfo(template.id);
                    if(Objects.isNull(mapInfo)){
                        continue;
                    }
                    //检查是否符合创建秘境的条件
                    if(template.limitGangLev > league.getLevel()){
                        return;
                    }

                    //检查联盟的势力值
                    if (league.getForceValue() < getStrongholdVal()) {
                        return;
                    }


                    List<String> list = new ArrayList<>(template.createGangPoints);
                    Collections.shuffle(list);
                    for(String gangPoint : list) {
                        //检查联盟是否创建了驻地
                        if (mapInfo.getMapGangInfos().containsKey(gangPoint)) {
                            continue;
                        }

                        //检查是否帮派归属
                        Map<String, Long> mapGangInfos = mapInfo.getMapGangInfos();
                        for (String targetGangPoint : mapGangInfos.keySet()) {
                            long curLeagueId = mapGangInfos.getOrDefault(targetGangPoint, 0L);
                            SecretRealmMapLeagueInfo curLeagueInfo = globalData.getMapLeagueInfos().get(curLeagueId);
                            if (Objects.nonNull(curLeagueInfo)) {
                                //检查距离,目标点跟势力点的距离比半径小，说明采集点在圈内
                                if (calMapPointDistance(gangPoint, targetGangPoint) < (double) curLeagueInfo.getRadius() + SecretRealmService.initialArea()) {
                                    break;
                                }
                            }
                        }
                        league.setNextCreateSRGangTime(ServerConstants.getCurrentTimeMillis() + getCreateCD() * DateTimeUtil.MillisOfHour);
                        //开始创建势力
                        mapInfo.getMapGangInfos().put(gangPoint, league.getLeagueId());
                        leagueInfo = buildSecretRealmLeagueInfo(template.id, gangPoint, league);
                        globalData.getMapLeagueInfos().put(leagueInfo.getLeagueId(), leagueInfo);
                        return;
                    }
                }else{
                    return;
                }
            }
        }
    }

    /**
     * 创建秘境驻地
     */
    @Handler(PtCode.SECRET_REALM_CREATE_GANG_REQ)
    private void creatGang(Player player, PbProtocol.SecretRealmCreateGangReq req, long time) {
        PbProtocol.SecretRealmCreateGangRst.Builder rst = PbProtocol.SecretRealmCreateGangRst.newBuilder().setResult(Text.genOkServerRstInfo());
        int mapId = req.getMapId();
        String gangPoint = req.getGangPoint();
        logic:{
            SecretRealmGlobalData globalData = GlobalDataManager.getData(GlobalDataType.secretRealm);
            SecretRealmMapInfo mapInfo = globalData.getMapInfo(mapId);
            if(Objects.isNull(mapInfo) || Objects.isNull(mapInfo.getTemplate()) || !mapInfo.getTemplate().createGangPoints.contains(gangPoint)){
                rst.setResult(Text.genServerRstInfo(Text.秘境不存在));
                break logic;
            }
            //检查是否符合创建秘境的条件
            SecretRealmMapTemplate template = mapInfo.getTemplate();
            League league = LeagueManager.getLeagueByPlayer(player);
            if(template.limitGangLev > league.getLevel()){
                rst.setResult(Text.genServerRstInfo(Text.联盟等级不足创建秘境));
                break logic;
            }
            if(league.getLeader() != player.getPlayerId()){
                rst.setResult(Text.genServerRstInfo(Text.帮派权限不足));
                break logic;
            }
            //检查联盟是否创建了驻地
            SecretRealmMapLeagueInfo mapLeagueInfo = globalData.getMapLeagueInfos().get(league.getLeagueId());
            if(Objects.nonNull(mapLeagueInfo) && !mapLeagueInfo.getGangPoint().isEmpty()){
                rst.setResult(Text.genServerRstInfo(Text.联盟已经创建秘境));
                break logic;
            }
            if(mapInfo.getMapGangInfos().containsKey(gangPoint)){
                rst.setResult(Text.genServerRstInfo(Text.秘境点位上存在联盟));
                break logic;
            }
            //检查联盟的势力值
            if(league.getForceValue() < getStrongholdVal()){
                rst.setResult(Text.genServerRstInfo(Text.势力值不足));
                break logic;
            }
            //检查是否帮派归属
            Map<String, Long> mapGangInfos = mapInfo.getMapGangInfos();
            for (String targetGangPoint : mapGangInfos.keySet()) {
                long curLeagueId = mapGangInfos.getOrDefault(targetGangPoint, 0L);
                SecretRealmMapLeagueInfo curLeagueInfo = globalData.getMapLeagueInfos().get(curLeagueId);
                if(Objects.nonNull(curLeagueInfo)){
                    //检查距离,目标点跟势力点的距离比半径小，说明采集点在圈内
                    if(calMapPointDistance(gangPoint, targetGangPoint) < (double)curLeagueInfo.getRadius() + SecretRealmService.initialArea()){
                        rst.setResult(Text.genServerRstInfo(Text.存在帮派归属不能占领));
                        break logic;
                    }
                }
            }
            //检查创建cd
            if(league.getNextCreateSRGangTime() > ServerConstants.getCurrentTimeMillis()){
                rst.setResult(Text.genServerRstInfo(Text.创建据点cd中));
                break logic;
            }
            league.setNextCreateSRGangTime(ServerConstants.getCurrentTimeMillis() + getCreateCD() * DateTimeUtil.MillisOfHour);
            //开始创建势力
            mapInfo.getMapGangInfos().put(gangPoint, league.getLeagueId());
            SecretRealmMapLeagueInfo leagueInfo = buildSecretRealmLeagueInfo(mapId, gangPoint, league);
            globalData.getMapLeagueInfos().put(leagueInfo.getLeagueId(), leagueInfo);

            rst.setMapInfo(globalData.genSecretRealmMapInfoPb(mapId));
        }

        player.send(PtCode.SECRET_REALM_CREATE_GANG_RST, rst.build(), time);
    }

    /**
     * 构建联盟势力信息
     */
    private static SecretRealmMapLeagueInfo buildSecretRealmLeagueInfo(int mapId, String gangPoint, League league){
        SecretRealmMapLeagueInfo leagueInfo = new SecretRealmMapLeagueInfo(ServerConstants.getCurrentTimeMillis());
        leagueInfo.setLeagueId(league.getLeagueId());
        leagueInfo.setMapId(mapId);
        leagueInfo.setGangPoint(gangPoint);
        leagueInfo.setRadius(SecretRealmService.initialArea());
        SecretRealmCampAttTemplate campAttTemplate = campAttTemplates.get(league.getLevel());
        leagueInfo.setHp(campAttTemplate.getShieldLife(league));
        leagueInfo.setStatus(SecretRealmMapLeagueInfo.status_increase, true);
        return leagueInfo;
    }

    /**
     * 移动秘境驻地
     */
    @Handler(PtCode.SECRET_REALM_MOVE_GANG_REQ)
    private void moveGang(Player player, PbProtocol.SecretRealmMoveGangReq req, long time){
        PbProtocol.SecretRealmMoveGangRst.Builder rst = PbProtocol.SecretRealmMoveGangRst.newBuilder().setResult(Text.genOkServerRstInfo());
        int mapId = req.getMapId();
        String gangPoint = req.getGangPoint();
        logic:{
            //检查
            SecretRealmGlobalData globalData = GlobalDataManager.getData(GlobalDataType.secretRealm);
            SecretRealmMapInfo mapInfo = globalData.getMapInfo(mapId);
            if(Objects.isNull(mapInfo) || Objects.isNull(mapInfo.getTemplate())|| !mapInfo.getTemplate().createGangPoints.contains(gangPoint)){
                rst.setResult(Text.genServerRstInfo(Text.秘境不存在));
                break logic;
            }
            //检查是否符合创建秘境的条件
            SecretRealmMapTemplate template = mapInfo.getTemplate();
            League league = LeagueManager.getLeagueByPlayer(player);
            if(template.limitGangLev > league.getLevel()){
                rst.setResult(Text.genServerRstInfo(Text.联盟等级不足创建秘境));
                break logic;
            }
            //检查联盟是否创建了驻地
            SecretRealmMapLeagueInfo oldMapLeagueInfo = globalData.getMapLeagueInfos().get(league.getLeagueId());
            if(Objects.isNull(oldMapLeagueInfo) || oldMapLeagueInfo.getGangPoint().isEmpty()){
                rst.setResult(Text.genServerRstInfo(Text.秘境旧点位没有联盟));
                break logic;
            }
            SecretRealmMapInfo oldMapInfo = globalData.getMapInfo(oldMapLeagueInfo.getMapId());
            if(Objects.isNull(oldMapInfo)){
                rst.setResult(Text.genServerRstInfo(Text.秘境不存在));
                break logic;
            }
            if(mapInfo.getMapGangInfos().containsKey(gangPoint)){
                rst.setResult(Text.genServerRstInfo(Text.秘境点位上存在联盟));
                break logic;
            }
            //检查联盟的势力值
            if(league.getForceValue() < getStrongholdVal()){
                rst.setResult(Text.genServerRstInfo(Text.势力值不足));
                break logic;
            }
            if(league.getLeader() != player.getPlayerId()){
                rst.setResult(Text.genServerRstInfo(Text.帮派权限不足));
                break logic;
            }
            //检查创建cd
            if(league.getNextCreateSRGangTime() > ServerConstants.getCurrentTimeMillis()){
                rst.setResult(Text.genServerRstInfo(Text.创建据点cd中));
                break logic;
            }
            //检查是否帮派归属
            Map<String, Long> mapGangInfos = mapInfo.getMapGangInfos();
            for (String targetGangPoint : mapGangInfos.keySet()) {
                long curLeagueId = mapGangInfos.getOrDefault(targetGangPoint, 0L);
                SecretRealmMapLeagueInfo curLeagueInfo = globalData.getMapLeagueInfos().get(curLeagueId);
                if(Objects.nonNull(curLeagueInfo) && curLeagueId != oldMapLeagueInfo.getLeagueId()){
                    //检查距离,目标点跟势力点的距离比半径小，说明采集点在圈内
                    if(calMapPointDistance(gangPoint, targetGangPoint) < (double)curLeagueInfo.getRadius() + SecretRealmService.initialArea()){
                        rst.setResult(Text.genServerRstInfo(Text.存在帮派归属不能占领));
                        break logic;
                    }
                }
            }
            if(oldMapLeagueInfo.getMapId() != mapId && oldMapInfo.getReignLeagueId() == league.getLeagueId()){
                //统治战场标记清除
                oldMapInfo.setReignLeagueId(-1);
            }
            //移除旧势力
            globalData.getMapInfo(oldMapLeagueInfo.getMapId()).getMapGangInfos().remove(oldMapLeagueInfo.getGangPoint());

            //开始移动势力
            mapInfo.getMapGangInfos().put(gangPoint, league.getLeagueId());
            SecretRealmMapLeagueInfo leagueInfo = buildSecretRealmLeagueInfo(mapId, gangPoint, league);
            leagueInfo.setStatus(SecretRealmMapLeagueInfo.status_first_break, false);
            globalData.getMapLeagueInfos().put(leagueInfo.getLeagueId(), leagueInfo);

            rst.setMapInfo(globalData.genSecretRealmMapInfoPb(mapId));
        }
        player.send(PtCode.SECRET_REALM_MOVE_GANG_RST, rst.build(), time);
    }

    /**
     * 采集
     */
    @Handler(PtCode.SECRET_REALM_COLLECT_REQ)
    private void collect(Player player, PbProtocol.SecretRealmCollectReq req, long time){
        PbProtocol.SecretRealmCollectRst.Builder rst = PbProtocol.SecretRealmCollectRst.newBuilder().setResult(Text.genOkServerRstInfo());
        int mapId = req.getMapId();
        String collectPoint = req.getCollectPoint();
        int lineupType = req.getLineupType();
        logic:{
            SecretRealmGlobalData globalData = GlobalDataManager.getData(GlobalDataType.secretRealm);
            SecretRealmMapInfo mapInfo = globalData.getMapInfo(mapId);
            if(Objects.isNull(mapInfo) || Objects.isNull(mapInfo.getTemplate())|| !mapInfo.getTemplate().collectPoints.contains(collectPoint)){
                rst.setResult(Text.genServerRstInfo(Text.秘境不存在));
                break logic;
            }
            SecretRealmPlayerCollectInfo playerCollectInfo = globalData.getPlayerCollectInfo(player.getPlayerId());
            int text = checkLineUpType(player, lineupType);
            if(text != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(text));
                break logic;
            }
            SecretRealmCollectInfo collectInfo = mapInfo.getMapCollectInfos().get(collectPoint);
            if(Objects.nonNull(collectInfo)){
                if(collectInfo.getOccupyPlayerId() > 0){
                    rst.setResult(Text.genServerRstInfo(Text.秘境不能采集));
                    break logic;
                }
                //秘境消失或者没有奖励，不能采集，等待消失
                if(collectInfo.getDisTime() > 0 && collectInfo.getDisTime() <= ServerConstants.getCurrentTimeMillis()
                    || collectInfo.getSurplusRewardTime() <= 0){
                    rst.setResult(Text.genServerRstInfo(Text.秘境不能采集));
                    break logic;
                }
            }
            //检查每周采集时长
            if(playerCollectInfo.getWeekCollectTime() >= getWeekLimitTime(LeagueManager.getLeagueByPlayerId(collectInfo.getOccupyPlayerId()))){
                rst.setResult(Text.genServerRstInfo(Text.本周不能采集));
                break logic;
            }
            if(playerCollectInfo.getDayCollectTime() >= getDayLimitTime()){
                rst.setResult(Text.genServerRstInfo(Text.今日不能采集));
                break logic;
            }

            long leagueId = LeagueManager.getLeagueId(player);
            //检查是否帮派归属
            Map<String, Long> mapGangInfos = mapInfo.getMapGangInfos();
            for (String gangPoint : mapGangInfos.keySet()) {
                long curLeagueId = mapGangInfos.getOrDefault(gangPoint, 0L);
                SecretRealmMapLeagueInfo curLeagueInfo = globalData.getMapLeagueInfos().get(curLeagueId);
                if(Objects.nonNull(curLeagueInfo)){
                    //检查距离,采集点跟势力点的距离比半径小，说明采集点在圈内
                    if(leagueId != curLeagueId &&
                            calMapPointDistance(collectPoint, gangPoint) < (double)curLeagueInfo.getRadius()){
                        rst.setResult(Text.genServerRstInfo(Text.存在帮派归属不能采集));
                        break logic;
                    }
                }
            }
            List<HeroUnit> heroUnits = player.getLineupModel().createHeroUnits(StageType.secretRealmGang, LineupType.getType(lineupType));
            if(CollectionUtil.isEmpty(heroUnits)){
                rst.setResult(Text.genServerRstInfo(Text.阵容不存在));
                break logic;
            }

            SecretRealmCollectionTemplate collectionTemplate = SecretRealmService.collectionTemplates.get(collectInfo.getCollectId());
            List<RewardTemplate> collectCosts = collectionTemplate.collectCost;
            if(CollectionUtil.isNotEmpty(collectCosts)){
                List<Reward> rewardList = Reward.templateCollectionToReward(collectCosts);
                if(Reward.check(player, rewardList) != -1){
                    rst.setResult(Text.genServerRstInfo(Text.消耗不足));
                    break logic;
                }
                Reward.remove(rewardList, player, BehaviorType.secretRealmCollect);
            }

            long maxFightPower = 0;
            int showHeroTempId = 0;
            for (HeroUnit heroUnit : heroUnits) {
                if(Objects.nonNull(heroUnit) && maxFightPower < heroUnit.getFightingPower()){
                    maxFightPower =  heroUnit.getFightingPower();
                    showHeroTempId = heroUnit.getTid();
                }
            }

            //开始采集
            collectInfo.setOccupyPlayerId(player.getPlayerId());
            collectInfo.setStartTime(ServerConstants.getCurrentTimeMillis());
            collectInfo.setDisTime(-1);
            collectInfo.setShowHeroTempId(showHeroTempId);
            collectInfo.setLineupType(req.getLineupType());
            playerCollectInfo.getCollects().add(Pair.of(mapId, collectPoint));

            //记录采集日志
            League league = LeagueManager.getLeagueById(leagueId);
            if(Objects.nonNull(league)){
                league.addLeagueLog(LeagueLogType.collect, player.getName(), mapId + "", collectPoint);
            }

            rst.setCollectInfo(collectInfo.genPb());
//            player.postEvent(PlayerEventType.secretRealmCollect);
        }


        player.send(PtCode.SECRET_REALM_COLLECT_RST, rst.build(), time);
    }

    /**
     * 计算地图坐标点距离
     */
    public static double calMapPointDistance(String point1, String point2){
        String[] s1 = point1.split("\\|");
        int x1 = Integer.parseInt(s1[0]);
        int y1 = Integer.parseInt(s1[1]);
        String[] s2 = point2.split("\\|");
        int x2 = Integer.parseInt(s2[0]);
        int y2 = Integer.parseInt(s2[1]);

        return Math.sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1));
    }

    /**
     * 获取防御列表
     */
    @Handler(PtCode.SECRET_REALM_GET_LEAGUE_DEFENCE_LIST_REQ)
    private void getLeagueDefenceList(Player player, PbProtocol.SecretRealmGetLeagueDefenceListReq req, long time){
        PbProtocol.SecretRealmGetLeagueDefenceListRst.Builder rst = PbProtocol.SecretRealmGetLeagueDefenceListRst.newBuilder().setResult(Text.genOkServerRstInfo());
        long leagueId = req.getLeagueId();
        logic:{
            if(leagueId <= 0){
                //自己联盟
                leagueId = LeagueManager.getLeagueId(player);
            }
            SecretRealmGlobalData globalData = GlobalDataManager.getData(GlobalDataType.secretRealm);
            SecretRealmMapLeagueInfo mapLeagueInfo = globalData.getMapLeagueInfos().get(leagueId);
            if(Objects.isNull(mapLeagueInfo)){
                rst.setResult(Text.genServerRstInfo(Text.联盟势力不存在));
                break logic;
            }

            ThreadPool.execute(new SecretRealmGetDefenceAsync(player, mapLeagueInfo, time));
            return ;
//            Map<Long, Map<Integer, SecretRealmDefInfo>> defPlayerIds = mapLeagueInfo.getDefPlayerIds();
//            for (long defPlayerId : defPlayerIds.keySet()) {
//                for (SecretRealmDefInfo defInfo : defPlayerIds.get(defPlayerId).values()) {
//                    rst.addPlayerListInfos(SecretRealmManager.buildPlayerListInfo(defPlayerId, defInfo.getDefStartTime(), 0, defInfo.getHeroTempId(), defInfo.getLineupType()));
//                }
//            }
        }

        player.send(PtCode.SECRET_REALM_GET_LEAGUE_DEFENCE_LIST_RST, rst.build(), time);
    }

    /**
     * 获取进攻列表
     */
    @Handler(PtCode.SECRET_REALM_GET_ATTACK_LEAGUE_LIST_REQ)
    private void getAttackLeagueList(Player player, PbProtocol.SecretRealmGetAttackLeagueListReq req, long time){
        PbProtocol.SecretRealmGetAttackLeagueListRst.Builder rst = PbProtocol.SecretRealmGetAttackLeagueListRst.newBuilder().setResult(Text.genOkServerRstInfo());

        logic:{
            long leagueId = LeagueManager.getLeagueId(player);
            SecretRealmGlobalData globalData = GlobalDataManager.getData(GlobalDataType.secretRealm);
            SecretRealmMapLeagueInfo mapLeagueInfo = globalData.getMapLeagueInfos().get(leagueId);
            if(Objects.isNull(mapLeagueInfo)){
                rst.setResult(Text.genServerRstInfo(Text.联盟势力不存在));
                break logic;
            }

            Map<Long, Map<Integer, SecretRealmAtkInfo>> attackPlayerIds = mapLeagueInfo.getAttackPlayerIds();
            for (Long attackPlayerId : attackPlayerIds.keySet()) {
                Map<Integer, SecretRealmAtkInfo> lineupTypeSecretRealmAtkInfoMap = attackPlayerIds.get(attackPlayerId);
                for (Integer lineupType : lineupTypeSecretRealmAtkInfoMap.keySet()) {
                    SecretRealmAtkInfo info = lineupTypeSecretRealmAtkInfoMap.get(lineupType);
                    rst.addPlayerListInfos(SecretRealmManager.buildPlayerListInfo(attackPlayerId, info.getAtkStartTime(), info.realHurt(mapLeagueInfo), info.getHeroTempId(), info.getLineupType().getType()));
                }
            }
        }
        player.send(PtCode.SECRET_REALM_GET_ATTACK_LEAGUE_LIST_RST, rst.build(), time);
    }

    /**
     * 防守大阵
     */
    @Handler(PtCode.SECRET_REALM_GANG_DEFENCE_REQ)
    private void gangDefence(Player player, PbProtocol.SecretRealmGangDefenceReq req, long time){
        PbProtocol.SecretRealmGangDefenceRst.Builder rst = PbProtocol.SecretRealmGangDefenceRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            League league = LeagueManager.getLeagueByPlayer(player);
            if(Objects.isNull(league)){
                rst.setResult(Text.genServerRstInfo(Text.帮派不存在));
                break logic;
            }
            int lineupType = req.getLineupType();
            SecretRealmGlobalData globalData = GlobalDataManager.getData(GlobalDataType.secretRealm);
            SecretRealmMapLeagueInfo mapLeagueInfo = globalData.getMapLeagueInfos().get(league.getLeagueId());
            if(Objects.isNull(mapLeagueInfo)){
                rst.setResult(Text.genServerRstInfo(Text.联盟势力不存在));
                break logic;
            }
            //检查行动力
            Reward costPower = getCaDC().createReward();
            if(costPower.check(player) != -1){
                rst.setResult(Text.genServerRstInfo(Text.行动力不足));
                break logic;
            }

            //检查当前是否正在驻守，进攻，采集
            int text = checkLineUpType(player, lineupType);
            if(text != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(text));
                break logic;
            }
            if(CollectionUtil.isNotEmpty(mapLeagueInfo.getAttackPlayerIds())){
                rst.setResult(Text.genServerRstInfo(Text.秘境帮派存在进攻军不能防守));
                break logic;
            }
            if(player.getSecretRealmModel().getSurplusDefTimes() <= 0){
                rst.setResult(Text.genServerRstInfo(Text.秘境驻守次数不足));
                break logic;
            }
            //扣除行动力
            costPower.remove(player,  BehaviorType.gangDefence);

            List<HeroUnit> heroUnits = player.getLineupModel().createHeroUnits(StageType.secretRealmGang, LineupType.getType(lineupType));
            if(CollectionUtil.isEmpty(heroUnits)){
                rst.setResult(Text.genServerRstInfo(Text.阵容不存在));
                break logic;
            }
            long maxFightPower = 0;
            int showHeroTempId = 0;
            for (HeroUnit heroUnit : heroUnits) {
                if(Objects.nonNull(heroUnit) && maxFightPower < heroUnit.getFightingPower()){
                    maxFightPower =  heroUnit.getFightingPower();
                    showHeroTempId = heroUnit.getTid();
                }
            }
            SecretRealmDefInfo secretRealmDefInfo = new SecretRealmDefInfo();
            secretRealmDefInfo.setDefStartTime(ServerConstants.getCurrentTimeMillis());
            secretRealmDefInfo.setLineupType(req.getLineupType());
            secretRealmDefInfo.setHeroTempId(showHeroTempId);
            if(!mapLeagueInfo.getDefPlayerIds().containsKey(player.getPlayerId())){
                mapLeagueInfo.getDefPlayerIds().put(player.getPlayerId(), new HashMap<>());
            }
            mapLeagueInfo.getDefPlayerIds().get(player.getPlayerId()).put(secretRealmDefInfo.getLineupType(), secretRealmDefInfo);

            league.addLeagueLog(LeagueLogType.unionDefense, player.getName());

            Map<Long, Map<Integer, SecretRealmDefInfo>> defPlayerIds = mapLeagueInfo.getDefPlayerIds();
            for (long defPlayerId : defPlayerIds.keySet()) {
                for (SecretRealmDefInfo defInfo : defPlayerIds.get(defPlayerId).values()) {
                    rst.addPlayerListInfos(SecretRealmManager.buildPlayerListInfo(defPlayerId, defInfo.getDefStartTime(), 0, defInfo.getHeroTempId(), defInfo.getLineupType()));
                }
            }
        }
        player.send(PtCode.SECRET_REALM_GANG_DEFENCE_RST, rst.build(), time);
    }

    private int checkLineUpType(Player player, int lineupType){
        long playerId = player.getPlayerId();
        LineupType type = LineupType.getType(lineupType);
        if(Objects.isNull(type)){
            return Text.阵容不存在;
        }
        //检查是不是三套布阵之一
        if(LineupType.secretRealm1.getType() != lineupType
                && LineupType.secretRealm2.getType() != lineupType
                && LineupType.secretRealm3.getType() != lineupType
                && LineupType.secretRealmTemp.getType() != lineupType){
            return Text.阵容不存在;
        }
        SecretRealmGlobalData globalData = GlobalDataManager.getData(GlobalDataType.secretRealm);
        SecretRealmPlayerCollectInfo playerCollectInfo = globalData.getPlayerCollectInfo(playerId);
        //检查采集
        if(Objects.nonNull(playerCollectInfo)){
            for (Pair<Integer, String> collect : playerCollectInfo.getCollects()) {
                SecretRealmMapInfo mapInfo = globalData.getMapInfo(collect.getKey());
                SecretRealmCollectInfo collectInfo = mapInfo.getMapCollectInfos().get(collect.getValue());
                if(Objects.nonNull(collectInfo) && collectInfo.getOccupyPlayerId() == playerId && collectInfo.getLineupType() == lineupType){
                    return Text.秘境队伍正在使用;
                }
            }
        }
        //检查进攻
        Map<Integer, String> atkMaps = globalData.getAttackOtherPlayerInfos().get(playerId);
        if(CollectionUtil.isNotEmpty(atkMaps)){
            for (Integer mapId : atkMaps.keySet()) {
                String point = atkMaps.get(mapId);
                SecretRealmMapInfo mapInfo = globalData.getMapInfo(mapId);
                long leagueId = mapInfo.getMapGangInfos().getOrDefault(point, 0L);
                SecretRealmMapLeagueInfo mapLeagueInfo = globalData.getMapLeagueInfos().get(leagueId);
                if(Objects.nonNull(mapLeagueInfo)){
                    for (Integer typeId : mapLeagueInfo.getAttackPlayerIds().getOrDefault(playerId, new HashMap<>()).keySet()) {
                        if(typeId == lineupType){
                            return Text.秘境队伍正在使用;
                        }
                    }
                }
            }
        }
        //检查防守
        League league = LeagueManager.getLeagueByPlayerId(playerId);
        if(Objects.nonNull(league)){
            SecretRealmMapLeagueInfo mapLeagueInfo = globalData.getMapLeagueInfos().get(league.getLeagueId());
            if(Objects.nonNull(mapLeagueInfo)){
                for (Integer typeId : mapLeagueInfo.getDefPlayerIds().getOrDefault(playerId, new HashMap<>()).keySet()) {
                    if(typeId == lineupType){
                        return Text.秘境队伍正在使用;
                    }
                }
            }
        }
        //检查布阵是否有人
        if(player.getLineupModel().nullCheck(type)){
            return Text.阵容为空;
        }

        return Text.没有异常;
    }

    /**
     * 进攻大阵
     */
    @Handler(PtCode.SECRET_REALM_ATTACK_GANG_REQ)
    private void attackGang(Player player, PbProtocol.SecretRealmAttackGangReq req, long time){
        PbProtocol.SecretRealmAttackGangRst.Builder rst = PbProtocol.SecretRealmAttackGangRst.newBuilder().setResult(Text.genOkServerRstInfo());
        int mapId = req.getMapId();
        String gangPoint = req.getGangPoint();
        boolean isRage = req.getIsRage();
        int lineupType = req.getLineupType();
        logic:{
            SecretRealmGlobalData globalData = GlobalDataManager.getData(GlobalDataType.secretRealm);
            SecretRealmMapInfo mapInfo = globalData.getMapInfo(mapId);
            if(Objects.isNull(mapInfo) || Objects.isNull(mapInfo.getTemplate())|| !mapInfo.getTemplate().createGangPoints.contains(gangPoint)){
                rst.setResult(Text.genServerRstInfo(Text.秘境不存在));
                break logic;
            }
            long targetLeagueId = mapInfo.getMapGangInfos().get(gangPoint);
            SecretRealmMapLeagueInfo targetMapLeagueInfo = globalData.getMapLeagueInfos().get(targetLeagueId);
            League targetLeague = LeagueManager.getLeagueById(targetLeagueId);
            if(Objects.isNull(targetMapLeagueInfo) || Objects.isNull(targetLeague)){
                rst.setResult(Text.genServerRstInfo(Text.联盟势力不存在));
                break logic;
            }
            long leagueId = LeagueManager.getLeagueId(player);
            if(leagueId == targetLeagueId){
                rst.setResult(Text.genServerRstInfo(Text.秘境不能攻打自己联盟));
                break logic;
            }

            //检查当前是否正在驻守，进攻，采集
            SecretRealmPlayerCollectInfo playerCollectInfo = globalData.getPlayerCollectInfo(player.getPlayerId());
            int text = checkLineUpType(player, lineupType);
            if(text != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(text));
                break logic;
            }
            if(targetMapLeagueInfo.getStatus(SecretRealmMapLeagueInfo.status_protect)){
                rst.setResult(Text.genServerRstInfo(Text.目标帮派大阵处于保护期不能攻打));
                break logic;
            }
            //检查是否还有驻守军
            for (Map<Integer, SecretRealmDefInfo> defInfoMap : targetMapLeagueInfo.getDefPlayerIds().values()) {
                if(defInfoMap.size() > 0){
                    rst.setResult(Text.genServerRstInfo(Text.秘境帮派存在驻守军不能攻打));
                    break logic;
                }
            }

            if(player.getSecretRealmModel().getTiredEndTime() > ServerConstants.getCurrentTimeMillis()){
                rst.setResult(Text.genServerRstInfo(Text.秘境疲劳期不能行动));
                break logic;
            }
            //检查行动力
            Reward costPower = getCaAC().createReward();
            if(costPower.check(player) != -1){
                rst.setResult(Text.genServerRstInfo(Text.行动力不足));
                break logic;
            }
            //扣除行动力
            costPower.remove(player,  BehaviorType.attackGang);

            //记录进攻信息
            globalData.getAttackOtherPlayerInfo(player.getPlayerId()).put(mapId, gangPoint);
            List<HeroUnit> heroUnits = player.getLineupModel().createHeroUnits(StageType.secretRealmGang, LineupType.getType(lineupType));
            if(CollectionUtil.isEmpty(heroUnits)){
                rst.setResult(Text.genServerRstInfo(Text.阵容不存在));
                break logic;
            }
            long fightPower = 0;
            long maxFightPower = 0;
            int showHeroTempId = 0;
            for (HeroUnit heroUnit : heroUnits) {
                if(Objects.nonNull(heroUnit)){
                    fightPower += heroUnit.getFightingPower();
                    if(maxFightPower < heroUnit.getFightingPower()){
                        maxFightPower =  heroUnit.getFightingPower();
                        showHeroTempId = heroUnit.getTid();
                    }
                }
            }
            SecretRealmAtkInfo atkInfo = new SecretRealmAtkInfo();
            atkInfo.setPlayerId(player.getPlayerId());
            atkInfo.setAtkStartTime(ServerConstants.getCurrentTimeMillis());
            atkInfo.setHurt(getHurtByPower(fightPower));
            atkInfo.setHeroTempId(showHeroTempId);
            atkInfo.setLineupType(LineupType.getType(lineupType));

            targetMapLeagueInfo.getAttackPlayerId(player.getPlayerId()).put(atkInfo.getLineupType().getType(), atkInfo);
            //修改大阵状态
            targetMapLeagueInfo.setStatus(SecretRealmMapLeagueInfo.status_shed_blood, true);
            targetMapLeagueInfo.setLastShedBloodTime(ServerConstants.getCurrentTimeMillis());
            targetMapLeagueInfo.setStatus(SecretRealmMapLeagueInfo.status_recover_blood, false);
            targetMapLeagueInfo.setLastAttackedFinishTime(0);

            //增加日志
            targetLeague.addLeagueLog(LeagueLogType.unionAttack, req.getMapId() + "", player.getName());

            rst.setMapInfo(globalData.genSecretRealmMapInfoPb(mapId));
            rst.setRageTime(player.getSecretRealmModel().getRageTime());
            rst.setLineupType(lineupType);
        }

        player.send(PtCode.SECRET_REALM_ATTACK_GANG_RST, rst.build(), time);
    }

    /**
     * 抵御进攻者
     */
    @Handler(PtCode.SECRET_REALM_DEF_GANG_ATTACKER_REQ)
    private void defGangAttacker(Player player, PbProtocol.SecretRealmDefGangAttackerReq req, long time){
        PbProtocol.SecretRealmDefGangAttackerRst.Builder rst = PbProtocol.SecretRealmDefGangAttackerRst.newBuilder().setResult(Text.genOkServerRstInfo());
        boolean isRage = req.getIsRage();
        long targetAtkPlayerId = req.getAtkPlayerId();
        long targetLeagueId = req.getTargetLeagueId();
        int lineupType = req.getLineupType();
        int targetLineupType = req.getTargetLineupType();
        logic:
        {
            PlayerSecretRealmModel secretRealmModel = player.getSecretRealmModel();
            if(isRage && secretRealmModel.getRageTime() <= 0){
                rst.setResult(Text.genServerRstInfo(Text.秘境狂暴次数不足));
                break logic;
            }
            SecretRealmGlobalData globalData = GlobalDataManager.getData(GlobalDataType.secretRealm);
            SecretRealmMapLeagueInfo targetLeagueInfo = globalData.getMapLeagueInfos().get(targetLeagueId);
            if (Objects.isNull(targetLeagueInfo)) {
                rst.setResult(Text.genServerRstInfo(Text.联盟势力不存在));
                break logic;
            }
            int text = checkLineUpType(player, lineupType);
            if(text != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(text));
                break logic;
            }
            Map<Integer, SecretRealmAtkInfo> atkInfo = targetLeagueInfo.getAttackPlayerId(targetAtkPlayerId);
            if(CollectionUtil.isEmpty(atkInfo) || !atkInfo.containsKey(targetLineupType)){
                rst.setResult(Text.genServerRstInfo(Text.秘境进攻列表不存在该玩家));
                break logic;
            }
            //检查行动力
            Reward costPower = getCaAC().createReward();
            if(costPower.check(player) != -1){
                rst.setResult(Text.genServerRstInfo(Text.行动力不足));
                break logic;
            }
            //扣除行动力
            costPower.remove(player, BehaviorType.defGangAttacker);

            SecretRealmMapLeagueInfo myLeagueInfo = globalData.getMapLeagueInfos().get(LeagueManager.getLeagueId(player));
            int buffId = -1;
            if(Objects.nonNull(myLeagueInfo)){
                if(myLeagueInfo.getProtectStateFinishTime() > ServerConstants.getCurrentTimeMillis()){
                    String[] protectInfo = SecretRealmService.getLeagueProtect().split("\\|");
                    buffId = Integer.parseInt(protectInfo[0]);
                }
            }

            ThreadPool.execute(new SecretRealmAttackAtkBattleAsync(player, targetLeagueInfo.getMapId()
                    , targetLeagueInfo.getGangPoint(), targetAtkPlayerId, isRage, ServerConstants.getCurrentTimeMillis()
                    , buffId, LineupType.getType(lineupType), LineupType.getType(targetLineupType), time));
            return;
        }
        if(!rst.getResult().getResult()){
            player.send(PtCode.SECRET_REALM_DEF_GANG_ATTACKER_RST, rst.build(), time);
        }
    }

    /**
     * 进攻采集点
     */
    @Handler(PtCode.SECRET_REALM_COLLECT_BATTLE_REQ)
    private void collectBattle(Player player, PbProtocol.SecretRealmCollectBattleReq req, long time){
        PbProtocol.SecretRealmCollectBattleRst.Builder rst = PbProtocol.SecretRealmCollectBattleRst.newBuilder().setResult(Text.genOkServerRstInfo());
        int mapId = req.getMapId();
        String collectPoint = req.getCollectPoint();
        int lineupType = req.getLineupType();
        logic:{
            SecretRealmGlobalData globalData = GlobalDataManager.getData(GlobalDataType.secretRealm);
            SecretRealmMapInfo mapInfo = globalData.getMapInfo(mapId);
            if(Objects.isNull(mapInfo) || Objects.isNull(mapInfo.getTemplate())|| !mapInfo.getTemplate().collectPoints.contains(collectPoint)){
                rst.setResult(Text.genServerRstInfo(Text.秘境不存在));
                break logic;
            }
            SecretRealmCollectInfo collectInfo = mapInfo.getMapCollectInfos().get(collectPoint);
            long leagueId = LeagueManager.getLeagueId(player);
            if(Objects.isNull(collectInfo) || collectInfo.getOccupyPlayerId() == player.getPlayerId()
                || (leagueId > 0 && leagueId == LeagueManager.getLeagueId(collectInfo.getOccupyPlayerId()))){
                rst.setResult(Text.genServerRstInfo(Text.秘境采集点不能攻打));
                break logic;
            }
            int text = checkLineUpType(player, lineupType);
            if(text != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(text));
                break logic;
            }

            Map<String, Long> mapGangInfos = mapInfo.getMapGangInfos();
            for (String gangPoint : mapGangInfos.keySet()) {
                long curLeagueId = mapGangInfos.getOrDefault(gangPoint, 0L);
                SecretRealmMapLeagueInfo curLeagueInfo = globalData.getMapLeagueInfos().get(curLeagueId);
                if(Objects.nonNull(curLeagueInfo)){
                    //检查距离,采集点跟势力点的距离比半径小，说明采集点在圈内
                    if(leagueId != curLeagueId &&
                            calMapPointDistance(collectPoint, gangPoint) < (double)curLeagueInfo.getRadius()){
                        rst.setResult(Text.genServerRstInfo(Text.存在帮派归属不能攻打));
                        break logic;
                    }
                }
            }

            //检查行动力
            Reward costPower = getCaAC().createReward();
            PlayerCurrencyModel currencyModel = player.getCurrencyModel();
            if(costPower.check(player) != -1){
                rst.setResult(Text.genServerRstInfo(Text.行动力不足));
                break logic;
            }

            //扣除行动力
            costPower.remove(player, BehaviorType.secretRealmCollectAtk);

            //开打
            ThreadPool.execute(new SecretRealmCollectBattleAsync(player, mapId, collectPoint, collectInfo.getOccupyPlayerId()
                , LineupType.getType(lineupType), LineupType.getType(collectInfo.getLineupType()), time));
            return;

        }
        if(!rst.getResult().getResult()){
            player.send(PtCode.SECRET_REALM_COLLECT_BATTLE_RST, rst.build(), time);
        }
    }

    /**
     * 进攻驻守军
     */
    @Handler(PtCode.SECRET_REALM_ATTACK_GANG_DEF_REQ)
    private void attackGangDef(Player player, PbProtocol.SecretRealmAttackGangDefReq req, long time){
        PbProtocol.SecretRealmAttackGangDefRst.Builder rst = PbProtocol.SecretRealmAttackGangDefRst.newBuilder().setResult(Text.genOkServerRstInfo());
        long targetLeagueId = req.getLeagueId();
        long defPlayerId = req.getDefPlayerId();
        boolean isRage = req.getIsRage();
        int lineupType = req.getLineupType();
        int targetLineupType = req.getTargetLineupType();
        logic:
        {
            long leagueId = LeagueManager.getLeagueId(player);
            if(leagueId == targetLeagueId){
                rst.setResult(Text.genServerRstInfo(Text.秘境不能攻打自己联盟));
                break logic;
            }
            PlayerSecretRealmModel secretRealmModel = player.getSecretRealmModel();
            if(isRage && secretRealmModel.getRageTime() <= 0){
                rst.setResult(Text.genServerRstInfo(Text.秘境狂暴次数不足));
                break logic;
            }
            SecretRealmGlobalData globalData = GlobalDataManager.getData(GlobalDataType.secretRealm);
            SecretRealmMapLeagueInfo targetLeagueInfo = globalData.getMapLeagueInfos().get(targetLeagueId);
            if(Objects.isNull(targetLeagueInfo)){
                rst.setResult(Text.genServerRstInfo(Text.秘境不存在));
                break logic;
            }
            SecretRealmMapInfo mapInfo = globalData.getMapInfo(targetLeagueInfo.getMapId());
            if (Objects.isNull(mapInfo) || Objects.isNull(mapInfo.getTemplate()) || !mapInfo.getTemplate().createGangPoints.contains(targetLeagueInfo.getGangPoint())) {
                rst.setResult(Text.genServerRstInfo(Text.秘境不存在));
                break logic;
            }
            if(!targetLeagueInfo.getDefPlayerIds().containsKey(defPlayerId) || !targetLeagueInfo.getDefPlayerIds().get(defPlayerId).containsKey(targetLineupType)){
                rst.setResult(Text.genServerRstInfo(Text.秘境防御列表不存在该玩家));
                break logic;
            }
            if(player.getSecretRealmModel().getTiredEndTime() > ServerConstants.getCurrentTimeMillis()){
                rst.setResult(Text.genServerRstInfo(Text.秘境疲劳期不能行动));
                break logic;
            }
            int text = checkLineUpType(player, lineupType);
            if(text != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(text));
                break logic;
            }
            //检查行动力
            String[] split = getCaAD().split("\\|");
            int costPower = Integer.parseInt(split[0]);
//            int returnPower = Integer.parseInt(split[1]);
            PlayerCurrencyModel currencyModel = player.getCurrencyModel();
            if(costPower > currencyModel.getCurrency(Currency.actionPower)){
                rst.setResult(Text.genServerRstInfo(Text.行动力不足));
                break logic;
            }
            //扣除行动力
            currencyModel.decreaseCurrency(Currency.actionPower, costPower, BehaviorType.defGangAttacker);

            int buffId = -1;
            if(targetLeagueInfo.getProtectStateFinishTime() > ServerConstants.getCurrentTimeMillis()){
                String[] protectInfo = SecretRealmService.getLeagueProtect().split("\\|");
                buffId = Integer.parseInt(protectInfo[0]);
            }

            ThreadPool.execute(new SecretRealmAttackDefBattleAsync(player, targetLeagueInfo.getMapId()
                    , targetLeagueInfo.getGangPoint(), defPlayerId, isRage, buffId
                    , LineupType.getType(lineupType), LineupType.getType(targetLineupType), time));
            return;

        }
        if(!rst.getResult().getResult()){
            player.send(PtCode.SECRET_REALM_ATTACK_GANG_DEF_RST, rst.build(), time);
        }
    }

    @Handler(PtCode.SECRET_REALM_RANK_REQ)
    private void rank(Player player, PbProtocol.SecretRealmRankReq req, long time){
        PbProtocol.SecretRealmRankRst.Builder rst = PbProtocol.SecretRealmRankRst.newBuilder().setResult(Text.genOkServerRstInfo());
        int mapId = req.getMapId();
        logic:{
            SecretRealmGlobalData globalData = GlobalDataManager.getData(GlobalDataType.secretRealm);
            SecretRealmMapInfo mapInfo = globalData.getMapInfo(mapId);
            if(Objects.isNull(mapInfo) || Objects.isNull(mapInfo.getTemplate())){
                rst.setResult(Text.genServerRstInfo(Text.秘境不存在));
                break logic;
            }
            for (SecretRealmRankInfo rankInfo : mapInfo.getRankInfos()) {
                rst.addRankList(rankInfo.genPb());
            }
        }
        player.send(PtCode.SECRET_REALM_RANK_RST, rst.build(), time);
    }

    @Handler(PtCode.SECRET_REALM_GET_GANG_INFO_REQ)
    private void getGangInfo(Player player, PbProtocol.SecretRealmGetGangInfoReq req, long time){
        PbProtocol.SecretRealmGetGangInfoRst.Builder rst = PbProtocol.SecretRealmGetGangInfoRst.newBuilder().setResult(Text.genOkServerRstInfo());
        int mapId = req.getMapId();
        String gangPoint = req.getGangPoint();
        logic:{
            SecretRealmGlobalData globalData = GlobalDataManager.getData(GlobalDataType.secretRealm);
            SecretRealmMapInfo mapInfo = globalData.getMapInfo(mapId);
            if(Objects.isNull(mapInfo) || Objects.isNull(mapInfo.getTemplate())){
                rst.setResult(Text.genServerRstInfo(Text.秘境不存在));
                break logic;
            }

            long leagueId = mapInfo.getMapGangInfos().getOrDefault(gangPoint, -1L);
            if(leagueId < 0){
                rst.setResult(Text.genServerRstInfo(Text.联盟势力不存在));
                break logic;
            }
            SecretRealmMapLeagueInfo mapLeagueInfo = globalData.getMapLeagueInfos().get(leagueId);
            if(Objects.isNull(mapLeagueInfo)){
                rst.setResult(Text.genServerRstInfo(Text.联盟势力不存在));
                break logic;
            }

            rst.setDetailInfo(mapLeagueInfo.genDetailInfo());
        }
        player.send(PtCode.SECRET_REALM_GET_GANG_INFO_RST, rst.build(), time);
    }

    @Handler(PtCode.SECRET_REALM_GET_MY_GANG_INFO_REQ)
    private void getMyGangInfo(Player player, PbProtocol.SecretRealmGetMyGangInfoReq req, long time){
        PbProtocol.SecretRealmGetMyGangInfoRst.Builder rst = PbProtocol.SecretRealmGetMyGangInfoRst.newBuilder().setResult(Text.genOkServerRstInfo());
        SecretRealmGlobalData globalData = GlobalDataManager.getData(GlobalDataType.secretRealm);
        long leagueId = LeagueManager.getLeagueId(player);
        SecretRealmMapLeagueInfo mapLeagueInfo = globalData.getMapLeagueInfos().get(leagueId);
        if(Objects.nonNull(mapLeagueInfo)){
            PbSecretRealm.SecretRealmMapInfo.Builder mapInfo = PbSecretRealm.SecretRealmMapInfo.newBuilder();
            mapInfo.setMapId(mapLeagueInfo.getMapId());
            mapInfo.addGangInfos(mapLeagueInfo.genPb());
            rst.setMapInfo(mapInfo);
        }
        SecretRealmPlayerCollectInfo playerCollectInfo = globalData.getPlayerCollectInfo(player.getPlayerId());
        if(Objects.nonNull(playerCollectInfo)){
            rst.setDayCollectTime(playerCollectInfo.getDayCollectTime());
            rst.setWeekCollectTime(playerCollectInfo.getWeekCollectTime());
        }
        rst.setRageTime(player.getSecretRealmModel().getRageTime());
        rst.setServerRegisterNum(PlayerManager.getRegisteredPlayerCount());
        rst.addAllRankMapIds(globalData.getRankMapIds());

        player.send(PtCode.SECRET_REALM_GET_MY_GANG_INFO_RST, rst.build(), time);
    }

    @Handler(PtCode.SECRET_REALM_CANCEL_REQ)
    private void cancel(Player player, PbProtocol.SecretRealmCancelReq req, long time){
        PbProtocol.SecretRealmCancelRst.Builder rst = PbProtocol.SecretRealmCancelRst.newBuilder().setResult(Text.genOkServerRstInfo());
        int type = req.getType();
        int lineupType = req.getLineupType();
        SecretRealmGlobalData globalData = GlobalDataManager.getData(GlobalDataType.secretRealm);
        if(type == 1){
            //采集
            SecretRealmPlayerCollectInfo playerCollectInfo = globalData.getPlayerCollectInfo(player.getPlayerId());
            if(Objects.nonNull(playerCollectInfo)){
                List<Pair<Integer, String>> collects = playerCollectInfo.getCollects();
                for (Pair<Integer, String> collect : collects) {
                    //清除采集点
                    SecretRealmMapInfo mapInfo = globalData.getMapInfo(collect.getKey());
                    //计算防守玩家采集了多长时间
                    SecretRealmCollectInfo collectInfo = mapInfo.getMapCollectInfos().get(collect.getValue());
                    if(collectInfo.getLineupType() == lineupType){
                        //结算
                        collectInfo.settlement(false);

                        playerCollectInfo.getCollects().remove(collect);
                        rst.setCollectInfo(collectInfo.genPb());
                    }
                }
            }
            rst.setDayCollectTime(playerCollectInfo.getDayCollectTime());
            rst.setWeekCollectTime(playerCollectInfo.getWeekCollectTime());
        }else if(type == 2){
            //进攻
            Map<Integer, String> atkInfo = new HashMap<>(globalData.getAttackOtherPlayerInfos().getOrDefault(player.getPlayerId(), new HashMap<>()));
            if(CollectionUtil.isNotEmpty(atkInfo)){
                atkInfo.forEach((k, v)->{
                    long leagueId = globalData.getMapInfo(k).getMapGangInfos().get(v);
                    SecretRealmMapLeagueInfo mapLeagueInfo = globalData.getMapLeagueInfos().get(leagueId);
                    if(Objects.nonNull(mapLeagueInfo)){
                        mapLeagueInfo.getAttackPlayerIds().get(player.getPlayerId()).remove((Integer)lineupType);
                        if(CollectionUtil.isEmpty(mapLeagueInfo.getAttackPlayerIds().get(player.getPlayerId()))){
                            mapLeagueInfo.getAttackPlayerIds().remove(player.getPlayerId());
                        }
                        mapLeagueInfo.checkAttackedFinish();
                    }
                    globalData.getAttackOtherPlayerInfos().get(player.getPlayerId()).remove(mapLeagueInfo.getMapId());
                });
            }
        }else if(type == 3){
            //驻守
            League league = LeagueManager.getLeagueByPlayer(player);
            SecretRealmMapLeagueInfo mapLeagueInfo = globalData.getMapLeagueInfos().get(league.getLeagueId());
            if(Objects.nonNull(mapLeagueInfo)){
                mapLeagueInfo.getDefPlayerIds().get(player.getPlayerId()).remove(lineupType);
                if(CollectionUtil.isEmpty(mapLeagueInfo.getDefPlayerIds().get(player.getPlayerId()))){
                    mapLeagueInfo.getDefPlayerIds().remove(player.getPlayerId());
                }
            }

        }else{
            rst.setResult(Text.genServerRstInfo(Text.秘境参数异常));
        }
        rst.setType(type);
        rst.setLineupType(lineupType);

        player.send(PtCode.SECRET_REALM_CANCEL_RST, rst.build(), time);
    }

    @Override
    public ServerEventType[] getEventTypes() {
        return eventTypes;
    }

    @Override
    public void handle(ServerEvent event) {
        switch (event.getEventType()) {
            case day5clock: {
                refreshDaily();
                break;
            }
        }
    }

    /**
     * 每日刷新
     */
    public static void refreshDaily(){
        //地图采集点位刷新
        SecretRealmGlobalData globalData = GlobalDataManager.getData(GlobalDataType.secretRealm);
        for (int mapId : globalData.getMapInfos().keySet()) {
            collectRefresh(mapId);
        }
        //检测联盟活跃和缩圈
        int influenceWeakDay = getInfluenceWeakDay();
        int reduceDay = getReduceDay();

        for (SecretRealmMapLeagueInfo leagueInfo : globalData.getMapLeagueInfos().values()) {
            League league = LeagueManager.getLeagueById(leagueInfo.getLeagueId());
            if(league != null) {
                int day = (int) ((ServerConstants.getCurrentTimeMillis() - league.getLastDonationTime()) / DateTimeUtil.MillisOfDay);
                if (day >= influenceWeakDay) {
                    //扣除势力值
                    int weakVal = getWeakVal();
                    SecretRealmManager.gangReduceForce(leagueInfo, weakVal, false);
                    leagueInfo.setStatus(SecretRealmMapLeagueInfo.status_increase, false);
                }
                if (day >= reduceDay) {
                    //进入缩圈期
                    leagueInfo.setStatus(SecretRealmMapLeagueInfo.status_decrease, true);
                    leagueInfo.setLastDecreaseRadiusTime(-1);
                    leagueInfo.setStatus(SecretRealmMapLeagueInfo.status_increase, false);
                }
                leagueInfo.setTodayReduceForce(0);
                leagueInfo.setStatus(SecretRealmMapLeagueInfo.status_protect, false);
            }
        }
        //检查已经占领过的最大mapId
        int maxMapId = -1;
        for (SecretRealmMapInfo mapInfo : globalData.getMapInfos().values()) {
            if(mapInfo.getMapGangInfos().size() > 0){
                maxMapId = Math.max(maxMapId, mapInfo.getMapId());
            }
        }
        //取最大mapId降序前4
        List<Integer> rankMapIds = new ArrayList<>();
        for(int i = maxMapId; i >= 1; i--){
            rankMapIds.add(i);
            if(rankMapIds.size() >= 4){
                break;
            }
        }
        globalData.setRankMapIds(rankMapIds);

        globalData.setRankMapIds(rankMapIds);

        for (SecretRealmMapInfo mapInfo : globalData.getMapInfos().values()) {
            //检测当前秘境地图是否需要刷榜
            if(rankMapIds.contains(mapInfo.getMapId())){
                List<SecretRealmRankInfo> rankInfos = new ArrayList<>();
                for (Long leagueId : mapInfo.getMapGangInfos().values()) {
                    SecretRealmMapLeagueInfo mapLeagueInfo = globalData.getMapLeagueInfos().get(leagueId);
                    if(Objects.nonNull(mapLeagueInfo)){
                        rankInfos.add(new SecretRealmRankInfo(mapLeagueInfo));
                    }
                }
                rankInfos.sort((o1, o2) -> {
                    //大阵范围>统治帮派>大阵在本秘境的建立时间
                    if(o2.getRadius() == o1.getRadius()){
                        SecretRealmMapLeagueInfo mapLeagueInfo1 = globalData.getMapLeagueInfos().get(o1.getLeagueId());
                        SecretRealmMapLeagueInfo mapLeagueInfo2 = globalData.getMapLeagueInfos().get(o2.getLeagueId());
                        boolean reign1 = mapInfo.getReignLeagueId() == mapLeagueInfo1.getLeagueId();
                        boolean reign2 = mapInfo.getReignLeagueId() == mapLeagueInfo2.getLeagueId();
                        if(reign1 ^ reign2){
                            return reign1 ? -1 : 1;
                        }else{
                            return (int)(mapLeagueInfo2.getCreateTime() - mapLeagueInfo1.getCreateTime());
                        }
                    }
                    return (int)Math.ceil(o2.getRadius() - o1.getRadius());
                });

                SecretRealmMapTemplate secretRealmMapTemplate = mapTemplates.get(mapInfo.getMapId());
                String[] rankRewardInfos = secretRealmMapTemplate.hangUpProfit.split(";");
                String[] rankRewardInfo1s = secretRealmMapTemplate.hangUpTribute.split(";");
                rankInfos = rankInfos.subList(0, Math.min(rankRewardInfos.length, rankInfos.size()));
                MailType mailType = MailType.SecretRealmRank;
                for (int i = 0; i < rankInfos.size(); i++) {
                    List<RewardTemplate> rankRewards = RewardTemplate.readListFromText(rankRewardInfos[i]);
                    String leagueTribute = rankRewardInfo1s[i];
                    SecretRealmRankInfo secretRealmRankInfo = rankInfos.get(i);
                    League leagueById = LeagueManager.getLeagueById(secretRealmRankInfo.getLeagueId());
                    //增加帮贡
                    leagueById.addLeagueTribute(Integer.parseInt(leagueTribute));
                    for (Long playerId : leagueById.getAllMember()) {
                        //发送个人邮件
                        PbCommons.PbText title = Text.genText(mailType.getTitleId()).build();
                        PbCommons.PbText content = Text.genText(mailType.getContentId(), new TextParamText(String.valueOf(i + 1))).build();
                        MailManager.sendMail(mailType, playerId, title, content, ServerConstants.getCurrentTimeMillis(), Reward.templateCollectionToReward(rankRewards));

                    }

                }
                if(globalData.getRankMapIds().contains(mapInfo.getMapId()) && CollectionUtil.isNotEmpty(rankInfos)){
                    //设置统治帮派
                    SecretRealmRankInfo secretRealmRankInfo = rankInfos.get(0);
                    mapInfo.setReignLeagueId(secretRealmRankInfo.getLeagueId());
                }
                //记录排行
                mapInfo.setRankInfos(rankInfos);
            }else{
                //清除当前地图所有势力的加成
                mapInfo.getRankInfos().clear();
            }
        }


    }

    /**
     * 采集点刷新
     */
    public static void collectRefresh(int mapId){
        SecretRealmGlobalData globalData = GlobalDataManager.getData(GlobalDataType.secretRealm);
        //地图刷新
        SecretRealmMapInfo mapInfo = globalData.getMapInfos().get(mapId);
        SecretRealmMapTemplate secretRealmMapTemplate = mapTemplates.get(mapId);
        if(Objects.nonNull(secretRealmMapTemplate)){
            //地图所有采集点信息
            Map<Integer, Integer> collectTempInfos = new HashMap<>(secretRealmMapTemplate.collectUpdates);
            //去除不用刷新的采集点
            for (SecretRealmCollectInfo collectInfo : mapInfo.getMapCollectInfos().values()) {
                if(collectTempInfos.containsKey(collectInfo.getCollectId())){
                    collectTempInfos.put(collectInfo.getCollectId(), collectTempInfos.get(collectInfo.getCollectId()) - 1);
                }
            }
            //获取可以随机的采集id，并且打乱
            List<Integer> collectIds = new ArrayList<>();
            for (Integer collectId : collectTempInfos.keySet()) {
                for(int i = 0; i < collectTempInfos.get(collectId); i++){
                    collectIds.add(collectId);
                }
            }
            if(CollectionUtil.isNotEmpty(collectIds)){
                Collections.shuffle(collectIds);
                int collectIndex = 0;
                //开始刷新采集点
                Collections.shuffle(collectIds);
                Collections.shuffle(secretRealmMapTemplate.collectPoints);
                for (String collectPoint : secretRealmMapTemplate.collectPoints) {
                    if(collectIds.size() <= collectIndex){
                        break;
                    }
                    if(!mapInfo.getMapCollectInfos().containsKey(collectPoint)){
                        SecretRealmCollectInfo collectInfo = new SecretRealmCollectInfo();
                        collectInfo.setMapId(mapId);
                        collectInfo.setCollectPoint(collectPoint);
                        collectInfo.setCollectId(collectIds.get(collectIndex));
                        collectInfo.setStartTime(-1);
                        collectInfo.setDisTime(-1);
                        SecretRealmCollectionTemplate secretRealmCollectionTemplate = collectionTemplates.get(collectInfo.getCollectId());
                        collectInfo.setSurplusRewardTime(secretRealmCollectionTemplate.collectTime);
                        mapInfo.getMapCollectInfos().put(collectPoint, collectInfo);
                        collectIndex++;
                    }
                }
            }
        }else{
            System.out.println("collectRefresh map is null , mapId ： " + mapId);
        }
    }

    /**
     * 创建据点需势力值
     */
    public static long getStrongholdVal(){
        return Long.parseLong(constants.get("strongholdVal"));
    }

    /**
     * 采集物消失时间（分）
     */
    public static int getCollectDisTime(){
        return Integer.parseInt(constants.get("collectDisTime"));
    }

    /**
     * 无活跃势力值减少时间（天）
     */
    public static int getInfluenceWeakDay(){
        return Integer.parseInt(constants.getOrDefault("influenceWeakDay", "10"));
    }
    /**
     * 无活跃势力值减少数值
     */
    public static int getWeakVal(){
        return Integer.parseInt(constants.get("weakVal"));
    }
    public static int initialArea(){
        return Integer.parseInt(constants.get("initialArea"));
    }
    /**
     * 大阵被击破扣除势力值
     */
    public static int getCampFailReduceFaction(){
        return Integer.parseInt(constants.get("campFailReduceFaction"));
    }
    public static int getFactionAttackDown(){
        return Integer.parseInt(constants.get("factionAttackDown"));
    }
    /**
     * 驻守者为大阵提供承伤值（百分比）
     */
    public static int getBearInjury(){
        return Integer.parseInt(constants.get("bearInjury"));
    }
    /**
     * 像素和米转化比例（1像素=N公里）
     */
    public static String getScaleConversion(){
        return constants.get("scaleConversion");
    }
    /**
     * 势力值增加拓展值（每100点增加N）
     */
    public static float getInfluenceGrowup(){
        return Float.parseFloat(constants.get("influenceGrowup"));
    }
    /**
     * 势力范围每次拓展时间（分钟）
     */
    public static int getInfluenceTime(){
        return Integer.parseInt(constants.get("influenceTime"));
    }
    /**
     * 大阵迁移/拆除重新创建CD时间（小时）
     */
    public static int getCreateCD(){
        return Integer.parseInt(constants.get("createCD"));
    }
    /**
     * 大阵自行缩圈时间（天）
     */
    public static int getReduceDay(){
        return Integer.parseInt(constants.getOrDefault("reduceDay", "10"));
    }
    /**
     * 每日限制采集时长（分钟）
     */
    public static long getDayLimitTime(){
        return Integer.parseInt(constants.get("dayLimitTime")) * DateTimeUtil.MinutesOfHour;
    }
    /**
     * 每周限制采集时长（分钟）
     */
    public static long getWeekLimitTime(League league){
        return Integer.parseInt(constants.get("weekLimitTime")) * DateTimeUtil.MinutesOfHour + (Objects.isNull(league) ? 0 : league.getTGAddedNumInt(LeagueTGTypeEnums.提高每周秘境挖矿采集时间上限, 0));
    }
    /**
     * 夜间挑战大阵攻击力降低M%
     */
    public static int getNightReduceAtt(){
        return Integer.parseInt(constants.get("nightReduceAtt"));
    }
    /**
     * 动画停顿时长（秒）
     */
    public static float getPauseTime(){
        return Float.parseFloat(constants.get("pauseTime"));
    }

    /**
     * 动画等待时长（秒）
     */
    public static float getWaitTime(){
        return Float.parseFloat(constants.get("waitTime"));
    }
    /**
     * 每日狂暴次数
     */
    public static int getViolentNum(){
        return Integer.parseInt(constants.getOrDefault("violentNum", "3"));
    }
    /**
     * 狂暴属性加成（属性类型|百分比）
     */
    public static String getViolentAtt(){
        return constants.get("violentAtt");
    }
    /**
     * 远征挂机进攻大阵时长（小时）
     */
    public static int getAttackTime(){
        return Integer.parseInt(constants.get("attackTime"));
    }
    /**
     * 远征驻守大阵时长（小时）
     */
    public static int getDefenseTime(){
        return Integer.parseInt(constants.get("defenseTime"));
    }
    /**
     * 大阵进入复原状态时间（分钟）
     */
    public static int getRestoreState(){
        return Integer.parseInt(constants.get("restoreState"));
    }
    /**
     * 大阵生命恢复百分比（每分钟恢复N%）
     */
    public static int getCampRecovery(League league){
        return Integer.parseInt(constants.get("campRecovery")) + (Objects.nonNull(league) ? league.getTGAddedNumInt(LeagueTGTypeEnums.提高帮派秘境大阵的护盾恢复速度, 0) : 0);
    }
    /**
     * 进攻阶段时长
     */
    public static String getAttackState(){
        return constants.get("attackState");
    }
    /**
     * 超过进攻时长恢复大阵血量百分比
     */
    public static String getStateOverRecovery(){
        return constants.get("StateOverRecovery");
    }
    /**
     * 天地保护2级持续时间
     */
    public static int getProtectState2(){
        return Integer.parseInt(constants.get("protectState2"));
    }
    /**
     * 天地保护3级持续时间
     */
    public static int getProtectState3(){
        return Integer.parseInt(constants.get("protectState3"));
    }
    /**
     * 行动力上限
     */
    public static int getActionLimit(){
        return Integer.parseInt(constants.getOrDefault("actionLimit", "0"));
    }

    /**
     * 行动力消耗：进攻驻守军|成功返还
     */
    public static String getCaAD(){
        return constants.get("caAD");
    }
    /**
     * 主动发起战斗
     */
    public static RewardTemplate getCaAC(){
        return RewardTemplate.readListFromText(constants.get("caAC")).get(0);
    }

    /**
     * 主动驻守大阵
     */
    public static RewardTemplate getCaDC(){
        return RewardTemplate.readListFromText(constants.get("caDC")).get(0);
    }

    public static int geteatNarrowPixel(){
        return Integer.parseInt(constants.getOrDefault("beatNarrowPixel", "0"));

    }

    public static float getFactionRange(){
        return Float.parseFloat(constants.getOrDefault("factionRange", "0"));
    }

    /**
     * 根据势力值获取最大半径
     */
    public static float getMaxRange(long forceNum){
        return (float)(getFactionRange() * forceNum);
    }

    /**
     * 进攻状态持续时间
     */
    public static String getAttackStateTime(){
        return constants.get("attackState");
    }

    public static String getQuickSettlement(){
        return constants.get("quickSettlement");
    }

    public static int getVictoryFatigue(){
        return Integer.parseInt(constants.get("victoryFatigue"));
    }
    public static int getDefensiveTimes(){
        return Integer.parseInt(constants.get("defensiveTimes"));
    }
    public static String getLeagueProtect(){
        return constants.get("leagueProtect");
    }

    public static int getForceUpperLimit(){
        return Integer.parseInt(constants.get("forceUpperLimit"));
    }

    public static String getMapOpen(){
        return constants.get("mapOpen");
    }

    public static int getMapRankCount(){
        return Integer.parseInt(constants.get("mapRank"));
    }

    public static List<Integer> getRankClose(){
        int[] array = StringUtil.splitToIntArray(constants.get("rankClose"), ",");
        return Arrays.stream(constants.get("rankClose").split(",")).mapToInt(Integer::parseInt).boxed().collect(Collectors.toList());
    }

    /**
     * 联盟采集货币
     */
    public static int getOre(){
        return Integer.parseInt(constants.get("ore"));
    }


    public static int getAttackScene(){
        return Integer.parseInt(constants.get("attackScene"));
    }

    public static int getDefendScene(){
        return Integer.parseInt(constants.get("attackScene"));
    }

    public static int getCollectionScene(){
        return Integer.parseInt(constants.get("attackScene"));
    }

    public static int getTop4Count(){
        return Integer.parseInt(constants.get("attackScene"));
    }

    @Override
    public void clearConfigData() {
        constants.clear();
        campAttTemplates.clear();
        collectionTemplates.clear();
        mapTemplates.clear();
        mapChapterToIds.clear();
        factionTemplates.clear();
        hurtTransformations.clear();
        maxHurtTransformation = null;
    }

}

package com.gy.server.game.secretRealm.async;

import java.util.HashMap;
import java.util.Map;

import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.secretRealm.SecretRealmManager;
import com.gy.server.game.secretRealm.bean.SecretRealmDefInfo;
import com.gy.server.game.secretRealm.bean.SecretRealmMapLeagueInfo;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;

/**
 * 异步获取防守方信息
 * <AUTHOR> - [Created on 2024-03-19 16:27]
 */
public class SecretRealmGetDefenceAsync extends AsyncCall {

    SecretRealmMapLeagueInfo mapLeagueInfo;
    Map<Long, Integer> surplusDefTimeInfos = new HashMap<>();
    Player player;
    long time;

    public SecretRealmGetDefenceAsync(Player player, SecretRealmMapLeagueInfo mapLeagueInfo, long time){
        this.player = player;
        this.mapLeagueInfo = mapLeagueInfo;
        this.time = time;
    }

    @Override
    public void asyncExecute() {
        for (Long playerId : mapLeagueInfo.getDefPlayerIds().keySet()) {
            Player player = PlayerManager.getPlayer(playerId);
            surplusDefTimeInfos.put(playerId, player.getSecretRealmModel().getSurplusDefTimes());
        }
    }

    @Override
    public void execute() {
        PbProtocol.SecretRealmGetLeagueDefenceListRst.Builder rst = PbProtocol.SecretRealmGetLeagueDefenceListRst.newBuilder().setResult(Text.genOkServerRstInfo());

        Map<Long, Map<Integer, SecretRealmDefInfo>> defPlayerIds = mapLeagueInfo.getDefPlayerIds();
        for (long defPlayerId : defPlayerIds.keySet()) {
            Map<Integer, SecretRealmDefInfo> defInfoMap = defPlayerIds.get(defPlayerId);
            for (SecretRealmDefInfo secretRealmDefInfo : defInfoMap.values()) {
                rst.addPlayerListInfos(SecretRealmManager.buildPlayerListInfo(defPlayerId, secretRealmDefInfo.getDefStartTime(), 0, surplusDefTimeInfos.get(defPlayerId), secretRealmDefInfo.getHeroTempId(), secretRealmDefInfo.getLineupType()));
            }
        }
        player.send(PtCode.SECRET_REALM_GET_LEAGUE_DEFENCE_LIST_RST, rst.build(), time);
    }
}

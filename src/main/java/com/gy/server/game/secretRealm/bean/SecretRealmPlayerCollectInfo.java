package com.gy.server.game.secretRealm.bean;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

import com.google.protobuf.InvalidProtocolBufferException;
import com.gy.server.packet.PbCommons;
import com.ttlike.server.tl.baselib.serialize.world.SecretRealmGlobalDb;

import org.apache.commons.lang3.tuple.Pair;

/**
 * 玩家收集信息存储
 * <AUTHOR> - [Created on 2023-05-30 17:02]
 */
public class SecretRealmPlayerCollectInfo {

    public SecretRealmPlayerCollectInfo(){}

    long playerId;

    List<Pair<Integer, String>> collects = new CopyOnWriteArrayList<>();

    /**
     * 日收集时间(分)
     */
    private long dayCollectTime;
    /**
     * 周收集时间（分）
     */
    private long weekCollectTime;

    /**
     * 记录收集时间
     */
    public void addCollectTime(long collectTime){
        dayCollectTime += collectTime;
        weekCollectTime += collectTime;
    }

    public void clearPlayerCollect(int mapId, String collectPoint){
        collects.remove(Pair.of(mapId, collectPoint));
    }

    public List<Pair<Integer, String>> getCollects() {
        return collects;
    }

    public void setCollects(List<Pair<Integer, String>> collects) {
        this.collects = collects;
    }

    public static SecretRealmPlayerCollectInfo build(long playerId){
        SecretRealmPlayerCollectInfo collectInfo = new SecretRealmPlayerCollectInfo();
        collectInfo.setPlayerId(playerId);
        return collectInfo;
    }

    public long getDayCollectTime() {
        return dayCollectTime;
    }

    public void setDayCollectTime(long dayCollectTime) {
        this.dayCollectTime = dayCollectTime;
    }

    public long getWeekCollectTime() {
        return weekCollectTime;
    }

    public void setWeekCollectTime(long weekCollectTime) {
        this.weekCollectTime = weekCollectTime;
    }

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }



    public void readFromPb(SecretRealmGlobalDb.SecretRealmPlayerCollectInfoDb db) {
        this.playerId = db.getPlayerId();
        for (byte[] collectPoint : db.getCollectPoints()) {
            try {
                PbCommons.KeyValueDb keyValueDb = PbCommons.KeyValueDb.parseFrom(collectPoint);
                collects.add(Pair.of(keyValueDb.getIntKey(), keyValueDb.getStringValue()));
            } catch (InvalidProtocolBufferException e) {
                e.printStackTrace();
            }
        }

        this.dayCollectTime = db.getDayCollectTime();
        this.weekCollectTime = db.getWeekCollectTime();
    }

    public SecretRealmGlobalDb.SecretRealmPlayerCollectInfoDb writeToPb() {
        SecretRealmGlobalDb.SecretRealmPlayerCollectInfoDb db = new SecretRealmGlobalDb.SecretRealmPlayerCollectInfoDb();
        db.setPlayerId(playerId);
        for (Pair<Integer, String> collect : collects) {
            db.getCollectPoints().add(PbCommons.KeyValueDb.newBuilder().setIntKey(collect.getKey()).setStringValue(collect.getValue()).build().toByteArray());
        }
        db.setDayCollectTime(dayCollectTime);
        db.setWeekCollectTime(weekCollectTime);
        return db;
    }
}

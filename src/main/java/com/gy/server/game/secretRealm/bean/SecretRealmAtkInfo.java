package com.gy.server.game.secretRealm.bean;

import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.secretRealm.SecretRealmService;

/**
 * 进攻信息
 * <AUTHOR> - [Created on 2024-03-21 10:40]
 */
public class SecretRealmAtkInfo {

    //玩家id
    private long playerId;
    //进攻开始时间
    private volatile long atkStartTime;
    //伤害
    private long hurt;
    //展示头像
    private volatile int heroTempId;
    //布阵
    private LineupType lineupType;

    public void modifyTime(long atkStartTime){
        this.atkStartTime = atkStartTime;
    }

    public LineupType getLineupType() {
        return lineupType;
    }

    public void setLineupType(LineupType lineupType) {
        this.lineupType = lineupType;
    }

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public long getAtkStartTime() {
        return atkStartTime;
    }

    public void setAtkStartTime(long atkStartTime) {
        this.atkStartTime = atkStartTime;
    }

    public long getHurt() {
        return hurt;
    }

    public void setHurt(long hurt) {
        this.hurt = hurt;
    }

    public int getHeroTempId() {
        return heroTempId;
    }

    public void setHeroTempId(int heroTempId) {
        this.heroTempId = heroTempId;
    }

    public long realHurt(SecretRealmMapLeagueInfo leagueInfo){
        return hurt * (100 - SecretRealmService.getBearInjury() * leagueInfo.getDefPlayerIds().size()) / 100;
//        return 0L;
    }

}

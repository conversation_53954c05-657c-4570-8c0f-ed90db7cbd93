package com.gy.server.game.secretRealm.template;

import java.util.ArrayList;
import java.util.List;

import com.gy.server.game.drop.RewardTemplate;

/**
 * 秘境采集配置
 * <AUTHOR> - [Created on 2023-05-11 16:17]
 */
public class SecretRealmCollectionTemplate {

    //采集id
    public int id;
    //最大采集数量
    public int collectNum;
    //单次采集时长（分）
    public int collectTime;
    //采集效率（分）
    public int collectEfficiency;
    //采集消耗
    public List<RewardTemplate> collectCost = new ArrayList<>();


}

package com.gy.server.game.secretRealm.async;

import java.util.List;
import java.util.Objects;

import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.attribute.Attribute;
import com.gy.server.game.combat.CombatManager;
import com.gy.server.game.combat.StageType;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.secretRealm.SecretRealmService;
import com.gy.server.game.secretRealm.stage.SecretRealmStage;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.time.DateTimeUtil;

/**
 * 秘境清理进攻
 * <AUTHOR> - [Created on 2023-05-24 10:23]
 */
public class SecretRealmAttackAtkBattleAsync extends AsyncCall {

    Player player;
    int mapId;
    String point;
    long defPlayerId;
    List<HeroUnit> defs;
    StageType stageType = StageType.secretRealmGang;
    boolean isRage;
    long time;
    int protectBuffId;
    LineupType atkLineupType;
    LineupType defLineupType;
    long pbTime;

    public SecretRealmAttackAtkBattleAsync(Player player, int mapId, String point, long defPlayerId
            , boolean isRage, long time, int protectBuffId
            , LineupType atkLineupType, LineupType defLineupType, long pbTime){
        this.player = player;
        this.mapId = mapId;
        this.point = point;
        this.defPlayerId = defPlayerId;
        this.isRage = isRage;
        this.time = time;
        this.protectBuffId = protectBuffId;
        this.atkLineupType = atkLineupType;
        this.defLineupType = defLineupType;
        this.pbTime = pbTime;
    }

    @Override
    public void asyncExecute() {
        Player defPlayer = PlayerManager.getPlayer(defPlayerId);
        if(Objects.nonNull(defPlayer)){
            defs = defPlayer.getLineupModel().createHeroUnits(stageType, defLineupType);
        }
    }

    @Override
    public void execute() {
        PbProtocol.SecretRealmAttackGangDefRst.Builder rst = PbProtocol.SecretRealmAttackGangDefRst.newBuilder().setResult(Text.genOkServerRstInfo());
        List<HeroUnit> atks = player.getLineupModel().createHeroUnits(stageType, atkLineupType);

        if(protectBuffId > 0){
            for (HeroUnit atk : atks) {
                atk.addBuff(protectBuffId, 1);
            }
        }

        //狂暴属性加成
        if(isRage){
            String violentAtt = SecretRealmService.getViolentAtt();
            for (HeroUnit atk : atks) {
                atk.getAttributes().addAttributes(new Attribute(violentAtt));
            }
        }
        //检查防守者时间是不是0-6点（夜间挑战大阵攻击力降低）
        int hour = DateTimeUtil.toLocalDateTime(time).getHour();
        if(hour < 6){
            int buffId = SecretRealmService.getNightReduceAtt();
            for (HeroUnit def : defs) {
                def.addBuff(buffId, 1);
            }
        }

        //初始化战斗
        SecretRealmStage stage = new SecretRealmStage(player, atks, defPlayerId, defs
                , mapId, point, stageType, isRage, SecretRealmService.getAttackScene(), atkLineupType, defLineupType);
        stage.init();
        CombatManager.combatPrepare(stage);
        rst.setStage(stage.genAbstractPb());
        rst.setRageTime(player.getSecretRealmModel().getRageTime());

        player.send(PtCode.SECRET_REALM_DEF_GANG_ATTACKER_RST, rst.build(), pbTime);
    }

}

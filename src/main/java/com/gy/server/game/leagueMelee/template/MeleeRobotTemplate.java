package com.gy.server.game.leagueMelee.template;

import com.gy.server.game.util.StringExtUtil;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Map;

/**
 * 机器人
 *
 * <AUTHOR> 2024/8/27 11:19
 **/
public class MeleeRobotTemplate {

    public int id;

    /**
     * 玩家等级
     */
    public Pair<Integer, Integer> level;

    /**
     * 普通阵容
     */
    public int battleCollectId;

    /**
     * 机器人阵容内
     */
    public int robotLevel;

    /**
     * 机器人id
     */
    public int robotId;

    public MeleeRobotTemplate(Map<String, String> map){
        this.id = Integer.parseInt(map.get("id"));
        this.level = StringExtUtil.string2Pair(map.get("level"), ",", Integer.class, Integer.class);
        this.battleCollectId = Integer.parseInt(map.get("battleCollectId"));
        this.robotLevel = Integer.parseInt(map.get("robotLevel"));
        this.robotId = Integer.parseInt(map.get("robotID"));
    }

}

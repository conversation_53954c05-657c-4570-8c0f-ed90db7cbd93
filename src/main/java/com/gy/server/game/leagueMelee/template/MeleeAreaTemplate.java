package com.gy.server.game.leagueMelee.template;

import com.gy.server.game.util.StringExtUtil;
import org.apache.commons.lang3.tuple.Pair;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 区域
 *
 * <AUTHOR> 2024/8/27 11:53
 **/
public class MeleeAreaTemplate {

    public int id;

    /**
     * 地块开启的时间id
     */
    public List<Integer> areaTime = new ArrayList<>();

    /**
     * 地块存在时间
     */
    public List<Integer> areaSurvivalTime = new ArrayList<>();

    /**
     * left:地块开启的时间id  right:地块存在时间
     */
    public List<Pair<Integer, Integer>> pairList = new ArrayList<>();

    /**
     * 归属类型
     * 1.打架积分最高
     * 2.打怪物（伤害最高）
     * 3.搬砖（积分最高）
     */
    public int areaType;

    /**
     * 归属收益类型
     * 1.获得归属权/一次性获得的荣誉值
     * 2.获得增益buff/buffId
     * 3.获得本据点荣誉值加成
     */
    public int areaIncome;

    /**
     * 怪物组
     */
    public int battleCollect;

    /**
     * 归属收益参数
     * 参数对应areaIncome类型
     *
     * 当areaIncome=1时，参数为对应百分比
     * 当areaIncome=2时，参数为对应buffID
     */
    public int areaParameter;

    public MeleeAreaTemplate(Map<String, String> map){
        this.id = Integer.parseInt(map.get("id"));
        this.areaTime = StringExtUtil.string2List(map.get("areaTime"), ",", Integer.class);
        this.areaSurvivalTime = StringExtUtil.string2List(map.get("areaSurvivalTime"), ",", Integer.class);
        if(areaTime.size() != areaSurvivalTime.size()){
            throw new IllegalArgumentException("MeleeAreaTemplate areaTime and areaSurvivalTime size is not same, id is " + id);
        }
        for (int i = 0; i < areaTime.size(); i++){
            pairList.add(Pair.of(areaTime.get(i), areaSurvivalTime.get(i)));
        }

        this.areaType = Integer.parseInt(map.get("area"));
        this.areaIncome = Integer.parseInt(map.get("areaIncome"));
        this.battleCollect = Integer.parseInt(map.get("BattleCollect"));
        this.areaParameter = Integer.parseInt(map.get("areaParameter"));
    }
}

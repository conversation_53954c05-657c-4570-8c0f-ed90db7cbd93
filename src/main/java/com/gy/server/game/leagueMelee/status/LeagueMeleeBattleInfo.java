package com.gy.server.game.leagueMelee.status;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.battleRule.base.BaseBattleInfo;
import com.gy.server.core.battleRule.enums.BattleEnums;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.league.LeagueModelEnums;
import com.gy.server.game.league.bean.LeagueMember;
import com.gy.server.game.leagueMelee.LeagueMeleeHelper;
import com.gy.server.game.leagueMelee.LeagueMeleeModel;
import com.gy.server.game.leagueMelee.LeagueMeleeService;
import com.gy.server.game.leagueMelee.PlayerLeagueMeleeModel;
import com.gy.server.game.leagueMelee.template.MeleeConstant;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.game.role.PlayerRoleService;
import com.gy.server.packet.PbLeagueMelee;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.serialize.leagueMelee.*;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 帮派乱斗战斗逻辑数据
 *
 * <AUTHOR> - [Created on 2023/7/18 14:13]
 */
public class LeagueMeleeBattleInfo extends BaseBattleInfo {

    /**
     * 玩法开启
     */
    private boolean isOpen;

    /**
     * 标记时间
     * 上次开启标准结束时间
     */
    private long signTime;

    /**
     * 本服务器开启次数
     */
    private int openNum;

    /**
     * 分组索引信息
     * Key:帮派ID  Value:天地ID
     */
    private Map<Long, Integer> groupIndexMap = new ConcurrentHashMap<>();

    /**
     * 野怪唯一id
     */
    private AtomicLong monsterId = new AtomicLong(1);

    /**
     * 分组数据信息
     * Key:分组ID  Value:分组数据
     */
    private Map<Integer, MeleeGroupInfo> groupInfoMap = new ConcurrentHashMap<>();
    /**
     * 玩家数据
     */
    private Map<Long, MeleePlayerInfo> playerInfoMap = new ConcurrentHashMap<>();

    /**
     * 帮派数据
     */
    private Map<Long, MeleeLeagueInfo> leagueInfoMap = new ConcurrentHashMap<>();

    /**
     * 荣誉值产出倍率
     */
    private int honorPer;
    /**
     * 区域积分产出倍率
     */
    private int countPer;

    /**
     * 上周天组帮派荣誉值排行信息
     */
    private List<MeleeLeagueHonorRankNode> lastTHonorRankList = new CopyOnWriteArrayList<>();
    /**
     * 上周地组帮派荣誉值排行信息
     */
    private List<MeleeLeagueHonorRankNode> lastDHonorRankList = new CopyOnWriteArrayList<>();

    /**
     * 荣誉面板信息
     */
    private HonorPanel honorPanel;
    /**
     * 战斗阶段已经开启的阶段
     */
    private Set<Integer> openTimeIdSet = new HashSet<>();

    public void clear(){
        //帮派内部数据清空
        for (long leagueId : groupIndexMap.keySet()) {
            League league = LeagueManager.getLeagueById(leagueId);
            if(league != null){
                LeagueMeleeModel meleeModel = league.getModel(LeagueModelEnums.melee);
                meleeModel.endAndClear();
            }
        }

        //玩家数据清空
        ThreadPool.execute(() -> {
            for (Long playerId : playerInfoMap.keySet()) {
                Player player = PlayerManager.getPlayer(playerId);
                PlayerLeagueMeleeModel playerLeagueMeleeModel = player.getModel(PlayerModelEnums.leagueMelee);
                playerLeagueMeleeModel.endAndClear();
            }
        });

        lastTHonorRankList.clear();
        lastDHonorRankList.clear();
        for (Map.Entry<Integer, MeleeGroupInfo> entry : groupInfoMap.entrySet()) {
            int groupId = entry.getKey();
            MeleeGroupInfo meleeGroupInfo = entry.getValue();
            List<MeleeLeagueHonorRankNode> rankNodeList = meleeGroupInfo.getHonorRankList();

            if (groupId == LeagueMeleeService.GROUP_T) {
                lastTHonorRankList.addAll(rankNodeList);
            } else {
                lastDHonorRankList.addAll(rankNodeList);
            }
        }

        //荣誉面板信息
        MeleeGroupInfo groupTInfo = groupInfoMap.get(LeagueMeleeService.GROUP_T);
        if (Objects.nonNull(groupTInfo)) {
            HonorPanel honorPanelTemp = new HonorPanel();
            List<MeleeLeagueHonorRankNode> honorRankList = groupTInfo.getHonorRankList();
            if (!honorRankList.isEmpty()) {
                MeleeConstant constant = LeagueMeleeService.getConstant();
                int end = Math.min(constant.participateNum, honorRankList.size());
                for (int i = 0; i < end; i++) {
                    MeleeLeagueHonorRankNode leagueHonorRankNode = honorRankList.get(i);
                    long leagueId = leagueHonorRankNode.getLeagueId();
                    League league = LeagueManager.getLeagueById(leagueId);
                    HonorPanelLeague honorPanelLeague = new HonorPanelLeague();
                    honorPanelLeague.setLeagueId(leagueId);
                    honorPanelLeague.setName(league.getName());
                    honorPanelLeague.setIconName(league.getIconName());
                    honorPanelLeague.setIconFrameId(league.getIconFrameId());
                    honorPanelTemp.getLeagueList().add(honorPanelLeague);
                }
                HonorPanelLeague honorPanelLeague = honorPanelTemp.getLeagueList().get(0);
                League league = LeagueManager.getLeagueById(honorPanelLeague.getLeagueId());
                Map<Long, LeagueMember> memberMap = league.getMemberMap();
                List<MeleePlayerHonorRankNode> playerHonorRankList = groupTInfo.getPlayerHonorRankList();
                int showNum = 0;
                for (MeleePlayerHonorRankNode meleePlayerHonorRankNode : playerHonorRankList) {
                    if (showNum >= constant.showNum) {
                        break;
                    }
                    long playerId = meleePlayerHonorRankNode.getPlayerId();
                    if (memberMap.containsKey(playerId)) {
                        MiniGamePlayer miniPlayer = PlayerManager.getMiniPlayer(playerId);
                        LeagueMember leagueMember = memberMap.get(playerId);
                        HonorPanelPlayer honorPanelPlayer = new HonorPanelPlayer();
                        honorPanelPlayer.setPlayerId(playerId);
                        honorPanelPlayer.setName(miniPlayer.getName());
                        honorPanelPlayer.setJob(leagueMember.getJobId());
                        honorPanelPlayer.setFightPower(miniPlayer.getFightingPower());
                        honorPanelPlayer.setProtagonistId(PlayerRoleService
                                .getProtagonistTemplate(miniPlayer.getProfession(), miniPlayer.getGender()).id);
                        honorPanelTemp.getPlayerList().add(honorPanelPlayer);
                        showNum++;
                    }
                }
            }
            this.honorPanel = honorPanelTemp;
        }

        isOpen = false;
        groupIndexMap.clear();
        monsterId = new AtomicLong(1);
        groupInfoMap.clear();
        playerInfoMap.clear();
        leagueInfoMap.clear();
        honorPer = 0;
        countPer = 0;
        openTimeIdSet.clear();
    }

    /**
     *
     * @param group 组
     * @return
     */
    public PbLeagueMelee.MeleeMapInfo genMapInfo(int group, long leagueId, long playerId){
        PbLeagueMelee.MeleeMapInfo.Builder builder = PbLeagueMelee.MeleeMapInfo.newBuilder();
        MeleeGroupInfo groupInfo = groupInfoMap.get(group);

        builder.setRankInfo(LeagueMeleeHelper.genTotalRankInfo(groupInfo, leagueId, playerId));

        for (MeleePiratesInfo piratesInfo : groupInfo.getMeleePiratesInfoMap().values()) {
            builder.addMonster(piratesInfo.genPb());
        }

        return builder.build();
    }

    /**
     * 帮派是否参加本次活动
     *
     * @param leagueId 帮派ID
     * @return true 未报名
     */
    public boolean isNotJoinLeague(long leagueId) {
        return !isJoinLeague(leagueId);
    }

    /**
     * 帮派是否参加本次活动
     *
     * @param leagueId 帮派ID
     * @return true 已报名
     */
    public boolean isJoinLeague(long leagueId) {
        return groupIndexMap.containsKey(leagueId);
    }

    public MeleeGroupInfo getGroupInfoByPlayerId(long playerId){
        long leagueId = LeagueManager.getLeagueId(playerId);
        return getGroupInfoByLeagueId(leagueId);
    }

    /**
     * 通过帮派ID 获取分组信息
     * 如果数据不存在 返回Null
     *
     * @param leagueId 帮派ID
     * @return 分组信息
     */
    public MeleeGroupInfo getGroupInfoByLeagueId(long leagueId) {
        Integer groupId = groupIndexMap.get(leagueId);
        if (groupId == null) {
            return null;
        }
        return groupInfoMap.get(groupId);
    }

    /**
     * 通过玩家 获取分组信息
     *
     * @return 分组信息
     */
    public MeleeGroupInfo getGroupInfoByPlayer(Player player) {
        long leagueId = LeagueManager.getLeagueId(player);
        return getGroupInfoByLeagueId(leagueId);
    }

    public LeagueMeleeBattleInfo() {
        super(BattleEnums.league_melee);
    }

    public int getOpenNum() {
        return openNum;
    }

    public void setOpenNum(int openNum) {
        this.openNum = openNum;
    }

    public boolean isOpen() {
        return isOpen;
    }

    public void setOpen(boolean open) {
        isOpen = open;
    }

    public Map<Long, Integer> getGroupIndexMap() {
        return groupIndexMap;
    }

    public void setGroupIndexMap(Map<Long, Integer> groupIndexMap) {
        this.groupIndexMap = groupIndexMap;
    }

    public Map<Integer, MeleeGroupInfo> getGroupInfoMap() {
        return groupInfoMap;
    }

    public void setGroupInfoMap(Map<Integer, MeleeGroupInfo> groupInfoMap) {
        this.groupInfoMap = groupInfoMap;
    }

    public Map<Long, MeleePlayerInfo> getPlayerInfoMap() {
        return playerInfoMap;
    }

    public void setPlayerInfoMap(Map<Long, MeleePlayerInfo> playerInfoMap) {
        this.playerInfoMap = playerInfoMap;
    }

    public Map<Long, MeleeLeagueInfo> getLeagueInfoMap() {
        return leagueInfoMap;
    }

    public void setLeagueInfoMap(Map<Long, MeleeLeagueInfo> leagueInfoMap) {
        this.leagueInfoMap = leagueInfoMap;
    }

    public int getHonorPer() {
        return honorPer;
    }

    public void setHonorPer(int honorPer) {
        this.honorPer = honorPer;
    }

    public int getCountPer() {
        return countPer;
    }

    public void setCountPer(int countPer) {
        this.countPer = countPer;
    }

    public AtomicLong getMonsterId() {
        return monsterId;
    }

    public void setMonsterId(AtomicLong monsterId) {
        this.monsterId = monsterId;
    }

    public List<MeleeLeagueHonorRankNode> getLastTHonorRankList() {
        return lastTHonorRankList;
    }

    public void setLastTHonorRankList(List<MeleeLeagueHonorRankNode> lastTHonorRankList) {
        this.lastTHonorRankList = lastTHonorRankList;
    }

    public List<MeleeLeagueHonorRankNode> getLastDHonorRankList() {
        return lastDHonorRankList;
    }

    public void setLastDHonorRankList(List<MeleeLeagueHonorRankNode> lastDHonorRankList) {
        this.lastDHonorRankList = lastDHonorRankList;
    }

    public HonorPanel getHonorPanel() {
        return honorPanel;
    }

    public void setHonorPanel(HonorPanel honorPanel) {
        this.honorPanel = honorPanel;
    }

    public Set<Integer> getOpenTimeIdSet() {
        return openTimeIdSet;
    }

    public void setOpenTimeIdSet(Set<Integer> openTimeIdSet) {
        this.openTimeIdSet = openTimeIdSet;
    }

    public long getSignTime() {
        return signTime;
    }

    public void setSignTime(long signTime) {
        this.signTime = signTime;
    }

    public long getStatusStartTime(MeleeNodeType nodeType){
        LocalTime openTime = LeagueMeleeService.getConstant().onTimeMap.get(nodeType).getLeft();
        LocalDateTime localDateTime = DateTimeUtil.toLocalDateTime(signTime);
        localDateTime = ServerConstants.getOneDayHourMinuteSecond(localDateTime, openTime.getHour(), openTime.getMinute(), 0);
        return DateTimeUtil.toMillis(localDateTime);
    }
}

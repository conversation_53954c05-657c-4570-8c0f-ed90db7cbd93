package com.gy.server.game.leagueMelee;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueDataInterface;
import com.gy.server.game.league.LeagueModel;
import com.gy.server.game.leagueMelee.template.MeleeConstant;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.packet.PbLeagueMelee;
import com.gy.server.packet.PbProtocol;
import com.ttlike.server.tl.baselib.serialize.league.LeagueBlobDb;
import com.ttlike.server.tl.baselib.serialize.leagueMelee.*;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.*;

/**
 * 帮派乱斗 （凤凰古城）
 *
 * <AUTHOR> - [Created on 2023/7/18 10:09]
 */
public class LeagueMeleeModel extends LeagueModel implements LeagueDataInterface {

    /**
     * 指挥标记
     * key:据点id  value:标记信息
     */
    private Map<Integer, MeleeCmdInfoDb> cmdMap = new HashMap<>();

    /**
     * 战术板记录
     */
    private List<MeleeCmdRecordInfo> cmdRecordList = new ArrayList<>();
    /**
     * 帮派战场记录
     */
    private List<MeleeRecord> recordList = new ArrayList<>();

    /**
     * 添加帮派战报
     *
     * @param record 战报记录
     */
    public void addRecord(MeleeRecord record) {
        MeleeConstant constant = LeagueMeleeService.getConstant();
        recordList.add(record);
        int exceedCount = recordList.size() - constant.gangReportNum;
        if (exceedCount > 0) {
            List<MeleeRecord> removeRecordList = recordList.subList(constant.gangReportNum, recordList.size());
            removeRecordList.clear();
        }
    }

    public LeagueMeleeModel(League league) {
        super(league);
    }

    public List<PbLeagueMelee.CmdInfo> genCmdInfos() {
        List<PbLeagueMelee.CmdInfo> list = new ArrayList<>();
        cmdMap.values().forEach(bean -> {
            PbLeagueMelee.CmdInfo.Builder cmdInfo = PbLeagueMelee.CmdInfo.newBuilder();
            cmdInfo.setIslandId(bean.getIslandId());
            cmdInfo.putAllAreaCmdMap(bean.getAreaCmdMap());
            list.add(cmdInfo.build());
        });
        return list;
    }

    public List<PbLeagueMelee.MeleeCmdRecord> genCmdRecords() {
        List<PbLeagueMelee.MeleeCmdRecord> list = new ArrayList<>();
        cmdRecordList.forEach(bean -> {
            PbLeagueMelee.MeleeCmdRecord.Builder cmdRecord = PbLeagueMelee.MeleeCmdRecord.newBuilder();
            cmdRecord.setName(bean.getName());
            cmdRecord.setJob(bean.getJob());
            cmdRecord.setTitle(bean.getTitle());
            cmdRecord.setIslandId(bean.getIslandId());
            cmdRecord.setAreaId(bean.getAreaId());
            cmdRecord.setIsMark(bean.isMark());
            cmdRecord.setCreate(bean.getCreate());
            list.add(cmdRecord.build());
        });
        return list;
    }

    public void markDeal(Player player, int islandId, int areaId, boolean isMark, MeleeGroupInfo groupInfo) {
        if(isMark){
            cmdMap.computeIfAbsent(islandId, bean -> new MeleeCmdInfoDb(islandId)).getAreaCmdMap().put(areaId, player.getPlayerId());
        }else {
            if(cmdMap.containsKey(islandId)){
                MeleeCmdInfoDb meleeCmdInfoDb = cmdMap.get(islandId);
                meleeCmdInfoDb.getAreaCmdMap().remove(areaId);
                if(meleeCmdInfoDb.getAreaCmdMap().isEmpty()){
                    cmdMap.remove(islandId);
                }
            }
        }

        //战术板记录
        addCmdRecord(player, islandId, areaId, isMark);

        //战术板变动通知
        ThreadPool.execute(() -> {
            long leagueId = getLeague().getLeagueId();
            Map<Long, MeleePlayerInInfo> playerInInfoMap = groupInfo.getPlayerInMap().get(leagueId);
            if(playerInInfoMap != null){
                PbProtocol.LeagueMeleeCmdIslandNotify.Builder notify = PbProtocol.LeagueMeleeCmdIslandNotify.newBuilder();
                notify.addAllCmdInfo(genCmdInfos());

                for (MeleePlayerInInfo inInfo : playerInInfoMap.values()) {
                    Player sendPlayer = PlayerManager.getOnlinePlayer(inInfo.getPlayerId());
                    if(sendPlayer != null){
                        sendPlayer.send(PtCode.LEAGUE_MELEE_CMD_ISLAND_NOTIFY, notify.build());
                    }
                }
            }
        });

    }

    /**
     * 添加战术板记录
     *
     * @param player   玩家
     * @param islandId 据点ID
     */
    private void addCmdRecord(Player player, int islandId, int areaId, boolean mark) {
//        String name, int title, int job, int islandId, int areaId, boolean isMark, long create
        MeleeCmdRecordInfo recordInfo = new MeleeCmdRecordInfo(player.getName(), player.getRoleModel().getHeroTitleId(), getLeague().getJob(player.getPlayerId()),
                islandId, areaId, mark, ServerConstants.getCurrentTimeMillis());
        cmdRecordList.add(recordInfo);
        MeleeConstant constant = LeagueMeleeService.getConstant();
        while (cmdRecordList.size() > constant.tacticNum){
            cmdRecordList.remove(0);
        }
    }

    /**
     * 检查集火次数
     * @return
     */
    public boolean checkHaveCmdNum(long playerId) {
        int num = 0;
        for (MeleeCmdInfoDb bean : cmdMap.values()) {
            for (Map.Entry<Integer, Long> entry : bean.getAreaCmdMap().entrySet()) {
                if(entry.getValue() == playerId){
                    num++;
                }
            }
        }
        return LeagueMeleeService.getConstant().convergeTimes > num;
    }

    @Override
    protected void loadData(LeagueBlobDb leagueBlobDb) {
        LeagueMeleeModelDb meleeModelDb = leagueBlobDb.getMeleeModelDb();
        if (Objects.nonNull(meleeModelDb)) {
            this.cmdRecordList = meleeModelDb.getCmdRecordList();
            this.recordList = meleeModelDb.getRecordList();
            meleeModelDb.getCmdList().forEach(bean -> {
                this.cmdMap.put(bean.getIslandId(), bean);
            });
        }
    }

    @Override
    protected void saveData(LeagueBlobDb leagueBlobDb) {
        LeagueMeleeModelDb db = new LeagueMeleeModelDb();
        db.setCmdRecordList(this.cmdRecordList);
        db.setRecordList(this.recordList);
        db.getCmdList().addAll(this.cmdMap.values());
        leagueBlobDb.setMeleeModelDb(db);
    }

    /**
     * 结束并清空数据
     */
    public void endAndClear() {
        this.cmdMap.clear();
        this.cmdRecordList.clear();
        this.recordList.clear();
    }

    public Map<Integer, MeleeCmdInfoDb> getCmdMap() {
        return cmdMap;
    }

    public void setCmdMap(Map<Integer, MeleeCmdInfoDb> cmdMap) {
        this.cmdMap = cmdMap;
    }

    public List<MeleeCmdRecordInfo> getCmdRecordList() {
        return cmdRecordList;
    }

    public void setCmdRecordList(List<MeleeCmdRecordInfo> cmdRecordList) {
        this.cmdRecordList = cmdRecordList;
    }

    public List<MeleeRecord> getRecordList() {
        return recordList;
    }

    public void setRecordList(List<MeleeRecord> recordList) {
        this.recordList = recordList;
    }

    @Override
    public boolean canMerge(long leagueId) {
        return true;
    }

    @Override
    public void merge(League selfLeague, League targetLeague) {

    }

    @Override
    public void removeLeague(League league) {

    }

    @Override
    public void quitLeague(long pid) {

    }
}

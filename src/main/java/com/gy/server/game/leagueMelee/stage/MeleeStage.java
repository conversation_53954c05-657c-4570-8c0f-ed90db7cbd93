package com.gy.server.game.leagueMelee.stage;

import com.gy.server.game.battleCollect.BattleCollectService;
import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.combat.StageType;
import com.gy.server.game.combat.unit.TeamUnit;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.leagueMelee.LeagueMeleeHelper;
import com.gy.server.game.leagueMelee.LeagueMeleeService;
import com.gy.server.game.leagueMelee.PlayerLeagueMeleeModel;
import com.gy.server.game.leagueMelee.template.*;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.world.World;
import com.gy.server.packet.PbProtocol;
import com.ttlike.server.tl.baselib.serialize.leagueMelee.MeleeGroupInfo;
import com.ttlike.server.tl.baselib.serialize.leagueMelee.MeleeIslandRankInfo;
import com.ttlike.server.tl.baselib.serialize.leagueMelee.MeleePlayerRecord;
import org.apache.commons.lang3.tuple.Pair;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 帮派乱斗战斗
 *
 * <AUTHOR> - [Created on 2023/8/4 15:40]
 */
public class MeleeStage extends AbstractStage {

    private final Player atkPlayer;
    private final Player defPlayer;

    private final boolean isRobot;
    private int id;

    private final int islandId;
    private final MeleeAreaTemplate areaTemplate;

    private Set<Integer> atkBuffIds = new HashSet<>();
    private Set<Integer> defBuffIds = new HashSet<>();

    LineupType lineupType = LineupType.leagueMelee;
    StageType stageType = StageType.leagueMelee;

    public MeleeStage(Player atkPlayer, boolean isRobot, Player defPlayer, int islandId, int areaId, MeleeGroupInfo groupInfo) {
        this.atkPlayer = atkPlayer;
        this.isRobot = isRobot;
        this.defPlayer = defPlayer;
        this.islandId = islandId;
        this.areaTemplate = LeagueMeleeService.getAreaMap().get(areaId);

        MeleePointTemplate pointTemplate = LeagueMeleeService.getPointMap().get(islandId);
        for (int id : pointTemplate.pointAreaIds) {
            MeleeAreaTemplate template = LeagueMeleeService.getAreaMap().get(id);
            if(template != null){
                if(template.areaIncome == MeleeIslandAreaBelongIncomeType.getBuff.getType()){
                    long beLongLeagueId = groupInfo.getIslandRankInfoMap().getOrDefault(islandId, new MeleeIslandRankInfo()).getFirstLeagueIdMap().getOrDefault(template.id, 0l);
                    if(beLongLeagueId == LeagueManager.getLeagueId(atkPlayer)){
                        atkBuffIds.add(template.areaParameter);
                    }
                    if(defPlayer != null && beLongLeagueId == LeagueManager.getLeagueId(defPlayer)){
                        defBuffIds.add(template.areaParameter);
                    }
                }
            }
        }

    }

    @Override
    public void init() {
        List<TeamUnit> atkList = atkPlayer.getLineupModel().createTeamUnits(this, stageType, lineupType);
        if(!atkBuffIds.isEmpty()){
            for (TeamUnit teamUnit : atkList) {
                atkBuffIds.forEach(buffId -> teamUnit.addUnitBuff(buffId, 1));
            }
        }

        List<TeamUnit> defList;
        if (isRobot) {
            //增加机器人对手
            MeleeRobotTemplate robotTemplate = LeagueMeleeService.getMeleeRobotTemplateByLevel(atkPlayer.getLevel());
            id = robotTemplate.id;
            defList = BattleCollectService.createTeamUnits(this, World.getWorldLevel(), robotTemplate.battleCollectId);
//            defList = BattleCollectService.createTeamUnits(this, robotTemplate.robotLevel, robotTemplate.battleCollectId);
        } else {
            defList = defPlayer.getLineupModel().createTeamUnits(this, stageType, lineupType);
            if(!defBuffIds.isEmpty()){
                for (TeamUnit teamUnit : defList) {
                    defBuffIds.forEach(buffId -> teamUnit.addUnitBuff(buffId, 1));
                }
            }
        }

        init(LeagueMeleeService.getConstant().battleCollectId, stageType, atkList, defList, lineupType);
    }

    @Override
    public void afterFinish() {
        finishHandler(atkPlayer, defPlayer, isWin);

        atkPlayer.postEvent(PlayerEventType.leagueMeleeAtk);
    }

    private void finishHandler(Player player, Player opponentPlayer, boolean isWin) {
        MeleeConstant constant = LeagueMeleeService.getConstant();

        //体力处理
        PlayerLeagueMeleeModel playerMeleeModel = player.getModel(PlayerModelEnums.leagueMelee);
        int costPower = isWin ? constant.combatWinEnergyMap.get(areaTemplate.areaType) : constant.combatLoseEnergyMap.get(areaTemplate.areaType);
        playerMeleeModel.setWiningStreak(isWin ? playerMeleeModel.getWiningStreak() + 1 : 0);
        playerMeleeModel.costPower(costPower);

        if (defPlayer != null) {
            PlayerLeagueMeleeModel defMeleeModel = player.getModel(PlayerModelEnums.leagueMelee);
            int defCostPower = isWin ? constant.combatWinEnergyMap.get(areaTemplate.areaType) : constant.combatLoseEnergyMap.get(areaTemplate.areaType);
            defMeleeModel.costPower(defCostPower);
            //战报记录
            MeleePlayerRecord meleePlayerRecord = LeagueMeleeHelper.createMeleePlayerRecord(player, false, id, 0, costPower, Pair.of(0, 0), !isWin, islandId);
            playerMeleeModel.addRecord(meleePlayerRecord);

            LeagueMeleeHelper.beFightMapChange(opponentPlayer.getPlayerId(), player.getPlayerId(), opponentPlayer.getName(), false);

        }


        //积分增加
        Pair<Integer, Integer> scorePair = Pair.of(0, 0);
        if (areaTemplate.areaType != 2) {
            //boss区打人不加积分
            scorePair = LeagueMeleeHelper.addFightScore(islandId, areaTemplate, player, isRobot);
        }

        PbProtocol.CombatSettlementNotify.Builder rst = genCombatSettlement();
        PbProtocol.MeleeSettlement.Builder meleeSettlement = PbProtocol.MeleeSettlement.newBuilder();
        //战报记录
        MeleePlayerRecord meleePlayerRecord = LeagueMeleeHelper.createMeleePlayerRecord(opponentPlayer, isRobot, id, playerMeleeModel.getWiningStreak(), costPower, scorePair, isWin, islandId);
        playerMeleeModel.addRecord(meleePlayerRecord);
        meleeSettlement.setScore(scorePair.getLeft())
                .setHonor(scorePair.getRight())
                .setRemainPower(playerMeleeModel.getRemainPower())
                .setRebirthTime(playerMeleeModel.getRebirthTime());
        rst.setMelee(meleeSettlement.build());

        notifyCombatSettlement(player, rst.build());
    }

    @Override
    public void combatAbort(boolean isStart) {
        Player player = atkPlayer;
        Player opponentPlayer = defPlayer;
        LeagueMeleeHelper.beFightMapChange(opponentPlayer.getPlayerId(), player.getPlayerId(), opponentPlayer.getName(), false);
    }
}

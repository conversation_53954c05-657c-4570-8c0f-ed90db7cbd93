package com.gy.server.game.leagueMelee.status.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.battleRule.base.IBattleStatusDeal;
import com.gy.server.core.delay.DelayTaskManager;
import com.gy.server.core.log.CommonLogger;
import com.gy.server.game.leagueMelee.LeagueMeleeHelper;
import com.gy.server.game.leagueMelee.LeagueMeleeService;
import com.gy.server.game.leagueMelee.PlayerLeagueMeleeModel;
import com.gy.server.game.leagueMelee.status.LeagueMeleeBattleInfo;
import com.gy.server.game.leagueMelee.status.MeleeNodeType;
import com.gy.server.game.leagueMelee.template.*;
import com.gy.server.game.log.GameLogger;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.MathUtil;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.serialize.leagueMelee.*;
import com.ttlike.server.tl.baselib.thread.ThreadPool;
import org.apache.commons.lang3.tuple.Pair;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 战斗状态
 * 随着战斗时间推进对开启区域和刷新海盗的检测
 *
 * <AUTHOR> - [Created on 2023/7/19 9:50]
 */
public class LeagueMelee8BattleHandler implements IBattleStatusDeal<LeagueMeleeBattleInfo> {

    /**
     * 上一次海岛自动产出时间
     */
    private long lastAutoTime = -1;
    /**
     * 产出间隔
     */
    private static final int DURATION_SEC = 5;
    private static final long DURATION_MIL = DURATION_SEC * DateTimeUtil.MillisOfSecond;
    private long startTime;


    @Override
    public void init(LeagueMeleeBattleInfo battleInfo) {
        this.startTime = battleInfo.getStatusStartTime(getMeleeNodeType());

        if (lastAutoTime == -1) {
            lastAutoTime = ServerConstants.getCurrentTimeMillis();

//            //时间点初始化
//            refreshGameTime(battleInfo, new HashMap<>());
//            //据点初始化
//            refreshArea(battleInfo);
        }
    }


    /**
     * 据点刷新
     * @param battleInfo
     */
    private boolean refreshArea(LeagueMeleeBattleInfo battleInfo){
        boolean update = false;

        Map<Integer, MeleePointTemplate> pointMap = LeagueMeleeService.getPointMapBySeasonId(battleInfo.getSeasonId());
        Map<Integer, MeleeAreaTemplate> areaMap = LeagueMeleeService.getAreaMap();
        for (MeleeGroupInfo groupInfo : battleInfo.getGroupInfoMap().values()) {
            for (MeleePointTemplate pointTemplate : pointMap.values()) {
                MeleeIslandAreaOpenInfo islandInfo = groupInfo.getIslandAreaOpenInfoMap().computeIfAbsent(pointTemplate.id, bean -> new MeleeIslandAreaOpenInfo(pointTemplate.id));

                //如果据店内地块已到达开始时间点，开启
                for (int areaId : pointTemplate.pointAreaIds) {
                    //核对是否该关闭
                    long now = ServerConstants.getCurrentTimeMillis();
                    for (Map.Entry<Integer, Long> entry : new HashMap<>(islandInfo.getAreaEndMap()).entrySet()) {
                        if(now > entry.getValue()){
                            islandInfo.getAreaEndMap().remove(entry.getKey());
                            update = true;
                        }
                    }

                    if(islandInfo.getAreaEndMap().containsKey(areaId)){
                        continue;
                    }

                    MeleeAreaTemplate areaTemplate = areaMap.get(areaId);
                    if(areaTemplate != null){
                        for (Pair<Integer, Integer> pair : areaTemplate.pairList) {
                            if(battleInfo.getOpenTimeIdSet().contains(pair.getLeft())){
                                if(!islandInfo.getAreaEndMap().containsKey(areaId)){
                                    long endTime = getStatusStartTime() + pair.getRight() * DateTimeUtil.MillisOfMinute;
                                    islandInfo.getAreaEndMap().put(areaId, endTime);
                                    update = true;
                                }
                            }
                        }
                    }
                }
            }
        }
        return update;
    }

    /**
     * 刷新乱斗时间点
     * @param battleInfo
     */
    private boolean refreshGameTime(LeagueMeleeBattleInfo battleInfo, Map<Integer, List<MeleePiratesInfo>> returnMap){
        LocalDateTime startTime = DateTimeUtil.toLocalDateTime(getStatusStartTime()).withNano(0);
        LocalDateTime nowTime = ServerConstants.getCurrentTimeLocalDateTime().withNano(0);
        long start = DateTimeUtil.toMillis(startTime);
        long now = DateTimeUtil.toMillis(nowTime);

        boolean needRefresh = false;
        Map<Integer, MeleeGameTimeTemplate> gameTimeTemplateMap = new HashMap<>();
        Set<Integer> openTimeIdSet = battleInfo.getOpenTimeIdSet();

        List<MeleeGameTimeTemplate> gameTimeTemplatelist = LeagueMeleeService.getGameTimeListBySeason(battleInfo.getSeasonId());
        //核对开启阶段
         for (MeleeGameTimeTemplate meleeGameTimeTemplate : gameTimeTemplatelist) {
            if (openTimeIdSet.contains(meleeGameTimeTemplate.id)) {
                continue;
            }

            if(now >= (start + (long) meleeGameTimeTemplate.beginTime * DateTimeUtil.MillisOfMinute)){
                openTimeIdSet.add(meleeGameTimeTemplate.id);
                battleInfo.setHonorPer(meleeGameTimeTemplate.honorPer);
                battleInfo.setCountPer(meleeGameTimeTemplate.countPer);
                needRefresh = true;
                gameTimeTemplateMap.put(meleeGameTimeTemplate.id, meleeGameTimeTemplate);
            }
        }

        returnMap.putAll(refreshMonster(battleInfo, now, gameTimeTemplateMap));
        needRefresh = needRefresh || refreshArea(battleInfo) || !returnMap.isEmpty();
        return needRefresh;
    }

    /**
     * 野怪过期检查
     * @param battleInfo
     */
    private Map<Integer, List<MeleePiratesInfo>> checkMonsterExpire(LeagueMeleeBattleInfo battleInfo){
        //野怪过期时间检测
        return checkMonsterExist(battleInfo);
    }

    /**
     * 刷新野怪
     * @param battleInfo
     */
    private Map<Integer, List<MeleePiratesInfo>> refreshMonster(LeagueMeleeBattleInfo battleInfo, long now, Map<Integer, MeleeGameTimeTemplate> gameTimeTemplateMap){
        Map<Integer, List<MeleePiratesInfo>> returnMap = new HashMap<>();

        if(battleInfo.getStatus() != MeleeStatusType.battle.getType() || gameTimeTemplateMap.isEmpty()){
            return returnMap;
        }

        Map<Integer, MeleeMonsterTemplate> monsterTemplateMap = LeagueMeleeService.getMonsterMap().getOrDefault(battleInfo.getSeasonId(), new HashMap<>());
        if(monsterTemplateMap.isEmpty()){
            GameLogger.error("monster is null, seasonId is " + battleInfo.getSeasonId());
            return returnMap;
        }

        for (Map.Entry<Integer, MeleeGroupInfo> entry : battleInfo.getGroupInfoMap().entrySet()){
            MeleeGroupInfo groupInfo = entry.getValue();
            for (MeleeMonsterTemplate monsterTemplate : monsterTemplateMap.values()) {
                if(!gameTimeTemplateMap.containsKey(monsterTemplate.monsterTime)){
                    continue;
                }

                if(monsterTemplate.monsterNum < 1){
                    continue;
                }

                //检查是否过期
                MeleeGameTimeTemplate gameTimeTemplate = gameTimeTemplateMap.get(monsterTemplate.monsterTime);
                long end = battleInfo.getStatusStartTime() + (gameTimeTemplate.beginTime + monsterTemplate.monsterSurvivalTime) * DateTimeUtil.MillisOfMinute;
                if(now >= end){
                    continue;
                }

                int randomNum = Math.min(monsterTemplate.genPoint.size(), monsterTemplate.monsterNum);
                List<MeleeMapPosition> randomGenPoint = MathUtil.random(monsterTemplate.genPoint, randomNum);
                if(randomGenPoint.isEmpty()){
                    CommonLogger.error(String.format("未刷新出新的野怪！联系策划确认表数据,beginTime:%s,group:%s, monsterInfo:%s", gameTimeTemplate.beginTime,
                            entry.getKey(), Arrays.toString(groupInfo.getMeleePiratesInfoMap().values().toArray())));
                    continue;
                }

                for (MeleeMapPosition mapPosition : randomGenPoint) {
                    MeleePiratesInfo piratesInfo = new MeleePiratesInfo();
                    piratesInfo.setUid(battleInfo.getMonsterId().incrementAndGet());
                    piratesInfo.setPosition(new MeleeMapPosition(mapPosition));
                    piratesInfo.setTemplateId(monsterTemplate.id);
                    piratesInfo.setEndTime(end);
                    groupInfo.getMeleePiratesInfoMap().put(piratesInfo.getUid(), piratesInfo);
                    returnMap.computeIfAbsent(entry.getKey(), list -> new ArrayList<>()).add(piratesInfo);
                }
            }

        }
        return returnMap;
    }

//    private Map<Integer, List<MeleePiratesInfo>> refreshMonster(LeagueMeleeBattleInfo battleInfo, MeleeGameTimeTemplate gameTimeTemplate) {
//        Map<Integer, List<MeleePiratesInfo>> returnMap = new HashMap<>();
//
//        Map<Integer, MeleeMonsterTemplate> monsterTemplateMap = LeagueMeleeService.getMonsterMap().getOrDefault(battleInfo.getSeasonId(), new HashMap<>());
//        if(monsterTemplateMap.isEmpty()){
//            GameLogger.error("monster is null, seasonId is " + battleInfo.getSeasonId());
//            return returnMap;
//        }
//
//        Map<Integer, MeleeGroupInfo> groupInfoMap = battleInfo.getGroupInfoMap();
//        for (Map.Entry<Integer, MeleeGroupInfo> entry : groupInfoMap.entrySet()) {
//            Integer groupId = entry.getKey();
//            MeleeGroupInfo groupInfo = entry.getValue();
//            List<MeleePiratesInfo> returnList = new ArrayList<>();
//
//            for (MeleeMonsterTemplate monsterTemplate : monsterTemplateMap.values()) {
//                if(monsterTemplate.monsterTime != gameTimeTemplate.beginTime){
//                    continue;
//                }
//
//                if(monsterTemplate.monsterNum < 1){
//                    continue;
//                }
//
//                int randomNum = Math.min(monsterTemplate.genPoint.size(), monsterTemplate.monsterNum);
//                List<MeleeMapPosition> randomGenPoint = MathUtil.random(monsterTemplate.genPoint, randomNum);
//                if(randomGenPoint.isEmpty()){
//                    CommonLogger.error(String.format("未刷新出新的野怪！联系策划确认表数据,beginTime:%s,group:%s, monsterInfo:%s", gameTimeTemplate.beginTime,
//                            groupId, Arrays.toString(groupInfo.getMeleePiratesInfoMap().values().toArray())));
////                    return returnMap;
//                    continue;
//                }
//
//                long end = ServerConstants.getCurrentTimeMillis() + monsterTemplate.monsterSurvivalTime * DateTimeUtil.MillisOfMinute;
//                for (MeleeMapPosition mapPosition : randomGenPoint) {
//                    MeleePiratesInfo piratesInfo = new MeleePiratesInfo();
//                    piratesInfo.setUid(battleInfo.getMonsterId().incrementAndGet());
//                    piratesInfo.setPosition(new MeleeMapPosition(mapPosition));
//                    piratesInfo.setTemplateId(monsterTemplate.id);
//                    piratesInfo.setEndTime(end);
//                    groupInfo.getMeleePiratesInfoMap().put(piratesInfo.getUid(), piratesInfo);
//                    returnList.add(piratesInfo);
//                }
//            }
//            returnMap.put(groupId, returnList);
//
//        }
//        return returnMap;
//    }

    @Override
    public void deal(LeagueMeleeBattleInfo battleInfo) {
        //检查野怪过期
        Map<Integer, List<MeleePiratesInfo>> removeMonsters = checkMonsterExpire(battleInfo);
        //刷新时间点
        Map<Integer, List<MeleePiratesInfo>> addMonsters = new HashMap<>();
        boolean areaChange = refreshGameTime(battleInfo, addMonsters);
        // 每个据点按时间产出
        checkIslandAuto(battleInfo);
        // 死亡重生检测
        checkRebirth(battleInfo);

         if(!removeMonsters.isEmpty() || !addMonsters.isEmpty() || areaChange){
            dealBattleTimeChangeNotify(battleInfo, removeMonsters, addMonsters);
        }
    }

    /**
     * 处理战斗阶段变化通知
     * @param battleInfo
     * @param removeMonsters
     * @param addMonsters
     */
    private void dealBattleTimeChangeNotify(LeagueMeleeBattleInfo battleInfo, Map<Integer, List<MeleePiratesInfo>> removeMonsters, Map<Integer, List<MeleePiratesInfo>> addMonsters){
        for (Map.Entry<Integer, MeleeGroupInfo> entry : battleInfo.getGroupInfoMap().entrySet()) {
            MeleeGroupInfo groupInfo = entry.getValue();
            PbProtocol.LeagueMeleeBattleOpenTimeNotify.Builder notify = PbProtocol.LeagueMeleeBattleOpenTimeNotify.newBuilder();
            notify.addAllAreaOpenInfo(LeagueMeleeHelper.genPointAreaOpenInfo(groupInfo.getIslandAreaOpenInfoMap().values()));

            if(!battleInfo.getOpenTimeIdSet().isEmpty()){
                notify.setTimeId(battleInfo.getOpenTimeIdSet().stream().max(Integer::compare).get());
            }

            notify.setCountPer(battleInfo.getCountPer());
            notify.setHonorPer(battleInfo.getHonorPer());

            if(addMonsters.containsKey(entry.getKey())){
                List<MeleePiratesInfo> monsters = addMonsters.get(entry.getKey());
                notify.addAllAddMonster(LeagueMeleeHelper.genMeleeMonsterInfoList(monsters));
            }

            if(removeMonsters.containsKey(entry.getKey())){
                List<MeleePiratesInfo> monsters = removeMonsters.get(entry.getKey());
                notify.addAllRemoveMonster(LeagueMeleeHelper.genMeleeMonsterInfoList(monsters));
            }

            ThreadPool.execute(() -> {
                for (Map<Long, MeleePlayerInInfo> bean : groupInfo.getPlayerInMap().values()) {
                    for (long playerId : bean.keySet()) {
                        Player onLinePlayer = PlayerManager.getOnlinePlayer(playerId);
                        if(onLinePlayer != null){
                            onLinePlayer.send(PtCode.LEAGUE_MELEE_BATTLE_OPEN_TIME_NOTIFY, notify.build());
                        }
                    }
                }
            });
        }
    }

    /**
     * 检测海岛激战区第一自动产出海岛积分
     * 5s 一次
     */
    private void checkIslandAuto(LeagueMeleeBattleInfo battleInfo) {
        long currentTimeMillis = ServerConstants.getCurrentTimeMillis();
        if (currentTimeMillis < lastAutoTime + DURATION_MIL) {
            return;
        }

        this.lastAutoTime = currentTimeMillis;
        DelayTaskManager.addTask(() -> {
            Map<Integer, MeleeGroupInfo> groupInfoMap = battleInfo.getGroupInfoMap();
            Map<Integer, MeleePointTemplate> pointMap = LeagueMeleeService.getPointMapBySeasonId(battleInfo.getSeasonId());
//            Map<Integer, MeleePointTemplate> pointTemplateMap = LeagueMeleeService.getPointMap().getOrDefault(battleInfo.getSeasonId(), new HashMap<>());
            Map<Integer, MeleeAreaTemplate> areaTemplateMap = LeagueMeleeService.getAreaMap();

            for (MeleeGroupInfo groupInfo : groupInfoMap.values()) {
                Map<Integer, MeleeIslandRankInfo> islandRankInfoMap = groupInfo.getIslandRankInfoMap();
                for (Map.Entry<Integer, MeleeIslandRankInfo> entry : islandRankInfoMap.entrySet()) {
                    MeleePointTemplate pointTemplate = pointMap.get(entry.getKey());
                    if(pointTemplate == null){
                        GameLogger.error("pointTemplate is null, seasonId is " + battleInfo.getSeasonId() + " pointId is " + entry.getKey());
                        continue;
                    }
                    int addNum = pointTemplate.pointHonor;

                    long belongLeagueId = 0;
                    Pair<Long, Integer> leagueIdAddition = null;
                    MeleeIslandRankInfo meleeIslandRankInfo = entry.getValue();
                    for (Map.Entry<Integer, Long> longEntry : meleeIslandRankInfo.getFirstLeagueIdMap().entrySet()) {
                        MeleeAreaTemplate areaTemplate = areaTemplateMap.get(longEntry.getKey());
                        if(areaTemplate == null){
                            continue;
                        }

                        if(areaTemplate.areaType == 1 || areaTemplate.areaType == 3){
                            belongLeagueId = longEntry.getValue();
                        }

                        if(areaTemplate.areaType == 2){
                            String key = entry.getKey() + "" + longEntry.getKey();
                            MeleeBossAreaInfo bossAreaInfo = groupInfo.getBossAreaInfoMap().get(key);
                            if(bossAreaInfo != null){
                                belongLeagueId = bossAreaInfo.getLeagueId();
                            }
                        }

                        if(areaTemplate.areaIncome == MeleeIslandAreaBelongIncomeType.honorAddition.getType()){
                            leagueIdAddition = Pair.of(longEntry.getValue(), areaTemplate.areaParameter);
                        }
                    }

                    if(belongLeagueId == 0){
                        continue;
                    }

                    LeagueMeleeHelper.addIslandAuto(belongLeagueId, addNum, pointTemplate.id, leagueIdAddition.getValue());
                }
            }
        });
    }

    /**
     * 检查是否自动重生
     */
    private void checkRebirth(LeagueMeleeBattleInfo battleInfo) {
        long currentTimeMillis = ServerConstants.getCurrentTimeMillis();
        Map<Integer, MeleeGroupInfo> groupInfoMap = battleInfo.getGroupInfoMap();
        for (Map.Entry<Integer, MeleeGroupInfo> entry : groupInfoMap.entrySet()) {
            MeleeGroupInfo groupInfo = entry.getValue();
            for (Map<Long, MeleePlayerInInfo> map : groupInfo.getPlayerInMap().values()) {
                for (MeleePlayerInInfo bean : map.values()) {
                    Long playerId = bean.getPlayerId();
                    Player onlinePlayer = PlayerManager.getOnlinePlayer(playerId);
                    if (Objects.isNull(onlinePlayer)) {
                        continue;
                    }
                    PlayerLeagueMeleeModel playerMeleeModel = onlinePlayer.getModel(PlayerModelEnums.leagueMelee);
                    long rebirthTime = playerMeleeModel.getRebirthTime();
                    //复活放到主线程执行
                    if (playerMeleeModel.getRemainPower() == 0 && currentTimeMillis >= rebirthTime && rebirthTime != -1) {
                        DelayTaskManager.addTask(playerMeleeModel::checkRebirth);
                    }
                }
            }
        }
    }

    /**
     * 检测野怪存在时间，处理过期野怪
     *
     * @param battleInfo 战斗信息
     * @return 移除的天地组海盗
     */
    private Map<Integer, List<MeleePiratesInfo>> checkMonsterExist(LeagueMeleeBattleInfo battleInfo) {
        Map<Integer, MeleeGroupInfo> groupInfoMap = battleInfo.getGroupInfoMap();

        //key:组id
         Map<Integer, List<MeleePiratesInfo>> returnMap = new HashMap<>();
         for (Map.Entry<Integer, MeleeGroupInfo> entry : groupInfoMap.entrySet()) {
            Integer groupId = entry.getKey();
            MeleeGroupInfo groupInfo = entry.getValue();
            Map<Long, MeleePiratesInfo> meleeMonsterInfoMap = groupInfo.getMeleePiratesInfoMap();
            List<MeleePiratesInfo> returnList = new ArrayList<>();
             for (Long uid : new HashSet<>(meleeMonsterInfoMap.keySet())) {
                MeleePiratesInfo meleePiratesInfo = meleeMonsterInfoMap.get(uid);
                if (ServerConstants.getCurrentTimeMillis() >= meleePiratesInfo.getEndTime()) {
                    meleeMonsterInfoMap.remove(uid);
                    returnList.add(meleePiratesInfo);
                    //终止其他在海盗中的战斗
                    LeagueMeleeHelper.stopPiratesFight(groupInfo, uid);
                }
             }
            if (!returnList.isEmpty()) {
                returnMap.put(groupId, returnList);
            }
        }
        return returnMap;
    }

    @Override
    public boolean finish(LeagueMeleeBattleInfo battleInfo) {
        return false;
    }

    @Override
    public int nowStatus() {
        return MeleeStatusType.battle.getType();
    }

    @Override
    public int nextStatus(LeagueMeleeBattleInfo battleInfo) {
        return MeleeStatusType.over.getType();
    }

    @Override
    public long getStatusDurationTime(LeagueMeleeBattleInfo battleInfo) {
        return LeagueMeleeService.getConstant().onTimeMap.get(getMeleeNodeType()).getRight() * DateTimeUtil.MinutesOfHour;
    }

    @Override
    public long getStatusStartTime() {
        return startTime;
    }

    public MeleeNodeType getMeleeNodeType(){
        return MeleeNodeType.battle;
    }
}

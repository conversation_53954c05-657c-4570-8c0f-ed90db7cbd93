package com.gy.server.game.leagueMelee.template;

import java.util.Map;

/**
 * 赛季时间
 *
 * <AUTHOR> 2024/8/27 10:57
 **/
public class MeleeGameTimeTemplate {

    public int id;

    /**
     * 赛季id
     */
    public int seasonId;

    /**
     * 时间节点
     */
    public int beginTime;

    /**
     * 荣誉产出倍率
     */
    public int honorPer;

    /**
     * 各区域积分产出倍率
     */
    public int countPer;

    public MeleeGameTimeTemplate(Map<String, String> map){
        this.id = Integer.parseInt(map.get("id"));
        this.seasonId = Integer.parseInt(map.get("SeasonId"));
        this.beginTime = Integer.parseInt(map.get("beginTime"));
        this.honorPer = Integer.parseInt(map.get("honorPer"));
        this.countPer = Integer.parseInt(map.get("countPer"));
    }

}

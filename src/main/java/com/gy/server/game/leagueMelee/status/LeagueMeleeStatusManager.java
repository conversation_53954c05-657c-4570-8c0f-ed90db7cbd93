package com.gy.server.game.leagueMelee.status;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.battleRule.AbstractBattleManager;
import com.gy.server.core.battleRule.base.BaseBattleInfo;
import com.gy.server.core.log.CommonLogger;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.leagueMelee.LeagueMeleeGlobalData;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.serialize.leagueMelee.MeleeStatusType;

import java.util.Objects;

/**
 * 帮派乱斗管理类
 *
 * <AUTHOR> - [Created on 2023/7/18 10:12]
 */
public class LeagueMeleeStatusManager extends AbstractBattleManager<LeagueMeleeBattleInfo> {

    public LeagueMeleeStatusManager(boolean isRunner) {
        super(isRunner);
    }

    @Override
    protected void saveSonStatus(BaseBattleInfo baseBattleInfo, int currentStatus) {

    }

    @Override
    public LeagueMeleeBattleInfo getBattleInfo() {
        LeagueMeleeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueMelee);
        LeagueMeleeBattleInfo battleInfo = globalData.getBattleInfo();
        if (Objects.isNull(battleInfo)) {
            battleInfo = new LeagueMeleeBattleInfo();
            battleInfo.setStatus(MeleeStatusType.noStart.getType());
            globalData.setBattleInfo(battleInfo);
        }
        return battleInfo;
    }

    @Override
    public void saveBattleInfo(LeagueMeleeBattleInfo baseBattleInfo) {
        LeagueMeleeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueMelee);
        globalData.setBattleInfo(baseBattleInfo);
    }

    @Override
    protected void printChangeStatus(int currentStatus, int nextStatus, long statusStartTime) {
        CommonLogger.info(String.format("帮派乱斗状态切换：%s -> %s, %s 开始时间: %s, 切换时间: %s", MeleeStatusType.of(currentStatus), MeleeStatusType.of(nextStatus),
                 MeleeStatusType.of(currentStatus), DateTimeUtil.toLocalDateTime(statusStartTime), ServerConstants.getCurrentTimeLocalDateTime()));
    }

    @Override
    public void start() {
        try {
            super.initClazz();
        } catch (IllegalAccessException | InstantiationException e) {
            e.printStackTrace();
        }

    }

    @Override
    public boolean isGameServer() {
        return true;
    }

    @Override
    public boolean isWorldServer() {
        return false;
    }
}

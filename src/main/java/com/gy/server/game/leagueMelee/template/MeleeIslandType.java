package com.gy.server.game.leagueMelee.template;

import java.util.HashMap;
import java.util.Map;

/**
 * 据点类型
 * 乱斗据点类型枚举
 * 1.战斗据点
 * 2.资源据点
 * 3.出生据点
 * <AUTHOR> - [Created on 2023/7/20 15:05]
 */
public enum MeleeIslandType {
    /**
     * 战斗岛
     */
    battle(1),
    /**
     * 资源岛
     */
    resource(2),
    /**
     * 出生岛
     */
    birth(3),

    ;
    private final int type;


    MeleeIslandType(int type) {
        this.type = type;
    }

    private static final Map<Integer, MeleeIslandType> MAP = new HashMap<>();

    public int getType() {
        return type;
    }

    public static MeleeIslandType of(int type) {
        return MAP.get(type);
    }

    static {
        for (MeleeIslandType islandType : values()) {
            MAP.put(islandType.getType(), islandType);
        }
    }
}

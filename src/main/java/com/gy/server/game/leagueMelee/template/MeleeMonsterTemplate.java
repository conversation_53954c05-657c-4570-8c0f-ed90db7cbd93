package com.gy.server.game.leagueMelee.template;

import com.gy.server.game.log.GameLogger;
import com.gy.server.game.util.StringExtUtil;
import com.ttlike.server.tl.baselib.serialize.leagueMelee.MeleeMapPosition;
import org.apache.commons.lang3.tuple.Pair;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 怪物
 *
 * <AUTHOR> 2024/8/27 11:07
 **/
public class MeleeMonsterTemplate {

    public int id;

    /**
     * 赛季id
     */
    public int seasonId;

    /**
     * 机器人id
     */
    public int npcId;

    /**
     * 阵容id
     */
    public int battleCollectId;

    /**
     * 生成区域
     */
    public List<MeleeMapPosition> genPoint = new ArrayList<>();

    /**
     * 大地图怪物半径
     */
    public int radius;

    /**
     * 击杀后荣誉值
     */
    public int honor;

    /**
     * 击杀后获得buff
     */
    public int commandCount;

    /**
     * 怪出现的时间
     */
    public int monsterTime;

    /**
     * 怪存在时间 分钟
     */
    public int monsterSurvivalTime;

    /**
     * 刷新的怪物数量
     */
    public int monsterNum;

    public MeleeMonsterTemplate(Map<String, String> map){
        this.id = Integer.parseInt(map.get("id"));
        this.seasonId = Integer.parseInt(map.get("SeasonId"));
        this.npcId = Integer.parseInt(map.get("npcId"));
        this.battleCollectId = Integer.parseInt(map.get("battleCollectId"));
        List<String> genPointStr = StringExtUtil.string2List(map.get("genPoint"), "|", String.class);
        for (String s : genPointStr) {
            Pair<Integer, Integer> pair = StringExtUtil.string2Pair(s, ",", Integer.class, Integer.class);
            MeleeMapPosition position = new MeleeMapPosition(pair.getLeft(), pair.getRight());
            if(genPoint.contains(position)){
                GameLogger.error("league melee monster genPoint repeat, id is " + id);
            }
            this.genPoint.add(position);
        }
        this.radius = Integer.parseInt(map.get("radius"));
        this.honor = Integer.parseInt(map.get("honor"));
        this.commandCount = Integer.parseInt(map.get("commandCount"));
        this.monsterTime = Integer.parseInt(map.get("monsterTime"));
        this.monsterSurvivalTime = Integer.parseInt(map.get("monsterSurvivalTime"));
        this.monsterNum = Integer.parseInt(map.get("monsterNum"));
    }
}

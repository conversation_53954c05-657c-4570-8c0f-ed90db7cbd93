package com.gy.server.game.leagueMelee;

import com.gy.server.game.global.GlobalData;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueDataInterface;
import com.gy.server.game.leagueMelee.status.LeagueMeleeBattleInfo;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.ttlike.server.tl.baselib.serialize.leagueMelee.LeagueMeleeGlobalDb;
import com.ttlike.server.tl.baselib.serialize.leagueMelee.MeleeStatusType;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 帮派乱斗
 *
 * <AUTHOR> - [Created on 2023/7/18 10:35]
 */
public class LeagueMeleeGlobalData extends GlobalData implements LeagueDataInterface {

    private LeagueMeleeBattleInfo battleInfo;

    /**
     * 玩家累计荣誉值
     */
    private Map<Long, Long> totalHonorMap = new HashMap<>();


    @Override
    public void readFromPb(byte[] bytes) throws Exception {
        LeagueMeleeGlobalDb db = PbUtilCompress.decode(LeagueMeleeGlobalDb.class, bytes);
        if (Objects.nonNull(db)) {
            battleInfo = new LeagueMeleeBattleInfo();
            battleInfo.setOpenNum(db.getOpenNum());
            battleInfo.setOpen(db.isOpen());
            battleInfo.setGroupIndexMap(db.getGroupIndexMap());
            battleInfo.setGroupInfoMap(db.getGroupInfoMap());
            battleInfo.setPlayerInfoMap(db.getPlayerInfoMap());
            battleInfo.setLeagueInfoMap(db.getLeagueInfoMap());
            battleInfo.setHonorPer(db.getHonorPer());
            battleInfo.setCountPer(db.getCountPer());
            battleInfo.setLastTHonorRankList(db.getLastTHonorRankList());
            battleInfo.setLastDHonorRankList(db.getLastDHonorRankList());
            battleInfo.setHonorPanel(db.getHonorPanel());
            battleInfo.setStatusStartTime(db.getStatusStartTime());
            battleInfo.setStatus(db.getStatus());
            battleInfo.setOpenTimeIdSet(db.getOpenTimeIdSet());
            battleInfo.setSeasonId(db.getSeasonId());
            battleInfo.setSignTime(db.getSignTime());
            totalHonorMap = db.getTotalHonorMap();
        }
    }

    @Override
    public byte[] writeToPb() {
        if (Objects.isNull(battleInfo)) {
            battleInfo = new LeagueMeleeBattleInfo();
            battleInfo.setStatus(MeleeStatusType.noStart.getType());
            battleInfo.setSeasonId(0);
        }
        LeagueMeleeGlobalDb db = new LeagueMeleeGlobalDb();
        db.setStatus(battleInfo.getStatus());
        db.setStatusStartTime(battleInfo.getStatusStartTime());
        db.setOpenNum(battleInfo.getOpenNum());
        db.setOpen(battleInfo.isOpen());
        db.setGroupIndexMap(battleInfo.getGroupIndexMap());
        db.setGroupInfoMap(battleInfo.getGroupInfoMap());
        db.setPlayerInfoMap(battleInfo.getPlayerInfoMap());
        db.setLeagueInfoMap(battleInfo.getLeagueInfoMap());
        db.setHonorPer(battleInfo.getHonorPer());
        db.setCountPer(battleInfo.getCountPer());
        db.setLastTHonorRankList(battleInfo.getLastTHonorRankList());
        db.setLastDHonorRankList(battleInfo.getLastDHonorRankList());
        db.setHonorPanel(battleInfo.getHonorPanel());
        db.setTotalHonorMap(totalHonorMap);
        db.setOpenTimeIdSet(battleInfo.getOpenTimeIdSet());
        db.setSeasonId(battleInfo.getSeasonId());
        db.setSignTime(battleInfo.getSignTime());
        return PbUtilCompress.encode(db);
    }

    public void setBattleInfo(LeagueMeleeBattleInfo battleInfo) {
        this.battleInfo = battleInfo;
    }

    public LeagueMeleeBattleInfo getBattleInfo() {
        return battleInfo;
    }

    public Map<Long, Long> getTotalHonorMap() {
        return totalHonorMap;
    }

    public void setTotalHonorMap(Map<Long, Long> totalHonorMap) {
        this.totalHonorMap = totalHonorMap;
    }

    @Override
    public boolean canMerge(long leagueId) {
        if(battleInfo.isOpen()){
            return battleInfo.getGroupIndexMap().get(leagueId) == null;
        }
        return true;
    }

    @Override
    public void merge(League selfLeague, League targetLeague) {

    }

    @Override
    public void removeLeague(League league) {

    }

    @Override
    public void quitLeague(long pid) {

    }
}

package com.gy.server.game.leagueMelee.status.impl;

import com.gy.server.core.battleRule.base.IBattleStatusDeal;
import com.gy.server.game.leagueMelee.LeagueMeleeService;
import com.gy.server.game.leagueMelee.status.LeagueMeleeBattleInfo;
import com.gy.server.game.leagueMelee.status.MeleeNodeType;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.serialize.leagueMelee.MeleeStatusType;


/**
 * 数据拉取完成等待布阵状态开启
 * 等待时间到了自动切状态
 *
 * <AUTHOR> - [Created on 2023/7/19 9:50]
 */
public class LeagueMelee4BeforeLineupHandler implements IBattleStatusDeal<LeagueMeleeBattleInfo> {

    private long startTime;

    @Override
    public void init(LeagueMeleeBattleInfo battleInfo) {
        this.startTime = battleInfo.getStatusStartTime(getMeleeNodeType());
    }

    @Override
    public void deal(LeagueMeleeBattleInfo battleInfo) {

    }

    /**
     * 完成标志
     * 永远为false等待时间结束自动切状态
     */
    @Override
    public boolean finish(LeagueMeleeBattleInfo battleInfo) {
        return false;
    }

    @Override
    public int nowStatus() {
        return MeleeStatusType.beforeLineup.getType();
    }

    @Override
    public int nextStatus(LeagueMeleeBattleInfo battleInfo) {
        return MeleeStatusType.lineup.getType();
    }

    @Override
    public long getStatusDurationTime(LeagueMeleeBattleInfo battleInfo) {
        return LeagueMeleeService.getConstant().onTimeMap.get(getMeleeNodeType()).getRight() * DateTimeUtil.MinutesOfHour;
    }

    @Override
    public long getStatusStartTime() {
        return startTime;
    }

    public MeleeNodeType getMeleeNodeType(){
        return MeleeNodeType.ready;
    }
}

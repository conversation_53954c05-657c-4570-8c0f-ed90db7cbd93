package com.gy.server.game.leagueMelee.status.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.battleRule.base.IBattleStatusDeal;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.function.Function;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.league.bean.LeagueMember;
import com.gy.server.game.league.event.LeagueEventType;
import com.gy.server.game.leagueMelee.LeagueMeleeService;
import com.gy.server.game.leagueMelee.PlayerLeagueMeleeModel;
import com.gy.server.game.leagueMelee.status.LeagueMeleeBattleInfo;
import com.gy.server.game.leagueMelee.status.MeleeNodeType;
import com.gy.server.game.leagueMelee.template.MeleeLeagueRewardTemplate;
import com.gy.server.game.leagueMelee.template.MeleeRewardTemplate;
import com.gy.server.game.mail.MailManager;
import com.gy.server.game.mail.MailType;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbCommons;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.serialize.leagueMelee.MeleeGroupInfo;
import com.ttlike.server.tl.baselib.serialize.leagueMelee.MeleeLeagueHonorRankNode;
import com.ttlike.server.tl.baselib.serialize.leagueMelee.MeleePlayerHonorRankNode;
import com.ttlike.server.tl.baselib.serialize.leagueMelee.MeleeStatusType;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.*;

/**
 * 发奖状态
 *
 * <AUTHOR> - [Created on 2023/7/19 9:50]
 */
public class LeagueMelee10SendRewardHandler implements IBattleStatusDeal<LeagueMeleeBattleInfo> {

    private long startTime;

    @Override
    public void init(LeagueMeleeBattleInfo battleInfo) {
        this.startTime = battleInfo.getStatusStartTime(getMeleeNodeType());
    }

    @Override
    public void deal(LeagueMeleeBattleInfo battleInfo) {
        Map<Integer, MeleeLeagueRewardTemplate> leagueRewardMap = LeagueMeleeService.getLeagueRewardMap();
        Map<Integer, MeleeRewardTemplate> rewardMap = LeagueMeleeService.getRewardMap();
        Map<Integer, MeleeGroupInfo> groupInfoMap = battleInfo.getGroupInfoMap();
        long sendTime = ServerConstants.getCurrentTimeMillis();

        //key:联盟id  value:排名
        Map<Long, Integer> rankMap = new HashMap<>();
        //key:联盟id  value:成员id Map
        Map<Long, Set<Long>> memberIdMap = new HashMap<>();

        for (Map.Entry<Integer, MeleeGroupInfo> entry : groupInfoMap.entrySet()) {
            Integer groupId = entry.getKey();
            MeleeGroupInfo meleeGroupInfo = entry.getValue();
            // 帮派团队奖励
            Map<Long, MeleeLeagueHonorRankNode> honorRankMap = meleeGroupInfo.getHonorRankMap();
            List<MeleeLeagueHonorRankNode> honorRankNodeList = new ArrayList<>(honorRankMap.values());
            Collections.sort(honorRankNodeList);
            for (int i = 0; i < honorRankNodeList.size(); i++) {
                int rank = i + 1;
                for (MeleeLeagueRewardTemplate leagueReward : leagueRewardMap.values()) {
                    if (leagueReward.group != groupId) {
                        continue;
                    }

                    if (rank < leagueReward.rank.getKey() || rank > leagueReward.rank.getRight()) {
                        continue;
                    }

                    MeleeLeagueHonorRankNode meleeLeagueHonorRankNode = honorRankNodeList.get(i);
                    League league = LeagueManager.getLeagueById(meleeLeagueHonorRankNode.getLeagueId());
                    Map<Long, LeagueMember> memberMap = league.getMemberMap();
                    for (Long memberId : memberMap.keySet()) {
                        List<Reward> rewardList = RewardTemplate.createRewards(leagueReward.rewardGroup);
                        MailType mailType = MailType.meleeTeamRankReward;
                        PbCommons.PbText titleText = Text.genText(mailType.getTitleId()).build();
                        PbCommons.PbText contentText = Text.genText(mailType.getContentId()).build();
                        MailManager.sendMail(mailType, memberId, titleText, contentText, sendTime, rewardList);
                    }

                    rankMap.put(league.getLeagueId(), rank);
                    break;
                }
            }

            //个人荣誉值奖励处理
            Map<Long, MeleePlayerHonorRankNode> playerHonorRankMap = meleeGroupInfo.getPlayerHonorRankMap();
            List<MeleePlayerHonorRankNode> playerHonorRankList = new ArrayList<>(playerHonorRankMap.values());
            Collections.sort(playerHonorRankList);
            for (int i = 0; i < playerHonorRankList.size(); i++) {
                MeleePlayerHonorRankNode meleePlayerHonorRankNode = playerHonorRankList.get(i);
                List<RewardTemplate> reissueRewardList = new ArrayList<>();
                // 个人排名奖励
                for (MeleeRewardTemplate rewardTemplate : rewardMap.values()) {
                    if (rewardTemplate.group != groupId) {
                        continue;
                    }

                    if(rewardTemplate.rewardType == 1){
                        //个人荣誉值奖励补发
                        long honorValue = meleePlayerHonorRankNode.getHonorValue();
                        if (honorValue >= rewardTemplate.rewardScore) {
                            reissueRewardList.addAll(rewardTemplate.rewardGroup);
                        }
                    }
                }

                //个人荣誉奖励补发
                if (!reissueRewardList.isEmpty()) {
                    List<Reward> rewards = RewardTemplate.createRewards(reissueRewardList);
                    Reward.merge(rewards);
                    MailType mailType = MailType.meleeReissuePersonReward;
                    PbCommons.PbText titleText = Text.genText(mailType.getTitleId()).build();
                    PbCommons.PbText contentText = Text.genText(mailType.getContentId()).build();
                    MailManager.sendMail(mailType, meleePlayerHonorRankNode.getPlayerId(), titleText, contentText, sendTime, rewards);
                }

                long leagueId = LeagueManager.getLeagueId(meleePlayerHonorRankNode.getPlayerId());
                memberIdMap.computeIfAbsent(leagueId, set -> new HashSet<>()).add(meleePlayerHonorRankNode.getPlayerId());
            }
        }

        for (Map.Entry<Long, Integer> entry : rankMap.entrySet()) {
            long leagueId = entry.getKey();
            int rank = entry.getValue();
            Set<Long> set = memberIdMap.getOrDefault(leagueId, new HashSet<>());

            League league = LeagueManager.getLeagueById(leagueId);
            if(league != null){
                // 1.functionId 2.帮派排名（没有填0）3.玩法胜负（没有填false） 4.参与成员id集合
                league.postEvent(LeagueEventType.gveEnd, Function.leagueMelee.getId(), rank, false, set);
            }
        }

        ThreadPool.execute(() -> {
            for (Map.Entry<Integer, MeleeGroupInfo> entry : groupInfoMap.entrySet()){
                int groupId = entry.getKey();
                MeleeGroupInfo meleeGroupInfo = entry.getValue();

                List<RewardTemplate> rewardTemplateList = new ArrayList<>();
                for (MeleePlayerHonorRankNode bean : meleeGroupInfo.getPlayerHonorRankList()) {
                    for (MeleeRewardTemplate template : LeagueMeleeService.getRewardMap().values()) {
                        Player player = PlayerManager.getPlayer(bean.getPlayerId());
                        if(player != null){
                            PlayerLeagueMeleeModel meleeModel = player.getPlayerLeagueMeleeModel();
                            if(groupId == template.group && template.rewardType == 2 && meleeModel.getAllCostPower() >= template.rewardScore){
                                rewardTemplateList.addAll(template.rewardGroup);
                            }
                        }
                    }

                    if(!rewardTemplateList.isEmpty()){
                        List<Reward> rewardList = Reward.templateCollectionToReward(rewardTemplateList);
                        Reward.merge(rewardList);

                        MailType mailType = MailType.meleePersonRankReward;
                        PbCommons.PbText titleText = Text.genText(mailType.getTitleId()).build();
                        PbCommons.PbText contentText = Text.genText(mailType.getContentId()).build();
                        MailManager.sendMail(mailType, bean.getPlayerId(), titleText, contentText, sendTime, rewardList);
                    }
                }

            }
        });

    }

    /**
     * 完成标志
     */
    @Override
    public boolean finish(LeagueMeleeBattleInfo battleInfo) {
        return true;
    }

    @Override
    public int nowStatus() {
        return MeleeStatusType.sendReward.getType();
    }

    @Override
    public int nextStatus(LeagueMeleeBattleInfo battleInfo) {
        return MeleeStatusType.beforeEnd.getType();
    }

    @Override
    public long getStatusDurationTime(LeagueMeleeBattleInfo battleInfo) {
        return LeagueMeleeService.getConstant().onTimeMap.get(getMeleeNodeType()).getRight() * DateTimeUtil.MinutesOfHour;
    }

    @Override
    public long getStatusStartTime() {
        return startTime;
    }

    public MeleeNodeType getMeleeNodeType(){
        return MeleeNodeType.sendReward;
    }
}

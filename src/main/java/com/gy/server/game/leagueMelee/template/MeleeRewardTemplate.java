package com.gy.server.game.leagueMelee.template;

import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.log.GameLogger;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 奖励
 *
 * <AUTHOR> 2024/8/27 13:23
 **/
public class MeleeRewardTemplate {

    public int id;

    /**
     * 组别
     * 1天组2地组
     */
    public int group;

    /**
     * 奖励类型
     * 1.荣誉值
     * 2.消耗x体力
     */
    public int rewardType;

    /**
     * 类型1：对应的荣誉值
     * 类型2：消耗的体力值
     */
    public int rewardScore;

    /**
     * 奖励
     */
    public List<RewardTemplate> rewardGroup = new ArrayList<>();

    public MeleeRewardTemplate(Map<String, String> map){
        this.id = Integer.parseInt(map.get("id"));
        this.group = Integer.parseInt(map.get("group"));
        this.rewardType = Integer.parseInt(map.get("rewardType"));
        if(this.rewardType != 1 && this.rewardType != 2){
            GameLogger.error("league melee reward rewardType is null, id is " + this.id);
        }

        this.rewardScore = Integer.parseInt(map.get("rewardScore"));
        this.rewardGroup = RewardTemplate.readListFromText(map.get("rewardGroup"));
    }

}

package com.gy.server.game.leagueMelee.template;

import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.util.StringExtUtil;
import org.apache.commons.lang3.tuple.Pair;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 联盟奖励
 *
 * <AUTHOR> 2024/8/27 13:31
 **/
public class MeleeLeagueRewardTemplate {

    public int id;

    /**
     * 组别
     */
    public int group;

    /**
     * 排名
     */
    public Pair<Integer, Integer> rank;

    /**
     * 奖励
     */
    public List<RewardTemplate> rewardGroup = new ArrayList<>();


    public MeleeLeagueRewardTemplate(Map<String, String> map){
        this.id = Integer.parseInt(map.get("id"));
        this.group = Integer.parseInt(map.get("group"));
        this.rank = StringExtUtil.string2Pair(map.get("rank"), ",", Integer.class, Integer.class);
        this.rewardGroup = RewardTemplate.readListFromText(map.get("rewardGroup"));
    }

}

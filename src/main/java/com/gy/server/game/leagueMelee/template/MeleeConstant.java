package com.gy.server.game.leagueMelee.template;

import com.gy.server.game.leagueMelee.status.MeleeNodeType;
import com.gy.server.game.util.StringExtUtil;
import org.apache.commons.lang3.tuple.Pair;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 乱斗常量表
 *
 * <AUTHOR> 2024/8/27 13:35
 **/
public class MeleeConstant {

    /**
     * 服务器匹配（多少天内算活跃玩家）
     */
    public int activeTimeHour;

    /**
     * 帮派定义所需的玩家人数
     */
    public int activeMember;

    /**
     * 天组帮会数
     */
    public int groupTNumber;

    /**
     * 开启玩法需要的最小满足条件的帮派数量
     */
    public int openMinLeagueNum;

    /**
     * 组末X名和地组前X名进行互换
     */
    public int changeRankNum;

    /**
     * 每周几开启 1-7
     */
    public int onTimeWeek;

    /**
     *  移动速度
     */
    public int speed;

    /**
     * 2D地图宽度
     */
    public int xMax;

    /**
     * 2D地图高度
     */
    public int yMax;

    /***
     * 时间表信息！（节点|开启时间|到下个节点的时间）
     */
    public Map<MeleeNodeType, Pair<LocalTime, Integer>> onTimeMap = new HashMap<>();

    /**
     * 连续输多少把保底
     */
    public int loseMax;

    /**
     * 指挥帮派Buffid
     */
    public int commandBuff;

    /**
     * 区域类型_积分
     * 激战区胜利保底区域积分
     */
    public Map<Integer, Integer> combatWinCountMap = new HashMap<>();

    /**
     * 区域类型_积分
     * 激战区失败保底区域积分
     */
    public Map<Integer, Integer> combatLoseCountMap = new HashMap<>();

    /**
     * 区域类型_积分
     * 激战区单场连胜区域积分
     */
    public Map<Integer, Integer> combatContinuousWinCountMap = new HashMap<>();

    /**
     * 区域类型_荣誉值
     * 激战区胜利保底荣誉值
     */
    public Map<Integer, Integer> combatWinHonorMap = new HashMap<>();

    /**
     * 区域类型_荣誉值
     * 激战区失败保底荣誉值
     */
    public Map<Integer, Integer> combatLoseHonorMap = new HashMap<>();

    /**
     * 区域类型_荣誉值
     * 激战区单场连胜荣誉值
     */
    public Map<Integer, Integer> combatContinuousWinHonorMap = new HashMap<>();

    /**
     * 区域类型_消耗体力
     * 激战区胜利消耗体力
     */
    public Map<Integer, Integer> combatWinEnergyMap = new HashMap<>();

    /**
     * 区域类型_消耗体力
     * 激战区失败消耗体力
     */
    public Map<Integer, Integer> combatLoseEnergyMap = new HashMap<>();

    /**
     * 连胜场次最大数
     */
    public int winCountMax;

    /**
     * 后援区护送荣誉值
     */
    public int supportHonor;

    /**
     * 后援区护送积分
     */
    public int supportCount;

    /**
     * 后援区最大运送次数
     */
    public int supportMaxCount;

    /**
     * 复活所需元宝
     */
    public List<Integer> forefrontLoseCountList = new ArrayList<>();

    /**
     * 多少个海盗点获得一次特殊指挥
     */
    public int commandEvery;

    /**
     * 默认开始的岛屿区域类型
     */
    public List<Integer> alwaysOpenArea = new ArrayList<>();

    /**
     * 区域排行榜条数
     */
    public int countRankNum;

    /**
     * 荣誉值排行榜条数
     */
    public int honorRankNum;

    /**
     * 战术板条数
     */
    public int tacticNum;

    /**
     * buff持續時間（秒）
     */
    public int tacticBuffTime;

    /**
     * 某区域展示条数
     */
    public int regionalReportNum;

    /**
     * 失败机器人匹配时间
     */
    public List<Integer> roborMateTime = new ArrayList<>();

    /**
     * 普通匹配时间
     */
    public List<Integer> ordinaryMateTime = new ArrayList<>();

    /**
     * 战斗场景id
     */
    public int battleCollectId;

    /**
     * 初始体力
     */
    public int beginEnergy;

    /**
     * 个人战报条数
     */
    public int personalReportNum;

    /**
     * 帮派战报条数
     */
    public int gangReportNum;

    /**
     * 复活时间
     */
    public int resurrectionTime;

    /**
     * 前几名会参加八荒逐鹿
     */
    public int participateNum;

    /**
     * 第一名工会展示前几名玩家
     */
    public int showNum;

    /**
     * 周几展示八荒逐鹿名单
     */
    public int showWeekDay;

    /**
     * 有权限的玩家集火指挥次数
     */
    public int convergeTimes;

    /**
     * 机器人的积分
     */
    public int robotPoints;

    /**
     * 机器人的荣誉值
     */
    public int robotHonor;

    public MeleeConstant(Map<String, String> map){
        activeTimeHour = Integer.parseInt(map.get("activeTimeHour"));
        activeMember = Integer.parseInt(map.get("activeMember"));
        groupTNumber = Integer.parseInt(map.get("groupTNumber"));
        openMinLeagueNum = Integer.parseInt(map.get("openMinLeagueNum"));
        changeRankNum = Integer.parseInt(map.get("changeRankNum"));
        onTimeWeek = Integer.parseInt(map.get("onTimeWeek"));
        speed = Integer.parseInt(map.get("speed"));
        xMax = Integer.parseInt(map.get("xMax"));
        yMax = Integer.parseInt(map.get("yMax"));

        String onTimeStr = map.get("onTime");
        String[] onTimeStrArr = onTimeStr.split(",");
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("HH:mm");
        for (String onTimeTmp : onTimeStrArr) {
            String[] split = onTimeTmp.split("[|]");
            MeleeNodeType nodeType = MeleeNodeType.of(Integer.parseInt(split[0]));
            LocalTime localTime = LocalTime.parse(split[1], dateTimeFormatter);
            int duration = Integer.parseInt(split[2]);
            this.onTimeMap.put(nodeType, Pair.of(localTime, duration));
        }

        loseMax = Integer.parseInt(map.get("loseMax"));
        commandBuff = Integer.parseInt(map.get("activeTimeHour"));
        this.combatWinCountMap = StringExtUtil.string2Map(map.get("combatWinCount"), ",", "_", Integer.class, Integer.class);
        this.combatLoseCountMap = StringExtUtil.string2Map(map.get("combatLoseCount"), ",", "_", Integer.class, Integer.class);
        this.combatContinuousWinCountMap = StringExtUtil.string2Map(map.get("combatContinuousWinCount"), ",", "_", Integer.class, Integer.class);

        this.combatWinHonorMap = StringExtUtil.string2Map(map.get("combatWinHonor"), ",", "_", Integer.class, Integer.class);
        this.combatLoseHonorMap = StringExtUtil.string2Map(map.get("combatLoseHonor"), ",", "_", Integer.class, Integer.class);
        this.combatContinuousWinHonorMap = StringExtUtil.string2Map(map.get("combatContinuousWinHonor"), ",", "_", Integer.class, Integer.class);

        this.combatWinEnergyMap = StringExtUtil.string2Map(map.get("combatWinEnergy"), ",", "_", Integer.class, Integer.class);
        this.combatLoseEnergyMap = StringExtUtil.string2Map(map.get("combatLoseEnergy"), ",", "_", Integer.class, Integer.class);

        winCountMax = Integer.parseInt(map.get("winCountMax"));
        supportHonor = Integer.parseInt(map.get("supportHonor"));
        supportCount = Integer.parseInt(map.get("supportCount"));
        supportMaxCount = Integer.parseInt(map.get("supportMaxCount"));
        forefrontLoseCountList = StringExtUtil.string2List(map.get("forefrontLoseCount"), "|", Integer.class);
        commandEvery = Integer.parseInt(map.get("commandEvery"));
        alwaysOpenArea = StringExtUtil.string2List(map.get("alwaysOpenArea"), "|", Integer.class);
        countRankNum = Integer.parseInt(map.get("countRankNum"));
        honorRankNum = Integer.parseInt(map.get("honorRankNum"));
        tacticNum = Integer.parseInt(map.get("tacticNum"));
        if(tacticNum < 0){
            throw new IllegalArgumentException("MeleeConstant tacticNum error!");
        }

        tacticBuffTime = Integer.parseInt(map.get("tacticBuffTime"));
        regionalReportNum = Integer.parseInt(map.get("regionalReportNum"));
        roborMateTime = StringExtUtil.string2List(map.get("roborMateTime"), ",", Integer.class);
        ordinaryMateTime = StringExtUtil.string2List(map.get("ordinaryMateTime"), ",", Integer.class);
        battleCollectId = Integer.parseInt(map.get("battleCollectId"));
        beginEnergy = Integer.parseInt(map.get("beginEnergy"));
        personalReportNum = Integer.parseInt(map.get("personalReportNum"));
        gangReportNum = Integer.parseInt(map.get("gangReportNum"));
        resurrectionTime = Integer.parseInt(map.get("resurrectionTime"));
        participateNum = Integer.parseInt(map.get("participateNum"));
        showNum = Integer.parseInt(map.get("showNum"));
        showWeekDay = Integer.parseInt(map.get("showWeekDay"));
        convergeTimes = Integer.parseInt(map.get("ConvergeTimes"));
        robotPoints = Integer.parseInt(map.get("RobotPoints"));
        robotHonor = Integer.parseInt(map.get("RobotHonor"));

        if(groupTNumber < changeRankNum){
            throw new RuntimeException("MeleeConstant data error, groupTNumber less than changeRankNum.");
        }
    }

}

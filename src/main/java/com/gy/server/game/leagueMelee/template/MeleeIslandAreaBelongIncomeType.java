package com.gy.server.game.leagueMelee.template;

/**
 * 地块区域收益类型
 * 1.获得归属权/一次性获得的荣誉值
 * 2.获得增益buff/buffId
 * 3.获得本据点荣誉值加成
 * <AUTHOR> 2024/8/29 10:15
 **/
public enum MeleeIslandAreaBelongIncomeType {
    /**
     * 1.获得归属权/一次性获得的荣誉值
     */
    attribution(1),

    /**
     * 2.获得增益buff/buffId
     */
    getBuff(2),

    /**
     * 3.获得本据点荣誉值加成
     */
    honorAddition(3),
    ;

    private int type;

    MeleeIslandAreaBelongIncomeType(int type){
        this.type = type;
    }

    public int getType() {
        return type;
    }
}

package com.gy.server.game.leagueMelee.status;

import java.util.HashMap;
import java.util.Map;

/**
 * 帮派乱斗各个节点
 * 1.赛程准备&本服玩家分组:本节点统计全体服务器的数据准备,按照当前服务器当前的数据，按照分组规则对服务器的玩家进行分组处理
 * 2.玩法玩家准备:本节点内，玩家可以预设玩家在玩法内的阵容进入2d场景等操作
 * 3.玩法开战节点：玩法正式开始节点，本节点中玩家可以进入战场，进行战斗
 * 4.展示/发奖节点：统计排名，发奖等信息，对玩家进行对应名次的发奖等操作（团队失败胜利/个人奖励达标/宝箱奖励）分三个邮件
 * 5.結束階段：清空对应垃圾数据，以及对玩法进行关闭
 * <AUTHOR> - [Created on 2023/7/19 16:29]
 */
public enum MeleeNodeType {
    /**
     * 准备阶段
     */
    ready(1),
    /**
     * 玩法玩家准备
     */
    lineup(2),
    /**
     * 玩法开战节点
     */
    battle(3),

    /**
     * 玩法结束，展示/发奖节点
     */
    sendReward(4),
    /**
     * 结束并清空数据
     */
    endAndClear(5),

    ;

    private final int type;

    private static final Map<Integer, MeleeNodeType> map = new HashMap<>();

    MeleeNodeType(int type) {
        this.type = type;
    }

    public static MeleeNodeType of(int type) {
        return map.get(type);
    }

    static {
        for (MeleeNodeType value : values()) {
            map.put(value.type, value);
        }
    }

    public int getType() {
        return type;
    }
}

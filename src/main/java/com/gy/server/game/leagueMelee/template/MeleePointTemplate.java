package com.gy.server.game.leagueMelee.template;

import com.gy.server.game.util.StringExtUtil;
import com.ttlike.server.tl.baselib.serialize.leagueMelee.MeleeMapPosition;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 据点
 *
 * <AUTHOR> 2024/8/27 11:31
 **/
public class MeleePointTemplate {

    public int id;

    /**
     * 赛季id
     */
    public int seasonId;

    /**
     * 据点类型
     */
    public MeleeIslandType pointType;

    /**
     * 据点位置
     */
    public MeleeMapPosition pointPos;

    /**
     * 据点分数
     */
    public int pointHonor;

    /**
     * 据点中地块构成
     */
    public List<Integer> pointAreaIds = new ArrayList<>();

    /**
     * 搬砖任务目标据点
     */
    public int pointNatural;

    public MeleePointTemplate(Map<String, String> map){
        this.id = Integer.parseInt(map.get("id"));
        this.seasonId = Integer.parseInt(map.get("SeasonId"));
        this.pointType = MeleeIslandType.of(Integer.parseInt(map.get("pointType")));
        this.pointPos = MeleeMapPosition.readFromStr(map.get("pointPos"));

        String pointHonorTemp = map.get("pointHonor");
        if(pointHonorTemp != null && !pointHonorTemp.equals("")){
            this.pointHonor = Integer.parseInt(pointHonorTemp);
        }

        String pointAreaStr = map.get("pointarea");
        if(!pointAreaStr.equals("-1")){
            this.pointAreaIds = StringExtUtil.string2List(pointAreaStr, "|", Integer.class);
        }

        String pointNaturalTemp = map.get("pointNatural");
        if(pointNaturalTemp != null && !pointNaturalTemp.equals("")){
            this.pointNatural = Integer.parseInt(pointNaturalTemp);
        }
    }
}

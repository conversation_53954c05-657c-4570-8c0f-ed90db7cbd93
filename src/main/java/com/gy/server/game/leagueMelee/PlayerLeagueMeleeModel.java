package com.gy.server.game.leagueMelee;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.leagueMelee.status.LeagueMeleeBattleInfo;
import com.gy.server.game.leagueMelee.template.MeleeConstant;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModel;
import com.gy.server.game.player.event.PlayerEvent;
import com.gy.server.game.player.event.PlayerEventHandler;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.packet.PbLeagueMelee;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbScene;
import com.ttlike.server.tl.baselib.serialize.leagueMelee.*;
import com.ttlike.server.tl.baselib.serialize.player.PlayerBlobDb;

import java.util.*;

/**
 * 帮派乱斗模块
 *
 * <AUTHOR> - [Created on 2023/7/18 10:08]
 */
public class PlayerLeagueMeleeModel extends PlayerModel implements PlayerEventHandler {

    /**
     * 剩余体力值
     */
    private int remainPower = -1;

    /**
     * 总花费体力
     */
    private int allCostPower;

    /**
     * 重生时间
     */
    private long rebirthTime = -1;

    /**
     * 原地复活次数-付费复活
     */
    private int rebirthPayCount;

    /**
     * 当前进行运送任务的岛屿ID -1代表没有任务
     */
    private int curShippingIslandId = -1;

    /**
     * 运送任务状态
     * 1.已领取
     * 2.已采集
     */
    private int shippingStatus;
    /**
     * 完成运送任务次数
     */
    private int shippingNum;

    /**
     * 连胜次数
     */
    private int winingStreak;

    /**
     * 个人战场记录
     */
    private List<MeleePlayerRecord> recordList = new ArrayList<>();

    /**
     * 今日是否已经推送荣誉弹窗
     */
    private boolean todaySendHonor;

    /**
     * buff
     * key:buffId  value:失效时间
     */
    private Map<Integer, Long> buffIdMap = new HashMap<>();

    /**
     * 前端标记，一天一清
     */
    private boolean clientSign;

    public PlayerLeagueMeleeModel(Player player) {
        super(player);
    }

    @Override
    protected void loadData(PlayerBlobDb playerBlob) {
        LeagueMeleePlayerModelDb meleePlayerModelDb = playerBlob.getMeleePlayerModelDb();
        if (Objects.nonNull(meleePlayerModelDb)) {
            this.remainPower = meleePlayerModelDb.getRemainPower();
            this.allCostPower = meleePlayerModelDb.getAllCostPower();
            this.rebirthTime = meleePlayerModelDb.getRebirthTime();
            this.rebirthPayCount = meleePlayerModelDb.getRebirthPayCount();
            this.curShippingIslandId = meleePlayerModelDb.getCurShippingIslandId();
            this.shippingStatus = meleePlayerModelDb.getShippingStatus();
            this.shippingNum = meleePlayerModelDb.getShippingNum();
            this.winingStreak = meleePlayerModelDb.getWiningStreak();
            this.recordList = meleePlayerModelDb.getRecordList();
            this.todaySendHonor = meleePlayerModelDb.isTodaySendHonor();
            this.buffIdMap.putAll(meleePlayerModelDb.getBuffIdMap());
            this.clientSign = meleePlayerModelDb.isClientSign();
        }
    }

    @Override
    protected void saveData(PlayerBlobDb playerBlob) {
        LeagueMeleePlayerModelDb db = new LeagueMeleePlayerModelDb();
        db.setRemainPower(remainPower);
        db.setAllCostPower(allCostPower);
        db.setRebirthTime(rebirthTime);
        db.setRebirthPayCount(rebirthPayCount);
        db.setCurShippingIslandId(curShippingIslandId);
        db.setShippingStatus(shippingStatus);
        db.setShippingNum(shippingNum);
        db.setWiningStreak(winingStreak);
        db.setRecordList(recordList);
        db.setTodaySendHonor(todaySendHonor);
        db.getBuffIdMap().putAll(buffIdMap);
        db.setClientSign(clientSign);
        playerBlob.setMeleePlayerModelDb(db);
    }

    /**
     * 结束并清空
     */
    public void endAndClear() {
        this.remainPower = -1;
        this.rebirthTime = -1;
        this.rebirthPayCount = 0;
        this.curShippingIslandId = -1;
        this.shippingStatus = 0;
        this.shippingNum = 0;
        this.winingStreak = 0;
        this.recordList.clear();
        this.buffIdMap.clear();
    }

    /**
     * 添加个人战报
     *
     * @param record 战报记录
     */
    public void addRecord(MeleePlayerRecord record) {
        MeleeConstant constant = LeagueMeleeService.getConstant();
        recordList.add(record);
        int exceedCount = recordList.size() - constant.personalReportNum;
        if (exceedCount > 0) {
            List<MeleePlayerRecord> removeRecordList = recordList.subList(constant.personalReportNum, recordList.size());
            removeRecordList.clear();
        }
    }

    public PbLeagueMelee.MeleeShipping genShippingPb() {
        return PbLeagueMelee.MeleeShipping.newBuilder()
                .setIslandId(curShippingIslandId)
                .setStatus(shippingStatus).build();
    }

    @Override
    public PlayerEventType[] getEventTypes() {
        return new PlayerEventType[]{
                PlayerEventType.login,
                PlayerEventType.logout,
                PlayerEventType.day5Refresh,
                PlayerEventType.enterScene,
        };
    }

    @Override
    public void handle(PlayerEvent event) {
        switch (event.getEventType()) {
            case enterScene:{
                if (LeagueManager.isNotJoinLeague(getPlayerId())) {
                    break;
                }

                LeagueMeleeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueMelee);
                LeagueMeleeBattleInfo battleInfo = globalData.getBattleInfo();
                long leagueId = LeagueManager.getLeagueId(getPlayerId());

                MeleeGroupInfo groupInfo = battleInfo.getGroupInfoByLeagueId(leagueId);
                if (Objects.isNull(groupInfo)) {
                    break;
                }

                if(!groupInfo.getPlayerInMap().containsKey(leagueId)){
                    break;
                }

                PbScene.SceneType type = event.getParam(0);
                if (type == PbScene.SceneType.leagueMelee || type == PbScene.SceneType.leagueMeleeCity) {
                    if(type == PbScene.SceneType.leagueMelee){
                        //进入场景时，创建一个0,0的MeleePlayerInInfo放进去
                        groupInfo.getPlayerInMap().computeIfAbsent(leagueId, map -> new HashMap<>()).put(getPlayerId(), new MeleePlayerInInfo(getPlayerId()));
                    }
                }else {
                    groupInfo.getPlayerInMap().getOrDefault(leagueId, new HashMap<>()).remove(getPlayerId());
                }

                break;
            }
            case logout: {  //退出据点、地块
                //离线退出地图
                if (LeagueManager.isNotJoinLeague(getPlayerId())) {
                    return;
                }
                long leagueId = LeagueManager.getLeagueId(getPlayerId());
                LeagueMeleeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueMelee);
                LeagueMeleeBattleInfo battleInfo = globalData.getBattleInfo();
                MeleeGroupInfo groupInfo = battleInfo.getGroupInfoByLeagueId(leagueId);
                if (Objects.isNull(groupInfo)) {
                    return;
                }

                if(!groupInfo.getPlayerInMap().containsKey(leagueId)){
                    return;
                }

                groupInfo.getPlayerInMap().get(leagueId).remove(getPlayerId());

                Map<Long, Map<Long, MeleePlayerInInfo>> mapPlayerInfoMap = groupInfo.getPlayerInMap();
                mapPlayerInfoMap.getOrDefault(leagueId, new HashMap<>()).remove(getPlayerId());
                break;
            }
            case login: {
                MeleeConstant constant = LeagueMeleeService.getConstant();
                if(constant == null){
                    return;
                }

                if (ServerConstants.getCurrentTimeLocalDateTime().getDayOfWeek().getValue() != constant.showWeekDay) {
                    return;
                }

                if (LeagueManager.isNotJoinLeague(getPlayerId())) {
                    return;
                }

                long leagueId = LeagueManager.getLeagueId(getPlayer());
                LeagueMeleeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueMelee);
                LeagueMeleeBattleInfo battleInfo = globalData.getBattleInfo();
                if (!todaySendHonor) {
                    HonorPanel honorPanel = battleInfo.getHonorPanel();
                    if (Objects.nonNull(honorPanel)) {
                        List<MeleeLeagueHonorRankNode> lastTHonorRankList = battleInfo.getLastTHonorRankList();
                        int rank = -1;
                        for (int i = 0; i < lastTHonorRankList.size(); i++) {
                            MeleeLeagueHonorRankNode meleeLeagueHonorRankNode = lastTHonorRankList.get(i);
                            if (meleeLeagueHonorRankNode.getLeagueId() == leagueId) {
                                rank = i + 1;
                                break;
                            }
                        }
                        PbLeagueMelee.MeleeHonorPanel meleeHonorPanel = LeagueMeleeHelper.genPbMeleeHonorPanel(honorPanel, rank);
                        getPlayer().send(PtCode.LEAGUE_MELEE_HONOR_PANEL_NOTIFY, PbProtocol.LeagueMeleeHonorPanelNotify.newBuilder()
                                .setHonorPanel(meleeHonorPanel).build());
                    }
                    this.todaySendHonor = true;
                }
                break;
            }
            case day5Refresh: {
                this.todaySendHonor = false;
                this.clientSign = false;
                endAndClear();
                break;
            }
        }
    }

    /**
     * 扣除体力值
     *
     * @param costPower 体力值
     */
    public void costPower(int costPower) {
        this.remainPower = Math.max(remainPower - costPower, 0);
        this.allCostPower += costPower;
        notifyPowerChange();
    }

    /**
     * 检查复活时间
     */
    public void checkRebirth() {
        //死亡时间加复活时间
        if (remainPower <= 0 && ServerConstants.getCurrentTimeMillis() >= rebirthTime) {
            this.remainPower = LeagueMeleeService.getConstant().beginEnergy;
            this.rebirthTime = -1;
            //
            notifyPowerChange();
        }
    }

    public void notifyPowerChange() {
        PbProtocol.LeagueMeleePowerChangeNotify.Builder notify = PbProtocol.LeagueMeleePowerChangeNotify.newBuilder();
        notify.setRemainPower(remainPower);
//        notify.setAllCostPower(allCostPower);
        notify.setRebirthTime(rebirthTime);
        getPlayer().send(PtCode.LEAGUE_MELEE_POWER_CHANGE_NOTIFY, notify.build());
    }

    public int getRemainPower() {
        return remainPower;
    }

    public void setRemainPower(int remainPower) {
        this.remainPower = remainPower;
    }

    public int getCurShippingIslandId() {
        return curShippingIslandId;
    }

    public void setCurShippingIslandId(int curShippingIslandId) {
        this.curShippingIslandId = curShippingIslandId;
    }

    public int getShippingStatus() {
        return shippingStatus;
    }

    public void setShippingStatus(int shippingStatus) {
        this.shippingStatus = shippingStatus;
    }

    public int getShippingNum() {
        return shippingNum;
    }

    public void addShippingNum(int addNum) {
        this.shippingNum += addNum;
    }

    public void setShippingNum(int shippingNum) {
        this.shippingNum = shippingNum;
    }

    public int getWiningStreak() {
        return winingStreak;
    }

    public void setWiningStreak(int winingStreak) {
        this.winingStreak = winingStreak;
    }

    public List<MeleePlayerRecord> getRecordList() {
        return recordList;
    }

    public long getRebirthTime() {
        return rebirthTime;
    }

    public void setRebirthTime(long rebirthTime) {
        this.rebirthTime = rebirthTime;
    }

    public void setRecordList(List<MeleePlayerRecord> recordList) {
        this.recordList = recordList;
    }

    public int getRebirthPayCount() {
        return rebirthPayCount;
    }

    public void setRebirthPayCount(int rebirthPayCount) {
        this.rebirthPayCount = rebirthPayCount;
    }

    public Map<Integer, Long> getBuffIdMap() {
        return buffIdMap;
    }

    public void setBuffIdMap(Map<Integer, Long> buffIdMap) {
        this.buffIdMap = buffIdMap;
    }

    public boolean isTodaySendHonor() {
        return todaySendHonor;
    }

    public void setTodaySendHonor(boolean todaySendHonor) {
        this.todaySendHonor = todaySendHonor;
    }

    public boolean isClientSign() {
        return clientSign;
    }

    public void setClientSign(boolean clientSign) {
        this.clientSign = clientSign;
    }

    public int getAllCostPower() {
        return allCostPower;
    }

    public void setAllCostPower(int allCostPower) {
        this.allCostPower = allCostPower;
    }
}

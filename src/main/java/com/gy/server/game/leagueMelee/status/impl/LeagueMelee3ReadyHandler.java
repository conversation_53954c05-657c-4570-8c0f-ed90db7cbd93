package com.gy.server.game.leagueMelee.status.impl;

import com.gy.server.core.battleRule.base.IBattleStatusDeal;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.leagueMelee.LeagueMeleeHelper;
import com.gy.server.game.leagueMelee.LeagueMeleeService;
import com.gy.server.game.leagueMelee.status.LeagueMeleeBattleInfo;
import com.gy.server.game.leagueMelee.status.MeleeNodeType;
import com.gy.server.game.leagueMelee.template.MeleeAreaTemplate;
import com.gy.server.game.leagueMelee.template.MeleeConstant;
import com.gy.server.game.leagueMelee.template.MeleePointTemplate;
import com.gy.server.game.leagueMelee.template.MeleeSeasonTemplate;
import com.gy.server.game.secretRealm.SecretRealmGlobalData;
import com.gy.server.game.secretRealm.bean.SecretRealmMapInfo;
import com.gy.server.game.secretRealm.bean.SecretRealmRankInfo;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.serialize.leagueMelee.MeleeBossAreaInfo;
import com.ttlike.server.tl.baselib.serialize.leagueMelee.MeleeGroupInfo;
import com.ttlike.server.tl.baselib.serialize.leagueMelee.MeleeLeagueHonorRankNode;
import com.ttlike.server.tl.baselib.serialize.leagueMelee.MeleeStatusType;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 准备阶段
 * 对本服帮派统计并进行分组
 *
 * <AUTHOR> - [Created on 2023/7/18 15:43]
 */
public class LeagueMelee3ReadyHandler implements IBattleStatusDeal<LeagueMeleeBattleInfo> {

    private long startTime;

    @Override
    public void init(LeagueMeleeBattleInfo battleInfo) {

    }

    @Override
    public void deal(LeagueMeleeBattleInfo battleInfo) {
        this.startTime = battleInfo.getStatusStartTime(getMeleeNodeType());

        MeleeConstant constant = LeagueMeleeService.getConstant();
        List<League> leagues = LeagueManager.getLeagues();
        //假如服务器没有帮派 直接结束
        if (leagues.isEmpty()) {
            return;
        }

        //没有赛季配置，结束
        if(LeagueMeleeService.getSeasonMap().isEmpty()){
            return;
        }

        Map<Integer, MeleeGroupInfo> groupInfoMap = battleInfo.getGroupInfoMap();
        MeleeGroupInfo groupTInfo = groupInfoMap.computeIfAbsent(LeagueMeleeService.GROUP_T, list1 -> new MeleeGroupInfo());
        MeleeGroupInfo groupDInfo = groupInfoMap.computeIfAbsent(LeagueMeleeService.GROUP_D, list1 -> new MeleeGroupInfo());

        int activeTimeHour = constant.activeTimeHour;
        //第一次开启
        int openNum = battleInfo.getOpenNum();
        if (openNum == 0) {
            //系统自动报名
            Map<Long, MeleeLeagueHonorRankNode> rankMap = new HashMap<>();
            for (League league : leagues) {
                //活跃条件暂时不加 TODO
//                if (!league.isActiveLeague(activeTimeHour, activeTimeHour)) {
//                    continue;
//                }
                MeleeLeagueHonorRankNode rankNodeInfo = new MeleeLeagueHonorRankNode();
                rankNodeInfo.setLeagueId(league.getLeagueId());
                rankNodeInfo.setActiveFightPower(league.getActiveFightPower(activeTimeHour));
                rankNodeInfo.setForceValue(league.getForceValue());
                rankMap.put(league.getLeagueId(), rankNodeInfo);

            }

            //小于这么多帮派数就不开启活动 切换到终止
            if (rankMap.size() >= constant.openMinLeagueNum) {
                //根据秘境排名分组
                SecretRealmGlobalData globalData = GlobalDataManager.getData(GlobalDataType.secretRealm);
                Map<Integer, SecretRealmMapInfo> secretRealmMap = new HashMap<>(globalData.getMapInfos());
                //从后解锁秘境开始（序号大的）
                List<Integer> secretRealmIds = new ArrayList<>(secretRealmMap.keySet());
                secretRealmIds.sort((o1, o2) -> o2 - o1);

                int otherNeedNum = 0;
                for (int i = 0; i < secretRealmIds.size(); i++){
                    //根据顺序确定要选择几个 + 上一轮未达到的个数
                    int needNum = (i == 0 ? 3 : i == 1 ? 2 : i <= 3 ? 1 : 0) + otherNeedNum;
                    if(needNum == 0){
                        continue;
                    }

                    SecretRealmMapInfo secretRealmMapInfo = secretRealmMap.get(secretRealmIds.get(i));
                    List<SecretRealmRankInfo> secretRealmRankInfos = secretRealmMapInfo.getRankInfos();
                    secretRealmRankInfos.sort((o1, o2) -> (int) (o2.getRadius() - o2.getRadius()));

                    for (SecretRealmRankInfo secretRealmRankInfo : secretRealmRankInfos) {
                        if(needNum == 0){
                            continue;
                        }

                        long leagueId = secretRealmRankInfo.getLeagueId();
                        //过滤 TODO
                        MeleeLeagueHonorRankNode honorRankNode = rankMap.get(leagueId);
                        if(honorRankNode == null){
                            continue;
                        }

                        groupTInfo.getHonorRankMap().put(leagueId, rankMap.get(leagueId));
                        battleInfo.getGroupIndexMap().put(leagueId, LeagueMeleeService.GROUP_T);
                        rankMap.remove(leagueId);
                        needNum--;
                        otherNeedNum = needNum;
                    }
                }

                //剩余帮会放入地组
                for (MeleeLeagueHonorRankNode rankNode : rankMap.values()) {
                    groupDInfo.getHonorRankMap().put(rankNode.getLeagueId(), rankNode);
                    battleInfo.getGroupIndexMap().put(rankNode.getLeagueId(), LeagueMeleeService.GROUP_D);
                }

                battleInfo.setSeasonId(LeagueMeleeService.getSeasonMap().keySet().stream().min(Integer::compare).get());
                battleInfo.setOpenNum(openNum + 1);
            }
        } else {//非首次开启
            //先剔除已经解散的帮派
            List<MeleeLeagueHonorRankNode> lastGroupTRankList = battleInfo.getLastTHonorRankList();
            lastGroupTRankList = removeLeague(lastGroupTRankList);
            List<MeleeLeagueHonorRankNode> lastGroupDRankList = battleInfo.getLastDHonorRankList();
            lastGroupDRankList = removeLeague(lastGroupDRankList);
            //然后将新增的帮派补入地组
            List<Long> lastTLeagueIdList = lastGroupTRankList.stream().map(MeleeLeagueHonorRankNode::getLeagueId).collect(Collectors.toList());
            List<Long> lastDLeagueIdList = lastGroupDRankList.stream().map(MeleeLeagueHonorRankNode::getLeagueId).collect(Collectors.toList());
            for (League league : leagues) {
                long leagueId = league.getLeagueId();
                if (!lastTLeagueIdList.contains(leagueId) && !lastDLeagueIdList.contains(leagueId)) {
//                    if (league.isActiveLeague(activeMember, activeTimeHour)) {    //活跃条件暂时不加 TODO
                        MeleeLeagueHonorRankNode rankNodeInfo = new MeleeLeagueHonorRankNode();
                        rankNodeInfo.setLeagueId(leagueId);
                        rankNodeInfo.setActiveFightPower(league.getActiveFightPower(constant.activeTimeHour));
                        lastGroupDRankList.add(rankNodeInfo);
//                    }
                }
            }

//            //剔除地组不满足要求的帮派 活跃条件暂时不加 TODO
//            for (MeleeLeagueHonorRankNode rankNodeInfo : new ArrayList<>(lastGroupDRankList)) {
//                League league = LeagueManager.getLeagueById(rankNodeInfo.getLeagueId());
//                if (!league.isActiveLeague(activeMember, activeTimeHour)) {
//                    lastGroupDRankList.remove(rankNodeInfo);
//                }
//            }

            //天组末X名和地组前X名进行互换
            List<MeleeLeagueHonorRankNode> tempListT = new ArrayList<>();
            List<MeleeLeagueHonorRankNode> tempListD = new ArrayList<>();

            int changeNum = constant.changeRankNum;
            int tNumber = constant.groupTNumber;
            tempListT.addAll(lastGroupTRankList.subList(0, Math.max((Math.min(lastGroupTRankList.size(), tNumber - changeNum)), 0)));
            if(lastTLeagueIdList.size() > tNumber - changeNum){
                tempListD.addAll(lastGroupTRankList.subList(tNumber - changeNum, lastGroupTRankList.size()));
            }
            tempListT.addAll(lastGroupDRankList.subList(0, Math.max(Math.min(lastGroupDRankList.size(), changeNum), 0)));
            if(lastGroupDRankList.size() > changeNum){
                tempListD.addAll(lastGroupDRankList.subList(changeNum, lastGroupDRankList.size()));
            }

            //天组空末位用地组前N名替补
            int tSize = lastGroupTRankList.size();
            if (tSize < constant.groupTNumber && !tempListD.isEmpty()) {
                int addNum = Math.min(lastGroupDRankList.size(), constant.groupTNumber - tSize);
                for (int i = 0; i < addNum; i++) {
                    if(!lastGroupDRankList.isEmpty()){
                        MeleeLeagueHonorRankNode remove = lastGroupDRankList.remove(0);
                        lastGroupTRankList.add(remove);
                    }
                }
            }

            for (MeleeLeagueHonorRankNode meleeLeagueHonorRankNode : tempListT) {
                League league = LeagueManager.getLeagueById(meleeLeagueHonorRankNode.getLeagueId());
                meleeLeagueHonorRankNode.setActiveFightPower(league.getActiveFightPower(constant.activeTimeHour));
                groupTInfo.getHonorRankMap().put(meleeLeagueHonorRankNode.getLeagueId(), meleeLeagueHonorRankNode);
                battleInfo.getGroupIndexMap().put(league.getLeagueId(), LeagueMeleeService.GROUP_T);
            }

            for (MeleeLeagueHonorRankNode meleeLeagueHonorRankNode : tempListD) {
                League league = LeagueManager.getLeagueById(meleeLeagueHonorRankNode.getLeagueId());
                meleeLeagueHonorRankNode.setActiveFightPower(league.getActiveFightPower(constant.activeTimeHour));
                groupDInfo.getHonorRankMap().put(meleeLeagueHonorRankNode.getLeagueId(), meleeLeagueHonorRankNode);
                battleInfo.getGroupIndexMap().put(league.getLeagueId(), LeagueMeleeService.GROUP_D);
            }

            lastGroupTRankList.clear();
            lastGroupDRankList.clear();

            MeleeSeasonTemplate seasonTemplate = LeagueMeleeService.getSeasonMap().get(battleInfo.getSeasonId());
            if(seasonTemplate == null){
                seasonTemplate = LeagueMeleeService.getSeasonMap().values().stream().min(Comparator.comparingInt(o -> o.id)).get();
            }

            battleInfo.setSeasonId(seasonTemplate.nextSeasonId);
            battleInfo.setOpenNum(openNum + 1);
        }

        //清空荣誉弹窗信息
        battleInfo.setHonorPanel(null);
        initBossAreaInfo(battleInfo);
    }

    private List<MeleeLeagueHonorRankNode> removeLeague(List<MeleeLeagueHonorRankNode> lastGroupRankList) {
        List<MeleeLeagueHonorRankNode> list = new ArrayList<>();
        for (MeleeLeagueHonorRankNode rankNodeInfo : lastGroupRankList) {
            League league = LeagueManager.getLeagueById(rankNodeInfo.getLeagueId());
            //已经解散
            if (Objects.isNull(league)) {

            }else {
                list.add(rankNodeInfo);
            }
        }
        return list;
    }

    /**
     * 初始化boss区信息
     * @param battleInfo
     */
    private void initBossAreaInfo(LeagueMeleeBattleInfo battleInfo){
        Map<Integer, MeleeBossAreaInfo> bossAreaInfoMap = new HashMap<>();
        for (MeleeAreaTemplate bean : LeagueMeleeService.getAreaMap().values()) {
            if(bean.areaType == 2){
                MeleeBossAreaInfo bossAreaInfo = LeagueMeleeHelper.initBoss(bean);
                bossAreaInfoMap.put(bossAreaInfo.getAreaId(), bossAreaInfo);
            }
        }

        for (MeleePointTemplate bean : LeagueMeleeService.getPointMapBySeasonId(battleInfo.getSeasonId()).values()) {
            for (int areaId : bean.pointAreaIds) {
                MeleeAreaTemplate areaTemplate = LeagueMeleeService.getAreaMap().get(areaId);
                if(areaTemplate.areaType != 2){
                    continue;
                }

                MeleeBossAreaInfo bossAreaInfo = bossAreaInfoMap.get(areaId).copy();
                bossAreaInfo.setIslandId(bean.id);
                for (MeleeGroupInfo groupInfo : battleInfo.getGroupInfoMap().values()) {
                    groupInfo.getBossAreaInfoMap().put(bossAreaInfo.getKey(), bossAreaInfo);
                }
            }
        }
    }

    @Override
    public boolean finish(LeagueMeleeBattleInfo battleInfo) {
        return true;
    }

    @Override
    public int nowStatus() {
        return MeleeStatusType.ready.getType();
    }

    /**
     * 没数据的话下个状态就终止
     *
     * @param battleInfo 业务数据
     */
    @Override
    public int nextStatus(LeagueMeleeBattleInfo battleInfo) {
        Map<Integer, MeleeGroupInfo> groupInfoMap = battleInfo.getGroupInfoMap();
        if(groupInfoMap.isEmpty()){
            return MeleeStatusType.abort.getType();
        }

        for (MeleeGroupInfo value : groupInfoMap.values()) {
            if(!value.getHonorRankMap().isEmpty()){
                return MeleeStatusType.beforeLineup.getType();
            }
        }

        return MeleeStatusType.abort.getType();
    }

    @Override
    public long getStatusDurationTime(LeagueMeleeBattleInfo battleInfo) {
        return LeagueMeleeService.getConstant().onTimeMap.get(getMeleeNodeType()).getRight() * DateTimeUtil.MinutesOfHour;
    }

    @Override
    public long getStatusStartTime() {
        return startTime;
    }

    public MeleeNodeType getMeleeNodeType(){
        return MeleeNodeType.ready;
    }

}

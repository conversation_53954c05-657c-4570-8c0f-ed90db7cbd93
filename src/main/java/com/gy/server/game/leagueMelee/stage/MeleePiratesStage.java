package com.gy.server.game.leagueMelee.stage;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.battleCollect.BattleCollectService;
import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.combat.StageType;
import com.gy.server.game.combat.unit.TeamUnit;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.leagueMelee.LeagueMeleeGlobalData;
import com.gy.server.game.leagueMelee.LeagueMeleeHelper;
import com.gy.server.game.leagueMelee.LeagueMeleeService;
import com.gy.server.game.leagueMelee.PlayerLeagueMeleeModel;
import com.gy.server.game.leagueMelee.status.LeagueMeleeBattleInfo;
import com.gy.server.game.leagueMelee.template.MeleeMonsterTemplate;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.world.World;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.serialize.leagueMelee.MeleeGroupInfo;
import com.ttlike.server.tl.baselib.serialize.leagueMelee.MeleePiratesInfo;

import java.util.List;
import java.util.Map;

/**
 * 帮派乱斗-野怪战斗
 *
 * <AUTHOR> - [Created on 2023/8/1 20:19]
 */
public class MeleePiratesStage extends AbstractStage {

    private final Player player;
    private final long uid;
    private final MeleeMonsterTemplate monsterTemplate;
    private final Map<Integer, Long> buffIdMap;

    LineupType lineupType = LineupType.leagueMelee;
    StageType stageType = StageType.leagueMelee;

    public MeleePiratesStage(Player player, long uid, MeleeMonsterTemplate monsterTemplate, Map<Integer, Long> buffIdMap) {
        this.player = player;
        this.uid = uid;
        this.monsterTemplate = monsterTemplate;
        this.buffIdMap = buffIdMap;
    }

    @Override
    public void init() {
        List<TeamUnit> atkTeamUnitList = player.getLineupModel().createTeamUnits(this, stageType, lineupType);
        //处理buff
        if(!buffIdMap.isEmpty()){
            for (TeamUnit teamUnit : atkTeamUnitList) {
                buffIdMap.forEach((key, value) -> {
                    if(ServerConstants.getCurrentTimeMillis() < value){
                        teamUnit.addUnitBuff(key, 1);
                    }
                });
            }
        }

        List<TeamUnit> defTeamUnitList = BattleCollectService.createTeamUnits(this, World.getWorldLevel(), monsterTemplate.battleCollectId);
        init(monsterTemplate.battleCollectId, stageType, atkTeamUnitList, defTeamUnitList, lineupType);
    }

    @Override
    public void afterFinish() {
        PbProtocol.CombatSettlementNotify.Builder rst = genCombatSettlement();
        PbProtocol.MeleePiratesSettlement.Builder meleePiratesSettlement = PbProtocol.MeleePiratesSettlement.newBuilder();
        LeagueMeleeGlobalData meleeGlobalData = GlobalDataManager.getData(GlobalDataType.leagueMelee);
        LeagueMeleeBattleInfo battleInfo = meleeGlobalData.getBattleInfo();
        MeleeGroupInfo groupInfo = battleInfo.getGroupInfoByLeagueId(LeagueManager.getLeagueId(player));
        groupInfo.removeFightingPiratesPlayer(uid, player.getPlayerId());
        Map<Long, MeleePiratesInfo> meleeMonsterInfoMap = groupInfo.getMeleePiratesInfoMap();
        if (meleeMonsterInfoMap.containsKey(uid)) {
            meleePiratesSettlement.setIsExists(true);
            if (isWin) {
                LeagueMeleeHelper.addPiratesHonor(player, monsterTemplate);
                //移除野怪并终止其他人在野怪中的战斗
                meleeMonsterInfoMap.remove(uid);
                LeagueMeleeHelper.stopPiratesFight(groupInfo, uid);

                //增加buff
                PlayerLeagueMeleeModel playerLeagueMeleeModel = player.getModel(PlayerModelEnums.leagueMelee);
                playerLeagueMeleeModel.getBuffIdMap().put(monsterTemplate.commandCount, ServerConstants.getCurrentTimeMillis() + LeagueMeleeService.getConstant().tacticBuffTime * DateTimeUtil.MillisOfSecond);
            }
        } else {
            meleePiratesSettlement.setIsExists(false);
        }
        rst.setMeleePirates(meleePiratesSettlement.build());

        notifyCombatSettlement(player, rst.build());

        player.postEvent(PlayerEventType.leagueMeleeAtk);
    }

    @Override
    public void combatAbort(boolean isStart) {
        LeagueMeleeGlobalData meleeGlobalData = GlobalDataManager.getData(GlobalDataType.leagueMelee);
        LeagueMeleeBattleInfo battleInfo = meleeGlobalData.getBattleInfo();
        MeleeGroupInfo groupInfo = battleInfo.getGroupInfoByLeagueId(LeagueManager.getLeagueId(player));
        groupInfo.removeFightingPiratesPlayer(uid, player.getPlayerId());
    }
}

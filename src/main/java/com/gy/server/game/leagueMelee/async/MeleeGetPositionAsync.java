package com.gy.server.game.leagueMelee.async;

import com.gy.server.scene.map.bean.SceneMapType;
import com.ttlike.server.tl.baselib.ServerType;
import com.ttlike.server.tl.baselib.TLBase;
import com.ttlike.server.tl.baselib.rpc.CommandRequests;
import com.ttlike.server.tl.baselib.rpc.TLMessageCallbackTask;

import java.util.ArrayList;
import java.util.List;

/**
 * 场景服获取坐标
 *
 * <AUTHOR> - [Created on 2023/7/24 14:34]
 */
public class MeleeGetPositionAsync {


    public static void execute(long playerId, TLMessageCallbackTask callbackTask) {
        List<Long> playerIdList = new ArrayList<>();
        playerIdList.add(playerId);
        execute(playerIdList, callbackTask);
    }

    public static void execute(List<Long> playerIdList, TLMessageCallbackTask callbackTask) {
        int sceneId = SceneMapType.leagueMelee.getRouteSceneId(-1);
        TLBase.getInstance().getRpcUtil()
                .sendToNodeWithCallBack(callbackTask, ServerType.SCENE, sceneId,
                        CommandRequests.newServerCommandRequest("SceneOperationCommandService.getPosition"), playerIdList);
    }


}

package com.gy.server.game.leagueMelee;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.attribute.AttributeKey;
import com.gy.server.game.battleCollect.BattleCollectService;
import com.gy.server.game.battleCollect.template.BattleCollectTemplate;
import com.gy.server.game.combat.CombatManager;
import com.gy.server.game.combat.Stage;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.league.LeagueModelEnums;
import com.gy.server.game.leagueMelee.stage.MeleeBossStage;
import com.gy.server.game.leagueMelee.stage.MeleePiratesStage;
import com.gy.server.game.leagueMelee.status.LeagueMeleeBattleInfo;
import com.gy.server.game.leagueMelee.status.MeleeNodeType;
import com.gy.server.game.leagueMelee.template.*;
import com.gy.server.game.monster.MonsterService;
import com.gy.server.game.monster.MonsterTemplate;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.game.role.PlayerRoleService;
import com.gy.server.packet.PbCommons;
import com.gy.server.packet.PbLeague;
import com.gy.server.packet.PbLeagueMelee;
import com.gy.server.packet.PbProtocol;
import com.ttlike.server.tl.baselib.serialize.league.LeagueUserDataDb;
import com.ttlike.server.tl.baselib.serialize.leagueMelee.*;
import com.ttlike.server.tl.baselib.thread.ThreadPool;
import org.apache.commons.lang3.tuple.Pair;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.stream.Collectors;

/**
 * 帮派乱斗帮助类
 *
 * <AUTHOR> - [Created on 2023/7/18 16:11]
 */
public class LeagueMeleeHelper {

    public static PbLeagueMelee.LeagueRankNode genLeagueRankPb(MeleeLeagueAreaRankNode rankNode, int rank) {
        League league = LeagueManager.getLeagueById(rankNode.getLeagueId());
        PbLeagueMelee.LeagueRankNode.Builder rankBuilder = PbLeagueMelee.LeagueRankNode.newBuilder()
                .setLeagueId(rankNode.getLeagueId())
                .setName(league.getName())
                .setScore(rankNode.getScore())
                .setRank(rank);
        return rankBuilder.build();
    }

    public static PbLeagueMelee.LeagueRankNode genLeagueRankPb(MeleeLeagueHonorRankNode rankNode, int rank) {
        League league = LeagueManager.getLeagueById(rankNode.getLeagueId());
        PbLeagueMelee.LeagueRankNode.Builder rankBuilder = PbLeagueMelee.LeagueRankNode.newBuilder()
                .setLeagueId(rankNode.getLeagueId())
                .setName(league.getName())
                .setScore(rankNode.getHonorValue())
                .setRank(rank);
        return rankBuilder.build();
    }

    public static PbLeagueMelee.PlayerRankNode genPlayerRankPb(MeleePlayerAreaRankNode rankNode, int rank) {
        PbLeagueMelee.PlayerRankNode.Builder rankBuilder = PbLeagueMelee.PlayerRankNode.newBuilder()
                .setPlayerId(rankNode.getPlayerId())
                .setScore(rankNode.getScore())
                .setName(PlayerManager.getMiniPlayer(rankNode.getPlayerId()).getName())
                .setRank(rank);
        return rankBuilder.build();
    }

    public static PbLeagueMelee.PlayerRankNode genPlayerRankPb(MeleePlayerHonorRankNode rankNode, int rank) {
        PbLeagueMelee.PlayerRankNode.Builder rankBuilder = PbLeagueMelee.PlayerRankNode.newBuilder()
                .setPlayerId(rankNode.getPlayerId())
                .setScore(rankNode.getHonorValue())
                .setName(PlayerManager.getMiniPlayer(rankNode.getPlayerId()).getName())
                .setRank(rank);
        return rankBuilder.build();
    }

    public static List<PbLeagueMelee.MeleeIslandAreaRankInfo> genMeleeIslandAreaRankInfoList(Player player, MeleeGroupInfo groupInfo, int islandId, List<Integer> areaIds){
        List<PbLeagueMelee.MeleeIslandAreaRankInfo> rankInfos = new ArrayList<>();
        MeleeIslandRankInfo islandRankInfo = groupInfo.getIslandRankInfoMap().get(islandId);

        long leagueId = LeagueManager.getLeagueId(player.getPlayerId());

        MeleePointTemplate pointTemplate = LeagueMeleeService.getPointMap().get(islandId);
        for (int areaId : pointTemplate.pointAreaIds) {
            if(!areaIds.contains(areaId)){
                continue;
            }

            PbLeagueMelee.MeleeIslandAreaRankInfo.Builder islandAreaRankInfo = PbLeagueMelee.MeleeIslandAreaRankInfo.newBuilder();
            islandAreaRankInfo.setAreaId(areaId);

            if(islandRankInfo != null){
                MeleeIslandAreaRankInfo areaRankInfo = islandRankInfo.getAreaRankInfoMap().get(areaId);
                if(areaRankInfo != null){
                    List<MeleeLeagueAreaRankNode> leagueAreaRankNodes = areaRankInfo.getAreaLeagueRankList();
                    for (int i = 0; i < Math.min(LeagueMeleeService.getConstant().countRankNum, leagueAreaRankNodes.size()); i++) {
                        PbLeagueMelee.LeagueRankNode rankNode = LeagueMeleeHelper.genLeagueRankPb(leagueAreaRankNodes.get(i), i + 1);
                        islandAreaRankInfo.addLeagueRank(rankNode);
                        if(rankNode.getLeagueId() == leagueId){
                            islandAreaRankInfo.setMyLeagueRank(rankNode);
                        }
                    }

                    List<MeleePlayerAreaRankNode> playerAreaRankNodes = areaRankInfo.getAreaPlayerRankList();
                    for (int i = 0; i < Math.min(LeagueMeleeService.getConstant().countRankNum, playerAreaRankNodes.size()); i++) {
                        PbLeagueMelee.PlayerRankNode rankNode = LeagueMeleeHelper.genPlayerRankPb(playerAreaRankNodes.get(i), i + 1);
                        islandAreaRankInfo.addPlayerRank(rankNode);
                        if(rankNode.getPlayerId() == player.getPlayerId()){
                            islandAreaRankInfo.setMyPlayerRank(rankNode);
                        }
                    }
                }
            }

            String key = islandId + "" + areaId;
            if(groupInfo.getBossAreaInfoMap().containsKey(key)){
                MeleeBossAreaInfo bossAreaInfo = groupInfo.getBossAreaInfoMap().get(key);
                PbLeagueMelee.MeleeBossInfo.Builder bossInfobuilder = PbLeagueMelee.MeleeBossInfo.newBuilder();
                bossInfobuilder.putAllHpMap(bossAreaInfo.getBossHpMap());
                int defNum = groupInfo.getPlayerInMap().containsKey(bossAreaInfo.getLeagueId()) ? groupInfo.getPlayerInMap().get(bossAreaInfo.getLeagueId()).size() : 0;
                int atkNum = 0;
                for (Map.Entry<Long, Map<Long, MeleePlayerInInfo>> inEntry : groupInfo.getPlayerInMap().entrySet()) {
                    if(inEntry.getKey() == bossAreaInfo.getLeagueId()){
                        continue;
                    }
                    atkNum = atkNum + inEntry.getValue().size();
                }
                bossInfobuilder.setAtkNum(atkNum);
                bossInfobuilder.setDefNum(defNum);
                islandAreaRankInfo.setBossInfo(bossInfobuilder.build());
            }
            rankInfos.add(islandAreaRankInfo.build());
        }

        return rankInfos;
    }

    /**
     * 区域排行榜推送
     * @param groupInfo
     * @param islandId
     * @param areaId
     */
    public static void leagueMeleeAreaRankNotify(MeleeGroupInfo groupInfo, int islandId, int areaId){
        PbLeagueMelee.MeleeIslandAreaRankInfo.Builder areaRankInfoBuilder = PbLeagueMelee.MeleeIslandAreaRankInfo.newBuilder();
        areaRankInfoBuilder.setAreaId(areaId);
        MeleeIslandRankInfo islandRankInfo = groupInfo.getIslandRankInfoMap().get(islandId);

        if(islandRankInfo != null){
            MeleeIslandAreaRankInfo areaRankInfo = islandRankInfo.getAreaRankInfoMap().get(areaId);
            if(areaRankInfo != null){
                List<MeleeLeagueAreaRankNode> leagueAreaRankNodes = areaRankInfo.getAreaLeagueRankList();
                for (int i = 0; i < Math.min(LeagueMeleeService.getConstant().countRankNum, leagueAreaRankNodes.size()); i++) {
                    PbLeagueMelee.LeagueRankNode rankNode = LeagueMeleeHelper.genLeagueRankPb(leagueAreaRankNodes.get(i), i + 1);
                    areaRankInfoBuilder.addLeagueRank(rankNode);
                }

                List<MeleePlayerAreaRankNode> playerAreaRankNodes = areaRankInfo.getAreaPlayerRankList();
                for (int i = 0; i < Math.min(LeagueMeleeService.getConstant().countRankNum, playerAreaRankNodes.size()); i++) {
                    PbLeagueMelee.PlayerRankNode rankNode = LeagueMeleeHelper.genPlayerRankPb(playerAreaRankNodes.get(i), i + 1);
                    areaRankInfoBuilder.addPlayerRank(rankNode);
                }
            }
        }

        String key = islandId + "" + areaId;
        if(groupInfo.getBossAreaInfoMap().containsKey(key)){
            MeleeBossAreaInfo bossAreaInfo = groupInfo.getBossAreaInfoMap().get(key);
            PbLeagueMelee.MeleeBossInfo.Builder bossInfobuilder = PbLeagueMelee.MeleeBossInfo.newBuilder();
            bossInfobuilder.putAllHpMap(bossAreaInfo.getBossHpMap());
            int defNum = groupInfo.getPlayerInMap().containsKey(bossAreaInfo.getLeagueId()) ? groupInfo.getPlayerInMap().get(bossAreaInfo.getLeagueId()).size() : 0;
            int atkNum = 0;
            for (Map.Entry<Long, Map<Long, MeleePlayerInInfo>> inEntry : groupInfo.getPlayerInMap().entrySet()) {
                if(inEntry.getKey() == bossAreaInfo.getLeagueId()){
                    continue;
                }
                atkNum = atkNum + inEntry.getValue().size();
            }
            bossInfobuilder.setAtkNum(atkNum);
            bossInfobuilder.setDefNum(defNum);
            areaRankInfoBuilder.setBossInfo(bossInfobuilder.build());
        }

        MeleeIslandAreaRankInfo areaRankInfo = islandRankInfo.getAreaRankInfoMap().get(areaId);
        List<MeleeLeagueAreaRankNode> leagueAreaRankNodes = areaRankInfo == null ? new ArrayList<>() : areaRankInfo.getAreaLeagueRankList();
        List<MeleePlayerAreaRankNode> playerAreaRankNodes = areaRankInfo == null ? new ArrayList<>() : areaRankInfo.getAreaPlayerRankList();

        for (Map.Entry<Long, Map<Long, MeleePlayerInInfo>> entry : groupInfo.getPlayerInMap().entrySet()) {
            if(areaRankInfo != null){
                MeleeLeagueAreaRankNode leagueAreaRankNode = areaRankInfo.getLeagueAreaRankMap().get(entry.getKey());
                if(leagueAreaRankNode != null){
                    int rank = leagueAreaRankNodes.indexOf(leagueAreaRankNode) + 1;
                    areaRankInfoBuilder.setMyLeagueRank(LeagueMeleeHelper.genLeagueRankPb(leagueAreaRankNode, rank));
                }else {
                    areaRankInfoBuilder.setMyLeagueRank(PbLeagueMelee.LeagueRankNode.newBuilder());
                }
            }

            for (MeleePlayerInInfo bean : entry.getValue().values()) {
                if(bean.getIslandId() == islandId){
                    if(areaRankInfo != null){
                        MeleePlayerAreaRankNode playerAreaRankNode = areaRankInfo.getPlayerAreaRankMap().get(bean.getPlayerId());
                        if(playerAreaRankNode != null){
                            int rank = playerAreaRankNodes.indexOf(playerAreaRankNode) + 1;
                            areaRankInfoBuilder.setMyPlayerRank(LeagueMeleeHelper.genPlayerRankPb(playerAreaRankNode, rank));
                        }else {
                            areaRankInfoBuilder.setMyPlayerRank(PbLeagueMelee.PlayerRankNode.newBuilder().build());
                        }
                    }

                    Player onlinePlayer = PlayerManager.getOnlinePlayer(bean.getPlayerId());
                    if(onlinePlayer != null){
                        PbProtocol.LeagueMeleeAreaRankNotify.Builder notify = PbProtocol.LeagueMeleeAreaRankNotify.newBuilder();
                        notify.setAreaRankInfo(areaRankInfoBuilder.build());
                        onlinePlayer.send(PtCode.LEAGUE_MELEE_AREA_RANK_NOTIFY, notify.build());
                    }
                }
            }
        }
    }

    public static List<PbLeagueMelee.MeleeMonsterInfo> genMeleeMonsterInfoList(List<MeleePiratesInfo> monsterInfos){
        List<PbLeagueMelee.MeleeMonsterInfo> list = new ArrayList<>();
        if(monsterInfos != null){
            monsterInfos.forEach(bean -> list.add(genMeleeMonsterInfo(bean)));
        }
        return list;
    }

    public static PbLeagueMelee.MeleeMonsterInfo genMeleeMonsterInfo(MeleePiratesInfo monsterInfo){
        return PbLeagueMelee.MeleeMonsterInfo.newBuilder().setUid(monsterInfo.getUid())
                    .setTemplateId(monsterInfo.getTemplateId())
                    .setPosition(monsterInfo.getPosition().genPb())
                    .setEndTime(monsterInfo.getEndTime()).build();
    }

    public static PbLeagueMelee.TotalRankInfo genTotalRankInfo(MeleeGroupInfo groupInfo, long playerId, long leagueId){
        PbLeagueMelee.TotalRankInfo.Builder builder = PbLeagueMelee.TotalRankInfo.newBuilder();

        List<MeleeLeagueHonorRankNode> leagueHonorRankNodes = groupInfo.getHonorRankList();
        for(int i = 0; i < Math.min(leagueHonorRankNodes.size(), LeagueMeleeService.getConstant().honorRankNum); i++){
            MeleeLeagueHonorRankNode rankNode = leagueHonorRankNodes.get(i);
            builder.addLeagueRank(genLeagueRankPb(rankNode, i + 1));
        }

        MeleeLeagueHonorRankNode myLeague = groupInfo.getHonorRankMap().get(leagueId);
        if(myLeague != null){
            int indexLeague = leagueHonorRankNodes.indexOf(myLeague);
            builder.setMyLeagueRank(genLeagueRankPb(myLeague, indexLeague + 1));
        }

        List<MeleePlayerHonorRankNode> playerHonorRankNodes = groupInfo.getPlayerHonorRankList();
        for(int i = 0; i < Math.min(playerHonorRankNodes.size(), LeagueMeleeService.getConstant().honorRankNum); i++){
            MeleePlayerHonorRankNode rankNode = playerHonorRankNodes.get(i);
            builder.addPlayerRank(genPlayerRankPb(rankNode, i + 1));
        }

        MeleePlayerHonorRankNode myPlayer = groupInfo.getPlayerHonorRankMap().get(playerId);
        if(myPlayer != null){
            int indexPlayer = playerHonorRankNodes.indexOf(myPlayer);
            builder.setMyPlayerRank(genPlayerRankPb(myPlayer, indexPlayer + 1));
        }

        return builder.build();
    }

    public static List<PbLeagueMelee.PointAreaOpenInfo> genPointAreaOpenInfo(Collection<MeleeIslandAreaOpenInfo> areaOpenInfoList){
        List<PbLeagueMelee.PointAreaOpenInfo> list = new ArrayList<>();
        areaOpenInfoList.forEach(bean -> {
            PbLeagueMelee.PointAreaOpenInfo.Builder builder = PbLeagueMelee.PointAreaOpenInfo.newBuilder();
            long now = ServerConstants.getCurrentTimeMillis();
            builder.setPointId(bean.getIslandId());
            bean.getAreaEndMap().forEach((key, value) -> {
                if(now <= value){
                    builder.putAreaOpen(key, value);
                }
            });

            if(builder.getAreaOpenCount() > 0){
                list.add(builder.build());
            }
        });
        return list;
    }

    /**
     * 终止海盗战斗
     *
     * @param groupInfo 天地组
     * @param uid       海盗ID
     */
    public static void stopPiratesFight(MeleeGroupInfo groupInfo, long uid) {
        //终止其他在海盗中的战斗
        Map<Long, CopyOnWriteArraySet<Long>> fightingPiratesMap = groupInfo.getFightingPiratesMap();
        Set<Long> fightingSet = fightingPiratesMap.getOrDefault(uid, new CopyOnWriteArraySet<>());
        for (Long fightPlayerId : fightingSet) {
            Stage stage = CombatManager.getStageByPid(fightPlayerId);
            if (Objects.nonNull(stage) && stage instanceof MeleePiratesStage) {
                //强制退出
                stage.forceQuit(fightPlayerId);
            } else {
                groupInfo.removeFightingPiratesPlayer(uid, fightPlayerId);
            }
        }
    }

    public static void stopBossFight(MeleeGroupInfo groupInfo, int islandId, int areaId, long playerId) {
        //终止其他在海盗中的战斗
        String key = islandId + "" + areaId;
        List<Long> playerIds = new ArrayList<>(groupInfo.getFightingBossMap().get(key));
        groupInfo.getFightingBossMap().remove(key);

        for (Long fightPlayerId : playerIds) {
            Stage stage = CombatManager.getStageByPid(fightPlayerId);
            if (Objects.nonNull(stage) && stage instanceof MeleeBossStage) {
                //强制退出
                stage.forceQuit(fightPlayerId);
            }
        }
    }

    /**
     * 据点自增长增加荣誉值
     */
    public static void addIslandAuto(long leagueId, int addHonor, int islandId, int pointAddition) {
        addScoreAndHonor(null, leagueId, islandId, 0, 0, getAddPerHonor(addHonor, pointAddition));
    }

    /**
     * 后援区增加积分
     *
     * @param islandId 据点ID
     * @param areaId
     * @param player   玩家
     */
    public static void addBackupScore(int islandId, int areaId, Player player) {    //TODO
        MeleeConstant constant = LeagueMeleeService.getConstant();
        int backupHonor = getAddPerHonor(constant.supportHonor, LeagueManager.getLeagueId(player), islandId);
        int backupScore = getAddPerScore(constant.supportCount);
        addScoreAndHonor(player, LeagueManager.getLeagueId(player), islandId, areaId, backupScore, backupHonor);
    }

    /**
     * 海盗增加荣誉值积分
     *
     * @param player    玩家
     * @param monsterTemplate 海盗模板ID
     */
    public static void addPiratesHonor(Player player, MeleeMonsterTemplate monsterTemplate) {
//        for (Map<Integer, MeleeMonsterTemplate> monsterTemplateMap : LeagueMeleeService.getMonsterMap().values()) {
//            MeleeMonsterTemplate monsterTemplate = monsterTemplateMap.get(monsterId);
//            if(monsterTemplate != null){
//                addScoreAndHonor(player, LeagueManager.getLeagueId(player), 0, 0, 0, getAddPerHonor(monsterTemplate.honor));
//                return;
//            }
//        }
        addScoreAndHonor(player, LeagueManager.getLeagueId(player), 0, 0, 0, getAddPerHonor(monsterTemplate.honor, 0));
    }

    /**
     * 战斗区域增加积分
     *
     * @param islandId 据点ID
     * @param areaTemplate 战斗区域
     * @param player   玩家
     * @param isRobot 对手是否是机器人
     * @return key:积分 value:荣誉值
     */
    public static Pair<Integer, Integer> addFightScore(int islandId, MeleeAreaTemplate areaTemplate, Player player, boolean isRobot) {
        MeleeConstant constant = LeagueMeleeService.getConstant();
        PlayerLeagueMeleeModel playerMeleeModel = player.getModel(PlayerModelEnums.leagueMelee);
        int addHonor = 0;
        int addCount = 0;
        int type = areaTemplate.areaType;
        int winingStreak = Math.min(playerMeleeModel.getWiningStreak(), constant.winCountMax);
        if(isRobot){
            if(winingStreak > 0){
                addHonor = constant.robotHonor;
                addCount = constant.robotPoints;
            }
        }else {
            if (winingStreak > 0) {
                // 胜利积分=胜利保底积分+(连胜场次-1)*单场连胜积分
                addHonor = constant.combatWinHonorMap.get(type) + (winingStreak - 1) * constant.combatContinuousWinHonorMap.get(type);
                addCount = constant.combatWinCountMap.get(type) + (winingStreak - 1) * constant.combatContinuousWinCountMap.get(type);
            } else {
                addHonor = constant.combatLoseHonorMap.get(type);
                addCount = constant.combatLoseCountMap.get(type);
            }
        }


        int addPerScore = getAddPerScore(addCount);
        int addPerHonor = getAddPerHonor(addHonor, LeagueManager.getLeagueId(player), islandId);
        addScoreAndHonor(player, LeagueManager.getLeagueId(player), islandId, areaTemplate.id, addPerScore, addPerHonor);
        return Pair.of(addPerScore, addPerHonor);
    }

    /**
     * boss区增加分数
     * @param playerId
     * @param damage
     * @param islandId
     * @param areaId
     */
    public static void addBossScore(long playerId, long damage, int islandId, int areaId){
        LeagueMeleeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueMelee);
        LeagueMeleeBattleInfo battleInfo = globalData.getBattleInfo();

        long leagueId = LeagueManager.getLeagueId(playerId);
        //据点内排行榜更新
        MeleeGroupInfo groupInfo = battleInfo.getGroupInfoByLeagueId(leagueId);
        MeleeIslandRankInfo islandRankInfo = groupInfo.getIslandRankInfoMap().computeIfAbsent(islandId, bean -> new MeleeIslandRankInfo(islandId));
        MeleeIslandAreaRankInfo islandAreaRankInfo = islandRankInfo.getAreaRankInfoMap().computeIfAbsent(areaId, bean -> new MeleeIslandAreaRankInfo(areaId));

        //增加战斗人员
        islandAreaRankInfo.getBattlePlayerMap().computeIfAbsent(leagueId, map -> new HashSet<>()).add(playerId);

        //据点内联盟排行
        MeleeLeagueAreaRankNode leagueAreaRankNode = islandAreaRankInfo.getLeagueAreaRankMap().computeIfAbsent(leagueId, bean -> new MeleeLeagueAreaRankNode(leagueId));
        leagueAreaRankNode.setScore(leagueAreaRankNode.getScore() + damage);
        long firstLeagueId = islandAreaRankInfo.getAreaLeagueRankList().get(0).getLeagueId();
        if(islandRankInfo.getFirstLeagueIdMap().getOrDefault(areaId, 0L) != firstLeagueId){
            islandRankInfo.getFirstLeagueIdMap().put(areaId, firstLeagueId);
        }
        //据点内个人排行
        MeleePlayerAreaRankNode playerAreaRankNode = islandAreaRankInfo.getPlayerAreaRankMap().computeIfAbsent(playerId, bean -> new MeleePlayerAreaRankNode(playerId));
        playerAreaRankNode.setScore(playerAreaRankNode.getScore() + damage);

        ThreadPool.execute(() -> leagueMeleeAreaRankNotify(groupInfo, islandId, areaId));
    }

    /**
     * 获得加成之后的荣誉值
     *
     * @param addHonor 荣誉值加成
     * @return 加成之后的荣誉值
     */
    public static int getAddPerHonor(int addHonor, int pointAddition) {
        LeagueMeleeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueMelee);
        LeagueMeleeBattleInfo battleInfo = globalData.getBattleInfo();
        return addHonor * battleInfo.getHonorPer() * (100 + pointAddition) / 100;
    }

    public static int getAddPerHonor(int addHonor, long leagueId, int pointId) {
        LeagueMeleeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueMelee);
        LeagueMeleeBattleInfo battleInfo = globalData.getBattleInfo();
        MeleeGroupInfo meleeGroupInfo = battleInfo.getGroupInfoByLeagueId(leagueId);
        MeleeIslandRankInfo islandRankInfo = meleeGroupInfo.getIslandRankInfoMap().get(pointId);
        int pointAddition = 0;
        if(islandRankInfo != null){
            for (Map.Entry<Integer, Long> entry : islandRankInfo.getFirstLeagueIdMap().entrySet()) {
                MeleeAreaTemplate areaTemplate = LeagueMeleeService.getAreaMap().get(entry.getKey());
                if(areaTemplate != null){
                    if(areaTemplate.areaIncome == MeleeIslandAreaBelongIncomeType.honorAddition.getType() && entry.getValue() == leagueId){
                        pointAddition = areaTemplate.areaParameter;
                    }
                }
            }
        }
        return getAddPerHonor(addHonor, pointAddition);
    }

    /**
     * 获得加成之后的积分
     *
     * @param addScore 荣誉值加成
     * @return 加成之后的积分
     */
    public static int getAddPerScore(int addScore) {
        LeagueMeleeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueMelee);
        LeagueMeleeBattleInfo battleInfo = globalData.getBattleInfo();
        return addScore * battleInfo.getCountPer();
    }


    /**
     * 增加区域积分和荣誉值
     *
     * @param player   玩家信息
     * @param islandId 据点ID
     * @param areaId 区域id
     * @param addScore 增加岛屿积分 <=0 不增加
     * @param addHonor 增加荣誉值 <=0 不增加
     */
    private static void addScoreAndHonor(Player player, long leagueId, int islandId, int areaId, long addScore, long addHonor) {
        LeagueMeleeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueMelee);
        LeagueMeleeBattleInfo battleInfo = globalData.getBattleInfo();
        Map<Long, MeleePlayerInfo> playerInfoMap = battleInfo.getPlayerInfoMap();
        Map<Long, MeleeLeagueInfo> leagueInfoMap = battleInfo.getLeagueInfoMap();

        MeleeGroupInfo groupInfo = battleInfo.getGroupInfoByLeagueId(leagueId);

        if(islandId != 0){
            //据点内排行榜更新
            Map<Integer, MeleeIslandRankInfo> islandRankInfoMap = groupInfo.getIslandRankInfoMap();
            MeleeIslandRankInfo meleeIslandRankInfo = islandRankInfoMap.computeIfAbsent(islandId, vo -> new MeleeIslandRankInfo(islandId));
            Map<Integer, MeleeIslandAreaRankInfo> areaRankInfoMap = meleeIslandRankInfo.getAreaRankInfoMap();
            MeleeIslandAreaRankInfo meleeIslandAreaRankInfo = areaRankInfoMap.computeIfAbsent(areaId, vo -> new MeleeIslandAreaRankInfo(areaId));

            //增加战斗人员
            meleeIslandAreaRankInfo.getBattlePlayerMap().computeIfAbsent(leagueId, map -> new HashSet<>()).add(player.getPlayerId());

            // 据点积分增加
            if (addScore > 0) {
                if (Objects.nonNull(player)) {
                    long playerId = player.getPlayerId();
                    MeleePlayerInfo meleePlayerInfo = playerInfoMap.computeIfAbsent(playerId, vo -> new MeleePlayerInfo(playerId));
                    // 据点个人积分
                    long playerTotalScore = meleePlayerInfo.addScore(islandId, areaId, addScore);
                    Map<Long, MeleePlayerAreaRankNode> playerAreaRankMap = meleeIslandAreaRankInfo.getPlayerAreaRankMap();
                    MeleePlayerAreaRankNode playerAreaRankNode = playerAreaRankMap.computeIfAbsent(playerId, vo -> new MeleePlayerAreaRankNode(playerId));
                    playerAreaRankNode.setScore(playerTotalScore);
                }

                if (leagueId > 0) {
                    MeleeLeagueInfo meleeLeagueInfo = leagueInfoMap.computeIfAbsent(leagueId, vo -> new MeleeLeagueInfo(leagueId));
                    //岛屿内帮派排行榜更新
                    // 岛屿帮派积分
                    long leagueTotalScore = meleeLeagueInfo.addScore(islandId, areaId, addScore);
                    Map<Long, MeleeLeagueAreaRankNode> leagueAreaRankMap = meleeIslandAreaRankInfo.getLeagueAreaRankMap();
                    //旧排名
                    int oldRank = getIslandLeagueRank(islandId, areaId, leagueId);
                    MeleeLeagueAreaRankNode leagueAreaRankNode = leagueAreaRankMap.computeIfAbsent(leagueId, vo -> new MeleeLeagueAreaRankNode(leagueId));
                    leagueAreaRankNode.setScore(leagueTotalScore);
                    //新排名
                    int newRank = getIslandLeagueRank(islandId, areaId, leagueId);
                    //激战区变成第一名 记录日志
                    MeleeAreaTemplate meleeAreaTemplate = LeagueMeleeService.getAreaMap().get(areaId);
                    if(meleeAreaTemplate != null){
                        if (meleeAreaTemplate.areaIncome == MeleeIslandAreaBelongIncomeType.attribution.getType() && oldRank != 1 && newRank == 1) {
                            combatMeleeRecord(leagueId, islandId, meleeIslandAreaRankInfo);
                        }
                    }
                }
            }
        }


        // 荣誉值增加
        if (addHonor > 0) {
            if (Objects.nonNull(player)) {
                long playerId = player.getPlayerId();
                MeleePlayerInfo meleePlayerInfo = playerInfoMap.computeIfAbsent(playerId, vo -> new MeleePlayerInfo(playerId));
                //荣誉值增加
                meleePlayerInfo.addHonor(addHonor);

                //玩家总榜更新
                Map<Long, MeleePlayerHonorRankNode> playerHonorRankMap = groupInfo.getPlayerHonorRankMap();
                MeleePlayerHonorRankNode meleePlayerHonorRankNode = playerHonorRankMap.computeIfAbsent(leagueId, vo -> new MeleePlayerHonorRankNode(playerId));
                meleePlayerHonorRankNode.setHonorValue(meleePlayerInfo.getHonorValue());
                globalData.getTotalHonorMap().merge(playerId, addHonor, Long::sum);
            }

            if (leagueId > 0) {
                MeleeLeagueInfo meleeLeagueInfo = leagueInfoMap.computeIfAbsent(leagueId, vo -> new MeleeLeagueInfo(leagueId));
                //帮荣誉值增加
                meleeLeagueInfo.addHonor(addHonor);

                //据点荣誉排行榜更新
                MeleeIslandRankInfo islandInfo = groupInfo.getIslandRankInfoMap().computeIfAbsent(islandId, bean -> new MeleeIslandRankInfo(islandId));
                islandInfo.addHonor(leagueId, addHonor);

                //帮派总榜更新
                Map<Long, MeleeLeagueHonorRankNode> honorRankMap = groupInfo.getHonorRankMap();
                MeleeLeagueHonorRankNode meleeLeagueHonorRankNode = honorRankMap.computeIfAbsent(leagueId, vo -> new MeleeLeagueHonorRankNode(leagueId));
                meleeLeagueHonorRankNode.setHonorValue(meleeLeagueInfo.getHonorValue());
            }

            ThreadPool.execute(() -> leagueMeleeTotalRankNotify(groupInfo, leagueId));
        }

        ThreadPool.execute(() -> leagueMeleeAreaRankNotify(groupInfo, islandId, areaId));
    }

    private static void leagueMeleeTotalRankNotify(MeleeGroupInfo groupInfo, long leagueId) {
        for (Map<Long, MeleePlayerInInfo> map : groupInfo.getPlayerInMap().values()) {
            for (MeleePlayerInInfo bean : map.values()) {
                if(bean.getIslandId() == 0){
                    Player player = PlayerManager.getOnlinePlayer(bean.getPlayerId());
                    if(player != null){
                        PbProtocol.LeagueMeleeTotalRankNotify.Builder notify = PbProtocol.LeagueMeleeTotalRankNotify.newBuilder();
                        notify.setRankInfo(genTotalRankInfo(groupInfo, player.getPlayerId(), leagueId));
                        player.send(PtCode.LEAGUE_MELEE_TOTAL_RANK_NOTIFY, notify.build());
                    }
                }
            }
        }
    }

    /**
     * 帮派战场记录
     *
     * @param leagueId                帮派ID
     * @param islandId                据点ID
     * @param meleeIslandAreaRankInfo 战场V0
     */
    private static void combatMeleeRecord(long leagueId, int islandId, MeleeIslandAreaRankInfo meleeIslandAreaRankInfo) {
        League league = LeagueManager.getLeagueById(leagueId);
        LeagueMeleeModel meleeModel = league.getModel(LeagueModelEnums.melee);
        Map<Long, MeleeLeagueAreaRankNode> leagueAreaRankMap = meleeIslandAreaRankInfo.getLeagueAreaRankMap();
        //初始占领/拥有者未变动不记录
        if(leagueAreaRankMap.size() < 2){
            return;
        }

        List<MeleeLeagueAreaRankNode> rankList = new ArrayList<>(leagueAreaRankMap.values());
        Collections.sort(rankList);
        MeleeRecord meleeRecord = new MeleeRecord();
        meleeRecord.setIslandId(islandId);

        if(rankList.size() > 1){
            meleeRecord.setHoldLeagueId(rankList.get(0).getLeagueId());
        }

        if (rankList.size() >= 2) {
            MeleeLeagueAreaRankNode leagueAreaRankNode = rankList.get(1);
            League tmpLeague = LeagueManager.getLeagueById(leagueAreaRankNode.getLeagueId());
            meleeRecord.setOpponentLeague(tmpLeague.createLeagueUserDataDb());
            meleeRecord.setOpponentFightNum(getLeaguePlayersInIsland(meleeIslandAreaRankInfo, leagueAreaRankNode.getLeagueId()));
        }

        meleeRecord.setMyLeague(league.createLeagueUserDataDb());
        meleeRecord.setMyLeagueFightNum(getLeaguePlayersInIsland(meleeIslandAreaRankInfo, leagueId));
        meleeRecord.setTime(ServerConstants.getCurrentTimeMillis());
        meleeModel.addRecord(meleeRecord);
    }

    /**
     * 获取区域内帮派玩家数量
     * @param meleeIslandAreaRankInfo
     * @param leagueId 帮派
     * @return 帮派内玩家数量
     */
    public static int getLeaguePlayersInIsland(MeleeIslandAreaRankInfo meleeIslandAreaRankInfo, long leagueId) {
        return meleeIslandAreaRankInfo.getBattlePlayerMap().getOrDefault(leagueId, new HashSet<>()).size();
    }

    /**
     * 获取岛屿内帮派排行
     *
     * @param islandId 岛屿ID
     * @param areaId 区域ID
     * @param leagueId 帮派ID
     * @return 排名 -1代表没有排名
     */
    public static int getIslandLeagueRank(int islandId, int areaId, long leagueId) {
        LeagueMeleeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueMelee);
        LeagueMeleeBattleInfo battleInfo = globalData.getBattleInfo();
        MeleeGroupInfo meleeGroupInfo = battleInfo.getGroupInfoByLeagueId(leagueId);
        MeleeIslandRankInfo meleeIslandRankInfo = meleeGroupInfo.getIslandRankInfoMap().get(islandId);
        if (Objects.isNull(meleeIslandRankInfo)) {
            return -1;
        }
        MeleeIslandAreaRankInfo meleeIslandAreaRankInfo = meleeIslandRankInfo.getAreaRankInfoMap().get(areaId);
        if (Objects.isNull(meleeIslandAreaRankInfo)) {
            return -1;
        }
        Map<Long, MeleeLeagueAreaRankNode> leagueAreaRankMap = meleeIslandAreaRankInfo.getLeagueAreaRankMap();
        MeleeLeagueAreaRankNode leagueAreaRankNode = leagueAreaRankMap.get(leagueId);
        if (Objects.isNull(leagueAreaRankNode)) {
            return -1;
        }
        List<MeleeLeagueAreaRankNode> rankList = new ArrayList<>(leagueAreaRankMap.values());
        Collections.sort(rankList);
        return rankList.indexOf(leagueAreaRankNode) + 1;
    }


    /**
     * 获取本周节点的结束时间
     *
     * @param nodeType 节点
     * @return 结束时间
     */
    public static LocalDateTime getStatusEndTime(MeleeNodeType nodeType) {
        LocalDateTime statusStartTime = getStatusStartTime(nodeType);
        MeleeConstant constant = LeagueMeleeService.getConstant();
        Pair<LocalTime, Integer> timeConfig = constant.onTimeMap.get(nodeType);
        return statusStartTime.plusMinutes(timeConfig.getRight());
    }

    /**
     * 获取本周节点的结束时间
     *
     * @param nodeType 节点
     * @return 结束时间
     */
    public static LocalDateTime getStatusStartTime(MeleeNodeType nodeType) {
        MeleeConstant constant = LeagueMeleeService.getConstant();
        Pair<LocalTime, Integer> timeConfig = constant.onTimeMap.get(nodeType);
        LocalDateTime localDateTime = ServerConstants.getCurrentTimeLocalDateTime();
        int dayOfWeek = localDateTime.getDayOfWeek().getValue();
        int durationDay = dayOfWeek - constant.onTimeWeek;
        return localDateTime.plusDays(durationDay).with(timeConfig.getLeft());
    }

    /**
     * 验证移动请求的合法性
     *
     * @param curPosition 当前位置
     * @param newX        新的X坐标
     * @param newY        新的Y坐标
     * @return true 移动合法
     */
    public static boolean isValidMove(MeleeMapPosition curPosition, float newX, float newY) {
        MeleeConstant constant = LeagueMeleeService.getConstant();
        //不能超过地图边界
        if (newX > constant.xMax || newY > constant.yMax) {
            return false;
        }
        float distance = calculateDistance(curPosition.getX(), curPosition.getY(), newX, newY);
        float maxAllowedDistance = constant.speed; // 假设玩家的速度决定了最大允许移动距离
        return distance <= maxAllowedDistance;
    }

    // 计算两点之间的距离
    private static float calculateDistance(float x1, float y1, float x2, float y2) {
        float dx = x2 - x1;
        float dy = y2 - y1;
        return (float) Math.sqrt(dx * dx + dy * dy);
    }

    /**
     * 判断点是否在圆内的方法
     *
     * @param position       点
     * @param centerPosition 圆心坐标
     * @param radius         半径
     * @return true 在圆内
     */
    public static boolean isPointInCircle(MeleeMapPosition position, MeleeMapPosition centerPosition, float radius) {
        float x = position.getX();
        float y = position.getY();
        return isPointInCircle(x, y, centerPosition, radius);
    }

    /**
     * 判断点是否在圆内的方法
     *
     * @param centerPosition 圆心坐标
     * @param radius         半径
     * @return true 在圆内
     */
    public static boolean isPointInCircle(float x, float y, MeleeMapPosition centerPosition, float radius) {
        float centerX = centerPosition.getX();
        float centerY = centerPosition.getY();
        // 计算点到圆心的距离
        float distanceSquared = (x - centerX) * (x - centerX) + (y - centerY) * (y - centerY);

        // 判断距离是否小于等于圆的半径的平方
        return distanceSquared <= (radius * radius);
    }

    /**
     * 创建乱斗战报
     * @param opponentPlayer
     * @param isRobot
     * @param id
     * @param winingStreak
     * @param costPower
     * @param scorePair
     * @param isWin
     * @param islandId
     * @return
     */
    public static MeleePlayerRecord createMeleePlayerRecord(Player opponentPlayer, boolean isRobot, int id, int winingStreak, int costPower, Pair<Integer, Integer> scorePair, boolean isWin, int islandId){
        MeleePlayerRecord meleePlayerRecord = new MeleePlayerRecord();
        if(isRobot){
            meleePlayerRecord.setRobot(true);
            //前端需要id
            meleePlayerRecord.setRobotId(id);
        }else {
            meleePlayerRecord.setId(opponentPlayer.getPlayerId());
            meleePlayerRecord.setName(opponentPlayer.getName());
            meleePlayerRecord.setLevel(opponentPlayer.getLevel());
            meleePlayerRecord.setFightingPower(opponentPlayer.getFightingPower());
            meleePlayerRecord.setVipLevel(opponentPlayer.getVipLevel());
            meleePlayerRecord.setProtagonistId(PlayerRoleService.getProtagonistTemplate(opponentPlayer.getProfession(), opponentPlayer.getSex()).id);
            meleePlayerRecord.setHeadPlan(opponentPlayer.getRoleModel().getHeadPlan());
            meleePlayerRecord.setHeadFrameId(opponentPlayer.getRoleModel().getHeadFrameId());
            meleePlayerRecord.setHeroTitleId(opponentPlayer.getRoleModel().getHeroTitleId());
            meleePlayerRecord.setDesignationId(opponentPlayer.getRoleModel().getDesignationId());
        }

        meleePlayerRecord.setChangePower(costPower);
        meleePlayerRecord.setWiningStreak(winingStreak);
        if(scorePair != null){
            meleePlayerRecord.setScore(scorePair.getLeft());
            meleePlayerRecord.setHonor(scorePair.getRight());
        }
        meleePlayerRecord.setWin(isWin);
        meleePlayerRecord.setIslandId(islandId);
        meleePlayerRecord.setTime(ServerConstants.getCurrentTimeMillis());
        return meleePlayerRecord;
    }

    public static PbLeagueMelee.MeleeRecord genMeleeRecord(MeleeRecord db){
        PbLeagueMelee.MeleeRecord.Builder builder = PbLeagueMelee.MeleeRecord.newBuilder();
        builder.setIslandId(db.getIslandId());
        builder.setMyLeague(genLeagueUserData(db.getMyLeague()));
        builder.setMyLeagueFightNum(db.getMyLeagueFightNum());
        if(db.getOpponentLeague() != null){
            builder.setOpponentLeague(genLeagueUserData(db.getOpponentLeague()));
            builder.setOpponentFightNum(db.getOpponentFightNum());
        }
        builder.setHoldLeagueId(db.getHoldLeagueId());
        builder.setTime(db.getTime());
        return builder.build();
    }

    public static PbLeague.LeagueUserData genLeagueUserData(LeagueUserDataDb db){
        PbLeague.LeagueUserData.Builder builder = PbLeague.LeagueUserData.newBuilder();
        builder.setId(db.getId());
        builder.setName(db.getName());
        builder.setLevel(db.getLevel());
        builder.setIconName(db.getIconName());
        builder.setIconFrameId(db.getIconFrameId());
        return builder.build();
    }

    public static PbLeagueMelee.MeleePlayerRecord genMeleePlayerRecord(MeleePlayerRecord db){
        PbLeagueMelee.MeleePlayerRecord.Builder builder = PbLeagueMelee.MeleePlayerRecord.newBuilder();
        if(!db.isRobot()){
            builder.setMinMinUser(genMinMiniUser(db.getId(), db.getName(), db.getLevel(), db.getFightingPower(), db.getVipLevel(), db.getProtagonistId(), db.getHeadPlan(), db.getHeadFrameId(), db.getHeroTitleId(), db.getDesignationId()));
        }
        builder.setChangePower(db.getChangePower());
        builder.setWiningStreak(db.getWiningStreak());
        builder.setScore(db.getScore());
        builder.setHonor(db.getHonor());
        builder.setIsWin(db.isWin());
        builder.setIslandId(db.getIslandId());
        builder.setIsRobot(db.isRobot());
        builder.setRobotId(db.getRobotId());
        builder.setTime(db.getTime());
        return builder.build();
    }

    public static PbCommons.MinMiniUser genMinMiniUser(long id, String name, int level, long fightPower, int vipLevel, int protagonistId, int headPlan, int headFrameId, int heroTitleId, int designationId){
        PbCommons.MinMiniUser.Builder builder = PbCommons.MinMiniUser.newBuilder();
        builder.setId(id);
        builder.setName(name);
        builder.setLevel(level);
        builder.setFightingPower(fightPower);
        builder.setVipLevel(vipLevel);
        builder.setProtagonistId(protagonistId);
        builder.setHeadPlan(headPlan);
        builder.setHeadFrameId(headFrameId);
        builder.setHeroTitleId(heroTitleId);
        builder.setDesignationId(designationId);
        return builder.build();
    }

    /**
     * 帮派乱斗初始化boss
     * @param bean
     * @return
     */
    public static MeleeBossAreaInfo initBoss(MeleeAreaTemplate bean){
        MeleeBossAreaInfo bossAreaInfo = new MeleeBossAreaInfo();
        bossAreaInfo.setAreaId(bean.id);
        BattleCollectTemplate battleCollectTemplate = BattleCollectService.getBattleCollectTemplateMap().get(bean.battleCollect);
        if(battleCollectTemplate != null){
            for (int monsterId : battleCollectTemplate.monsterList) {
                MonsterTemplate monsterTemplate = MonsterService.monsterTemplates.get(monsterId);
                if(monsterTemplate != null){
                    bossAreaInfo.getBossHpMap().put(monsterId, monsterTemplate.attrs.getValue(AttributeKey.最大生命, true));
                }
            }
        }else {
            throw new IllegalArgumentException("battleCollectTemplate is null, id is " + bean.battleCollect);
        }
        return bossAreaInfo;
    }

    public static long getBossHpSum(MeleeAreaTemplate bean){
        BattleCollectTemplate battleCollectTemplate = BattleCollectService.getBattleCollectTemplateMap().get(bean.battleCollect);
        long sumHp = 0;
        for (int monsterId : battleCollectTemplate.monsterList) {
            MonsterTemplate monsterTemplate = MonsterService.monsterTemplates.get(monsterId);
            if(monsterTemplate != null){
                sumHp = sumHp + monsterTemplate.attrs.getValue(AttributeKey.最大生命);
            }
        }
        return sumHp;
    }

    /**
     * 被攻击的玩家map变动
     * @param defPlayerId
     * @param atkPlayerId
     * @param atkPlayerName
     * @param addOrRemove
     */
    public static void beFightMapChange(long defPlayerId, long atkPlayerId, String atkPlayerName, boolean addOrRemove){
        LeagueMeleeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueMelee);
        LeagueMeleeBattleInfo battleInfo = globalData.getBattleInfo();
        MeleeGroupInfo groupInfo = battleInfo.getGroupInfoByPlayerId(defPlayerId);
        if(groupInfo == null){
            return;
        }

        if(addOrRemove){
            groupInfo.getBeFightMap().computeIfAbsent(defPlayerId, bean -> new ConcurrentHashMap<>()).put(atkPlayerId, atkPlayerName);
        }else {
            if(groupInfo.getBeFightMap().containsKey(defPlayerId)){
                ConcurrentHashMap<Long, String> map = groupInfo.getBeFightMap().get(defPlayerId);
                map.remove(atkPlayerId);
                if(map.isEmpty()){
                    groupInfo.getBeFightMap().remove(defPlayerId);
                }
            }
        }

        //通知被攻击者
        PbProtocol.LeagueMeleeBeFightNotify.Builder notify = PbProtocol.LeagueMeleeBeFightNotify.newBuilder();
        ConcurrentHashMap<Long, String> map = groupInfo.getBeFightMap().getOrDefault(defPlayerId, new ConcurrentHashMap<>());
        if(!map.isEmpty()){
            for (Long playerId : new ArrayList<>(map.keySet())) {
                long leagueId = LeagueManager.getLeagueId(playerId);
                if(groupInfo.getPlayerInMap().containsKey(leagueId)){
                    if(groupInfo.getPlayerInMap().get(leagueId).containsKey(playerId)){
                        continue;
                    }
                }
                map.remove(playerId);
            }
            notify.putAllBeFightMap(map);
        }

        Player player = PlayerManager.getOnlinePlayer(defPlayerId);
        if(player != null){
            player.send(PtCode.LEAGUE_MELEE_BE_FIGHT_NOTIFY, notify.build());
        }
    }

    public static PbLeagueMelee.MeleeHonorPanel genPbMeleeHonorPanel(HonorPanel honorPanel, int rank) {
        return PbLeagueMelee.MeleeHonorPanel.newBuilder()
                .addAllLeagueInfo(honorPanel.getLeagueList().stream().map(bean -> genHonorPanelLeague(bean)).collect(Collectors.toList()))
                .addAllPlayerInfo(honorPanel.getPlayerList().stream().map(bean -> genHonorPanelPlayer(bean)).collect(Collectors.toList()))
                .setRank(rank).build();
    }

    public static PbLeagueMelee.HonorPanelLeague genHonorPanelLeague(HonorPanelLeague bean) {
        return PbLeagueMelee.HonorPanelLeague.newBuilder()
                .setLeagueId(bean.getLeagueId())
                .setName(bean.getName())
                .setIconName(bean.getIconName())
                .setIconFrameId(bean.getIconFrameId()).build();
    }

    public static PbLeagueMelee.HonorPanelPlayer genHonorPanelPlayer(HonorPanelPlayer bean) {
        return PbLeagueMelee.HonorPanelPlayer.newBuilder()
                .setPlayerId(bean.getPlayerId())
                .setName(bean.getName())
                .setJob(bean.getJob())
                .setFightPower(bean.getFightPower())
                .setProtagonistId(bean.getProtagonistId()).build();
    }
}

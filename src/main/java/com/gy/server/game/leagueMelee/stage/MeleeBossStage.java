package com.gy.server.game.leagueMelee.stage;

import com.gy.server.game.attribute.AttributeKey;
import com.gy.server.game.battleCollect.BattleCollectService;
import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.combat.CombatManager;
import com.gy.server.game.combat.StageType;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.combat.unit.TeamUnit;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.leagueMelee.LeagueMeleeGlobalData;
import com.gy.server.game.leagueMelee.LeagueMeleeHelper;
import com.gy.server.game.leagueMelee.LeagueMeleeService;
import com.gy.server.game.leagueMelee.PlayerLeagueMeleeModel;
import com.gy.server.game.leagueMelee.status.LeagueMeleeBattleInfo;
import com.gy.server.game.leagueMelee.template.MeleeAreaTemplate;
import com.gy.server.game.leagueMelee.template.MeleeIslandAreaBelongIncomeType;
import com.gy.server.game.leagueMelee.template.MeleePointTemplate;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.world.World;
import com.gy.server.packet.PbProtocol;
import com.ttlike.server.tl.baselib.serialize.leagueMelee.MeleeBossAreaInfo;
import com.ttlike.server.tl.baselib.serialize.leagueMelee.MeleeGroupInfo;
import com.ttlike.server.tl.baselib.serialize.leagueMelee.MeleeIslandRankInfo;

import java.util.*;

/**
 * 帮派乱斗boss战斗
 *
 * <AUTHOR> 2024/10/8 13:29
 **/
public class MeleeBossStage extends AbstractStage {

    private Player atkPlayer;
    private int islandId;
    private MeleeAreaTemplate areaTemplate;
    private Set<Integer> atkBuffIds = new HashSet<>();
    private int battleCollectId;
    private Map<Integer, Long> areaBossHpMap = new HashMap<>();

    LineupType lineupType = LineupType.leagueMeleePVE;
    StageType stageType = StageType.leagueMeleePVE;

    public MeleeBossStage(Player atkPlayer, int islandId, int areaId, int battleCollectId, MeleeGroupInfo groupInfo) {
        this.atkPlayer = atkPlayer;
        this.islandId = islandId;
        this.areaTemplate = LeagueMeleeService.getAreaMap().get(areaId);

        String key = islandId + "" + areaId;
        MeleeBossAreaInfo bossAreaInfo = groupInfo.getBossAreaInfoMap().get(key);
        if(bossAreaInfo != null){
            this.areaBossHpMap.putAll(bossAreaInfo.getBossHpMap());
        }

        MeleePointTemplate pointTemplate = LeagueMeleeService.getPointMap().get(islandId);
        MeleeIslandRankInfo islandRankInfo = groupInfo.getIslandRankInfoMap().get(islandId);
        if(islandRankInfo != null){
            for (int id : pointTemplate.pointAreaIds) {
                MeleeAreaTemplate template = LeagueMeleeService.getAreaMap().get(id);
                if(template != null){
                    if(template.areaIncome == MeleeIslandAreaBelongIncomeType.getBuff.getType()){
                        if(islandRankInfo.getFirstLeagueIdMap().containsKey(template.id)){
                            long beLongLeagueId = islandRankInfo.getFirstLeagueIdMap().get(template.id);
                            if(beLongLeagueId == LeagueManager.getLeagueId(atkPlayer)){
                                atkBuffIds.add(template.areaParameter);
                            }
                        }
                    }
                }
            }
        }

        this.battleCollectId = battleCollectId;
    }

    @Override
    public void init() {
        List<TeamUnit> atkList = atkPlayer.getLineupModel().createTeamUnits(this, stageType, lineupType);
        if(!atkBuffIds.isEmpty()){
            for (TeamUnit teamUnit : atkList) {
                atkBuffIds.forEach(buffId -> teamUnit.addUnitBuff(buffId, 1));
            }
        }

        //更改初始化方法
        List<TeamUnit> defList = BattleCollectService.createTeamUnitsWithHp(this, World.getWorldLevel(), areaBossHpMap, battleCollectId);
        init(battleCollectId, stageType, atkList, defList, lineupType);
    }

    @Override
    public void afterFinish() {
        finishHandler(atkPlayer, isWin);

        atkPlayer.postEvent(PlayerEventType.leagueMeleeAtk);
    }

    @Override
    public void combatAbort(boolean isStart) {
        LeagueMeleeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueMelee);
        LeagueMeleeBattleInfo battleInfo = globalData.getBattleInfo();
        MeleeGroupInfo groupInfo = battleInfo.getGroupInfoByPlayer(atkPlayer);
        groupInfo.removeFightingBossPlayer(islandId, areaTemplate.id, atkPlayer.getPlayerId());
    }

    @Override
    public List<Long> needSyncDefHpPlayerId() {
        List<Long> playerIds = new ArrayList<>();
        LeagueMeleeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueMelee);
        LeagueMeleeBattleInfo battleInfo = globalData.getBattleInfo();
        MeleeGroupInfo groupInfo = battleInfo.getGroupInfoByPlayer(atkPlayer);
        String key = islandId + "" + areaTemplate.id;
        if(groupInfo.getFightingBossMap().containsKey(key)){
            playerIds.addAll(groupInfo.getFightingBossMap().get(key));
        }
        return playerIds;
    }

    private void finishHandler(Player player, boolean isWin){
        PbProtocol.CombatSettlementNotify.Builder rst = genCombatSettlement();
        PbProtocol.MeleeSettlement.Builder meleeSettlement = PbProtocol.MeleeSettlement.newBuilder();

        LeagueMeleeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueMelee);
        LeagueMeleeBattleInfo battleInfo = globalData.getBattleInfo();
        MeleeGroupInfo groupInfo = battleInfo.getGroupInfoByPlayer(player);
        groupInfo.removeFightingBossPlayer(islandId, areaTemplate.id, player.getPlayerId());

        //排行榜变动
        long damage = CombatManager.getCombatDamage(getAtks(), this);
        LeagueMeleeHelper.addBossScore(player.getPlayerId(), damage, islandId, areaTemplate.id);

        if(isWin){
            //获取boss区排行第一名
            MeleeIslandRankInfo islandRankInfo = groupInfo.getIslandRankInfoMap().get(islandId);
            //移除野怪并终止其他人在野怪中的战斗
            LeagueMeleeHelper.stopBossFight(groupInfo, islandId, areaTemplate.id, player.getPlayerId());
            if(islandRankInfo != null){
                long beLeagueId = islandRankInfo.getFirstLeagueIdMap().getOrDefault(LeagueManager.getLeagueId(player), 0l);
                MeleeBossAreaInfo bossAreaInfo = LeagueMeleeHelper.initBoss(areaTemplate);
                bossAreaInfo.setLeagueId(beLeagueId);
                islandRankInfo.getFirstLeagueIdMap().remove(LeagueManager.getLeagueId(player));
                islandRankInfo.getAreaRankInfoMap().remove(areaTemplate.id);
                groupInfo.getBossAreaInfoMap().put(bossAreaInfo.getKey(), bossAreaInfo);
            }
        }else {
            String key = islandId + "" + areaTemplate.id;
            MeleeBossAreaInfo bossAreaInfo = groupInfo.getBossAreaInfoMap().get(key);
            if(bossAreaInfo == null){
                bossAreaInfo = LeagueMeleeHelper.initBoss(areaTemplate);
                groupInfo.getBossAreaInfoMap().put(bossAreaInfo.getKey(), bossAreaInfo);
            }

            //boss血量变动
            for (TeamUnit def : getDefs()) {
                for (HeroUnit unit : def.getUnits()) {
                    if(bossAreaInfo.getBossHpMap().containsKey(unit.getTid())){
                        bossAreaInfo.getBossHpMap().put(unit.getTid(), Math.max(unit.getAttributes().getValue(AttributeKey.当前生命值), 0l));
                    }
                }
            }
        }

        PlayerLeagueMeleeModel playerMeleeModel = player.getModel(PlayerModelEnums.leagueMelee);
        int costPower = isWin ? LeagueMeleeService.getConstant().combatWinEnergyMap.get(areaTemplate.areaType) : LeagueMeleeService.getConstant().combatLoseEnergyMap.get(areaTemplate.areaType);
        playerMeleeModel.costPower(costPower);

        long hpSum = LeagueMeleeHelper.getBossHpSum(areaTemplate);
        if(hpSum != 0){
            meleeSettlement.setPercent((double) (damage / hpSum));
        }
        meleeSettlement.setRemainPower(playerMeleeModel.getRemainPower());
        meleeSettlement.setRebirthTime(playerMeleeModel.getRebirthTime());
        rst.setMelee(meleeSettlement.build());

        notifyCombatSettlement(player, rst.build());

    }
}

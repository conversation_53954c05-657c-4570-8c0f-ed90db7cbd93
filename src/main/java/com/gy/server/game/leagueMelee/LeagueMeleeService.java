package com.gy.server.game.leagueMelee;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.callback.response.CallbackResponse;
import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.combat.CombatManager;
import com.gy.server.game.common.ConstantConfigReader;
import com.gy.server.game.currency.Currency;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.function.Function;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.league.LeagueModelEnums;
import com.gy.server.game.league.enums.LeagueJobTypeEnums;
import com.gy.server.game.leagueMelee.async.MeleeGetPositionAsync;
import com.gy.server.game.leagueMelee.stage.MeleeBossStage;
import com.gy.server.game.leagueMelee.stage.MeleePiratesStage;
import com.gy.server.game.leagueMelee.stage.MeleeStage;
import com.gy.server.game.leagueMelee.status.LeagueMeleeBattleInfo;
import com.gy.server.game.leagueMelee.template.*;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.game.service.Service;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbLeagueMelee;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.rpc.TLMessageCallbackTask;
import com.ttlike.server.tl.baselib.serialize.leagueMelee.*;
import com.ttlike.server.tl.baselib.serialize.scene.ScenePosition3D;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 帮派乱斗（凤凰古城）
 *
 * <AUTHOR> - [Created on 2023/7/18 10:01]
 */
public class LeagueMeleeService extends PlayerPacketHandler implements Service {

    //运送任务已领取
    public static final int SHIPPING_RECEIVE = 1;
    //运送任务已采集
    public static final int SHIPPING_COLLECT = 2;

    /**
     * 天地分组
     */
    public static final int GROUP_T = 1;
    public static final int GROUP_D = 2;

    private static MeleeConstant constant;

    /**
     * 赛季map
     */
    private static Map<Integer, MeleeSeasonTemplate> seasonMap = new HashMap<>();

    /**
     * key:赛季id   value: key:时间点 value:MeleeGameTimeTemplate
     */
    private static Map<Integer, Map<Integer, MeleeGameTimeTemplate>> gameTimeMap = new HashMap<>();

    /**
     * key:赛季id   value:key:阵容id value:MeleeMonsterTemplate
     */
    private static Map<Integer, Map<Integer, MeleeMonsterTemplate>> monsterMap = new HashMap<>();

    /**
     * key:登记区间最大等级  value:MeleeRobotTemplate
     */
    private static TreeMap<Integer, MeleeRobotTemplate> robotMap = new TreeMap<>();

    /**
     * 据点map
     * key:id  value:MeleePointTemplate
     */
    private static Map<Integer, MeleePointTemplate> pointMap = new HashMap<>();

    /**
     * 出生点map
     * key:赛季id  value:据点map
     */
    private static Map<Integer, MeleePointTemplate> brithPointMap = new HashMap<>();

    /**
     * 地块map
     * key:id   value:
     */
    private static Map<Integer, MeleeAreaTemplate> areaMap = new HashMap<>();

    /**
     * 个人奖励map
     */
    private static Map<Integer, MeleeRewardTemplate> rewardMap = new HashMap<>();

    /**
     * 联盟奖励map
     */
    private static Map<Integer, MeleeLeagueRewardTemplate> leagueRewardMap = new HashMap<>();

    /**
     * 公用检测
     */
    private int check(Player player) {
        if (Function.leagueMelee.isNotOpen(player)) {
            return Text.功能未开启;
        }
        if (LeagueManager.isNotJoinLeague(player)) {
            return Text.未加入帮派;
        }
        LeagueMeleeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueMelee);
        LeagueMeleeBattleInfo battleInfo = globalData.getBattleInfo();

        if(battleInfo.getStatus() == MeleeStatusType.noStart.getType() || battleInfo.getStatus() == MeleeStatusType.abort.getType()){
            return Text.帮派乱斗不在开启时间;
        }

        long leagueId = LeagueManager.getLeagueId(player);
        if (battleInfo.isNotJoinLeague(leagueId)) {
            return Text.帮派乱斗帮派未参加本次活动;
        }
        
        MeleeGroupInfo groupInfo = battleInfo.getGroupInfoByLeagueId(leagueId);
        if (Objects.isNull(groupInfo)) {
            return Text.帮派乱斗帮派不在分组内;
        }
        if(LeagueManager.canNotPlayLeague(player)){
            return Text.您退出帮派未满足24小时无法参与帮派活动;
        }
        return Text.没有异常;
    }

    /**
     * 乱斗信息
     */
    @Handler(PtCode.LEAGUE_MELEE_INFO_REQ)
    private void leagueMeleeInfoReq(Player player, long time){
        PbProtocol.LeagueMeleeInfoRst.Builder rst = PbProtocol.LeagueMeleeInfoRst.newBuilder().setResult(Text.genOkServerRstInfo());

        LeagueMeleeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueMelee);
        LeagueMeleeBattleInfo battleInfo = globalData.getBattleInfo();
        int status = battleInfo.getStatus();

        int text = check(player);
        boolean enter = text == Text.没有异常 && (status != MeleeStatusType.beforeEnd.getType() && status != MeleeStatusType.endAndClear.getType() && status != MeleeStatusType.abort.getType() && status != MeleeStatusType.noStart.getType());
        rst.setEnter(enter);
        rst.setResult(Text.genServerRstInfo(text));

        if(enter){
            rst.setSeasonId(battleInfo.getSeasonId());

            League league = LeagueManager.getLeagueByPlayer(player);
            rst.setLeagueId(league.getLeagueId());
            int groupId = battleInfo.getGroupIndexMap().getOrDefault(league.getLeagueId(), 0);
            rst.setGroupId(groupId);
            if(groupId == GROUP_T || groupId == GROUP_D){
                rst.setMapInfo(battleInfo.genMapInfo(groupId, league.getLeagueId(), player.getPlayerId()));
            }

            rst.setCountPer(battleInfo.getCountPer());
            rst.setHonorPer(battleInfo.getHonorPer());

            MeleeGroupInfo groupInfo = battleInfo.getGroupInfoMap().get(groupId);
            rst.setTimeId(battleInfo.getStatus());
            rst.addAllAreaOpenInfo(LeagueMeleeHelper.genPointAreaOpenInfo(groupInfo.getIslandAreaOpenInfoMap().values()));

            LeagueMeleeModel meleeModel = league.getModel(LeagueModelEnums.melee);
            rst.addAllCmdInfo(meleeModel.genCmdInfos());

            PlayerLeagueMeleeModel playerMeleeModel = player.getModel(PlayerModelEnums.leagueMelee);
            if (playerMeleeModel.getRemainPower() == -1) {
                playerMeleeModel.setRemainPower(LeagueMeleeService.getConstant().beginEnergy);
            }
            rst.setRebirthNum(playerMeleeModel.getRebirthPayCount());
            rst.setRemainPower(playerMeleeModel.getRemainPower());
//            rst.setAllCostPower(playerMeleeModel.getAllCostPower());
            rst.setRebirthTime(playerMeleeModel.getRebirthTime());
            if(playerMeleeModel.getCurShippingIslandId() != -1){
                rst.setShipping(playerMeleeModel.genShippingPb());
            }
            rst.setClientSign(playerMeleeModel.isClientSign());
        }
        player.send(PtCode.LEAGUE_MELEE_INFO_RST, rst.build(), time);
    }

    /**
     * 分组名单
     */
    @Handler(PtCode.LEAGUE_MELEE_GROUP_INFO_REQ)
    private void leagueMeleeGroupInfoReq(Player player, long time) {
        PbProtocol.LeagueMeleeGroupInfoRst.Builder rst = PbProtocol.LeagueMeleeGroupInfoRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        logic:
        {
            int result = check(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }
            LeagueMeleeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueMelee);
            LeagueMeleeBattleInfo battleInfo = globalData.getBattleInfo();
            if (!MeleeStatusType.of(battleInfo.getStatus()).isShowGroupInfo()) {
                rst.setResult(Text.genServerRstInfo(Text.当前状态不能查看分组名单));
                break logic;
            }
            MeleeGroupInfo groupInfo = battleInfo.getGroupInfoByPlayer(player);
            Map<Long, MeleeLeagueHonorRankNode> honorRankMap = groupInfo.getHonorRankMap();
            for (MeleeLeagueHonorRankNode honorRankNode : honorRankMap.values()) {
                League league = LeagueManager.getLeagueById(honorRankNode.getLeagueId());
                PbLeagueMelee.MeleeGroupLeague.Builder builder = PbLeagueMelee.MeleeGroupLeague.newBuilder()
                        .setLeagueId(honorRankNode.getLeagueId())
                        .setActivePower(honorRankNode.getActiveFightPower())
                        .setForceValue(honorRankNode.getForceValue())
                        .setCount(league.getMemberCount())
                        .setIconFrameId(league.getIconFrameId())
                        .setIconName(league.getIconName())
                        .setName(league.getName())
                        .setLevel(league.getLevel())
                        .setGroupId(battleInfo.getGroupIndexMap().getOrDefault(honorRankNode.getLeagueId(), 0));
                rst.addGroupInfo(builder);
            }
        }
        player.send(PtCode.LEAGUE_MELEE_GROUP_INFO_RST, rst.build(), time);
    }

    /**
     * 集火指挥
     */
    @Handler(PtCode.LEAGUE_MELEE_NORMAL_CMD_REQ)
    private void leagueMeleeNormalCmdReq(Player player, PbProtocol.LeagueMeleeNormalCmdReq req, long time) {
        PbProtocol.LeagueMeleeNormalCmdRst.Builder rst = PbProtocol.LeagueMeleeNormalCmdRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        int islandId = req.getIslandId();
        int areaId = req.getAreaId();
        boolean isMark = req.getIsMark();

        logic:{
            LeagueMeleeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueMelee);
            LeagueMeleeBattleInfo battleInfo = globalData.getBattleInfo();

            MeleePointTemplate pointTemplate = pointMap.get(islandId);
            if (pointTemplate == null) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }

            if (pointTemplate.pointType != MeleeIslandType.battle) {
                rst.setResult(Text.genServerRstInfo(Text.非战斗据点不能标记));
                break logic;
            }

            MeleeAreaTemplate areaTemplate = areaMap.get(areaId);
            if (areaTemplate == null) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }

            if(!pointTemplate.pointAreaIds.contains(areaId)){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }

            int result = check(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }

            if (battleInfo.getStatus() != MeleeStatusType.battle.getType()) {
                rst.setResult(Text.genServerRstInfo(Text.非战斗状态不能标记));
                break logic;
            }

            League league = LeagueManager.getLeagueByPlayer(player);
            if (!league.hasAuth(player.getPlayerId(), LeagueJobTypeEnums.玩法指挥)) {
                rst.setResult(Text.genServerRstInfo(Text.权限不足不能标记));
                break logic;
            }

            LeagueMeleeModel meleeModel = league.getModel(LeagueModelEnums.melee);
            //核对标记次数
            if(isMark && !meleeModel.checkHaveCmdNum(player.getPlayerId())){
                rst.setResult(Text.genServerRstInfo(Text.剩余标记次数不足));
                break logic;
            }

            MeleeGroupInfo groupInfo = battleInfo.getGroupInfoByPlayer(player);
            meleeModel.markDeal(player, islandId, areaId, isMark, groupInfo);
        }
        player.send(PtCode.LEAGUE_MELEE_NORMAL_CMD_RST, rst.build(), time);
    }

    /**
     * 战术板
     */
    @Handler(PtCode.LEAGUE_MELEE_CMD_RECORD_REQ)
    private void leagueMeleeCmdRecordReq(Player player, long time) {
        PbProtocol.LeagueMeleeCmdRecordRst.Builder rst = PbProtocol.LeagueMeleeCmdRecordRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        logic:{
            int result = check(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            LeagueMeleeModel meleeModel = league.getModel(LeagueModelEnums.melee);

            rst.addAllRecord(meleeModel.genCmdRecords());
        }
        player.send(PtCode.LEAGUE_MELEE_CMD_RECORD_RST, rst.build(), time);
    }

    /**
     * 野怪战斗
     */
    @Handler(PtCode.LEAGUE_MELEE_PIRATES_FIGHT_REQ)
    private void monsterFight(Player player, PbProtocol.LeagueMeleePiratesFightReq req, long time) {
        PbProtocol.LeagueMeleePiratesFightRst.Builder rst = PbProtocol.LeagueMeleePiratesFightRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());

        logic:{
            int result = check(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }
            PlayerLeagueMeleeModel playerLeagueMeleeModel = player.getModel(PlayerModelEnums.leagueMelee);
            if (playerLeagueMeleeModel.getRemainPower() <= 0) {
                rst.setResult(Text.genServerRstInfo(Text.阵亡状态不能进行该操作));
                break logic;
            }

            LeagueMeleeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueMelee);
            LeagueMeleeBattleInfo battleInfo = globalData.getBattleInfo();
            if(battleInfo.getStatus() != MeleeStatusType.battle.getType()){
                rst.setResult(Text.genServerRstInfo(Text.不在战斗阶段));
                break logic;
            }

            MeleeGroupInfo groupInfo = battleInfo.getGroupInfoByPlayer(player);
            long uid = req.getUid();
            Map<Long, MeleePiratesInfo> meleeMonsterInfoMap = groupInfo.getMeleePiratesInfoMap();
            if (!meleeMonsterInfoMap.containsKey(uid)) {
                rst.setResult(Text.genServerRstInfo(Text.野怪不存在或者已被击杀));
                break logic;
            }

            if(player.getPlayerLeagueMeleeModel().getRemainPower() == 0){
                rst.setResult(Text.genServerRstInfo(Text.阵亡状态不能进行该操作));
                break logic;
            }

            long leagueId = LeagueManager.getLeagueId(player);
            //TODO 核对玩家和野怪距离
//            boolean inCircle = LeagueMeleeHelper.isPointInCircle(curPosition, new center, monsterTemplate.radius);
//            if (!inCircle) {
//                rst.setResult(Text.genServerRstInfo(Text.不在海盗圆形区域内));
//                break logic;
//            }
//            MeleePlayerInInfo playerInInfo = groupInfo.getPlayerInMap().getOrDefault(leagueId, new HashMap<>()).get(player.getPlayerId());
//            if (!mapPlayerInfoMap.containsKey(player.getPlayerId())) {
//                rst.setResult(Text.genServerRstInfo(Text.不在地图内));
//                break logic;
//            }
            MeleePiratesInfo meleePiratesInfo = meleeMonsterInfoMap.get(uid);
            int templateId = meleePiratesInfo.getTemplateId();
            MeleeMonsterTemplate monsterTemplate = monsterMap.getOrDefault(battleInfo.getSeasonId(), new HashMap<>()).get(templateId);
            if(monsterTemplate == null){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }

//            TLMessageCallbackTask receiveCallback = getShippingReceiveCallback(player, pointTemplate.id, time);
            MeleeGetPositionAsync.execute(player.getPlayerId(), new MonsterFightCallbackTask(player, rst, groupInfo, monsterTemplate, uid, playerLeagueMeleeModel, time));
            return;
//            MeleeMapPosition curPosition = mapPlayerInfoMap.get(player.getPlayerId()).getCurPosition();
//            // TODO 测试先注释 核对玩家位置？？？
//
//            for (Map<Integer, MeleeMonsterTemplate> monsterTemplateMap : LeagueMeleeService.getMonsterMap().values()) {
//                if(monsterTemplateMap.containsKey(templateId)){
//                    monsterTemplate = monsterTemplateMap.get(templateId);
//                    return;
//                }
//            }
        }

        player.send(PtCode.LEAGUE_MELEE_PIRATES_FIGHT_RST, rst.build(), time);
    }

    /**
     * 据点信息
     */
    @Handler(PtCode.LEAGUE_MELEE_ISLAND_INFO_REQ)
    private void leagueMeleeInfoReq(Player player, PbProtocol.LeagueMeleeIslandInfoReq req, long time){
        PbProtocol.LeagueMeleeIslandInfoRst.Builder rst = PbProtocol.LeagueMeleeIslandInfoRst.newBuilder().setResult(Text.genOkServerRstInfo());
        List<Integer> islandIdList = req.getIslandIdsList();

        logic:{
            int result = check(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }

            LeagueMeleeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueMelee);
            LeagueMeleeBattleInfo battleInfo = globalData.getBattleInfo();

            for (int islandId : islandIdList) {
                MeleePointTemplate pointTemplate = pointMap.get(islandId);
                if (pointTemplate == null) {
//                    rst.setResult(Text.genServerRstInfo(Text.参数异常));
//                    break logic;
                    continue;
                }

                PbLeagueMelee.MeleeIslandInfo.Builder builder = PbLeagueMelee.MeleeIslandInfo.newBuilder();
                builder.setIslandId(islandId);
                MeleeGroupInfo groupInfo = battleInfo.getGroupInfoByPlayer(player);
                MeleeIslandRankInfo islandRankInfo = groupInfo.getIslandRankInfoMap().get(islandId);
                if(islandRankInfo != null){
                    List<MeleeLeagueHonorRankNode> honorRankNodes = islandRankInfo.getHonorRankList();
                    for(int i = 0; i < honorRankNodes.size(); i++){
                        int rank = i + 1;
                        MeleeLeagueHonorRankNode leagueHonorRankNode = honorRankNodes.get(i);
                        PbLeagueMelee.LeagueRankNode leagueRankNode = LeagueMeleeHelper.genLeagueRankPb(leagueHonorRankNode, rank);
                        builder.addLeagueHonorRank(leagueRankNode);

                        if(leagueHonorRankNode.getLeagueId() == LeagueManager.getLeagueId(player)){
                            builder.setMyLeagueHonorRank(leagueRankNode);
                        }
                    }
                }

                rst.addIslandInfos(builder.build());
            }
        }

        player.send(PtCode.LEAGUE_MELEE_ISLAND_INFO_RST, rst.build(), time);
    }

    /**
     * 帮派乱斗-地点
     */
    @Handler(PtCode.LEAGUE_MELEE_ENTER_REQ)
    private void leagueMeleeEnterReq(Player player, PbProtocol.LeagueMeleeEnterReq req, long time){
        PbProtocol.LeagueMeleeEnterRst.Builder rst = PbProtocol.LeagueMeleeEnterRst.newBuilder().setResult(Text.genOkServerRstInfo());
        int islandId = req.getIslandId();
        int areaId = req.getAreaId();

        logic:{
            int result = check(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }

            LeagueMeleeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueMelee);
            LeagueMeleeBattleInfo battleInfo = globalData.getBattleInfo();
            MeleeGroupInfo groupInfo = battleInfo.getGroupInfoByPlayer(player);
            long leagueId = LeagueManager.getLeagueId(player);

            if(battleInfo.getStatus() != MeleeStatusType.battle.getType() && battleInfo.getStatus() != MeleeStatusType.lineup.getType()){
                rst.setResult(Text.genServerRstInfo(Text.不在战斗阶段));
                break logic;
            }

            Map<Long, MeleePlayerInInfo> playerInInfoMap = groupInfo.getPlayerInMap().computeIfAbsent(leagueId, map -> new HashMap<>());
            if(islandId < 0){
                //离开
                playerInInfoMap.remove(player.getPlayerId());
                break logic;
            }

            if(islandId == 0){
                if(areaId != 0){
                    areaId = 0;
                }
                playerInInfoMap.put(player.getPlayerId(), new MeleePlayerInInfo(player.getPlayerId(), islandId, areaId));
                break logic;
            }


            MeleePointTemplate pointTemplate = pointMap.get(islandId);
            if (pointTemplate == null) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }

            if(areaId != 0){
                MeleeAreaTemplate areaTemplate = areaMap.get(areaId);
                if (areaTemplate == null) {
                    rst.setResult(Text.genServerRstInfo(Text.参数异常));
                    break logic;
                }

                if(!pointTemplate.pointAreaIds.contains(areaId)){
                    rst.setResult(Text.genServerRstInfo(Text.参数异常));
                    break logic;
                }

                MeleeIslandAreaOpenInfo islandAreaOpenInfo = groupInfo.getIslandAreaOpenInfoMap().get(islandId);
                if(islandAreaOpenInfo == null){
                    rst.setResult(Text.genServerRstInfo(Text.区域未开启));
                    break logic;
                }

                if(!islandAreaOpenInfo.getAreaEndMap().containsKey(areaId)){
                    rst.setResult(Text.genServerRstInfo(Text.区域未开启));
                    break logic;
                }

                long endTime = islandAreaOpenInfo.getAreaEndMap().get(areaId);
                if(ServerConstants.getCurrentTimeMillis() > endTime){
                    rst.setResult(Text.genServerRstInfo(Text.区域已关闭));
                    break logic;
                }
            }

            playerInInfoMap.put(player.getPlayerId(), new MeleePlayerInInfo(player.getPlayerId(), islandId, areaId));
        }

        player.send(PtCode.LEAGUE_MELEE_ENTER_RST, rst.build(), time);
    }

    /**
     * 区域排行榜
     */
    @Handler(PtCode.LEAGUE_MELEE_AREA_RANK_REQ)
    private void leagueMeleeAreaRankReq(Player player, PbProtocol.LeagueMeleeAreaRankReq req, long time){
        PbProtocol.LeagueMeleeAreaRankRst.Builder rst = PbProtocol.LeagueMeleeAreaRankRst.newBuilder().setResult(Text.genOkServerRstInfo());
        int islandId = req.getIslandId();
        List<Integer> areaIds = new ArrayList<>(req.getAreaIdList());

        logic:{
            int result = check(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }

            LeagueMeleeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueMelee);
            LeagueMeleeBattleInfo battleInfo = globalData.getBattleInfo();

            MeleePointTemplate pointTemplate = pointMap.get(islandId);
            if (pointTemplate == null) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }

            if(!areaIds.isEmpty() && pointTemplate.pointAreaIds.containsAll(areaIds)){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }

            if(areaIds.isEmpty()){
                areaIds.addAll(pointTemplate.pointAreaIds);
            }

            MeleeGroupInfo groupInfo = battleInfo.getGroupInfoByPlayer(player);
            rst.addAllAreaRankInfo(LeagueMeleeHelper.genMeleeIslandAreaRankInfoList(player, groupInfo, islandId, areaIds));
        }
        
        player.send(PtCode.LEAGUE_MELEE_AREA_RANK_RST, rst.build(), time);
    }

    /**
     * 帮派乱斗-战场记录
     */
    @Handler(PtCode.LEAGUE_MELEE_PLAYER_RECORD_REQ)
    private void leagueMeleePlayerRecordReq(Player player, long time){
        PbProtocol.LeagueMeleePlayerRecordRst.Builder rst = PbProtocol.LeagueMeleePlayerRecordRst.newBuilder().setResult(Text.genOkServerRstInfo());

        logic:{
            int result = check(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }

            League league = LeagueManager.getLeagueByPlayer(player);
            LeagueMeleeModel leagueMeleeModel = league.getModel(LeagueModelEnums.melee);
            List<MeleeRecord> records = leagueMeleeModel.getRecordList();
            for (MeleeRecord meleeRecord : records) {
                rst.addRecord(LeagueMeleeHelper.genMeleeRecord(meleeRecord));
            }

            PlayerLeagueMeleeModel meleeModel = player.getModel(PlayerModelEnums.leagueMelee);
            List<MeleePlayerRecord> playerRecords = meleeModel.getRecordList();
            for (MeleePlayerRecord meleePlayerRecord : playerRecords) {
                rst.addPlayerRecord(LeagueMeleeHelper.genMeleePlayerRecord(meleePlayerRecord));
            }
        }

        player.send(PtCode.LEAGUE_MELEE_PLAYER_RECORD_RST, rst.build(), time);
    }

    /**
     * 运送任务领取
     */
    @Handler(PtCode.LEAGUE_CANCEL_SHIPPING_RECEIVE_REQ)
    private void leagueCancelShippingReceiveReq(Player player, long time) {
        PbProtocol.LeagueCancelShippingReceiveRst.Builder rst = PbProtocol.LeagueCancelShippingReceiveRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            int result = check(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }
            LeagueMeleeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueMelee);
            LeagueMeleeBattleInfo battleInfo = globalData.getBattleInfo();
            MeleeGroupInfo groupInfo = battleInfo.getGroupInfoByPlayer(player);

            long leagueId = LeagueManager.getLeagueId(player);
            MeleePlayerInInfo playerInInfo = groupInfo.getPlayerInMap().getOrDefault(leagueId, new HashMap<>()).get(player.getPlayerId());
            if(playerInInfo == null){
                rst.setResult(Text.genServerRstInfo(Text.不在任务区域内不能领取任务));
                break logic;
            }

            MeleePointTemplate pointTemplate = pointMap.get(playerInInfo.getIslandId());
            if(pointTemplate == null){
                rst.setResult(Text.genServerRstInfo(Text.对应的模板数据找不到));
                break logic;
            }

            MeleeAreaTemplate areaTemplate = areaMap.get(playerInInfo.getAreaId());
            if(areaTemplate == null || (areaTemplate != null && areaTemplate.areaType != 3)){
                rst.setResult(Text.genServerRstInfo(Text.不在任务区域内不能领取任务));
                break logic;
            }

            PlayerLeagueMeleeModel meleeModel = player.getModel(PlayerModelEnums.leagueMelee);
            if (meleeModel.getCurShippingIslandId() != -1) {
                rst.setResult(Text.genServerRstInfo(Text.当前有任务未完成));
                break logic;
            }

            if(meleeModel.getRemainPower() == 0){
                rst.setResult(Text.genServerRstInfo(Text.阵亡状态不能进行该操作));
                break logic;
            }

            if (meleeModel.getShippingNum() >= constant.supportMaxCount) {
                rst.setResult(Text.genServerRstInfo(Text.运送任务次数已达上限));
                break logic;
            }

            if (meleeModel.getRemainPower() <= 0) {
                rst.setResult(Text.genServerRstInfo(Text.阵亡状态不能进行该操作));
                break logic;
            }

            List<Long> playerIdList = new ArrayList<>();
            playerIdList.add(player.getPlayerId());

            //核对坐标点
            TLMessageCallbackTask receiveCallback = getShippingReceiveCallback(player, pointTemplate.pointNatural, time);
            MeleeGetPositionAsync.execute(playerIdList, receiveCallback);
            return;
        }
        player.send(PtCode.LEAGUE_CANCEL_SHIPPING_RECEIVE_RST, rst.build(), time);
    }

    private TLMessageCallbackTask getShippingReceiveCallback(Player player, int targetIslandId, long time) {
        PbProtocol.LeagueCancelShippingReceiveRst.Builder rst = PbProtocol.LeagueCancelShippingReceiveRst.newBuilder().setResult(Text.genOkServerRstInfo());
        return new ShippingReceiveCallbackTask(player, targetIslandId, rst, time);
    }

    /**
     * 运送任务采集
     */
    @Handler(PtCode.LEAGUE_CANCEL_SHIPPING_COLLECT_REQ)
    private void leagueCancelShippingCollectReq(Player player, long time) {
        PbProtocol.LeagueCancelShippingCollectRst.Builder rst = PbProtocol.LeagueCancelShippingCollectRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        logic:
        {
            int result = check(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }
//            LeagueMeleeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueMelee);
//            LeagueMeleeBattleInfo battleInfo = globalData.getBattleInfo();
//            MeleeGroupInfo groupInfo = battleInfo.getGroupInfoByPlayer(player);
//            long leagueId = LeagueManager.getLeagueId(player);

//            MeleePlayerInInfo playerInInfo = groupInfo.getPlayerInMap().getOrDefault(leagueId, new HashMap<>()).get(player.getPlayerId());
//            if(playerInInfo != null && playerInInfo.getIslandId() != -1 && playerInInfo.getAreaId() != -1){
//                rst.setResult(Text.genServerRstInfo(Text.不在对应的采集地点不能采集));
//                break logic;
//            }

            PlayerLeagueMeleeModel meleeModel = player.getModel(PlayerModelEnums.leagueMelee);
            int curShippingIslandId = meleeModel.getCurShippingIslandId();
            if (curShippingIslandId == -1) {
                rst.setResult(Text.genServerRstInfo(Text.没领取任务不能进行采集));
                break logic;
            }

            if(meleeModel.getRemainPower() == 0){
                rst.setResult(Text.genServerRstInfo(Text.阵亡状态不能进行该操作));
                break logic;
            }

            MeleePointTemplate pointTemplate = pointMap.get(meleeModel.getCurShippingIslandId());
            if(pointTemplate == null || (pointTemplate != null && pointTemplate.pointType != MeleeIslandType.resource)){
                rst.setResult(Text.genServerRstInfo(Text.不在对应的采集地点不能采集));
                break logic;
            }

            if (meleeModel.getShippingStatus() != SHIPPING_RECEIVE) {
                rst.setResult(Text.genServerRstInfo(Text.采集已完成不能重复采集));
                break logic;
            }
            if (meleeModel.getRemainPower() <= 0) {
                rst.setResult(Text.genServerRstInfo(Text.阵亡状态不能进行该操作));
                break logic;
            }

            //TODO 核对当前位置测试先注释
//            MeleeMapPlayerInfo mapPlayerInfo = mapPlayerInfoMap.get(player.getPlayerId());
//            LeagueMeleeIslandTemplate meleeIslandTemplate = meleeIslandTemplateMap.get(playerLeagueMeleeModel.getCurShippingIslandId());
//            LeagueMeleeIslandTemplate resourceIslandTemplate = meleeIslandTemplateMap.get(meleeIslandTemplate.resourceIslandId);
            //校验是否在任务采集海岛
//            boolean pointInCircle = LeagueMeleeHelper.isPointInCircle(mapPlayerInfo.getCurPosition(), resourceIslandTemplate.position, resourceIslandTemplate.radius);
            /*if (!pointInCircle) {
                rst.setResult(Text.genServerRstInfo(Text.不在采集海岛不能采集));
                break logic;
            }*/
            //修改任务状态
            meleeModel.setShippingStatus(SHIPPING_COLLECT);
            rst.setShipping(meleeModel.genShippingPb());
        }
        player.send(PtCode.LEAGUE_CANCEL_SHIPPING_COLLECT_RST, rst.build(), time);
    }

    /**
     * 交付运送任务
     */
    @Handler(PtCode.LEAGUE_CANCEL_SHIPPING_FINISH_REQ)
    private void leagueCancelShippingFinishFinishReq(Player player, long time) {
        PbProtocol.LeagueCancelShippingFinishRst.Builder rst = PbProtocol.LeagueCancelShippingFinishRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            int result = check(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }

            LeagueMeleeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueMelee);
            LeagueMeleeBattleInfo battleInfo = globalData.getBattleInfo();
            MeleeGroupInfo groupInfo = battleInfo.getGroupInfoByPlayer(player);

            long leagueId = LeagueManager.getLeagueId(player);
            MeleePlayerInInfo playerInInfo = groupInfo.getPlayerInMap().getOrDefault(leagueId, new HashMap<>()).get(player.getPlayerId());
            if(playerInInfo == null){
                rst.setResult(Text.genServerRstInfo(Text.不在领取任务的据点内));
                break logic;
            }

            MeleePointTemplate pointTemplate = pointMap.get(playerInInfo.getIslandId());
            if(pointTemplate == null) {
                rst.setResult(Text.genServerRstInfo(Text.对应的模板数据找不到));
                break logic;
            }

            MeleeAreaTemplate areaTemplate = areaMap.get(playerInInfo.getAreaId());
            if(areaTemplate == null || (areaTemplate != null && areaTemplate.areaType != 3)){
                rst.setResult(Text.genServerRstInfo(Text.不在领取任务的据点内));
                break logic;
            }

            PlayerLeagueMeleeModel playerLeagueMeleeModel = player.getModel(PlayerModelEnums.leagueMelee);
            if (playerLeagueMeleeModel.getShippingStatus() != SHIPPING_COLLECT) {
                rst.setResult(Text.genServerRstInfo(Text.采集未完成不能交付));
                break logic;
            }

//            if(playerLeagueMeleeModel.getRemainPower() == 0){
//                rst.setResult(Text.genServerRstInfo(Text.阵亡状态不能进行该操作));
//                break logic;
//            }
//            Integer islandId = playerInIslandMap.get(player.getPlayerId());
//            if (playerLeagueMeleeModel.getCurShippingIslandId() != islandId) {
//                rst.setResult(Text.genServerRstInfo(Text.不在交付岛屿内不能完成任务));
//                break logic;
//            }
            if (playerLeagueMeleeModel.getRemainPower() <= 0) {
                rst.setResult(Text.genServerRstInfo(Text.阵亡状态不能进行该操作));
                break logic;
            }

            List<Long> playerIdList = new ArrayList<>();
            playerIdList.add(player.getPlayerId());
            TLMessageCallbackTask finishCallback = getFinishCallback(player, playerInInfo.getIslandId(), playerInInfo.getAreaId(), time);
            MeleeGetPositionAsync.execute(playerIdList, finishCallback);
            return;
        }
        player.send(PtCode.LEAGUE_CANCEL_SHIPPING_FINISH_RST, rst.build(), time);
    }

    /**
     * 原地复活
     */
    @Handler(PtCode.LEAGUE_MELEE_REBIRTH_REQ)
    private void leagueMeleeRebirth(Player player, long time){
        PbProtocol.LeagueMeleeRebirthRst.Builder rst = PbProtocol.LeagueMeleeRebirthRst.newBuilder().setResult(Text.genOkServerRstInfo());

        logic:{
            int result = check(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }

            PlayerLeagueMeleeModel meleeModel = player.getModel(PlayerModelEnums.leagueMelee);
            if(meleeModel.getRemainPower() > 0){
                rst.setResult(Text.genServerRstInfo(Text.已经复活));
                break logic;
            }

            int rebirthPayCount = meleeModel.getRebirthPayCount();
            int num = constant.forefrontLoseCountList.size();
            int goldNum = rebirthPayCount + 1 > num ? constant.forefrontLoseCountList.get(num - 1) : constant.forefrontLoseCountList.get(rebirthPayCount + 1 - 1);
            Reward cost = new Reward(Currency.gold.getId(), -1, goldNum);
            if(cost.check(player) != -1){
                rst.setResult(Text.genServerRstInfo(Text.消耗不足));
                break logic;
            }
            cost.remove(player, BehaviorType.leagueMeleeRebirth);

            meleeModel.setRebirthTime(-1);
            meleeModel.setRemainPower(constant.beginEnergy);
            meleeModel.setRebirthPayCount(rebirthPayCount + 1);
            meleeModel.notifyPowerChange();

            rst.setRebirthNum(meleeModel.getRebirthPayCount());
            rst.setRemainPower(meleeModel.getRemainPower());
        }

        player.send(PtCode.LEAGUE_MELEE_REBIRTH_RST, rst.build(), time);
    }

    /**
     * 对手列表
     */
    @Handler(PtCode.LEAGUE_MELEE_OPPONENT_LIST_REQ)
    private void leagueMeleeOpponentListReq(Player player, long time){
        PbProtocol.LeagueMeleeOpponentListRst.Builder rst = PbProtocol.LeagueMeleeOpponentListRst.newBuilder().setResult(Text.genOkServerRstInfo());

        logic:{
            int result = check(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }

            long leagueId = LeagueManager.getLeagueId(player);
            LeagueMeleeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueMelee);
            LeagueMeleeBattleInfo battleInfo = globalData.getBattleInfo();
            MeleeGroupInfo groupInfo = battleInfo.getGroupInfoByPlayer(player);
            MeleePlayerInInfo playerInInfo = groupInfo.getPlayerInMap().getOrDefault(leagueId, new HashMap<>()).get(player.getPlayerId());
            if(playerInInfo == null){
                rst.setResult(Text.genServerRstInfo(Text.不在地图内));
                break logic;
            }

            MeleeAreaTemplate areaTemplate = areaMap.get(playerInInfo.getAreaId());
            if(areaTemplate == null){
                rst.setResult(Text.genServerRstInfo(Text.不在战斗区域内不能匹配));
                break logic;
            }

            if(areaTemplate.areaType != 1 && areaTemplate.areaType != 2){
                rst.setResult(Text.genServerRstInfo(Text.不在战斗区域内不能匹配));
                break logic;
            }

            long defLeagueId = -1;
            if(areaTemplate.areaType == 2 ){
                MeleeIslandRankInfo islandRankInfo = groupInfo.getIslandRankInfoMap().get(playerInInfo.getIslandId());
                if(islandRankInfo != null){
                    defLeagueId = islandRankInfo.getFirstLeagueIdMap().getOrDefault(playerInInfo.getAreaId(), -1L);
                }
            }


            for (Map.Entry<Long, Map<Long, MeleePlayerInInfo>> entry : groupInfo.getPlayerInMap().entrySet()) {
                if(entry.getKey() == leagueId){
                    continue;
                }

                for (MeleePlayerInInfo bean : entry.getValue().values()) {
                    PbLeagueMelee.MeleeOpponentInfo.Builder opponentInfoBuilder = PbLeagueMelee.MeleeOpponentInfo.newBuilder();
                    opponentInfoBuilder.setRobot(false);

                    long opponentPlayerId = bean.getPlayerId();
                    Player opponentPlayer = PlayerManager.getOnlinePlayer(opponentPlayerId);
                    if(opponentPlayer == null){
                        continue;
                    }

                    PbLeagueMelee.MeleePlayer.Builder meleePlayerBuilder = PbLeagueMelee.MeleePlayer.newBuilder();
                    meleePlayerBuilder.setMiniPlayerInfo(opponentPlayer.genMinMiniUser());
                    PlayerLeagueMeleeModel opponentMeleeModel = opponentPlayer.getPlayerLeagueMeleeModel();
                    int winPoint = areaTemplate.areaType == 2 ? 0 : LeagueMeleeService.constant.combatWinCountMap.get(areaTemplate.areaType) +
                            (opponentMeleeModel.getWiningStreak() >= 1 ? LeagueMeleeService.constant.combatContinuousWinCountMap.get(areaTemplate.areaType) : 0);
                    meleePlayerBuilder.setWinPoint(winPoint);
                    meleePlayerBuilder.setRemainPower(opponentMeleeModel.getRemainPower());
                    meleePlayerBuilder.setLeagueId(entry.getKey());
                    League league = LeagueManager.getLeagueById(entry.getKey());
                    if(league != null){
                        meleePlayerBuilder.setLeagueName(league.getName());
                    }
                    meleePlayerBuilder.setIsDef(defLeagueId == entry.getKey());
                    opponentInfoBuilder.setMinUser(meleePlayerBuilder.build());
                    rst.addOpponentInfo(opponentInfoBuilder.build());
                }
            }

            //增加机器人对手
            MeleeRobotTemplate robotTemplate = getMeleeRobotTemplateByLevel(player.getLevel());
            if(robotTemplate != null){
                PbLeagueMelee.MeleeOpponentInfo.Builder opponentInfoBuilder = PbLeagueMelee.MeleeOpponentInfo.newBuilder();
                opponentInfoBuilder.setRobot(true);
                //前端要使用id
                opponentInfoBuilder.setRobotId(robotTemplate.id);
                rst.addOpponentInfo(opponentInfoBuilder.build());
            }
        }

        player.send(PtCode.LEAGUE_MELEE_OPPONENT_LIST_RST, rst.build(), time);
    }

    /**
     * 选择对手
     */
    @Handler(PtCode.LEAGUE_MELEE_SELECT_OPPONENT_REQ)
    private void leagueMeleeSelectOpponentReq(Player player, PbProtocol.LeagueMeleeSelectOpponentReq req, long time){
        PbProtocol.LeagueMeleeSelectOpponentRst.Builder rst = PbProtocol.LeagueMeleeSelectOpponentRst.newBuilder().setResult(Text.genOkServerRstInfo());
        long selectPlayerId = req.getSelectPlayerId();
        boolean isRobot = req.getRobot();

        logic:{
            int result = check(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }

            long leagueId = LeagueManager.getLeagueId(player);
            LeagueMeleeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueMelee);
            LeagueMeleeBattleInfo battleInfo = globalData.getBattleInfo();
            MeleeGroupInfo groupInfo = battleInfo.getGroupInfoByPlayer(player);
            MeleePlayerInInfo playerInInfo = groupInfo.getPlayerInMap().getOrDefault(leagueId, new HashMap<>()).get(player.getPlayerId());
            if(playerInInfo == null){
                rst.setResult(Text.genServerRstInfo(Text.不在地图内));
                break logic;
            }

            if(player.getPlayerLeagueMeleeModel().getRemainPower() == 0){
                rst.setResult(Text.genServerRstInfo(Text.阵亡状态不能进行该操作));
                break logic;
            }

            MeleeIslandAreaOpenInfo islandAreaOpenInfo = groupInfo.getIslandAreaOpenInfoMap().get(playerInInfo.getIslandId());
            if(islandAreaOpenInfo == null){
                rst.setResult(Text.genServerRstInfo(Text.不在对应区域));
                break logic;
            }

            if(!islandAreaOpenInfo.getAreaEndMap().containsKey(playerInInfo.getAreaId())){
                rst.setResult(Text.genServerRstInfo(Text.区域未开启));
                break logic;
            }

            if(islandAreaOpenInfo.getAreaEndMap().get(playerInInfo.getAreaId()) < ServerConstants.getCurrentTimeMillis()){
                rst.setResult(Text.genServerRstInfo(Text.区域已关闭));
                break logic;
            }

            MeleeStage stage = null;
            if(isRobot){
                stage = new MeleeStage(player, true, null, playerInInfo.getIslandId(), playerInInfo.getAreaId(), groupInfo);
            }else {
                long selectLeagueId = LeagueManager.getLeagueId(selectPlayerId);
                if(selectLeagueId == 0){
                    rst.setResult(Text.genServerRstInfo(Text.参数异常));
                    break logic;
                }

                MeleePlayerInInfo selectPlayerInInfo = groupInfo.getPlayerInMap().getOrDefault(leagueId, new HashMap<>()).get(player.getPlayerId());
                if(selectPlayerInInfo == null){
                    rst.setResult(Text.genServerRstInfo(Text.选定对手已离开));
                    break logic;
                }

                Player selectPlayer = PlayerManager.getOnlinePlayer(selectPlayerId);
                if(selectPlayer == null){
                    rst.setResult(Text.genServerRstInfo(Text.选定对手已离开));
                    break logic;
                }

                stage = new MeleeStage(player, false, selectPlayer, playerInInfo.getIslandId(), playerInInfo.getAreaId(), groupInfo);
            }

            stage.init();

            rst.setStage(stage.getStageRecord());
            CombatManager.combatPrepare(stage);
            if(!isRobot){
                LeagueMeleeHelper.beFightMapChange(selectPlayerId, player.getPlayerId(), player.getName(), true);
            }
        }

        player.send(PtCode.LEAGUE_MELEE_SELECT_OPPONENT_RST, rst.build(), time);
    }

    /**
     * 打boss区boss
     */
    @Handler(PtCode.LEAGUE_MELEE_ATTACK_BOSS_REQ)
    private void leagueMeleeAttackBossReq(Player player, long time){
        PbProtocol.LeagueMeleeAttackBossRst.Builder rst = PbProtocol.LeagueMeleeAttackBossRst.newBuilder().setResult(Text.genOkServerRstInfo());

        logic:{
            int result = check(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }

            long leagueId = LeagueManager.getLeagueId(player);
            LeagueMeleeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueMelee);
            LeagueMeleeBattleInfo battleInfo = globalData.getBattleInfo();
            MeleeGroupInfo groupInfo = battleInfo.getGroupInfoByPlayer(player);
            MeleePlayerInInfo playerInInfo = groupInfo.getPlayerInMap().getOrDefault(leagueId, new HashMap<>()).get(player.getPlayerId());
            if(playerInInfo == null){
                rst.setResult(Text.genServerRstInfo(Text.不在地图内));
                break logic;
            }

            if(player.getPlayerLeagueMeleeModel().getRemainPower() == 0){
                rst.setResult(Text.genServerRstInfo(Text.阵亡状态不能进行该操作));
                break logic;
            }

            MeleeAreaTemplate areaTemplate = areaMap.get(playerInInfo.getAreaId());
            if(areaTemplate == null){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }

            if(areaTemplate.areaType != 2){
                rst.setResult(Text.genServerRstInfo(Text.不在对应区域));
                break logic;
            }

            MeleeIslandAreaOpenInfo islandAreaOpenInfo = groupInfo.getIslandAreaOpenInfoMap().get(playerInInfo.getIslandId());
            if(islandAreaOpenInfo == null){
                rst.setResult(Text.genServerRstInfo(Text.不在对应区域));
                break logic;
            }

            if(!islandAreaOpenInfo.getAreaEndMap().containsKey(playerInInfo.getAreaId())){
                rst.setResult(Text.genServerRstInfo(Text.区域未开启));
                break logic;
            }

            if(islandAreaOpenInfo.getAreaEndMap().get(playerInInfo.getAreaId()) < ServerConstants.getCurrentTimeMillis()){
                rst.setResult(Text.genServerRstInfo(Text.区域已关闭));
                break logic;
            }

            MeleeBossStage stage = new MeleeBossStage(player, playerInInfo.getIslandId(), playerInInfo.getAreaId(), areaTemplate.battleCollect, groupInfo);
            stage.init();

            rst.setStage(stage.getStageRecord());
            CombatManager.combatPrepare(stage);
            groupInfo.addFightingBossPlayer(playerInInfo.getIslandId(), areaTemplate.id, player.getPlayerId());
        }
        player.send(PtCode.LEAGUE_MELEE_ATTACK_BOSS_RST, rst.build(), time);
    }

    /**
     * 获取boss血量信息
     */
    @Handler(PtCode.LEAGUE_MELEE_BOSS_HP_REQ)
    private void leagueMeleeBossHpReq(Player player, PbProtocol.LeagueMeleeBossHpReq req, long time){
        PbProtocol.LeagueMeleeBossHpRst.Builder rst = PbProtocol.LeagueMeleeBossHpRst.newBuilder().setResult(Text.genOkServerRstInfo());
        int islandId = req.getIslandId();
        int areaId = req.getAreaId();

        logic:{
            rst.setAreaId(areaId);

            int result = check(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }

            MeleePointTemplate pointTemplate = pointMap.get(islandId);
            if(pointTemplate == null){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }

            LeagueMeleeGlobalData globalData = GlobalDataManager.getData(GlobalDataType.leagueMelee);
            LeagueMeleeBattleInfo battleInfo = globalData.getBattleInfo();
            if(pointTemplate.seasonId != battleInfo.getSeasonId()){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }

            MeleeAreaTemplate areaTemplate = areaMap.get(areaId);
            if(areaTemplate == null){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }

            if(areaTemplate.areaType != 2){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }

            if(!pointTemplate.pointAreaIds.contains(areaId)){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }

            MeleeGroupInfo groupInfo = battleInfo.getGroupInfoByPlayer(player);

            String key = islandId + "" + areaId;
            MeleeBossAreaInfo bossAreaInfo = groupInfo.getBossAreaInfoMap().get(key);
            if(bossAreaInfo == null){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            rst.putAllHpMap(bossAreaInfo.getBossHpMap());
        }
        player.send(PtCode.LEAGUE_MELEE_BOSS_HP_RST, rst.build(), time);
    }

    /**
     * 前端标记
     */
    @Handler(PtCode.LEAGUE_MELEE_CLIENT_SIGN_REQ)
    private void leagueMeleeClientSignReq(Player player, PbProtocol.LeagueMeleeClientSignReq req, long time){
        PbProtocol.LeagueMeleeClientSignRst.Builder rst = PbProtocol.LeagueMeleeClientSignRst.newBuilder().setResult(Text.genOkServerRstInfo());
        boolean clientSign = req.getClientSign();

        PlayerLeagueMeleeModel model = player.getPlayerLeagueMeleeModel();
        model.setClientSign(clientSign);
        rst.setClientSign(model.isClientSign());
        player.send(PtCode.LEAGUE_MELEE_CLIENT_SIGN_RST, rst.build(), time);
    }

    /**
     * 开始复活
     */
    @Handler(PtCode.LEAGUE_MELEE_START_REBIRTH_REQ)
    private void leagueMeleeStartRebirthReq(Player player, PbProtocol.LeagueMeleeStartRebirthReq req, long time){
        PbProtocol.LeagueMeleeStartRebirthRst.Builder rst = PbProtocol.LeagueMeleeStartRebirthRst.newBuilder().setResult(Text.genOkServerRstInfo());

        logic:{
            int result = check(player);
            if (result != -1) {
                rst.setResult(Text.genServerRstInfo(result));
                break logic;
            }

            PlayerLeagueMeleeModel model = player.getPlayerLeagueMeleeModel();
            if(model.getRemainPower() == 0){
                //死亡
                long rebirthTime = ServerConstants.getCurrentTimeMillis() + DateTimeUtil.MillisOfSecond * LeagueMeleeService.getConstant().resurrectionTime;
                model.setRebirthTime(rebirthTime);
            }

            rst.setRebirthTime(model.getRebirthTime());
            rst.setRemainPower(model.getRemainPower());
        }

        player.send(PtCode.LEAGUE_MELEE_START_REBIRTH_RST, rst.build(), time);
    }

    /**
     * 获取帮派荣誉值排行榜
     *
     * @param player    玩家信息
     * @param groupInfo 分组信息
     * @return Key 排行信息 Value 我所在的排名
     */
    private Pair<List<PbLeagueMelee.LeagueRankNode>, PbLeagueMelee.LeagueRankNode> getLeagueHonorRank(Player player, MeleeGroupInfo groupInfo) {
        List<PbLeagueMelee.LeagueRankNode> returnList = new ArrayList<>();
        Map<Long, MeleeLeagueHonorRankNode> honorRankMap = groupInfo.getHonorRankMap();
        List<MeleeLeagueHonorRankNode> leagueRankList = new ArrayList<>(honorRankMap.values());
        Collections.sort(leagueRankList);
        List<MeleeLeagueHonorRankNode> showRankList = leagueRankList.stream()
                .limit(constant.honorRankNum).collect(Collectors.toList());
        for (int i = 1; i <= showRankList.size(); i++) {
            MeleeLeagueHonorRankNode rankNode = showRankList.get(i - 1);
            returnList.add(LeagueMeleeHelper.genLeagueRankPb(rankNode, i));
        }
        PbLeagueMelee.LeagueRankNode myNode = null;
        League myLeague = LeagueManager.getLeagueByPlayer(player);
        if (honorRankMap.containsKey(myLeague.getLeagueId())) {
            MeleeLeagueHonorRankNode myRankNode = honorRankMap.get(myLeague.getLeagueId());
            myNode = LeagueMeleeHelper.genLeagueRankPb(myRankNode, leagueRankList.indexOf(myRankNode) + 1);
        }
        return Pair.of(returnList, myNode);
    }

    /**
     * 获取帮派积分排行榜
     *
     * @param player    玩家信息
     * @param groupInfo 分组信息
     * @return Key 排行信息 Value 我所在的排名
     */
    private Pair<List<PbLeagueMelee.LeagueRankNode>, PbLeagueMelee.LeagueRankNode> getLeagueScoreRank(Player player, MeleeGroupInfo groupInfo,
                                                                                                      int islandId, int areaId) {
        List<PbLeagueMelee.LeagueRankNode> returnList = new ArrayList<>();
        PbLeagueMelee.LeagueRankNode myNode = null;
        Map<Integer, MeleeIslandRankInfo> islandRankInfoMap = groupInfo.getIslandRankInfoMap();
        if (islandRankInfoMap.containsKey(islandId)) {
            MeleeIslandRankInfo meleeIslandRankInfo = islandRankInfoMap.get(islandId);
            MeleeIslandAreaRankInfo meleeIslandAreaRankInfo = meleeIslandRankInfo.getAreaRankInfoMap().get(areaId);
            if (Objects.nonNull(meleeIslandAreaRankInfo)) {
                Map<Long, MeleeLeagueAreaRankNode> leagueAreaRankMap = meleeIslandAreaRankInfo.getLeagueAreaRankMap();
                List<MeleeLeagueAreaRankNode> leagueRankList = new ArrayList<>(leagueAreaRankMap.values());
                Collections.sort(leagueRankList);
                List<MeleeLeagueAreaRankNode> showRankList = leagueRankList.stream().limit(constant.countRankNum).collect(Collectors.toList());
                for (int i = 1; i <= showRankList.size(); i++) {
                    MeleeLeagueAreaRankNode rankNode = showRankList.get(i - 1);
                    returnList.add(LeagueMeleeHelper.genLeagueRankPb(rankNode, i));
                }
                League myLeague = LeagueManager.getLeagueByPlayer(player);
                if (leagueAreaRankMap.containsKey(myLeague.getLeagueId())) {
                    MeleeLeagueAreaRankNode myRankNode = leagueAreaRankMap.get(myLeague.getLeagueId());
                    myNode = LeagueMeleeHelper.genLeagueRankPb(myRankNode, leagueRankList.indexOf(myRankNode) + 1);
                }
            }
        }
        return Pair.of(returnList, myNode);
    }

    private TLMessageCallbackTask getFinishCallback(Player player, int islandId, int areaId, long time) {
        int ptCode = PtCode.LEAGUE_CANCEL_SHIPPING_FINISH_RST;
        PbProtocol.LeagueCancelShippingFinishRst.Builder rst = PbProtocol.LeagueCancelShippingFinishRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        return new FinishCallbackTask(player, islandId, areaId, ptCode, rst, time);
    }

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.leagueMelee_season);
        Map<Integer, MeleeSeasonTemplate> seasonMapTemp = new HashMap();
        for (Map<String, String> map : mapList) {
            MeleeSeasonTemplate template = new MeleeSeasonTemplate(map);
            seasonMapTemp.put(template.id, template);
        }
        seasonMap = seasonMapTemp;

        mapList = ConfigReader.read(ConfigFile.leagueMelee_gameTime);
        Map<Integer, Map<Integer, MeleeGameTimeTemplate>> gameTimeMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            MeleeGameTimeTemplate template = new MeleeGameTimeTemplate(map);
            gameTimeMapTemp.computeIfAbsent(template.seasonId, bean -> new HashMap<>()).put(template.id, template);
        }
        gameTimeMap = gameTimeMapTemp;

        mapList = ConfigReader.read(ConfigFile.leagueMelee_monster);
        Map<Integer, Map<Integer, MeleeMonsterTemplate>> monsterMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            MeleeMonsterTemplate template = new MeleeMonsterTemplate(map);
            monsterMapTemp.computeIfAbsent(template.seasonId, bean -> new HashMap<>()).put(template.id, template);
        }
        monsterMap = monsterMapTemp;

        mapList = ConfigReader.read(ConfigFile.leagueMelee_robot);
        TreeMap<Integer, MeleeRobotTemplate> robotMapTemp = new TreeMap<>();
        for (Map<String, String> map : mapList) {
            MeleeRobotTemplate template = new MeleeRobotTemplate(map);
            robotMapTemp.put(template.level.getValue(), template);
        }
        robotMap = robotMapTemp;

        mapList = ConfigReader.read(ConfigFile.leagueMelee_point);
        Map<Integer, MeleePointTemplate> pointMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            MeleePointTemplate template = new MeleePointTemplate(map);
            pointMapTemp.put(template.id, template);
        }
        pointMap = pointMapTemp;

        mapList = ConfigReader.read(ConfigFile.leagueMelee_area);
        Map<Integer, MeleeAreaTemplate> areaMapTemp = new HashMap();
        for (Map<String, String> map : mapList) {
            MeleeAreaTemplate template = new MeleeAreaTemplate(map);
            areaMapTemp.put(template.id, template);
        }
        areaMap = areaMapTemp;

        mapList = ConfigReader.read(ConfigFile.leagueMelee_reward);
        Map<Integer, MeleeRewardTemplate> rewardMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            MeleeRewardTemplate template = new MeleeRewardTemplate(map);
            rewardMapTemp.put(template.id, template);
        }
        rewardMap = rewardMapTemp;

        mapList = ConfigReader.read(ConfigFile.leagueMelee_leaguereward);
        Map<Integer, MeleeLeagueRewardTemplate> leagueRewardMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            MeleeLeagueRewardTemplate template = new MeleeLeagueRewardTemplate(map);
            leagueRewardMapTemp.put(template.id, template);
        }
        leagueRewardMap = leagueRewardMapTemp;

        Map<String, String> map = ConstantConfigReader.read(ConfigFile.leaguemelee_const);
        constant = new MeleeConstant(map);
    }

    @Override
    public void clearConfigData() {
        seasonMap.clear();
        gameTimeMap.clear();
        monsterMap.clear();
        robotMap.clear();
        pointMap.clear();
        areaMap.clear();
        rewardMap.clear();
        leagueRewardMap.clear();
        constant = null;
    }

    /**
     * 根据等级获取机器人
     * @param level
     * @return 需要判空
     */
    public static MeleeRobotTemplate getMeleeRobotTemplateByLevel(int level){
        Integer firstKey = robotMap.tailMap(level).firstKey();
        return firstKey == null ? null : robotMap.get(firstKey);
    }

    public static Map<Integer, MeleeSeasonTemplate> getSeasonMap() {
        return seasonMap;
    }

    public static Map<Integer, Map<Integer, MeleeGameTimeTemplate>> getGameTimeMap() {
        return gameTimeMap;
    }

    public static List<MeleeGameTimeTemplate> getGameTimeListBySeason(int seasonId) {
        List<MeleeGameTimeTemplate> list = new ArrayList<>(gameTimeMap.getOrDefault(seasonId, new HashMap<>()).values());
        list.sort(Comparator.comparingInt(bean -> bean.id));
        return list;
    }

    public static Map<Integer, Map<Integer, MeleeMonsterTemplate>> getMonsterMap() {
        return monsterMap;
    }

    public static TreeMap<Integer, MeleeRobotTemplate> getRobotMap() {
        return robotMap;
    }

    public static Map<Integer, MeleePointTemplate> getBrithPointMap() {
        return brithPointMap;
    }

    public static Map<Integer, MeleePointTemplate> getPointMap() {
        return pointMap;
    }

    public static Map<Integer, MeleePointTemplate> getPointMapBySeasonId(int seasonId){
        Map<Integer, MeleePointTemplate> pointMapTemp = new HashMap<>();
        LeagueMeleeService.getPointMap().values().forEach(bean -> {
            if(seasonId == bean.seasonId){
                pointMapTemp.put(bean.id, bean);
            }
        });
        return pointMapTemp;
    }

    public static Map<Integer, MeleeAreaTemplate> getAreaMap() {
        return areaMap;
    }


    public static Map<Integer, MeleeRewardTemplate> getRewardMap() {
        return rewardMap;
    }

    public static Map<Integer, MeleeLeagueRewardTemplate> getLeagueRewardMap() {
        return leagueRewardMap;
    }

    public static MeleeConstant getConstant() {
        return constant;
    }

    private static class MonsterFightCallbackTask extends TLMessageCallbackTask {
        private final Player player;
        private final PbProtocol.LeagueMeleePiratesFightRst.Builder rst;
        private final MeleeGroupInfo groupInfo;
        private final MeleeMonsterTemplate monsterTemplate;
        private final long uid;
        private final PlayerLeagueMeleeModel playerLeagueMeleeModel;
        private final long time;

        public MonsterFightCallbackTask(Player player, PbProtocol.LeagueMeleePiratesFightRst.Builder rst, MeleeGroupInfo groupInfo, MeleeMonsterTemplate monsterTemplate, long uid, PlayerLeagueMeleeModel playerLeagueMeleeModel, long time) {
            this.player = player;
            this.rst = rst;
            this.groupInfo = groupInfo;
            this.monsterTemplate = monsterTemplate;
            this.uid = uid;
            this.playerLeagueMeleeModel = playerLeagueMeleeModel;
            this.time = time;
        }

        @Override
        public void complete(CallbackResponse response) {
            byte[] bytes = response.getParam(0);
            PlayerPositionMap positionMap = PbUtilCompress.decode(PlayerPositionMap.class, bytes);
            Map<Long, ScenePosition3D> position3DMap = positionMap.getPosition3DMap();
            ScenePosition3D scenePosition3D = position3DMap.get(player.getPlayerId());

            if(scenePosition3D == null){
                rst.setResult(Text.genServerRstInfo(Text.场景位置异常));
                player.send(PtCode.LEAGUE_MELEE_PIRATES_FIGHT_RST, rst.build(), time);
                return;
            }

            MeleeMapPosition myPosition = new MeleeMapPosition(scenePosition3D.getX(), scenePosition3D.getZ());
            for (MeleePiratesInfo bean : groupInfo.getMeleePiratesInfoMap().values()) {
                MeleeMapPosition monsterPosition = bean.getPosition();
                boolean inCircle = LeagueMeleeHelper.isPointInCircle(myPosition, monsterPosition, monsterTemplate.radius);
                if(inCircle){
                    MeleePiratesStage stage = new MeleePiratesStage(player, uid, monsterTemplate, playerLeagueMeleeModel.getBuffIdMap());
                    stage.init();
                    rst.setStage(stage.getStageRecord());
                    player.send(PtCode.LEAGUE_MELEE_PIRATES_FIGHT_RST, rst.build(), time);
                    CombatManager.combatPrepare(stage);
                    groupInfo.addFightingPiratesPlayer(uid, player.getPlayerId());
                    return;
                }
            }

            rst.setResult(Text.genServerRstInfo(Text.不在野怪圆形区域内));
            player.send(PtCode.LEAGUE_MELEE_PIRATES_FIGHT_RST, rst.build(), time);
        }

        @Override
        public void timeout() {
            rst.setResult(Text.genServerRstInfo(Text.场景服响应超时));
            player.send(PtCode.LEAGUE_MELEE_PIRATES_FIGHT_RST, rst.build(), time);
        }
    }

    private static class ShippingReceiveCallbackTask extends TLMessageCallbackTask {
        private final Player player;
        private final int targetIslandId;
        private final PbProtocol.LeagueCancelShippingReceiveRst.Builder rst;
        private final long time;

        public ShippingReceiveCallbackTask(Player player, int targetIslandId, PbProtocol.LeagueCancelShippingReceiveRst.Builder rst, long time) {
            this.player = player;
            this.targetIslandId = targetIslandId;
            this.rst = rst;
            this.time = time;
        }

        @Override
        public void complete(CallbackResponse response) {
            byte[] bytes = response.getParam(0);
            PlayerPositionMap positionMap = PbUtilCompress.decode(PlayerPositionMap.class, bytes);
            Map<Long, ScenePosition3D> position3DMap = positionMap.getPosition3DMap();
            // TODO 测试先注释
            /*
            ScenePosition3D scenePosition3D = position3DMap.get(player.getPlayerId());
            if (Objects.isNull(scenePosition3D)) {
                rst.setResult(Text.genServerRstInfo(Text.场景服获取坐标超时));
                player.send(PtCode.LEAGUE_MELEE_SHIPPING_RECEIVE_SERVER, rst.build());
                return;
            }
            MeleeMapPosition meleeMapPosition = new MeleeMapPosition();
            meleeMapPosition.setX(scenePosition3D.getX());
            meleeMapPosition.setY(scenePosition3D.getZ());
            Map<MeleeIslandAreaType, LeagueMeleeIslandAreaTemplate> arenaTemplateMap = LeagueMeleeService.getMeleeIslandArenaByIslandTemplateMap().get(islandId);
            LeagueMeleeIslandAreaTemplate arenaTemplate = arenaTemplateMap.get(MeleeIslandAreaType.backup);
            //校验是否在领取任务区域
            boolean pointInCircle = LeagueMeleeHelper.isPointInCircle(meleeMapPosition, arenaTemplate.position, arenaTemplate.radius);

            if (!pointInCircle) {
                rst.setResult(Text.genServerRstInfo(Text.不在后援区));
                player.send(PtCode.LEAGUE_MELEE_SHIPPING_RECEIVE_SERVER, rst.build());
                return;
            }*/
            //领取任务
            PlayerLeagueMeleeModel meleeModel = player.getModel(PlayerModelEnums.leagueMelee);
            meleeModel.setCurShippingIslandId(targetIslandId);
            meleeModel.setShippingStatus(SHIPPING_RECEIVE);
            rst.setShipping(meleeModel.genShippingPb());
            player.send(PtCode.LEAGUE_CANCEL_SHIPPING_RECEIVE_RST, rst.build(), time);
        }

        @Override
        public void timeout() {
            rst.setResult(Text.genServerRstInfo(Text.场景服响应超时));
            player.send(PtCode.LEAGUE_CANCEL_SHIPPING_RECEIVE_RST, rst.build(), time);
        }
    }

    private static class FinishCallbackTask extends TLMessageCallbackTask {
        private final Player player;
        private final int islandId;
        private final int areaId;
        private final int ptCode;
        private final PbProtocol.LeagueCancelShippingFinishRst.Builder rst;

        private long time;

        public FinishCallbackTask(Player player, int islandId, int areaId, int ptCode, PbProtocol.LeagueCancelShippingFinishRst.Builder rst, long time) {
            this.player = player;
            this.islandId = islandId;
            this.areaId = areaId;
            this.ptCode = ptCode;
            this.rst = rst;
            this.time = time;
        }

        @Override
        public void complete(CallbackResponse response) {
            byte[] bytes = response.getParam(0);
            PlayerPositionMap positionMap = PbUtilCompress.decode(PlayerPositionMap.class, bytes);
            Map<Long, ScenePosition3D> position3DMap = positionMap.getPosition3DMap();
            // TODO 测试先注释
            /*
            ScenePosition3D scenePosition3D = position3DMap.get(player.getPlayerId());
            if (Objects.isNull(scenePosition3D)) {
                rst.setResult(Text.genServerRstInfo(Text.场景服获取坐标超时));
                player.send(ptCode, rst.build());
                return;
            }
            MeleeMapPosition meleeMapPosition = new MeleeMapPosition();
            meleeMapPosition.setX(scenePosition3D.getX());
            meleeMapPosition.setY(scenePosition3D.getZ());
            LeagueMeleeIslandTemplate meleeIslandTemplate = LeagueMeleeService.getMeleeIslandTemplateMap().get(islandId);
            //校验是否在领取任务区域
            boolean pointInCircle = LeagueMeleeHelper.isPointInCircle(meleeMapPosition, meleeIslandTemplate.position, meleeIslandTemplate.radius);
            if (!pointInCircle) {
                rst.setResult(Text.genServerRstInfo(Text.不在领取任务的岛屿内));
                player.send(ptCode, rst.build());
                return;
            }*/
            //交付任务
            PlayerLeagueMeleeModel playerLeagueMeleeModel = player.getModel(PlayerModelEnums.leagueMelee);
            playerLeagueMeleeModel.setCurShippingIslandId(-1);
            playerLeagueMeleeModel.setShippingStatus(-1);
            playerLeagueMeleeModel.addShippingNum(1);
            //增加积分
//                LeagueMeleeConstant constant1 = LeagueMeleeService.getConstant();
            LeagueMeleeHelper.addBackupScore(islandId, areaId, player);
//                rst.setHonor(constant1.backupHonor)
//                        .setScore(constant1.backupScore);
            player.send(ptCode, rst.build(), time);
        }

        @Override
        public void timeout() {
            rst.setResult(Text.genServerRstInfo(Text.场景服响应超时));
            player.send(ptCode, rst.build(), time);
        }
    }
}

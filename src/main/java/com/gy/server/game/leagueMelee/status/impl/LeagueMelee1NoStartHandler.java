package com.gy.server.game.leagueMelee.status.impl;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.battleRule.base.IBattleStatusDeal;
import com.gy.server.game.leagueMelee.LeagueMeleeService;
import com.gy.server.game.leagueMelee.template.MeleeConstant;
import com.gy.server.game.leagueMelee.status.LeagueMeleeBattleInfo;
import com.gy.server.game.leagueMelee.status.MeleeNodeType;
import com.ttlike.server.tl.baselib.serialize.leagueMelee.MeleeStatusType;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * 未开启状态
 *
 * <AUTHOR> - [Created on 2023/7/18 14:23]
 */
public class LeagueMelee1NoStartHandler implements IBattleStatusDeal<LeagueMeleeBattleInfo> {

    @Override
    public void init(LeagueMeleeBattleInfo battleInfo) {

    }

    @Override
    public void deal(LeagueMeleeBattleInfo battleInfo) {
        //获取开启时间
        LocalDateTime nowLocal = ServerConstants.getCurrentTimeLocalDateTime();
        MeleeConstant constant = LeagueMeleeService.getConstant();
        if(constant == null){
            return;
        }

        if(ServerConstants.getCurrentTimeMillis() < battleInfo.getSignTime() && battleInfo.getSignTime() != 0l){
            return;
        }

        int onTimeWeek = LeagueMeleeService.getConstant().onTimeWeek;
        if (nowLocal.getDayOfWeek() == DayOfWeek.of(onTimeWeek)) {
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("HH:mm");
            LocalTime nowLocalTime = LocalTime.parse(nowLocal.toLocalTime().format(dateTimeFormatter));

            LocalTime openTime = LeagueMeleeService.getConstant().onTimeMap.get(MeleeNodeType.ready).getKey();
            LocalTime endTime = LeagueMeleeService.getConstant().onTimeMap.get(MeleeNodeType.endAndClear).getKey();

            if (nowLocalTime.isAfter(openTime) && nowLocalTime.isBefore(endTime)) {
                //到达开启时间设置为开启状态
                battleInfo.setOpen(true);

                long signTime = ServerConstants.getNowHourMinuteTime(openTime.getHour(), openTime.getMinute());
                battleInfo.setSignTime(signTime);
            }
        }else {
            if(battleInfo.isOpen()) battleInfo.setOpen(false);
        }
    }

    @Override
    public boolean finish(LeagueMeleeBattleInfo battleInfo) {
        return battleInfo.isOpen();
    }

    @Override
    public int nowStatus() {
        return MeleeStatusType.noStart.getType();
    }

    @Override
    public int nextStatus(LeagueMeleeBattleInfo battleInfo) {
        return MeleeStatusType.ready.getType();
    }

    @Override
    public long getStatusDurationTime(LeagueMeleeBattleInfo battleInfo) {
        return -1;
    }


}

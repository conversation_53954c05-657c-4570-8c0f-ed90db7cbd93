package com.gy.server.game.leagueMelee.status.impl;

import com.gy.server.core.battleRule.base.IBattleStatusDeal;
import com.gy.server.game.leagueMelee.LeagueMeleeService;
import com.gy.server.game.leagueMelee.status.LeagueMeleeBattleInfo;
import com.gy.server.game.leagueMelee.status.MeleeNodeType;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.serialize.leagueMelee.MeleeStatusType;

/**
 * 终止
 *
 * <AUTHOR> - [Created on 2023/7/18 18:03]
 */
public class LeagueMelee13AbortHandler implements IBattleStatusDeal<LeagueMeleeBattleInfo> {

    private long startTime;

    @Override
    public void init(LeagueMeleeBattleInfo battleInfo) {
        this.startTime = battleInfo.getStatusStartTime(getMeleeNodeType());
    }

    @Override
    public void deal(LeagueMeleeBattleInfo battleInfo) {

    }

    /**
     * 等待超时
     * @param battleInfo
     * @return
     */
    @Override
    public boolean finish(LeagueMeleeBattleInfo battleInfo) {
        return false;
    }

    @Override
    public int nowStatus() {
        return MeleeStatusType.abort.getType();
    }

    @Override
    public int nextStatus(LeagueMeleeBattleInfo battleInfo) {
        return MeleeStatusType.noStart.getType();
    }

    @Override
    public long getStatusDurationTime(LeagueMeleeBattleInfo battleInfo) {
        return LeagueMeleeService.getConstant().onTimeMap.get(getMeleeNodeType()).getRight() * DateTimeUtil.MinutesOfHour;
    }

    @Override
    public long getStatusStartTime() {
        return startTime;
    }

    public MeleeNodeType getMeleeNodeType(){
        return MeleeNodeType.endAndClear;
    }
    
}

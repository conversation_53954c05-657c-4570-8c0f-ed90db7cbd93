package com.gy.server.game.heroSpectrum;

import java.util.*;

import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModel;
import com.gy.server.game.player.event.PlayerEvent;
import com.gy.server.game.player.event.PlayerEventHandler;
import com.gy.server.game.player.event.PlayerEventType;
import com.ttlike.server.tl.baselib.serialize.player.HeroSpectrumModelDb;
import com.ttlike.server.tl.baselib.serialize.player.PlayerBlobDb;

/**
 * <AUTHOR> - [Created on 2022/11/17 9:51]
 */
public class PlayerHeroSpectrumModel extends PlayerModel implements PlayerEventHandler {

    private static final PlayerEventType[] eventTypes = new PlayerEventType[]{};

    /**
     * 英雄谱等级
     */
    private Set<Integer> heroSpectrumLvMap = new HashSet<>();

    public PlayerHeroSpectrumModel(Player player) {
        super(player);
    }

    @Override
    protected void loadData(PlayerBlobDb playerBlob) {
        HeroSpectrumModelDb heroSpectrumModelDb = playerBlob.getHeroSpectrumModelDb();
        if (Objects.isNull(heroSpectrumModelDb)){
            return;
        }
        this.heroSpectrumLvMap = new HashSet<>(heroSpectrumModelDb.getHeroSpectrumLvMap().keySet());
    }

    @Override
    protected void saveData(PlayerBlobDb playerBlob) {
        HeroSpectrumModelDb heroSpectrumModelDb = new HeroSpectrumModelDb();
        for (Integer i : heroSpectrumLvMap) {
            heroSpectrumModelDb.getHeroSpectrumLvMap().put(i, -1);
        }

        playerBlob.setHeroSpectrumModelDb(heroSpectrumModelDb);
    }

    @Override
    public PlayerEventType[] getEventTypes() {
        return eventTypes;
    }

    @Override
    public void handle(PlayerEvent event) {

    }

    public Set<Integer> getHeroSpectrumLvMap() {
        return heroSpectrumLvMap;
    }

    public void setHeroSpectrumLvMap(Set<Integer> heroSpectrumLvMap) {
        this.heroSpectrumLvMap = heroSpectrumLvMap;
    }

}

package com.gy.server.game.heroSpectrum;

import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.function.Function;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.heroSpectrum.template.HeroSpectrumTemplate;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.service.Service;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.ttlike.server.tl.baselib.serialize.combatAddition.CombatAdditions;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> - [Created on 2022/11/17 10:16]
 */
public class HeroSpectrumService extends PlayerPacketHandler implements Service {

    /**
     * 英雄谱
     * star:info
     */
    private static Map<Integer, HeroSpectrumTemplate> heroSpectrumTemplateMap = new HashMap<>();

    public static int oneClickLevel;


    /**
     * 英雄谱激活/升星
     */
    @Handler(PtCode.HERO_SPECTRUM_LEVEL_CLIENT)
    private void heroSpectrum(Player player, PbProtocol.HeroSpectrumLevelReq req, long time) {
        PbProtocol.HeroSpectrumLevelRst.Builder rst = PbProtocol.HeroSpectrumLevelRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        int star = req.getStarNum();
        logic:
        {
            if (Function.heroSpectrum.isNotOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            if(star > 0){
                HeroSpectrumTemplate template = heroSpectrumTemplateMap.get(star);
                if (template == null) {
                    rst.setResult(Text.genServerRstInfo(Text.参数异常));
                    break logic;
                }
                PlayerHeroSpectrumModel heroModel = player.getModel(PlayerModelEnums.heroSpectrum);
                Set<Integer> heroSpectrumLvMap = heroModel.getHeroSpectrumLvMap();
                if (heroSpectrumLvMap.contains(star)) {
                    rst.setResult(Text.genServerRstInfo(Text.参数异常));
                    break logic;
                }
                int allHeroStar = player.getBagModel().getAllHeroStar();
                if(star > allHeroStar){
                    rst.setResult(Text.genServerRstInfo(Text.参数异常));
                    break logic;
                }
                heroSpectrumLvMap.add(star);
            }else{
                //一键升级
                int allHeroStar = player.getBagModel().getAllHeroStar();
                PlayerHeroSpectrumModel heroModel = player.getModel(PlayerModelEnums.heroSpectrum);
                Set<Integer> heroSpectrumLvMap = heroModel.getHeroSpectrumLvMap();
                for (Integer i : heroSpectrumTemplateMap.keySet()) {
                    if(allHeroStar >= i){
                        if(!heroSpectrumLvMap.contains(i)){
                            //可以激活
                            heroSpectrumLvMap.add(i);
                        }
                        star = Math.max(i,star);
                    }
                }
            }

            player.postEvent(PlayerEventType.heroSpectrum, star);
            rst.setStarNum(star);

        }


        player.send(PtCode.HERO_SPECTRUM_LEVEL_SERVER, rst.build(), time);
    }

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.heroSpectrum_heroSpectrumAddition);
        Map<Integer, HeroSpectrumTemplate> heroSpectrumTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            HeroSpectrumTemplate template = new HeroSpectrumTemplate();
            template.id = Integer.parseInt(map.get("id"));
            template.star = Integer.parseInt(map.get("starNum"));
            template.combatAdditions = CombatAdditions.readFromStr(map.get("combatAdditions"));
            heroSpectrumTemplateMapTemp.put(template.star, template);
        }
        heroSpectrumTemplateMap = heroSpectrumTemplateMapTemp;

        mapList = ConfigReader.read(ConfigFile.herospectrum_const);
        for (Map<String, String> map : mapList) {
            if("oneClickLevel".equals(map.get("key"))){
                oneClickLevel = Integer.parseInt(map.get("value"));
            }

        }
    }

    @Override
    public void clearConfigData() {
        heroSpectrumTemplateMap.clear();
    }

    public static Map<Integer, HeroSpectrumTemplate> getHeroSpectrumTemplateMap() {
        return heroSpectrumTemplateMap;
    }

}

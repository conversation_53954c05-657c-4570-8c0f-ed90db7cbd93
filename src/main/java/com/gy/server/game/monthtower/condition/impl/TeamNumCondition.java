package com.gy.server.game.monthtower.condition.impl;

import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.monthtower.condition.MonthTowerChallengeCondition;
import com.gy.server.game.player.Player;

/**
 *  战斗结算时，需要N个队伍通过关卡
 *  1. 队伍数量
 */
public class TeamNumCondition implements MonthTowerChallengeCondition {

    /**
     * 队伍数量
     */
    private int num;

    @Override
    public void initParams(String paramStr) {
        num = Integer.parseInt(paramStr);
    }

    @Override
    public boolean isFinish(Player player, AbstractStage stage) {
        return stage.getAtks().size() == num;
    }
}

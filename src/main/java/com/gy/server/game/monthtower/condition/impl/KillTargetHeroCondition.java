package com.gy.server.game.monthtower.condition.impl;

import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.combat.unit.TeamUnit;
import com.gy.server.game.monthtower.condition.MonthTowerChallengeCondition;
import com.gy.server.game.player.Player;
import com.gy.server.utils.CollectionUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 同时击杀某些阵位的敌人（或不能同时击杀）
 * 1. 参数1：可同时击杀，不可同时击杀
 * 2. 参数2：阵位1-6，可同时配置2-6个阵位
 */
public class KillTargetHeroCondition implements MonthTowerChallengeCondition {

    private boolean bothDead;

    private List<Integer> indexList = new ArrayList<>();

    @Override
    public void initParams(String paramStr) {
        String[] split = paramStr.split("\\|");

        bothDead = Integer.parseInt(split[0]) == 1;

        indexList = Arrays.stream(split[1].split("&")).map(j -> Integer.parseInt(j) - 1).collect(Collectors.toList());
    }

    @Override
    public boolean isFinish(Player player, AbstractStage stage) {

        if (indexList.size() <= 0) {
            return false;
        }

        TeamUnit teamUnit = stage.getDefs().get(0);
        List<Integer> deadUnitIds = new ArrayList<>();
        Set<Integer> deadIndex = new HashSet<>();
        for (Integer i : indexList) {
            HeroUnit unit = teamUnit.getByPosIndex(i);

            if (unit == null) {
                return false;
            }
            if(unit.isTeamSoul()){
                continue;
            }

            if (unit.isAlive()) {
                return false;
            }

            deadUnitIds.add(unit.getId());
            deadIndex.add(unit.getDeadIndex());
        }

        if (CollectionUtil.isEmpty(deadUnitIds)) {
            return false;
        }
        if (bothDead) {
            return deadIndex.size() == 1;
        }else {
            return deadIndex.size() == deadUnitIds.size();
        }
    }
}

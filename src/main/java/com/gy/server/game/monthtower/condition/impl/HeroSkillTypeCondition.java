package com.gy.server.game.monthtower.condition.impl;

import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.combat.CbtRecord;
import com.gy.server.game.combat.CombatService;
import com.gy.server.game.combat.skill.SkillTemplate;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.combat.unit.TeamUnit;
import com.gy.server.game.monthtower.condition.MonthTowerChallengeCondition;
import com.gy.server.game.player.Player;

import java.util.Map;
import java.util.Objects;

/**
 * 敌人某阵位角色释放某种技能（大于，等于，小于）N次
 *
 *  参数1：阵位1
 *  参数2：技能Type
 *  参数3：大于1，小于2，等于3
 *  参数4：N次
 */
public class HeroSkillTypeCondition implements MonthTowerChallengeCondition {

    private int index;

    private int skillType;

    private int action;

    private int count;


    @Override
    public void initParams(String paramStr) {
        String[] split = paramStr.split("\\|");
        index = Integer.parseInt(split[0]) - 1;
        skillType = Integer.parseInt(split[1]);
        action = Integer.parseInt(split[2]);
        count = Integer.parseInt(split[3]);
    }

    @Override
    public boolean isFinish(Player player, AbstractStage stage) {

        int releaseCount = 0;
        for (TeamUnit teamUnit : stage.getDefs()) {
            if (index >=0 && index < 6) {
                HeroUnit unit = teamUnit.getByPosIndex(index);
                CbtRecord cbtRecord= stage.getCbtRecord(teamUnit.getTeamId(), unit.getId());
                Map<Integer, Integer> releaseSkillTimes = cbtRecord.getReleaseSkillTimes();
                for (Integer tid : releaseSkillTimes.keySet()) {
                    SkillTemplate skillTemplate = CombatService.skills.get(tid);
                    if(Objects.nonNull(skillTemplate) && skillTemplate.skillType.getValue() == skillType){
                        releaseCount += releaseSkillTimes.get(tid);
                    }
                }

            }
        }
        switch (action){
            case 1:{
                if (releaseCount > this.count)
                    return true;
                break;
            }
            case 2:{
                if (releaseCount < this.count)
                    return true;
                break;
            }
            case 3:{
                if (releaseCount == this.count)
                    return true;
                break;
            }

        }

        return false;
    }
}

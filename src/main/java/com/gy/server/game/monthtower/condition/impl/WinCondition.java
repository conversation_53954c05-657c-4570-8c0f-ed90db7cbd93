package com.gy.server.game.monthtower.condition.impl;

import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.monthtower.condition.MonthTowerChallengeCondition;
import com.gy.server.game.player.Player;

/**
 * 赢
 * <AUTHOR> -[Create on 2022/5/26]
 */
public class WinCondition implements MonthTowerChallengeCondition {
    @Override
    public void initParams(String paramStr) {

    }

    @Override
    public boolean isFinish(Player player, AbstractStage stage) {
        return stage.isWin();
    }
}

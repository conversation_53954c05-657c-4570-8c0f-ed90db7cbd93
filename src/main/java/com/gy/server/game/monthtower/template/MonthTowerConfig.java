package com.gy.server.game.monthtower.template;

import com.gy.server.game.monthtower.MonthTowerService;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> -[Create on 2022/5/25]
 */
public class MonthTowerConfig {

    //关卡开启日期（活动开启x天后开启关卡）
    public Map<Integer, Integer> openDays = new HashMap<>();

    //初级赛季奖励要求通关的关卡数
    public int seasonRewardReq1;
    //高级赛季奖励要求通关的关卡数
    public int seasonRewardReq2;

    public int getSeasonRewardReq1MissionId(int seasonId){
        return MonthTowerService.getAllMissionIdMap().get(seasonId).get(seasonRewardReq1 - 1);
    }


    public int getSeasonRewardReq2MissionId(int seasonId){
        return MonthTowerService.getAllMissionIdMap().get(seasonId).get(seasonRewardReq2 - 1);
    }

    public MonthTowerConfig(Map<String, String> constantMap) {
        //1|10,1;11|20,7;21,14
        String openDates = constantMap.get("openDate");
        for (String openDate : openDates.split(";")) {
            String[] split = openDate.split(",");
            String missionIds = split[0];
            int openDay = Integer.parseInt(split[1]);
            String[] split1 = missionIds.split("\\|");
            if(split1.length == 1){
                openDays.put(Integer.parseInt(missionIds), openDay);
            }else{
                int startMissionId = Integer.parseInt(split1[0]);
                int endMissionId = Integer.parseInt(split1[1]);
                for(int i = startMissionId; i <= endMissionId; i++){
                    this.openDays.put(i, openDay);
                }
            }
        }
        this.seasonRewardReq1 = Integer.parseInt(constantMap.get("seasonRewardReq1"));
        this.seasonRewardReq2 = Integer.parseInt(constantMap.get("seasonRewardReq2"));

    }
}

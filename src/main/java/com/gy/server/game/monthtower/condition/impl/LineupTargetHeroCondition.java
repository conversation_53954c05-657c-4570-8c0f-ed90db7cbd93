package com.gy.server.game.monthtower.condition.impl;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.monthtower.condition.MonthTowerChallengeCondition;
import com.gy.server.game.player.Player;

/**
 * 队伍需要有X(heroID)英雄
 * <AUTHOR> -[Create on 2022/5/26]
 */
public class LineupTargetHeroCondition implements MonthTowerChallengeCondition {

    private List<Integer> heroTemplates;

    @Override
    public void initParams(String paramStr) {
        String[] params = paramStr.split("\\|");
        heroTemplates = Arrays.stream(params).mapToInt(Integer::parseInt).boxed().collect(Collectors.toList());
    }

    @Override
    public boolean isFinish(Player player, AbstractStage stage) {
        Collection<HeroUnit> heroUnits = stage.getAtks().get(0).getUnits();
        List<Integer> atkHeroTemplates = heroUnits.stream().filter(Objects::nonNull).mapToInt(HeroUnit::getTid).boxed().collect(Collectors.toList());
        return atkHeroTemplates.containsAll(heroTemplates);
    }
}

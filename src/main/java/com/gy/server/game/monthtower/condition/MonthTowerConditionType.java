package com.gy.server.game.monthtower.condition;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;

import com.gy.server.game.monthtower.condition.impl.*;

/**
 * <AUTHOR> -[Create on 2022/5/26]
 */
public enum MonthTowerConditionType {
    WinCondition(1, WinCondition::new),
    LineupTargetSoulHero(2, LineupTargetSoulHeroCondition::new),
    LimitRound(3, LimitRoundCondition::new),
    LineupTargetProfessionHero(4, LineupTargetProfessionHeroCondition::new),
    LineupTargetHero(5, LineupTargetHeroCondition::new),
    HeroDieCountLimit(6, HeroDieCountLimitCondition::new),
    FirstKillTargetHeroCondition(7, FirstKillTargetHeroCondition::new),
    HeroSkillCondition(8, HeroSkillCondition::new),
    HeroRemainingHPCondition(9, HeroRemainingHPCondition::new),
    TeamRemainingHPCondition(10, TeamRemainingHPCondition::new),
    KillTargetHeroCondition(11, KillTargetHeroCondition::new),
    TargetHeroBuffCondition(12, TargetHeroBuffCondition::new),
    HeroBuffCondition(13, HeroBuffCondition::new),
    TeamNumCondition(14, TeamNumCondition::new),
    HeroSkillTypeCondition(15, HeroSkillTypeCondition::new),
    ;


    private Supplier<MonthTowerChallengeCondition> supplier;
    private int id;

    private static Map<Integer, MonthTowerConditionType> idToMap = new HashMap<>();

    static {
        for (MonthTowerConditionType type : MonthTowerConditionType.values()) {
            idToMap.put(type.id, type);
        }
    }

    MonthTowerConditionType(int id, Supplier<MonthTowerChallengeCondition> supplier) {
        this.id = id;
        this.supplier = supplier;
    }

    public static MonthTowerConditionType getTypeById(int type) {
        return idToMap.get(type);
    }

    public MonthTowerChallengeCondition createCondition() {
        return supplier.get();
    }

}

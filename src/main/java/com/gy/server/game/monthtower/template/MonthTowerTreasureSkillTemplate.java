package com.gy.server.game.monthtower.template;

import java.util.ArrayList;
import java.util.List;

/**
 * 楼兰寻宝-宝物技能信息
 * <AUTHOR> - [Created on 2024-06-06 19:40]
 */
public class MonthTowerTreasureSkillTemplate {

    public int id;

    public int seasonId;

    public int frontTreasureId;

    public int nextTreasureId;

    public int level;

    public int type;

    public String addedInfo;

    //互斥节点
    public int mutualExclusion;
    //升级消耗
    public List<Integer> levelUpCost = new ArrayList<>();


    public int getLevelUpCost(int level){
        if(level > levelUpCost.size()){
            return levelUpCost.get(0);
        }
        return levelUpCost.get(level - 1);
    }

}

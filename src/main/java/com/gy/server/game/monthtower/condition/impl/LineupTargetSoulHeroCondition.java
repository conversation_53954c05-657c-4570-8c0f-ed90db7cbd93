package com.gy.server.game.monthtower.condition.impl;

import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.hero.template.HeroTemplate;
import com.gy.server.game.item.ItemService;
import com.gy.server.game.monthtower.condition.MonthTowerChallengeCondition;
import com.gy.server.game.player.Player;
import com.gy.server.game.role.PlayerRoleService;
import com.gy.server.game.role.template.ProtagonistTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 上阵X个Y四魂英雄
 * <AUTHOR> -[Create on 2022/5/26]
 */
public class LineupTargetSoulHeroCondition implements MonthTowerChallengeCondition {

    private Map<Integer, Integer> map = new HashMap<>();

    @Override
    public void initParams(String paramStr) {
        String[] params = paramStr.split("&");

        for (String param : params) {
            String[] str = param.split("\\|");
            map.put(Integer.parseInt(str[1]), Integer.parseInt(str[0]));
        }
    }

    @Override
    public boolean isFinish(Player player, AbstractStage stage) {
        int count;
        for (Map.Entry<Integer, Integer> entry : map.entrySet()) {
            int soulType = entry.getKey();
            count = entry.getValue();

            int sum = 0;
            for (HeroUnit unit : stage.getAtks().get(0).getUnits()) {
                if (unit != null && !unit.isTeamSoul()) {
                    if(unit.isHero()){
                        HeroTemplate heroTemplate = (HeroTemplate) ItemService.getItemTemplate(unit.getTid());
                        if (heroTemplate.soulType == soulType) {
                            sum++;
                        }
                    }else{
                        ProtagonistTemplate template = PlayerRoleService.getProtagonistTemplate(unit.getTid());
                        if (template.soulType == soulType) {
                            sum++;
                        }
                    }
                }
            }
            if (sum < count) {
                return false;
            }
        }

        return true;
    }
}


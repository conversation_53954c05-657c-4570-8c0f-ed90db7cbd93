package com.gy.server.game.monthtower.bean;

import com.gy.server.game.hero.MiniHero;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.packet.PbCommons;
import com.gy.server.packet.PbMonthTower;
import com.ttlike.server.tl.baselib.serialize.hero.MiniHeroDb;
import com.ttlike.server.tl.baselib.serialize.monthTower.MonthTowerGlobalDataDb;

import java.util.ArrayList;
import java.util.List;

/**
 * 楼兰寻宝排名信息
 * <AUTHOR> - [Created on 2024-06-14 17:58]
 */
public class MonthTowerRankInfo {

    //玩家id
    private long playerId;
    //通关时间
    private long hurt;
    //阵容信息
    private List<PbCommons.MiniHero> lineupInfos = new ArrayList<>();

    public MonthTowerRankInfo(){}



    public MonthTowerRankInfo(long playerId, long hurt){
        this.playerId = playerId;
        this.hurt = hurt;
    }

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public long getHurt() {
        return hurt;
    }

    public void setHurt(long hurt) {
        this.hurt = hurt;
    }

    public List<PbCommons.MiniHero> getLineupInfos() {
        return lineupInfos;
    }

    public void setLineupInfos(List<PbCommons.MiniHero> lineupInfos) {
        this.lineupInfos = lineupInfos;
    }

    public PbMonthTower.MonthTowerGetRankInfo genPb(){
        PbMonthTower.MonthTowerGetRankInfo.Builder rankInfo = PbMonthTower.MonthTowerGetRankInfo.newBuilder();
        rankInfo.setPlayerId(playerId);
        rankInfo.setHurt(hurt);
        MiniGamePlayer miniPlayer = PlayerManager.getMiniPlayer(playerId);
        rankInfo.setMiniUser(miniPlayer.genMinMiniUser());
        for (PbCommons.MiniHero lineupInfo : lineupInfos) {
            rankInfo.addLineUps(lineupInfo);
        }
        return rankInfo.build();
    }

    public void readFromDb(MonthTowerGlobalDataDb.MonthTowerRankInfo rankInfo){
        this.playerId = rankInfo.getPlayerId();
        this.hurt = rankInfo.getPassTime();
        for (MiniHeroDb miniHeroDb : rankInfo.getMiniHeroDbs()) {
            this.lineupInfos.add(MiniHero.genPb(miniHeroDb));
        }
    }

    public MonthTowerGlobalDataDb.MonthTowerRankInfo writeToDb(){
        MonthTowerGlobalDataDb.MonthTowerRankInfo rankInfo = new MonthTowerGlobalDataDb.MonthTowerRankInfo();
        rankInfo.setPlayerId(playerId);
        rankInfo.setPassTime(hurt);
        for (PbCommons.MiniHero lineupInfo : this.lineupInfos) {
            rankInfo.getMiniHeroDbs().add(MiniHero.genDb(lineupInfo, playerId));
        }
        return rankInfo;
    }

}

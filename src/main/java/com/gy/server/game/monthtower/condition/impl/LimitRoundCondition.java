package com.gy.server.game.monthtower.condition.impl;

import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.monthtower.condition.MonthTowerChallengeCondition;
import com.gy.server.game.player.Player;

/**
 * 最大回合数以内完成战斗
 * <AUTHOR> -[Create on 2022/5/26]
 */
public class LimitRoundCondition implements MonthTowerChallengeCondition {

    /**
     * 最大回合数
     */
    private int maxRound;

    @Override
    public void initParams(String paramStr) {
        maxRound = Integer.parseInt(paramStr);
    }

    @Override
    public boolean isFinish(Player player, AbstractStage stage) {
        return stage.getTimer().roundIndex <= maxRound;
    }
}

package com.gy.server.game.monthtower.condition.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import com.gy.server.game.attribute.AttributeKey;
import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.combat.unit.TeamUnit;
import com.gy.server.game.monthtower.condition.MonthTowerChallengeCondition;
import com.gy.server.game.player.Player;

/**
 * 战斗结算时，我方每个角色的剩余血量均大于X%
 * 参数1：X%
 */
public class HeroRemainingHPCondition implements MonthTowerChallengeCondition {

    /**
     * 位置
     */
    private List<Integer> list = new ArrayList<>();

    /**
     * 剩余血量百分比
     */
    private double hp;


    @Override
    public void initParams(String paramStr) {
        String[] split = paramStr.split("\\|");

        hp = Integer.parseInt(split[0]) * 1.0 / 100;
        list = Arrays.stream(split[1].split("&")).map(j -> Integer.parseInt(j) - 1).collect(Collectors.toList());
    }

    @Override
    public boolean isFinish(Player player, AbstractStage stage) {

        for (TeamUnit teamUnit : stage.getAtks()) {
            for (Integer i : list) {
                HeroUnit unit = teamUnit.getByPosIndex(i);
                if (unit == null ) {
                    return false;
                }
                if(unit.isTeamSoul()){
                    continue;
                }
                double v = 0.0;
                if (unit.isAlive()) {
                    v = unit.getAttributes().getValue(AttributeKey.当前生命值) * 1.0 / unit.getAttributes().getValue(AttributeKey.最大生命);
                }
                if (v < hp) {
                    return false;
                }
            }
        }
        return true;
    }
}

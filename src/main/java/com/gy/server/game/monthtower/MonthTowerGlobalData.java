package com.gy.server.game.monthtower;

import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.ServerConstants;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.event.ServerEvent;
import com.gy.server.game.event.ServerEventHandler;
import com.gy.server.game.event.ServerEventType;
import com.gy.server.game.global.GlobalData;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.mail.MailManager;
import com.gy.server.game.mail.MailType;
import com.gy.server.game.monthtower.bean.MonthTowerRankInfo;
import com.gy.server.game.monthtower.template.MonthTowerRankRewardTemplate;
import com.gy.server.game.monthtower.template.MonthTowerSeasonTemplate;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.text.Text;
import com.gy.server.game.text.TextParamText;
import com.gy.server.game.world.World;
import com.gy.server.game.world.WorldLevelUseTypeEnum;
import com.gy.server.packet.PbCommons;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.serialize.monthTower.MonthTowerGlobalDataDb;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * <AUTHOR> -[Create on 2022/5/27]
 */
public class MonthTowerGlobalData extends GlobalData implements ServerEventHandler {
    private static final ServerEventType[] eventTypes = new ServerEventType[]{ServerEventType.day5clock};


    /**
     * 已经重置多少次了
     */
    private int seasonId;

    //开始结束时间
    private long startTime;

    private List<MonthTowerRankInfo> rankInfos = new CopyOnWriteArrayList<>();

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public int getSeasonId() {
        return seasonId;
    }

    public void setSeasonId(int seasonId) {
        this.seasonId = seasonId;
    }

    public long getEndTime(){
        MonthTowerSeasonTemplate seasonTemplate = MonthTowerService.getSeasonTemplateMap().get(seasonId);
        return DateTimeUtil.toMillis(DateTimeUtil.toLocalDateTime(startTime).plusDays(seasonTemplate.continueDay));
    }

    public int calOpenDay(){
        Duration duration = Duration.between(DateTimeUtil.toLocalDateTime(startTime), ServerConstants.getCurrentTimeLocalDateTime());
        return Math.max((int)duration.toDays(), 0) + 1;
    }

    /**
     * 增加排行榜数据
     * @param rankInfo 排行信息
     */
    public void addRank(MonthTowerRankInfo rankInfo){
        for (MonthTowerRankInfo curRankInfo : rankInfos) {
            if(curRankInfo.getPlayerId() == rankInfo.getPlayerId()){
                rankInfos.remove(curRankInfo);
                break;
            }
        }
        rankInfos.add(rankInfo);
        sort();
    }

    public void sort(){
        rankInfos.sort((o1, o2) -> {
            if(o1.getHurt() == o2.getHurt()){
                return (int)(o1.getPlayerId() - o2.getPlayerId());
            }else {
                return (int)(o2.getHurt() - o1.getHurt());
            }
        });
    }

    public List<MonthTowerRankInfo> getRankInfos() {
        return rankInfos;
    }

    public void setRankInfos(List<MonthTowerRankInfo> rankInfos) {
        this.rankInfos = rankInfos;
    }

    public void refresh(){
        if(ServerConstants.getCurrentTimeMillis() >= getEndTime()){
            reset();

            for (Player onlinePlayer : PlayerManager.getOnlinePlayers()) {
                onlinePlayer.getMonthTowerModel().reset();
            }
        }
    }

    /**
     * 切换赛季
     */
    public void reset(){
        int nextSeasonId = seasonId + 1;
        MonthTowerSeasonTemplate nextSeasonTemplate = MonthTowerService.getSeasonTemplateMap().get(nextSeasonId);
        if(Objects.isNull(nextSeasonTemplate)){
            throw new IllegalArgumentException("month tower season is error, next season fail !!!!!!!!!, nextSeasonId : " + nextSeasonId);
        }
        LocalDateTime openTime = World.getOpenServerTime().plusDays(nextSeasonTemplate.openDay)
                .withHour(CommonUtils.getRefreshTimeHour())
                .withMinute(0).withSecond(0).withNano(0);

        //结算排行榜奖励
        MonthTowerGlobalData globalData = GlobalDataManager.getData(GlobalDataType.monthTower);

        List<MonthTowerRankInfo> rankInfos = globalData.getRankInfos();
        MailType mailType = MailType.monthTowerRankReward;
        PbCommons.PbText title = Text.genText(mailType.getTitleId()).build();
        long sendTime = ServerConstants.getCurrentTimeMillis();
        for (int i = 0; i < rankInfos.size(); i++) {
            int rank = i + 1;
            MonthTowerRankInfo monthTowerRankInfo = rankInfos.get(i);
            //发送奖励
            List<RewardTemplate> rewardTemplates = getRankReward(rank);
            if(CollectionUtil.isNotEmpty(rewardTemplates)){
                PbCommons.PbText content = Text.genText(mailType.getContentId(), new TextParamText(String.valueOf(rank))).build();
                MailManager.sendMail(mailType, monthTowerRankInfo.getPlayerId(), title, content, sendTime, Reward.templateCollectionToReward(rewardTemplates));
            }
        }

        globalData.getRankInfos().clear();

        seasonId = nextSeasonId;
        startTime = DateTimeUtil.toMillis(openTime);
        WorldLevelUseTypeEnum.louLan.refreshWorldLevel();
    }

    private List<RewardTemplate> getRankReward(int rank){
        for (MonthTowerRankRewardTemplate rankRewardTemplate : MonthTowerService.getRankRewardTemplateMap()) {
            if(rankRewardTemplate.minRank == rank || rankRewardTemplate.maxRank == rank){
                return rankRewardTemplate.rewardTemplateList;
            }
        }
        return new ArrayList<>();
    }

    @Override
    public void init() {
        if(seasonId <= 0){
            //根据开服时间计算开始
            List<MonthTowerSeasonTemplate> seasonTemplates = new ArrayList<>(MonthTowerService.getSeasonTemplateMap().values());
            seasonTemplates.sort(Comparator.comparingInt(o -> o.id));
            long openServerDay = World.getOpenServerDay();
            for (MonthTowerSeasonTemplate seasonTemplate : seasonTemplates) {
                //未开启或者正在进行
                if(seasonTemplate.openDay + seasonTemplate.continueDay >= openServerDay){
                    seasonId = seasonTemplate.id;
                    //计算开启时间
                    LocalDateTime openTime = World.getOpenServerTime().plusDays(seasonTemplate.openDay)
                            .withHour(CommonUtils.getRefreshTimeHour())
                            .withMinute(0).withSecond(0).withNano(0);
                    startTime = DateTimeUtil.toMillis(openTime);

                    if(openTime.isAfter(ServerConstants.getCurrentTimeLocalDateTime())){
                        Duration between = Duration.between(ServerConstants.getCurrentTimeLocalDateTime(), openTime);
                        WorldLevelUseTypeEnum.louLan.refreshWorldLevelPlusDay((int)between.toDays());
                    }else{
                        WorldLevelUseTypeEnum.louLan.refreshWorldLevel();
                    }
                    break;
                }
        }
        }

//        if(seasonId <= 0){
//            throw new IllegalArgumentException("month tower season is error, init fail !!!!!!!!!");
//        }
    }

    @Override
    public void readFromPb(byte[] bytes) throws Exception {
        MonthTowerGlobalDataDb monthTowerGlobalDataDb = PbUtilCompress.decode(MonthTowerGlobalDataDb.class, bytes);
        this.seasonId = monthTowerGlobalDataDb.getSeasonId();
        this.startTime = monthTowerGlobalDataDb.getStartTime();
        for (MonthTowerGlobalDataDb.MonthTowerRankInfo rankInfo : monthTowerGlobalDataDb.getRankInfos()) {
            MonthTowerRankInfo towerRankInfo = new MonthTowerRankInfo();
            towerRankInfo.readFromDb(rankInfo);
            this.rankInfos.add(towerRankInfo);
        }
    }

    @Override
    public byte[] writeToPb() {
        MonthTowerGlobalDataDb monthTowerGlobalDataDb = new MonthTowerGlobalDataDb();
        monthTowerGlobalDataDb.setSeasonId(seasonId);
        monthTowerGlobalDataDb.setStartTime(startTime);
        for (MonthTowerRankInfo rankInfo : this.rankInfos) {
            monthTowerGlobalDataDb.getRankInfos().add(rankInfo.writeToDb());
        }

        return PbUtilCompress.encode(monthTowerGlobalDataDb);
    }

    @Override
    public ServerEventType[] getEventTypes() {
        return eventTypes;
    }

    @Override
    public void handle(ServerEvent event) {
        switch (event.getEventType()){
            case day5clock:{
                refresh();
                break;
            }
        }
    }
}

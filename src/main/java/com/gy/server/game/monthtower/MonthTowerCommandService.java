package com.gy.server.game.monthtower;

import com.gy.server.annotation.MessageMethod;
import com.gy.server.annotation.MessageServiceBean;
import com.gy.server.core.MessageServerType;
import com.gy.server.core.command.CommandRequestParams;
import com.gy.server.core.command.ServerCommandRequest;

@MessageServiceBean(description = "大闹天宫", messageServerType = MessageServerType.game)
public class MonthTowerCommandService {

    @MessageMethod(description = "大闹天宫的刷新")
    public static void refresh(ServerCommandRequest request, CommandRequestParams params){




    }
}

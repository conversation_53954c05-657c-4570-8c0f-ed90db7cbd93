package com.gy.server.game.monthtower;


import com.google.common.collect.Sets;
import com.google.protobuf.InvalidProtocolBufferException;
import com.gy.server.common.util.CommonUtils;
import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.combat.CombatManager;
import com.gy.server.game.common.ConstantConfigReader;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.function.Function;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.monthtower.bean.MonthTowerRankInfo;
import com.gy.server.game.monthtower.bean.MonthTowerStage;
import com.gy.server.game.monthtower.condition.MonthTowerChallengeCondition;
import com.gy.server.game.monthtower.condition.MonthTowerConditionType;
import com.gy.server.game.monthtower.template.*;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.rank.RankService;
import com.gy.server.game.reddot.RedDot;
import com.gy.server.game.service.Service;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.StringUtil;

import java.io.IOException;
import java.util.*;

/**
 * 月度爬塔（楼兰寻宝）
 * <AUTHOR> -[Create on 2022/5/26]
 */
public class MonthTowerService extends PlayerPacketHandler implements Service {
    /**
     * 关卡信息
     * key：missionId
     *  value：信息
     */
    public volatile static Map<Integer, MonthTowerMissionTemplate> towerMissionTemplates = new HashMap<>();
    /**
     * 赛季信息
     * key：seasonId
     *  value:信息
     */
    public volatile static Map<Integer, MonthTowerSeasonTemplate> seasonTemplateMap = new HashMap<>();
    /**
     * 宝物信息
     * key：宝物id
     *  value:宝物信息
     */
    public volatile static Map<Integer, MonthTowerTreasureSkillTemplate> treasureSkillTemplateMap = new HashMap<>();

    /**
     * 赛季奖励信息
     * key:赛季id
     *  key:初级/高级
     *   key：奖励id
     *     value：奖励信息
     */
    public volatile static Map<Integer, Map<Integer, Map<Integer, MonthTowerSeasonRewardTemplate>>> seasonRewardTemplateMap = new HashMap<>();
    /**
     * 星级奖励
     * key：赛季id
     *  key：星数
     *   value:奖励信息
     */
    public volatile static Map<Integer, Map<Integer, MonthTowerStarRewardTemplate>> starRewardTemplateMap = new HashMap<>();
    /**
     * 排行奖励
     */
    public volatile static List<MonthTowerRankRewardTemplate> rankRewardTemplateMap = new ArrayList<>();

    /**
     * 所有关卡id集合
     * key：seasonId
     *  value：有序missionId列表
     */
    public volatile static Map<Integer, List<Integer>> allMissionIdMap = new HashMap<>();


    private volatile static MonthTowerConfig config;

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.louLan_mission);
        Map<Integer, MonthTowerMissionTemplate> templateMap = new HashMap<>();
        Map<Integer, List<Integer>> allMissionIds = new HashMap<>();
        for (Map<String, String> map : mapList) {
            MonthTowerMissionTemplate template = new MonthTowerMissionTemplate();
            template.missionId = Integer.parseInt(map.get("id"));
            template.seasonId = Integer.parseInt(map.get("season"));

            if(!"-1".equals(map.get("goal1Condition"))){
                String[] bronzeCondition = map.get("goal1Condition").split(",");
                String[] bronzeParameter = map.get("goal1Para").split(",");
                if (bronzeCondition.length != bronzeParameter.length) {
                    CommonUtils.handleException(new IOException("bronzeCondition and bronzeParameter length not save"));
                }
                for (int i = 0; i < bronzeCondition.length; i++) {
                    MonthTowerChallengeCondition condition = MonthTowerConditionType.getTypeById(Integer.parseInt(bronzeCondition[i])).createCondition();
                    condition.initParams(bronzeParameter[i]);
                    template.bronzeCondition.add(condition) ;
                }
            }

            if(!"-1".equals(map.get("goal2Condition"))){
                String[] silverCondition = map.get("goal2Condition").split(",");
                String[] silverParameter = map.get("goal2Para").split(",");
                if (silverCondition.length != silverParameter.length) {
                    CommonUtils.handleException(new IOException("silverCondition and silverParameter length not save"));
                }
                for (int i = 0; i < silverCondition.length; i++) {
                    MonthTowerChallengeCondition condition = MonthTowerConditionType.getTypeById(Integer.parseInt(silverCondition[i])).createCondition();
                    condition.initParams(silverParameter[i]);
                    template.silverCondition.add(condition) ;
                }
            }

            if(!"-1".equals(map.get("goal3Condition"))){
                String[] goldCondition = map.get("goal3Condition").split(",");
                String[] goldParameter = map.get("goal3Para").split(",");
                if (goldCondition.length != goldParameter.length) {
                    CommonUtils.handleException(new IOException("goldCondition and goldParameter length not save"));
                }
                for (int i = 0; i < goldCondition.length; i++) {
                    MonthTowerChallengeCondition condition = MonthTowerConditionType.getTypeById(Integer.parseInt(goldCondition[i])).createCondition();
                    condition.initParams(goldParameter[i]);
                    template.goldCondition.add(condition) ;
                }
            }

            template.rewardTemplates.addAll(RewardTemplate.readListFromText(map.get("reward"))) ;
            int[] ints = StringUtil.splitToIntArray(map.get("monster"), ",");
            for (int anInt : ints) {
                template.battleCollectIds.add(anInt);
            }
            template.attack = Double.parseDouble(map.get("attack"));
            template.hitPoint = Double.parseDouble(map.get("hitPoint"));
            template.openDay = Integer.parseInt(map.get("openDay"));
            template.lineUp = Integer.parseInt(map.get("lineUp"));

            if(!allMissionIds.containsKey(template.seasonId)){
                allMissionIds.put(template.seasonId, new ArrayList<>());
            }
            allMissionIds.get(template.seasonId).add(template.missionId);

            templateMap.put(template.missionId, template);
        }
        towerMissionTemplates = templateMap;
        allMissionIdMap = allMissionIds;

        mapList = ConfigReader.read(ConfigFile.louLan_season);
        Map<Integer, MonthTowerSeasonTemplate> seasonTemplates = new HashMap<>();
        for (Map<String, String> map : mapList) {
            MonthTowerSeasonTemplate seasonTemplate = new MonthTowerSeasonTemplate();
            seasonTemplate.id = Integer.parseInt(map.get("ID"));
            seasonTemplate.openDay = Integer.parseInt(map.get("openDay"));
            seasonTemplate.continueDay = Integer.parseInt(map.get("lastDay"));
            seasonTemplate.monsterId = Integer.parseInt(map.get("monsterId"));
            seasonTemplates.put(seasonTemplate.id, seasonTemplate);
        }
        seasonTemplateMap = seasonTemplates;

        mapList = ConfigReader.read(ConfigFile.louLan_seasonReward);
        Map<Integer, Map<Integer, Map<Integer, MonthTowerSeasonRewardTemplate>>> seasonRewardTemplates = new HashMap<>();
        for (Map<String, String> map : mapList) {
            MonthTowerSeasonRewardTemplate seasonRewardTemplate = new MonthTowerSeasonRewardTemplate();
            seasonRewardTemplate.id = Integer.parseInt(map.get("id"));
            seasonRewardTemplate.seasonId = Integer.parseInt(map.get("seasonId"));
            seasonRewardTemplate.level = Integer.parseInt(map.get("level"));
            seasonRewardTemplate.rewards1 = RewardTemplate.readListFromText(map.get("reward1"));
            seasonRewardTemplate.rewards2 = RewardTemplate.readListFromText(map.get("reward2"));
            seasonRewardTemplate.rewards3 = RewardTemplate.readListFromText(map.get("reward3"));
            seasonRewardTemplate.rewards4 = RewardTemplate.readListFromText(map.get("reward4"));
            seasonRewardTemplate.rewards5 = RewardTemplate.readListFromText(map.get("reward5"));

            if(!seasonRewardTemplates.containsKey(seasonRewardTemplate.seasonId)){
                seasonRewardTemplates.put(seasonRewardTemplate.seasonId, new HashMap<>());
            }
            Map<Integer, Map<Integer, MonthTowerSeasonRewardTemplate>> integerMapMap = seasonRewardTemplates.get(seasonRewardTemplate.seasonId);
            if(!integerMapMap.containsKey(seasonRewardTemplate.level)){
                integerMapMap.put(seasonRewardTemplate.level, new HashMap<>());
            }
            integerMapMap.get(seasonRewardTemplate.level).put(seasonRewardTemplate.id, seasonRewardTemplate);
        }
        seasonRewardTemplateMap = seasonRewardTemplates;

        mapList = ConfigReader.read(ConfigFile.louLan_starReward);
        Map<Integer, Map<Integer, MonthTowerStarRewardTemplate>> starRewardTemplates = new HashMap<>();
        for (Map<String, String> map : mapList) {
            MonthTowerStarRewardTemplate starRewardTemplate = new MonthTowerStarRewardTemplate();
            starRewardTemplate.id = Integer.parseInt(map.get("id"));
            starRewardTemplate.seasonId = Integer.parseInt(map.get("seasonId"));
            starRewardTemplate.starNum = Integer.parseInt(map.get("satrNum"));
            starRewardTemplate.rewards = RewardTemplate.readListFromText(map.get("reward"));
            if(!starRewardTemplates.containsKey(starRewardTemplate.seasonId)){
                starRewardTemplates.put(starRewardTemplate.seasonId, new HashMap<>());
            }
            starRewardTemplates.get(starRewardTemplate.seasonId).put(starRewardTemplate.starNum, starRewardTemplate);
        }
        starRewardTemplateMap = starRewardTemplates;

        mapList = ConfigReader.read(ConfigFile.louLan_rankReward);
        List<MonthTowerRankRewardTemplate> rankRewardTemplates = new ArrayList<>();
        for (Map<String, String> map : mapList) {
            MonthTowerRankRewardTemplate rankRewardTemplate = new MonthTowerRankRewardTemplate();
            String rank = map.get("rank");
            String[] split = rank.split("\\|");
            if(split.length == 1){
                rankRewardTemplate.minRank = Integer.parseInt(rank);
                rankRewardTemplate.maxRank = Integer.parseInt(rank);
            }else{
                rankRewardTemplate.minRank = Integer.parseInt(split[0]);
                rankRewardTemplate.maxRank = Integer.parseInt(split[1]);
            }
            rankRewardTemplate.rewardTemplateList = RewardTemplate.readListFromText(map.get("reward"));

            rankRewardTemplates.add(rankRewardTemplate);
        }
        rankRewardTemplateMap = rankRewardTemplates;

        mapList = ConfigReader.read(ConfigFile.louLan_treasureSkill);
        Map<Integer, MonthTowerTreasureSkillTemplate> treasureSkillTemplates = new HashMap<>();
        for (Map<String, String> map : mapList) {
            MonthTowerTreasureSkillTemplate treasureSkillTemplate = new MonthTowerTreasureSkillTemplate();
            treasureSkillTemplate.id = Integer.parseInt(map.get("id"));
            treasureSkillTemplate.seasonId = Integer.parseInt(map.get("seasonId"));
            treasureSkillTemplate.frontTreasureId = Integer.parseInt(map.get("beforePoint"));
//            treasureSkillTemplate.nextTreasureId = Integer.parseInt(map.get("point"));
            treasureSkillTemplate.level = Integer.parseInt(map.get("level"));
            treasureSkillTemplate.type = Integer.parseInt(map.get("type"));
            treasureSkillTemplate.addedInfo = map.get("addedInfo");
            treasureSkillTemplate.mutualExclusion = Integer.parseInt(map.get("mutualExclusion"));
            int[] ints = StringUtil.splitToIntArray(map.get("levelUp"), ",");
            for (int anInt : ints) {
                treasureSkillTemplate.levelUpCost.add(anInt);
            }

            treasureSkillTemplates.put(treasureSkillTemplate.id, treasureSkillTemplate);

        }
        treasureSkillTemplateMap = treasureSkillTemplates;

        mapList = ConfigReader.read(ConfigFile.louLan_constant);
        Map<String, String> constantMap = ConstantConfigReader.read(mapList, "key", "value");
        try {
            config = new MonthTowerConfig(constantMap);
        } catch (Exception e) {
            CommonUtils.handleException(e);
        }
    }

    @Override
    public boolean isWorldServer() {
        return true;
    }

    @Override
    public boolean isPreSystemStartup() {
        return true;
    }

    public static Map<Integer, MonthTowerMissionTemplate> getTowerMissionTemplates() {
        return towerMissionTemplates;
    }

    public static void setTowerMissionTemplates(Map<Integer, MonthTowerMissionTemplate> towerMissionTemplates) {
        MonthTowerService.towerMissionTemplates = towerMissionTemplates;
    }

    public static Map<Integer, MonthTowerSeasonTemplate> getSeasonTemplateMap() {
        return seasonTemplateMap;
    }

    public static void setSeasonTemplateMap(Map<Integer, MonthTowerSeasonTemplate> seasonTemplateMap) {
        MonthTowerService.seasonTemplateMap = seasonTemplateMap;
    }

    public static Map<Integer, MonthTowerTreasureSkillTemplate> getTreasureSkillTemplateMap() {
        return treasureSkillTemplateMap;
    }

    public static void setTreasureSkillTemplateMap(Map<Integer, MonthTowerTreasureSkillTemplate> treasureSkillTemplateMap) {
        MonthTowerService.treasureSkillTemplateMap = treasureSkillTemplateMap;
    }

    public static Map<Integer, Map<Integer, Map<Integer, MonthTowerSeasonRewardTemplate>>> getSeasonRewardTemplateMap() {
        return seasonRewardTemplateMap;
    }

    public static void setSeasonRewardTemplateMap(Map<Integer, Map<Integer, Map<Integer, MonthTowerSeasonRewardTemplate>>> seasonRewardTemplateMap) {
        MonthTowerService.seasonRewardTemplateMap = seasonRewardTemplateMap;
    }

    public static Map<Integer, Map<Integer, MonthTowerStarRewardTemplate>> getStarRewardTemplateMap() {
        return starRewardTemplateMap;
    }

    public static void setStarRewardTemplateMap(Map<Integer, Map<Integer, MonthTowerStarRewardTemplate>> starRewardTemplateMap) {
        MonthTowerService.starRewardTemplateMap = starRewardTemplateMap;
    }

    public static Map<Integer, List<Integer>> getAllMissionIdMap() {
        return allMissionIdMap;
    }

    public static void setAllMissionIdMap(Map<Integer, List<Integer>> allMissionIdMap) {
        MonthTowerService.allMissionIdMap = allMissionIdMap;
    }

    public static List<MonthTowerRankRewardTemplate> getRankRewardTemplateMap() {
        return rankRewardTemplateMap;
    }

    public static void setRankRewardTemplateMap(List<MonthTowerRankRewardTemplate> rankRewardTemplateMap) {
        MonthTowerService.rankRewardTemplateMap = rankRewardTemplateMap;
    }

    public static void setConfig(MonthTowerConfig config) {
        MonthTowerService.config = config;
    }

    public static MonthTowerConfig getConfig() {
        return config;
    }

    @SuppressWarnings("unchecked")
    @Override
    public Set<Class<? extends Service>> preServices() {
        return Sets.newHashSet(RankService.class);
    }

    @Override
    public void clearConfigData() {
        towerMissionTemplates.clear();
        seasonTemplateMap.clear();
        treasureSkillTemplateMap.clear();

        seasonRewardTemplateMap.clear();
        starRewardTemplateMap.clear();
        rankRewardTemplateMap.clear();
        allMissionIdMap.clear();

        config = null;
    }

    /**
     * 月度爬塔-主界面
     */
    @Handler(PtCode.MONTH_TOWER_MAIN_REQ)
    private void main(Player player, PbProtocol.MonthTowerMainReq req, long time) {
        PbProtocol.MonthTowerMainRst.Builder rst = PbProtocol.MonthTowerMainRst.newBuilder().setResult(Text.genOkServerRstInfo());

        PlayerMonthTowerModel model = player.getMonthTowerModel();
        rst.setMainInfo(model.genPb());

        player.send(PtCode.MONTH_TOWER_MAIN_RST, rst.build(), time);
    }

    /**
     * 月度爬塔-战斗
     */
    @Handler(PtCode.MONTH_TOWER_FIGHT_REQ)
    private void fight(Player player, PbProtocol.MonthTowerFightReq req, long time) throws InvalidProtocolBufferException {
        PbProtocol.MonthTowerFightRst.Builder rst = PbProtocol.MonthTowerFightRst.newBuilder().setResult(Text.genOkServerRstInfo());

        int challengeMissionId = req.getMissionId();
        MonthTowerStage stage = null;
        logic:
        {

            if(Function.monthTower.isNotOpen(player)){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }

            PlayerMonthTowerModel model = player.getMonthTowerModel();


            MonthTowerMissionTemplate missionTemplate = towerMissionTemplates.get(challengeMissionId);
            if(Objects.isNull(missionTemplate)){
                rst.setResult(Text.genServerRstInfo(Text.对应的模板数据找不到));
                break logic;
            }

            if (missionTemplate.seasonId != model.getSelfSession()) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }

            List<Integer> missionIds = allMissionIdMap.get(model.getSelfSession());

            //如果不是第一层，上层还没打过
            if (challengeMissionId != missionIds.get(0)
                    && !model.getPassMissions().containsKey(missionIds.get(missionIds.indexOf(challengeMissionId) - 1)) ) {
                rst.setResult(Text.genServerRstInfo(Text.请先通关上一层挑战));
                break logic;
            }

            //检查关卡是否开启
            MonthTowerGlobalData globalData = GlobalDataManager.getData(GlobalDataType.monthTower);
            if(missionTemplate.openDay > globalData.calOpenDay()){
                rst.setResult(Text.genServerRstInfo(Text.楼兰寻宝关卡为开启));
                break logic;
            }


            stage = new MonthTowerStage(player, model.getSelfSession(), missionTemplate);
            stage.init();


            rst.setStage(stage.getStageRecord());
        }

        player.send(PtCode.MONTH_TOWER_FIGHT_RST, rst.build(), time);

        if (stage != null) {
            CombatManager.combatPrepare(stage);
        }
    }

    @Handler(PtCode.MONTH_TOWER_RECEIVE_SEASON_REWARD_REQ)
    public void receiveSeasonReward(Player player, PbProtocol.MonthTowerReceiveSeasonRewardReq req, long time) {
        PbProtocol.MonthTowerReceiveSeasonRewardRst.Builder rst = PbProtocol.MonthTowerReceiveSeasonRewardRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        logic:
        {
            int level = req.getLevel();
            Map<Integer, Integer> rewardInfosMap = req.getRewardInfosMap();
            //功能是否开启
            if (Function.monthTower.isNotOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            //参数level是否合法
            PlayerMonthTowerModel model = player.getMonthTowerModel();
            Map<Integer, Map<Integer, MonthTowerSeasonRewardTemplate>> seasonRewardTemplates = seasonRewardTemplateMap.get(model.getSelfSession());
            if(!seasonRewardTemplates.containsKey(level)){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            //参数rewardInfosMap是否合法
            Map<Integer, MonthTowerSeasonRewardTemplate> monthTowerSeasonRewardTemplates = seasonRewardTemplates.get(level);
            if(monthTowerSeasonRewardTemplates.size() != rewardInfosMap.size()){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            if(model.getSeasonRewardReceiveLevelIds().contains(level)){
                rst.setResult(Text.genServerRstInfo(Text.楼兰寻宝赛季奖励已经领取过));
                break logic;
            }
            //检查关卡是否通过
            MonthTowerConfig config = getConfig();
            int levelId;
            if(level == 1){
                levelId = config.getSeasonRewardReq1MissionId(model.getSelfSession());
            }else{
                levelId = config.getSeasonRewardReq2MissionId(model.getSelfSession());
            }
            if(!model.getPassMissions().containsKey(levelId)){
                rst.setResult(Text.genServerRstInfo(Text.楼兰寻宝关卡未通关));
                break logic;
            }
            //id是否全部正确
            //value奖励是否存在
            List<RewardTemplate> rewardTemplates = new ArrayList<>();
            for (Integer id : rewardInfosMap.keySet()) {
                if(!monthTowerSeasonRewardTemplates.containsKey(id)){
                    rst.setResult(Text.genServerRstInfo(Text.参数异常));
                    break logic;
                }
                Integer index = rewardInfosMap.get(id);
                MonthTowerSeasonRewardTemplate monthTowerSeasonRewardTemplate = monthTowerSeasonRewardTemplates.get(id);
                if(Objects.isNull(monthTowerSeasonRewardTemplate)){
                    rst.setResult(Text.genServerRstInfo(Text.参数异常));
                    break logic;
                }
                List<RewardTemplate> reward = monthTowerSeasonRewardTemplate.getReward(index);
                if(CollectionUtil.isEmpty(reward)){
                    rst.setResult(Text.genServerRstInfo(Text.参数异常));
                    break logic;
                }
                rewardTemplates.addAll(reward);
            }
            //记录奖励
            model.getSeasonRewardReceiveLevelIds().add(level);
            model.getHadReceiveRewardInfos().put(level, rewardInfosMap);
            List<Reward> rewardList = Reward.templateCollectionToReward(rewardTemplates);
            Reward.merge(rewardList);
            Reward.add(rewardList, player, BehaviorType.monthTowerReasonReward);
            RedDot.monthTowerCanChoiceReward.sync(player);
            rst.setLevel(level);
            rst.addAllRewards(Reward.writeCollectionToPb(rewardList));

        }
        player.send(PtCode.MONTH_TOWER_RECEIVE_SEASON_REWARD_RST, rst.build(), time);
    }

    @Handler(PtCode.MONTH_TOWER_RECEIVE_LEVEL_REWARD_REQ)
    public void receiveLevelReward(Player player, PbProtocol.MonthTowerReceiveLevelRewardReq req, long time) {
        PbProtocol.MonthTowerReceiveLevelRewardRst.Builder rst = PbProtocol.MonthTowerReceiveLevelRewardRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        logic:{
            int missionId = req.getMissionId();
            //功能是否开启
            if (Function.monthTower.isNotOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }

            MonthTowerMissionTemplate missionTemplate = towerMissionTemplates.get(missionId);
            if(Objects.isNull(missionTemplate)){
                rst.setResult(Text.genServerRstInfo(Text.对应的模板数据找不到));
                break logic;
            }
            PlayerMonthTowerModel model = player.getMonthTowerModel();
            if (missionTemplate.seasonId != model.getSelfSession()) {
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            if(!model.getPassMissions().containsKey(missionId)){
                rst.setResult(Text.genServerRstInfo(Text.楼兰寻宝关卡未通关));
                break logic;
            }
            if(model.getLevelReceiveIds().contains(missionId)){
                rst.setResult(Text.genServerRstInfo(Text.楼兰寻宝关卡奖励已经领取过));
                break logic;
            }
            model.getLevelReceiveIds().add(missionId);

            List<Reward> rewardList = Reward.templateCollectionToReward(missionTemplate.rewardTemplates);
            Reward.merge(rewardList);
            Reward.add(rewardList, player, BehaviorType.monthTowerLevelReward);

            RedDot.monthTowerPassRewards.sync(player);
            rst.setMissionId(missionId);
            rst.addAllRewards(Reward.writeCollectionToPb(rewardList));
        }

        player.send(PtCode.MONTH_TOWER_RECEIVE_LEVEL_REWARD_RST, rst.build(), time);
    }

    @Handler(PtCode.MONTH_TOWER_RECEIVE_STAR_REWARD_REQ)
    public void receiveStarReward(Player player, PbProtocol.MonthTowerReceiveStarRewardReq req, long time) {
        PbProtocol.MonthTowerReceiveStarRewardRst.Builder rst = PbProtocol.MonthTowerReceiveStarRewardRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        logic:{
            int starNum = req.getStarNum();
            //功能是否开启
            if (Function.monthTower.isNotOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }

            PlayerMonthTowerModel model = player.getMonthTowerModel();
            Map<Integer, MonthTowerStarRewardTemplate> starRewardTemplateMap = MonthTowerService.starRewardTemplateMap.get(model.getSelfSession());
            if(!starRewardTemplateMap.containsKey(starNum)){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            if(model.getTotalStar() < starNum){
                rst.setResult(Text.genServerRstInfo(Text.星数不够无法选择));
                break logic;
            }
            if(model.getStarRewardReceiveIds().contains(starNum)){
                rst.setResult(Text.genServerRstInfo(Text.楼兰寻宝星级奖励已经领取过));
                break logic;
            }
            model.getStarRewardReceiveIds().add(starNum);
            MonthTowerStarRewardTemplate starRewardTemplate = starRewardTemplateMap.get(starNum);

            List<Reward> rewardList = Reward.templateCollectionToReward(starRewardTemplate.rewards);
            Reward.merge(rewardList);
            Reward.add(rewardList, player, BehaviorType.monthTowerStarReward);

            RedDot.monthTowerStarRewards.sync(player);
            rst.setStarNum(starNum);
            rst.addAllRewards(Reward.writeCollectionToPb(rewardList));
        }
        player.send(PtCode.MONTH_TOWER_RECEIVE_STAR_REWARD_RST, rst.build(), time);
    }

    @Handler(PtCode.MONTH_TOWER_TREASURE_LEVEL_UP_REQ)
    public void treasureLevelUp(Player player, PbProtocol.MonthTowerTreasureLevelUpReq req, long time) {
        PbProtocol.MonthTowerTreasureLevelUpRst.Builder rst = PbProtocol.MonthTowerTreasureLevelUpRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        logic:{
            int treasureLevelId = req.getTreasureLevelId();
            //功能是否开启
            if (Function.monthTower.isNotOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            PlayerMonthTowerModel model = player.getMonthTowerModel();
            MonthTowerTreasureSkillTemplate treasureSkillTemplate = getTreasureSkillTemplateMap().get(treasureLevelId);
            if(treasureSkillTemplate.seasonId != model.getSelfSession()){
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            //检查是否满级
            Map<Integer, Integer> treasureInfos = model.getTreasureInfos();
            int nowLevel = treasureInfos.getOrDefault(treasureLevelId, 0);
            if(nowLevel >= treasureSkillTemplate.level){
                rst.setResult(Text.genServerRstInfo(Text.楼兰寻宝点位满级));
                break logic;
            }
            //检查前置是否完成
            if(treasureSkillTemplate.frontTreasureId > 0){
                MonthTowerTreasureSkillTemplate frontTreasureTemp = getTreasureSkillTemplateMap().get(treasureSkillTemplate.frontTreasureId);
                if(treasureInfos.getOrDefault(treasureSkillTemplate.frontTreasureId, 0) < frontTreasureTemp.level) {
                    rst.setResult(Text.genServerRstInfo(Text.楼兰寻宝前置点位未满级));
                    break logic;
                }
            }
            //检查是否存在互斥节点
            if(treasureInfos.containsKey(treasureSkillTemplate.mutualExclusion)){
                rst.setResult(Text.genServerRstInfo(Text.楼兰寻宝不能升级互斥技能节点));
                break logic;
            }
            int newLevel = nowLevel + 1;
            int hadCostStarNum = model.getTreasureHadCostStarNum();
            int totalStar = model.getTotalStar();
            if(totalStar - hadCostStarNum < treasureSkillTemplate.getLevelUpCost(newLevel)){
                rst.setResult(Text.genServerRstInfo(Text.楼兰寻宝星数不足));
                break logic;
            }
            //升级
            treasureInfos.put(treasureLevelId, newLevel);

        }
        player.send(PtCode.MONTH_TOWER_TREASURE_LEVEL_UP_RST, rst.build(), time);
    }

    @Handler(PtCode.MONTH_TOWER_TREASURE_RESET_REQ)
    public void treasureReset(Player player, PbProtocol.MonthTowerTreasureResetReq req, long time) {
        PbProtocol.MonthTowerTreasureResetRst.Builder rst = PbProtocol.MonthTowerTreasureResetRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        logic:{
            //功能是否开启
            if (Function.monthTower.isNotOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            PlayerMonthTowerModel model = player.getMonthTowerModel();
            model.getTreasureInfos().clear();

        }
        player.send(PtCode.MONTH_TOWER_TREASURE_RESET_RST, rst.build(), time);
    }

    @Handler(PtCode.MONTH_TOWER_RANK_REQ)
    public void rank(Player player, PbProtocol.MonthTowerGetRankReq req, long time) {
        PbProtocol.MonthTowerGetRankRst.Builder rst = PbProtocol.MonthTowerGetRankRst.newBuilder()
                .setResult(Text.genOkServerRstInfo());
        logic:{
            //功能是否开启
            if (Function.monthTower.isNotOpen(player)) {
                rst.setResult(Text.genServerRstInfo(Text.功能未开启));
                break logic;
            }
            MonthTowerGlobalData globalData = GlobalDataManager.getData(GlobalDataType.monthTower);
            for (MonthTowerRankInfo rankInfo : globalData.getRankInfos()) {
                rst.addRankInfos(rankInfo.genPb());
            }
        }
        player.send(PtCode.MONTH_TOWER_RANK_RST, rst.build(), time);
    }

}

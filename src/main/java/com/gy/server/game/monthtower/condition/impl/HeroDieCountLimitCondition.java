package com.gy.server.game.monthtower.condition.impl;

import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.monthtower.condition.MonthTowerChallengeCondition;
import com.gy.server.game.player.Player;

/**
 * 最多阵亡X个英雄
 * <AUTHOR> -[Create on 2022/5/26]
 */
public class HeroDieCountLimitCondition implements MonthTowerChallengeCondition {
    private int count;

    @Override
    public void initParams(String paramStr) {
        this.count = Integer.parseInt(paramStr.split("\\|")[0]);
    }

    @Override
    public boolean isFinish(Player player, AbstractStage stage) {
        return stage.getDieCount() <= count;
    }
}

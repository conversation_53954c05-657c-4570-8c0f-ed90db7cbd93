package com.gy.server.game.monthtower;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.attribute.Attributes;
import com.gy.server.game.combat.skill.Skill;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.function.Function;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.mail.MailManager;
import com.gy.server.game.mail.MailType;
import com.gy.server.game.monthtower.template.*;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModel;
import com.gy.server.game.player.event.PlayerEvent;
import com.gy.server.game.player.event.PlayerEventHandler;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.text.Text;
import com.gy.server.packet.PbCommons;
import com.gy.server.packet.PbMonthTower;
import com.gy.server.utils.CollectionUtil;
import com.ttlike.server.tl.baselib.serialize.monthTower.MonthTowerHadReceiveRewardDb;
import com.ttlike.server.tl.baselib.serialize.monthTower.MonthTowerPassInfoDb;
import com.ttlike.server.tl.baselib.serialize.monthTower.PlayerMonthTowerModelDb;
import com.ttlike.server.tl.baselib.serialize.player.PlayerBlobDb;

import java.util.*;

import static com.gy.server.game.player.event.PlayerEventType.functionCheck;
import static com.gy.server.game.player.event.PlayerEventType.login;

/**
 * 楼兰寻宝（万仙大会）（月度爬塔）
 * <AUTHOR> -[Create on 2022/5/26]
 */
public class PlayerMonthTowerModel  extends PlayerModel implements PlayerEventHandler {
    private static final PlayerEventType[] eventTypes = new PlayerEventType[]{PlayerEventType.day5Refresh, functionCheck, login};

    /**
     * 通关信息，Key:MissionId，Value:已经达成了哪个挑战
     */
    private Map<Integer, Set<PbMonthTower.MonthTowerDifficulty>> passMissions = new HashMap<>();
    //赛季奖励领取id
    List<Integer> seasonRewardReceiveLevelIds = new ArrayList<>();
    //已经领取奖励信息
    Map<Integer, Map<Integer, Integer>> hadReceiveRewardInfos = new HashMap<>();
    //赛季奖励星数领取id
    List<Integer> starRewardReceiveIds = new ArrayList<>();
    //关卡奖励
    List<Integer> levelReceiveIds = new ArrayList<>();
    //宝物等级信息
    private Map<Integer, Integer> treasureInfos = new HashMap<>();
    //当前赛季id
    private int selfSession = -1;
    //最短通关时间
    private long minPassTimeInterval;
    private long maxHurt;
    //最大回合数
    private int maxRound;


    public PlayerMonthTowerModel(Player player) {
        super(player);
    }

    /**
     * 获得神器技能
     */
    public List<Skill> getTreasureSkillInfo(){
        Map<Integer, Integer> skillInfos = new HashMap<>();
        for (Integer id : treasureInfos.keySet()) {
            MonthTowerTreasureSkillTemplate treasureSkillTemplate = MonthTowerService.getTreasureSkillTemplateMap().get(id);
            if(treasureSkillTemplate.type == 1){
                for (String added : treasureSkillTemplate.addedInfo.split(",")) {
                    String[] split = added.split("\\|");
                    int skillId = Integer.parseInt(split[0]);
                    int skillLevel = Integer.parseInt(split[1]);
                    if(skillInfos.getOrDefault(skillId, 0) >= skillLevel){
                        continue;
                    }
                    skillInfos.put(skillId, skillLevel);
                }
            }
        }
        List<Skill> skills = new ArrayList<>();
        skillInfos.forEach((k, v)->skills.add(new Skill(k, v)));
        return skills;
    }

    /**
     * 获得神器属性
     */
    public Attributes getTreasureAttributes(){
        Attributes attributes = new Attributes();
        for (Integer id : treasureInfos.keySet()) {
            MonthTowerTreasureSkillTemplate treasureSkillTemplate = MonthTowerService.getTreasureSkillTemplateMap().get(id);
            if (treasureSkillTemplate.type == 2) {
                int level = treasureInfos.get(id);
                for(int i = 0; i < level; i++){
                    Attributes.mergeAttributes(attributes, Attributes.readFromStr(treasureSkillTemplate.addedInfo));
                }
            }
        }
        return attributes;
    }

    /**
     * 获得当前总星数
     */
    public int getTotalStar() {
        int totalStar = 0;
        for (Set<PbMonthTower.MonthTowerDifficulty> set : passMissions.values()) {
            totalStar += set.size();
        }
        return totalStar;
    }

    public PbMonthTower.MonthTowerMainInfo genPb(){
        PbMonthTower.MonthTowerMainInfo.Builder mainInfo = PbMonthTower.MonthTowerMainInfo.newBuilder();
        MonthTowerGlobalData globalData = GlobalDataManager.getData(GlobalDataType.monthTower);
        mainInfo.setSeasonId(globalData.getSeasonId());
        mainInfo.setStartTime(globalData.getStartTime());
        mainInfo.addAllSeasonRewardReceiveLevelIds(seasonRewardReceiveLevelIds);
        mainInfo.addAllStarRewardReceiveIds(starRewardReceiveIds);
        mainInfo.addAllLevelReceiveIds(levelReceiveIds);
        for (Integer missionId : passMissions.keySet()) {
            mainInfo.addMissionInfo(PbMonthTower.MonthTowerPassInfo.newBuilder()
                    .setMissionId(missionId)
                    .addAllDifficulty(passMissions.get(missionId)).build());
        }
        for (Integer id : treasureInfos.keySet()) {
            mainInfo.addTreasureInfos(PbMonthTower.MonthTowerTreasureInfo.newBuilder()
                    .setId(id).setLevel(treasureInfos.get(id)).build());

        }
        mainInfo.setMaxHurt(maxHurt);
        PbMonthTower.MonthTowerRewardInfo.Builder rewardInfoBuilder = PbMonthTower.MonthTowerRewardInfo.newBuilder();
        for (Integer level : hadReceiveRewardInfos.keySet()) {
            rewardInfoBuilder.setLevel(level);
            rewardInfoBuilder.putAllRewardInfos(hadReceiveRewardInfos.get(level));
            mainInfo.addRewardInfos(rewardInfoBuilder.build());
        }
        return mainInfo.build();
    }

    public boolean showCanChoiceRewardRedDot() {
        MonthTowerConfig config = MonthTowerService.getConfig();
        Map<Integer, Map<Integer, MonthTowerSeasonRewardTemplate>> rewardMap = MonthTowerService.getSeasonRewardTemplateMap().get(getSelfSession());
        if(CollectionUtil.isNotEmpty(rewardMap)){
            for (Integer level : rewardMap.keySet()){
                //已经领取过
                if(getSeasonRewardReceiveLevelIds().contains(level)){
                    continue;
                }
                int levelId;
                if(level == 1){
                    levelId = config.getSeasonRewardReq1MissionId(getSelfSession());
                }else{
                    levelId = config.getSeasonRewardReq2MissionId(getSelfSession());
                }
                if(getPassMissions().containsKey(levelId)){
                    //已经通关
                    return true;
                }
            }
        }
        return false;
    }

    public boolean showStarRedDot(){
        Map<Integer, MonthTowerStarRewardTemplate> starRewardTemplateMap = MonthTowerService.starRewardTemplateMap.get(getSelfSession());
        for (Integer starNum : starRewardTemplateMap.keySet()) {
            //星数达到，并且未领取奖励
            if(getTotalStar() >= starNum && !getStarRewardReceiveIds().contains(starNum)){
                return true;
            }
        }
        return false;
    }

    public boolean showPassRedDot(){
        for (MonthTowerMissionTemplate template : MonthTowerService.getTowerMissionTemplates().values()) {
            if (template.seasonId != getSelfSession() || template.rewardTemplates.isEmpty()) {
                continue;
            }
            int missionId = template.missionId;
            if(getPassMissions().containsKey(missionId) && !getLevelReceiveIds().contains(missionId)){
                return true;
            }
        }
        return false;
    }

    public boolean canChallenge(){
        MonthTowerGlobalData globalData = GlobalDataManager.getData(GlobalDataType.monthTower);
        for (MonthTowerMissionTemplate template : MonthTowerService.getTowerMissionTemplates().values()) {
            if(template.seasonId != getSelfSession()){
                continue;
            }
            if(template.openDay <= globalData.calOpenDay() && !passMissions.containsKey(template.missionId)){
                return true;
            }
        }
        return false;
    }

    /**
     * 获取可以领取星数奖励
     */
    public List<Integer> getCanReceiveStarIds() {
        List<Integer> canReceiveSlot = new ArrayList<>();
        int totalStar = getTotalStar();
        MonthTowerGlobalData globalData = GlobalDataManager.getData(GlobalDataType.monthTower);
        MonthTowerService.getStarRewardTemplateMap().get(globalData.getSeasonId()).values().stream()
                .filter(template -> template.starNum <= totalStar)
                .forEach(template -> canReceiveSlot.add(template.id));
        return canReceiveSlot;
    }

    public int getTreasureHadCostStarNum(){
        int allCostStarNum = 0;
        for (Integer id : treasureInfos.keySet()) {
            MonthTowerTreasureSkillTemplate treasureSkillTemplate = MonthTowerService.getTreasureSkillTemplateMap().get(id);
            int level = treasureInfos.get(id);
            for (int i = 1; i <= level; i++) {
                allCostStarNum += treasureSkillTemplate.getLevelUpCost(i);
            }

        }
        return allCostStarNum;
    }

    public void setMinPassTimeInterval(long minPassTimeInterval) {
        this.minPassTimeInterval = minPassTimeInterval;
    }

    public long getMinPassTimeInterval() {
        return minPassTimeInterval;
    }

    public long getMaxHurt() {
        return maxHurt;
    }

    public void setMaxHurt(long maxHurt) {
        this.maxHurt = maxHurt;
    }

    public Map<Integer, Set<PbMonthTower.MonthTowerDifficulty>> getPassMissions() {
        return passMissions;
    }

    public void setPassMissions(Map<Integer, Set<PbMonthTower.MonthTowerDifficulty>> passMissions) {
        this.passMissions = passMissions;
    }

    public List<Integer> getSeasonRewardReceiveLevelIds() {
        return seasonRewardReceiveLevelIds;
    }

    public void setSeasonRewardReceiveLevelIds(List<Integer> seasonRewardReceiveLevelIds) {
        this.seasonRewardReceiveLevelIds = seasonRewardReceiveLevelIds;
    }

    public List<Integer> getStarRewardReceiveIds() {
        return starRewardReceiveIds;
    }

    public void setStarRewardReceiveIds(List<Integer> starRewardReceiveIds) {
        this.starRewardReceiveIds = starRewardReceiveIds;
    }

    public List<Integer> getLevelReceiveIds() {
        return levelReceiveIds;
    }

    public void setLevelReceiveIds(List<Integer> levelReceiveIds) {
        this.levelReceiveIds = levelReceiveIds;
    }

    public Map<Integer, Integer> getTreasureInfos() {
        return treasureInfos;
    }

    public void setTreasureInfos(Map<Integer, Integer> treasureInfos) {
        this.treasureInfos = treasureInfos;
    }

    public int getSelfSession() {
        return selfSession;
    }

    public void setSelfSession(int selfSession) {
        this.selfSession = selfSession;
    }

    public int getMaxRound() {
        return maxRound;
    }

    public void setMaxRound(int maxRound) {
        this.maxRound = maxRound;
    }

    public void reset(){
        MonthTowerGlobalData globalData = GlobalDataManager.getData(GlobalDataType.monthTower);
        if(MonthTowerService.getAllMissionIdMap().containsKey(selfSession)){
            //补发赛季奖励领取id
            List<Integer> seasonRewardReceiveLevelIds = new ArrayList<>(this.seasonRewardReceiveLevelIds);
            //补发赛季奖励星数领取id
            List<Integer> starRewardReceiveIds = new ArrayList<>(this.starRewardReceiveIds);
            //补发关卡奖励
            List<Integer> levelReceiveIds = new ArrayList<>(this.levelReceiveIds);
    
            //赛季奖励
            List<RewardTemplate> seasonRewards = new ArrayList<>();
            MonthTowerConfig config = MonthTowerService.getConfig();
            int level = 1;
            if(passMissions.containsKey(config.getSeasonRewardReq1MissionId(selfSession))
                && !seasonRewardReceiveLevelIds.contains(level)){
                //自动选择初级奖励
                for (MonthTowerSeasonRewardTemplate seasonRewardTemplate : MonthTowerService.getSeasonRewardTemplateMap().get(selfSession).get(level).values()) {
                    seasonRewards.addAll(seasonRewardTemplate.rewards1);
                }
            }
            level = 2;
            if(passMissions.containsKey(config.getSeasonRewardReq2MissionId(selfSession))
                    && !seasonRewardReceiveLevelIds.contains(level)){
                //自动选择高级奖励
                for (MonthTowerSeasonRewardTemplate seasonRewardTemplate : MonthTowerService.getSeasonRewardTemplateMap().get(selfSession).get(level).values()) {
                    seasonRewards.addAll(seasonRewardTemplate.rewards1);
                }
            }
            sendMail(MailType.monthTowerSeasonReward, seasonRewards);
    
            //星级奖励
            Map<Integer, MonthTowerStarRewardTemplate> starRewardTemplateMap = MonthTowerService.getStarRewardTemplateMap().get(selfSession);
            int totalStar = getTotalStar();
            if(CollectionUtil.isNotEmpty(starRewardTemplateMap)){
                List<RewardTemplate> starRewards = new ArrayList<>();
                for (MonthTowerStarRewardTemplate starRewardTemplate : starRewardTemplateMap.values()) {
                    if(totalStar >= starRewardTemplate.starNum
                        && !starRewardReceiveIds.contains(starRewardTemplate.starNum)){
                        starRewards.addAll(starRewardTemplate.rewards);
                    }
                }
                sendMail(MailType.monthTowerStarReward, starRewards);
            }
    
            //关卡奖励
            List<RewardTemplate> levelRewards = new ArrayList<>();
            for (MonthTowerMissionTemplate missionTemplate : MonthTowerService.getTowerMissionTemplates().values()) {
                if(missionTemplate.seasonId == selfSession && passMissions.containsKey(missionTemplate.missionId)
                    && !levelReceiveIds.contains(missionTemplate.missionId)){
                    levelRewards.addAll(missionTemplate.rewardTemplates);
                }
            }
            if(CollectionUtil.isNotEmpty(levelRewards)){
                sendMail(MailType.monthTowerMissionReward, levelRewards);
            }
        }


        selfSession = globalData.getSeasonId();

        this.passMissions.clear();
        this.treasureInfos.clear();
        this.seasonRewardReceiveLevelIds.clear();
        this.hadReceiveRewardInfos.clear();
        this.starRewardReceiveIds.clear();
        this.levelReceiveIds.clear();
        this.maxRound = 0;
    }

    private void sendMail(MailType mailType, List<RewardTemplate> rewards){
        PbCommons.PbText title = Text.genText(mailType.getTitleId()).build();
        PbCommons.PbText content = Text.genText(mailType.getContentId()).build();
        MailManager.sendMail(mailType, getPlayerId(), title, content, ServerConstants.getCurrentTimeMillis(), Reward.templateCollectionToReward(rewards));
    }

    public Map<Integer, Map<Integer, Integer>> getHadReceiveRewardInfos() {
        return hadReceiveRewardInfos;
    }

    public void setHadReceiveRewardInfos(Map<Integer, Map<Integer, Integer>> hadReceiveRewardInfos) {
        this.hadReceiveRewardInfos = hadReceiveRewardInfos;
    }

    @Override
    protected void loadData(PlayerBlobDb playerBlob) {
        PlayerMonthTowerModelDb monthTowerModelDb = playerBlob.getMonthTowerModelDb();
        if (monthTowerModelDb != null) {
            seasonRewardReceiveLevelIds = monthTowerModelDb.getSeasonRewardReceiveLevelIds();
            starRewardReceiveIds = monthTowerModelDb.getStarRewardReceiveIds();
            levelReceiveIds = monthTowerModelDb.getLevelReceiveIds();
            treasureInfos = monthTowerModelDb.getTreasureInfos();
            selfSession = monthTowerModelDb.getSelfSession();
            maxHurt = monthTowerModelDb.getMinPassTime();
            minPassTimeInterval = monthTowerModelDb.getMinPassTimeInterval();
            for (MonthTowerPassInfoDb passMissionDb : monthTowerModelDb.getPassMissionDbs()) {
                Set<PbMonthTower.MonthTowerDifficulty> difficultyList = new HashSet<>();
                for (int difficulty : passMissionDb.getDifficulty()) {
                    difficultyList.add(PbMonthTower.MonthTowerDifficulty.forNumber(difficulty));
                }
                passMissions.put(passMissionDb.getMissionId(), difficultyList);
            }
            maxRound = monthTowerModelDb.getMaxRound();
            for (MonthTowerHadReceiveRewardDb receiveRewardDb : monthTowerModelDb.getReceiveRewardDbs()) {
                hadReceiveRewardInfos.put(receiveRewardDb.getLevel(), receiveRewardDb.getRewardInfos());
            }
        }
    }

    @Override
    protected void saveData(PlayerBlobDb playerBlob) {
        PlayerMonthTowerModelDb monthTowerModelDb = new PlayerMonthTowerModelDb();
        monthTowerModelDb.getSeasonRewardReceiveLevelIds().addAll(seasonRewardReceiveLevelIds);
        monthTowerModelDb.getStarRewardReceiveIds().addAll(starRewardReceiveIds);
        monthTowerModelDb.getLevelReceiveIds().addAll(levelReceiveIds);
        monthTowerModelDb.getTreasureInfos().putAll(treasureInfos);
        monthTowerModelDb.setSelfSession(selfSession);
        monthTowerModelDb.setMinPassTime(maxHurt);
        monthTowerModelDb.setMinPassTimeInterval(minPassTimeInterval);
        for (Integer missionId : passMissions.keySet()) {
            MonthTowerPassInfoDb monthTowerPassInfoDb = new MonthTowerPassInfoDb();
            monthTowerPassInfoDb.setMissionId(missionId);
            for (PbMonthTower.MonthTowerDifficulty monthTowerDifficulty : passMissions.get(missionId)) {
                monthTowerPassInfoDb.getDifficulty().add(monthTowerDifficulty.getNumber());
            }
            monthTowerModelDb.getPassMissionDbs().add(monthTowerPassInfoDb);
        }
        monthTowerModelDb.setMaxRound(maxRound);
        for (Integer level : hadReceiveRewardInfos.keySet()) {
            Map<Integer, Integer> rewardInfos = hadReceiveRewardInfos.get(level);
            MonthTowerHadReceiveRewardDb receiveRewardDb = new MonthTowerHadReceiveRewardDb();
            receiveRewardDb.setLevel(level);
            receiveRewardDb.setRewardInfos(rewardInfos);
            monthTowerModelDb.getReceiveRewardDbs().add(receiveRewardDb);
        }
        playerBlob.setMonthTowerModelDb(monthTowerModelDb);
    }


    @Override
    public PlayerEventType[] getEventTypes() {
        return eventTypes;
    }

    @Override
    public void handle(PlayerEvent event) {
        switch (event.getEventType()) {
            //如果刷新的时候玩家在线，这个刷新方法不会走到里面去，除非是很久之前上线的玩家
            //因为是先跑PlayerService里的dayRefresh再跑world里的day5clock，这些玩家靠World里的reset刷新，
            //world刷新后其他的再登录上来的玩家，靠这个方法刷新
            case functionCheck: {
                if (Function.monthTower.isOpen(event.getSource())) {
                    //初次开启，直接取世界数据第几届
                    if (selfSession <= 0) {
                        MonthTowerGlobalData globalData = GlobalDataManager.getData(GlobalDataType.monthTower);
                        selfSession = globalData.getSeasonId();
                    }
                }
                break;
            }
            case login:
            case day5Refresh: {
                MonthTowerGlobalData globalData = GlobalDataManager.getData(GlobalDataType.monthTower);
                if (Function.monthTower.isOpen(event.getSource()) &&
                        selfSession != globalData.getSeasonId()) {
                    reset();
                }
                break;
            }
        }
    }
}

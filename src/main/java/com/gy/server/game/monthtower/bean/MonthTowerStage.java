package com.gy.server.game.monthtower.bean;

import com.gy.server.game.attribute.AttributeKey;
import com.gy.server.game.attribute.Attributes;
import com.gy.server.game.battleCollect.BattleCollectService;
import com.gy.server.game.battleCollect.template.BattleCollectTemplate;
import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.combat.CombatManager;
import com.gy.server.game.combat.Formula;
import com.gy.server.game.combat.StageType;
import com.gy.server.game.combat.unit.Camp;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.combat.unit.TeamUnit;
import com.gy.server.game.function.Function;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.hero.MiniHero;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.monster.MonsterService;
import com.gy.server.game.monster.MonsterTemplate;
import com.gy.server.game.monthtower.MonthTowerGlobalData;
import com.gy.server.game.monthtower.MonthTowerService;
import com.gy.server.game.monthtower.PlayerMonthTowerModel;
import com.gy.server.game.monthtower.template.MonthTowerMissionTemplate;
import com.gy.server.game.monthtower.template.MonthTowerSeasonTemplate;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.event.PlayerEventType;
import com.gy.server.game.record.RecordManager;
import com.gy.server.game.record.RecordType;
import com.gy.server.game.record.combat.CombatRecord;
import com.gy.server.game.reddot.RedDot;
import com.gy.server.game.text.Text;
import com.gy.server.game.world.World;
import com.gy.server.game.world.WorldLevelUseTypeEnum;
import com.gy.server.packet.PbCommons;
import com.gy.server.packet.PbMonthTower;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.MathUtil;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;

/**
 * <AUTHOR> -[Create on 2022/5/28]
 */
public class MonthTowerStage extends AbstractStage {

    public static final String ACHIVE_GOLED = "2";
    public static final String ACHIVE_SILVER = "1";
    /**
     * 青铜成就
     */
    public static final String ACHIVE_NONE = "0";

    private Player player;
    private MonthTowerMissionTemplate missionTemplate;
    /**
     * 是否达成白银和黄金成就
     */
    public boolean achieveSilverCondition;
    public boolean achieveGoldCondition;

    private Set<String> strategyAchives = new HashSet<>();
    int seasonId;
    final LineupType lineupType;

    public MonthTowerStage(Player player, int seasonId, MonthTowerMissionTemplate missionTemplate) {
        this.player = player;
        this.missionTemplate = missionTemplate;
        this.seasonId = seasonId;
        this.lineupType = LineupType.getType(missionTemplate.lineUp);
    }

    @Override
    public void init() {
        List<TeamUnit> atkTeamList = player.getLineupModel().createTeamUnits(this, lineupType.getStageType(), lineupType, false);
        //构建宝物
        PlayerMonthTowerModel model = player.getMonthTowerModel();
        //增加额外属性
        Attributes addedAttributes = model.getTreasureAttributes();
        for (TeamUnit atkTeam : atkTeamList) {
            for (HeroUnit atkUnit : atkTeam.getFormation()) {
                Attributes attributes = atkUnit.getAttributes();
                Attributes.mergeAttributes(attributes, addedAttributes);
                //重新计算面板属性
                Formula.calPanelAttrs(attributes, attributes);
                attributes.refreshCurrentValue();
            }
        }

        //防守方阵容初始化
        List<TeamUnit> defenderTeams = new ArrayList<>();
        int worldLevel = WorldLevelUseTypeEnum.louLan.getWorldLevel();
        for (Integer battleCollectId : missionTemplate.battleCollectIds) {
            BattleCollectTemplate battleCollectTemplate = BattleCollectService.getBattleCollectTemplateMap().get(battleCollectId);
            List<HeroUnit> defs = battleCollectTemplate.genHeroUnits(worldLevel);
            for (HeroUnit def : defs) {
                Attributes attributes = def.getAttributes();
                attributes.setValue(AttributeKey.最大生命, (long)(attributes.getValue(AttributeKey.最大生命) * missionTemplate.hitPoint));
                attributes.setValue(AttributeKey.面板攻击, (long)(attributes.getValue(AttributeKey.面板攻击) * missionTemplate.attack));
                attributes.refreshCurrentValue();
            }
            TeamUnit def = new TeamUnit(getNewId(), defs);
            defenderTeams.add(def);
        }


        init(missionTemplate.battleCollectIds.get(0), StageType.monthTower, atkTeamList, defenderTeams, LineupType.monthTower);
    }

    @Override
    public void modifyTeamSoul(TeamUnit teamUnit, boolean isAtk) {
        HeroUnit teamSoul = teamUnit.getTeamSoul();
        //修改队魂
        MonthTowerSeasonTemplate seasonTemplate = MonthTowerService.getSeasonTemplateMap().get(seasonId);
        MonsterTemplate monsterTemplate = MonsterService.monsterTemplates.get(seasonTemplate.monsterId);
        if(monsterTemplate.id == teamSoul.getTid()){
            PlayerMonthTowerModel model = player.getMonthTowerModel();
            teamSoul.addSkills(model.getTreasureSkillInfo());
        }
    }

    @Override
    public void afterFinish() {
        PbProtocol.CombatSettlementNotify.Builder rst = genCombatSettlement();

        PlayerMonthTowerModel model = player.getMonthTowerModel();
        int missionId = missionTemplate.missionId;

        PbProtocol.MonthTowerSettlement.Builder settlement = PbProtocol.MonthTowerSettlement.newBuilder();
        logic:
        {
            //如果已经被动过了，本场不算，客户端只要收到这个，就把玩家弹出去
            if (model.getSelfSession() != seasonId) {
                rst.setResult(Text.genServerRstInfo(Text.该次战斗不纳入计算));
                break logic;
            }
            List<Integer> missionIds = MonthTowerService.getAllMissionIdMap().get(model.getSelfSession());
            boolean isLastMission = missionIds.get(missionIds.size() - 1) == missionId;
            if (isWin() || isLastMission) {
                settlement.addAllRemainingHpRates(getAtkRemainingHpRates());
                boolean getNewStar = false;
                Map<Integer, Set<PbMonthTower.MonthTowerDifficulty>> passMission = model.getPassMissions();

                Set<PbMonthTower.MonthTowerDifficulty> difficultyList = passMission.computeIfAbsent(missionId, j-> new HashSet<>());

                //青铜
                if (missionTemplate.isFinish(player, this, missionTemplate.bronzeCondition)) {
                    strategyAchives.add(ACHIVE_NONE);

                    if (difficultyList.add(PbMonthTower.MonthTowerDifficulty.BRONZE)){
                        getNewStar = true;
                    }
                }

                //白银
                if (missionTemplate.isFinish(player, this, missionTemplate.silverCondition)) {
                    strategyAchives.add(ACHIVE_SILVER);
                    achieveSilverCondition = true;

                    if (difficultyList.add(PbMonthTower.MonthTowerDifficulty.SILVER)){
                        getNewStar = true;
                    }
                }

                //黄金
                if (missionTemplate.isFinish(player, this, missionTemplate.goldCondition)) {
                    strategyAchives.add(ACHIVE_GOLED);
                    achieveGoldCondition = true;

                    if (difficultyList.add(PbMonthTower.MonthTowerDifficulty.GOLD)){
                        getNewStar = true;
                    }
                }

                if (getNewStar) {
                    RedDot.monthTowerStarRewards.sync(player);
                    RedDot.monthTowerPassRewards.sync(player);
                    RedDot.monthTowerCanChoiceReward.sync(player);
                }

                //最后一关通关
                if(isLastMission){
                    long damage = CombatManager.getCombatDamage(getAtks(), this);
                    if(model.getMaxHurt() < damage){
                        //尝试进入排行榜
                        MonthTowerGlobalData globalData = GlobalDataManager.getData(GlobalDataType.monthTower);
                        MonthTowerRankInfo rankInfo = new MonthTowerRankInfo(player.getPlayerId(), damage);
                        List<MiniHero> miniHeroes = player.getLineupModel().getMiniHeroIncludeProtagonist(lineupType);
                        for (MiniHero miniHero : miniHeroes) {
                            rankInfo.getLineupInfos().add(miniHero.genMiniHero());
                        }
                        model.setMaxHurt(damage);
                        globalData.addRank(rankInfo);
                    }
                    
                }

                player.postEvent(PlayerEventType.passMonthTower, missionId);
                settlement.addAllFinishedDifficulty(passMission.get(missionId));
                Function.monthTower.syncFunction(player);

                // 生成战报
                CombatRecord combatRecord = CombatRecord.create(
                        this,
                        player.getMiniPlayer(),
                        null,
                        CombatRecord.OverdueTime.MonthTower, RecordType.monthTower.getId());
                RecordManager.save(combatRecord);
            }
        }

        rst.setMonthTower(settlement);

        notifyCombatSettlement(player, rst.build());

        player.postEvent(PlayerEventType.partakeMonthTower, missionId, isWin);
    }

    @Override
    protected void judgeWinner() {
        //进攻方全胜才算胜利
        List<TeamUnit> defList = this.getDefs();
        if (defList == null) {
            this.isWin = true;
            return;
        }
        for (TeamUnit def : defList) {
            // 守方全挂
            if (!def.isAllFail()) {
                this.isWin = false;
                return;
            }
        }
        this.isWin = true;
    }

    @Override
    public List<Camp> changeTeam() {
        //阵容切换 一队打一队  二队打二队
        List<Camp> list = new ArrayList<>();
        for (Camp camp : getCamps()) {
            list.add(camp);
            camp.setCurrentTeamIndex(camp.getCurrentTeamIndex() + 1);
        }
        return list;
    }

    /**
     * 获取攻方剩余血量百分比
     * intKey: 战斗内实例ID, longKey: 站位index(0 ~ 5), intValue: 血量百分比(除100使用)
     */
    public List<PbCommons.KeyValueDb> getAtkRemainingHpRates() {
        List<PbCommons.KeyValueDb> result = new ArrayList<>();

        Camp camp = getAtkCamp();
        List<TeamUnit> atks = camp.getTeams();
        if (CollectionUtil.isNotEmpty(atks)) {
            PbCommons.KeyValueDb.Builder kv = PbCommons.KeyValueDb.newBuilder();
            for (int i = 0; i < atks.size(); i++) {
                TeamUnit teamUnit = atks.get(i);

                Map<Integer, HeroUnit> formationMap = teamUnit.getFormationMap();
                for (int j = 0; j < teamUnit.getUnits().size(); j++) {
                    HeroUnit heroUnit = formationMap.get(j);
                    if (Objects.nonNull(heroUnit)) {
                        // 仅保留整数
                        long hpRemainingRate = heroUnit.getAttributes().getHp() * 100 / heroUnit.getAttributes().getValue(AttributeKey.最大生命);
                        hpRemainingRate = MathUtil.max(hpRemainingRate, 0);
                        hpRemainingRate = MathUtil.min(hpRemainingRate, 100);

                        kv.setIntKey(heroUnit.getId())
                                .setLongKey(j)
                                .setIntValue((int) hpRemainingRate)
                                .setLongValue(i + 1);

                        kv.setBoolValue(heroUnit.isAlive());
                        result.add(kv.build());
                        kv.clear();
                    }
                }
            }
        }

        return result;
    }

    /**
     * 获取攻方真实英雄人数和总上阵人数
     */
    public Pair<List<Integer>, Integer> getAtkTrulyHeroIdsAndTotalNum() {
        List<Integer> trulyHeroIds = new ArrayList<>();
        int totalNum = 0;

        Camp camp = getAtkCamp();
        List<TeamUnit> atks = camp.getTeams();
        for (TeamUnit teamUnit : atks) {
            for (HeroUnit heroUnit : teamUnit.getUnits()) {
                if (Objects.nonNull(heroUnit)) {
                    totalNum++;

                    if (!heroUnit.isMonster()) {
                        trulyHeroIds.add(heroUnit.getInstanceId());
                    }
                }
            }
        }

        return Pair.of(trulyHeroIds, totalNum);
    }

    @Override
    public Pair<Integer, Integer> getAtkDefLevel() {
        return Pair.of(World.getWorldLevel(), World.getWorldLevel());
    }
}

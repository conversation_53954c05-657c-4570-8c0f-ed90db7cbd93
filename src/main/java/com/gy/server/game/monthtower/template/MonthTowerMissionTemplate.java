package com.gy.server.game.monthtower.template;

import java.util.ArrayList;
import java.util.List;

import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.monthtower.condition.MonthTowerChallengeCondition;
import com.gy.server.game.player.Player;
import com.gy.server.utils.CollectionUtil;

/**
 * <AUTHOR> -[Create on 2022/5/25]
 */
public class MonthTowerMissionTemplate {

    public int missionId;

    public int seasonId;

    public List<MonthTowerChallengeCondition> bronzeCondition = new ArrayList<>();
    public List<MonthTowerChallengeCondition> silverCondition = new ArrayList<>();
    public List<MonthTowerChallengeCondition> goldCondition = new ArrayList<>();

    public List<RewardTemplate> rewardTemplates = new ArrayList<>();

    public List<Integer> battleCollectIds = new ArrayList<>();

    //攻击倍率
    public double attack;
    //生命倍率
    public double hitPoint;
    //开启天数
    public int openDay;
    //布阵id
    public int lineUp;

    public boolean isFinish (Player player, AbstractStage stage, List<MonthTowerChallengeCondition> list) {
        if(CollectionUtil.isEmpty(list)){
            return false;
        }
        return list.stream().allMatch(condition -> condition.isFinish(player, stage));
    }
}

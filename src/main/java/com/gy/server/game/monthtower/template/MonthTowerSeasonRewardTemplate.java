package com.gy.server.game.monthtower.template;

import java.util.ArrayList;
import java.util.List;

import com.gy.server.game.drop.RewardTemplate;

/**
 * 楼兰寻宝-赛季奖励
 * <AUTHOR> - [Created on 2024-06-06 19:28]
 */
public class MonthTowerSeasonRewardTemplate {

    //奖励点位id
    public int id;
    //赛季id
    public int seasonId;
    //等级（初级/高级）
    public int level;
    //奖励
    public List<RewardTemplate> rewards1 = new ArrayList<>();
    public List<RewardTemplate> rewards2 = new ArrayList<>();
    public List<RewardTemplate> rewards3 = new ArrayList<>();
    public List<RewardTemplate> rewards4 = new ArrayList<>();
    public List<RewardTemplate> rewards5 = new ArrayList<>();

    public List<RewardTemplate> getReward(int index){
        switch (index){
            case 0:{
                return rewards1;
            }
            case 1:{
                return rewards2;
            }
            case 2:{
                return rewards3;
            }
            case 3:{
                return rewards4;
            }
            case 4:{
                return rewards5;
            }
            default:{
                return null;
            }
        }
    }

}

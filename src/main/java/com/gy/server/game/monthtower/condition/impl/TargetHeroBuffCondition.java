package com.gy.server.game.monthtower.condition.impl;

import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.combat.CombatService;
import com.gy.server.game.combat.buff.Buff;
import com.gy.server.game.combat.buff.BuffTemplate;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.combat.unit.TeamUnit;
import com.gy.server.game.monthtower.condition.MonthTowerChallengeCondition;
import com.gy.server.game.player.Player;
import com.gy.server.utils.CollectionUtil;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 某阵位敌人拥有或未拥有某个buff的情况下阵亡
 * 1. 参数1：阵位1-6
 * 2. 参数2：拥有或未拥有
 * 3. 参数3：buffTId
 * 3. 参数3：groupId
 * 3. 参数3：buffType
 */
public class TargetHeroBuffCondition implements MonthTowerChallengeCondition {

    private List<Integer> list = new ArrayList<>();

    private boolean have;

    private int buffTId;
    private int groupId;
    private int buffType;

    @Override
    public void initParams(String paramStr) {

        String[] split = paramStr.split("\\|");
        list = Arrays.stream(split[0].split("&")).map(j -> Integer.parseInt(j) - 1).collect(Collectors.toList());

        have = Integer.parseInt(split[1]) == 1;

        buffTId = Integer.parseInt(split[2]);

        groupId = Integer.parseInt(split[3]);

        buffType = Integer.parseInt(split[4]);
    }

    @Override
    public boolean isFinish(Player player, AbstractStage stage) {

        for (TeamUnit teamUnit : stage.getDefs()) {
            for (Integer i : list) {
                HeroUnit unit = teamUnit.getByPosIndex(i);
                if (unit == null || unit.isAlive()) {
                    return false;
                }
                if(unit.isTeamSoul()){
                    continue;
                }

                Set<Integer> set = new HashSet<>();
                for (Buff buff : unit.getBuffs().getAllBuffs()) {
                    BuffTemplate buffTemplate = CombatService.buffs.get(buff.getTid());
                    if (buffTemplate != null &&
                            ((buffTId > 0 && buffTemplate.id == buffTId) ||
                            (groupId > 0 && buffTemplate.groupId == groupId) ||
                            (buffType > 0 && buffTemplate.tag == buffType))
                        ){
                        set.add(buffTemplate.id);
                    }
                }
                if (have != CollectionUtil.isNotEmpty(set)){
                    return false;
                }
            }
        }
        return true;
    }
}

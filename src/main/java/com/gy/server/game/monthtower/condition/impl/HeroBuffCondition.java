package com.gy.server.game.monthtower.condition.impl;

import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.combat.CombatService;
import com.gy.server.game.combat.buff.Buff;
import com.gy.server.game.combat.buff.BuffTemplate;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.combat.unit.TeamUnit;
import com.gy.server.game.monthtower.condition.MonthTowerChallengeCondition;
import com.gy.server.game.player.Player;
import com.gy.server.utils.CollectionUtil;

import java.util.HashSet;
import java.util.Set;

/**
 *   战斗结算时，我方存活的人都拥有（或未拥有）某种buff
 *     1. 参数1：拥有或未拥有
 *     2. 参数2：buffType
 *     3. 参数3：groupId
 *     4. 参数4：buffType
 */
public class HeroBuffCondition implements MonthTowerChallengeCondition {

    private boolean have;

    private int buffType;
    private int buffTId;
    private int groupId;


    @Override
    public void initParams(String paramStr) {
        String[] split = paramStr.split("\\|");
        have = Integer.parseInt(split[0]) == 1;

        buffTId = Integer.parseInt(split[1]);

        groupId = Integer.parseInt(split[2]);

        buffType = Integer.parseInt(split[3]);
    }

    @Override
    public boolean isFinish(Player player, AbstractStage stage) {
        for (TeamUnit teamUnit : stage.getAtks()) {
            for (HeroUnit unit : teamUnit.getUnits()) {
                if (unit != null && unit.isAlive() && !unit.isTeamSoul()) {
                    Set<Integer> set = new HashSet<>();
                    for (Buff buff : unit.getBuffs().getAllBuffs()) {
                        BuffTemplate buffTemplate = CombatService.buffs.get(buff.getTid());
                        if (buffTemplate != null &&
                                ((buffTId > 0 && buffTemplate.id == buffTId) ||
                                        (groupId > 0 && buffTemplate.groupId == groupId) ||
                                        (buffType > 0 && buffTemplate.tag == buffType))
                        ){
                            set.add(buffTemplate.id);
                        }
                    }
                    if (have != CollectionUtil.isNotEmpty(set)){
                        return false;
                    }
                }
            }
        }
        return true;
    }


}

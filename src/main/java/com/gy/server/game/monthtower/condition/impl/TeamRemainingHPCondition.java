package com.gy.server.game.monthtower.condition.impl;

import com.gy.server.game.attribute.AttributeKey;
import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.combat.unit.TeamUnit;
import com.gy.server.game.monthtower.condition.MonthTowerChallengeCondition;
import com.gy.server.game.player.Player;

/**
 *  战斗结算时，我方所有角色剩余总血量大于X%
 *  1. 比例计算方式：我方全员剩余血量值总和/全员血量上限总和
 *  2. 参数1：X%
 */
public class TeamRemainingHPCondition implements MonthTowerChallengeCondition {

    private double hp;
    @Override
    public void initParams(String paramStr) {
        hp = Integer.parseInt(paramStr) / 100.0;
    }

    @Override
    public boolean isFinish(Player player, AbstractStage stage) {

        long now = 0;
        long total = 0;

        for (TeamUnit teamUnit : stage.getAtks()) {
            for (HeroUnit unit : teamUnit.getUnits()) {
                if (unit != null && !unit.isTeamSoul()) {
                    total += unit.getAttributes().getValue(AttributeKey.最大生命);
                    if (unit.isAlive()) {
                        now += unit.getAttributes().getHp();
                    }
                }
            }
        }
        double v = now * 1.0 / total;
        return hp < v;
    }
}

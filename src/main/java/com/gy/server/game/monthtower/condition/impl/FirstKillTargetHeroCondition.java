package com.gy.server.game.monthtower.condition.impl;

import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.combat.unit.TeamUnit;
import com.gy.server.game.monthtower.condition.MonthTowerChallengeCondition;
import com.gy.server.game.player.Player;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 最先或最后，击杀某个或某些阵位的敌人
 * 1. 参数1：最先或最后 ()
 * 2. 参数2：阵位1-6
 */
public class FirstKillTargetHeroCondition implements MonthTowerChallengeCondition {

    private boolean isFirst;

    private List<Integer> indexList = new ArrayList<>();
    @Override
    public void initParams(String paramStr) {
        String[] split = paramStr.split("\\|");

        isFirst = Integer.parseInt(split[0]) == 0;

        indexList = Arrays.stream(split[1].split("&")).map(j -> Integer.parseInt(j) - 1).collect(Collectors.toList());
    }

    @Override
    public boolean isFinish(Player player, AbstractStage stage) {

        TeamUnit teamUnit = stage.getDefs().get(0);

        List<Integer> heroIds = new ArrayList<>();

        List<Integer> deadUnitIds = stage.getDeadUnitIds();
        for (Integer index : indexList) {
            HeroUnit unit = teamUnit.getByPosIndex(index);
            if (unit == null) {
                return false;
            }

            if(unit.isTeamSoul()){
                continue;
            }

            if (unit.isAlive()) {
                return false;
            }
        }
        List<Integer> deadHeroIds = new ArrayList<>();
        for (Integer deadUnitId : deadUnitIds) {
            HeroUnit unit = teamUnit.getById(deadUnitId);
            if (Objects.nonNull(unit)) {
                if(indexList.contains(unit.getPosIndex())){
                    deadHeroIds.add(deadUnitId);
                }
                heroIds.add(unit.getId());
            }
        }

        if (indexList.size() > deadHeroIds.size()) {
            return false;
        }

        if (isFirst) {
            heroIds = heroIds.subList(0, indexList.size());
        }else {
            heroIds = heroIds.subList(heroIds.size() - indexList.size(), heroIds.size());
        }
        return heroIds.containsAll(deadHeroIds);
    }
}

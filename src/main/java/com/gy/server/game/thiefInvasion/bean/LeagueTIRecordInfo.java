package com.gy.server.game.thiefInvasion.bean;

import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.packet.PbLeague;
import com.ttlike.server.tl.baselib.serialize.league.LeagueThiefInvasionDb;
import org.apache.commons.lang3.tuple.Pair;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 贼兵入侵记录信息
 * @author: gbk
 * @date: 2024-09-30 09:47
 */
public class LeagueTIRecordInfo {

    //唯一id
    String key;
    //成员
    List<Long> memberIds = new ArrayList<>();
    //击破护盾数量
    int breakShieldCount;
    //精英怪伤害
    long eliteThiefDamage;
    //头目伤害
    long leaderThiefDamage;

    public LeagueThiefInvasionDb.LeagueTIRecordInfoDb writeToDb(){
        LeagueThiefInvasionDb.LeagueTIRecordInfoDb infoDb = new LeagueThiefInvasionDb.LeagueTIRecordInfoDb();
        infoDb.setKey(key);
        infoDb.setMemberIds(memberIds);
        infoDb.setBreakShieldCount(breakShieldCount);
        infoDb.setEliteThiefDamage(eliteThiefDamage);
        infoDb.setLeaderThiefDamage(leaderThiefDamage);
        return infoDb;
    }

    public void readFromDb(LeagueThiefInvasionDb.LeagueTIRecordInfoDb infoDb){
        this.key = infoDb.getKey();
        this.memberIds = infoDb.getMemberIds();
        this.breakShieldCount = infoDb.getBreakShieldCount();
        this.eliteThiefDamage = infoDb.getEliteThiefDamage();
        this.leaderThiefDamage = infoDb.getLeaderThiefDamage();
    }

    public LeagueTIRecordInfo(){

    }

    public LeagueTIRecordInfo(String key, List<Long> memberIds){
        this.key = key;
        this.memberIds = memberIds;
    }


    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public List<Long> getMemberIds() {
        return memberIds;
    }

    public void setMemberIds(List<Long> memberIds) {
        this.memberIds = memberIds;
    }

    public int getBreakShieldCount() {
        return breakShieldCount;
    }

    public void setBreakShieldCount(int breakShieldCount) {
        this.breakShieldCount = breakShieldCount;
    }

    public long getEliteThiefDamage() {
        return eliteThiefDamage;
    }

    public void setEliteThiefDamage(long eliteThiefDamage) {
        this.eliteThiefDamage = eliteThiefDamage;
    }

    public long getLeaderThiefDamage() {
        return leaderThiefDamage;
    }

    public void setLeaderThiefDamage(long leaderThiefDamage) {
        this.leaderThiefDamage = leaderThiefDamage;
    }

    public PbLeague.ThiefInvasionRecordInfo genPb(){
        PbLeague.ThiefInvasionRecordInfo.Builder builder = PbLeague.ThiefInvasionRecordInfo.newBuilder();
        for (Long memberId : memberIds) {
            MiniGamePlayer gamePlayer = PlayerManager.getMiniPlayer(memberId);
            builder.addMembers(gamePlayer.genMiniUser());
        }
        builder.setBreakShieldCount(breakShieldCount);
        builder.setEliteThiefDamage(eliteThiefDamage);
        builder.setLeaderThiefDamage(leaderThiefDamage);
        return builder.build();
    }

}
package com.gy.server.game.thiefInvasion.async;

import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.combat.CombatManager;
import com.gy.server.game.combat.StageType;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueModelEnums;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.team.TeamManager;
import com.gy.server.game.text.Text;
import com.gy.server.game.thiefInvasion.LeagueThiefInvasionModel;
import com.gy.server.game.thiefInvasion.ThiefInvasionService;
import com.gy.server.game.thiefInvasion.bean.LeagueTIStage;
import com.gy.server.game.thiefInvasion.template.ThiefInvasionConstant;
import com.gy.server.packet.PbProtocol;
import com.gy.server.world.team.base.TeamMemberInfo;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;

/**
 * GVE贼兵入侵获取组队布阵信息
 * @author: gbk
 * @date: 2024-09-26 14:51
 */
public class ThiefInvasionGetLineupAsync extends AsyncCall {
    Player player;
    boolean isTeam;
    boolean isTest;
    long time;
    List<List<HeroUnit>> teamUnits = new ArrayList<>();
    long teamId = -1;
    List<Long> members = new ArrayList<>();
    League league;
    long npcId;

    public ThiefInvasionGetLineupAsync(Player player, League league, boolean isTeam
            , boolean isTest, long time, long npcId){
        this.player = player;
        this.isTeam = isTeam;
        this.isTest = isTest;
        this.league = league;
        this.time = time;
        this.npcId = npcId;
    }

    @Override
    public void asyncExecute() {
        LineupType lineupType = LineupType.thiefInvasion;
        StageType stageType = StageType.thiefInvasion;
        if(isTeam){
            Pair<Long, Integer> teamIdByPlayerId = TeamManager.getInstance().getTeamIdByPlayerId(player.getPlayerId());
            long teamId = teamIdByPlayerId.getKey();
            this.teamId = teamId;
            Set<Long> memberList = TeamManager.getInstance().getMembers(teamId);
            List<TeamMemberInfo> memberInfos = new ArrayList<>();
            for (Long memberId : memberList) {
                TeamMemberInfo memberInfo = TeamManager.getInstance().getMemberInfo(memberId);
                if(Objects.nonNull(memberInfo)){
                    memberInfos.add(memberInfo);
                }
            }
            Collections.sort(memberInfos, (o1, o2) -> o1.getTeamPosition()- o2.getTeamPosition());

            for (TeamMemberInfo memberInfo : memberInfos) {
                Player member = PlayerManager.getPlayer(memberInfo.getPlayerId());
                List<HeroUnit> heroUnits = member.getLineupModel().createHeroUnits(stageType, lineupType);
                checkUnitsBuff(memberInfo.getPlayerId(), heroUnits);
                teamUnits.add(heroUnits);
                members.add(memberInfo.getPlayerId());
            }
        }else{
            List<HeroUnit> heroUnits = player.getLineupModel().createHeroUnits(stageType, lineupType);
            checkUnitsBuff(player.getPlayerId(), heroUnits);
            teamUnits.add(heroUnits);
            members.add(player.getPlayerId());
        }
    }

    /**
     * 检查buff过期
     */
    private void checkUnitsBuff(long memberId, List<HeroUnit> heroUnits){
        LeagueThiefInvasionModel thiefInvasionModel = league.getModel(LeagueModelEnums.thiefInvasion);
        ThiefInvasionConstant constant = ThiefInvasionService.getConstant();
        if(thiefInvasionModel.buffCanEffect(memberId)){
            for (HeroUnit heroUnit : heroUnits) {
                heroUnit.addBuff(constant.buff, 1);
            }
        }
    }

    @Override
    public void execute() {
        PbProtocol.ThiefInvasionCombatRst.Builder rst = PbProtocol.ThiefInvasionCombatRst.newBuilder().setResult(Text.genOkServerRstInfo());
        LeagueTIStage stage = new LeagueTIStage(league, teamId, npcId, isTest, members, teamUnits);
        stage.init();
        rst.setStage(stage.getStageRecord());
        rst.setIsTeam(isTeam);
        rst.setNpcId(npcId);
        rst.setIsTest(isTest);

        LeagueThiefInvasionModel thiefInvasionModel = league.getModel(LeagueModelEnums.thiefInvasion);
        thiefInvasionModel.getInCombatPlayerIds().addAll(members);
        CombatManager.combatPrepare(stage);
        for (Long member : members) {
            Player onlinePlayer = PlayerManager.getOnlinePlayer(member);
            onlinePlayer.send(PtCode.THIEF_INVASION_COMBAT_RST, rst.build(), time);
        }


    }
}
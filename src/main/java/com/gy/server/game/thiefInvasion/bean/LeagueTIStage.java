package com.gy.server.game.thiefInvasion.bean;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.attribute.AttributeKey;
import com.gy.server.game.battleCollect.BattleCollectService;
import com.gy.server.game.battleCollect.template.BattleCollectTemplate;
import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.combat.CbtRecord;
import com.gy.server.game.combat.StageType;
import com.gy.server.game.combat.unit.HeroUnit;
import com.gy.server.game.combat.unit.TeamUnit;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueModelEnums;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.mail.MailManager;
import com.gy.server.game.mail.MailType;
import com.gy.server.game.monster.MonsterTemplate;
import com.gy.server.game.player.MiniGamePlayer;
import com.gy.server.game.player.PlayerHelper;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.text.Text;
import com.gy.server.game.thiefInvasion.LeagueThiefInvasionModel;
import com.gy.server.game.thiefInvasion.ThiefInvasionService;
import com.gy.server.game.thiefInvasion.template.ThiefInvasionConstant;
import com.gy.server.game.thiefInvasion.template.ThiefInvasionDifficultyTemplate;
import com.gy.server.packet.PbCommons;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.time.DateTimeUtil;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;

/**
 * GVE贼兵入侵战斗
 * @author: gbk
 * @date: 2024-09-26 13:23
 */
public class LeagueTIStage extends AbstractStage {

    League league;
    long teamId;
    long npcId;
    boolean isTest;
    List<List<HeroUnit>> heroUnitList;
    List<Long> members;

    public LeagueTIStage(League league, long teamId, long npcId, boolean isTest, List<Long> members, List<List<HeroUnit>> heroUnitList){
        this.league = league;
        this.teamId = teamId;
        this.npcId = npcId;
        this.isTest = isTest;
        this.heroUnitList = heroUnitList;
        this.members = members;
    }

    @Override
    public void init() {
        LineupType lineupType = LineupType.thiefInvasion;
        StageType stageType = StageType.thiefInvasion;
        //进攻队伍
        List<TeamUnit> atkTeamUnits = new ArrayList<>();
        for (List<HeroUnit> heroUnits : heroUnitList) {
            atkTeamUnits.add(new TeamUnit(getNewId(), heroUnits));
        }
        //防守队伍
        LeagueThiefInvasionModel thiefInvasionModel = league.getModel(LeagueModelEnums.thiefInvasion);
        int npcTid;
        double shieldRatio = 10000L;
        long currentHp = -1;
        if(npcId > 0){
            LeagueTINpcInfo npcInfo = thiefInvasionModel.getNpcInfos().get(npcId);
            npcTid = npcInfo.getTempId();
            shieldRatio = npcInfo.getShieldRatio();
            currentHp = npcInfo.getCurrentHp();
        }else{
            ThiefInvasionConstant constant = ThiefInvasionService.getConstant();
            npcTid = constant.eliteThiefId;
        }
        ThiefInvasionDifficultyTemplate difficultyTemplate = ThiefInvasionService.getDifficultyTemplateMap().get(thiefInvasionModel.getDifficultyId());
        BattleCollectTemplate battleCollectTemplate = BattleCollectService.getBattleCollectTemplateMap().get(npcTid);
        List<HeroUnit> heroUnits = battleCollectTemplate.genHeroUnits(difficultyTemplate.monsterLevel);
        if(npcId > 0){
            defDeal(heroUnits, shieldRatio, currentHp);
        }
        List<TeamUnit> defTeamUnits = new ArrayList<>();
        defTeamUnits.add(new TeamUnit(getNewId(), heroUnits));

        init(npcTid, stageType, atkTeamUnits, defTeamUnits, lineupType);
    }

    long oldShieldNum = 0;
    long oldHp = 0;
    long maxShieldNum;
    long maxHp;

    /**
     * 防守方处理
     */
    private void defDeal(List<HeroUnit> heroUnits, double shieldRatio, long currentHp){
        for (HeroUnit heroUnit : heroUnits) {
            if(Objects.nonNull(heroUnit)){
                MonsterTemplate monsterTemplate = heroUnit.getMonsterTemplate();
                if(monsterTemplate.monsterType == 2){
                    maxShieldNum = heroUnit.getShieldValueTotal();
                    heroUnit.shieldValueDec(((10000L - (long)shieldRatio) * heroUnit.getShieldValueTotal() / 10000L), this.cbSection);
                    if(currentHp > 0){
                        heroUnit.getAttributes().setHp(currentHp);
                    }
                    //精英怪
                    oldShieldNum = heroUnit.getShieldValueTotal();
                    oldHp = heroUnit.getAttributes().getHp();
                    maxHp = heroUnit.getAttributes().getValue(AttributeKey.最大生命);
                }
            }
        }
    }

    @Override
    public void afterFinish() {
        LeagueThiefInvasionModel thiefInvasionModel = league.getModel(LeagueModelEnums.thiefInvasion);
        thiefInvasionModel.getInCombatPlayerIds().removeAll(members);

        PbProtocol.CombatSettlementNotify.Builder notify = genCombatSettlement();
        PbProtocol.LeagueTiSettlement.Builder settlement = PbProtocol.LeagueTiSettlement.newBuilder();
        if(!isTest){
            LeagueTINpcInfo npcInfo = thiefInvasionModel.getNpcInfos().get(npcId);
            //先解锁
            npcInfo.setLock(false);

            ThiefInvasionConstant constant = ThiefInvasionService.getConstant();
            //记录monster护盾和血量
            TeamUnit teamUnit = getDefs().get(0);
            long curShieldValue = 0;
            long curHp = 0;
            long reduceHp = 0;
            for (HeroUnit heroUnit : teamUnit.getFormation()) {
                if(Objects.nonNull(heroUnit)){
                    MonsterTemplate monsterTemplate = heroUnit.getMonsterTemplate();
                    if(monsterTemplate.monsterType == 2){
                        long defCurHp = npcInfo.getCurrentHp() > 0 ? npcInfo.getCurrentHp() : heroUnit.getAttributes().getValue(AttributeKey.最大生命);
                        reduceHp = oldHp - heroUnit.getAttributes().getHp();
                        npcInfo.setCurrentHp(defCurHp - reduceHp);
                        curShieldValue = heroUnit.getShieldValueTotal();
                        curHp = npcInfo.getCurrentHp();
                        //计算护盾比例
                        npcInfo.setShieldRatio(maxShieldNum > 0 ? curShieldValue * 10000L / maxShieldNum : 0);
                    }
                }
            }
            //记录伤害
            String key = buildRecordKey();
            Map<String, LeagueTIRecordInfo> recordInfos = thiefInvasionModel.getRecordInfos();
            LeagueTIRecordInfo recordInfo = recordInfos.getOrDefault(key, new LeagueTIRecordInfo(key, members));

            if(npcInfo.getTempId() == constant.leaderThiefId){
                //头目
                recordInfo.setLeaderThiefDamage(recordInfo.getLeaderThiefDamage() + reduceHp);
            }else{
                //精英
                recordInfo.setEliteThiefDamage(recordInfo.getEliteThiefDamage() + reduceHp);
            }
            //击破护盾
            if(curShieldValue <= 0 && oldShieldNum > 0){
                recordInfo.setBreakShieldCount(recordInfo.getBreakShieldCount() + 1);
                //队伍buff延期
                long buffTime = DateTimeUtil.toMillis(ServerConstants.getCurrentTimeLocalDateTime().plusSeconds(constant.buffInvalidatedTime));
                for (Long member : members) {
                    thiefInvasionModel.getBuffInfos().put(member, buffTime);
                }

                //进入护盾cd倒计时
                if(curHp > 0){
                    long shiedResetTime = DateTimeUtil.toMillis(ServerConstants.getCurrentTimeLocalDateTime().plusSeconds(constant.shieldResetTime));
                    npcInfo.setBuffResetTime(shiedResetTime);
                }
            }
            //记录信息
            recordInfos.put(key, recordInfo);

            if(npcInfo.getTempId() == constant.eliteThiefId){
                if(curHp <= 0){
                    ThiefInvasionDifficultyTemplate difficultyTemplate = ThiefInvasionService.getDifficultyTemplateMap().get(thiefInvasionModel.getDifficultyId());
                    //击败精英怪奖励
                    List<Reward> rewards = Reward.templateCollectionToReward(constant.eliteThiefRewards);
                    Map<Long, Integer> eliteThiefRewardTimes = thiefInvasionModel.getEliteThiefRewardTimes();
                    Map<Long, Integer> points = thiefInvasionModel.getPoints();
                    MailType mailType = MailType.leagueThiefInvasionKill;
                    PbCommons.PbText title = Text.genText(mailType.getTitleId()).build();
                    PbCommons.PbText content = Text.genText(mailType.getContentId()).build();
                    for (Long member : members) {
                        //发送击杀精英怪奖励邮件
                        int times = eliteThiefRewardTimes.getOrDefault(member, 0);
                        if(times < constant.eliteThiefRewardsTime){
                            eliteThiefRewardTimes.put(member, times + 1);
                            MailManager.sendMail(
                                    mailType,
                                    member,
                                    title,
                                    content,
                                    ServerConstants.getCurrentTimeMillis(),
                                    rewards);
                        }
                        //增加积分
                        points.put(member, points.getOrDefault(member, 0) + difficultyTemplate.eliteThiefPoint);
                    }

                    //检查是否可以生成精英怪
                    if(thiefInvasionModel.getNpcInfos().size() < difficultyTemplate.eliteThiefNum){
                        long allRadio = thiefInvasionModel.getAllRadio();
                        //在旧位置生成一个精英怪
                        LeagueTINpcInfo newNpc = LeagueTINpcInfo.create(constant.eliteThiefId, 10000L - allRadio, npcInfo.getPosition());
                        thiefInvasionModel.getNpcInfos().put(newNpc.getNpcId(), newNpc);
                    }
                }
            }else{
                //头目血量积分计算
                Map<Long, Integer> points = thiefInvasionModel.getPoints();
                ThiefInvasionDifficultyTemplate difficultyTemplate = ThiefInvasionService.getDifficultyTemplateMap().get(thiefInvasionModel.getDifficultyId());
                long 万分比 = 100L;//百分之一
                long 血量减少万分比 = reduceHp * 10000L / maxHp;
//                int 积分倍数 = (int)(血量减少万分比 % 万分比 > 0 ? 血量减少万分比 / 万分比 + 1L : 血量减少万分比 / 万分比);
                int 积分倍数 = (int)(血量减少万分比 / 万分比);
                for (Long member : members) {
                    points.put(member, points.getOrDefault(member, 0) + (difficultyTemplate.leaderThiefPoint * 积分倍数));
                }
            }

            finish:{
                if(curHp <= 0){
                    if(npcInfo.getTempId() == constant.leaderThiefId){
                        //击杀头目检查是否结束
                        thiefInvasionModel.reset();
                        break finish;
                    }
                    //检查精英怪是否都死亡
                    boolean isAllDead = true;
                    for (LeagueTINpcInfo tiNpcInfo : thiefInvasionModel.getNpcInfos().values()) {
                        if(tiNpcInfo.getCurrentHp() != 0){
                            isAllDead = false;
                            break;
                        }
                    }
                    if(isAllDead && !thiefInvasionModel.getStatus(LeagueThiefInvasionModel.TI_add_Monster)){
                        //生成头目
                        thiefInvasionModel.setStatus(LeagueThiefInvasionModel.TI_add_Monster, true);
                        LeagueTINpcInfo newNpc = LeagueTINpcInfo.create(constant.leaderThiefId, 10000L, constant.leaderThiefPoint);
                        thiefInvasionModel.getNpcInfos().put(newNpc.getNpcId(), newNpc);
                    }
                }
                thiefInvasionModel.sync(true);
            }
            //计算mvp
            fillMvp(settlement);
            settlement.setShieldRatio(npcInfo.getShieldRatio());
        }
        notify.setTiSettlement(settlement.build());
        for (Long member : members) {
            notifyCombatSettlement(member, notify.build());
        }
    }


    private void fillMvp(PbProtocol.LeagueTiSettlement.Builder settlement){
        List<TeamUnit> atks = getAtks();
        Map<Long, Pair<Long, Long>> playerHurtInfos = new HashMap<>();
        for (TeamUnit atk : atks) {
            for (HeroUnit heroUnit : atk.getFormation()) {
                if(Objects.nonNull(heroUnit)){
                    CbtRecord cbtRecord = this.getCbtRecord(atk.getTeamId(), heroUnit.getId());
                    if(!playerHurtInfos.containsKey(heroUnit.getPlayerId())){
                        playerHurtInfos.put(heroUnit.getPlayerId(), Pair.of(0L, 0L));
                    }
                    long playerId = heroUnit.getPlayerId();
                    Pair<Long, Long> orDefault = playerHurtInfos.getOrDefault(playerId, Pair.of(0L, 0L));
                    playerHurtInfos.put(playerId,  Pair.of(orDefault.getKey() + cbtRecord.getAtk(), orDefault.getValue() + cbtRecord.getShieldDamage()));
                }
            }
        }
        //检查护盾最大玩家id
        long mvpPlayerId = findMvpPlayerId(playerHurtInfos,true);
        if(mvpPlayerId > 0){
            MiniGamePlayer miniPlayer = PlayerManager.getMiniPlayer(mvpPlayerId);
            if(Objects.nonNull(miniPlayer)){
                settlement.setMvpInfo(miniPlayer.genMiniUser());
            }
        }else{
            //检查伤害最大玩家id
            mvpPlayerId = findMvpPlayerId(playerHurtInfos,false);
            MiniGamePlayer miniPlayer = PlayerManager.getMiniPlayer(mvpPlayerId);
            if(Objects.nonNull(miniPlayer)){
                settlement.setMvpInfo(miniPlayer.genMiniUser());
            }
        }
    }

    private long findMvpPlayerId(Map<Long, Pair<Long, Long>> playerHurtInfos, boolean isShieldDamage){
        long maxValue = 0;
        long mvpPlayerId = 0L;
        for (long playerId : playerHurtInfos.keySet()) {
            Pair<Long, Long> longLongPair = playerHurtInfos.get(playerId);
            long compareValue = isShieldDamage ? longLongPair.getValue() : longLongPair.getKey();
            if(maxValue < compareValue){
                maxValue = compareValue;
                mvpPlayerId = playerId;
            }
        }
        return mvpPlayerId;
    }

    private String buildRecordKey(){
        return members.toString();
    }

    @Override
    public void combatAbort(boolean isStart) {
        LeagueThiefInvasionModel thiefInvasionModel = league.getModel(LeagueModelEnums.thiefInvasion);
        members.forEach(thiefInvasionModel.getInCombatPlayerIds()::remove);
    }
}
package com.gy.server.game.thiefInvasion;

import com.gy.server.core.ServerConstants;
import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.function.Function;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.league.LeagueModelEnums;
import com.gy.server.game.league.enums.LeagueJobTypeEnums;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.service.Service;
import com.gy.server.game.team.TeamManager;
import com.gy.server.game.team.TeamService;
import com.gy.server.game.team.template.TeamTemplate;
import com.gy.server.game.text.Text;
import com.gy.server.game.thiefInvasion.async.ThiefInvasionGetLineupAsync;
import com.gy.server.game.thiefInvasion.bean.LeagueTIDonateInfo;
import com.gy.server.game.thiefInvasion.bean.LeagueTINpcInfo;
import com.gy.server.game.thiefInvasion.bean.LeagueTIRecordInfo;
import com.gy.server.game.thiefInvasion.template.ThiefInvasionConstant;
import com.gy.server.game.thiefInvasion.template.ThiefInvasionDifficultyTemplate;
import com.gy.server.game.thiefInvasion.template.ThiefInvasionRewardTemplate;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.StringUtil;
import com.gy.server.world.team.base.TeamInfo;
import com.ttlike.server.tl.baselib.serialize.scene.ScenePosition3D;
import com.ttlike.server.tl.baselib.thread.ThreadPool;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;

/**
 * GVE-贼兵入侵 （车队集结）
 * @author: gbk
 * @date: 2024-09-21 14:21
 */
public class ThiefInvasionService extends PlayerPacketHandler implements Service {

    private static ThiefInvasionConstant constant;
    private static Map<Integer, ThiefInvasionDifficultyTemplate> difficultyTemplateMap = new HashMap<>();
    private static TreeMap<Integer, ThiefInvasionRewardTemplate> rewardTemplateTreeMap = new TreeMap<>();
    public static int minPoint = 999999;
    public static int maxDifficultyId = -1;

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {

        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.leagueBanditsInvade_difficulty);
        Map<Integer, ThiefInvasionDifficultyTemplate> difficultyTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            ThiefInvasionDifficultyTemplate difficultyTemplate = new ThiefInvasionDifficultyTemplate();
            difficultyTemplate.id = Integer.parseInt(map.get("id"));
            difficultyTemplate.monsterLevel = Integer.parseInt(map.get("worldLevel"));
            difficultyTemplate.eliteThiefNum = Integer.parseInt(map.get("eliteThiefNum"));
            difficultyTemplate.eliteThiefPoint = Integer.parseInt(map.get("eliteThiefPoint"));
            difficultyTemplate.leaderThiefPoint = Integer.parseInt(map.get("leaderThiefPoint"));
            maxDifficultyId = Math.max(maxDifficultyId, difficultyTemplate.id);
            difficultyTemplateMapTemp.put(difficultyTemplate.id, difficultyTemplate);
        }
        difficultyTemplateMap = difficultyTemplateMapTemp;

        mapList = ConfigReader.read(ConfigFile.leagueBanditsInvade_rewards);
        TreeMap<Integer, ThiefInvasionRewardTemplate> rewardTemplateTreeMapTemp = new TreeMap<>();
        for (Map<String, String> map : mapList) {
            ThiefInvasionRewardTemplate rewardTemplate = new ThiefInvasionRewardTemplate();
            rewardTemplate.point = Integer.parseInt(map.get("point"));
            rewardTemplate.rewardsNum = Integer.parseInt(map.get("rewardsNum"));
            rewardTemplateTreeMapTemp.put(rewardTemplate.point, rewardTemplate);
            minPoint = Math.min(rewardTemplate.point, minPoint);
        }
        rewardTemplateTreeMap = rewardTemplateTreeMapTemp;

        mapList = ConfigReader.read(ConfigFile.leagueBanditsInvade_const);
        ThiefInvasionConstant constTemp = new ThiefInvasionConstant();
        for(Map<String, String> map : mapList){
            if("WeekNum".equals(map.get("key"))){
                constTemp.WeekNum = Integer.parseInt(map.get("value"));
            }else if("OpenTimePeriod".equals(map.get("key"))){
                constTemp.OpenTimePeriod = map.get("value");
            }else if("time".equals(map.get("key"))){
                constTemp.time = Long.parseLong(map.get("value"));
            }else if("initialDifficulty".equals(map.get("key"))){
                constTemp.initialDifficulty = Integer.parseInt(map.get("value"));
            }else if("shieldResetTime".equals(map.get("key"))){
                constTemp.shieldResetTime = Integer.parseInt(map.get("value"));
            }else if("buff".equals(map.get("key"))){
                constTemp.buff = Integer.parseInt(map.get("value"));
            }else if("buffInvalidatedTime".equals(map.get("key"))){
                constTemp.buffInvalidatedTime = Integer.parseInt(map.get("value"));
            }else if("personDailyNumber".equals(map.get("key"))){
                constTemp.personDailyNumber = Integer.parseInt(map.get("value"));
            }else if("personDailyNormalTrapNumber".equals(map.get("key"))){
                constTemp.personDailyNormalTrapNumber = Integer.parseInt(map.get("value"));
            }else if("personDailyEliteTrapNumber".equals(map.get("key"))){
                constTemp.personDailyEliteTrapNumber = Integer.parseInt(map.get("value"));
            }else if("totalNumber".equals(map.get("key"))){
                constTemp.totalNumber = Integer.parseInt(map.get("value"));
            }else if("normalTrapConsum".equals(map.get("key"))){
                constTemp.normalTrapConsum = RewardTemplate.readListFromText(map.get("value"));
            }else if("eliteTrapConsum".equals(map.get("key"))){
                constTemp.eliteTrapConsum = RewardTemplate.readListFromText(map.get("value"));
            }else if("normalTrapRewards".equals(map.get("key"))){
                constTemp.normalTrapRewards = RewardTemplate.readListFromText(map.get("value"));
            }else if("eliteTrapRewards".equals(map.get("key"))){
                constTemp.eliteTrapRewards = RewardTemplate.readListFromText(map.get("value"));
            }else if("normalTrapEffect".equals(map.get("key"))){
                constTemp.normalTrapEffect = Long.parseLong(map.get("value"));
            }else if("eliteTrapEffect".equals(map.get("key"))){
                constTemp.eliteTrapEffect = Long.parseLong(map.get("value"));
            }else if("eliteThiefRewards".equals(map.get("key"))){
                constTemp.eliteThiefRewards = RewardTemplate.readListFromText(map.get("value"));
            }else if("eliteThiefRewardsTime".equals(map.get("key"))){
                constTemp.eliteThiefRewardsTime = Integer.parseInt(map.get("value"));
            }else if("rewardsItemId".equals(map.get("key"))){
                constTemp.rewardsItemId = RewardTemplate.readListFromText(map.get("value"));
            }else if("eliteThiefId".equals(map.get("key"))){
                constTemp.eliteThiefId = Integer.parseInt(map.get("value"));
            }else if("leaderThiefId".equals(map.get("key"))){
                constTemp.leaderThiefId = Integer.parseInt(map.get("value"));
            }else if("eliteThiefRefreshCoordinate".equals(map.get("key"))){
                for (String points : map.get("value").split("\\|")) {
                    int[] ints = StringUtil.splitToIntArray(points, ",");
                    ScenePosition3D position3D = new ScenePosition3D();
                    position3D.setX(ints[0]);
                    position3D.setY(ints[1]);
                    position3D.setZ(ints[2]);
                    constTemp.eliteThiefPoints.add(position3D);
                }
            }else if("leaderThieRefreshCoordinate".equals(map.get("key"))){
                int[] ints = StringUtil.splitToIntArray(map.get("value"), ",");
                ScenePosition3D position3D = new ScenePosition3D();
                position3D.setX(ints[0]);
                position3D.setY(ints[1]);
                position3D.setZ(ints[2]);
                constTemp.leaderThiefPoint = position3D;
            }else if("fightMinimum".equals(map.get("key"))){
                constTemp.fightMinimum = Integer.parseInt(map.get("value"));
            }else if("contributionNum".equals(map.get("key"))){
                constTemp.contributionNum = Integer.parseInt(map.get("value"));
            }else if("eliteThiefRefreshMaxNum".equals(map.get("key"))){
                constTemp.minNpcCount = Integer.parseInt(map.get("value"));
            }else if("teamTypeId".equals(map.get("key"))){
                constTemp.teamTypeId = Integer.parseInt(map.get("value"));
            }
        }

        if(constTemp.minNpcCount > constTemp.eliteThiefPoints.size()){
            throw new RuntimeException("eliteThiefRefreshCoordinate count is error !!! ");
        }
        constant = constTemp;
    }

    public static ThiefInvasionConstant getConstant() {
        return constant;
    }

    public static Map<Integer, ThiefInvasionDifficultyTemplate> getDifficultyTemplateMap() {
        return difficultyTemplateMap;
    }

    public static TreeMap<Integer, ThiefInvasionRewardTemplate> getRewardTemplateTreeMap() {
        return rewardTemplateTreeMap;
    }

    @Override
    public void clearConfigData() {
        difficultyTemplateMap.clear();
        rewardTemplateTreeMap.clear();
    }

    private static int commonCheck(Player player) {
        //检查帮派限制
        League league = LeagueManager.getLeagueByPlayer(player);
        if(Objects.isNull(league)){
            return Text.未加入帮派;
        }

        //检查帮派限制
        if(Function.leagueBanditsInvade.isNotOpen(player)){
            return Text.功能未开启;
        }

        if(LeagueManager.canNotPlayLeague(player)){
            return Text.您退出帮派未满足24小时无法参与帮派活动;
        }
        if(!League.canPlay(player, Function.leagueBanditsInvade)){
            return Text.帮派玩法已经参加过;
        }
        return Text.没有异常;
    }

    /**
     * 设置开启时间
     */
    @Handler(PtCode.THIEF_INVASION_SET_OPENTIME_REQ)
    private void setOpenTime(Player player, PbProtocol.ThiefInvasionSetOpenTimeReq req, long time){
        PbProtocol.ThiefInvasionSetOpenTimeRst.Builder rst = PbProtocol.ThiefInvasionSetOpenTimeRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            long openTime = req.getOpenTime();
            //检查帮派限制
            int text = commonCheck(player);
            if(text != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(text));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            ThiefInvasionConstant constant = getConstant();
            if(league.getLeagueTribute() < constant.contributionNum){
                rst.setResult(Text.genServerRstInfo(Text.帮贡不足));
                break logic;
            }
            LeagueThiefInvasionModel thiefInvasionModel = league.getModel(LeagueModelEnums.thiefInvasion);
            if (!league.hasAuth(player.getPlayerId(), LeagueJobTypeEnums.设置活动开启时间)) {
                rst.setResult(Text.genServerRstInfo(Text.帮派权限不足));
                break logic;
            }
            //检查能否开启
            if(thiefInvasionModel.isOpen()){
                rst.setResult(Text.genServerRstInfo(Text.贼兵入侵_正在进行中));
                break logic;
            }
            if(ServerConstants.getCurrentTimeMillis() > req.getOpenTime()){
                rst.setResult(Text.genServerRstInfo(Text.参数异常));
                break logic;
            }
            //检查开启时间
            if(!constant.checkOpenTime(openTime)){
                rst.setResult(Text.genServerRstInfo(Text.贼兵入侵_开启时间错误));
                break logic;
            }
            if(thiefInvasionModel.getOpenTimes() >= constant.WeekNum){
                rst.setResult(Text.genServerRstInfo(Text.贼兵入侵_开启次数不足));
                break logic;
            }
//            LeagueBaseModel baseModel = league.getModel(LeagueModelEnums.base);
//            baseModel.setLeagueTribute(baseModel.getLeagueTribute() - constant.contributionNum);
            thiefInvasionModel.setStartTime(openTime);
            thiefInvasionModel.addOpenTimes();
            thiefInvasionModel.sync(false);
        }
        player.send(PtCode.THIEF_INVASION_SET_OPENTIME_RST, rst.build(), time);
    }

    /**
     * 捐献陷阱
     */
    @Handler(PtCode.THIEF_INVASION_DONATE_REQ)
    private void tiDonate(Player player, PbProtocol.ThiefInvasionDonateReq req, long time){
        PbProtocol.ThiefInvasionDonateRst.Builder rst = PbProtocol.ThiefInvasionDonateRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            //检查帮派限制
            int text = commonCheck(player);
            if(text != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(text));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            LeagueThiefInvasionModel thiefInvasionModel = league.getModel(LeagueModelEnums.thiefInvasion);
            if(thiefInvasionModel.isOpen()){
                rst.setResult(Text.genServerRstInfo(Text.贼兵入侵_当前时间不能捐献));
                break logic;
            }
            //检查捐献次数
            ThiefInvasionConstant constant = getConstant();
            LeagueTIDonateInfo donateInfo = thiefInvasionModel.getDonateInfo(player.getPlayerId());
            if(constant.personDailyNumber <= donateInfo.getAllDonateTime()){
                rst.setResult(Text.genServerRstInfo(Text.贼兵入侵_当天捐献次数已满));
                break logic;
            }
            if(constant.personDailyNormalTrapNumber <= donateInfo.getSimpleDonateTimes()){
                rst.setResult(Text.genServerRstInfo(Text.贼兵入侵_当天捐献次数已满));
                break logic;
            }
            if(constant.personDailyEliteTrapNumber <= donateInfo.getSeniorDonateTimes()){
                rst.setResult(Text.genServerRstInfo(Text.贼兵入侵_当天捐献次数已满));
                break logic;
            }
            if(constant.totalNumber <= thiefInvasionModel.getAllDonateTimes()){
                rst.setResult(Text.genServerRstInfo(Text.贼兵入侵_活动课捐献次数已满));
                break logic;
            }
            //检查消耗
            List<Reward> costs;
            List<Reward> rewards;
            if(req.getIsSenior()){
                costs = Reward.templateCollectionToReward(constant.eliteTrapConsum);
                rewards = Reward.templateCollectionToReward(constant.normalTrapRewards);
            }else{
                costs = Reward.templateCollectionToReward(constant.normalTrapConsum);
                rewards = Reward.templateCollectionToReward(constant.eliteTrapRewards);
            }
            if(Reward.check(player, costs) != -1){
                rst.setResult(Text.genServerRstInfo(Text.消耗不足));
                break logic;
            }
            Reward.remove(costs, player, BehaviorType.thiefInvasionDonate);
            Reward.add(rewards, player, BehaviorType.thiefInvasionDonate);

            if(req.getIsSenior()){
                donateInfo.addSeniorDonateTime();
            }else{
                donateInfo.addSimpleDonateTime();
            }
            thiefInvasionModel.getDonateInfoMap().put(player.getPlayerId(), donateInfo);
            thiefInvasionModel.sync(false);
        }
        player.send(PtCode.THIEF_INVASION_DONATE_RST, rst.build(), time);
    }

    /**
     * 进入战斗
     */
    @Handler(PtCode.THIEF_INVASION_COMBAT_REQ)
    private void tiCombat(Player player, PbProtocol.ThiefInvasionCombatReq req, long time){
        PbProtocol.ThiefInvasionCombatRst.Builder rst = PbProtocol.ThiefInvasionCombatRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:
        {
            boolean isTeam = req.getIsTeam();
            boolean isTest = req.getIsTest();
            long npcId = req.getNpcId();
            //检查帮派限制
            int text = commonCheck(player);
            if(text != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(text));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            LeagueThiefInvasionModel thiefInvasionModel = league.getModel(LeagueModelEnums.thiefInvasion);
            ThiefInvasionConstant constant = getConstant();
            if(isTest){
                Pair<Long, Integer> teamIdByPlayerId = TeamManager.getInstance().getTeamIdByPlayerId(player.getPlayerId());
                boolean canTeam = false;
                if(Objects.nonNull(teamIdByPlayerId)){
                    //满员才可以进行组队测试挑战
                    long teamId = teamIdByPlayerId.getKey();
                    TeamInfo teamInfo = TeamManager.getInstance().getTeamInfo(teamId);
                    TeamTemplate teamTemplate = TeamService.getTeamTemplate(teamInfo.getTeamTypeId());
                    Set<Long> members = TeamManager.getInstance().getMembers(teamId);
                    if(teamTemplate.playerNum <= members.size()){
                        canTeam = true;
                    }
                }
                //检查单人或组队模式
                if(!canTeam && isTeam){
                    rst.setResult(Text.genServerRstInfo(Text.贼兵入侵_人数不足));
                    break logic;
                }
                ThreadPool.execute(new ThiefInvasionGetLineupAsync(player, league, isTeam, isTest, time, -1));
                return;
            }else{
                if (!thiefInvasionModel.isOpen()) {
                    rst.setResult(Text.genServerRstInfo(Text.贼兵入侵_活动未开始));
                    break logic;
                }
                //检查队伍人数是否满
                Pair<Long, Integer> teamIdByPlayerId = TeamManager.getInstance().getTeamIdByPlayerId(player.getPlayerId());
                if(Objects.isNull(teamIdByPlayerId)){
                    rst.setResult(Text.genServerRstInfo(Text.组队你不在队伍));
                    break logic;
                }
                long teamId = teamIdByPlayerId.getKey();
                Set<Long> members = TeamManager.getInstance().getMembers(teamId);
                if(constant.fightMinimum > members.size()){
                    rst.setResult(Text.genServerRstInfo(Text.组队人数不足));
                    break logic;
                }
                LeagueTINpcInfo npcInfo = thiefInvasionModel.getNpcInfos().get(npcId);
                if(Objects.isNull(npcInfo)){
                    rst.setResult(Text.genServerRstInfo(Text.贼兵入侵_NPC不存在));
                    break logic;
                }
                if(npcInfo.getTempId() == constant.eliteThiefId){
                    if(npcInfo.isLock()){
                        rst.setResult(Text.genServerRstInfo(Text.贼兵入侵_NPC正在战斗));
                        break logic;
                    }
                    npcInfo.setLock(true);
                    //同步战斗状态
                    thiefInvasionModel.sync(true);
                }
                ThreadPool.execute(new ThiefInvasionGetLineupAsync(player, league,true, isTest, time, npcId));
                return;
            }
        }
        player.send(PtCode.THIEF_INVASION_COMBAT_RST, rst.build(), time);
    }

    @Handler(PtCode.THIEF_INVASION_RECORD_REQ)
    private void record(Player player, PbProtocol.ThiefInvasionRecordReq req, long time){
        PbProtocol.ThiefInvasionRecordRst.Builder rst = PbProtocol.ThiefInvasionRecordRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:
        {
            //检查帮派限制
            int text = commonCheck(player);
            if(text != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(text));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            LeagueThiefInvasionModel thiefInvasionModel = league.getModel(LeagueModelEnums.thiefInvasion);
            Map<String, LeagueTIRecordInfo> recordInfos = thiefInvasionModel.getRecordInfos();
            for (LeagueTIRecordInfo recordInfo : recordInfos.values()) {
                rst.addRecordInfos(recordInfo.genPb());
            }
        }
        player.send(PtCode.THIEF_INVASION_RECORD_RST, rst.build(), time);
    }

    @Handler(PtCode.THIEF_INVASION_GETINFO_REQ)
    public void getInfo(Player player, PbProtocol.ThiefInvasionGetInfoReq req, long time){
        PbProtocol.ThiefInvasionGetInfoRst.Builder rst = PbProtocol.ThiefInvasionGetInfoRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            //检查帮派限制
            int text = commonCheck(player);
            if(text != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(text));
                break logic;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            LeagueThiefInvasionModel thiefInvasionModel = league.getModel(LeagueModelEnums.thiefInvasion);
            rst.setInfo(thiefInvasionModel.genPb(player.getPlayerId()));
        }
        player.send(PtCode.THIEF_INVASION_GETINFO_RST, rst.build(), time);
    }


}

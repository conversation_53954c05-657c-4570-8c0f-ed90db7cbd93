package com.gy.server.game.thiefInvasion.bean;

import com.ttlike.server.tl.baselib.serialize.league.LeagueThiefInvasionDb;

/**
 * GVE-贼兵入侵-捐献信息
 * @author: gbk
 * @date: 2024-09-24 20:01
 */
public class LeagueTIDonateInfo {


    private long playerId;
    //普通捐献
    private int simpleDonateTimes;
    //高级捐献
    private int seniorDonateTimes;

    public LeagueThiefInvasionDb.LeagueTIDonateInfoDb writeToDb(){
        LeagueThiefInvasionDb.LeagueTIDonateInfoDb infoDb = new LeagueThiefInvasionDb.LeagueTIDonateInfoDb();
        infoDb.setPlayerId(playerId);
        infoDb.setSimpleDonateTimes(simpleDonateTimes);
        infoDb.setSeniorDonateTimes(seniorDonateTimes);
        return infoDb;
    }

    public void readFromDb(LeagueThiefInvasionDb.LeagueTIDonateInfoDb infoDb){
        this.playerId = infoDb.getPlayerId();
        this.simpleDonateTimes = infoDb.getSimpleDonateTimes();
        this.seniorDonateTimes = infoDb.getSeniorDonateTimes();
    }

    public LeagueTIDonateInfo(){

    }

    public LeagueTIDonateInfo(long playerId){
        setPlayerId(playerId);
    }

    public void addSimpleDonateTime(){
        simpleDonateTimes++;
    }

    public void addSeniorDonateTime(){
        seniorDonateTimes++;
    }

    public int getAllDonateTime(){
        return simpleDonateTimes + seniorDonateTimes;
    }

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public int getSimpleDonateTimes() {
        return simpleDonateTimes;
    }

    public void setSimpleDonateTimes(int simpleDonateTimes) {
        this.simpleDonateTimes = simpleDonateTimes;
    }

    public int getSeniorDonateTimes() {
        return seniorDonateTimes;
    }

    public void setSeniorDonateTimes(int seniorDonateTimes) {
        this.seniorDonateTimes = seniorDonateTimes;
    }
}
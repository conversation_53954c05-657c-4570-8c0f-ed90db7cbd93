package com.gy.server.game.thiefInvasion;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.combat.CombatManager;
import com.gy.server.game.combat.Stage;
import com.gy.server.game.combat.script.UA;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.function.Function;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueModel;
import com.gy.server.game.league.event.LeagueEvent;
import com.gy.server.game.league.event.LeagueEventHandler;
import com.gy.server.game.league.event.LeagueEventType;
import com.gy.server.game.mail.MailManager;
import com.gy.server.game.mail.MailType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.team.TeamHelper;
import com.gy.server.game.team.TeamManager;
import com.gy.server.game.text.Text;
import com.gy.server.game.text.TextParam;
import com.gy.server.game.text.TextParamText;
import com.gy.server.game.thiefInvasion.bean.LeagueTIDonateInfo;
import com.gy.server.game.thiefInvasion.bean.LeagueTINpcInfo;
import com.gy.server.game.thiefInvasion.bean.LeagueTIRecordInfo;
import com.gy.server.game.thiefInvasion.template.ThiefInvasionConstant;
import com.gy.server.game.thiefInvasion.template.ThiefInvasionDifficultyTemplate;
import com.gy.server.game.thiefInvasion.template.ThiefInvasionRewardTemplate;
import com.gy.server.packet.PbCommons;
import com.gy.server.packet.PbLeague;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.CollectionUtil;
import com.gy.server.utils.time.DateTimeUtil;
import com.gy.server.world.team.base.TeamInfo;
import com.ttlike.server.tl.baselib.serialize.league.LeagueBlobDb;
import com.ttlike.server.tl.baselib.serialize.league.LeagueThiefInvasionDb;
import com.ttlike.server.tl.baselib.serialize.scene.ScenePosition3D;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;

import static com.gy.server.game.league.event.LeagueEventType.enterScene;

/**
 * GVE-贼兵入侵
 * @author: gbk
 * @date: 2024-09-24 17:34
 */
public class LeagueThiefInvasionModel extends LeagueModel implements LeagueEventHandler {

    public LeagueThiefInvasionModel(League league) {
        super(league);
    }

    /**
     * 赛季开始
     */
    public static final int TI_Start = 1;
    /**
     * 赛季开始前
     */
    public static final int TI_Start_Before = 2;

    /**
     * 生成头目
     */
    public static final int TI_add_Monster = 3;


    //开启次数
    private int openTimes;
    //开启时间
    private long startTime;
    //难度id
    private int difficultyId;
    //捐献
    private Map<Long, LeagueTIDonateInfo> donateInfoMap = new HashMap<>();
    //状态标记
    private int status;
    //npc信息
    private Map<Long, LeagueTINpcInfo> npcInfos = new HashMap<>();
    //buff生效信息 key:playerId value:buff限制到期时间 -1表示没限制
    private Map<Long, Long> buffInfos = new HashMap<>();
    //在战斗中的玩家
    private Set<Long> inCombatPlayerIds = new HashSet<>();
    //战报记录信息
    private Map<String, LeagueTIRecordInfo> recordInfos = new HashMap<>();
    //奖励次数
    private Map<Long, Integer> eliteThiefRewardTimes = new HashMap<>();
    //积分
    private Map<Long, Integer> points = new HashMap<>();

    @Override
    public void init() {
        if(difficultyId <= 0){
            ThiefInvasionConstant constant = ThiefInvasionService.getConstant();
            difficultyId = constant.initialDifficulty;
        }
    }

    public long getEndTime(){
        return startTime + ThiefInvasionService.getConstant().time * DateTimeUtil.MillisOfSecond;
    }

    /**
     * 设置状态
     * @param statusType 状态类型
     * @param isReal 是否为真
     */
    public void setStatus(int statusType, boolean isReal){
        if(isReal){
            status |= (1 << statusType);
        }else{
            status &= ~(1 << statusType);
        }
    }

    /**
     * 获得状态类型
     */
    public boolean getStatus(int statusType){
        return (status & (1 << statusType)) > 0;
    }

    public LeagueTIDonateInfo getDonateInfo(long playerId){
        return donateInfoMap.getOrDefault(playerId, new LeagueTIDonateInfo(playerId));
    }

    public int getAllDonateTimes(){
        int allTimes = 0;
        for (LeagueTIDonateInfo donateInfo :donateInfoMap.values()){
            allTimes += donateInfo.getAllDonateTime();
        }
        return allTimes;
    }

    public int getDifficultyId() {
        return difficultyId;
    }

    public void setDifficultyId(int difficultyId) {
        this.difficultyId = difficultyId;
    }

    public Map<Long, LeagueTIDonateInfo> getDonateInfoMap() {
        return donateInfoMap;
    }

    public void setDonateInfoMap(Map<Long, LeagueTIDonateInfo> donateInfoMap) {
        this.donateInfoMap = donateInfoMap;
    }

    public Set<Long> getInCombatPlayerIds() {
        return inCombatPlayerIds;
    }

    public void setInCombatPlayerIds(Set<Long> inCombatPlayerIds) {
        this.inCombatPlayerIds = inCombatPlayerIds;
    }

    public int getOpenTimes() {
        return openTimes;
    }

    public void setOpenTimes(int openTimes) {
        this.openTimes = openTimes;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public boolean isOpen(){
        return startTime > 0 && startTime <= ServerConstants.getCurrentTimeMillis() && ServerConstants.getCurrentTimeMillis() < getEndTime();
    }

    public void addOpenTimes(){
        openTimes++;
    }

    public Map<Long, LeagueTINpcInfo> getNpcInfos() {
        return npcInfos;
    }

    public void setNpcInfos(Map<Long, LeagueTINpcInfo> npcInfos) {
        this.npcInfos = npcInfos;
    }

    public Map<String, LeagueTIRecordInfo> getRecordInfos() {
        return recordInfos;
    }

    public void setRecordInfos(Map<String, LeagueTIRecordInfo> recordInfos) {
        this.recordInfos = recordInfos;
    }

    public Map<Long, Long> getBuffInfos() {
        return buffInfos;
    }

    public void setBuffInfos(Map<Long, Long> buffInfos) {
        this.buffInfos = buffInfos;
    }
    
    public boolean buffCanEffect(long playerId){
        if(!buffInfos.containsKey(playerId)){
            return true;
        }
        return buffInfos.get(playerId) <= ServerConstants.getCurrentTimeMillis();
    }

    public void sync(boolean onlyScenePlayer){
        League league = getLeague();

        league.notify2AllOnlineMember(PtCode.THIEF_INVASION_CHANGE_NOTIFY, onlyScenePlayer, playerId -> {
            PbProtocol.ThiefInvasionChangeNotify.Builder builder = PbProtocol.ThiefInvasionChangeNotify.newBuilder();
            builder.setInfo(genPb(playerId));
            return builder.build();
        });
    }

    public void initNpc(){
        ThiefInvasionConstant constant = ThiefInvasionService.getConstant();
        long allRadio = getAllRadio();
        //生成精英怪npc
        List<ScenePosition3D> eliteThiefPoints = new ArrayList<>(constant.eliteThiefPoints);
        Collections.shuffle(eliteThiefPoints);

        ThiefInvasionDifficultyTemplate thiefInvasionDifficultyTemplate = ThiefInvasionService.getDifficultyTemplateMap().get(getDifficultyId());
        int npcCount = Math.min(thiefInvasionDifficultyTemplate.eliteThiefNum, constant.minNpcCount);
        for (int i = 0; i < npcCount; i++) {
            LeagueTINpcInfo npcInfo = LeagueTINpcInfo.create(constant.eliteThiefId, 10000L - allRadio, eliteThiefPoints.get(i));
            getNpcInfos().put(npcInfo.getNpcId(), npcInfo);
        }
    }

    public PbLeague.ThiefInvasionInfo genPb(long playerId){
        PbLeague.ThiefInvasionInfo.Builder builder = PbLeague.ThiefInvasionInfo.newBuilder();
        builder.setDifficultyId(difficultyId);
        builder.setOpenTimes(openTimes);
        builder.setStartTime(startTime);
        for (LeagueTINpcInfo npcInfo : npcInfos.values()) {
            if(npcInfo.getCurrentHp() != 0){
                builder.addNpcInfos(npcInfo.genPb());
            }
        }
        builder.setAllDonateTimes(getAllDonateTimes());
        builder.setSimpleDonateTimes(donateInfoMap.containsKey(playerId) ? donateInfoMap.get(playerId).getSimpleDonateTimes() : 0);
        builder.setSeniorDonateTimes(donateInfoMap.containsKey(playerId) ? donateInfoMap.get(playerId).getSeniorDonateTimes() : 0);

        builder.setEliteThiefRewardTimes(eliteThiefRewardTimes.getOrDefault(playerId, 0));
        builder.setPoints(points.getOrDefault(playerId, 0));
        return builder.build();
    }

    public Map<Long, Integer> getEliteThiefRewardTimes() {
        return eliteThiefRewardTimes;
    }

    public long getAllRadio(){
        ThiefInvasionConstant constant = ThiefInvasionService.getConstant();
        long allRadio = 0;
        for (LeagueTIDonateInfo donateInfo : getDonateInfoMap().values()) {
            allRadio += (donateInfo.getSimpleDonateTimes() * constant.normalTrapEffect);
            allRadio += (donateInfo.getSeniorDonateTimes() * constant.eliteTrapEffect);
        }
        return allRadio;
    }

    public void setEliteThiefRewardTimes(Map<Long, Integer> eliteThiefRewardTimes) {
        this.eliteThiefRewardTimes = eliteThiefRewardTimes;
    }

    public Map<Long, Integer> getPoints() {
        return points;
    }

    public void setPoints(Map<Long, Integer> points) {
        this.points = points;
    }

    public void reset(){
        //检查是否有在战斗中的人
        Set<Long> playerIds = new HashSet<>(inCombatPlayerIds);
        for (Long playerId : playerIds) {
            Stage stageByPid = CombatManager.getStageByPid(playerId);
            //强制结束战斗
            if(Objects.nonNull(stageByPid)){
                UA.finish(stageByPid.getSection());
            }
        }
        //结算积分
        ThiefInvasionConstant constant = ThiefInvasionService.getConstant();
        TreeMap<Integer, ThiefInvasionRewardTemplate> rewardTemplateTreeMap = ThiefInvasionService.getRewardTemplateTreeMap();
        MailType mailType = MailType.leagueThiefReward;
        PbCommons.PbText title = Text.genText(mailType.getTitleId()).build();
        points.forEach((k, v) -> {
            ThiefInvasionRewardTemplate rewardTemplate = rewardTemplateTreeMap.headMap(Math.max(ThiefInvasionService.minPoint, v), true).lastEntry().getValue();
            List<Reward> rewards = new ArrayList<>();
            //翻倍奖励
            for (int i = 0; i < rewardTemplate.rewardsNum; i++) {
                rewards.addAll(Reward.templateCollectionToReward(constant.rewardsItemId));
            }
            Reward.merge(rewards);
            PbCommons.PbText content = Text.genText(mailType.getContentId(), new TextParamText(v + "")).build();
                MailManager.sendMail(
                        mailType,
                        k,
                        title,
                        content,
                        ServerConstants.getCurrentTimeMillis(),
                        rewards);
        });
        League.addPlayerPlayTimes(points.keySet(), Function.leagueBanditsInvade);
        //是否进行下一难度
        boolean canNextDifficulty = false;
        for (LeagueTINpcInfo npcInfo : getNpcInfos().values()) {
            if(npcInfo.getTempId() == constant.leaderThiefId && npcInfo.getCurrentHp() <= 0){
                canNextDifficulty = true;
                break;
            }
        }
        if(canNextDifficulty){
            difficultyId = Math.min(difficultyId + 1, ThiefInvasionService.maxDifficultyId);
        }
        //统一退出队伍
        League league = getLeague();
        for (Long memberId : league.getMemberMap().keySet()) {
            Pair<Long, Integer> teamIdByPlayerId = TeamManager.getInstance().getTeamIdByPlayerId(memberId);
            if(Objects.nonNull(teamIdByPlayerId)){
                TeamInfo teamInfo = TeamManager.getInstance().getTeamInfo(teamIdByPlayerId.getKey());
                if(!teamInfo.equals(TeamManager.non_team) && teamInfo.getTeamTypeId() == constant.teamTypeId){
                    //直接销毁队伍
                    TeamHelper.commonDestroyTeamLogic(0, teamIdByPlayerId.getKey(), ServerConstants.getCurrentTimeMillis());
                }
            }
        }
        startTime = -1;
        donateInfoMap.clear();
        status = 0;
        npcInfos.clear();
        buffInfos.clear();
        inCombatPlayerIds.clear();
        recordInfos.clear();
        eliteThiefRewardTimes.clear();
        points.clear();

        sync(false);

    }

    @Override
    protected void loadData(LeagueBlobDb leagueBlobDb) {
        LeagueThiefInvasionDb modelDb = leagueBlobDb.getThiefInvasionDb();
        if(Objects.nonNull(modelDb)){
            this.openTimes = modelDb.getOpenTimes();
            this.startTime = modelDb.getStartTime();
            this.difficultyId = modelDb.getDifficultyId();
            for (LeagueThiefInvasionDb.LeagueTIDonateInfoDb infoDb : modelDb.getDonateInfoMap().values()) {
                LeagueTIDonateInfo info = new LeagueTIDonateInfo();
                info.readFromDb(infoDb);
                this.donateInfoMap.put(info.getPlayerId(), info);
            }
            this.status = modelDb.getStatus();
            for (LeagueThiefInvasionDb.LeagueTINpcInfoDb infoDb : modelDb.getNpcInfos().values()) {
                LeagueTINpcInfo info = new LeagueTINpcInfo();
                info.readFromDb(infoDb);
                this.npcInfos.put(info.getNpcId(), info);
            }
            this.buffInfos = new HashMap<>(modelDb.getBuffInfos());
            this.inCombatPlayerIds = new HashSet<>(modelDb.getInCombatPlayerIds());
            for (LeagueThiefInvasionDb.LeagueTIRecordInfoDb infoDb : modelDb.getRecordInfos().values()) {
                LeagueTIRecordInfo info = new LeagueTIRecordInfo();
                info.readFromDb(infoDb);
                this.recordInfos.put(info.getKey(), info);
            }
            this.eliteThiefRewardTimes = new HashMap<>(modelDb.getEliteThiefRewardTimes());
            this.points = new HashMap<>(modelDb.getPoints());
        }
    }

    @Override
    protected void saveData(LeagueBlobDb leagueBlobDb) {
        LeagueThiefInvasionDb modelDb = new LeagueThiefInvasionDb();
        modelDb.setOpenTimes(openTimes);
        modelDb.setStartTime(startTime);
        modelDb.setDifficultyId(difficultyId);
        for (LeagueTIDonateInfo donateInfo : donateInfoMap.values()) {
            modelDb.getDonateInfoMap().put(donateInfo.getPlayerId(), donateInfo.writeToDb());
        }
        modelDb.setStatus(status);
        for (LeagueTINpcInfo npcInfo : npcInfos.values()) {
            modelDb.getNpcInfos().put(npcInfo.getNpcId(), npcInfo.writeToDb());
        }
        for (LeagueTIRecordInfo recordInfo : recordInfos.values()) {
            modelDb.getRecordInfos().put(recordInfo.getKey(), recordInfo.writeToDb());
        }
        modelDb.setBuffInfos(buffInfos);
        modelDb.setInCombatPlayerIds(inCombatPlayerIds);
        modelDb.setEliteThiefRewardTimes(eliteThiefRewardTimes);
        modelDb.setPoints(points);
        leagueBlobDb.setThiefInvasionDb(modelDb);
    }

    @Override
    public LeagueEventType[] getEventTypes() {
        return new LeagueEventType[]{enterScene};
    }

    @Override
    public void handle(LeagueEvent event) {
        switch (event.getEventType()) {
            case enterScene: {
                // 同步数据
                if(startTime > 0){
//                    Long memberId = event.getParam(0);
//                    if (PlayerManager.isOnline(memberId)) {
//                        Player onlinePlayer = PlayerManager.getOnlinePlayer(memberId);
//                        if(Objects.nonNull(onlinePlayer)){
//                            PbProtocol.ThiefInvasionChangeNotify.Builder builder = PbProtocol.ThiefInvasionChangeNotify.newBuilder();
//                            builder.setInfo(genPb(memberId));
//                            onlinePlayer.send(PtCode.THIEF_INVASION_CHANGE_NOTIFY, builder.build());
//                        }
//                    }
                }
                break;
            }
        }
    }
}
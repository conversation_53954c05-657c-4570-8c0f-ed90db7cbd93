package com.gy.server.game.thiefInvasion;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.league.LeagueModelEnums;
import com.gy.server.game.thiefInvasion.bean.LeagueTINpcInfo;
import com.gy.server.game.thiefInvasion.template.ThiefInvasionConstant;
import com.gy.server.utils.function.Ticker;
import com.gy.server.utils.time.DateTimeUtil;

/**
 * GVE-贼兵入侵管理类
 * @author: gbk
 * @date: 2024-09-25 15:57
 */
public class LeagueThiefInvasionManager implements Ticker {

    private static final LeagueThiefInvasionManager instance = new LeagueThiefInvasionManager();
    LeagueThiefInvasionManager(){}
    public static LeagueThiefInvasionManager getInstance(){
        return instance;
    }

    @Override
    public void tick() {
        //检查开启
        ThiefInvasionConstant constant = ThiefInvasionService.getConstant();

        for (League league : LeagueManager.getLeagues()) {
            LeagueThiefInvasionModel thiefInvasionModel = league.getModel(LeagueModelEnums.thiefInvasion);
            if (thiefInvasionModel.getStartTime() <= 0) {
                continue;
            }

            //提前通知
            if(!thiefInvasionModel.getStatus(LeagueThiefInvasionModel.TI_Start_Before)){
                //未开启
                if(ServerConstants.getCurrentTimeMillis() < thiefInvasionModel.getStartTime()
                        //5分钟前
                        && thiefInvasionModel.getStartTime() - ServerConstants.getCurrentTimeMillis() <= 5L * DateTimeUtil.MillisOfMinute){
                    thiefInvasionModel.setStatus(LeagueThiefInvasionModel.TI_Start_Before, true);
                    //通知
                    thiefInvasionModel.sync(false);
                }
            }
            if(ServerConstants.getCurrentTimeMillis() < thiefInvasionModel.getStartTime()){
                continue;
            }
            //开打了
            if(!thiefInvasionModel.getStatus(LeagueThiefInvasionModel.TI_Start)){
                thiefInvasionModel.setStatus(LeagueThiefInvasionModel.TI_Start, true);
                //结算陷阱
                thiefInvasionModel.initNpc();
                //通知
                thiefInvasionModel.sync(false);

            }

            for (LeagueTINpcInfo npcInfo : thiefInvasionModel.getNpcInfos().values()) {
                //护盾重置
                if(npcInfo.getBuffResetTime() > 0 && ServerConstants.getCurrentTimeMillis() >= npcInfo.getBuffResetTime()){
                    npcInfo.setBuffResetTime(-1);
                    npcInfo.setShieldRatio(10000L);
                }
            }

            //检查结束
            if(ServerConstants.getCurrentTimeMillis() >= thiefInvasionModel.getEndTime()){
                //结算
                thiefInvasionModel.reset();
            }
        }
    }
}
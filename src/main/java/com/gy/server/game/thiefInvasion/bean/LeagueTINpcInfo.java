package com.gy.server.game.thiefInvasion.bean;

import com.gy.server.core.ServerConstants;
import com.gy.server.packet.PbLeague;
import com.gy.server.scene.map.SceneMapService;
import com.ttlike.server.tl.baselib.serialize.league.LeagueThiefInvasionDb;
import com.ttlike.server.tl.baselib.serialize.scene.ScenePosition3D;

/**
 * GVE贼兵入侵-NPC信息
 * @author: gbk
 * @date: 2024-09-26 15:02
 */
public class LeagueTINpcInfo {

    private long npcId;
    //模版id
    private int tempId;
    //护盾比例(万分比)
    private double shieldRatio;
    //当前血量
    private long currentHp;
    //位置信息
    private ScenePosition3D position;
    //护盾重置时间，没有为-1
    private long buffResetTime = -1;
    //是否锁定
    private boolean isLock = false;

    public LeagueThiefInvasionDb.LeagueTINpcInfoDb writeToDb(){
        LeagueThiefInvasionDb.LeagueTINpcInfoDb infoDb = new LeagueThiefInvasionDb.LeagueTINpcInfoDb();
        infoDb.setNpcId(npcId);
        infoDb.setTempId(tempId);
        infoDb.setShieldRatio(shieldRatio);
        infoDb.setCurrentHp(currentHp);
        infoDb.setPosition(position);
        infoDb.setBuffResetTime(buffResetTime);
        infoDb.setLock(isLock);
        return infoDb;
    }

    public void readFromDb(LeagueThiefInvasionDb.LeagueTINpcInfoDb infoDb){
        this.npcId = infoDb.getNpcId();
        this.tempId = infoDb.getTempId();
        this.shieldRatio = infoDb.getShieldRatio();
        this.currentHp = infoDb.getCurrentHp();
        this.position = infoDb.getPosition();
        this.buffResetTime = infoDb.getBuffResetTime();
        this.isLock = infoDb.isLock();
    }

    public boolean isLock() {
        return isLock;
    }

    public void setLock(boolean lock) {
        isLock = lock;
    }

    public long getBuffResetTime() {
        return buffResetTime;
    }

    public void setBuffResetTime(long buffResetTime) {
        this.buffResetTime = buffResetTime;
    }

    public long getNpcId() {
        return npcId;
    }

    public void setNpcId(long npcId) {
        this.npcId = npcId;
    }

    public int getTempId() {
        return tempId;
    }

    public void setTempId(int tempId) {
        this.tempId = tempId;
    }

    public double getShieldRatio() {
        return shieldRatio;
    }

    public void setShieldRatio(double shieldRatio) {
        this.shieldRatio = shieldRatio;
    }

    public long getCurrentHp() {
        return currentHp;
    }

    public void setCurrentHp(long currentHp) {
        this.currentHp = currentHp;
    }

    public ScenePosition3D getPosition() {
        return position;
    }

    public void setPosition(ScenePosition3D position) {
        this.position = position;
    }

    public PbLeague.ThiefInvasionNpcInfo genPb(){
        PbLeague.ThiefInvasionNpcInfo.Builder builder = PbLeague.ThiefInvasionNpcInfo.newBuilder();
        builder.setId(npcId);
        builder.setTempId(tempId);
        builder.setShieldRatio(shieldRatio);
        builder.setCurrentHp(currentHp);
        builder.setCurPoint(SceneMapService.convertPosition(position));
        builder.setIsBattle(isLock);
        return builder.build();
    }

    public static LeagueTINpcInfo create(int tempId, double shieldRatio, ScenePosition3D position){
        LeagueTINpcInfo npcInfo = new LeagueTINpcInfo();
        npcInfo.setNpcId(ServerConstants.allocateRuntimeIdentifier());
        npcInfo.setCurrentHp(-1);
        npcInfo.setTempId(tempId);
        npcInfo.setShieldRatio(shieldRatio);
        npcInfo.setPosition(position);
        return npcInfo;
    }

}
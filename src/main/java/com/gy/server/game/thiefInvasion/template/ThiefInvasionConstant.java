package com.gy.server.game.thiefInvasion.template;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.serialize.scene.ScenePosition3D;

import java.util.ArrayList;
import java.util.List;

/**
 * GVE-贼兵入侵常量配置
 * @author: gbk
 * @date: 2024-09-23 20:29
 */
public class ThiefInvasionConstant {

    //每周可开启次数
    public int WeekNum;
    //可选择开启的时间段
    public String OpenTimePeriod;
    //活动持续时间（秒）
    public long time;
    //初始难度
    public int initialDifficulty;
    //精英怪护盾重置时间（秒）
    public int shieldResetTime;
    //默认获得护盾伤害提升buff
    public int buff;
    //精英怪挑战过后buff消失时间（秒）
    public int buffInvalidatedTime;
    //每人每天可捐献陷阱数量
    public int personDailyNumber;
    public int personDailyNormalTrapNumber;
    public int personDailyEliteTrapNumber;
    //每次活动可捐献陷阱总数量
    public int totalNumber;
    //普通陷阱消耗
    public List<RewardTemplate> normalTrapConsum;
    //高级陷阱消耗
    public List<RewardTemplate> eliteTrapConsum;
    //普通陷阱捐献奖励
    public List<RewardTemplate> normalTrapRewards;
    //高级陷阱捐献奖励
    public List<RewardTemplate> eliteTrapRewards;
    //普通陷阱减少护盾（万分比）
    public long normalTrapEffect;
    //高级陷阱减少护盾（万分比）
    public long eliteTrapEffect;
    //击败精英怪奖励
    public List<RewardTemplate> eliteThiefRewards;
    //击败精英怪可领取奖励次数
    public int eliteThiefRewardsTime;
    //奖励宝箱ID（物品表）
    public List<RewardTemplate> rewardsItemId;
    //贼兵精英ID
    public int eliteThiefId;
    //贼兵头目ID
    public int leaderThiefId;
    //贼兵刷新坐标点
    public List<ScenePosition3D> eliteThiefPoints = new ArrayList<>();
    //头目刷新坐标点
    public ScenePosition3D leaderThiefPoint;
    //最小战斗人数
    public int fightMinimum;
    //开启所需帮派贡献
    public int contributionNum;
    //最小npc数量
    public int minNpcCount;

    public int teamTypeId;


    public boolean checkOpenTime(long time){
        String[] split = OpenTimePeriod.split("\\|");
        String[] minTimeArray = split[0].split(":");
        String[] maxTimeArray = split[1].split(":");
        long minTime = DateTimeUtil.toMillis(ServerConstants.getCurrentTimeLocalDateTime().withHour(Integer.parseInt(minTimeArray[0])).withMinute(Integer.parseInt(minTimeArray[1])).withSecond(0).withNano(0));
        long maxTime = DateTimeUtil.toMillis(ServerConstants.getCurrentTimeLocalDateTime().withHour(Integer.parseInt(maxTimeArray[0])).withMinute(Integer.parseInt(maxTimeArray[1])).withSecond(0).withNano(0));
        return minTime <= time && time <= maxTime;
    }


}
package com.gy.server.game.trusteeshipTask.async;

import com.gy.server.game.async.AsyncCall;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.trusteeshipTask.TrusteeshipTaskHelper;
import com.gy.server.game.trusteeshipTask.task.AbsTrusteeshipTask;

/**
 * 移除处理托管任务
 * @author: gbk
 * @date: 2025-01-21 13:41
 */
public class TrusteeshipTaskPushAsync extends AsyncCall {

    long playerId;
    Player player;
    AbsTrusteeshipTask task;

    public TrusteeshipTaskPushAsync(long playerId, AbsTrusteeshipTask task){
        this.playerId = playerId;
        this.task = task;
    }

    @Override
    public void asyncExecute() {
        player = PlayerManager.getPlayer(playerId);
    }

    @Override
    public void execute() {
        TrusteeshipTaskHelper.pushTrusteeshipTask(player, task);
        player.saveOnce();
    }
}
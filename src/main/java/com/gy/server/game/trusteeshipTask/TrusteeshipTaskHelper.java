package com.gy.server.game.trusteeshipTask;

import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.combat.CombatManager;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.player.Player;
import com.gy.server.game.trusteeshipTask.task.AbsTrusteeshipTask;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @author: gbk
 * @date: 2025-01-21 13:38
 */
public class TrusteeshipTaskHelper {

    public static void pushTrusteeshipTask(Player player, AbsTrusteeshipTask task){
        AbstractStage stage = task.buildStage(player);
        stage.init();
        stage.forceAutoModel();
        CombatManager.pushTrusteeshipTask(task.getId(), stage);
    }

    public static void finishTask(Player player, AbsTrusteeshipTask task, AbstractStage stage){
        boolean isContinue = false;
        if(Objects.nonNull(stage)){
            isContinue = task.dealCombatResult(player, stage.isWin());
        }
        if(!isContinue){
            task.finish(player);
            task.finishNotify();
            TrusteeshipTaskGlobalData globalData = GlobalDataManager.getData(GlobalDataType.trusteeshipTask);
            globalData.removeTask(task);
        }
    }

    public static void logout(long playerId){
        TrusteeshipTaskGlobalData globalData = GlobalDataManager.getData(GlobalDataType.trusteeshipTask);
        List<AbsTrusteeshipTask> tasks = new ArrayList<>(globalData.getTaskByPlayerId(playerId));
        tasks.forEach(globalData::removeTask);

    }

    public static void log(AbsTrusteeshipTask task, long playerId, String log){
        System.out.println(" trusteeship task, id : " + task.getId() + ", playerId : " + playerId + ", " + log);
    }

}
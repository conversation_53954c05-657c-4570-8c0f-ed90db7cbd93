package com.gy.server.game.trusteeshipTask;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.global.GlobalData;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.text.Text;
import com.gy.server.game.trusteeshipTask.async.TrusteeshipTaskFinishAsync;
import com.gy.server.game.trusteeshipTask.async.TrusteeshipTaskPushAsync;
import com.gy.server.game.trusteeshipTask.task.AbsTrusteeshipTask;
import com.gy.server.game.trusteeshipTask.template.TrusteeshipTaskTemplate;
import com.gy.server.utils.jprotobuf.PbUtilCompress;
import com.ttlike.server.tl.baselib.serialize.base.RedisSetLongBean;
import com.ttlike.server.tl.baselib.serialize.trusteeshipTask.TrusteeshipTaskDb;
import com.ttlike.server.tl.baselib.serialize.trusteeshipTask.TrusteeshipTaskGlobalDataDb;
import com.ttlike.server.tl.baselib.thread.ThreadPool;

import java.util.*;

/**
 * 托管任务全局数据
 * @author: gbk
 * @date: 2025-01-20 16:15
 */
public class TrusteeshipTaskGlobalData extends GlobalData {

    /**
     * 托管任务
     * taskId:taskInfo
     */
    private Map<Long, AbsTrusteeshipTask> tasks = new HashMap();
    /**
     * playerId:taskId
     */
    private Map<Long, Set<Long>> playerId2TaskIds = new HashMap<>();

    /**
     * 任务id
     */
    private long taskId;

    /**
     * 分配任务id
     * @return 任务id
     */
    public long allocateTaskId(){
        taskId++;
        return taskId;
    }

    public boolean canAddTask(long playerId, int type) {
        List<AbsTrusteeshipTask> taskList = getTaskByPlayerId(playerId);

        for (AbsTrusteeshipTask task : taskList) {
            if(task.getType() == type){
                return false;
            }
        }

        return true;
    }

    /**
     * 添加挂机任务
     * @param task
     */
    public void addTask(AbsTrusteeshipTask task){
        tasks.put(task.getId(), task);
        if(!playerId2TaskIds.containsKey(task.getPlayerId())){
            playerId2TaskIds.put(task.getPlayerId(), new HashSet<>());
        }
        playerId2TaskIds.get(task.getPlayerId()).add(task.getId());
    }

    /**
     * 获得玩家身上的挂机任务
     * @param playerId
     * @return
     */
    public List<AbsTrusteeshipTask> getTaskByPlayerId(long playerId){
        List<AbsTrusteeshipTask> taskList = new ArrayList<>();
        if(playerId2TaskIds.containsKey(playerId)){
            Set<Long> taskIds = playerId2TaskIds.get(playerId);
            for (Long taskId : taskIds) {
                taskList.add(tasks.get(taskId));
            }
        }
       return taskList;
    }

    public AbsTrusteeshipTask getTask(long taskId){
        return tasks.get(taskId);
    }

    /**
     * 战斗结束
     * @param taskId 任务id
     * @param stage 战场信息
     */
    public void stageFinish(long taskId, AbstractStage stage){
        if(tasks.containsKey(taskId)){
            AbsTrusteeshipTask task = getTask(taskId);
            long playerId = task.getPlayerId();
            if(PlayerManager.isOnline(playerId)){
                TrusteeshipTaskHelper.finishTask(PlayerManager.getOnlinePlayer(playerId), task, stage);
            }else{
                ThreadPool.execute(new TrusteeshipTaskFinishAsync(playerId, task, stage));
            }
            TrusteeshipTaskHelper.log(task,  playerId, "auto stage is finish");
        }
    }

    /**
     * 删除任务
     * @param task 任务信息
     */
    public void removeTask(AbsTrusteeshipTask task){
        tasks.remove(task.getId());
        playerId2TaskIds.get(task.getPlayerId()).remove(task.getId());
    }

    @Override
    public void tick() {
        for (AbsTrusteeshipTask task : tasks.values()) {
            //检测挂机任务是否可以触发
            if(task.canTrigger()){
                task.setLastTriggerTime(ServerConstants.getCurrentTimeMillis());
                long playerId = task.getPlayerId();
                if(PlayerManager.isOnline(playerId)){
                    TrusteeshipTaskHelper.pushTrusteeshipTask(PlayerManager.getOnlinePlayer(playerId), task);
                }else{
                    ThreadPool.execute(new TrusteeshipTaskPushAsync(playerId, task));
                }
            }
        }
    }

    @Override
    public void readFromPb(byte[] bytes) throws Exception {
        TrusteeshipTaskGlobalDataDb globalDataDb = PbUtilCompress.decode(TrusteeshipTaskGlobalDataDb.class, bytes);
        Map<Long, TrusteeshipTaskDb> taskMap = globalDataDb.getTasks();
        for (Long taskId : taskMap.keySet()) {
            TrusteeshipTaskDb trusteeshipTaskDb = taskMap.get(taskId);
            TrusteeshipTaskTemplate template = TrusteeshipTaskService.getTaskTemplateMap().get(trusteeshipTaskDb.getType());
            tasks.put(taskId, template.getTaskEnums().readFromDb(trusteeshipTaskDb));
        }
        Map<Long, RedisSetLongBean> playerId2TaskIdsMap = globalDataDb.getPlayerId2TaskIds();
        for (Long playerId : playerId2TaskIdsMap.keySet()) {
            RedisSetLongBean redisSetLongBean = playerId2TaskIdsMap.get(playerId);
            playerId2TaskIds.put(playerId,redisSetLongBean.getSet());
        }
        this.taskId = globalDataDb.getTaskId();
    }

    @Override
    public byte[] writeToPb() {
        TrusteeshipTaskGlobalDataDb globalDataDb = new TrusteeshipTaskGlobalDataDb();
        for (Long taskId : tasks.keySet()) {
            globalDataDb.getTasks().put(taskId, tasks.get(taskId).writeToDb());
        }
        for (Long playerId : playerId2TaskIds.keySet()) {
            RedisSetLongBean redisSetLongBean = new RedisSetLongBean();
            redisSetLongBean.setSet(playerId2TaskIds.get(playerId));
            globalDataDb.getPlayerId2TaskIds().put(playerId, redisSetLongBean);
        }
        globalDataDb.setTaskId(taskId);
        return PbUtilCompress.encode(globalDataDb);
    }

}
package com.gy.server.game.trusteeshipTask.task.impl;

import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.drop.Reward;
import com.gy.server.game.equipmentMission.EquipmentMissionService;
import com.gy.server.game.equipmentMission.PlayerEquipmentMissionModel;
import com.gy.server.game.equipmentMission.stage.EquipmentMissionStage;
import com.gy.server.game.equipmentMission.template.EquipmentMissionEquipInMissionTemplate;
import com.gy.server.game.log.constant.BehaviorType;
import com.gy.server.game.player.Player;
import com.gy.server.game.trusteeshipTask.task.AbsTrusteeshipTask;
import com.gy.server.packet.PbProtocol;
import com.gy.server.packet.PbTrusteeship;
import com.ttlike.server.tl.baselib.serialize.trusteeshipTask.EquipmentMissionTrusteeshipTaskDb;
import com.ttlike.server.tl.baselib.serialize.trusteeshipTask.TrusteeshipTaskDb;

import java.util.ArrayList;
import java.util.List;

/**
 * 山河探险任务托管
 * @author: gbk
 * @date: 2025-01-21 11:17
 */
public class EquipmentMissionTask extends AbsTrusteeshipTask {

    private int nowMissionId;

    public EquipmentMissionTask(){}
    public EquipmentMissionTask(long playerId, int type) {
        super(playerId, type);
    }

    @Override
    public boolean dealCombatResult(Player player, boolean isWin) {
        PlayerEquipmentMissionModel model = player.getEquipmentMissionModel();

        if (isWin){
            EquipmentMissionEquipInMissionTemplate missionTemplate = EquipmentMissionService.getInMissionTemplate(nowMissionId);
            if(missionTemplate != null){
                addedReward(missionTemplate.allReward);
            }
        }

        model.dealTrusteeshipCombatResult(nowMissionId, isWin);
        int nextMissionId = model.getTrusteeshipMissionId(isWin);

        //是否继续
        return nextMissionId > 0;
    }

    @Override
    public AbstractStage buildStage(Player player) {
        PlayerEquipmentMissionModel model = player.getEquipmentMissionModel();
        int missionId = model.getMapNextMissionId(model.getMapId());
        if(missionId > 0){
            EquipmentMissionEquipInMissionTemplate nextMissionTemplate = EquipmentMissionService.getInMissionTemplate(missionId);
            if(nextMissionTemplate != null){
                EquipmentMissionStage stage = new EquipmentMissionStage(player, nextMissionTemplate);
                nowMissionId = nextMissionTemplate.id;
                return stage;
            }
        }

        return null;
    }

    @Override
    public void finish(Player player) {
        player.getEquipmentMissionModel().addTrusteeshipReward(getRewardTemplateList());
    }

    @Override
    public void functionReceiveDeal(Player player, PbProtocol.TrusteeshipTaskCancelRst.Builder rst) {
        PlayerEquipmentMissionModel model = player.getEquipmentMissionModel();
        List<Reward> rewardList = new ArrayList<>(model.getTrusteeshipRewardList());

        PbTrusteeship.TrusteeshipEquipmentMission builder = model.genTrusteeshipEquipmentMission();

        model.clearTrusteeshipReward();
        model.getPassMissionIdSet().clear();

        if(!rewardList.isEmpty()){
            Reward.add(rewardList, player, BehaviorType.trusteeshipEquipmentMission);
        }

        rst.setEquipmentMission(builder);
    }

    @Override
    public void fillFunctionPb(Player player, PbProtocol.TrusteeshipTaskFinishNotify.Builder notify) {
        notify.setEquipmentMission(player.getEquipmentMissionModel().genTrusteeshipEquipmentMission());
    }

    @Override
    public void init(Player player) {
        super.init(player);
    }

    @Override
    public void subReadFromDb(TrusteeshipTaskDb db) {
        EquipmentMissionTrusteeshipTaskDb mission = db.getMission();
        if(mission != null){
            this.nowMissionId = mission.getNowMissionId();
        }
    }

    @Override
    public void subWriteToDb(TrusteeshipTaskDb db) {
        if(nowMissionId != 0){
            EquipmentMissionTrusteeshipTaskDb mission = new EquipmentMissionTrusteeshipTaskDb(nowMissionId);
            db.setMission(mission);
        }
    }

    public int getNowMissionId() {
        return nowMissionId;
    }

}
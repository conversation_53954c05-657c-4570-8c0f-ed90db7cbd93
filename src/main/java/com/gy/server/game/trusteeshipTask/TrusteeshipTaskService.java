package com.gy.server.game.trusteeshipTask;

import com.gy.server.core.packet.PlayerPacketHandler;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.combat.CombatManager;
import com.gy.server.game.combat.Stage;
import com.gy.server.game.cond.CondManager;
import com.gy.server.game.function.Function;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.handler.Handler;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.service.Service;
import com.gy.server.game.text.Text;
import com.gy.server.game.trusteeshipTask.bean.TrusteeshipTaskEnums;
import com.gy.server.game.trusteeshipTask.task.AbsTrusteeshipTask;
import com.gy.server.game.trusteeshipTask.template.TrusteeshipTaskTemplate;
import com.gy.server.packet.PbProtocol;

import java.util.*;

/**
 * 托管任务
 * @author: gbk
 * @date: 2025-01-20 14:14
 */
public class TrusteeshipTaskService extends PlayerPacketHandler implements Service {

    /**
     * type:template
     */
    private static Map<Integer, TrusteeshipTaskTemplate> taskTemplateMap = new HashMap<>();


    public static Map<Integer, TrusteeshipTaskTemplate> getTaskTemplateMap() {
        return taskTemplateMap;
    }

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.hosting_hosting);
        Map<Integer, TrusteeshipTaskTemplate> taskTemplateMapTemp = new HashMap<>();
        for (Map<String, String> map : mapList) {
            TrusteeshipTaskTemplate template = new TrusteeshipTaskTemplate();
            template.readTxt(map);
            taskTemplateMapTemp.put(template.type, template);
        }
        taskTemplateMap = taskTemplateMapTemp;
    }

    @Override
    public void clearConfigData() {
        taskTemplateMap.clear();
    }

    @Override
    public boolean isPreSystemStartup() {
        return true;
    }

    @Handler(PtCode.TRUSTEESHIP_TASK_CREATE_REQ)
    public void createTask(Player player, PbProtocol.TrusteeshipTaskCreateReq req, long time) {
        PbProtocol.TrusteeshipTaskCreateRst.Builder rst = PbProtocol.TrusteeshipTaskCreateRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            int type = req.getType();
            rst.setType(type);

            int text = baseCheck(player, type);
            if(text != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(text));
                break logic;
            }
            //检查托管任务是否已经存在
            TrusteeshipTaskGlobalData globalData = GlobalDataManager.getData(GlobalDataType.trusteeshipTask);
            if(!globalData.canAddTask(player.getPlayerId(), type)){
                rst.setResult(Text.genServerRstInfo(Text.托管任务_已经存在));
                break logic;
            }

            TrusteeshipTaskTemplate template = taskTemplateMap.get(type);
            TrusteeshipTaskEnums taskEnums = template.getTaskEnums();
            if(!taskEnums.canStart(player)){
                rst.setResult(Text.genServerRstInfo(Text.托管任务_不能开始));
                break logic;
            }
            //检查是否存在战斗,有就强制退出
            Stage stageByPlayer = CombatManager.getStageByPlayer(player);
            if(Objects.nonNull(stageByPlayer)){
                stageByPlayer.forceQuit(player.getPlayerId());
            }
            AbsTrusteeshipTask task = template.getTaskEnums().createTask(player.getPlayerId());
            task.init(player);
            globalData.addTask(task);
        }

        player.send(PtCode.TRUSTEESHIP_TASK_CREATE_RST, rst.build(), time);
    }

    @Handler(PtCode.TRUSTEESHIP_TASK_CANCEL_REQ)
    public void cancelTask(Player player, PbProtocol.TrusteeshipTaskCancelReq req, long time) {
        PbProtocol.TrusteeshipTaskCancelRst.Builder rst = PbProtocol.TrusteeshipTaskCancelRst.newBuilder().setResult(Text.genOkServerRstInfo());
        logic:{
            int type = req.getType();
            int text = baseCheck(player, type);
            if(text != Text.没有异常){
                rst.setResult(Text.genServerRstInfo(text));
                break logic;
            }

            TrusteeshipTaskGlobalData globalData = GlobalDataManager.getData(GlobalDataType.trusteeshipTask);
            List<AbsTrusteeshipTask> taskList = new ArrayList<>(globalData.getTaskByPlayerId(player.getPlayerId()));

            for (AbsTrusteeshipTask task : taskList) {
                if(task.getType() == type){
                    TrusteeshipTaskHelper.finishTask(player, task, null);
                    task.functionReceiveDeal(player, rst);
                    TrusteeshipTaskHelper.log(task,  player.getPlayerId(), "取消托管任务");
                }
            }
            rst.setType(type);
        }
        player.send(PtCode.TRUSTEESHIP_TASK_CANCEL_RST, rst.build(), time);
    }

    private int baseCheck(Player player, int type){
        TrusteeshipTaskTemplate template = taskTemplateMap.get(type);
        if(Objects.isNull(template)){
            return Text.参数异常;
        }
        //检查function是否开启
        if(Function.getFunctionById(template.functionId).isNotOpen(player)){
            return Text.功能未开启;
        }
        if(!CondManager.checkCond(player, template.cond)){
            return Text.托管任务_未开启;
        }
        return Text.没有异常;
    }

}
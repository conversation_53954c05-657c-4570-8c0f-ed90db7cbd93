package com.gy.server.game.trusteeshipTask.task;

import com.gy.server.game.combat.AbstractStage;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.player.Player;

import java.util.List;

/**
 * @author: gbk
 * @date: 2025-01-20 15:04
 */
public interface ITrusteeshipTask {

    /**
     * 累加奖励
     */
    public void addedReward(List<RewardTemplate> rewardTemplateList);

    /**
     * 处理战斗结果
     * @param player 玩家信息
     * @param isWin 是否胜利
     * @return 是否继续
     */
    public boolean dealCombatResult(Player player, boolean isWin);

    /**
     * 构建战场信息
     * @param player 玩家信息
     * @return 战场信息
     */
    public AbstractStage buildStage(Player player);

    /**
     * 能否触发托管任务
     * @return
     */
    public boolean canTrigger();

    /**
     * 托管任务结束
     */
    public void finish(Player player);

    public void init(Player player);

}
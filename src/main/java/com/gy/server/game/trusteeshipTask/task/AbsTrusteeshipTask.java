package com.gy.server.game.trusteeshipTask.task;

import com.gy.server.core.ServerConstants;
import com.gy.server.game.drop.RewardTemplate;
import com.gy.server.game.global.GlobalDataManager;
import com.gy.server.game.global.GlobalDataType;
import com.gy.server.game.packet.PtCode;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerManager;
import com.gy.server.game.trusteeshipTask.TrusteeshipTaskGlobalData;
import com.gy.server.game.trusteeshipTask.TrusteeshipTaskService;
import com.gy.server.game.trusteeshipTask.template.TrusteeshipTaskTemplate;
import com.gy.server.packet.PbProtocol;
import com.gy.server.utils.time.DateTimeUtil;
import com.ttlike.server.tl.baselib.serialize.trusteeshipTask.TrusteeshipTaskDb;

import java.util.ArrayList;
import java.util.List;

/**
 * 托管任务
 * @author: gbk
 * @date: 2025-01-20 15:14
 */
public abstract class AbsTrusteeshipTask implements ITrusteeshipTask {

    /**
     * 累加奖励
     */
    List<RewardTemplate> rewardTemplateList = new ArrayList<>();

    /**
     * 唯一id
     */
    long id;

    /**
     * 任务所属玩家id
     */
    long playerId;

    /**
     * 任务类型
     */
    int type;
    /**
     * 上次触发事件
     */
    long lastTriggerTime;

    public AbsTrusteeshipTask(){}
    public AbsTrusteeshipTask(long playerId, int type){
        TrusteeshipTaskGlobalData globalData = GlobalDataManager.getData(GlobalDataType.trusteeshipTask);
        this.id = globalData.allocateTaskId();
        this.type = type;
        this.playerId = playerId;
    }

    @Override
    public boolean canTrigger() {
        int triggerFrequency = getTemplate().triggerFrequency;
        return ServerConstants.getCurrentTimeMillis() >= lastTriggerTime + triggerFrequency * DateTimeUtil.MillisOfSecond;
    }

    @Override
    public void addedReward(List<RewardTemplate> rewardTemplateList) {
        this.rewardTemplateList.addAll(rewardTemplateList);
    }

    public void readFromDb(TrusteeshipTaskDb db) {
        this.id = db.getId();
        this.playerId = db.getPlayerId();
        this.type = db.getType();
        this.lastTriggerTime = db.getLastTriggerTime();
        if(db.getRewardTemplateList() != null && !db.getRewardTemplateList().equals("")){
            this.rewardTemplateList = RewardTemplate.readListFromText(db.getRewardTemplateList());
        }
        subReadFromDb(db);
    }

    public abstract void subReadFromDb(TrusteeshipTaskDb db);

    public TrusteeshipTaskDb writeToDb() {
        TrusteeshipTaskDb db = new TrusteeshipTaskDb();
        db.setId(getId());
        db.setType(getType());
        db.setPlayerId(getPlayerId());
        db.setLastTriggerTime(lastTriggerTime);
        db.setRewardTemplateList(getRewardTemplateList().isEmpty() ? null : RewardTemplate.templateList2Text(getRewardTemplateList()));
        subWriteToDb(db);
        return db;
    }

    public abstract void subWriteToDb(TrusteeshipTaskDb db);


    /**
     * 功能领奖处理
     * @return
     */
    public void functionReceiveDeal(Player player, PbProtocol.TrusteeshipTaskCancelRst.Builder rst){

    }

    public void finishNotify() {
        Player player = PlayerManager.getOnlinePlayer(playerId);
        if(player != null){
            PbProtocol.TrusteeshipTaskFinishNotify.Builder notify = PbProtocol.TrusteeshipTaskFinishNotify.newBuilder();
            notify.setType(type);
            fillFunctionPb(player, notify);
            player.send(PtCode.TRUSTEESHIP_TASK_FINISH_NOTIFY, notify.build());
        }
    }

    public abstract void fillFunctionPb(Player player, PbProtocol.TrusteeshipTaskFinishNotify.Builder notify);

    @Override
    public void init(Player player) {
        setLastTriggerTime(ServerConstants.getCurrentTimeMillis());
    }

    public TrusteeshipTaskTemplate getTemplate(){
        return TrusteeshipTaskService.getTaskTemplateMap().get(type);
    }

    public List<RewardTemplate> getRewardTemplateList() {
        return rewardTemplateList;
    }

    public void setRewardTemplateList(List<RewardTemplate> rewardTemplateList) {
        this.rewardTemplateList = rewardTemplateList;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public void setLastTriggerTime(long lastTriggerTime) {
        this.lastTriggerTime = lastTriggerTime;
    }
}
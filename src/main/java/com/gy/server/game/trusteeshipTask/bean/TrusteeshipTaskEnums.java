package com.gy.server.game.trusteeshipTask.bean;

import com.gy.server.game.equipmentMission.PlayerEquipmentMissionModel;
import com.gy.server.game.player.Player;
import com.gy.server.game.trusteeshipTask.task.AbsTrusteeshipTask;
import com.gy.server.game.trusteeshipTask.task.impl.EquipmentMissionTask;
import com.ttlike.server.tl.baselib.serialize.trusteeshipTask.TrusteeshipTaskDb;

import java.util.HashMap;
import java.util.Map;

/**
 * 托管任务构建
 * @author: gbk
 * @date: 2025-01-20 19:44
 */
public enum TrusteeshipTaskEnums {

    EquipmentMission(1, new EquipmentMissionTrusteeshipTaskBuilder()),

    ;
    int type;
    TrusteeshipTaskBuilder builder;

    TrusteeshipTaskEnums(int type, TrusteeshipTaskBuilder builder){
        this.builder = builder;
        this.type = type;
    }

    public static final Map<Integer, TrusteeshipTaskEnums> taskEnums = new HashMap<>();
    static{
        for (TrusteeshipTaskEnums typeEnums : TrusteeshipTaskEnums.values()) {
            taskEnums.put(typeEnums.type, typeEnums);
        }
    }

    public static TrusteeshipTaskEnums of(int type){
        return taskEnums.get(type);
    }

    public int getType(){
        return type;
    }

    public AbsTrusteeshipTask createTask(long playerId){
        return builder.build(this,playerId);
    }

    public AbsTrusteeshipTask readFromDb(TrusteeshipTaskDb db){
        return builder.readFromDb(db);
    }

    /**
     * 能否开始托管任务
     * @param player 玩家信息
     * @return 是否可以
     */
    public boolean canStart(Player player){
        return builder.canStart(player);
    }

    public static interface TrusteeshipTaskBuilder{
        public AbsTrusteeshipTask build(TrusteeshipTaskEnums type, long playerId);

        public AbsTrusteeshipTask readFromDb(TrusteeshipTaskDb db);

        public boolean canStart(Player player);

    }

    private static class EquipmentMissionTrusteeshipTaskBuilder implements TrusteeshipTaskBuilder {
        @Override
        public AbsTrusteeshipTask build(TrusteeshipTaskEnums type, long playerId) {
            return new EquipmentMissionTask(playerId, type.type);
        }

        @Override
        public AbsTrusteeshipTask readFromDb(TrusteeshipTaskDb db) {
            EquipmentMissionTask task = new EquipmentMissionTask();
            task.readFromDb(db);
            return task;
        }

        @Override
        public boolean canStart(Player player) {
            PlayerEquipmentMissionModel model = player.getEquipmentMissionModel();
            int missionId = model.getMapNextMissionId(model.getMapId());
            return missionId > 0;
        }
    }
}
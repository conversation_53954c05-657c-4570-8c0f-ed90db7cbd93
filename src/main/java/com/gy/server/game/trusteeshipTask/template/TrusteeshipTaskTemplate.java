package com.gy.server.game.trusteeshipTask.template;

import com.gy.server.common.base.AbsTemplate;
import com.gy.server.game.trusteeshipTask.bean.TrusteeshipTaskEnums;

import java.util.Map;

/**
 * 托管任务配表数据
 * @author: gbk
 * @date: 2025-01-21 18:06
 */
public class TrusteeshipTaskTemplate implements AbsTemplate {


    public int type;


    public int functionId;

    public String cond;

    public int triggerFrequency;


    @Override
    public void readTxt(Map<String, String> map) {
        type = Integer.parseInt(map.get("id"));
        functionId = Integer.parseInt(map.get("functionId"));
        cond = map.get("hostingUnlock");
        triggerFrequency = Integer.parseInt(map.get("hostingTime"));
    }

    public TrusteeshipTaskEnums getTaskEnums(){
        return TrusteeshipTaskEnums.of(type);
    }

}
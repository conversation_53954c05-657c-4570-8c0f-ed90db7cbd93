package com.gy.server.game.combataddition;

import com.gy.server.game.hero.Hero;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.player.Player;
import com.gy.server.game.role.PlayerRoleService;
import com.gy.server.game.role.template.ProtagonistTemplate;

import java.util.List;
import java.util.Objects;

/**
 * 过滤combat英雄范围
 * <AUTHOR> - [Created on 2022-07-28 11:49]
 */
public enum CombatHeroFilerEnums {

    /**
     * 所有英雄
     */
    all(1) {
        @Override
        public boolean check(Player player, Hero hero, List<Integer> params, LineupType lineupType) {
            return true;
        }
    },
    /**
     * 指定英雄
     */
    hero(2) {
        @Override
        public boolean check(Player player, Hero hero, List<Integer> params, LineupType lineupType) {
            return Objects.nonNull(hero) && params.contains(hero.getTemplateId());
        }
    },
    /**
     * 前后排
     */
    frontBackRow(3){
        @Override
        public boolean check(Player player, Hero hero, List<Integer> params, LineupType lineupType) {
            int type = params.get(0);//1前排 2后排
            int rowsNum = lineupType.getFormationType().getHeroNum() / 2; //阵容英雄数量
            //英雄位置信息
            int heroLineup;
            if(Objects.isNull(hero)){
                heroLineup = player.getLineupModel().getStandIndexProtagonist(lineupType);
            }else{
                heroLineup = hero.getPlayer().getLineupModel().getStandIndex(lineupType, hero);
            }
            if(heroLineup <= 0){
                return false;
            }
            //直接算index
            return rowsNum * (type - 1) <= heroLineup && heroLineup < rowsNum * type;
        }
    },
    /**
     * 指定位置
     */
    lineupPosition(4){
        @Override
        public boolean check(Player player, Hero hero, List<Integer> params, LineupType lineupType) {
            int standIndex;
            if(Objects.isNull(hero)){
                standIndex = player.getLineupModel().getStandIndexProtagonist(lineupType);
            }else{
                standIndex = hero.getPlayer().getLineupModel().getStandIndex(lineupType, hero);
            }
            return standIndex > 0 && params.contains(standIndex);
        }
    },
    /**
     * 魂系限制
     */
    soulType(5){
        @Override
        public boolean check(Player player, Hero hero, List<Integer> params, LineupType lineupType) {
            int soulType;
            if(Objects.isNull(hero)){
                ProtagonistTemplate protagonistTemplate = PlayerRoleService.getProtagonistTemplate(player);
                soulType = protagonistTemplate.soulType;
            }else{
                soulType = hero.getItemTemplate().soulType;
            }
            return params.contains(soulType);
        }
    },

    /**
     * 职业
     */
    career(6){
        @Override
        public boolean check(Player player, Hero hero, List<Integer> params, LineupType lineupType) {
            int position;
            if(Objects.isNull(hero)){
                ProtagonistTemplate protagonistTemplate = PlayerRoleService.getProtagonistTemplate(player);
                position = protagonistTemplate.position;
            }else{
                position = hero.getItemTemplate().profession.getNumber();
            }
            return params.contains(position);
        }
    },


    ;

    int id;

    CombatHeroFilerEnums(int id) {
        this.id = id;
    }

    public abstract boolean check(Player player, Hero hero, List<Integer> params, LineupType lineupType);

    public static CombatHeroFilerEnums getFiler(int id) {
        for (CombatHeroFilerEnums value : CombatHeroFilerEnums.values()) {
            if(value.id == id){
                return value;
            }
        }
        return null;
    }

}

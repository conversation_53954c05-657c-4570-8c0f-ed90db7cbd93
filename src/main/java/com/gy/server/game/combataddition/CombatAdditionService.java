package com.gy.server.game.combataddition;

import com.google.common.collect.Sets;
import com.gy.server.core.Configuration;
import com.gy.server.core.reader.ConfigFile;
import com.gy.server.core.reader.ConfigReader;
import com.gy.server.game.combat.CombatService;
import com.gy.server.game.combataddition.bean.CombatAdditionConditionTemplate;
import com.gy.server.game.service.Service;

import java.util.*;

/**
 * 属性附加接口
 *
 * <AUTHOR> - [Created on 2022-07-28 11:14]
 */
public class CombatAdditionService implements Service {

    private static Map<Integer, CombatAdditionConditionTemplate> combatAdditionConditionTemplates = new HashMap<>();

    public static CombatAdditionConditionTemplate getConditionTemplate(int id) {
        return combatAdditionConditionTemplates.get(id);
    }

    @Override
    public void loadConfigData(boolean isStartup) throws Exception {
        List<Map<String, String>> mapList = ConfigReader.read(ConfigFile.combatAddition_effect);
        Map<Integer, CombatAdditionConditionTemplate> conditionTemplates = new HashMap<>();
        for (Map<String, String> map : mapList) {
            int id = Integer.parseInt(map.get("id"));
            String stageTypes = map.get("stageType");//功能限制
            int heroFiler = Integer.parseInt(map.get("heroFiler"));//过滤器
            String heroFilerParam = map.get("heroFilerParam");//过滤器参数
            int upType = Integer.parseInt(map.get("upType"));//增幅类型
            String upParam = map.get("upParam");//增幅参数
            CombatAdditionConditionTemplate conditionTemplate = new CombatAdditionConditionTemplate();
            conditionTemplate.id = id;
            if (!"-1".equals(stageTypes)) {
                for (String s : stageTypes.split(",")) {
                    conditionTemplate.stageTypes.add(Integer.parseInt(s));
                }
            }
            CombatHeroFilerEnums filer = CombatHeroFilerEnums.getFiler(heroFiler);
            if (Objects.isNull(filer)) {
                if (Configuration.runMode.isTest() || Configuration.runMode.isPress()) {
                    System.err.println("CombatHeroFilerEnums is null, id : " + heroFiler);
                } else {
                    throw new IllegalArgumentException("CombatHeroFilerEnums is null, id : " + heroFiler);
                }
            }
            conditionTemplate.heroFiler = filer;
            for (String s : heroFilerParam.split(",")) {
                conditionTemplate.heroFileParams.add(Integer.parseInt(s));
            }
            conditionTemplate.heroFileParamsStr = heroFilerParam;
            conditionTemplate.upType = upType;
            conditionTemplate.upParam = upParam;
            conditionTemplate.deceivePower = Integer.parseInt(map.get("deceivePower")) == 1;

            conditionTemplates.put(conditionTemplate.id, conditionTemplate);
        }
        combatAdditionConditionTemplates = conditionTemplates;
    }

    @Override
    public void clearConfigData() {
        combatAdditionConditionTemplates.clear();
    }

    @SuppressWarnings("unchecked")
    @Override
    public Set<Class<? extends Service>> preServices() {
        return Sets.newHashSet(CombatService.class);
    }

    @Override
    public boolean isWorldServer() {
        return true;
    }

    @Override
    public boolean isGameServer() {
        return true;
    }

}

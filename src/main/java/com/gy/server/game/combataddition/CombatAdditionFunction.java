package com.gy.server.game.combataddition;

import com.gy.server.game.activity.ActivityModule;
import com.gy.server.game.activity.ActivityService;
import com.gy.server.game.activity.ActivityType;
import com.gy.server.game.activity.data.infiniteRealm.InfiniteAttriItemTemplate;
import com.gy.server.game.activity.data.infiniteRealm.InfiniteRealmActivityData;
import com.gy.server.game.activity.module.InfiniteRealmActivityModule;
import com.gy.server.game.artifact.PlayerArtifactModel;
import com.gy.server.game.heroShadows.HeroShadowsService;
import com.gy.server.game.heroShadows.PlayerHeroShadowsModel;
import com.gy.server.game.heroShadows.template.HeroShadowsAlbumLvTemplate;
import com.gy.server.game.heroShadows.template.HeroShadowsSkillTemplate;
import com.gy.server.game.heroSpectrum.PlayerHeroSpectrumModel;
import com.gy.server.game.league.League;
import com.gy.server.game.league.LeagueManager;
import com.gy.server.game.league.LeagueModelEnums;
import com.gy.server.game.leagueBoss.LeagueBossModel;
import com.gy.server.game.martialArtsTech.PlayerMartialArtsTechModel;
import com.gy.server.game.pet.PetService;
import com.gy.server.game.pet.template.PetSpectrumTemplate;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModelEnums;
import com.gy.server.game.role.BookInfoBean;
import com.gy.server.game.role.PlayerRoleService;
import com.gy.server.game.smallGroup.PlayerSmallGroupModel;
import com.gy.server.game.weaponSoul.PlayerWeaponSoulModel;
import com.gy.server.utils.CollectionUtil;
import com.ttlike.server.tl.baselib.serialize.combatAddition.CombatAdditions;
import com.ttlike.server.tl.baselib.serialize.item.pet.PetSpectrum;

import java.util.List;
import java.util.Map;

/**
 * 全队加成功能来源
 *
 * <AUTHOR> - [Created on 2023/4/23 10:53]
 */
public enum CombatAdditionFunction {

    //神器
    artifact() {
        @Override
        public CombatAdditions refresh(Player player) {
            PlayerArtifactModel artifactModel = player.getArtifactModel();
            int profession = player.getProfession();
            int currentUnlockLevel = artifactModel.getCurrentUnlockLevel();
            int smallLevel = artifactModel.getSmallLevel();
            return CombatAdditionHelper.calcArtifactCombatAdditions(currentUnlockLevel, smallLevel, profession);
        }
    },

    //英雄谱
    heroSpectrum() {
        @Override
        public CombatAdditions refresh(Player player) {
            PlayerHeroSpectrumModel heroModel = player.getModel(PlayerModelEnums.heroSpectrum);
            return CombatAdditionHelper.calcHeroSpectrumCombatAdditions(heroModel.getHeroSpectrumLvMap());
        }
    },

    //功法
    book() {
        @Override
        public CombatAdditions refresh(Player player) {
            Map<Integer, BookInfoBean> bookMap = player.getRoleModel().getBookMap();
            return CombatAdditionHelper.calcBookCombatAdditions(bookMap);
        }
    },

    //心法
    martialArtsTechStar() {
        @Override
        public CombatAdditions refresh(Player player) {
            PlayerMartialArtsTechModel artsTechModel = player.getModel(PlayerModelEnums.martialArtsTech);
            return CombatAdditionHelper.calcMartialArtsTechStarCombatAdditions(artsTechModel.getMartialArtsTechMap());
        }
    },

    //帮派门客堂
    leagueBossPal() {
        @Override
        public CombatAdditions refresh(Player player) {
            CombatAdditions totalAdditions = new CombatAdditions();
            if (LeagueManager.isNotJoinLeague(player)) {
                return totalAdditions;
            }
            League league = LeagueManager.getLeagueByPlayer(player);
            LeagueBossModel leagueBossModel = league.getModel(LeagueModelEnums.boss);
            return CombatAdditionHelper.calcLeagueBossPalCombatAdditions(leagueBossModel.getPalLevel());
        }
    },

    //器魂
    weaponSoul() {
        @Override
        public CombatAdditions refresh(Player player) {
            PlayerWeaponSoulModel model = player.getWeaponSoulModel();
            int id = model.getId();
            return CombatAdditionHelper.calcWeaponSoulCombatAdditions(id);
        }
    },

    /**
     * 小团体
     */
    smallGroup(){
        @Override
        public CombatAdditions refresh(Player player) {
            PlayerSmallGroupModel model = player.getPlayerSmallGroupModel();
            return CombatAdditionHelper.calcSmallGroupCombatAdditions(model.getBrotherLevel().get());
        }
    },

    /**
     * 无量环境道具增益
     * 仅对本活动有效，无全局加成
     */
    infiniteRealm(){
        @Override
        public CombatAdditions refresh(Player player) {
            CombatAdditions totalAdditions = new CombatAdditions();
            List<ActivityModule> list = player.getActivityModel().getActivityModulesByType(ActivityType.infiniteRealm);
            for(ActivityModule module : list){
                if(module instanceof InfiniteRealmActivityModule){
                    InfiniteRealmActivityData data = ActivityService.getActivityData(module.getActivityId());
                    for(Map.Entry<Integer, InfiniteAttriItemTemplate> entry : data.combatAdditions.entrySet()){
                        int count = player.getBagModel().getItemCount(entry.getKey());
                        if(count > 0){
                            CombatAdditions.mergeCombatAdditions(totalAdditions, entry.getValue().combatAdditions);
                        }
                    }
                }
            }
            return totalAdditions;
        }
    },

//    bookRealmExp(){
//        @Override
//        public CombatAdditions refresh(Player player) {
//            int realmLv = player.getRoleModel().getRealmLv();
//            CombatAdditions totalAdditions = new CombatAdditions();
//            Map<Integer, BookRealmExpTemplate> bookRealmExpTemplateMap = PlayerRoleService.getBookRealmExpTemplateMap();
//            for (int i = 1; i <= realmLv; i++) {
//                BookRealmExpTemplate bookRealmExpTemplate = bookRealmExpTemplateMap.get(i);
//                CombatAdditions.mergeCombatAdditions(totalAdditions, bookRealmExpTemplate.expCombatAdditions);
//            }
//            return totalAdditions;
//        }
//    },

    hiddenWeapon(){
        @Override
        public CombatAdditions refresh(Player player) {
            return player.getHiddenWeaponModel().getLevelCombatAddition();
        }
    },

    petSpectrum(){
        @Override
        public CombatAdditions refresh(Player player) {
            CombatAdditions totalAdditions = new CombatAdditions();
            for (PetSpectrum spectrum : player.getPetModel().getPetSpectrumMap().values()) {
                Map<Integer, Map<Integer, PetSpectrumTemplate>> spectrumTemplates = PetService.getSpectrumTemplates();
                Map<Integer, PetSpectrumTemplate> spectrumTemplateMap = spectrumTemplates.get(spectrum.getId());
                if(CollectionUtil.isNotEmpty(spectrumTemplateMap)){
                    for (Integer star : spectrumTemplateMap.keySet()) {
                        if(spectrum.getNowStar() >= star){
                            PetSpectrumTemplate template = spectrumTemplateMap.get(star);
                            CombatAdditions.mergeCombatAdditions(totalAdditions, template.combatAdditions);
                        }
                    }
                }
            }
            return totalAdditions;
        }
    },

    /**
     * 将魂
     */
    heroShadows(){
        @Override
        public CombatAdditions refresh(Player player) {
            CombatAdditions totalAdditions = new CombatAdditions();
            PlayerHeroShadowsModel model = player.getPlayerHeroShadowsModel();

            //羁绊
            for (Map.Entry<Integer, Integer> entry : model.getAlbumMap().entrySet()) {
                Map<Integer, HeroShadowsAlbumLvTemplate> map = HeroShadowsService.getAlbumLvMap().get(entry.getKey());
                if(CollectionUtil.isNotEmpty(map)){
                    for (HeroShadowsAlbumLvTemplate bean : map.values()) {
                        if(bean.level <= entry.getValue()){
                            CombatAdditions.mergeCombatAdditions(totalAdditions, bean.combatAddition);
                        }
                    }
                }
            }

            //技能
            for (Map.Entry<Integer, Integer> entry : model.getSkillLevelMap().entrySet()) {
                Map<Integer, HeroShadowsSkillTemplate> map = HeroShadowsService.getSkillMap().get(entry.getKey());
                for (HeroShadowsSkillTemplate bean : map.values()) {
                    if(entry.getValue() >= bean.level){
                        CombatAdditions.mergeCombatAdditions(totalAdditions, bean.skillEffect);
                    }
                }
            }

            return totalAdditions;
        }
    },
    ;

    /**
     * 刷新单个模块属性
     */
    public abstract CombatAdditions refresh(Player player);
}

package com.gy.server.game.combataddition;

import com.gy.server.game.artifact.ArtifactService;
import com.gy.server.game.artifact.bean.ArtifactTemplate;
import com.gy.server.game.attribute.Attributes;
import com.gy.server.game.combat.StageType;
import com.gy.server.game.combataddition.bean.CombatAdditionConditionTemplate;
import com.gy.server.game.hero.Hero;
import com.gy.server.game.heroSpectrum.HeroSpectrumService;
import com.gy.server.game.heroSpectrum.template.HeroSpectrumTemplate;
import com.gy.server.game.leagueBoss.LeagueBossService;
import com.gy.server.game.leagueBoss.template.LeagueBossPalTemplate;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.martialArtsTech.MartialArtsTechService;
import com.gy.server.game.martialArtsTech.template.MartialArtsTechSkillGroupTemplate;
import com.gy.server.game.player.Player;
import com.gy.server.game.role.BookInfoBean;
import com.gy.server.game.role.PlayerRoleService;
import com.gy.server.game.role.template.ProtagonistBookTemplate;
import com.gy.server.game.smallGroup.SmallGroupService;
import com.gy.server.game.smallGroup.template.SmallGroupBrothersLevelTemplate;
import com.gy.server.game.smallGroup.template.SmallGroupBrothersSkillTemplate;
import com.gy.server.game.weaponSoul.WeaponSoulService;
import com.gy.server.game.weaponSoul.template.WeaponSoulTemplate;
import com.ttlike.server.tl.baselib.serialize.combatAddition.CombatAddition;
import com.ttlike.server.tl.baselib.serialize.combatAddition.CombatAdditions;
import org.apache.commons.lang3.tuple.Pair;

import java.util.*;

/**
 * <AUTHOR> - [Created on 2022-07-28 11:14]
 */
public class CombatAdditionHelper {

    public static Pair<Attributes, Map<Integer, Set<Integer>>> getAttrs(Player player, Hero hero, LineupType lineupType, StageType stageType) {
        //获取所有加成信息
        CombatAdditions combatAdditions = player.getCombatAdditionModel().getCombatAdditions();
        return getAttrs(player, hero, lineupType, stageType, combatAdditions);
    }

    public static Pair<Attributes, Map<Integer, Set<Integer>>> getAttrs(Player player, Hero hero, LineupType lineupType, StageType stageType, CombatAdditions combatAdditions){
        Attributes result = new Attributes();
        Map<Integer, Long> attrMap = new HashMap<>();
        Map<Integer, Set<Integer>> buffResult = new HashMap<>();

        for (Map.Entry<Integer, CombatAddition> entry : combatAdditions.getCombatAdditionMap().entrySet()) {
            Integer id = entry.getKey();
            CombatAddition combatAddition = entry.getValue();
            CombatAdditionConditionTemplate conditionTemplate = CombatAdditionService.getConditionTemplate(id);
            if (Objects.isNull(conditionTemplate)) {
                continue;
            }
            if (!conditionTemplate.check(player, hero, lineupType, stageType)) {
                continue;
            }
            //记录信息
            int upType = conditionTemplate.upType;
            String[] split = conditionTemplate.upParam.split(",");
            if (upType == 1) {
                //属性
                String attrs = split[0];
                for (String attr : attrs.split("\\|")) {
                    attrMap.merge(Integer.parseInt(attr), combatAddition.getValue(), Long::sum);
                }
            } else {
                //buff
                for (String s : split) {
                    Set<Integer> buffIds = buffResult.computeIfAbsent((int) combatAddition.getValue(), integer -> new HashSet<>());
                    buffIds.add(Integer.parseInt(s));
                }
            }
        }
        Attributes.mergeAttributes(result, Attributes.createAttributes(attrMap));

        return Pair.of(result, buffResult);
    }

    /**
     * 计算神器全体属性
     * @param level
     * @param smallLevel
     * @param profession
     * @return
     */
    public static CombatAdditions calcArtifactCombatAdditions(int level, int smallLevel, int profession){
        CombatAdditions combatAdditions = new CombatAdditions();
        for (int i = 1; i <= level; i++) {
            ArtifactTemplate artifactTemplate = ArtifactService.getArtifactTemplate(profession, i);
            if (Objects.nonNull(artifactTemplate)) {
                List<CombatAdditions> list = new ArrayList<>();
                if(artifactTemplate.level < level){
                    list.addAll(artifactTemplate.combatAdditions);
                }else {
                    if(artifactTemplate.level == level){
                        if(artifactTemplate.combatAdditions.size() > 1){
                            for(int j = 1; j <= smallLevel; j++){
                                list.add(artifactTemplate.combatAdditions.get(j - 1));
                            }
                        }
                    }
                }

                if(!list.isEmpty()){
                    for (CombatAdditions bean : list) {
                        CombatAdditions.mergeCombatAdditions(combatAdditions, bean);
                    }
                }
            }
        }
        return combatAdditions;
    }

    /**
     * 计算英雄谱全体属性
     * @param starNums
     * @return
     */
    public static CombatAdditions calcHeroSpectrumCombatAdditions(Set<Integer> starNums){
        CombatAdditions combatAdditions = new CombatAdditions();
        Map<Integer, HeroSpectrumTemplate> heroSpectrumTemplateMap = HeroSpectrumService.getHeroSpectrumTemplateMap();
        for (Integer starNum : starNums) {
            HeroSpectrumTemplate heroSpectrumTemplate = heroSpectrumTemplateMap.get(starNum);
            if (heroSpectrumTemplate == null) {
                continue;
            }
            CombatAdditions.mergeCombatAdditions(combatAdditions, heroSpectrumTemplate.combatAdditions);
        }
        return combatAdditions;
    }

    /**
     * 计算功法全体属性
     * @param bookMap
     * @return
     */
    public static CombatAdditions calcBookCombatAdditions(Map<Integer, BookInfoBean> bookMap){
        CombatAdditions totalAdditions = new CombatAdditions();
        for (BookInfoBean bookInfoBean : bookMap.values()) {
            int bookId = bookInfoBean.getBookId();

            Map<Integer, Integer> grade2Level = bookInfoBean.getGrade2Level();
            for (Integer grade : grade2Level.keySet()) {
                // 每一卷等级所加成信息
                int bookLv = grade2Level.get(grade);
                for (int i = 0; i <= bookLv; i++) {
                    ProtagonistBookTemplate bookTemplate = PlayerRoleService.getBookTemplate(bookId, grade, i);
                    CombatAdditions.mergeCombatAdditions(totalAdditions, bookTemplate.combatAdditions);
                }
            }
        }
        return totalAdditions;
    }

    /**
     * 计算心法全局加成
     * @param martialArtsTechMap
     * @return
     */
    public static CombatAdditions calcMartialArtsTechStarCombatAdditions(Map<Integer, Integer> martialArtsTechMap){
        CombatAdditions totalAdditions = new CombatAdditions();
        for (Map.Entry<Integer, Integer> entry : martialArtsTechMap.entrySet()) {
            Integer id = entry.getKey();
            Integer star = entry.getValue();
            for (int i = 1; i <= star; i++) {
                MartialArtsTechSkillGroupTemplate skillGroupTemplate = MartialArtsTechService.getTechSkillGroupTemplateById(id, i);
                if (skillGroupTemplate.type == 1) {
                    CombatAdditions.mergeCombatAdditions(totalAdditions, skillGroupTemplate.combatAdditions);
                }
            }
        }
        return totalAdditions;
    }

    /**
     * 计算门客堂全局加成
     * @param palLevel
     * @return
     */
    public static CombatAdditions calcLeagueBossPalCombatAdditions(int palLevel){
        CombatAdditions totalAdditions = new CombatAdditions();
        for (int i = 1; i <= palLevel; i++) {
            LeagueBossPalTemplate leagueBossPalTemplate = LeagueBossService.getLeagueBossPalTemplateMap().get(i);
            if (Objects.nonNull(leagueBossPalTemplate)) {
                CombatAdditions.mergeCombatAdditions(totalAdditions, leagueBossPalTemplate.combatAdditions);
            }
        }
        return totalAdditions;
    }

    /**
     * 计算器魂全局加成
     * @param weaponSoulId
     * @return
     */
    public static CombatAdditions calcWeaponSoulCombatAdditions(int weaponSoulId){
        CombatAdditions totalAdditions = new CombatAdditions();
        Map<Integer, WeaponSoulTemplate> weaponSoulTemplateMap = WeaponSoulService.getWeaponSoulTemplateMap();
        for (WeaponSoulTemplate template : weaponSoulTemplateMap.values()) {
            if(weaponSoulId > template.id){
                if(!template.combatAdditions.getCombatAdditionMap().isEmpty()){
                    CombatAdditions.mergeCombatAdditions(totalAdditions, template.combatAdditions);
                }
            }
        }
        return totalAdditions;
    }

    /**
     * 计算小团体全局加成
     * @param brotherLevel
     * @return
     */
    public static CombatAdditions calcSmallGroupCombatAdditions(int brotherLevel) {
        CombatAdditions totalAdditions = new CombatAdditions();
        SmallGroupBrothersLevelTemplate brothersLevelTemplate = SmallGroupService.getBrothersLevelTemplateMap().get(brotherLevel);
        if(brothersLevelTemplate != null){
            for (Integer skillId : brothersLevelTemplate.skill) {
                SmallGroupBrothersSkillTemplate skillTemplate = SmallGroupService.getBrothersSkillTemplateMap().get(skillId);
                if(skillTemplate != null){
                    CombatAdditions.mergeCombatAdditions(totalAdditions, skillTemplate.combatAddition);
                }
            }
        }
        return totalAdditions;
    }
}

package com.gy.server.game.combataddition.bean;

import com.gy.server.game.combat.StageType;
import com.gy.server.game.combataddition.CombatHeroFilerEnums;
import com.gy.server.game.hero.Hero;
import com.gy.server.game.lineup.LineupType;
import com.gy.server.game.player.Player;
import com.gy.server.utils.CollectionUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * 限制配置
 * <AUTHOR> - [Created on 2022-07-28 13:27]
 */
public class CombatAdditionConditionTemplate {

    /**
     * 唯一id
     */
    public int id;
    /**
     * 功能限制（stage）
     */
    public List<Integer> stageTypes = new ArrayList<>();
    /**
     * 过滤生效英雄范围
     */
    public CombatHeroFilerEnums heroFiler;
    /**
     * 范围参数
     */
    public List<Integer> heroFileParams = new ArrayList<>();
    public String heroFileParamsStr;
    /**
     * 增幅类型 1.属性 2.buff
     */
    public int upType;

    /**
     * 增幅参数
     */
    public String upParam;

    /**
     * 是否计算假战斗力
     * true 计算总战斗力要加上该假战斗力
     */
    public boolean deceivePower;

    public boolean check(Player player, Hero hero, LineupType lineupType, StageType stageType){
        //检查功能
        if(CollectionUtil.isNotEmpty(stageTypes) && !stageTypes.contains(stageType.getId())){
            return false;
        }
        //检查范围
        if(!heroFiler.check(player, hero, heroFileParams, lineupType)){
            return false;
        }
        return true;
    }

    public boolean isUpAttribute(){
        return upType == 1;
    }

}

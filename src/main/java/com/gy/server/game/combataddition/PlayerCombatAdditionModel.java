package com.gy.server.game.combataddition;


import com.gy.server.game.attribute.AttributeSourceType;
import com.gy.server.game.combataddition.bean.CombatAdditionConditionTemplate;
import com.gy.server.game.hero.Hero;
import com.gy.server.game.player.Player;
import com.gy.server.game.player.PlayerModel;
import com.gy.server.game.player.event.PlayerEvent;
import com.gy.server.game.player.event.PlayerEventHandler;
import com.gy.server.game.player.event.PlayerEventType;
import com.ttlike.server.tl.baselib.serialize.combatAddition.CombatAddition;
import com.ttlike.server.tl.baselib.serialize.combatAddition.CombatAdditions;
import com.ttlike.server.tl.baselib.serialize.player.PlayerBlobDb;
import com.ttlike.server.tl.baselib.serialize.player.PlayerCombatAdditionModelDB;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

import static com.gy.server.game.player.event.PlayerEventType.*;

public class PlayerCombatAdditionModel extends PlayerModel implements PlayerEventHandler {

    private static final PlayerEventType[] eventTypes = new PlayerEventType[]{login, bookLv
            , martialArtsTechStar, artifactLevelUp, heroSpectrum, weaponSoulUp
            , bookRealmLv, hiddenWeaponCreate, hiddenWeaponLevelUp, petSpectrumActive,
            heroShadowsAlbumUp, heroShadowsSkillUp, heroShadowsChapterReset,};

    /**
     * <templateId: 加成的具体数值>
     */
    private CombatAdditions combatAdditions = new CombatAdditions();

    /**
     * Key:功能来源 Value:Key=id value=加成值
     */
    private final Map<CombatAdditionFunction, CombatAdditions> additionFunctionMap = new ConcurrentHashMap<>();

    public PlayerCombatAdditionModel(Player player) {
        super(player);
    }

    public CombatAdditions getCombatAdditions() {
        return combatAdditions;
    }

    public Map<CombatAdditionFunction, CombatAdditions> getAdditionFunctionMap() {
        return additionFunctionMap;
    }

    /**
     * 刷新总属性
     *
     * @param isTempChange      是否可能发生英雄模板变化
     * @param additionFunctions 指定的功能类型
     */
    public void refresh(boolean isTempChange, CombatAdditionFunction... additionFunctions) {
        boolean isChange = false;
        for (CombatAdditionFunction additionFunction : additionFunctions) {
            CombatAdditions combatAdditions = additionFunction.refresh(getPlayer());
            if (!combatAdditions.isEmpty()) {
                additionFunctionMap.put(additionFunction, combatAdditions);
                isChange = true;
            } else if (additionFunctionMap.containsKey(additionFunction)) {
                CombatAdditions tmp = additionFunctionMap.get(additionFunction);
                if (!tmp.isEmpty()) {
                    additionFunctionMap.remove(additionFunction);
                    isChange = true;
                }
            }
        }
        if (isChange) {
            //重新计算总属性
            CombatAdditions totalCombatAdditions = new CombatAdditions();
            for (CombatAdditions combatAdditions : additionFunctionMap.values()) {
                CombatAdditions.mergeCombatAdditions(totalCombatAdditions, combatAdditions);
            }
            this.combatAdditions = totalCombatAdditions;
            refreshAllHero(isTempChange);
            //获得养成效果事件
            getPlayer().postEvent(PlayerEventType.getCombatAdditionEffect);
        }
    }

    /**
     * 刷新全部
     */
    public void refreshAll() {
        refresh(true, CombatAdditionFunction.values());
    }

    /**
     * 获取加成的假战力
     */
    public long getDeceivePower() {
        long totalFightPower = 0L;
        //获取所有加成信息
        for (Map.Entry<Integer, CombatAddition> entry : combatAdditions.getCombatAdditionMap().entrySet()) {
            Integer id = entry.getKey();
            CombatAdditionConditionTemplate conditionTemplate = CombatAdditionService.getConditionTemplate(id);
            if (Objects.isNull(conditionTemplate)) {
                continue;
            }
            if (conditionTemplate.deceivePower) {
                CombatAddition combatAddition = entry.getValue();
                totalFightPower += combatAddition.getFightPower();
            }

        }
        return totalFightPower;
    }


    /**
     * 刷新所有武将和主角
     * 刷新英雄战力，一律在添加战斗属性培养之外添加。因为可能会有遍历添加的情况存在
     *
     * @param isTempChange 是否可能发生英雄模板变化
     */
    private void refreshAllHero(boolean isTempChange) {
        //刷新所有英雄
        for (Hero hero : getPlayer().getBagModel().getAllHeroes()) {
            if (isTempChange) {
                hero.refreshBagAttributes(true, false, AttributeSourceType.英雄模板属性, AttributeSourceType.战斗属性培养);
            } else {
                hero.refreshBagAttributes(true, false, AttributeSourceType.战斗属性培养);
            }
        }
        //刷新主角
        if (isTempChange) {
            getPlayer().getRoleModel().refreshAttributes(true, false, AttributeSourceType.英雄模板属性, AttributeSourceType.战斗属性培养);
        } else {
            getPlayer().getRoleModel().refreshAttributes(true, false, AttributeSourceType.战斗属性培养);
        }
        // 战斗力同步
        getPlayer().refreshFightingPower();
    }

    @Override
    protected void loadData(PlayerBlobDb playerBlob) {
        PlayerCombatAdditionModelDB playerCombatAdditionModelDB = playerBlob.getCombatAdditionModel();
        if (playerCombatAdditionModelDB != null) {
            this.combatAdditions = playerCombatAdditionModelDB.getCombatAdditions();
            playerCombatAdditionModelDB.getAdditionFunctionMap().forEach((k, v)-> additionFunctionMap.put(CombatAdditionFunction.valueOf(k), v));
        }
    }

    @Override
    protected void saveData(PlayerBlobDb playerBlob) {
        PlayerCombatAdditionModelDB builder = new PlayerCombatAdditionModelDB();
        builder.setCombatAdditions(combatAdditions);
        additionFunctionMap.forEach((k, v)-> builder.getAdditionFunctionMap().put(k.name(), v));
    }

    @Override
    public PlayerEventType[] getEventTypes() {
        return eventTypes;
    }

    @Override
    public void handle(PlayerEvent event) {
        switch (event.getEventType()) {
            case login: {
                // 登陆刷新一次
                refreshAll();
                break;
            }
            case bookLv: {
                refresh(false, CombatAdditionFunction.book);
                break;
            }
            case martialArtsTechStar: {
                refresh(false, CombatAdditionFunction.martialArtsTechStar);
                break;
            }
            case artifactLevelUp: {
                refresh(true, CombatAdditionFunction.artifact);
                break;
            }
            case heroSpectrum: {
                refresh(false, CombatAdditionFunction.heroSpectrum);
                break;
            }
            case weaponSoulUp: {
                refresh(false, CombatAdditionFunction.weaponSoul);
                break;
            }
//            case bookRealmLv:{
//                refresh(false, CombatAdditionFunction.bookRealmExp);
//                break;
//            }
            case smallGroupUp:{
                refresh(false, CombatAdditionFunction.smallGroup);
            }
            case hiddenWeaponCreate:
            case hiddenWeaponLevelUp:{
                refresh(false, CombatAdditionFunction.hiddenWeapon);
                break;
            }
            case petSpectrumActive:{
                refresh(false, CombatAdditionFunction.petSpectrum);
                break;
            }
            case heroShadowsAlbumUp:
            case heroShadowsSkillUp:
            case heroShadowsChapterReset:{
                refresh(false, CombatAdditionFunction.heroShadows);
                break;
            }
        }
    }
}

<assembly>


    <id>bin</id>
    <formats>
        <format>dir</format><!--打包的文件格式,也可以有：dir war zip tar.gz-->
    </formats>
    <!--tar.gz压缩包下是否生成和项目名相同的根目录-->
    <includeBaseDirectory>false</includeBaseDirectory>


    <!--<dependencySets>
        <dependencySet>
            &lt;!&ndash;是否把本项目添加到依赖文件夹下&ndash;&gt;
            <useProjectArtifact>true</useProjectArtifact>
            <outputDirectory>dist2</outputDirectory>
            &lt;!&ndash;将scope为runtime的依赖包打包&ndash;&gt;
            <scope>runtime</scope>
        </dependencySet>
    </dependencySets>-->


    <fileSets>
        <!-- 项目信息文件 -->
        <fileSet>
            <directory>${basedir}</directory>
            <outputDirectory>/</outputDirectory>
            <includes>
                <include>constants.xml</include>
                <include>hibernate.cfg.xml</include>
                <include>version.xml</include>
                <include>log4j2*</include>
                <include>*.sh</include>
                <include>sql/*</include>
            </includes>
        </fileSet>

        <fileSet>
            <directory>${basedir}/lib</directory>
            <outputDirectory>dist</outputDirectory>
        </fileSet>

        <fileSet>
            <directory>${basedir}/target/dist</directory>
            <outputDirectory>dist</outputDirectory>
        </fileSet>

        <fileSet>
            <directory>${basedir}/target</directory>
            <outputDirectory>dist</outputDirectory>
            <includes>
                <include>*.jar</include>
            </includes>
        </fileSet>
    </fileSets>
</assembly>
#!/bin/bash

#变量
shutdownScript=shutdown.sh
startupScript=startup.sh

#获取绑定端口。如<serverport>14001</serverport>，以">"拆分后获取14001</serverport>，再以"<"拆分后获取14001
port=`cat ../config/config.xml |grep \<serverport\>|cut -d '>' -f 2|cut -d '<' -f 1`
echo "bindPort -> "$port

#关服
echo "server shutdown start..."
sh $shutdownScript

#检测端口连接是否全部断开
playerCount=`netstat -anopt|grep $port|wc -l`
while [ "$playerCount" != "0" ]; do
    echo "wait "$playerCount" player disconnect..."
    sleep 1
    playerCount=`netstat -anopt|grep $port|wc -l`
done

#关服完成
echo "server shutdown end..."

#开服
echo "server start begin..."
sh $startupScript
#tail -f '../logs/stdout.log'
echo "server start end..."

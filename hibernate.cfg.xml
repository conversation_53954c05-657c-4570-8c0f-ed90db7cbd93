<?xml version='1.0' encoding='UTF-8'?>
<!DOCTYPE hibernate-configuration PUBLIC
        "-//Hibernate/Hibernate Configuration DTD 3.0//EN"
        "http://hibernate.sourceforge.net/hibernate-configuration-3.0.dtd">
<hibernate-configuration>

    <session-factory>
        <property name="hibernate.connection.driver_class">com.mysql.jdbc.Driver</property>
        <property name="hibernate.dialect">org.hibernate.dialect.MySQL5Dialect</property>
        <!--请务必确认此处db的ip、端口以及实例名-->
        <property name="hibernate.connection.pool.size">100</property>
        <property name="hibernate.show_sql">false</property>

        <!--hikariCP配置-->
        <property name="hibernate.connection.provider_class">com.gy.server.core.dbsource.hikari.HikariHibernateConnectionProvider</property>
        <!--官方强烈建议不要设置此值，将连接池作为一个固定大小的池性能更好-->
        <!--<property name="hibernate.hikari.minimumIdle">5</property>-->
        <!--4核i7为：9 = (4 * 2) + 1，此项默认值就是10-->
        <property name="hibernate.hikari.maximumPoolSize">10</property>
        <property name="hibernate.hikari.connectionTestQuery">SELECT 1</property>
        <!--只有当minimumIdle < maximumPoolSize时才有效-->
        <property name="hibernate.hikari.idleTimeout">30000</property>
        <!-- 官方强烈建议设置此值，一个连接的生命时长（毫秒），超时而且没被使用则被释放（retired），缺省:30分钟，建议设置比数据库超时时长少30秒，参考MySQL wait_timeout参数（show variables like '%timeout%';）-->
        <property name="hibernate.hikari.maxLifetime">7370000</property>
        <!--从数据库获取连接的超时时间-->
        <property name="hibernate.hikari.connectionTimeout">30000</property>
        <property name="hibernate.jdbc.fetch_size">200</property>
        <property name="hibernate.jdbc.batch_size">200</property>
        <property name="hibernate.hibernate.cache.use_second_level_cache">false</property>
        <property name="hibernate.current_session_context_class">thread</property>
        <property name="hibernate.generate_statistics">false</property>

        <!--<property name="show_sql">true</property>
        <property name="format_sql">true</property>-->

        <mapping class="com.gy.server.game.player.PlayerData"/>
<!--        <mapping class="com.gy.server.game.world.WorldData"/>-->
        <mapping class="com.gy.server.core.id.IdData"/>
        <mapping class="com.gy.server.game.mail.Mail"/>
        <mapping class="com.gy.server.game.rank.RankItem"/>
        <mapping class="com.gy.server.game.pay.Receipt"/>
<!--        <mapping class="com.gy.server.game.world.OtherWorldData"/>-->
        <mapping class="com.gy.server.game.global.Global"/>
        <mapping class="com.gy.server.game.league.LeagueData"/>

        <mapping class="com.gy.server.world.record.WorldCombatRecord"/>
        <mapping class="com.gy.server.world.crossData.db.CrossDataDTO"/>
    </session-factory>

</hibernate-configuration>